<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="PaymentIntroMethod_Main_Dialog">
    <decision-state id="PI1001_CheckContext_DS">
      <if cond="(GlobalVars.ValidateCardOptions &amp;&amp; GlobalVars.ValidateCardOptions.cardStatus == 'amount_under_min') || (GlobalVars.ValidateCardOptions &amp;&amp; GlobalVars.ValidateCardOptions.cardStatus == 'amount_over_max') || GlobalVars.authorizationFailureHandling == 'reconfirmDetails' || GlobalVars.authorizationFailureHandling == 'sameAmountDeclined'">
        <action next="PI1025_GoTo_BankCardDetails_SD"/>
        <elseif cond="GlobalVars.tryOtherCardReason == undefined || GlobalVars.tryOtherCardReason == ''">
          <if cond="GlobalVars.callType == 'activate'">
            <action next="PI1005_SummarizeCharges_PP"/>
            <else>
              <if cond="GlobalVars.GetPaymentOptions &amp;&amp; (GlobalVars.GetPaymentOptions.hasEWallet == true) &amp;&amp; (GlobalVars.GetPaymentOptions.isWalletPopulated == true) &amp;&amp; GlobalVars.abandonEWalletAuth == false &amp;&amp; GlobalVars.guestPayment == false">
                <action next="PI1035_UseWallet_SD"/>
                <else>
                  <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
                  <action next="PI1015_GetPaymentMethod_DM"/>
                </else>
              </if>
            </else>
          </if>
        </elseif>
        <else>
          <if cond="(GlobalVars.tryOtherCardReason == 'disconfirm_wallet') || (GlobalVars.tryOtherCardReason == 'startover')">
            <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
          </if>
          <if cond="GlobalVars.callType == 'activate'">
            <action next="PI1010_FindCardWaitSBI_DM"/>
            <elseif cond="GlobalVars.tryOtherCardReason == 'reenter_zip'">
              <action next="PI1025_GoTo_BankCardDetails_SD"/>
            </elseif>
            <else>
              <if cond="GlobalVars.GetPaymentOptions &amp;&amp; (GlobalVars.GetPaymentOptions.hasEWallet == true) &amp;&amp; (GlobalVars.GetPaymentOptions.isWalletPopulated == true) &amp;&amp; GlobalVars.abandonEWalletAuth == false &amp;&amp; GlobalVars.guestPayment == false &amp;&amp; GlobalVars.tryOtherCardReason != 'fail_authorization'">
                <action next="PI1035_UseWallet_SD"/>
                <else>
                  <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
                  <action next="PI1010_FindCardWaitSBI_DM"/>
                </else>
              </if>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="PI1005_SummarizeCharges_PP">
      <session-mapping key="ratePlanPrice" value="parseFloat(GlobalVars.selectedPlanPrice)" type="String"/>
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount" type="String"/>
      <session-mapping key="insurance" value="GlobalVars.insurance" type="String"/>
      <audio>
        <prompt id="PI1005_out_02" cond="paymentAmount == 35">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $35 payment for your first month's charges" src="PI1005_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_03" cond="paymentAmount == 40">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $40 payment for your first month's charges" src="PI1005_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_04" cond="paymentAmount == 45">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $45 payment for your first month's charges" src="PI1005_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_05" cond="paymentAmount == 50">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $50 payment for your first month's charges" src="PI1005_out_05.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_06" cond="paymentAmount == 55">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $55 payment for your first month's charges" src="PI1005_out_06.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_07" cond="paymentAmount == 60">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $60 payment for your first month's charges" src="PI1005_out_07.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_08" cond="paymentAmount == 65">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $65 payment for your first month's charges" src="PI1005_out_08.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_09" cond="paymentAmount == 70">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $70 payment for your first month's charges" src="PI1005_out_09.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_10" cond="paymentAmount == 75">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $75 payment for your first month's charges" src="PI1005_out_10.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_11" cond="paymentAmount == 80">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $80 payment for your first month's charges" src="PI1005_out_11.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_12" cond="paymentAmount == 85">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $85 payment for your first month's charges" src="PI1005_out_12.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_13" cond="paymentAmount == 90">
          <prompt-segments>
            <audiofile text="And last, to complete your activation I need a $90 payment for your first month's charges" src="PI1005_out_13.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_14" cond="!((paymentAmount == 35) || (paymentAmount == 40) || (paymentAmount == 45) || (paymentAmount == 50) || (paymentAmount == 55) || (paymentAmount == 60) || (paymentAmount == 65) || (paymentAmount == 70) || (paymentAmount == 75) || (paymentAmount == 80) || (paymentAmount == 85) || (paymentAmount == 90))">
          <prompt-segments>
            <audiofile text="And last, to complete your activation, I need a payment for your first month's charges" src="PI1005_out_14.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="currency" expr="ratePlanPrice" cond="!((paymentAmount == 35) || (paymentAmount == 40) || (paymentAmount == 45) || (paymentAmount == 50) || (paymentAmount == 55) || (paymentAmount == 60) || (paymentAmount == 65) || (paymentAmount == 70) || (paymentAmount == 75) || (paymentAmount == 80) || (paymentAmount == 85) || (paymentAmount == 90))">
          <param value="f" name="intonation"/>
          <param value="false" name="playZeroCents"/>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1005_out_16">
          <prompt-segments>
            <audiofile text="That includes all taxes and regulatory fees" src="PI1005_out_16.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PI1015_GetPaymentMethod_DM"/>
    </play-state>

    <dm-state id="PI1010_FindCardWaitSBI_DM" type="CUST">
      <session-mapping key="needResidualPayment" value="GlobalVars.needResidualPayment" type="String"/>
      <success>
        <action label="continue">
          <if cond="GlobalVars.preferredPaymentMethod == 'credit' || GlobalVars.preferredPaymentMethod == 'debit'">
            <action next="PI1025_GoTo_BankCardDetails_SD"/>
            <else>
              <action next="PI1015_GetPaymentMethod_DM"/>
            </else>
          </if>
        </action>
        <action label="cant_find">
          <action next="PI1030_GoTo_ErrorHandling_SD"/>
        </action>
        <action label="credit">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'credit'"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'credit'"/>
          <if cond="GlobalVars.callType == 'activate'">
            <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'83'"/>
            <else>
              <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'104'"/>
            </else>
          </if>
          <action next="PI1039_CheckCallType_JDA"/>
        </action>
        <action label="debit">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'debit'"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'debit'"/>
          <if cond="GlobalVars.callType == 'activate'">
            <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'83'"/>
            <else>
              <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'104'"/>
            </else>
          </if>
          <action next="PI1039_CheckCallType_JDA"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PI1010_ini_03" cond="needResidualPayment == true">
                <prompt-segments>
                  <audiofile text="You can pay with a credit or debit card, or another Payment PIN When you have it ready, say 'continue' " src="PI1010_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1010_ini_01" cond="needResidualPayment != true">
                <prompt-segments>
                  <audiofile text="When you have your card ready, say continue" src="PI1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1010_ini_02">
                <prompt-segments>
                  <audiofile text="When you're ready, say Continue or press 1, or say I can't find it or press 2" src="PI1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PI1010_ini_03" cond="needResidualPayment == true">
                <prompt-segments>
                  <audiofile text="You can pay with a credit or debit card, or another Payment PIN When you have it ready, say 'continue' " src="PI1010_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1010_ini_01" cond="needResidualPayment != true">
                <prompt-segments>
                  <audiofile text="When you have your card ready, say continue" src="PI1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1010_ini_02">
                <prompt-segments>
                  <audiofile text="When you're ready, say Continue or press 1, or say I can't find it or press 2" src="PI1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PI1010_ini_03" cond="needResidualPayment == true">
                <prompt-segments>
                  <audiofile text="You can pay with a credit or debit card, or another Payment PIN When you have it ready, say 'continue' " src="PI1010_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1010_ini_01" cond="needResidualPayment != true">
                <prompt-segments>
                  <audiofile text="When you have your card ready, say continue" src="PI1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1010_ini_02">
                <prompt-segments>
                  <audiofile text="When you're ready, say Continue or press 1, or say I can't find it or press 2" src="PI1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommandsNoOperator_dtmf.grxml">
          <grammars filename="PI1010_FindCardWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="PI1010_FindCardWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="0ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="PI1015_GetPaymentMethod_DM" type="CUST">
      <session-mapping key="preferredPaymentMethod" value="GlobalVars.preferredPaymentMethod" type="String"/>
      <session-mapping key="fromAnotherPaymentMenu" value="GlobalVars.fromAnotherPaymentMenu != undefined ? GlobalVars.fromAnotherPaymentMenu : false" type="String"/>
      <session-mapping key="offerPrepaid" value="GlobalVars.offerPrepaid  != undefined ? GlobalVars.offerPrepaid : false" type="String"/>
      <session-mapping key="collection_dtmfgrammar1" value="PI1015_GetPaymentMethod_DM_dtmf.jsp" type="String"/>
      <session-mapping key="collection_grammar1" value="PI1015_GetPaymentMethod_DM.jsp" type="String"/>
      <success>
        <action label="credit">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'credit'"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'credit'"/>
          <if cond="GlobalVars.callType == 'activate'">
            <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'83'"/>
            <action next="PI1039_CheckCallType_JDA"/>
            <else>
              <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'104'"/>
              <action next="PI1039_CheckCallType_JDA"/>
            </else>
          </if>
        </action>
        <action label="debit">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'debit'"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'debit'"/>
          <if cond="GlobalVars.callType == 'activate'">
            <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'83'"/>
            <action next="PI1039_CheckCallType_JDA"/>
            <else>
              <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'104'"/>
              <action next="PI1039_CheckCallType_JDA"/>
            </else>
          </if>
        </action>
        <action label="prepaid">
          <session-mapping key="GlobalVars.payingWithPrepaid" value="true" type="Boolean"/>
          <audio>
            <prompt id="PI1015_out_01">
              <prompt-segments>
                <audiofile text="Okay" src="PI1015_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="PI1016_PrepaidCard_SD"/>
        </action>
        <action label="wait">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
          <action next="PI1010_FindCardWaitSBI_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="fromAnotherPaymentMenu == false">
                <prompt id="PI1015_ini_04" cond="offerPrepaid == true">
                  <prompt-segments>
                    <audiofile text="We take Metro  Payment PINs and Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel We *don't* take Discover cards" src="PI1015_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="PI1015_ini_03" cond="offerPrepaid == false">
                  <prompt-segments>
                    <audiofile text="We take Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel We dont take Discover cards " src="PI1015_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_ini_02">
                <prompt-segments>
                  <audiofile text="If you need time, say wait a minute " src="PI1015_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_ini_05" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="If you're ready, say 'Payment PIN', 'credit', or 'debit' If you want to use a payment PIN AND a bank card, say 'Payment PIN' and we'll take care of that first " src="PI1015_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_ini_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="If you're ready, say 'credit', or 'debit' " src="PI1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="PI1015_GetPaymentMethod_DM_reinvoke"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PI1015_rin_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Is it a credit card debit card or  Payment PIN? " src="PI1015_rin_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_rin_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="Is it a *credit* card, or a *debit* card? " src="PI1015_rin_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2">
            <audio>
              <prompt id="PI1015_rin_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Is it a credit card debit card or  Payment PIN? " src="PI1015_rin_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_rin_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="Is it a *credit* card, or a *debit* card? " src="PI1015_rin_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="3">
            <audio>
              <prompt id="PI1015_rin_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Is it a credit card debit card or  Payment PIN? " src="PI1015_rin_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_rin_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="Is it a *credit* card, or a *debit* card? " src="PI1015_rin_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="4">
            <audio>
              <prompt id="PI1015_rin_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Is it a credit card debit card or  Payment PIN? " src="PI1015_rin_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_rin_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="Is it a *credit* card, or a *debit* card? " src="PI1015_rin_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PI1015_nm1_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Do you want to pay with a 'Payment PIN', a *credit* card or a *debit* card? If you need some time to find it, say 'wait a minute' If you want to use both a payment PIN and a bank card, say 'Payment PIN' and we'll do that one first" src="PI1015_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_nm1_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="Do you want to pay with *credit*  or *debit*? If you need some time to find your card, say 'wait a minute' " src="PI1015_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="PI1015_nm2_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Please say 'Payment PIN' or press 1, 'credit' or press 2, 'debit' - 3, or  'wait a minute' - 4" src="PI1015_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_nm2_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="Please say 'credit' or press 1, 'debit' or press 2,or 'wait a minute' or press 3 " src="PI1015_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="PI1015_nm3_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="To use a Payment PIN, press 1 For credit, press 2 For debit, press 3 If you have a card that has both credit and debit, just choose the method you prefer If you want me to wait while you look, press 4" src="PI1015_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_nm3_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="If you have a card that has both credit and debit, just choose the method you prefer For credit, press 1 For debit, press 2 If you want me to wait while you look, press 3 " src="PI1015_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_nm1_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Do you want to pay with a 'Payment PIN', a *credit* card or a *debit* card? If you need some time to find it, say 'wait a minute' If you want to use both a payment PIN and a bank card, say 'Payment PIN' and we'll do that one first" src="PI1015_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_nm1_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="Do you want to pay with *credit*  or *debit*? If you need some time to find your card, say 'wait a minute' " src="PI1015_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_nm2_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Please say 'Payment PIN' or press 1, 'credit' or press 2, 'debit' - 3, or  'wait a minute' - 4" src="PI1015_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_nm2_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="Please say 'credit' or press 1, 'debit' or press 2,or 'wait a minute' or press 3 " src="PI1015_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_nm3_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="To use a Payment PIN, press 1 For credit, press 2 For debit, press 3 If you have a card that has both credit and debit, just choose the method you prefer If you want me to wait while you look, press 4" src="PI1015_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_nm3_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="If you have a card that has both credit and debit, just choose the method you prefer For credit, press 1 For debit, press 2 If you want me to wait while you look, press 3 " src="PI1015_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PI1015_rin_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Is it a credit card debit card or  Payment PIN? " src="PI1015_rin_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PI1015_rin_01" cond="offerPrepaid == false">
                <prompt-segments>
                  <audiofile text="Is it a *credit* card, or a *debit* card? " src="PI1015_rin_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="PI1015_GetPaymentMethod_DM.jsp" count="1"/>
          <dtmfgrammars filename="PI1015_GetPaymentMethod_DM_dtmf.jsp" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="PI1025_GoTo_BankCardDetails_SD">
      <gotodialog next="BankCard_Details_Dialog"/>
      <action next="PI1025_GoTo_BankCardDetails_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PI1025_GoTo_BankCardDetails_SD_return_CS">
      <if cond="(GlobalVars.tryOtherCardReason == '') ||((GlobalVars.tryOtherCardReason == undefined) || (GlobalVars.tryOtherCardReason == 'undefined')) ||(GlobalVars.tryOtherCardReason == 'fail_authorization') ||(GlobalVars.ValidateCardOptions &amp;&amp; GlobalVars.ValidateCardOptions.cardStatus == 'amount_under_min') ||(GlobalVars.ValidateCardOptions &amp;&amp; GlobalVars.ValidateCardOptions.cardStatus == 'amount_over_max') ||(GlobalVars.authorizationFailureHandling == 'sameAmountDeclined') ||(GlobalVars.activationResult == 'goodbye')">
        <action next="getReturnLink()"/>
        <else>
          <action next="PI1001_CheckContext_JDA"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="PI1030_GoTo_ErrorHandling_SD">
      <gotodialog next="ErrorHandlingPayment_Dialog"/>
      <action next="PI1030_GoTo_ErrorHandling_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PI1030_GoTo_ErrorHandling_SD_return_CS">
      <if cond="(GlobalVars.callType == 'goodbye' || GlobalVars.activationResult == 'goodbye')">
        <action next="getReturnLink()"/>
        <else>
          <action next="PI1015_GetPaymentMethod_DM"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="PI1035_UseWallet_SD">
      <gotodialog next="UseWallet_Main_Dialog"/>
      <action next="PI1035_UseWallet_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PI1035_UseWallet_SD_return_CS">
      <if cond="GlobalVars.payingWithEWallet == true">
        <action next="PI1039_CheckCallType_JDA"/>
        <else>
          <if cond="GlobalVars.abandonEWalletAuth == true">
            <action next="PI1015_GetPaymentMethod_DM"/>
            <elseif cond="GlobalVars.authFailure == true || GlobalVars.callType == 'goodbye'">
              <action next="getReturnLink()"/>
            </elseif>
            <else>
              <action next="PI1015_GetPaymentMethod_DM"/>
            </else>
          </if>
        </else>
      </if>
    </custom-state>

    <play-state id="PI1040_PlayBankCardFee_PP">
      <session-mapping key="acceptedBCR" value="GlobalVars.acceptedBCR" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet" type="String"/>
      <session-mapping key="walletConvenienceFee" value="GlobalVars.GetConvenienceFee.walletConvenienceFee" type="String"/>
      <session-mapping key="cardConvenienceFee" value="GlobalVars.GetConvenienceFee.cardConvenienceFee" type="String"/>
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount" type="String"/>
      <session-mapping key="paymentErrorCount" value="GlobalVars.paymentErrorCount" type="String"/>
      <session-mapping key="paymentWithWalletTotal" value="(parseFloat(GlobalVars.GetConvenienceFee.walletConvenienceFee) + parseFloat(GlobalVars.paymentAmount)).toString()" type="String"/>
      <session-mapping key="paymentWithCardTotal" value="(parseFloat(GlobalVars.GetConvenienceFee.cardConvenienceFee) + parseFloat(GlobalVars.paymentAmount)).toString()" type="String"/>
      <session-mapping key="care_convenience_fees_threshold_amount" value="GlobalVars.GetBCSParameters.care_convenience_fees_threshold_amount" type="String"/>
      <audio>
        <prompt id="PI1040_out_02" cond="((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 0) || (parseFloat(paymentAmount) &lt;= care_convenience_fees_threshold_amount))">
          <prompt-segments>
            <audiofile text="There ll be no service fee for this payment" src="PI1040_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1040_out_03" cond="(!(parseFloat(paymentAmount) &lt;= care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 1) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 1)))">
          <prompt-segments>
            <audiofile text="There ll be a $1 service fee" src="PI1040_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1040_out_04" cond="(!(parseFloat(paymentAmount) &lt;= care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 2) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 2)))">
          <prompt-segments>
            <audiofile text="There ll be a $2 service fee" src="PI1040_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1040_out_05" cond="(!(parseFloat(paymentAmount) &lt;= care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 3) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 3)))">
          <prompt-segments>
            <audiofile text="There ll be a $3 service fee" src="PI1040_out_05.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1040_out_06" cond="!(((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 0)) || ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 1) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 1)) || ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 2) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 2)) || ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 3) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 3))) &amp;&amp; (paymentAmount &gt; care_convenience_fees_threshold_amount)">
          <prompt-segments>
            <audiofile text="There'll be a service fee of " src="PI1040_out_06.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="currency" expr="walletConvenienceFee" cond="(payingWithEWallet == true &amp;&amp; !(((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 0)) || ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 1) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 1)) || ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 2) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 2)) || ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 3) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 3)))) &amp;&amp; (paymentAmount &gt; care_convenience_fees_threshold_amount)">
          <param value="f" name="intonation"/>
          <param value="false" name="playZeroCents"/>
        </prompt>
        <prompt type="currency" expr="cardConvenienceFee" cond="(payingWithEWallet != true &amp;&amp; !(((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 0)) || ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 1) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 1)) || ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 2) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 2)) || ((payingWithEWallet == true &amp;&amp; walletConvenienceFee == 3) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee == 3)))) &amp;&amp; (paymentAmount &gt; care_convenience_fees_threshold_amount)">
          <param value="f" name="intonation"/>
          <param value="false" name="playZeroCents"/>
        </prompt>
        <prompt id="silence_250ms" cond="acceptedBCR == true">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1040_out_07" cond="acceptedBCR == true">
          <prompt-segments>
            <audiofile text="So your payment comes to " src="PI1040_out_07.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="currency" expr="paymentWithWalletTotal" cond="acceptedBCR == true &amp;&amp; payingWithEWallet == true">
          <param value="f" name="intonation"/>
          <param value="false" name="playZeroCents"/>
        </prompt>
        <prompt type="currency" expr="paymentWithCardTotal" cond="acceptedBCR == true &amp;&amp; payingWithEWallet != true">
          <param value="f" name="intonation"/>
          <param value="false" name="playZeroCents"/>
        </prompt>
        <prompt id="silence_250ms" cond="paymentErrorCount == 0 &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee != 0 &amp;&amp; parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee != 0 &amp;&amp; parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount)) &amp;&amp; callType != 'resetbillcycle'">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PI1040_out_08" cond="paymentErrorCount == 0 &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee != 0 &amp;&amp; parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee != 0 &amp;&amp; parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount)) &amp;&amp; callType != 'resetbillcycle'">
          <prompt-segments>
            <audiofile text="You can also pay for free with the myMetro app on your phone,or online at metrobyt-mobilecom " src="PI1040_out_08.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="GlobalVars.payingWithEWallet == true">
        <action next="PI1025_GoTo_BankCardDetails_SD"/>
        <else>
          <action next="PI1025_GoTo_BankCardDetails_SD"/>
        </else>
      </if>
    </play-state>

    <subdialog-state id="PI1016_PrepaidCard_SD">
      <gotodialog next="PrepaidCardPIN_Dialog"/>
      <action next="PI1016_PrepaidCard_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PI1016_PrepaidCard_SD_return_CS">
      <if cond="GlobalVars.payingWithPrepaid == false &amp;&amp; GlobalVars.callType != undefined &amp;&amp; GlobalVars.callType != '' &amp;&amp; GlobalVars.activationResult != 'transfer'">
        <action next="PI1039_CheckCallType_JDA"/>
        <elseif cond="GlobalVars.needResidualPayment == true">
          <action next="PI1010_FindCardWaitSBI_DM"/>
        </elseif>
        <else>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </custom-state>

    <decision-state id="PI1039_CheckCallType_DS">
      <if cond="GlobalVars.callType ==  'activate'">
        <action next="PI1025_GoTo_BankCardDetails_SD"/>
        <else>
          <action next="PI1040_PlayBankCardFee_PP"/>
        </else>
      </if>
    </decision-state>

  </dialog>
  