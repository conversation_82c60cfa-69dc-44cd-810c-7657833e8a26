memorize below yaml format
- kind: SendActivity
  id: Play_Welcome_PP
  displayName: Play_Welcome_PP
  activity:
    text:
      - This is dvxml.
    speak:
     - This is dvxml.

      
also use these instruction while conversion 
1) kind should always be "SendActivity"  
2) replace "id" and "displayName" value with "dvxml" tag id.
3) indentation for next yaml should be same as first one or as memorized yaml
4) do not provide duplicate yaml.. generate only for provided inputs.

### Example 1:
**Input XML:**
```xml
<dvxml xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="InvokeCS.dvxml"/>

```

**Output yaml:**
```yaml
- kind: SendActivity
  id: InvokeCS
  displayName: InvokeCS
  activity:
    text:
      - This is dvxml.
    speak:
      - This is dvxml.

```
