<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="FAQ_Main_Dialog">
    <decision-state id="FQ1001_CheckContext_DS">
      <if cond="(GlobalVars.tag == 'faq-voicemail')">
        <session-mapping key="GlobalVars.faq" expr="'voicemail_help'"/>
        <elseif cond="(GlobalVars.tag == 'faq-payment_methods')">
          <session-mapping key="GlobalVars.faq" expr="'ways_to_pay'"/>
        </elseif>
      </if>
      <if cond="(GlobalVars.tag == 'faq-voicemail' || GlobalVars.tag == 'faq-payment_methods')">
        <action next="FQ1025_ReadAnswers_PP"/>
        <else>
          <action next="FQ1005_NarrowDownFAQTopic_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="FQ1005_NarrowDownFAQTopic_DM" type="CUST">
      <session-mapping key="transferDestination" value="GlobalVars.GetBCSParameters.care_troubleshoot_by_design_dest" type="String"/>
      <session-mapping key="saidNoneOfThose" value="GlobalVars.saidNoneOfThose" type="String"/>
      <session-mapping key="reentry" expr="GlobalVars.FQ1005reentry"/>
      <success>
        <session-mapping key="GlobalVars.FQ1005reentry" value="true" type="Boolean"/>
        <action label="billing_and_payments" next="FQ1010_BillingPayments_DM">
          <session-mapping key="GlobalVars.saidNoneOfThose" value="false" type="Boolean"/>
        </action>
        <action label="voicemail" next="FQ1025_ReadAnswers_PP">
          <session-mapping key="GlobalVars.faq" expr="'voicemail_help'"/>
          <session-mapping key="GlobalVars.saidNoneOfThose" value="false" type="Boolean"/>
        </action>
        <action label="coverage" next="FQ1025_ReadAnswers_PP">
          <session-mapping key="GlobalVars.faq" expr="'metro_coverage'"/>
          <session-mapping key="GlobalVars.saidNoneOfThose" value="false" type="Boolean"/>
        </action>
        <action label="international_calling" next="FQ1025_ReadAnswers_PP">
          <session-mapping key="GlobalVars.faq" expr="'international_calling'"/>
          <session-mapping key="GlobalVars.saidNoneOfThose" value="false" type="Boolean"/>
        </action>
        <action label="go_back">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.saidNoneOfThose" value="false" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="inquire-store_location">
          <session-mapping key="GlobalVars.callType" expr="'find_store'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'unknown'"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <action next="FQ1040_GoToStoreLocator_SD"/>
        </action>
        <action label="my_device">
          <session-mapping key="GlobalVars.callType" expr="'device_handling'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.saidNoneOfThose" value="false" type="Boolean"/>
          <action next="FQ1037_GoToDeviceHandling_SD"/>
        </action>
        <action label="none_of_those">
          <audio>
            <prompt id="FQ1005_out_01">
              <prompt-segments>
                <audiofile text="Sure" src="FQ1005_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="transferDestination == 'contain'">
            <session-mapping key="GlobalVars.saidNoneOfThose" value="true" type="Boolean"/>
            <action next="FQ1005_NarrowDownFAQTopic_DM"/>
            <else>
              <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
              <action next="FQ1035_GoToCallTransfer_SD"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars count="1" filename="FQ1005_NarrowDownFAQTopic_DM.grxml"/>
          <dtmfgrammars count="1" filename="FQ1005_NarrowDownFAQTopic_DM_dtmf.grxml"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="FQ1005_ini_02" cond="!reentry">
                <prompt-segments>
                  <audiofile text="Here are the other areas where I can help You can say 'Payments,' 'Voicemail Help,' 'Nationwide Coverage,' 'International Calling' or 'My phone or tablet'  And you can say 'it's also none of those'" src="FQ1005_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="FQ1005_NarrowDownFAQTopic_DM_reentry"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="FQ1005_ini_02" cond="!reentry">
                <prompt-segments>
                  <audiofile text="Here are the other areas where I can help You can say 'Payments,' 'Voicemail Help,' 'Nationwide Coverage,' 'International Calling' or 'My phone or tablet'  And you can say 'it's also none of those'" src="FQ1005_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="FQ1005_nm1_02">
                <prompt-segments>
                  <audiofile text="Here are the other areas I can help you with You can say 'Payments,' 'Voicemail Help,' 'Nationwide Coverage,' 'International Calling,' or 'My phone or tablet'  You can also say 'it's also none of those' or 'find a store'" src="FQ1005_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1005_nm2_02">
                <prompt-segments>
                  <audiofile text="Please say 'Payments' or press one You can also say 'Voicemail Help' or press two  'Nationwide Coverage' - three  'International Calling' - four For help with your device, say 'My phone or tablet' or press five  You can also say 'it's none of those' - six; or 'Find a store,' - seven To go back to the main menu, say 'go back' or press star" src="FQ1005_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1005_nm2_02">
                <prompt-segments>
                  <audiofile text="Please say 'Payments' or press one You can also say 'Voicemail Help' or press two  'Nationwide Coverage' - three  'International Calling' - four For help with your device, say 'My phone or tablet' or press five  You can also say 'it's none of those' - six; or 'Find a store,' - seven To go back to the main menu, say 'go back' or press star" src="FQ1005_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1005_nm1_02">
                <prompt-segments>
                  <audiofile text="Here are the other areas I can help you with You can say 'Payments,' 'Voicemail Help,' 'Nationwide Coverage,' 'International Calling,' or 'My phone or tablet'  You can also say 'it's also none of those' or 'find a store'" src="FQ1005_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1005_nm2_02">
                <prompt-segments>
                  <audiofile text="Please say 'Payments' or press one You can also say 'Voicemail Help' or press two  'Nationwide Coverage' - three  'International Calling' - four For help with your device, say 'My phone or tablet' or press five  You can also say 'it's none of those' - six; or 'Find a store,' - seven To go back to the main menu, say 'go back' or press star" src="FQ1005_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1005_nm2_02">
                <prompt-segments>
                  <audiofile text="Please say 'Payments' or press one You can also say 'Voicemail Help' or press two  'Nationwide Coverage' - three  'International Calling' - four For help with your device, say 'My phone or tablet' or press five  You can also say 'it's none of those' - six; or 'Find a store,' - seven To go back to the main menu, say 'go back' or press star" src="FQ1005_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="FQ1005_rin_01" cond="saidNoneOfThose == true">
                <prompt-segments>
                  <audiofile src="FQ1005_rin_01.wav" text="For more help, you can bring your device to a Metro  store or authorized dealer You can say  find a store  or if you re finished, you can just hang up"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1005_ree_02" cond="saidNoneOfThose != true">
                <prompt-segments>
                  <audiofile text="Which category can I help you with?  You can say 'Payments,' 'Voicemail Help,' 'Nationwide Coverage,' 'International Calling' or 'My phone or tablet'  And you can say 'it's also none of those'" src="FQ1005_ree_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="FQ1010_BillingPayments_DM" type="CUST">
      <success>
        <action label="billing_info" next="FQ1025_ReadAnswers_PP">
          <session-mapping key="GlobalVars.faq" expr="'billing_info'"/>
        </action>
        <action label="ways_to_pay" next="FQ1025_ReadAnswers_PP">
          <session-mapping key="GlobalVars.faq" expr="'ways_to_pay'"/>
        </action>
        <action label="cost_of_changing_plans" next="FQ1025_ReadAnswers_PP">
          <session-mapping key="GlobalVars.faq" expr="'cost_of_changing_plans'"/>
        </action>
        <action label="cost_of_adding_features" next="FQ1025_ReadAnswers_PP">
          <session-mapping key="GlobalVars.faq" expr="'cost_of_adding_features'"/>
        </action>
        <action label="none_of_those">
          <action next="FQ1035_GoToCallTransfer_SD"/>
        </action>
        <action label="go_back">
          <action next="FQ1005_NarrowDownFAQTopic_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars count="1" filename="FQ1010_BillingPayments_DM.grxml"/>
          <dtmfgrammars count="1" filename="FQ1010_BillingPayments_DM_dtmf.grxml"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="FQ1010_ini_01">
                <prompt-segments>
                  <audiofile text="Say 'Statements', 'Ways to Pay', 'Cost of Changing Plans' or 'Cost of Adding Features'" src="FQ1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_3000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_3000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1010_ini_03">
                <prompt-segments>
                  <audiofile text="Or say  it s none of those  or, to go back and choose a different category, say  go back " src="FQ1010_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="FQ1010_ini_01">
                <prompt-segments>
                  <audiofile text="Say 'Statements', 'Ways to Pay', 'Cost of Changing Plans' or 'Cost of Adding Features'" src="FQ1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_3000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_3000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1010_ini_03">
                <prompt-segments>
                  <audiofile text="Or say  it s none of those  or, to go back and choose a different category, say  go back " src="FQ1010_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="FQ1010_nm1_01">
                <prompt-segments>
                  <audiofile text="Which would you like help with please say  'Statements', 'Ways to Pay', 'Cost of Changing Plans', 'Cost of Adding Features' or 'it's none of those'" src="FQ1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1010_nm2_01">
                <prompt-segments>
                  <audiofile text="For help understanding your statement and due date, say 'statement' or press one To hear about payment methods, say 'Ways to Pay', or press two To find out how changing plans will affect your charges, say 'Cost of Changing Plans' or press three For how adding features will affect your charges, say 'Cost of Adding Features' or press four  If none of those topics will help you, say 'none of those' or press five  Or say 'go back' or press star to go back and choose a different category" src="FQ1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1010_nm2_01">
                <prompt-segments>
                  <audiofile text="For help understanding your statement and due date, say 'statement' or press one To hear about payment methods, say 'Ways to Pay', or press two To find out how changing plans will affect your charges, say 'Cost of Changing Plans' or press three For how adding features will affect your charges, say 'Cost of Adding Features' or press four  If none of those topics will help you, say 'none of those' or press five  Or say 'go back' or press star to go back and choose a different category" src="FQ1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1010_nm1_01">
                <prompt-segments>
                  <audiofile text="Which would you like help with please say  'Statements', 'Ways to Pay', 'Cost of Changing Plans', 'Cost of Adding Features' or 'it's none of those'" src="FQ1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1010_nm2_01">
                <prompt-segments>
                  <audiofile text="For help understanding your statement and due date, say 'statement' or press one To hear about payment methods, say 'Ways to Pay', or press two To find out how changing plans will affect your charges, say 'Cost of Changing Plans' or press three For how adding features will affect your charges, say 'Cost of Adding Features' or press four  If none of those topics will help you, say 'none of those' or press five  Or say 'go back' or press star to go back and choose a different category" src="FQ1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1010_nm2_01">
                <prompt-segments>
                  <audiofile text="For help understanding your statement and due date, say 'statement' or press one To hear about payment methods, say 'Ways to Pay', or press two To find out how changing plans will affect your charges, say 'Cost of Changing Plans' or press three For how adding features will affect your charges, say 'Cost of Adding Features' or press four  If none of those topics will help you, say 'none of those' or press five  Or say 'go back' or press star to go back and choose a different category" src="FQ1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="FQ1010_ini_01">
                <prompt-segments>
                  <audiofile text="Say 'Statements', 'Ways to Pay', 'Cost of Changing Plans' or 'Cost of Adding Features'" src="FQ1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_3000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_3000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1010_ini_03">
                <prompt-segments>
                  <audiofile text="Or say  it s none of those  or, to go back and choose a different category, say  go back " src="FQ1010_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="FQ1025_ReadAnswers_PP">
      <audio>
        <prompt id="FQ1025_ini_01" cond="GlobalVars.faq == 'billing_info'">
          <prompt-segments>
            <audiofile src="FQ1025_ini_01.wav" text="Your monthly statement includes your base rate plan, and any additional features on your account Depending on your plan and the state you live in, there may also be taxes and fees To maintain your Metro service, you need to make a payment by your monthly due date Otherwise, your service will be suspended"/>
          </prompt-segments>
        </prompt>
        <prompt id="FQ1025_ini_03" cond="GlobalVars.faq == 'ways_to_pay'">
          <prompt-segments>
            <audiofile text="The easiest way to pay is to sign up for Auto-Pay, which will take an automatic payment from your designated credit or debit card every month You can also pay over the phone with this system using your credit or debit card Or through the myMetro app on your phone To pay online, go to metrobyt-mobilecom Or visit us at an authorized payment center!" src="FQ1025_ini_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="FQ1025_ini_04" cond="GlobalVars.faq == 'cost_of_changing_plans'">
          <prompt-segments>
            <audiofile text="You can change your plan at any time, without a fee But there will be prorated charges on your first month, for example if you change to a more expensive plan than you have now " src="FQ1025_ini_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="FQ1025_ini_05" cond="GlobalVars.faq == 'cost_of_adding_features'">
          <prompt-segments>
            <audiofile text="When you add a feature, your charges are not prorated, which means your next statement will include your regular charges plus the full cost of the feature for the current month and the next month" src="FQ1025_ini_05.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="FQ1025_ini_06" cond="GlobalVars.faq == 'voicemail_help'">
          <prompt-segments>
            <audiofile text="There are two possible ways to access your voicemail After you hang up our call, you can dial one or you can dial your own phone number and press star during the greeting When asked for a PIN, use the temporary PIN, which will be the last four digits of your phone number -- and then follow the instructions Once you set up your voicemail, change your PIN to something that s unique to you! " src="FQ1025_ini_06.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="FQ1025_ini_10" cond="GlobalVars.faq == 'metro_coverage'">
          <prompt-segments>
            <audiofile src="FQ1025_ini_10.wav" text="Metro s nationwide coverage is constantly being updated Visit metrobyt-mobilecomcoverage for the latest map, to get coverage details for specific locations, and for tips on optimizing your coverage"/>
          </prompt-segments>
        </prompt>
        <prompt id="FQ1025_ini_12" cond="GlobalVars.faq == 'update_roaming_list'">
          <prompt-segments>
            <audiofile text="For the best coverage when roaming, make sure to update the list of preferred roaming partners on your phone You can do this by dialing star 228 while in your local calling area and selecting  Roaming list  Our Roaming Partner s networks are continuously expanding, so be sure to update the list often" src="FQ1025_ini_12.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="7640ou13_ReadAnswers" cond="GlobalVars.faq == 'international_calling'">
          <prompt-segments>
            <audiofile text="World Calling allows you to dial directly to over 100 countries Coverage depends on the country and carrier To check if the number you want to dial is covered, go to metrobyt-mobilecom and click  services  This page is updated often, so be sure to check it regularly for any number you want to dial" src="7640ou13_ReadAnswers.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="FQ1030_RepeatOrAskAnotherQuestion_DM"/>
    </play-state>

    <dm-state id="FQ1030_RepeatOrAskAnotherQuestion_DM" type="CUST">
      <success>
        <action label="help_me_out">
          <action next="FQ1005_NarrowDownFAQTopic_DM"/>
        </action>
        <action label="main_menu">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="repeat">
          <action next="FQ1025_ReadAnswers_PP"/>
        </action>
        <action label="repeat">
          <action next="FQ1025_ReadAnswers_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars count="1" filename="FQ1030_RepeatOrAskAnotherQuestion_DM.grxml"/>
          <dtmfgrammars count="1" filename="FQ1030_RepeatOrAskAnotherQuestion_DM_dtmf.grxml"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="FQ1030_ini_01">
                <prompt-segments>
                  <audiofile text="Say  repeat that ,  help me with something else  or  main menu " src="FQ1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_3000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_3000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1030_ini_03">
                <prompt-segments>
                  <audiofile text="If you re done, just hang up   And, if you need more help, you might want to check metrobyt-mobilecom" src="FQ1030_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="FQ1030_nm1_01">
                <prompt-segments>
                  <audiofile text="To hear the information you just heard again, say  repeat that  or say  help with something else  or  main menu " src="FQ1030_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1030_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear the information for that help topic again, say  repeat  or press one  To choose another topic, say  help me with something else  or press two  Or say  main menu  or press three, to return to the Main Menu" src="FQ1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1030_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear the information for that help topic again, say  repeat  or press one  To choose another topic, say  help me with something else  or press two  Or say  main menu  or press three, to return to the Main Menu" src="FQ1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1030_nm1_01">
                <prompt-segments>
                  <audiofile text="To hear the information you just heard again, say  repeat that  or say  help with something else  or  main menu " src="FQ1030_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1030_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear the information for that help topic again, say  repeat  or press one  To choose another topic, say  help me with something else  or press two  Or say  main menu  or press three, to return to the Main Menu" src="FQ1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1030_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear the information for that help topic again, say  repeat  or press one  To choose another topic, say  help me with something else  or press two  Or say  main menu  or press three, to return to the Main Menu" src="FQ1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="FQ1030_ini_01">
                <prompt-segments>
                  <audiofile text="Say  repeat that ,  help me with something else  or  main menu " src="FQ1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_3000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_3000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="FQ1030_ini_03">
                <prompt-segments>
                  <audiofile text="If you re done, just hang up   And, if you need more help, you might want to check metrobyt-mobilecom" src="FQ1030_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="FQ1035_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="FQ1035_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="FQ1035_GoToCallTransfer_SD_return_CS">
      <session-mapping key="GlobalVars.mainMenuFromAnythingElse" value="true" type="Boolean"/>
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="FQ1037_GoToDeviceHandling_SD">
      <gotodialog next="DeviceHandling_Main_Dialog"/>
      <action next="FQ1037_GoToDeviceHandling_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="FQ1037_GoToDeviceHandling_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="FQ1040_GoToStoreLocator_SD">
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="FQ1040_GoToStoreLocator_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="FQ1040_GoToStoreLocator_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

  </dialog>
  