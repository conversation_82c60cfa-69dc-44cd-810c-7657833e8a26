<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="SwitchLines_Main_Dialog">
    <decision-state id="SW1001_CheckContext_DS">
      <session-mapping key="accountNumber" value="GlobalVars.GetAccountDetails.accountNumber" type="String"/>
      <session-mapping key="GlobalVars.collectedPINANI" expr="''"/>
      <session-mapping key="GlobalVars.collectedPINNewLine" expr="''"/>
      <session-mapping key="GlobalVars.switchLinesMDNAttempts" expr="0"/>
      <session-mapping key="GlobalVars.switchLinesPINAttempts" expr="0"/>
      <session-mapping key="GlobalVars.requestedMDNChangeSwitchLines" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.originalLine_accountNumber" expr="GlobalVars.accountNumber"/>
      <action next="SW1005_CheckSwitchLinesAllowed_JDA"/>
    </decision-state>

    <decision-state id="SW1005_CheckSwitchLinesAllowed_DS">
      <if cond="(GlobalVars.aniMatch == true || GlobalVars.aniMatch == 'true') &amp;&amp; GlobalVars.switchLinesSuccess == false &amp;&amp; (GlobalVars.GetBCSParameters.care_enable_switch_lines == true || GlobalVars.GetBCSParameters.care_enable_switch_lines == 'true')">
        <session-mapping key="GlobalVars.askSQEntry" expr="'careLogin'"/>
        <session-mapping key="GlobalVars.callType" expr="'switch_lines'"/>
        <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        <action next="SW1010_CheckNeedPIN_JDA"/>
        <else>
          <gotodialog next="SwitchLines_Security#SW1505_SwitchNotAllowedReason_DS"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="SW1010_CheckNeedPIN_DS">
      <if cond="GlobalVars.loggedIn == true">
        <session-mapping key="GlobalVars.loggedIn" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.originalLine_loggedIn" value="true" type="Boolean"/>
        <action next="SW1105_GetNewLineMDN_DM"/>
        <else>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <action next="SW1015_GetOriginalLinePIN_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="SW1015_GetOriginalLinePIN_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="SW1015_GetOriginalLinePIN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SW1015_GetOriginalLinePIN_SD_return_CS">
      <session-mapping key="GlobalVars.loggedIn" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.originalLine_loggedIn" value="true" type="Boolean"/>
      <action next="SW1020_SetPINValue_JDA"/>
    </custom-state>

    <decision-state id="SW1020_SetPINValue_DS">
      <session-mapping key="GlobalVars.collectedPINANI" expr="securityCode"/>
      <action next="SW1025_PlayThanks_PP"/>
    </decision-state>

    <play-state id="SW1025_PlayThanks_PP">
      <audio>
        <prompt id="SW1025_out_01">
          <prompt-segments>
            <audiofile text="Thanks!" src="SW1025_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SW1105_GetNewLineMDN_DM"/>
    </play-state>

    <dm-state id="SW1105_GetNewLineMDN_DM" type="CUST">
      <session-mapping key="requestedMDNChangeSwitchLines" value="GlobalVars.requestedMDNChangeSwitchLines" type="String"/>
      <session-mapping key="switchLinesMDNAttempts" value="GlobalVars.switchLinesMDNAttempts" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <success>
        <action label="default">
          <session-mapping key="GlobalVars.newLine_MDN" expr="SW1105_GetNewLineMDN_DM.returnvalue"/>
          <action next="SW1110_GetAccountDetails_DB_DA"/>
        </action>
        <action label="dont_know">
          <audio>
            <prompt id="SW1105_out_01">
              <prompt-segments>
                <audiofile text="No problem" src="SW1105_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <gotodialog next="SwitchLines_Security#SW1505_SwitchNotAllowedReason_DS"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="switchLinesMDNAttempts == 0">
                <prompt id="SW1105_ini_01" cond="requestedMDNChangeSwitchLines == true">
                  <prompt-segments>
                    <audiofile text="Enter that phone number on your keypad now" src="SW1105_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SW1105_ini_05" cond="requestedMDNChangeSwitchLines != true &amp;&amp; (tag == 'reactivate_phone_active' || tag == 'reactivate-old_account')">
                  <prompt-segments>
                    <audiofile text="What's the phone number you'd like to reactivate?" src="SW1105_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SW1105_ini_02" cond="requestedMDNChangeSwitchLines != true &amp;&amp; !(tag == 'reactivate_phone_active' || tag == 'reactivate-old_account')">
                  <prompt-segments>
                    <audiofile text="Now, what's the phone number you want to *work* with? " src="SW1105_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="switchLinesMDNAttempts == 1">
                  <prompt id="SW1105_ini_03">
                    <prompt-segments>
                      <audiofile text="I can't seem to find that Please try again using your keypad " src="SW1105_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="SW1105_ini_04">
                    <prompt-segments>
                      <audiofile text="Go ahead and enter the phone number you want to work with " src="SW1105_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="switchLinesMDNAttempts == 0">
                <prompt id="SW1105_ini_01" cond="requestedMDNChangeSwitchLines == true">
                  <prompt-segments>
                    <audiofile text="Enter that phone number on your keypad now" src="SW1105_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SW1105_ini_05" cond="requestedMDNChangeSwitchLines != true &amp;&amp; (tag == 'reactivate_phone_active' || tag == 'reactivate-old_account')">
                  <prompt-segments>
                    <audiofile text="What's the phone number you'd like to reactivate?" src="SW1105_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SW1105_ini_02" cond="requestedMDNChangeSwitchLines != true &amp;&amp; !(tag == 'reactivate_phone_active' || tag == 'reactivate-old_account')">
                  <prompt-segments>
                    <audiofile text="Now, what's the phone number you want to *work* with? " src="SW1105_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="switchLinesMDNAttempts == 1">
                  <prompt id="SW1105_ini_03">
                    <prompt-segments>
                      <audiofile text="I can't seem to find that Please try again using your keypad " src="SW1105_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="SW1105_ini_04">
                    <prompt-segments>
                      <audiofile text="Go ahead and enter the phone number you want to work with " src="SW1105_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SW1105_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the phone number for the account you want to work with, including the area code Or say 'I don't know it'" src="SW1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SW1105_nm2_01">
                <prompt-segments>
                  <audiofile text="On your keypad, enter the phone number you want to work with If you don't know it, press star" src="SW1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SW1105_nm2_01">
                <prompt-segments>
                  <audiofile text="On your keypad, enter the phone number you want to work with If you don't know it, press star" src="SW1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SW1105_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the phone number for the account you want to work with, including the area code Or say 'I don't know it'" src="SW1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SW1105_nm2_01">
                <prompt-segments>
                  <audiofile text="On your keypad, enter the phone number you want to work with If you don't know it, press star" src="SW1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SW1105_nm2_01">
                <prompt-segments>
                  <audiofile text="On your keypad, enter the phone number you want to work with If you don't know it, press star" src="SW1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="switchLinesMDNAttempts == 0">
                <prompt id="SW1105_ini_01" cond="requestedMDNChangeSwitchLines == true">
                  <prompt-segments>
                    <audiofile text="Enter that phone number on your keypad now" src="SW1105_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SW1105_ini_05" cond="requestedMDNChangeSwitchLines != true &amp;&amp; (tag == 'reactivate_phone_active' || tag == 'reactivate-old_account')">
                  <prompt-segments>
                    <audiofile text="What's the phone number you'd like to reactivate?" src="SW1105_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SW1105_ini_02" cond="requestedMDNChangeSwitchLines != true &amp;&amp; !(tag == 'reactivate_phone_active' || tag == 'reactivate-old_account')">
                  <prompt-segments>
                    <audiofile text="Now, what's the phone number you want to *work* with? " src="SW1105_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="switchLinesMDNAttempts == 1">
                  <prompt id="SW1105_ini_03">
                    <prompt-segments>
                      <audiofile text="I can't seem to find that Please try again using your keypad " src="SW1105_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="SW1105_ini_04">
                    <prompt-segments>
                      <audiofile text="Go ahead and enter the phone number you want to work with " src="SW1105_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SW1105_GetNewLineMDN_DM.grxml" count="1"/>
          <dtmfgrammars filename="SW1105_GetNewLineMDN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration>
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="IF_NECESSARY" highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="SW1110_GetAccountDetails_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.newLine_MDN" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <data-access id="GetSubscriberDetails" classname="com.nuance.metro.dataaccess.GetSubscriberDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="languageCode"/>
          <input-variable name="sessionId"/>
          <input-variable name="imei"/>
          <input-variable name="clearCache"/>
          <input-variable name="multiLineDetails"/>
        </inputs>
        <outputs>
          <output-variable name="mdn"/>
          <output-variable name="accountNumber" mask="true"/>
          <output-variable name="active"/>
          <output-variable name="serialNumber"/>
          <output-variable name="features"/>
          <output-variable name="thirdPartyFeatures"/>
          <output-variable name="payDate"/>
          <output-variable name="taxInclusive"/>
          <output-variable name="accountStatus"/>
          <output-variable name="dueImmediatelyAmount"/>
          <output-variable name="highSecurity"/>
          <output-variable name="ratePlan"/>
          <output-variable name="marketID"/>
          <output-variable name="language"/>
          <output-variable name="isAutopayEnabled"/>
          <output-variable name="hasPhoneInsurance"/>
          <output-variable name="balance"/>
          <output-variable name="isDeviceAllowed"/>
          <output-variable name="isSuspended"/>
          <output-variable name="accountType"/>
          <output-variable name="validAccount"/>
          <output-variable name="roamingBucket"/>
          <output-variable name="paymentStatus"/>
          <output-variable name="ratePlanAttributesMap"/>
          <output-variable name="networkType"/>
          <output-variable name="securityQuestionCode"/>
          <output-variable name="hasEWallet"/>
          <output-variable name="eligibleForBillCycleReset"/>
          <output-variable name="billCycleLength"/>
          <output-variable name="firstName"/>
          <output-variable name="lastName"/>
          <output-variable name="eligibleForUnhotlineCheck"/>
          <output-variable name="securityQuestionString"/>
          <output-variable name="iccid"/>
          <output-variable name="imei"/>
          <output-variable name="hasAutopayPending"/>
          <output-variable name="insuranceCode"/>
          <output-variable name="subscriberId"/>
          <output-variable name="usedESNAllowed"/>
          <output-variable name="emailVerificationStatus"/>
          <output-variable name="emailAddress"/>
          <output-variable name="currentPromo"/>
          <output-variable name="futurePromo"/>
          <output-variable name="featureCodes"/>
          <output-variable name="superRegion"/>
          <output-variable name="accountFutureRequestInd"/>
          <output-variable name="subscriberFuturePricePlanInd"/>
          <output-variable name="ratePlanPrice"/>
          <output-variable name="currentPlanPromptURL"/>
          <output-variable name="currentPlanPromptTTS"/>
          <output-variable name="familyPlanIndicator"/>
          <output-variable name="topupEligibility"/>
          <output-variable name="customerTreatmentLevel"/>
          <output-variable name="HasHighSpeedData"/>
          <output-variable name="includedFeatures"/>
          <output-variable name="hasDataMaximizer"/>
          <output-variable name="eligibleForUpgrade"/>
          <output-variable name="upgradeEligibilityDate"/>
          <output-variable name="arBalance"/>
          <output-variable name="multilineList"/>
          <output-variable name="planMedialPromptURL"/>
          <output-variable name="planMedialPromptTTS"/>
          <output-variable name="hasDefaultAccountValues"/>
          <output-variable name="zipCode" mask="true"/>
          <output-variable name="accountRestrictIndicator"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetSubscriberDetails.status &amp;&amp; GetSubscriberDetails.status.toUpperCase() == 'SUCCESS' &amp;&amp; GetSubscriberDetails.validAccount">
          <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
          <session-mapping key="GlobalVars.unCoopMaxRequest" expr="GetSubscriberDetails.unCoopMaxRequest"/>
          <session-mapping key="GlobalVars.coopMaxRequest" expr="GetSubscriberDetails.coopMaxRequest"/>
          <session-mapping key="GlobalVars.marketID" expr="GetSubscriberDetails.marketID"/>
          <session-mapping key="GlobalVars.zipCode" expr="GetSubscriberDetails.zipCode"/>
          <session-mapping key="GlobalVars.mdn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.networkType" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="GlobalVars.trn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.accountNumber" expr="GetSubscriberDetails.accountNumber"/>
          <session-mapping key="GlobalVars.isOnFamilyPlan" expr="GetSubscriberDetails.isOnFamilyPlan"/>
          <session-mapping key="GlobalVars.firstName" expr="GetSubscriberDetails.firstName"/>
          <session-mapping key="GlobalVars.lastName" expr="GetSubscriberDetails.lastName"/>
          <session-mapping key="GlobalVars.accountFutureRequestInd" expr="GetSubscriberDetails.accountFutureRequestInd"/>
          <session-mapping key="GlobalVars.subscriberFuturePricePlanInd" expr="GetSubscriberDetails.subscriberFuturePricePlanInd"/>
          <session-mapping key="GlobalVars.eligibleForUpgrade" expr="GetSubscriberDetails.isCurrentlyEligibleForDeviceUpgrade"/>
          <session-mapping key="GlobalVars.upgradeEligibilityDate" expr="GetSubscriberDetails.upgradeEligibilityDate"/>
          <session-mapping key="GlobalVars.accountRestrictIndicator" expr="GetSubscriberDetails.accountRestrictIndicator"/>
          <else>
            <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
            <session-mapping key="GlobalVars.GetAccountDetails.validAccount" expr="GetSubscriberDetails.validAccount"/>
          </else>
        </if>
        <if cond="GlobalVars.GetAccountDetails.parentDeviceType == 'INT'">
          <session-mapping key="GlobalVars.tag" expr="'home-internet_active'"/>
          <session-mapping key="GlobalVars.enteredHintLine" value="true" type="Boolean"/>
        </if>
        <action next="SW1115_CheckAccountFound_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="SW1115_CheckAccountFound_DS">
      <if cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.validAccount)">
        <session-mapping key="GlobalVars.cti_MDN" expr="GlobalVars.mdn"/>
        <if cond="(GlobalVars.GetAccountDetails.accountRestrictIndicator == true)">
          <action next="SW1140_FraudSuspension_PP"/>
          <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ9')">
            <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'not_authenticated_high_security_account')"/>
            <gotodialog next="SwitchLines_Security#SW1335_AuthFailTransfer_SD"/>
          </elseif>
          <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ8')">
            <gotodialog next="SwitchLines_Security#SW1525_Goodbye_SD"/>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.switchLinesPINAttempts" expr="0"/>
            <session-mapping key="GlobalVars.switchLinesMDNMatch" value="true" type="Boolean"/>
            <action next="SW1117_GetOTP_SD"/>
          </else>
        </if>
        <else>
          <session-mapping key="GlobalVars.switchLinesMDNAttempts" expr="GlobalVars.switchLinesMDNAttempts+1"/>
          <action next="SW1120_CheckMDNErrorCounter_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="SW1120_CheckMDNErrorCounter_DS">
      <if cond="GlobalVars.switchLinesMDNAttempts == 1">
        <action next="SW1105_GetNewLineMDN_DM"/>
        <elseif cond="GlobalVars.switchLinesMDNAttempts == 2">
          <action next="SW1125_PlayAccountClosedTip_PP"/>
        </elseif>
        <else>
          <action next="SW1130_PlayNoAccountFound_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="SW1125_PlayAccountClosedTip_PP">
      <audio>
        <prompt id="SW1125_out_01">
          <prompt-segments>
            <audiofile text="I *still* couldn't find that If it's been more than 30 days since the last payment, then this account was closed, and you'll need to open a new one To make sure, you can call 611 from the phone on that account " src="SW1125_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="SW1125_out_02">
          <prompt-segments>
            <audiofile text="Otherwise, let's try one last time " src="SW1125_out_02.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SW1105_GetNewLineMDN_DM"/>
    </play-state>

    <play-state id="SW1130_PlayNoAccountFound_PP">
      <audio>
        <prompt id="SW1130_out_01">
          <prompt-segments>
            <audiofile text="Sorry, still nothing found" src="SW1130_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <gotodialog next="SwitchLines_Security#SW1505_SwitchNotAllowedReason_DS"/>
    </play-state>

    <play-state id="SW1140_FraudSuspension_PP">
      <audio>
        <prompt id="SW1140_out_01">
          <prompt-segments>
            <audiofile text="Your account requires assistance" src="SW1140_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SW1220_Transfer_SD"/>
    </play-state>

    <dm-state id="SW1205_GetNewLinePIN_DM" type="CUST">
      <session-mapping key="requestedMDNChangeSwitchLines" value="GlobalVars.requestedMDNChangeSwitchLines" type="String"/>
      <session-mapping key="switchLinesPINAttempts" value="GlobalVars.switchLinesPINAttempts" type="String"/>
      <session-mapping key="lastPinTry" value="GlobalVars.lastPinTry" type="String"/>
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <success>
        <action label="default">
          <session-mapping key="GlobalVars.newLine_PIN" expr="SW1205_GetNewLinePIN_DM.returnvalue"/>
          <session-mapping key="GlobalVars.verificationType" expr="'pin'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="SW1205_GetNewLinePIN_DM.returnvalue"/>
          <session-mapping key="GlobalVars.collectedPINNewLine" expr="GlobalVars.newLine_PIN"/>
          <session-mapping key="GlobalVars.cti_PIN" expr="GlobalVars.newLine_PIN"/>
          <action next="SW1209_Authenticate_DB_DA"/>
        </action>
        <action label="dont_know">
          <audio>
            <prompt id="SW1205_out_02">
              <prompt-segments>
                <audiofile text="No problem" src="SW1205_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="GlobalVars.lastPinTry == true">
            <session-mapping key="GlobalVars.dontKnowPIN" value="true" type="Boolean"/>
            <action next="SW1220_Transfer_SD"/>
          </if>
          <action next="SW1225_GoToAccountPinReset_SD"/>
        </action>
        <action label="same_one">
          <session-mapping key="GlobalVars.collectedPINANI" expr="GlobalVars.verificationValue"/>
          <session-mapping key="GlobalVars.collectedPINNewLine" expr="GlobalVars.collectedPINANI"/>
          <session-mapping key="GlobalVars.verificationType" expr="'pin'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="GlobalVars.collectedPINANI"/>
          <session-mapping key="GlobalVars.cti_PIN" expr="GlobalVars.collectedPINANI"/>
          <action next="SW1209_Authenticate_DB_DA"/>
        </action>
        <action label="incorrect_MDN">
          <if cond="GlobalVars.requestedMDNChangeSwitchLines == true">
            <gotodialog next="SwitchLines_Security#SW1505_SwitchNotAllowedReason_DS"/>
            <else>
              <audio>
                <prompt id="SW1205_out_01">
                  <prompt-segments>
                    <audiofile text="Ok, let's get the right one " src="SW1205_out_01.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.requestedMDNChangeSwitchLines" value="true" type="Boolean"/>
              <session-mapping key="GlobalVars.switchLinesMDNAttempts" expr="0"/>
              <action next="SW1105_GetNewLineMDN_DM"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="switchLinesPINAttempts == 0">
                <prompt id="SW1205_ini_01">
                  <prompt-segments>
                    <audiofile text="Now, the account PIN for *that* number You can say 'it's the same one' Or if it's a different PIN, please enter it now " src="SW1205_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="switchLinesPINAttempts == 1">
                  <prompt id="SW1205_ini_02">
                    <prompt-segments>
                      <audiofile text="You can say 'that's wrong' or enter a different account PIN" src="SW1205_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="lastPinTry == true">
                  <prompt id="SW1205_ini_03">
                    <prompt-segments>
                      <audiofile text="Let's try one last time Please enter the PIN for this line" src="SW1205_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="SW1205_ini_04">
                    <prompt-segments>
                      <audiofile text="Please enter the Pin for THIS line" src="SW1205_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="switchLinesPINAttempts == 0">
                <prompt id="SW1205_ini_01">
                  <prompt-segments>
                    <audiofile text="Now, the account PIN for *that* number You can say 'it's the same one' Or if it's a different PIN, please enter it now " src="SW1205_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="switchLinesPINAttempts == 1">
                  <prompt id="SW1205_ini_02">
                    <prompt-segments>
                      <audiofile text="You can say 'that's wrong' or enter a different account PIN" src="SW1205_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="lastPinTry == true">
                  <prompt id="SW1205_ini_03">
                    <prompt-segments>
                      <audiofile text="Let's try one last time Please enter the PIN for this line" src="SW1205_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="SW1205_ini_04">
                    <prompt-segments>
                      <audiofile text="Please enter the Pin for THIS line" src="SW1205_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="switchLinesPINAttempts == 0">
                <prompt id="SW1205_nm1_01">
                  <prompt-segments>
                    <audiofile text="Please enter the account PIN for the account you want to work with If it's the same as the account you're *calling in* on, say 'same one' You can also say 'I don't know it' " src="SW1205_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SW1205_nm1_02">
                    <prompt-segments>
                      <audiofile text="Please enter the account PIN for the account you want to work with If it's the same as the account you're *calling in* on, say 'same one' To correct the phone number you entered, say 'go back' You can also say 'I don't know it' " src="SW1205_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="SW1205_nm2_01">
                  <prompt-segments>
                    <audiofile text="Please enter the 6-to-15-digit account PIN for the phone number you just gave me " src="SW1205_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SW1205_nm2_02">
                    <prompt-segments>
                      <audiofile text="Please enter the 8-digit account PIN for the phone number you just gave me  " src="SW1205_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="SW1205_nm2_03">
                <prompt-segments>
                  <audiofile text="If it's the same as the account you're *calling in* on, press 1 If you think you need to *correct* the phone number you gave me, press 2 If you *don't know* the account PIN for this account, press star" src="SW1205_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="SW1205_nm2_01">
                  <prompt-segments>
                    <audiofile text="Please enter the 6-to-15-digit account PIN for the phone number you just gave me " src="SW1205_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SW1205_nm2_02">
                    <prompt-segments>
                      <audiofile text="Please enter the 8-digit account PIN for the phone number you just gave me  " src="SW1205_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="SW1205_nm2_03">
                <prompt-segments>
                  <audiofile text="If it's the same as the account you're *calling in* on, press 1 If you think you need to *correct* the phone number you gave me, press 2 If you *don't know* the account PIN for this account, press star" src="SW1205_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="switchLinesPINAttempts == 0">
                <prompt id="SW1205_nm1_01">
                  <prompt-segments>
                    <audiofile text="Please enter the account PIN for the account you want to work with If it's the same as the account you're *calling in* on, say 'same one' You can also say 'I don't know it' " src="SW1205_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SW1205_nm1_02">
                    <prompt-segments>
                      <audiofile text="Please enter the account PIN for the account you want to work with If it's the same as the account you're *calling in* on, say 'same one' To correct the phone number you entered, say 'go back' You can also say 'I don't know it' " src="SW1205_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="SW1205_nm2_01">
                  <prompt-segments>
                    <audiofile text="Please enter the 6-to-15-digit account PIN for the phone number you just gave me " src="SW1205_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SW1205_nm2_02">
                    <prompt-segments>
                      <audiofile text="Please enter the 8-digit account PIN for the phone number you just gave me  " src="SW1205_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="SW1205_nm2_03">
                <prompt-segments>
                  <audiofile text="If it's the same as the account you're *calling in* on, press 1 If you think you need to *correct* the phone number you gave me, press 2 If you *don't know* the account PIN for this account, press star" src="SW1205_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="SW1205_nm2_01">
                  <prompt-segments>
                    <audiofile text="Please enter the 6-to-15-digit account PIN for the phone number you just gave me " src="SW1205_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SW1205_nm2_02">
                    <prompt-segments>
                      <audiofile text="Please enter the 8-digit account PIN for the phone number you just gave me  " src="SW1205_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="SW1205_nm2_03">
                <prompt-segments>
                  <audiofile text="If it's the same as the account you're *calling in* on, press 1 If you think you need to *correct* the phone number you gave me, press 2 If you *don't know* the account PIN for this account, press star" src="SW1205_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="switchLinesPINAttempts == 0">
                <prompt id="SW1205_ini_01">
                  <prompt-segments>
                    <audiofile text="Now, the account PIN for *that* number You can say 'it's the same one' Or if it's a different PIN, please enter it now " src="SW1205_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="switchLinesPINAttempts == 1">
                  <prompt id="SW1205_ini_02">
                    <prompt-segments>
                      <audiofile text="You can say 'that's wrong' or enter a different account PIN" src="SW1205_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="lastPinTry == true">
                  <prompt id="SW1205_ini_03">
                    <prompt-segments>
                      <audiofile text="Let's try one last time Please enter the PIN for this line" src="SW1205_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="SW1205_ini_04">
                    <prompt-segments>
                      <audiofile text="Please enter the Pin for THIS line" src="SW1205_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SW1205_GetNewLinePIN_DM.grxml" count="1"/>
          <dtmfgrammars filename="SW1205_GetNewLinePIN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="IF_NECESSARY" reco_suppress_logs="true" highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif cond="same_one == 'lastresult'">
                  <prompt id="SW1205_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="You said it's the same one " src="SW1205_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="incorrect_MDN == 'lastresult'">
                  <prompt id="SW1205_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="You want to enter a different phone number to work with " src="SW1205_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="dont_know == 'lastresult'">
                  <prompt id="SW1205_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="You don't know the account PIN " src="SW1205_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif cond="same_one == 'lastresult'">
                  <prompt id="SW1205_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="You said it's the same one " src="SW1205_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="incorrect_MDN == 'lastresult'">
                  <prompt id="SW1205_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="You want to enter a different phone number to work with " src="SW1205_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="dont_know == 'lastresult'">
                  <prompt id="SW1205_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="You don't know the account PIN " src="SW1205_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif cond="same_one == 'lastresult'">
                  <prompt id="SW1205_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="You said it's the same one " src="SW1205_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="incorrect_MDN == 'lastresult'">
                  <prompt id="SW1205_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="You want to enter a different phone number to work with " src="SW1205_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="dont_know == 'lastresult'">
                  <prompt id="SW1205_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="You don't know the account PIN " src="SW1205_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="SW1209_Authenticate_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="accessToken" value="GlobalVars.accessToken" type="String"/>
      <session-mapping key="pin" value="GlobalVars.verificationValue" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="verificationType" value="GlobalVars.verificationType" type="String"/>
      <session-mapping key="verificationValue" value="GlobalVars.verificationValue" type="String"/>
      <data-access id="Authenticate" classname="com.nuance.metro.dataaccess.ValidatePinForAuthenticate">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="pin" mask="true"/>
          <input-variable name="accessToken" mask="false"/>
          <input-variable name="verificationType"/>
          <input-variable name="verificationValue" mask="true"/>
          <input-variable name="sessionId"/>
          <input-variable name="providerId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="JWTToken"/>
          <output-variable name="expiresIn"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="Authenticate.status == 'FAILURE'">
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          <gotodialog next="Transfer_Main"/>
          <elseif cond="Authenticate.acctLocked == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.acctLocked" value="true" type="Boolean"/>
            <gotodialog next="SwitchLines_Security#SW1305_PlayPINInvalid_PP"/>
          </elseif>
          <elseif cond="Authenticate.onePinTryRemaining  == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.onePinTryRemaining" value="true" type="Boolean"/>
            <action next="SW1210_CheckPINMatch_JDA"/>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <action next="SW1210_CheckPINMatch_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="SW1210_CheckPINMatch_DS">
      <if cond="GlobalVars.Authenticate &amp;&amp; GlobalVars.Authenticate.status == 'SUCCESS'">
        <session-mapping key="GlobalVars.loggedIn" value="true" type="Boolean"/>
        <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'authenticated_by_pin')"/>
        <action next="SW1215_PlaySwitchLineSuccess_PP"/>
        <else>
          <if cond="GlobalVars.onePinTryRemaining == true">
            <action next="SW1211_AskResetInformation_DM"/>
            <else>
              <session-mapping key="GlobalVars.switchLinesPINAttempts" expr="GlobalVars.switchLinesPINAttempts+1"/>
              <gotodialog next="SwitchLines_Security#SW1305_PlayPINInvalid_PP"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="SW1215_PlaySwitchLineSuccess_PP">
      <session-mapping key="newLine_MDN" value="GlobalVars.newLine_MDN" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="accountStatus" value="GlobalVars.GetAccountDetails.accountStatus" type="String"/>
      <audio>
        <if cond="tag == 'reactivate-phone_active' || tag == 'reactivate-old_account'">
          <prompt id="SW1215_out_03" cond="accountStatus == 'active'">
            <prompt-segments>
              <audiofile text="Ok, I show this line as already active " src="SW1215_out_03.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="SW1215_out_01">
              <prompt-segments>
                <audiofile text="That checks out! You're now working with " src="SW1215_out_01.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="custom" expr="newLine_MDN">
              <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
              <param name="intonation" value="f"/>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <if cond="GlobalVars.tag == 'reactivate-phone_active' || tag == 'reactivate-old_account'">
        <if cond="accountStatus == 'active'">
          <action next="SW1220_Transfer_SD"/>
          <else>
            <action next="SW1220_Transfer_SD"/>
          </else>
        </if>
        <else>
          <session-mapping key="GlobalVars.switchLinesSuccess" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <gotodialog next="SwitchLines_Security#SW1405_CheckNeedBalance_DS"/>
        </else>
      </if>
    </play-state>

    <subdialog-state id="SW1220_Transfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
    </subdialog-state>
    <subdialog-state id="SW1225_GoToAccountPinReset_SD">
      <gotodialog next="AccountPINReset_Main_Dialog"/>
    </subdialog-state>
    <subdialog-state id="SW1117_GetOTP_SD">
      <gotodialog next="TwoFactorAuth_Main_Dialog"/>
      <action next="SW1117_GetOTP_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="SW1117_GetOTP_SD_Return_CS">
      <action next="SW1205_GetNewLinePIN_DM"/>
    </custom-state>

    <dm-state id="SW1211_AskResetInformation_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.onePinTryRemaining" value="false" type="Boolean"/>
          <action next="SW1225_GoToAccountPinReset_SD"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.lastPinTry" value="true" type="Boolean"/>
          <action next="SW1205_GetNewLinePIN_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SW1211_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, that still didnt match Would you like information on how to reset your pin online?" src="SW1211_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SW1211_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, that still didnt match Would you like information on how to reset your pin online?" src="SW1211_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SW1211_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1 If you'd like to try your pin once more say 'no' or press 2" src="SW1211_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SW1211_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1 If you'd like to try your pin once more say 'no' or press 2" src="SW1211_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SW1211_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1 If you'd like to try your pin once more say 'no' or press 2" src="SW1211_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SW1211_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1 If you'd like to try your pin once more say 'no' or press 2" src="SW1211_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SW1211_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1 If you'd like to try your pin once more say 'no' or press 2" src="SW1211_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SW1211_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1 If you'd like to try your pin once more say 'no' or press 2" src="SW1211_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SW1211_AskResetInformation_DM.grxml" count="1"/>
          <dtmfgrammars filename="SW1211_AskResetInformation_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  