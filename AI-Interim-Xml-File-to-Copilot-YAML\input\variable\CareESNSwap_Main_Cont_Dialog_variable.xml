<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="JWTToken" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="iccid" value="empty" type="String"/>
  <session-mapping key="ignoreDsDeviceProperty" value="empty" type="String"/>
  <session-mapping key="trn" value="empty" type="String"/>
  <session-mapping key="isByod" value="empty" type="String"/>
  <session-mapping key="deviceType" value="empty" type="String"/>
  <session-mapping key="removeFeatures" value="empty" type="String"/>
  <session-mapping key="newRatePlan" value="empty" type="String"/>
  <session-mapping key="isOrderApproved" value="empty" type="String"/>
  <session-mapping key="accessToken" value="empty" type="String"/>
  <session-mapping key="orderId" value="empty" type="String"/>
  <session-mapping key="useTwoStep" value="empty" type="String"/>
  <session-mapping key="simNumber" value="empty" type="String"/>
  <session-mapping key="transactionType" value="empty" type="String"/>
  <session-mapping key="usingOldSIMForSwap" value="true" type="string"/>
  <session-mapping key="aniMatch" value="false" type="boolean"/>
  <session-mapping key="twoFactorAuthOutcome" value="success" type="string"/>
  <session-mapping key="switchLinesSuccess" value="true" type="boolean"/>
  <session-mapping key="ValidateDevice.status" value="Success" type="string"/>
  <session-mapping key="ValidateDevice.BYODNotInInventory" value="true" type="boolean"/>
  <session-mapping key="ValidateDevice.simExpired" value="true" type="boolean"/>
  <session-mapping key="ValidateDevice.iccidEquipmentInNegativeList" value="true" type="boolean"/>
  <session-mapping key="ValidateDevice.imeiInvalidDevice" value="true" type="boolean"/>
  <session-mapping key="ValidateDevice.currentAddOnEligibleIndicator" value="false" type="boolean"/>
  <session-mapping key="ValidateDevice.currentPlanEligibleIndicator" value="false" type="boolean"/>
  <session-mapping key="ValidateDevice.imeiIsEligibleForActivation" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.isByod" value="true" type="boolean"/>
  <session-mapping key="ValidateDevice.simType" value="ESIM" type="string"/>
  <session-mapping key="GlobalVars.deviceChangeTargetSIMChosen" value="ESIM" type="string"/>
  <session-mapping key="esnChangedPlan" value="true" type="boolean"/>
  <session-mapping key="acceptedRemoveFeatures" value="true" type="boolean"/>
  <session-mapping key="simType" value="ESIM" type="string"/>
  <session-mapping key="deviceChangeTargetSIMChosen" value="ESIM" type="string"/>
  <session-mapping key="isByod" value="false" type="string"/>
  <session-mapping key="trn" value="null" type="string"/>
  <session-mapping key="trn.length" value="15" type="integer"/>
  <session-mapping key="SubmitChangeDevice.status" value="Success" type="string"/>
  <session-mapping key="SubmitChangeDevice.dueImmediatlyAmount" value="0" type="integer"/>
  <session-mapping key="SubmitChangeDevice.deviceChangedInd" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.SubmitChangeDeviceCalled" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.byodCompatibility" value="Fully Compatible" type="string"/>
  <session-mapping key="GlobalVars.compatibilityTransfer" value="true" type="string"/>
  <session-mapping key="deviceType" value="undefined" type="string"/>
  <session-mapping key="ValidateDevice.imeiEquipmentActive" value="true" type="boolean"/>
  <session-mapping key="ValidateDevice.imeiEquipmentInNegativeList" value="true" type="boolean"/>
  <session-mapping key="ES1725_operator_counter" value="2" type="integer"/>
  <session-mapping key="enteringFrom" value="ES1715_SIMExpired_PP" type="string"/>
  <session-mapping key="simExpired" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.hasPhoneInsurance" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.GetAccountDetails" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.twoFactorAuthOutcome" value="success" type="string"/>
  <session-mapping key="GetAccountDetails.customerInBannedMarket" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.accountFutureRequestInd" value="true" type="boolean"/>
  <session-mapping key="ValidateDevice.deviceType" value="IOT" type="string"/>
  <session-mapping key="ValidateDevice.isDualSimDevice" value="true" type="string"/>
  <session-mapping key="ValidateDevice.imeiList" value="null" type="string"/>
  <session-mapping key="imeiList.length" value="2" type="string"/>
  <session-mapping key="GlobalVars.BYODRegistered" value="false" type="boolean"/>
  <session-mapping key="ValidateDevice.eid" value="undefined" type="string"/>
  <session-mapping key="GetBCSParameters.device_change_esim_disable" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.currentSIMType" value="ESIM" type="string"/>
  <session-mapping key="GlobalVars.activityCode" value="BSS" type="string"/>
  <session-mapping key="ES2135_counter" value="0" type="integer"/>
  <session-mapping key="ES2145_counter" value="0" type="integer"/>
  <session-mapping key="ES2150_counter" value="0" type="integer"/>
  <session-mapping key="eid" value="undefined" type="string"/>
  <session-mapping key="AddOrUpdateEsim.status" value="Success" type="string"/>
</session-mappings>
