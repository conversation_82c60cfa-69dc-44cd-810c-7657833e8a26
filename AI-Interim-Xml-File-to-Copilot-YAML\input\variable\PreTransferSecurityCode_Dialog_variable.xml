<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="pin" value="empty" type="String"/>
  <session-mapping key="accessToken" value="empty" type="String"/>
  <session-mapping key="verificationType" value="empty" type="String"/>
  <session-mapping key="verificationValue" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="providerId" value="empty" type="String"/>
  <session-mapping key="GlobalVars.visitedXferTwoFactorAuth" value="true" type="string"/>
  <session-mapping key="GetBCSParameters.care_enable_pretransfer_code_collection" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.tag" value="vague-forgot_pin" type="string"/>
  <session-mapping key="GlobalVars.twoFactorAuthOutcome" value="code_timeout" type="string"/>
  <session-mapping key="GetBCSParameters.care_enable_twofactorauth" value="false" type="boolean"/>
  <session-mapping key="GetBCSParameters.care_transfer_twofactorauth_nophone" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.dontKnowPIN" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.lastPinTry" value="true" type="string"/>
  <session-mapping key="lastLoginAttempt" value="true" type="boolean"/>
  <session-mapping key="preTransferCodeCollectionErrors" value="0" type="integer"/>
  <session-mapping key="accountPinToggleOn" value="true" type="boolean"/>
  <session-mapping key="Authenticate.status" value="FAILURE" type="string"/>
  <session-mapping key="Authenticate.acctLocked" value="true" type="boolean"/>
  <session-mapping key="Authenticate.onePinTryRemaining" value="true" type="boolean"/>
  <session-mapping key="preTransferCodeCollectionOutcome" value="agentRequest" type="string"/>
  <session-mapping key="GetBCSParameters.care_enable_pretransfer_code_retries" value="true" type="boolean"/>
</session-mappings>
