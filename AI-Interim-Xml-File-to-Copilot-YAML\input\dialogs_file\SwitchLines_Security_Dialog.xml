<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="SwitchLines_Security_Dialog">
    <play-state id="SW1305_PlayPINInvalid_PP">
      <session-mapping key="switchLinesPINAttempts" value="GlobalVars.switchLinesPINAttempts" type="String"/>
      <session-mapping key="acctLocked" value="GlobalVars.acctLocked" type="String"/>
      <audio>
        <if cond="acctLocked == true">
          <prompt id="SW1305_out_04">
            <prompt-segments>
              <audiofile text="Sorry, I am unable to access this account" src="SW1305_out_04.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="switchLinesPINAttempts == 1">
            <prompt id="SW1305_out_01">
              <prompt-segments>
                <audiofile text="That *doesn't* match " src="SW1305_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="switchLinesPINAttempts == 2">
            <prompt id="SW1305_out_02">
              <prompt-segments>
                <audiofile text="That *doesn't* match either " src="SW1305_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="SW1305_out_03">
              <prompt-segments>
                <audiofile text="That's still not right " src="SW1305_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <if cond="GlobalVars.acctLocked == true">
        <session-mapping key="GlobalVars.playedAcctLocked" value="true" type="Boolean"/>
        <action next="SW1335_AuthFailTransfer_SD"/>
        <else>
          <action next="SW1310_CheckPINErrorCounter_JDA"/>
        </else>
      </if>
    </play-state>

    <decision-state id="SW1310_CheckPINErrorCounter_DS">
      <if cond="GlobalVars.switchLinesPINAttempts == 1">
        <action next="SW1315_ReplayNewLineMDN_PP"/>
        <else>
          <gotodialog next="SwitchLines_Main#SW1205_GetNewLinePIN_DM"/>
        </else>
      </if>
    </decision-state>

    <play-state id="SW1315_ReplayNewLineMDN_PP">
      <session-mapping key="newLine_MDN" value="GlobalVars.newLine_MDN" type="String"/>
      <audio>
        <prompt id="SW1315_out_02">
          <prompt-segments>
            <audiofile text="Just to be sure, I got the phone number " src="SW1315_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="newLine_MDN">
          <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
          <param name="intonation" value="f"/>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <gotodialog next="SwitchLines_Main#SW1205_GetNewLinePIN_DM"/>
    </play-state>

    <subdialog-state id="SW1335_AuthFailTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="SW1335_AuthFailTransfer_SD"/>
    </subdialog-state>
    <decision-state id="SW1405_CheckNeedBalance_DS">
      <if cond="GlobalVars.accountNumber == GlobalVars.originalLine_accountNumber">
        <action next="getReturnLink()"/>
        <else>
          <action next="SW1410_CheckAccountStatus_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="SW1410_CheckAccountStatus_DS">
      <session-mapping key="accountStatus" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.accountStatus:''" type="String"/>
      <if cond="accountStatus == 'active'">
        <if cond="GlobalVars.GetAccountDetails.parentDeviceType == 'INT'">
          <session-mapping key="GlobalVars.callType" expr="'home-internet_active'"/>
          <session-mapping key="GlobalVars.enteredHintLine" value="true" type="Boolean"/>
          <action next="SW1426_GoToHomeInternetMenu_SD"/>
          <else>
            <action next="SW1415_CheckOrigin_JDA"/>
          </else>
        </if>
        <else>
          <action next="SW1416_CheckSuspendedRouting_JDA"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="SW1426_GoToHomeInternetMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
      <action next="SW1426_GoToHomeInternetMenu_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SW1426_GoToHomeInternetMenu_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="SW1415_CheckOrigin_DS">
      <if cond="GlobalVars.switchLinesEntryPoint == 'esn_swap'">
        <action next="getReturnLink()"/>
        <else>
          <action next="SW1420_PlayNewLineBalance_PP"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="SW1416_CheckSuspendedRouting_DS">
      <if cond="GlobalVars.switchLinesEntryPoint == 'suspended'">
        <action next="getReturnLink()"/>
        <else>
          <action next="SW1425_GoToSuspendedHandling_SD"/>
        </else>
      </if>
    </decision-state>

    <play-state id="SW1420_PlayNewLineBalance_PP">
      <session-mapping key="balance" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="hasAutopay" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="dayEveryMonthX" value="" type="String"/>
      <session-mapping key="remainingDays" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails.payDate" type="String"/>
      <audio>
        <if cond="(balance == 0 &amp;&amp; dueImmediatelyAmount == 0)">
          <prompt id="SW1420_out_01" cond="(hasAutopay == true)">
            <prompt-segments>
              <audiofile text="You're all paid up, and your auto-payments are scheduled for" src="SW1420_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="SW1420_out_02" cond="(hasAutopay != true)">
            <prompt-segments>
              <audiofile text="You're all paid up, and your payments are due" src="SW1420_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <prompt type="custom" expr="dayEveryMonthX" scope="request">
            <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.VXMLDynamicPromptNameRendererImpl"/>
            <param name="tts" value="dayEveryMonthX" scope="request"/>
          </prompt>
          <elseif cond="(hasAutopay)">
            <prompt id="SW1420_out_03" cond="(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)">
              <prompt-segments>
                <audiofile text="You're all paid up, and your next auto-payment for" src="SW1420_out_03.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="SW1420_out_04" cond="!(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)">
              <prompt-segments>
                <audiofile text="An auto-payment for " src="SW1420_out_04.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="balance">
              <param value="false" name="playZeroCents"/>
            </prompt>
            <prompt id="SW1420_out_05" cond="(remainingDays == 0)">
              <prompt-segments>
                <audiofile text="is being processed today" src="SW1420_out_05.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="SW1420_out_06" cond="(remainingDays == 1)">
              <prompt-segments>
                <audiofile text="is scheduled for tomorrow " src="SW1420_out_06.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="SW1420_out_07" cond="(remainingDays == 2)">
              <prompt-segments>
                <audiofile text="is scheduled for the day after tomorrow " src="SW1420_out_07.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="SW1420_out_08" cond="(remainingDays &gt; 2)">
              <prompt-segments>
                <audiofile text="is scheduled for " src="SW1420_out_08.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="date" expr="payDate" cond="(remainingDays &gt; 2)">
              <param name="dateFormat" value="MMM dd, yyyy"/>
            </prompt>
          </elseif>
          <else>
            <prompt id="SW1420_out_10" cond="(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)">
              <prompt-segments>
                <audiofile text="You're all paid up, and your next payment for" src="SW1420_out_10.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="SW1420_out_11" cond="!(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)">
              <prompt-segments>
                <audiofile text="A payment for " src="SW1420_out_11.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="balance">
              <param value="false" name="playZeroCents"/>
            </prompt>
            <prompt id="SW1420_out_12" cond="(remainingDays == 0)">
              <prompt-segments>
                <audiofile text="is due today " src="SW1420_out_12.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="SW1420_out_13" cond="(remainingDays == 1)">
              <prompt-segments>
                <audiofile text="is due tomorrow " src="SW1420_out_13.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="SW1420_out_14" cond="(remainingDays == 2)">
              <prompt-segments>
                <audiofile text="is due the day after tomorrow " src="SW1420_out_14.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="SW1420_out_15" cond="(remainingDays &gt; 2)">
              <prompt-segments>
                <audiofile text="is due " src="SW1420_out_15.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="date" expr="payDate" cond="(remainingDays &gt; 2)">
              <param name="dateFormat" value="MMM dd, yyyy"/>
            </prompt>
          </else>
        </if>
        <if cond="(dueImmediatelyAmount &gt; 0)">
          <prompt id="SW1420_out_17">
            <prompt-segments>
              <audiofile text="There is also a charge due right now, for " src="SW1420_out_17.wav"/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="dueImmediatelyAmount">
            <param value="false" name="playZeroCents"/>
          </prompt>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <subdialog-state id="SW1425_GoToSuspendedHandling_SD">
      <gotodialog next="SuspendedHandling_Main_Dialog"/>
    </subdialog-state>
    <decision-state id="SW1505_SwitchNotAllowedReason_DS">
      <if cond="((GlobalVars.aniMatch == false || GlobalVars.aniMatch == 'false') || (GlobalVars.GetBCSParameters.care_enable_switch_lines == false || GlobalVars.GetBCSParameters.care_enable_switch_lines == 'false'))">
        <action next="SW1510_PlaySwitchDeniedBCSOff_PP"/>
        <elseif cond="GlobalVars.switchLinesSuccess == true">
          <action next="SW1515_PlaySwitchDeniedSecondRequest_PP"/>
        </elseif>
        <else>
          <action next="SW1520_PlaySwitchedDeniedLoginFail_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="SW1510_PlaySwitchDeniedBCSOff_PP">
      <audio>
        <prompt id="SW1510_out_01">
          <prompt-segments>
            <audiofile text="To manage a different account, you can call 611 from the phone you want to work with If you don't have it, you'll need to call from a *landline*, and dial 888-8METRO8 Again, that's 888-8-METRO-8, from a *landline* Then, you'll just enter the number you want to work with! " src="SW1510_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SW1525_Goodbye_SD"/>
    </play-state>

    <play-state id="SW1515_PlaySwitchDeniedSecondRequest_PP">
      <audio>
        <prompt id="SW1515_out_01">
          <prompt-segments>
            <audiofile text="For your security, we only allow switching lines *once* per call right now " src="SW1515_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="SW1515_out_02">
          <prompt-segments>
            <audiofile text="To work with a different line, please call us back and ask to switch lines at the main menu You can also call 888-8METRO8 from a *landline* and enter the number you want to work with " src="SW1515_out_02.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SW1525_Goodbye_SD"/>
    </play-state>

    <play-state id="SW1520_PlaySwitchedDeniedLoginFail_PP">
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <audio>
        <prompt id="SW1520_out_01" cond="accountPinToggleOn == true">
          <prompt-segments>
            <audiofile text="Please confirm the phone number you want to work with, and the 6-to-15-digit account PIN that goes with that account When you're ready, just call us back! " src="SW1520_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="SW1520_out_02" cond="accountPinToggleOn != true">
          <prompt-segments>
            <audiofile text="Please confirm the phone number you want to work with, and the 8-digit account PIN that goes with that account When you're ready, just call us back!  " src="SW1520_out_02.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SW1525_Goodbye_SD"/>
    </play-state>

    <subdialog-state id="SW1525_Goodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </subdialog-state>
  </dialog>
  