<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="clearCache" value="empty" type="String"/>
  <session-mapping key="multiLineDetails" value="empty" type="String"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.callType" value="dsgExtension" type="string"/>
  <session-mapping key="GlobalVars.GetAccountDetails" value="null" type="string"/>
  <session-mapping key="GlobalVars.pinFromIDUpfront" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.maxTriesAttempted" value="null" type="string"/>
  <session-mapping key="GlobalVars.securityRequired" value="true" type="boolean"/>
  <session-mapping key="callType" value="make_pmt" type="string"/>
  <session-mapping key="from611orStar99" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.shortSecurityCodePrompt" value="true" type="boolean"/>
  <session-mapping key="getAccountNumberTries" value="3" type="integer"/>
</session-mappings>
