<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="paymentType" value="empty" type="String"/>
  <session-mapping key="paymentMethod" value="empty" type="String"/>
  <session-mapping key="cardNumber" value="empty" type="String"/>
  <session-mapping key="paymentAmount" value="empty" type="String"/>
  <session-mapping key="expirationDate" value="empty" type="String"/>
  <session-mapping key="securityCode" value="empty" type="String"/>
  <session-mapping key="billingZip" value="empty" type="String"/>
  <session-mapping key="dueImmediately" value="empty" type="String"/>
  <session-mapping key="serviceAccountBalance" value="empty" type="String"/>
  <session-mapping key="validatePaymentAmountOnly" value="empty" type="String"/>
  <session-mapping key="GlobalVars.paymentAmtMinMaxError" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.tryOtherCardReason" value="reenter_zip" type="string"/>
  <session-mapping key="GlobalVars.payingWithEWallet" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.authorizationFailureHandling" value="sameAmountDeclined" type="string"/>
  <session-mapping key="GlobalVars.disconfirmedDetails" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.correctAllDetails" value="false" type="boolean"/>
  <session-mapping key="preferredPaymentMethod" value="credit" type="string"/>
  <session-mapping key="saidOperator" value="true" type="boolean"/>
  <session-mapping key="failedChecksum" value="true" type="boolean"/>
  <session-mapping key="disconfirmedDetails" value="true" type="boolean"/>
  <session-mapping key="correctAllDetails" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.failedChecksum" value="true" type="boolean"/>
  <session-mapping key="unsupportedCardIssuer" value="discover" type="string"/>
  <session-mapping key="callType" value="activate" type="string"/>
  <session-mapping key="implicitConfirmReject" value="true" type="boolean"/>
  <session-mapping key="fromBC1120" value="true" type="boolean"/>
  <session-mapping key="tryOtherCardReason" value="card_expired" type="string"/>
  <session-mapping key="GlobalVars.bankCardDateIsValid" value="1" type="integer"/>
  <session-mapping key="fromBC1110" value="true" type="boolean"/>
  <session-mapping key="voiceOrDtmf" value="voice" type="string"/>
  <session-mapping key="providedCVV" value="true" type="boolean"/>
  <session-mapping key="cardTypeAmex" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.useSameZipCode" value="no" type="string"/>
  <session-mapping key="GlobalVars.callType" value="activate" type="string"/>
  <session-mapping key="FromBC1205" value="true" type="boolean"/>
  <session-mapping key="providedZipCode" value="false" type="boolean"/>
  <session-mapping key="MDEinBC1305" value="true" type="boolean"/>
  <session-mapping key="useSameZipCode" value="disconfirmed" type="string"/>
  <session-mapping key="fromBC1320" value="true" type="boolean"/>
  <session-mapping key="ValidateCardOptionsREST.status" value="Success" type="string"/>
  <session-mapping key="ValidateCardOptions.cardType" value="credit" type="string"/>
  <session-mapping key="ValidateCardOptions.cardStatus" value="amount_under_min" type="string"/>
  <session-mapping key="cardStatus" value="valid" type="string"/>
  <session-mapping key="payingWithEWallet" value="false" type="string"/>
  <session-mapping key="authorizationFailureHandling" value="sameAmountDeclined" type="string"/>
  <session-mapping key="paymentAmount" value="35" type="integer"/>
  <session-mapping key="walletConvenienceFee" value="0" type="string"/>
  <session-mapping key="cardConvenienceFee" value="0" type="string"/>
  <session-mapping key="FirstEntryBC1420" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.isCareTransfer" value="true" type="boolean"/>
</session-mappings>
