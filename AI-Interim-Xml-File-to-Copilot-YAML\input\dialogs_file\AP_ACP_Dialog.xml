<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="AP_ACP_Dialog">
    <dm-state id="AP1005_ACPStart_DM" type="CUST">
      <session-mapping key="saidOperatorAcpAt1005" value="GlobalVars.saidOperatorAcpAt1005" type="String"/>
      <session-mapping key="GlobalVars.operatorReqCount_AP1005" expr="GlobalVars.operatorReqCount_AP1005 != undefined ? GlobalVars.operatorReqCount_AP1005 : 0"/>
      <success>
        <action label="send_text">
          <session-mapping key="GlobalVars.campaignId" expr="GlobalVars.GetBCSParameters.acp_info_campaign_id"/>
          <action next="AP1010_SendACPLinks_DB_DA"/>
        </action>
        <action label="main_menu">
          <session-mapping key="GlobalVars.acpFlowVisited" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.operatorReqCount_AP1005" expr="GlobalVars.operatorReqCount_AP1005+1"/>
          <session-mapping key="GlobalVars.callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperatorAcpAt1005" value="true" type="Boolean"/>
          <if cond="GlobalVars.operatorReqCount_AP1005 == 1">
            <audio>
              <prompt id="AP1005_Operator_01">
                <prompt-segments>
                  <audiofile text="Our representative cannot complete the national verifier application on your behalf If you'd like a link to full details on the program say, 'send me a text' " src="AP1005_Operator_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="AP1005_ACPStart_DM"/>
            <else>
              <action next="AP1065_CallTransfer_SD"/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AP1005_ini_01" cond="saidOperatorAcpAt1005 != true">
                <prompt-segments>
                  <audiofile text="If you are a customer using the Affordable Connectivity Program, please be aware that the program will be ended by the federal government in April But Metro is here to help We will continue to provide current Metro ACP beneficiaries the full ACP discount through your May bill Then, starting on your June bill date you will receive up to a $15 credit to help with the transition for three months Remember, at Metro you're never locked into a contract, you have control to change your service when you need to  You can say 'repeat that' or 'main menu'  If you'd like a link to our acp information page say 'send me a text'" src="AP1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AP1005_ini_01" cond="saidOperatorAcpAt1005 != true">
                <prompt-segments>
                  <audiofile text="If you are a customer using the Affordable Connectivity Program, please be aware that the program will be ended by the federal government in April But Metro is here to help We will continue to provide current Metro ACP beneficiaries the full ACP discount through your May bill Then, starting on your June bill date you will receive up to a $15 credit to help with the transition for three months Remember, at Metro you're never locked into a contract, you have control to change your service when you need to  You can say 'repeat that' or 'main menu'  If you'd like a link to our acp information page say 'send me a text'" src="AP1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AP1005_ini_01">
                <prompt-segments>
                  <audiofile text="If you are a customer using the Affordable Connectivity Program, please be aware that the program will be ended by the federal government in April But Metro is here to help We will continue to provide current Metro ACP beneficiaries the full ACP discount through your May bill Then, starting on your June bill date you will receive up to a $15 credit to help with the transition for three months Remember, at Metro you're never locked into a contract, you have control to change your service when you need to  You can say 'repeat that' or 'main menu'  If you'd like a link to our acp information page say 'send me a text'" src="AP1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1005_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say repeat that Or say main menu  If you'd like a link to our ACP information page say, 'send me a text'" src="AP1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1005_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say repeat that Or say main menu  If you'd like a link to our ACP information page say, 'send me a text'" src="AP1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1005_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say repeat that Or say main menu  If you'd like a link to our ACP information page say, 'send me a text'" src="AP1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1005_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say repeat that Or say main menu  If you'd like a link to our ACP information page say, 'send me a text'" src="AP1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1005_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say repeat that Or say main menu  If you'd like a link to our ACP information page say, 'send me a text'" src="AP1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AP1005_ACPStart_DM_reinvoke"/>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AP1005_ACPStart_DM.grxml" count="1"/>
          <dtmfgrammars filename="AP1005_ACPStart_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="confirmation_nomatch_1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="AP1010_SendACPLinks_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="campaignId" value="GlobalVars.campaignId" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="campaignIdCheck" value="GlobalVars.GetBCSParameters.acp_info_campaign_id" type="String"/>
      <data-access id="SendTextMessage" classname="com.nuance.metro.dataaccess.SendTextMessage">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="mdn"/>
          <input-variable name="campaignId"/>
          <input-variable name="languageCode"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="SendTextMessage.status == 'Success'">
          <session-mapping key="GlobalVars.AP1010failure" value="false" type="Boolean"/>
          <action next="AP1062_PlaySMSOutcome_PP"/>
          <else>
            <action next="AP1062_PlaySMSOutcome_PP"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <dm-state id="AP1020_FirstStep_DM" type="YSNO">
      <session-mapping key="saidOperatorAcpAt1020" value="GlobalVars.saidOperatorAcpAt1020" type="String"/>
      <session-mapping key="GlobalVars.operatorReqCount_AP1020" expr="GlobalVars.operatorReqCount_AP1020 != undefined ? GlobalVars.operatorReqCount_AP1020 : 0"/>
      <success>
        <action label="true">
          <action next="AP1030_SecondStep_DM"/>
        </action>
        <action label="false">
          <action next="AP1025_FullProcess_DM"/>
        </action>
        <action label="operator" next="AP1020_FirstStep_DM">
          <session-mapping key="GlobalVars.operatorReqCount_AP1020" expr="GlobalVars.operatorReqCount_AP1020+1"/>
          <session-mapping key="GlobalVars.callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperatorAcpAt1020" value="true" type="Boolean"/>
          <if cond="GlobalVars.operatorReqCount_AP1020 == 1">
            <audio>
              <prompt id="AP1020_Operator_01">
                <prompt-segments>
                  <audiofile text="Our representative cannot complete the national verifier application on your behalf  You can call them direct at 800 324 9473 again the number is 800-324-9472   Have you applied at the national verifier webpage yet? '" src="AP1020_Operator_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="AP1020_FirstStep_DM"/>
            <else>
              <action next="AP1065_CallTransfer_SD"/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AP1020_ini_01" cond="saidOperatorAcpAt1020 != true">
                <prompt-segments>
                  <audiofile text="The first step in applying for the ACP discount would be to receive approval through the federal national verifier webpage If approved, you will receive an approval ID  Have you done that yet?" src="AP1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AP1020_ini_01" cond="saidOperatorAcpAt1020 != true">
                <prompt-segments>
                  <audiofile text="The first step in applying for the ACP discount would be to receive approval through the federal national verifier webpage If approved, you will receive an approval ID  Have you done that yet?" src="AP1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AP1020_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry, I didnt hear anything  The first step in applying for the ACP discount would be to receive approval through the federal national verifier webpage If approved, you will receive an approval ID  Have you done that yet?" src="AP1020_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have already applied for the discount at the federal verify webpage say 'yes' or press 1If not, say 'no' or press 2'" src="AP1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have already applied for the discount at the federal verify webpage say 'yes' or press 1If not, say 'no' or press 2'" src="AP1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1020_nm1_01">
                <prompt-segments>
                  <audiofile text="Have you applied for the discount at the federal verifier webpage yet?" src="AP1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have already applied for the discount at the federal verify webpage say 'yes' or press 1If not, say 'no' or press 2'" src="AP1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have already applied for the discount at the federal verify webpage say 'yes' or press 1If not, say 'no' or press 2'" src="AP1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AP1020_FirstStep_DM.grxml" count="1"/>
          <dtmfgrammars filename="AP1020_FirstStep_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AP1025_FullProcess_DM" type="CUST">
      <session-mapping key="saidOperatorAcpAt1025" value="GlobalVars.saidOperatorAcpAt1025" type="String"/>
      <session-mapping key="AcpSMSFailure" value="GlobalVars.AcpSMSFailure" type="String"/>
      <session-mapping key="stateVar" value="GlobalVars.AcpSMSFailure != undefined ? GlobalVars.AcpSMSFailure : false" type="String"/>
      <session-mapping key="GlobalVars.operatorReqCount_AC1025" expr="GlobalVars.operatorReqCount_AC1025 != undefined ? GlobalVars.operatorReqCount_AC1025 : 0"/>
      <success>
        <action label="repeat_that">
          <action next="AP1025_FullProcess_DM"/>
        </action>
        <action label="text_me">
          <session-mapping key="GlobalVars.campaignId" expr="GlobalVars.GetBCSParameters.acp_info_campaign_id"/>
          <action next="AP1010_SendACPLinks_DB_DA"/>
        </action>
        <action label="main_menu">
          <action next="getReturnLink()"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperatorAcpAt1025" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.operatorReqCount_AC1025 " expr="GlobalVars.operatorReqCount_AC1025 +1"/>
          <if cond="GlobalVars.operatorReqCount_AC1025 == 1">
            <audio>
              <prompt id="AP1025_Operator_01">
                <prompt-segments>
                  <audiofile text="Our representatives cannot complete the national verifier application on your behalf  You can call them direct at 800 324 9473 again the number is 800-324-9472'" src="AP1025_Operator_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <if cond="AcpSMSFailure == true">
              <audio>
                <prompt id="AP1025_command_01">
                  <prompt-segments>
                    <audiofile text="To hear the ACP information again say, repeat that  You can also say main menu" src="AP1025_command_01.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <else>
                <audio>
                  <prompt id="AP1025_command_02">
                    <prompt-segments>
                      <audiofile text="To hear the ACP information again say, repeat that  You can also say main menu" src="AP1025_command_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </audio>
              </else>
            </if>
            <action next="AP1025_FullProcess_DM"/>
            <else>
              <action next="AP1065_CallTransfer_SD"/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_ini_01" cond="saidOperatorAcpAt1025 != true">
                  <prompt-segments>
                    <audiofile text="Sorry, I wasn't able to send the text  But here is how the process works" src="AP1025_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_ini_02" cond="saidOperatorAcpAt1025 != true">
                    <prompt-segments>
                      <audiofile text="Ok, that's the first step in applying for this federal discount Here's how it works" src="AP1025_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="AP1025_ini_05" cond="saidOperatorAcpAt1025 != true">
                <prompt-segments>
                  <audiofile text="Keep in mind our representatives can't complete these steps on your behalf  You'll have a chance to repeat this information when I'm doneFirst you need to submit an application at the following federal webpage a c p benefitorg  If approved for the discount you will be provided an 'approval ID'You can also call them direct at 800 324 9473For the second step you will use this approval ID to apply your discount to your Metro by T-Mobile account  To do this you'll log into 'my account' and go the profile section Scroll down until you see the link for the Affordable connectivity program  From there just follow the instructions to apply the discount to your billYou'll receive a text message within 3-5 business days of submitting your application with Metro that will contain approval information Please note, it may come under a different sender other than metro by Tmobile If approved, your ACP discount will apply to your account within 1-2 billing cycles" src="AP1025_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_ini_03" cond="saidOperatorAcpAt1025 != true">
                  <prompt-segments>
                    <audiofile text="You can say 'repeat that' or 'main menu'" src="AP1025_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_ini_04" cond="saidOperatorAcpAt1025 != true">
                    <prompt-segments>
                      <audiofile text="To hear this information again say, 'repeat that'I can also text a link to our ACP information page just say, 'text me'  You can also say 'main menu'" src="AP1025_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AP1025_FullProcess_DM_initial"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_ini_01" cond="saidOperatorAcpAt1025 != true">
                  <prompt-segments>
                    <audiofile text="Sorry, I wasn't able to send the text  But here is how the process works" src="AP1025_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_ini_02" cond="saidOperatorAcpAt1025 != true">
                    <prompt-segments>
                      <audiofile text="Ok, that's the first step in applying for this federal discount Here's how it works" src="AP1025_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="AP1025_ini_05" cond="saidOperatorAcpAt1025 != true">
                <prompt-segments>
                  <audiofile text="Keep in mind our representatives can't complete these steps on your behalf  You'll have a chance to repeat this information when I'm doneFirst you need to submit an application at the following federal webpage a c p benefitorg  If approved for the discount you will be provided an 'approval ID'You can also call them direct at 800 324 9473For the second step you will use this approval ID to apply your discount to your Metro by T-Mobile account  To do this you'll log into 'my account' and go the profile section Scroll down until you see the link for the Affordable connectivity program  From there just follow the instructions to apply the discount to your billYou'll receive a text message within 3-5 business days of submitting your application with Metro that will contain approval information Please note, it may come under a different sender other than metro by Tmobile If approved, your ACP discount will apply to your account within 1-2 billing cycles" src="AP1025_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_ini_03" cond="saidOperatorAcpAt1025 != true">
                  <prompt-segments>
                    <audiofile text="You can say 'repeat that' or 'main menu'" src="AP1025_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_ini_04" cond="saidOperatorAcpAt1025 != true">
                    <prompt-segments>
                      <audiofile text="To hear this information again say, 'repeat that'I can also text a link to our ACP information page just say, 'text me'  You can also say 'main menu'" src="AP1025_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_nm1_01">
                  <prompt-segments>
                    <audiofile text="You can say 'repeat that' or if you'd like, I can text you a link to the ACP information page, just say 'text me'  You can also say 'main menu'" src="AP1025_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_nm1_02">
                    <prompt-segments>
                      <audiofile text="You can say 'repeat that' or 'main menu'" src="AP1025_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'repeat that' or press 1  If you'd like a link to the ACP information page, say 'text me', or press 2   You can also say 'main menu', or  press 3" src="AP1025_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_nm2_02">
                    <prompt-segments>
                      <audiofile text="You can say 'repeat that' or press 1main menu' or  press 2" src="AP1025_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'repeat that' or press 1  If you'd like a link to the ACP information page, say 'text me', or press 2   You can also say 'main menu', or  press 3" src="AP1025_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_nm2_02">
                    <prompt-segments>
                      <audiofile text="You can say 'repeat that' or press 1main menu' or  press 2" src="AP1025_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_nm1_01">
                  <prompt-segments>
                    <audiofile text="You can say 'repeat that' or if you'd like, I can text you a link to the ACP information page, just say 'text me'  You can also say 'main menu'" src="AP1025_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_nm1_02">
                    <prompt-segments>
                      <audiofile text="You can say 'repeat that' or 'main menu'" src="AP1025_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'repeat that' or press 1  If you'd like a link to the ACP information page, say 'text me', or press 2   You can also say 'main menu', or  press 3" src="AP1025_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_nm2_02">
                    <prompt-segments>
                      <audiofile text="You can say 'repeat that' or press 1main menu' or  press 2" src="AP1025_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'repeat that' or press 1  If you'd like a link to the ACP information page, say 'text me', or press 2   You can also say 'main menu', or  press 3" src="AP1025_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_nm2_02">
                    <prompt-segments>
                      <audiofile text="You can say 'repeat that' or press 1main menu' or  press 2" src="AP1025_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_ini_01">
                  <prompt-segments>
                    <audiofile text="Sorry, I wasn't able to send the text  But here is how the process works" src="AP1025_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_ini_02">
                    <prompt-segments>
                      <audiofile text="Ok, that's the first step in applying for this federal discount Here's how it works" src="AP1025_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="AP1025_ini_05">
                <prompt-segments>
                  <audiofile text="Keep in mind our representatives can't complete these steps on your behalf  You'll have a chance to repeat this information when I'm doneFirst you need to submit an application at the following federal webpage a c p benefitorg  If approved for the discount you will be provided an 'approval ID'You can also call them direct at 800 324 9473For the second step you will use this approval ID to apply your discount to your Metro by T-Mobile account  To do this you'll log into 'my account' and go the profile section Scroll down until you see the link for the Affordable connectivity program  From there just follow the instructions to apply the discount to your billYou'll receive a text message within 3-5 business days of submitting your application with Metro that will contain approval information Please note, it may come under a different sender other than metro by Tmobile If approved, your ACP discount will apply to your account within 1-2 billing cycles" src="AP1025_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="AcpSMSFailure == true">
                <prompt id="AP1025_ini_03">
                  <prompt-segments>
                    <audiofile text="You can say 'repeat that' or 'main menu'" src="AP1025_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AP1025_ini_04">
                    <prompt-segments>
                      <audiofile text="To hear this information again say, 'repeat that'I can also text a link to our ACP information page just say, 'text me'  You can also say 'main menu'" src="AP1025_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AP1025_FullProcess_DM.jsp" count="1">
            <param name="stateVar" value="stateVarVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="AP1025_FullProcess_DM_dtmf.jsp" count="1">
            <param name="stateVar" value="stateVarVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AP1030_SecondStep_DM" type="YSNO">
      <success>
        <action label="true">
          <action next="AP1040_ACPMenu_DM"/>
        </action>
        <action label="false">
          <action next="AP1035_ApplyMetroSite_DM"/>
        </action>
        <action label="operator">
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperatorAcp" value="true" type="Boolean"/>
          <audio>
            <prompt id="AP1030_Operator_01">
              <prompt-segments>
                <audiofile text="Our representatives cannot complete the application on your behalf " src="AP1030_Operator_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="AP1035_ApplyMetroSite_DM"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AP1030_ini_01">
                <prompt-segments>
                  <audiofile text="Ok the next step would be to apply the discount to your Metro by Tmobile account   Have you finished this step as well? " src="AP1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="AP1030_SecondStep_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AP1030_ini_01">
                <prompt-segments>
                  <audiofile text="Ok the next step would be to apply the discount to your Metro by Tmobile account   Have you finished this step as well? " src="AP1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AP1030_ni1_01">
                <prompt-segments>
                  <audiofile text="The next step would be to apply the discount to your Metro by Tmobile account   Have you finished this step as well? " src="AP1030_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you've applied the discount to your Metro by Tmobile account, say 'yes' or press 1  If you haven't, say 'no' or press 2 " src="AP1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you've applied the discount to your Metro by Tmobile account, say 'yes' or press 1  If you haven't, say 'no' or press 2 " src="AP1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1030_nm1_01">
                <prompt-segments>
                  <audiofile text="Have you applied your discount to your Metro by T-Mobile account? " src="AP1030_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you've applied the discount to your Metro by Tmobile account, say 'yes' or press 1  If you haven't, say 'no' or press 2 " src="AP1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you've applied the discount to your Metro by Tmobile account, say 'yes' or press 1  If you haven't, say 'no' or press 2 " src="AP1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AP1030_ini_01">
                <prompt-segments>
                  <audiofile text="Ok the next step would be to apply the discount to your Metro by Tmobile account   Have you finished this step as well? " src="AP1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AP1030_SecondStep_DM.grxml" count="1"/>
          <dtmfgrammars filename="AP1030_SecondStep_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AP1035_ApplyMetroSite_DM" type="CUST">
      <session-mapping key="saidOperatorAcp" value="GlobalVars.saidOperatorAcp" type="String"/>
      <success>
        <action label="repeat_that">
          <action next="AP1035_ApplyMetroSite_DM"/>
        </action>
        <action label="main_menu">
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AP1035_ini_01" cond="saidOperatorAcp == true">
                <prompt-segments>
                  <audiofile text="Here are some instructions" src="AP1035_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1035_ini_02" cond="saidOperatorAcp != true">
                <prompt-segments>
                  <audiofile text="Ok, here is how you apply" src="AP1035_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1035_ini_03">
                <prompt-segments>
                  <audiofile text="Make sure you have your approval ID handy Then at the metro by Tmobile website log into 'my account' and go to your profile  Once there, scroll down until you see the link for the Affordable connectivity program  From there just follow the instructions to apply the discount to your billYou'll receive a text message within 3-5 business days of submitting your application that will contain approval information If approved, your ACP discount will apply to your account within 1-2 billing cycles To hear this information again say, 'repeat that', You can also say 'main menu'" src="AP1035_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AP1035_ini_01" cond="saidOperatorAcp == true">
                <prompt-segments>
                  <audiofile text="Here are some instructions" src="AP1035_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1035_ini_02" cond="saidOperatorAcp != true">
                <prompt-segments>
                  <audiofile text="Ok, here is how you apply" src="AP1035_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1035_ini_03">
                <prompt-segments>
                  <audiofile text="Make sure you have your approval ID handy Then at the metro by Tmobile website log into 'my account' and go to your profile  Once there, scroll down until you see the link for the Affordable connectivity program  From there just follow the instructions to apply the discount to your billYou'll receive a text message within 3-5 business days of submitting your application that will contain approval information If approved, your ACP discount will apply to your account within 1-2 billing cycles To hear this information again say, 'repeat that', You can also say 'main menu'" src="AP1035_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AP1035_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like to hear that again say, 'repeat that' You can also say 'main menu " src="AP1035_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1035_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that' or press 1  'main menu' or press 2 " src="AP1035_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1035_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that' or press 1  'main menu' or press 2 " src="AP1035_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1035_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like to hear that again say, 'repeat that' You can also say 'main menu " src="AP1035_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1035_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that' or press 1  'main menu' or press 2 " src="AP1035_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1035_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that' or press 1  'main menu' or press 2 " src="AP1035_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AP1035_ini_01" cond="saidOperatorAcp == true">
                <prompt-segments>
                  <audiofile text="Here are some instructions" src="AP1035_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1035_ini_02" cond="saidOperatorAcp != true">
                <prompt-segments>
                  <audiofile text="Ok, here is how you apply" src="AP1035_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1035_ini_03">
                <prompt-segments>
                  <audiofile text="Make sure you have your approval ID handy Then at the metro by Tmobile website log into 'my account' and go to your profile  Once there, scroll down until you see the link for the Affordable connectivity program  From there just follow the instructions to apply the discount to your billYou'll receive a text message within 3-5 business days of submitting your application that will contain approval information If approved, your ACP discount will apply to your account within 1-2 billing cycles To hear this information again say, 'repeat that', You can also say 'main menu'" src="AP1035_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AP1035_ApplyMetroSite_DM.grxml" count="1"/>
          <dtmfgrammars filename="AP1035_ApplyMetroSite_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AP1040_ACPMenu_DM" type="CUST">
      <success>
        <action label="repeat_that">
          <action next="AP1040_ACPMenu_DM"/>
        </action>
        <action label="no_text">
          <action next="AP1055_TextNotReceived_DM"/>
        </action>
        <action label="discount_dropped">
          <action next="AP1045_DiscountDropped_DM"/>
        </action>
        <action label="declined">
          <action next="AP1050_Declined_SM_CS"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AP1040_ini_01">
                <prompt-segments>
                  <audiofile text="Great, it can take 3-5 business days to receive a text with your application status Please note, it may come under a different sender other than metro by Tmobile If approved, the discount will apply to your account within 1-2 billing cyclesTo hear that again say, 'repeat that'  You can also say 'I didn't receive a text', I was declined, or 'my discount was dropped'" src="AP1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AP1040_ACPMenu_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AP1040_ini_01">
                <prompt-segments>
                  <audiofile text="Great, it can take 3-5 business days to receive a text with your application status Please note, it may come under a different sender other than metro by Tmobile If approved, the discount will apply to your account within 1-2 billing cyclesTo hear that again say, 'repeat that'  You can also say 'I didn't receive a text', I was declined, or 'my discount was dropped'" src="AP1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AP1040_nm1_01">
                <prompt-segments>
                  <audiofile text="To hear that again say, 'repeat that'  You can also say 'I didn't receive a text', I was declined, or 'my discount was dropped'" src="AP1040_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1040_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'repeat that',or press 1  'I didn't receive a text', or press 2  'I was declined', or press 3  'my discount was dropped', or press 4 " src="AP1040_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1040_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'repeat that',or press 1  'I didn't receive a text', or press 2  'I was declined', or press 3  'my discount was dropped', or press 4 " src="AP1040_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1040_nm1_01">
                <prompt-segments>
                  <audiofile text="To hear that again say, 'repeat that'  You can also say 'I didn't receive a text', I was declined, or 'my discount was dropped'" src="AP1040_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1040_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'repeat that',or press 1  'I didn't receive a text', or press 2  'I was declined', or press 3  'my discount was dropped', or press 4 " src="AP1040_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1040_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'repeat that',or press 1  'I didn't receive a text', or press 2  'I was declined', or press 3  'my discount was dropped', or press 4 " src="AP1040_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AP1040_ini_01">
                <prompt-segments>
                  <audiofile text="Great, it can take 3-5 business days to receive a text with your application status Please note, it may come under a different sender other than metro by Tmobile If approved, the discount will apply to your account within 1-2 billing cyclesTo hear that again say, 'repeat that'  You can also say 'I didn't receive a text', I was declined, or 'my discount was dropped'" src="AP1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AP1040_ACPMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="AP1040_ACPMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AP1045_DiscountDropped_DM" type="YSNO">
      <success>
        <action label="true">
          <action next="AP1045_DiscountDropped_DM"/>
        </action>
        <action label="false">
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AP1045_ini_01">
                <prompt-segments>
                  <audiofile text="It's possible to lose your ACP discount if you become ineligible due to FCC rules, non-usage, future dated rate plan changes, or if you changed your due date  You can apply again after first ensuring you are still approved with the National Verifier   The national verifier website is acpbenefitorg Would you like to hear that again? " src="AP1045_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="AP1045_DiscountDropped_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AP1045_ini_01">
                <prompt-segments>
                  <audiofile text="It's possible to lose your ACP discount if you become ineligible due to FCC rules, non-usage, future dated rate plan changes, or if you changed your due date  You can apply again after first ensuring you are still approved with the National Verifier   The national verifier website is acpbenefitorg Would you like to hear that again? " src="AP1045_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AP1045_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that information again? " src="AP1045_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1045_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again say 'yes' or press 1  Otherwise say 'no' or press 2" src="AP1045_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1045_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again say 'yes' or press 1  Otherwise say 'no' or press 2" src="AP1045_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1045_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that information again? " src="AP1045_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1045_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again say 'yes' or press 1  Otherwise say 'no' or press 2" src="AP1045_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1045_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again say 'yes' or press 1  Otherwise say 'no' or press 2" src="AP1045_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AP1045_ini_01">
                <prompt-segments>
                  <audiofile text="It's possible to lose your ACP discount if you become ineligible due to FCC rules, non-usage, future dated rate plan changes, or if you changed your due date  You can apply again after first ensuring you are still approved with the National Verifier   The national verifier website is acpbenefitorg Would you like to hear that again? " src="AP1045_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AP1045_DiscountDropped_DM.grxml" count="1"/>
          <dtmfgrammars filename="AP1045_DiscountDropped_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <custom-state id="AP1050_Declined_SM_CS">
      <success>
        <action label="text_me">
          <session-mapping key="GlobalVars.campaignId" expr="GlobalVars.GetBCSParameters.acp_declined_campaign_id"/>
          <action next="AP1010_SendACPLinks_DB_DA"/>
        </action>
        <action label="main_menu">
          <action next="getReturnLink()"/>
        </action>
      </success>
      <command>
        <action label="operator"/>
      </command>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
    </custom-state>

    <dm-state id="AP1055_TextNotReceived_DM" type="YSNO">
      <success>
        <action label="true">
          <action next="AP1065_CallTransfer_SD"/>
        </action>
        <action label="false">
          <action next="AP1060_WaitFullFiveDays_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AP1055_ini_01">
                <prompt-segments>
                  <audiofile text="It can take up to 5 business days to receive a text Has it been 5 business days since you applied at the Metro by Tmobile website? " src="AP1055_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AP1055_TextNotReceived_DM_initial"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AP1055_ini_01">
                <prompt-segments>
                  <audiofile text="It can take up to 5 business days to receive a text Has it been 5 business days since you applied at the Metro by Tmobile website? " src="AP1055_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AP1055_nm1_01">
                <prompt-segments>
                  <audiofile text="It can take up to 5 business days to receive a text Has it been 5 days since you applied at the Metro by Tmobile website? " src="AP1055_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1055_nm2_01">
                <prompt-segments>
                  <audiofile text="If it's been 5 days since you applied at the Metro by Tmobile website say yes or press 1  If not say no or press 2" src="AP1055_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1055_nm2_01">
                <prompt-segments>
                  <audiofile text="If it's been 5 days since you applied at the Metro by Tmobile website say yes or press 1  If not say no or press 2" src="AP1055_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1055_nm1_01">
                <prompt-segments>
                  <audiofile text="It can take up to 5 business days to receive a text Has it been 5 days since you applied at the Metro by Tmobile website? " src="AP1055_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1055_nm2_01">
                <prompt-segments>
                  <audiofile text="If it's been 5 days since you applied at the Metro by Tmobile website say yes or press 1  If not say no or press 2" src="AP1055_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AP1055_nm2_01">
                <prompt-segments>
                  <audiofile text="If it's been 5 days since you applied at the Metro by Tmobile website say yes or press 1  If not say no or press 2" src="AP1055_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AP1055_TextNotReceived_DM.grxml" count="1"/>
          <dtmfgrammars filename="AP1055_TextNotReceived_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="AP1060_WaitFullFiveDays_PP">
      <audio>
        <prompt id="AP1060_out_01">
          <prompt-segments>
            <audiofile text="Ok please wait the full 5 business days and call us back if you still have not received a text message " src="AP1060_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="AP1062_PlaySMSOutcome_PP">
      <if cond="GlobalVars.AP1010failure == false">
        <audio>
          <prompt id="AP1062_out_01">
            <prompt-segments>
              <audiofile text="Ok, your text has been sent " src="AP1062_out_01.wav"/>
            </prompt-segments>
          </prompt>
        </audio>
        <action next="getReturnLink()"/>
        <else>
          <audio>
            <prompt id="AP1062_out_02">
              <prompt-segments>
                <audiofile text="Sorry, I wasn't able to send the text" src="AP1062_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="AP1065_CallTransfer_SD"/>
        </else>
      </if>
    </play-state>

    <subdialog-state id="AP1065_CallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="AP1065_CallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AP1065_CallTransfer_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

  </dialog>
  