<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="BalanceBreakdown_Dialog">
    <decision-state id="BB1001_CheckContext_DS">
      <session-mapping key="GlobalVars.linesPlayedCounter" expr="0"/>
      <if cond="GlobalVars.loggedIn == true">
        <action next="BB1002_GetAutoPayMessages_DB_DA"/>
        <else>
          <action next="BB1005_CalculatePaidFeatures_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="BB1002_GetAutoPayMessages_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn != undefined ? GlobalVars.mdn  : GlobalVars.trn" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="GetAutoPayMessages" classname="com.nuance.metro.dataaccess.GetAutoPayMessages">
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetAutoPayMessages &amp;&amp; GetAutoPayMessages.status &amp;&amp; GetAutoPayMessages.status.toUpperCase() == 'SUCCESS'">
          <session-mapping key="GlobalVars.GetAutoPayMessages" expr="GetAutoPayMessages"/>
          <if cond="GlobalVars.GetAutoPayMessages.messageCode == '9003'">
            <action next="BB1007_AskOneTimePayment_DM"/>
            <else>
              <action next="BB1005_CalculatePaidFeatures_DB_DA"/>
            </else>
          </if>
          <else>
            <action next="BB1005_CalculatePaidFeatures_DB_DA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <data-access-state id="BB1005_CalculatePaidFeatures_DB_DA">
      <session-mapping key="features" value="GlobalVars.GetAccountDetails.features" type="String"/>
      <data-access id="CalculatePaidFeatures" classname="com.nuance.metro.dataaccess.CalculatePaidFeatures">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="features"/>
        </inputs>
        <outputs>
          <output-variable name="paidFeatures"/>
          <output-variable name="paidThirdPartyFeatures"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="(CalculatePaidFeatures &amp;&amp; CalculatePaidFeatures.status == 'Success')">
          <session-mapping key="GlobalVars.CalculatePaidFeatures" expr="CalculatePaidFeatures"/>
          <session-mapping key="GlobalVars.paidFeatures" expr="CalculatePaidFeatures.paidFeatures"/>
          <session-mapping key="GlobalVars.paidThirdPartyFeatures" expr="CalculatePaidFeatures.paidThirdPartyFeatures"/>
          <action next="BB1010_CheckBreakdownSize_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <dm-state id="BB1007_AskOneTimePayment_DM" type="YSNO">
      <session-mapping key="autoEligPlan" value="GlobalVars.GetAccountDetails.isAutopayEligPlanExists" type="String"/>
      <success>
        <action label="true">
          <if cond="autoEligPlan == true">
            <audio>
              <prompt id="BB1007_out_01">
                <prompt-segments>
                  <audiofile text="By the way, manual payments do not qualify for the self-pay discount " src="BB1007_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </if>
          <action next="BB1315_MakePayment_SD"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="BB1007_out_02">
              <prompt-segments>
                <audiofile text="no problem" src="BB1007_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="BB1005_CalculatePaidFeatures_DB_DA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BB1007_ini_01">
                <prompt-segments>
                  <audiofile text="We were unable to process your last auto payment Would you like to make a one-time payment now?" src="BB1007_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BB1007_ini_01">
                <prompt-segments>
                  <audiofile text="We were unable to process your last auto payment Would you like to make a one-time payment now?" src="BB1007_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BB1007_nm1_01">
                <prompt-segments>
                  <audiofile text="We were unable to process your last autopayment Would you like to make a one-time payment now?" src="BB1007_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1007_nm2_01">
                <prompt-segments>
                  <audiofile text="We were unable to process your last autopayment If you'd like to make a one-time payment now press 1  if not press 2" src="BB1007_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1007_nm2_01">
                <prompt-segments>
                  <audiofile text="We were unable to process your last autopayment If you'd like to make a one-time payment now press 1  if not press 2" src="BB1007_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1007_nm1_01">
                <prompt-segments>
                  <audiofile text="We were unable to process your last autopayment Would you like to make a one-time payment now?" src="BB1007_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1007_nm2_01">
                <prompt-segments>
                  <audiofile text="We were unable to process your last autopayment If you'd like to make a one-time payment now press 1  if not press 2" src="BB1007_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1007_nm2_01">
                <prompt-segments>
                  <audiofile text="We were unable to process your last autopayment If you'd like to make a one-time payment now press 1  if not press 2" src="BB1007_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BB1007_ini_01">
                <prompt-segments>
                  <audiofile text="We were unable to process your last auto payment Would you like to make a one-time payment now?" src="BB1007_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="BB1007_AskOneTimePayment_DM.grxml" count="1"/>
          <dtmfgrammars filename="BB1007_AskOneTimePayment_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="BB1010_CheckBreakdownSize_DS">
      <session-mapping key="paidMetroFeaturesLength" value="(GlobalVars.CalculatePaidFeatures.paidFeatures!=undefined)?GlobalVars.CalculatePaidFeatures.paidFeatures.length:0" type="String"/>
      <session-mapping key="paidTPFeaturesLength" value="(GlobalVars.CalculatePaidFeatures.paidThirdPartyFeatures!=undefined)?GlobalVars.CalculatePaidFeatures.paidThirdPartyFeatures.length:0" type="String"/>
      <session-mapping key="isSupported" value="(GlobalVars.isOnFamilyPlan == true || GlobalVars.isOnFamilyPlan == 'true')?GlobalVars.GetAccountDetails.isMultiLinePlansSupported:GlobalVars.GetAccountDetails.isSupported" type="String"/>
      <session-mapping key="numPaidFeatures" expr="paidMetroFeaturesLength + paidTPFeaturesLength"/>
      <if cond="(isSupported == true) &amp;&amp; (numPaidFeatures &lt; 10 || numPaidFeatures == 10) &amp;&amp; !(GlobalVars.accountFutureRequestInd == true || GlobalVars.accountFutureRequestInd == 'true')">
        <action next="BB1015_CheckLines_JDA"/>
        <else>
          <action next="BB1025_Transfer_SD"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="BB1015_CheckLines_DS">
      <if cond="(GlobalVars.isOnFamilyPlan == true || GlobalVars.isOnFamilyPlan == 'true')">
        <action next="BB1020_TransformMultilineInfo_DB_DA"/>
        <else>
          <action next="BB1105_PlayCurrentPlan_PP"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="BB1020_TransformMultilineInfo_DB_DA">
      <data-access id="TransformMultilineInfo" classname="com.nuance.metro.dataaccess.TransformMultilineInfoDB">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="multilineList"/>
        </inputs>
        <outputs>
          <output-variable name="numberLines"/>
          <output-variable name="lineLastFour"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="(TransformMultilineInfo &amp;&amp; TransformMultilineInfo.status == 'Success')">
          <session-mapping key="GlobalVars.TransformMultilineInfo" expr="TransformMultilineInfo"/>
          <session-mapping key="GlobalVars.numberLines" expr="TransformMultilineInfo.numberLines"/>
          <session-mapping key="GlobalVars.lineLastFour" expr="TransformMultilineInfo.lineLastFour"/>
          <if cond="TransformMultilineInfo.numberLines &lt; 5 ||  TransformMultilineInfo.numberLines == 5">
            <action next="BB1205_PlayIntro_PP"/>
            <else>
              <action next="BB1025_Transfer_SD"/>
            </else>
          </if>
          <action next="BB1205_PlayIntro_PP"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <subdialog-state id="BB1025_Transfer_SD">
      <if cond="GlobalVars.isCareTransfer == 'true'">
        <gotodialog next="CallTransfer_Main_Dialog"/>
        <else>
          <gotodialog next="Transfer_Main"/>
        </else>
      </if>
      <action next="BB1025_Transfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BB1025_Transfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <play-state id="BB1105_PlayCurrentPlan_PP">
      <session-mapping key="soc" value="GlobalVars.GetAccountDetails.ratePlan" type="String"/>
      <session-mapping key="currentPlanPromptURL" value="GlobalVars.GetAccountDetails.currentPlanPromptURL" type="String"/>
      <session-mapping key="currentPlanPromptTTS" value="GlobalVars.GetAccountDetails.currentPlanPromptTTS" type="String"/>
      <session-mapping key="ratePlanPrice" value="GlobalVars.GetAccountDetails.ratePlanPrice" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="amountDue" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="arBalance" value="GlobalVars.GetAccountDetails.arBalance != undefined ? GlobalVars.GetAccountDetails.arBalance : 0.0" type="String"/>
      <session-mapping key="totalBalance" value="(parseFloat(amountDue) + parseFloat(dueImmediatelyAmount) + parseFloat(arBalance)).toString()" type="String"/>
      <session-mapping key="totalBalanceCredit" value="(Math.abs(totalBalance)).toString()" type="String"/>
      <audio>
        <prompt id="BB1105_out_01">
          <prompt-segments>
            <audiofile src="BB1105_out_01.wav" text="Now, let me tell you what I see on your account "/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="soc">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayCurrentRatePlan"/>
          <param name="currentPlanPromptURL" value="currentPlanPromptURL" scope="request"/>
          <param name="currentPlanPromptTTS" value="currentPlanPromptTTS" scope="request"/>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BB1105_out_02">
          <prompt-segments>
            <audiofile src="BB1105_out_02.wav" text="It s"/>
          </prompt-segments>
        </prompt>
        <prompt type="currency" expr="ratePlanPrice">
          <param value="false" name="playZeroCents"/>
        </prompt>
        <prompt id="BB1105_out_03">
          <prompt-segments>
            <audiofile src="BB1105_out_03.wav" text="a month, including all taxes and regulatory fees"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BB1110_CheckHaveAddOns_JDA"/>
    </play-state>

    <decision-state id="BB1110_CheckHaveAddOns_DS">
      <if cond="numPaidFeatures &gt; 0">
        <action next="BB1115_PlayAddOns_PP"/>
        <else>
          <action next="BB1120_CheckArBalance_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="BB1115_PlayAddOns_PP">
      <audio>
        <prompt id="BB1115_out_01">
          <prompt-segments>
            <audiofile src="BB1115_out_01.wav" text="For add-on services *on top* of your monthly plan, your balance also includes"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayAddOn_BB1115"/>
        </prompt>
      </audio>
      <action next="BB1120_CheckArBalance_JDA"/>
    </play-state>

    <decision-state id="BB1120_CheckArBalance_DS">
      <if cond="GlobalVars.GetAccountDetails.arBalance != undefined &amp;&amp; GlobalVars.GetAccountDetails.arBalance == 0">
        <action next="BR1130_PlayTotalBalance_PP"/>
        <else>
          <action next="BR1125_PlayArBalance_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="BR1125_PlayArBalance_PP">
      <session-mapping key="arBalance" value="GlobalVars.GetAccountDetails.arBalance" type="String"/>
      <session-mapping key="arBalanceCredit" value="(Math.abs(arBalance)).toString()" type="String"/>
      <audio>
        <if cond="parseFloat(arBalance) &gt; 0">
          <prompt id="BR1125_out_01">
            <prompt-segments>
              <audiofile src="BR1125_out_01.wav" text="The balance includes a prorated charge of "/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="arBalance">
            <param value="f" name="intonation"/>
            <param value="false" name="playZeroCents"/>
          </prompt>
          <prompt id="silence_500ms">
            <prompt-segments>
              <audiofile text="test" src="silence_500ms.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="BR1125_out_02">
            <prompt-segments>
              <audiofile src="BR1125_out_02.wav" text="That s from changes you ve made last month like upgrading your plan or adding services"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="BR1125_out_03">
              <prompt-segments>
                <audiofile src="BR1125_out_03.wav" text="Your account also has a credit for"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="arBalanceCredit">
              <param value="f" name="intonation"/>
              <param value="false" name="playZeroCents"/>
            </prompt>
            <prompt id="BR1125_out_04">
              <prompt-segments>
                <audiofile src="BR1125_out_04.wav" text="which will be applied to your next payment"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BR1130_PlayTotalBalance_PP"/>
    </play-state>

    <play-state id="BR1130_PlayTotalBalance_PP">
      <session-mapping key="remainingDays" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails.payDate" type="String"/>
      <session-mapping key="payDateNextMonth" value="GlobalVars.GetAccountDetails.payDateNextMonth" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="amountDue" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="isSuspended" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" type="String"/>
      <session-mapping key="arBalance" value="GlobalVars.GetAccountDetails.arBalance" type="String"/>
      <session-mapping key="remainingBalance" value="(parseFloat(amountDue)).toString()" type="String"/>
      <session-mapping key="totalBalance" value="(parseFloat(amountDue)).toString()" type="String"/>
      <session-mapping key="totalBalanceCredit" value="(Math.abs(totalBalance)).toString()" type="String"/>
      <session-mapping key="remainingBalanceCredit" value="(Math.abs(remainingBalance)).toString()" type="String"/>
      <session-mapping key="isAutopayEnrolled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="isAutopayEligPlanExists" value="GlobalVars.GetAccountDetails.isAutopayEligPlanExists" type="String"/>
      <session-mapping key="messageCode" value="GlobalVars.GetAutoPayMessages &amp;&amp; GlobalVars.GetAutoPayMessages.messageCode  ? GlobalVars.GetAutoPayMessages.messageCode:''" type="String"/>
      <session-mapping key="calculatedAmountAfterDiscount" value="" type="String"/>
      <session-mapping key="discountAmount" value="(GlobalVars.GetAutoPayMessages  &amp;&amp; GlobalVars.GetAutoPayMessages.discountAmount) ? GlobalVars.GetAutoPayMessages.discountAmount: 0.0" type="String"/>
      <session-mapping key="amountAfterDiscount" value="(GlobalVars.GetAutoPayMessages &amp;&amp; GlobalVars.GetAutoPayMessages.amountAfterDiscount) ? GlobalVars.GetAutoPayMessages.amountAfterDiscount: 0.0" type="String"/>
      <session-mapping key="hasValidMessageCode" value="(messageCode != '' &amp;&amp;  (messageCode == '9001' || messageCode == '9002' || messageCode == '9003')) ? true: false" type="String"/>
      <if cond="(GlobalVars.GetAutoPayMessages ) &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
        <session-mapping key="calculatedAmountAfterDiscount" expr="parseFloat(amountDue) - parseFloat(discountAmount)"/>
      </if>
      <audio>
        <if cond="parseFloat(dueImmediatelyAmount) &gt; 3">
          <prompt id="BR1130_out_11" cond="isSuspended == true">
            <prompt-segments>
              <audiofile src="BR1130_out_11.wav" text="The amount due NOW to reactivate your account is "/>
            </prompt-segments>
          </prompt>
          <prompt id="BR1130_out_12" cond="isSuspended == false">
            <prompt-segments>
              <audiofile src="BR1130_out_12.wav" text="To avoid service disruption, the amount you would need to pay immediately is "/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="dueImmediatelyAmount">
            <param value="false" name="playZeroCents"/>
            <param value="m" name="intonation"/>
          </prompt>
          <prompt id="BR1130_out_15" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0)">
            <prompt-segments>
              <audiofile src="BR1130_out_15.wav" text="Then you have a remaining balance of "/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="remainingBalanceCredit" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0)">
            <param value="false" name="playZeroCents"/>
            <param value="m" name="intonation"/>
          </prompt>
          <prompt id="BR1130_out_02" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (remainingDays == 0)">
            <prompt-segments>
              <audiofile src="BR1130_out_02.wav" text="It s due today"/>
            </prompt-segments>
          </prompt>
          <prompt id="BR1130_out_03" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (remainingDays == 1)">
            <prompt-segments>
              <audiofile src="BR1130_out_03.wav" text="It s due tomorrow"/>
            </prompt-segments>
          </prompt>
          <prompt id="BR1130_out_04" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (remainingDays == 2)">
            <prompt-segments>
              <audiofile src="BR1130_out_04.wav" text="It s due the day after tomorrow"/>
            </prompt-segments>
          </prompt>
          <prompt id="BR1130_out_05" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (remainingDays != 0 &amp;&amp; remainingDays != 1  &amp;&amp; remainingDays != 2)">
            <prompt-segments>
              <audiofile src="BR1130_out_05.wav" text="It s due"/>
            </prompt-segments>
          </prompt>
          <prompt type="date" expr="payDate" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (remainingDays != 0 &amp;&amp; remainingDays != 1  &amp;&amp; remainingDays != 2)">
            <param name="dateFormat" value="MMM dd, yyyy"/>
            <param name="playDayOfMonth" value="true"/>
            <param name="playYear" value="false"/>
            <param name="playDayOfTheWeek" value="false"/>
            <param name="intonation" value="f"/>
          </prompt>
          <else>
            <prompt id="BR1130_out_07" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (isAutopayEnrolled  == true &amp;&amp; isAutopayEligPlanExists == true &amp;&amp; hasValidMessageCode == true)">
              <prompt-segments>
                <audiofile src="BR1130_out_07.wav" text="Your balance including your autopay discount is "/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="amountAfterDiscount" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (isAutopayEnrolled  == true &amp;&amp; isAutopayEligPlanExists == true &amp;&amp; hasValidMessageCode == true) &amp;&amp; (messageCode == '9001')">
              <param value="false" name="playZeroCents"/>
              <param value="f" name="intonation"/>
            </prompt>
            <prompt type="currency" expr="calculatedAmountAfterDiscount" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (isAutopayEnrolled  == true &amp;&amp; isAutopayEligPlanExists == true &amp;&amp; hasValidMessageCode == true) &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
              <param value="false" name="playZeroCents"/>
              <param value="f" name="intonation"/>
            </prompt>
            <prompt id="BR1130_out_01" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; !(isAutopayEnrolled == true &amp;&amp; hasValidMessageCode == true)">
              <prompt-segments>
                <audiofile src="BR1130_out_01.wav" text="So again, your balance is "/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="totalBalanceCredit" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; !(isAutopayEnrolled == true &amp;&amp; hasValidMessageCode == true)">
              <param value="false" name="playZeroCents"/>
              <param value="f" name="intonation"/>
            </prompt>
            <prompt id="BR1130_out_02" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (remainingDays == 0)">
              <prompt-segments>
                <audiofile src="BR1130_out_02.wav" text="It s due today"/>
              </prompt-segments>
            </prompt>
            <prompt id="BR1130_out_03" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (remainingDays == 1)">
              <prompt-segments>
                <audiofile src="BR1130_out_03.wav" text="It s due tomorrow"/>
              </prompt-segments>
            </prompt>
            <prompt id="BR1130_out_04" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (remainingDays == 2)">
              <prompt-segments>
                <audiofile src="BR1130_out_04.wav" text="It s due the day after tomorrow"/>
              </prompt-segments>
            </prompt>
            <prompt id="BR1130_out_05" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (remainingDays != 0 &amp;&amp; remainingDays != 1  &amp;&amp; remainingDays != 2)">
              <prompt-segments>
                <audiofile src="BR1130_out_05.wav" text="It s due"/>
              </prompt-segments>
            </prompt>
            <prompt type="date" expr="payDate" cond="(parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0) &amp;&amp; (remainingDays != 0 &amp;&amp; remainingDays != 1  &amp;&amp; remainingDays != 2)">
              <param name="dateFormat" value="MMM dd, yyyy"/>
              <param name="playDayOfMonth" value="true"/>
              <param name="playYear" value="false"/>
              <param name="playDayOfTheWeek" value="false"/>
              <param name="intonation" value="f"/>
            </prompt>
            <prompt id="BR1130_out_10" cond="!(((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)))">
              <prompt-segments>
                <audiofile src="BR1130_out_10.wav" text="You re all paid up for now, and your next payment is due"/>
              </prompt-segments>
            </prompt>
            <prompt type="date" expr="payDateNextMonth" cond="!(((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)))">
              <param name="dateFormat" value="MMM dd, yyyy"/>
              <param name="playDayOfMonth" value="true"/>
              <param name="playYear" value="false"/>
              <param name="playDayOfTheWeek" value="false"/>
              <param name="intonation" value="f"/>
            </prompt>
          </else>
        </if>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BR1130_out_14">
          <prompt-segments>
            <audiofile src="BR1130_out_14.wav" text="You can also get this information in the myMetro app"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BB1305_WrapMenu_DM"/>
    </play-state>

    <play-state id="BB1205_PlayIntro_PP">
      <session-mapping key="numberLines" value="GlobalVars.numberLines" type="String"/>
      <audio>
        <prompt id="BB1205_out_04">
          <prompt-segments>
            <audiofile src="BB1205_out_04.wav" text="Now, let me tell you what I see on your account   You can say 'repeat' or 'next' at any time"/>
          </prompt-segments>
        </prompt>
        <prompt id="BB1205_out_01">
          <prompt-segments>
            <audiofile src="BB1205_out_01.wav" text="There s"/>
          </prompt-segments>
        </prompt>
        <prompt type="digits" expr="numberLines">
          <param name="intonation" value="m"/>
        </prompt>
        <prompt id="BB1205_out_02">
          <prompt-segments>
            <audiofile src="BB1205_out_02.wav" text="lines on your account"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BB1210_PlayLinePlan_PP"/>
    </play-state>

    <play-state id="BB1210_PlayLinePlan_PP">
      <session-mapping key="numberLines" value="GlobalVars.numberLines" type="String"/>
      <session-mapping key="linesPlayedCounter" value="GlobalVars.linesPlayedCounter" type="String"/>
      <session-mapping key="currentPlanPromptURL" value="" type="String"/>
      <session-mapping key="currentPlanPromptTTS" value="" type="String"/>
      <session-mapping key="lastFour" value="" type="String"/>
      <session-mapping key="ratePlanPrice" value="0" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="amountDue" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="arBalance" value="GlobalVars.GetAccountDetails.arBalance != undefined ? GlobalVars.GetAccountDetails.arBalance : 0.0" type="String"/>
      <session-mapping key="totalBalance" value="(parseFloat(amountDue) + parseFloat(dueImmediatelyAmount) + parseFloat(arBalance)).toString()" type="String"/>
      <session-mapping key="totalBalanceCredit" value="(Math.abs(totalBalance)).toString()" type="String"/>
      <audio>
        <prompt id="BB1210_out_01" cond="linesPlayedCounter ==(numberLines-1) ">
          <prompt-segments>
            <audiofile src="BB1210_out_01.wav" text="Finally"/>
          </prompt-segments>
        </prompt>
        <prompt id="BB1210_out_02">
          <prompt-segments>
            <audiofile src="BB1210_out_02.wav" text="The line ending in"/>
          </prompt-segments>
        </prompt>
        <prompt type="digits" expr="lastFour">
          <param name="intonation" value="m"/>
        </prompt>
        <prompt id="BB1210_out_03">
          <prompt-segments>
            <audiofile src="BB1210_out_03.wav" text="is on"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayCurrentRatePlan"/>
          <param name="currentPlanPromptURL" value="currentPlanPromptURL" scope="request"/>
          <param name="currentPlanPromptTTS" value="currentPlanPromptTTS" scope="request"/>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BB1210_out_05">
          <prompt-segments>
            <audiofile src="BB1210_out_05.wav" text="It s"/>
          </prompt-segments>
        </prompt>
        <prompt type="currency" expr="ratePlanPrice">
          <param value="m" name="intonation"/>
          <param value="false" name="playZeroCents"/>
        </prompt>
        <prompt id="BB1210_out_06" cond="linesPlayedCounter == 0">
          <prompt-segments>
            <audiofile src="BB1210_out_06.wav" text="a month, including all taxes and regulatory fees"/>
          </prompt-segments>
        </prompt>
        <prompt id="BB1210_out_07" cond="linesPlayedCounter != 0">
          <prompt-segments>
            <audiofile src="BB1210_out_07.wav" text="a month"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BB1215_CheckLineAddOns_JDA"/>
    </play-state>

    <decision-state id="BB1215_CheckLineAddOns_DS">
      <session-mapping key="linePaidFeatures" value="false" type="Boolean"/>
      <if cond="linePaidFeatures == false">
        <action next="BB1225_SBINavigate_DM"/>
        <else>
          <action next="BB1220_PlayLineAddOns_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="BB1220_PlayLineAddOns_PP">
      <session-mapping key="linesPlayedCounter" value="GlobalVars.linesPlayedCounter" type="String"/>
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayLineAddOns_BB1220"/>
          <param name="linesPlayedCounter" value="linesPlayedCounter" scope="request"/>
        </prompt>
      </audio>
      <action next="BB1225_SBINavigate_DM"/>
    </play-state>

    <dm-state id="BB1225_SBINavigate_DM" type="CUST">
      <success>
        <action label="next" next="BB1230_CheckLinesLeft_JDA">
          <session-mapping key="GlobalVars.linesPlayedCounter" expr="GlobalVars.linesPlayedCounter + 1"/>
        </action>
        <action label="repeat" next="BB1210_PlayLinePlan_PP"/>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BB1225_ini_01">
                <prompt-segments>
                  <audiofile src="BB1225_ini_01.wav" text="Repeat, or next?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="" text="" id="BB1225_SBINavigate_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BB1225_ni1_01">
                <prompt-segments>
                  <audiofile src="BB1225_ni1_01.wav" text="Next"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BB1225_ini_01">
                <prompt-segments>
                  <audiofile src="BB1225_ini_01.wav" text="Repeat, or next?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="BB1225_SBINavigate_DM.grxml" count="1"/>
          <dtmfgrammars filename="BB1225_SBINavigate_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="5000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="BB1230_CheckLinesLeft_DS">
      <session-mapping key="numberLines" value="GlobalVars.numberLines" type="String"/>
      <session-mapping key="linesPlayedCounter" value="GlobalVars.linesPlayedCounter" type="String"/>
      <if cond="(linesPlayedCounter &lt; numberLines)">
        <action next="BB1210_PlayLinePlan_PP"/>
        <else>
          <action next="BB1235_CheckArBalance_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="BB1235_CheckArBalance_DS">
      <if cond="(parseFloat(GlobalVars.GetAccountDetails.arBalance) == 0.00)">
        <action next="BR1245_PlayTotalBalance_PP"/>
        <else>
          <action next="BB1240_PlayArBalance_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="BB1240_PlayArBalance_PP">
      <session-mapping key="arBalance" value="GlobalVars.GetAccountDetails.arBalance" type="String"/>
      <session-mapping key="arBalanceCredit" value="(Math.abs(arBalance)).toString()" type="String"/>
      <audio>
        <if cond="(parseFloat(arBalance) &gt; 0)">
          <prompt id="BB1240_out_01">
            <prompt-segments>
              <audiofile src="BB1240_out_01.wav" text="The balance includes a prorated charge of "/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="arBalance">
            <param value="f" name="intonation"/>
            <param value="false" name="playZeroCents"/>
          </prompt>
          <prompt id="silence_500ms">
            <prompt-segments>
              <audiofile text="test" src="silence_500ms.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="BB1240_out_02">
            <prompt-segments>
              <audiofile src="BB1240_out_02.wav" text="That s from changes you ve made last month like upgrading your plan or adding services"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="BB1240_out_03">
              <prompt-segments>
                <audiofile src="BB1240_out_03.wav" text="Your account also has a credit for"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="arBalanceCredit">
              <param value="f" name="intonation"/>
              <param value="false" name="playZeroCents"/>
            </prompt>
          </else>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BR1245_PlayTotalBalance_PP"/>
    </play-state>

    <play-state id="BR1245_PlayTotalBalance_PP">
      <session-mapping key="remainingDays" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails.payDate" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="amountDue" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="arBalance" value="GlobalVars.GetAccountDetails.arBalance" type="String"/>
      <session-mapping key="remainingBalance" value="(parseFloat(amountDue)).toString()" type="String"/>
      <session-mapping key="remainingBalanceCredit" value="(Math.abs(remainingBalance)).toString()" type="String"/>
      <session-mapping key="isSuspended" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" type="String"/>
      <session-mapping key="totalBalance" value="(parseFloat(amountDue)).toString()" type="String"/>
      <session-mapping key="totalBalanceCredit" value="(Math.abs(totalBalance)).toString()" type="String"/>
      <session-mapping key="messageCode" value="GlobalVars.GetAutoPayMessages &amp;&amp; GlobalVars.GetAutoPayMessages.messageCode  ? GlobalVars.GetAutoPayMessages.messageCode:''" type="String"/>
      <session-mapping key="isAutopayEnrolled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="isAutopayEligPlanExists" value="GlobalVars.GetAccountDetails.isAutopayEligPlanExists" type="String"/>
      <session-mapping key="calculatedAmountAfterDiscount" value="" type="String"/>
      <session-mapping key="discountAmount" value="(GlobalVars.GetAutoPayMessages  &amp;&amp; GlobalVars.GetAutoPayMessages.discountAmount) ? GlobalVars.GetAutoPayMessages.discountAmount: 0.0" type="String"/>
      <session-mapping key="amountAfterDiscount" value="(GlobalVars.GetAutoPayMessages &amp;&amp; GlobalVars.GetAutoPayMessages.amountAfterDiscount) ? GlobalVars.GetAutoPayMessages.amountAfterDiscount: 0.0" type="String"/>
      <session-mapping key="hasValidMessageCode" value="(messageCode != '' &amp;&amp;  (messageCode == '9001' || messageCode == '9002' || messageCode == '9003')) ? true: false" type="String"/>
      <if cond="(GlobalVars.GetAutoPayMessages ) &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
        <session-mapping key="calculatedAmountAfterDiscount" expr="parseFloat(amountDue) - parseFloat(discountAmount)"/>
      </if>
      <audio>
        <if cond="(parseFloat(dueImmediatelyAmount) &gt; 3)">
          <prompt id="BR1245_out_11" cond="isSuspended == true">
            <prompt-segments>
              <audiofile src="BR1245_out_11.wav" text="The amount due NOW to reactivate your account is "/>
            </prompt-segments>
          </prompt>
          <prompt id="BR1245_out_12" cond="isSuspended == false">
            <prompt-segments>
              <audiofile src="BR1245_out_12.wav" text="To avoid service disruption, the amount you would need to pay immediately is "/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="dueImmediatelyAmount">
            <param value="false" name="playZeroCents"/>
            <param value="m" name="intonation"/>
          </prompt>
          <prompt id="BR1245_out_14" cond="(parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)">
            <prompt-segments>
              <audiofile src="BR1245_out_14.wav" text="Then you have a remaining balance of "/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="remainingBalanceCredit" cond="(parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)">
            <param value="false" name="playZeroCents"/>
            <param value="m" name="intonation"/>
          </prompt>
          <prompt id="BR1245_out_02" cond="((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;amp; remainingDays == 0">
            <prompt-segments>
              <audiofile src="BR1245_out_02.wav" text="It s due today"/>
            </prompt-segments>
          </prompt>
          <prompt id="BR1245_out_03" cond="((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;amp; remainingDays == 1">
            <prompt-segments>
              <audiofile src="BR1245_out_03.wav" text="It s due tomorrow"/>
            </prompt-segments>
          </prompt>
          <prompt id="BR1245_out_04" cond="((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;amp; remainingDays == 2">
            <prompt-segments>
              <audiofile src="BR1245_out_04.wav" text="It s due the day after tomorrow"/>
            </prompt-segments>
          </prompt>
          <prompt id="BR1245_out_05" cond="((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;amp; (remainingDays != 0 &amp;&amp; remainingDays != 1 &amp;&amp; remainingDays != 2)">
            <prompt-segments>
              <audiofile src="BR1245_out_05.wav" text="It s due"/>
            </prompt-segments>
          </prompt>
          <prompt type="date" expr="payDate" cond="((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;amp; (remainingDays != 0 &amp;&amp; remainingDays != 1 &amp;&amp; remainingDays != 2)">
            <param name="dateFormat" value="MMM dd, yyyy"/>
            <param name="playDayOfMonth" value="true"/>
            <param name="playYear" value="false"/>
            <param name="playDayOfTheWeek" value="false"/>
            <param name="intonation" value="f"/>
          </prompt>
          <else>
            <prompt id="BR1245_out_06" cond="(((parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0)) &amp;&amp; (isAutopayEnrolled  == true &amp;&amp; isAutopayEligPlanExists == true &amp;&amp; hasValidMessageCode == true))">
              <prompt-segments>
                <audiofile src="BR1245_out_06.wav" text="You also have charges of"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="amountAfterDiscount" cond="(((parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0)) &amp;&amp; (isAutopayEnrolled  == true &amp;&amp; isAutopayEligPlanExists == true &amp;&amp; hasValidMessageCode == true) &amp;&amp; (messageCode == '9001'))">
              <param value="false" name="playZeroCents"/>
              <param value="f" name="intonation"/>
            </prompt>
            <prompt type="currency" expr="calculatedAmountAfterDiscount" cond="(((parseFloat(amountDue) &gt; 0 || parseFloat(arBalance) &gt; 0)) &amp;&amp; (isAutopayEnrolled  == true &amp;&amp; isAutopayEligPlanExists == true &amp;&amp; hasValidMessageCode == true) &amp;&amp; (messageCode == '9002' || messageCode == '9003'))">
              <param value="false" name="playZeroCents"/>
              <param value="f" name="intonation"/>
            </prompt>
            <prompt id="BR1245_out_01" cond="(((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;&amp; !(isAutopayEnrolled  == true &amp;&amp; isAutopayEligPlanExists == true &amp;&amp; hasValidMessageCode == true))">
              <prompt-segments>
                <audiofile src="BR1245_out_01.wav" text="So again the total balance is"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="totalBalanceCredit" cond="(((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;&amp; !(isAutopayEnrolled  == true &amp;&amp; isAutopayEligPlanExists == true &amp;&amp; hasValidMessageCode == true))">
              <param value="false" name="playZeroCents"/>
              <param value="m" name="intonation"/>
            </prompt>
            <prompt id="BR1245_out_02" cond="((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;amp; remainingDays == 0">
              <prompt-segments>
                <audiofile src="BR1245_out_02.wav" text="It s due today"/>
              </prompt-segments>
            </prompt>
            <prompt id="BR1245_out_03" cond="((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;amp; remainingDays == 1">
              <prompt-segments>
                <audiofile src="BR1245_out_03.wav" text="It s due tomorrow"/>
              </prompt-segments>
            </prompt>
            <prompt id="BR1245_out_04" cond="((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;amp; remainingDays == 2">
              <prompt-segments>
                <audiofile src="BR1245_out_04.wav" text="It s due the day after tomorrow"/>
              </prompt-segments>
            </prompt>
            <prompt id="BR1245_out_05" cond="((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;amp; (remainingDays != 0 &amp;&amp; remainingDays != 1 &amp;&amp; remainingDays != 2)">
              <prompt-segments>
                <audiofile src="BR1245_out_05.wav" text="It s due"/>
              </prompt-segments>
            </prompt>
            <prompt type="date" expr="payDate" cond="((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)) &amp;amp; (remainingDays != 0 &amp;&amp; remainingDays != 1 &amp;&amp; remainingDays != 2)">
              <param name="dateFormat" value="MMM dd, yyyy"/>
              <param name="playDayOfMonth" value="true"/>
              <param name="playYear" value="false"/>
              <param name="playDayOfTheWeek" value="false"/>
              <param name="intonation" value="f"/>
            </prompt>
            <prompt id="BR1245_out_10" cond="!(((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)))">
              <prompt-segments>
                <audiofile src="BR1245_out_10.wav" text="You re all paid up for now, and your next payment is due"/>
              </prompt-segments>
            </prompt>
            <prompt type="date" expr="payDate" cond="!(((parseFloat(amountDue) &gt; 0) || (parseFloat(arBalance) &gt; 0)))">
              <param name="dateFormat" value="MMM dd, yyyy"/>
              <param name="playDayOfMonth" value="true"/>
              <param name="playYear" value="false"/>
              <param name="playDayOfTheWeek" value="false"/>
              <param name="intonation" value="f"/>
            </prompt>
          </else>
        </if>
        <prompt id="BR1245_out_15">
          <prompt-segments>
            <audiofile src="BR1245_out_15.wav" text="You can also get this information in the myMetro app"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BB1305_WrapMenu_DM"/>
    </play-state>

    <dm-state id="BB1305_WrapMenu_DM" type="CUST">
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <success>
        <action label="repeat">
          <action next="BB1310_CheckOrigin_JDA"/>
        </action>
        <action label="make-payment">
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="BB1315_MakePayment_SD"/>
        </action>
        <action label="main_menu">
          <session-mapping key="GlobalVars.callType" expr="'mainmenu'"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="more-options">
          <action next="BB1320_CheckOrigin_JDA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BB1305_ini_01" cond="tag == 'payment-help' ||  tag == 'vague-billing'">
                <prompt-segments>
                  <audiofile src="BB1305_ini_01.wav" text="Say  repeat ,  pay now ,  main menu , or  back to payment options  If you re done, you can hang up"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1305_ini_02" cond="!(tag == 'payment-help' ||  tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="BB1305_ini_02.wav" text="Say  repeat ,  pay now ,  main menu , or  payment options  If you re done, you can hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="BB1305_WrapMenu_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BB1305_ini_01" cond="tag == 'payment-help' ||  tag == 'vague-billing'">
                <prompt-segments>
                  <audiofile src="BB1305_ini_01.wav" text="Say  repeat ,  pay now ,  main menu , or  back to payment options  If you re done, you can hang up"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1305_ini_02" cond="!(tag == 'payment-help' ||  tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="BB1305_ini_02.wav" text="Say  repeat ,  pay now ,  main menu , or  payment options  If you re done, you can hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BB1305_ini_01" cond="tag == 'payment-help' ||  tag == 'vague-billing'">
                <prompt-segments>
                  <audiofile src="BB1305_ini_01.wav" text="Say  repeat ,  pay now ,  main menu , or  back to payment options  If you re done, you can hang up"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1305_ini_02" cond="!(tag == 'payment-help' ||  tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="BB1305_ini_02.wav" text="Say  repeat ,  pay now ,  main menu , or  payment options  If you re done, you can hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1305_nm2_01">
                <prompt-segments>
                  <audiofile src="BB1305_nm2_01.wav" text="Say  repeat  or press 1,  pay now  or press 2,  main menu  - 3, or  payment options  - 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1305_nm2_01">
                <prompt-segments>
                  <audiofile src="BB1305_nm2_01.wav" text="Say  repeat  or press 1,  pay now  or press 2,  main menu  - 3, or  payment options  - 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1305_ini_01" cond="tag == 'payment-help' ||  tag == 'vague-billing'">
                <prompt-segments>
                  <audiofile src="BB1305_ini_01.wav" text="Say  repeat ,  pay now ,  main menu , or  back to payment options  If you re done, you can hang up"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1305_ini_02" cond="!(tag == 'payment-help' ||  tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="BB1305_ini_02.wav" text="Say  repeat ,  pay now ,  main menu , or  payment options  If you re done, you can hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1305_nm2_01">
                <prompt-segments>
                  <audiofile src="BB1305_nm2_01.wav" text="Say  repeat  or press 1,  pay now  or press 2,  main menu  - 3, or  payment options  - 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1305_nm2_01">
                <prompt-segments>
                  <audiofile src="BB1305_nm2_01.wav" text="Say  repeat  or press 1,  pay now  or press 2,  main menu  - 3, or  payment options  - 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BB1305_ini_01" cond="tag == 'payment-help' ||  tag == 'vague-billing'">
                <prompt-segments>
                  <audiofile src="BB1305_ini_01.wav" text="Say  repeat ,  pay now ,  main menu , or  back to payment options  If you re done, you can hang up"/>
                </prompt-segments>
              </prompt>
              <prompt id="BB1305_ini_02" cond="!(tag == 'payment-help' ||  tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="BB1305_ini_02.wav" text="Say  repeat ,  pay now ,  main menu , or  payment options  If you re done, you can hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="BB1305_WrapMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="BB1305_WrapMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="BB1305_WrapMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BB1305_WrapMenu_DM_cnf_nomatch_1"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="BB1305_WrapMenu_DM_cnf_nomatch_1"/>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="BB1310_CheckOrigin_DS">
      <session-mapping key="isOnFamilyPlan" value="GlobalVars.GetAccountDetails.isOnFamilyPlan" type="String"/>
      <if cond="isOnFamilyPlan == true">
        <action next="BB1235_CheckArBalance_JDA"/>
        <else>
          <action next="BB1120_CheckArBalance_JDA"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="BB1315_MakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="BB1315_MakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BB1315_MakePayment_SD_return_CS">
      <if cond="GlobalVars.tag == 'request-extension'">
        <action next="BB1316_ApplyExtension_SD"/>
        <else>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="BB1316_ApplyExtension_SD">
      <gotodialog next="ApplyExtension_Main_Dialog"/>
      <action next="BB1316_ApplyExtension_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BB1316_ApplyExtension_SD_return_CS">
      <session-mapping key="GlobalVars.callType" expr="undefined"/>
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="BB1320_CheckOrigin_DS">
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <if cond="tag == 'payment-help' || tag == 'vague-billing'">
        <action next="getReturnLink()"/>
        <else>
          <action next="BR1325_PaymentHelp_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="BR1325_PaymentHelp_SD">
      <gotodialog next="Payment_Help_Dialog"/>
      <action next="BR1325_PaymentHelp_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BR1325_PaymentHelp_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  