<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="RatePlan_Main_Dialog">
    <decision-state id="RP0001_CheckContext_DS">
      <session-mapping key="supportedPlanIndicator" value="GlobalVars.GetAccountDetails.isSupported" type="String"/>
      <session-mapping key="GlobalVars.futureChangeOffer" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.declineChangeCounter" expr="0"/>
      <if cond="(GlobalVars.GetAccountDetails.accountStatus == 'suspended')">
        <session-mapping key="GlobalVars.suspendedRatePlanChange" value="true" type="Boolean"/>
      </if>
      <if cond="(supportedPlanIndicator == 'true' || supportedPlanIndicator == true)">
        <action next="RP0005_CheckCallerIntent_JDA"/>
        <else>
          <action next="RP0030_PlayTransferMessage_PP"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="RP0005_CheckCallerIntent_DS">
      <if cond="(GlobalVars.callType == 'change_plan')">
        <action next="RP0010_BroadcastMessages_SD"/>
        <else>
          <action next="RP0104_CheckNeedFutureTransactions_JDA"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="RP0010_BroadcastMessages_SD">
      <session-mapping key="GlobalVars.broadcastMessageKey" expr="'RP'"/>
      <gotodialog next="BroadcastMessages_Dialog"/>
      <action next="RP0010_BroadcastMessages_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="RP0010_BroadcastMessages_SD_return_CS">
      <action next="RP0020_CheckIfFutureDatedRequest_JDA"/>
    </custom-state>

    <decision-state id="RP0020_CheckIfFutureDatedRequest_DS">
      <if cond="(GlobalVars.accountFutureRequestInd == true)">
        <action next="RP0030_PlayTransferMessage_PP"/>
        <else>
          <if cond="(GlobalVars.GetAccountDetails.isOnFamilyPlan == true)">
            <action next="RP0024_AskAddRemoveLines_DM"/>
            <else>
              <action next="RP0025_CheckNeedPlayCurrentPlan_JDA"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <decision-state id="RP0025_CheckNeedPlayCurrentPlan_DS">
      <session-mapping key="GlobalVars.oldPlanPrice" expr="GlobalVars.GetAccountDetails.ratePlanPrice"/>
      <if cond="(GlobalVars.heardCurrentPlan == true)">
        <action next="RP0205_GetAvailableRatePlanOffers_DB_DA"/>
        <else>
          <action next="RP0104_CheckNeedFutureTransactions_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="RP0030_PlayTransferMessage_PP">
      <audio>
        <prompt id="RP0030_out_02">
          <prompt-segments>
            <audiofile text="Because you have an upcoming change on your account I'll need to transfer you to an agent for help with your plan " src="RP0030_out_02.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="RP1280_GoToCallTransfer_SD"/>
    </play-state>

    <subdialog-state id="RP0040_GoToDeviceHandling_SD">
      <gotodialog next="DeviceHandling_Main_Dialog"/>
      <action next="RP0040_GoToDeviceHandling_SD_return"/>
    </subdialog-state>
    <decision-state id="RP0104_CheckNeedFutureTransactions_DS">
      <if cond="(GlobalVars.accountFutureRequestInd == true || GlobalVars.subscriberFuturePricePlanInd == true)">
        <action next="RP0105_GetFutureTransactionDetails_DB_DA"/>
        <else>
          <action next="RP0120_PlayCurrentPlan_PP"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="RP0105_GetFutureTransactionDetails_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="sessionId" value="GlobalVars.sessionId" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <data-access id="GetFutureTransactionDetails" classname="com.nuance.metro.dataaccess.GetFutureTransactionDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
          <input-variable name="languageCode"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="mdn"/>
          <output-variable name="futurePlan"/>
          <output-variable name="futureAddOns"/>
          <output-variable name="futureRequestDate"/>
          <output-variable name="planFinalPromptURL"/>
          <output-variable name="planFinalPromptTTS"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="(GetFutureTransactionDetails &amp;&amp; GetFutureTransactionDetails.status == 'Success')">
          <session-mapping key="GlobalVars.GetFutureTransactionDetails" expr="GetFutureTransactionDetails"/>
          <session-mapping key="GlobalVars.futurePlan" expr="GetFutureTransactionDetails.futurePlan"/>
          <session-mapping key="GlobalVars.futureAddOns" expr="GetFutureTransactionDetails.futureAddOns"/>
          <session-mapping key="GlobalVars.futureRequestDate" expr="GetFutureTransactionDetails.futureRequestDate"/>
          <session-mapping key="GlobalVars.futurePlanPromptURL" expr="GetFutureTransactionDetails.futurePlanPromptURL"/>
          <session-mapping key="GlobalVars.futurePlanPromptTTS" expr="GetFutureTransactionDetails.futurePlanPromptTTS"/>
          <action next="RP0110_HaveFutureDatedChanges_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="RP0110_HaveFutureDatedChanges_DS">
      <if cond="((GlobalVars.futurePlan == undefined) || (GlobalVars.futurePlan == ''))">
        <action next="RP0120_PlayCurrentPlan_PP"/>
        <else>
          <action next="RP0115_PlayFutureDatedChanges_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="RP0115_PlayFutureDatedChanges_PP">
      <session-mapping key="futureRequestDate" value="GlobalVars.futureRequestDate" type="String"/>
      <session-mapping key="futurePlan" value="GlobalVars.futurePlan" type="String"/>
      <session-mapping key="futurePlanPromptURL" value="GlobalVars.futurePlanPromptURL" type="String"/>
      <session-mapping key="futurePlanPromptTTS" value="GlobalVars.futurePlanPromptTTS" type="String"/>
      <audio>
        <prompt id="RP0115_out_01">
          <prompt-segments>
            <audiofile text="I see you plan is schduled to change on" src="RP0115_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="date" expr="futureRequestDate">
          <param name="dateFormat" value="yyyyMMdd"/>
          <param name="playDayOfMonth" value="true"/>
          <param name="playYear" value="false"/>
          <param name="playDayOfTheWeek" value="false"/>
          <param name="intonation" value="f"/>
        </prompt>
        <prompt id="RP0115_out_02" cond="((futurePlanPromptURL != undefined) &amp;&amp; (futurePlanPromptURL != null) &amp;&amp; (futurePlanPromptURL != ''))         || ((futurePlanPromptTTS != undefined) &amp;&amp; (futurePlanPromptTTS != null) &amp;&amp; (futurePlanPromptTTS != ''))">
          <prompt-segments>
            <audiofile text="You'll be changing to" src="RP0115_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" cond="((futurePlanPromptURL != undefined) &amp;&amp; (futurePlanPromptURL != null) &amp;&amp; (futurePlanPromptURL != ''))      || ((futurePlanPromptTTS != undefined) &amp;&amp; (futurePlanPromptTTS != null) &amp;&amp; (futurePlanPromptTTS != ''))">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayPlanFinalPromptURL"/>
          <param name="futurePlanPromptURL" value="futurePlanPromptURL" scope="request"/>
          <param name="futurePlanPromptTTS" value="futurePlanPromptTTS" scope="request"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="RP0115_out_03">
          <prompt-segments>
            <audiofile text="Until then" src="RP0115_out_03.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="RP0120_PlayCurrentPlan_PP"/>
    </play-state>

    <play-state id="RP0120_PlayCurrentPlan_PP">
      <session-mapping key="soc" value="GlobalVars.GetAccountDetails.ratePlan" type="String"/>
      <session-mapping key="isPlanDetails" value="GlobalVars.callType == 'plan_details'" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="ratePlanAction" value="GlobalVars.ratePlanAction" type="String"/>
      <session-mapping key="currentPlanPromptURL" value="GlobalVars.GetAccountDetails.currentPlanPromptURL" type="String"/>
      <session-mapping key="currentPlanPromptTTS" value="GlobalVars.GetAccountDetails.currentPlanPromptTTS" type="String"/>
      <session-mapping key="includedFeatures" value="GlobalVars.GetAccountDetails.includedFeatures" type="String"/>
      <session-mapping key="isOnFamilyPlan" value="GlobalVars.GetAccountDetails.isOnFamilyPlan" type="String"/>
      <audio>
        <if cond="tag == 'home-internet' || callType == 'home_internet'">
          <prompt type="custom" expr="soc">
            <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayCurrentRatePlan"/>
            <param name="currentPlanPromptURL" value="currentPlanPromptURL" scope="request"/>
            <param name="currentPlanPromptTTS" value="currentPlanPromptTTS" scope="request"/>
          </prompt>
          <else>
            <prompt type="custom" expr="soc" cond="ratePlanAction != 'tell_me_more'">
              <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayCurrentRatePlan"/>
              <param name="currentPlanPromptURL" value="currentPlanPromptURL" scope="request"/>
              <param name="currentPlanPromptTTS" value="currentPlanPromptTTS" scope="request"/>
            </prompt>
            <prompt id="RP0120_out_06" cond="isPlanDetails == true &amp;&amp; ratePlanAction != 'tell_me_more'">
              <prompt-segments>
                <audiofile text="That includes all taxes and regulatory fees" src="RP0120_out_06.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="RP0120_out_08" cond="ratePlanAction == 'tell_me_more'">
              <prompt-segments>
                <audiofile text="You ve got the following features" src="RP0120_out_08.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="custom" cond="ratePlanAction == 'tell_me_more'">
              <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayCurrentRatePlanFeatures"/>
            </prompt>
            <prompt id="silence_500ms" cond="ratePlanAction != 'tell_me_more' &amp;&amp; callType == 'change_plan'">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="RP0120_out_11" cond="ratePlanAction != 'tell_me_more' &amp;&amp; callType == 'change_plan'">
              <prompt-segments>
                <audiofile text="Just so you know, if you change your plan, it's possible you won't be able to get your current plan back" src="RP0120_out_11.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_1000ms" cond="ratePlanAction != 'tell_me_more' &amp;&amp; callType == 'change_plan'">
              <prompt-segments>
                <audiofile text="test" src="silence_1000ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="RP0120_out_07" cond="ratePlanAction != 'tell_me_more' &amp;&amp; callType == 'change_plan' &amp;&amp; isOnFamilyPlan == true">
              <prompt-segments>
                <audiofile text="Other lines on your account may be impacted as well  The plan choices provided will not include multiline pricing BUT, I will be able to calculate your new total monthly price, including *ALL* your lines before you confirm the change" src="RP0120_out_07.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_1000ms" cond="ratePlanAction != 'tell_me_more' &amp;&amp; callType == 'change_plan' &amp;&amp; isOnFamilyPlan == true">
              <prompt-segments>
                <audiofile text="test" src="silence_1000ms.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <session-mapping key="GlobalVars.heardCurrentPlan" value="true" type="Boolean"/>
      <if cond="GlobalVars.tag == 'home-internet_active'">
        <action next="RP0140_InternetRatePlanWrapMenu_DM"/>
        <else>
          <action next="RP0125_CheckCallerIntent_JDA"/>
        </else>
      </if>
    </play-state>

    <decision-state id="RP0125_CheckCallerIntent_DS">
      <if cond="(GlobalVars.callType == 'change_plan')">
        <action next="RP0205_GetAvailableRatePlanOffers_DB_DA"/>
        <else>
          <action next="RP0130_CurrentPlanOptions_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="RP0130_CurrentPlanOptions_DM" type="CUST">
      <session-mapping key="ratePlanActionUndefinedOrRepeat" value="GlobalVars.ratePlanAction == undefined || GlobalVars.ratePlanAction == 'repeat'" type="String"/>
      <session-mapping key="isOnFamilyPlan" value="GlobalVars.GetAccountDetails.isOnFamilyPlan" type="String"/>
      <success>
        <action label="change-plan" next="RP0010_BroadcastMessages_SD">
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <audio>
            <prompt id="RP0130_out_01">
              <prompt-segments>
                <audiofile text="Just so you know, if you change your plan, it's possible you won't be able to get your current plan back" src="RP0130_out_01.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_1000ms">
              <prompt-segments>
                <audiofile text="test" src="silence_1000ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="(GlobalVars.GetAccountDetails.isOnFamilyPlan == true)">
            <audio>
              <prompt id="RP0130_out_02">
                <prompt-segments>
                  <audiofile text="Other lines on your account may be impacted as well  The plan choices provided will not include multiline pricing BUT, I will be able to calculate your new total monthly price, including *ALL* your lines before you confirm the change" src="RP0130_out_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <session-mapping key="GlobalVars.ratePlanAction" expr="undefined"/>
          </if>
        </action>
        <action label="tell_me_more" next="RP0120_PlayCurrentPlan_PP">
          <session-mapping key="GlobalVars.ratePlanAction" expr="'tell_me_more'"/>
        </action>
        <action label="my_features" next="RP0135_CurrentFeatures_SD">
          <session-mapping key="GlobalVars.ratePlanAction" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.callType" expr="'my_features'"/>
        </action>
        <action label="operator"/>
        <action label="repeat" next="RP0110_HaveFutureDatedChanges_JDA">
          <if cond="GlobalVars.ratePlanAction == undefined || GlobalVars.ratePlanAction == ''">
            <session-mapping key="GlobalVars.ratePlanAction" expr="'repeat'"/>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP0130_ini_01" cond="ratePlanActionUndefinedOrRepeat">
                <prompt-segments>
                  <audiofile text="You can say repeat that or tell me more You can also say change my plan or my features" src="RP0130_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0130_ini_02" cond="! ratePlanActionUndefinedOrRepeat">
                <prompt-segments>
                  <audiofile text="You can say repeat that  You can also say change my plan or my features" src="RP0130_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP0130_nm1_01">
                <prompt-segments>
                  <audiofile text="If you d like to hear that information again, say repeat that  To hear more information about your plan, say tell me more  You can also say change my plan  If you d like to hear your current features, say my features" src="RP0130_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0130_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear about your calling plan again, say repeat that, or press *  To hear more details about it, say tell me more or press 1  You can also say change my plan or press 2 Or my features or press 3" src="RP0130_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0130_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear about your calling plan again, say repeat that, or press *  To hear more details about it, say tell me more or press 1  You can also say change my plan or press 2 Or my features or press 3" src="RP0130_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0130_nm1_01">
                <prompt-segments>
                  <audiofile text="If you d like to hear that information again, say repeat that  To hear more information about your plan, say tell me more  You can also say change my plan  If you d like to hear your current features, say my features" src="RP0130_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0130_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear about your calling plan again, say repeat that, or press *  To hear more details about it, say tell me more or press 1  You can also say change my plan or press 2 Or my features or press 3" src="RP0130_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0130_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear about your calling plan again, say repeat that, or press *  To hear more details about it, say tell me more or press 1  You can also say change my plan or press 2 Or my features or press 3" src="RP0130_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP0130_ini_01" cond="ratePlanActionUndefinedOrRepeat">
                <prompt-segments>
                  <audiofile text="You can say repeat that or tell me more You can also say change my plan or my features" src="RP0130_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0130_ini_02" cond="! ratePlanActionUndefinedOrRepeat">
                <prompt-segments>
                  <audiofile text="You can say repeat that  You can also say change my plan or my features" src="RP0130_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP0130_CurrentPlanOptions_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP0130_CurrentPlanOptions_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="RP0135_CurrentFeatures_SD">
      <gotodialog next="CurrentFeatures_Main_Dialog"/>
      <action next="RP0135_CurrentFeatures_SD_return_CS"/>
    </subdialog-state>
    <dm-state id="RP0140_InternetRatePlanWrapMenu_DM" type="CUST">
      <success>
        <action label="main_menu">
          <session-mapping key="GlobalVars.tag" expr="undefined"/>
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="hear-data_usage">
          <session-mapping key="GlobalVars.callType" expr="'data_usage'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <action next="RP0155_GoToDataUsage_SD"/>
        </action>
        <action label="operator"/>
        <action label="repeat" next="RP0120_PlayCurrentPlan_PP"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP0140_ini_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'usage information', or 'main menu' " src="RP0140_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="RP0140_InternetRatePlanWrapMenu_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP0140_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say  'repeat that', or press 1  'usage information, or press 2  'main menu' or press 3 " src="RP0140_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0140_nm2_01">
                <prompt-segments>
                  <audiofile text="Say  'repeat that', or press 1  'usage information, or press 2  'main menu' or press 3" src="RP0140_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0140_nm2_01">
                <prompt-segments>
                  <audiofile text="Say  'repeat that', or press 1  'usage information, or press 2  'main menu' or press 3" src="RP0140_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0140_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say  'repeat that', or press 1  'usage information, or press 2  'main menu' or press 3 " src="RP0140_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0140_nm2_01">
                <prompt-segments>
                  <audiofile text="Say  'repeat that', or press 1  'usage information, or press 2  'main menu' or press 3" src="RP0140_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0140_nm2_01">
                <prompt-segments>
                  <audiofile text="Say  'repeat that', or press 1  'usage information, or press 2  'main menu' or press 3" src="RP0140_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP0140_ini_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'usage information', or 'main menu' " src="RP0140_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="RP0140_InternetRatePlanWrapMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP0140_InternetRatePlanWrapMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.510" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="RP0140_InternetRatePlanWrapMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <custom-state id="RP0135_CurrentFeatures_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <data-access-state id="RP0205_GetAvailableRatePlanOffers_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="sessionId" value="GlobalVars.sessionId" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="currentRatePlan" value="GlobalVars.GetAccountDetails.ratePlan" type="String"/>
      <session-mapping key="isSwitchRatePlan" value="true" type="Boolean"/>
      <session-mapping key="futureChangeOffer" value="GlobalVars.futureChangeOffer" type="String"/>
      <data-access id="GetAvailableRatePlanOffers" classname="com.nuance.metro.dataaccess.GetAvailableRatePlanOffers">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="imei"/>
          <input-variable name="sessionId"/>
          <input-variable name="languageCode"/>
          <input-variable name="cheaperOnly"/>
          <input-variable name="JWTToken"/>
          <input-variable name="futureChangeOffer"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="ratePlans"/>
          <output-variable name="futureDate"/>
          <output-variable name="restrictionType"/>
          <output-variable name="existingPlanPricePrimaryRate"/>
          <output-variable name="RatePlanGrammarURL"/>
          <output-variable name="RatePlanDTMFGrammarURL"/>
          <output-variable name="RatePlanUnambiguousGrammarURL"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="(GetAvailableRatePlanOffers &amp;&amp; GetAvailableRatePlanOffers.status == 'Success')">
          <session-mapping key="GlobalVars.ratePlans" expr="GetAvailableRatePlanOffers.ratePlans"/>
          <session-mapping key="GlobalVars.GetAvailableRatePlans" expr="GetAvailableRatePlanOffers"/>
          <session-mapping key="GlobalVars.futureDate" expr="GetAvailableRatePlanOffers.futureDate"/>
          <session-mapping key="GlobalVars.restrictionType" expr="GetAvailableRatePlanOffers.restrictionType"/>
          <session-mapping key="GlobalVars.RatePlanGrammarURL" expr="GetAvailableRatePlanOffers.RatePlanGrammarURL"/>
          <session-mapping key="GlobalVars.RatePlanDTMFGrammarURL" expr="GetAvailableRatePlanOffers.RatePlanDTMFGrammarURL"/>
          <session-mapping key="GlobalVars.RatePlanUnambiguousGrammarURL" expr="GetAvailableRatePlanOffers.RatePlanUnambiguousGrammarURL"/>
          <action next="RP0206_CalculatePlanOptions_DB_DA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <data-access-state id="RP0206_CalculatePlanOptions_DB_DA">
      <session-mapping key="restrictionType" value="GlobalVars.restrictionType" type="String"/>
      <session-mapping key="ratePlanPrice" value="GlobalVars.GetAccountDetails.ratePlanPrice" type="String"/>
      <data-access id="CalculatePlanOptions_MW" classname="com.nuance.metro.dataaccess.CalculatePlanOptions_MW">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="restrictionType"/>
          <input-variable name="ratePlanPrice"/>
        </inputs>
        <outputs>
          <output-variable name="ratePlans"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="(CalculatePlanOptions_MW &amp;&amp; CalculatePlanOptions_MW.status == 'Success')">
          <session-mapping key="GlobalVars.ratePlans" expr="CalculatePlanOptions_MW.ratePlans"/>
          <action next="RP0210_CheckAccountRestrictions_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="RP0210_CheckAccountRestrictions_DS">
      <if cond="(GlobalVars.restrictionType == 'NoChangeAllowed')">
        <action next="RP0230_MetricsNoChangesAllowed_JDA"/>
        <elseif cond="(GlobalVars.restrictionType == 'AllowImmediateOnly')">
          <action next="RP0225_MetricsImmediateOnly_JDA"/>
        </elseif>
        <elseif cond="(GlobalVars.restrictionType == 'AllowFutureOnly')">
          <action next="RP0220_MetricsFutureOnly_JDA"/>
        </elseif>
        <else>
          <action next="RP0215_MetricsNoRestrictions_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="RP0215_MetricsNoRestrictions_DS">
      <action next="RP0415_CheckAccountStatus_JDA"/>
    </decision-state>

    <decision-state id="RP0220_MetricsFutureOnly_DS">
      <action next="RP0415_CheckAccountStatus_JDA"/>
    </decision-state>

    <decision-state id="RP0225_MetricsImmediateOnly_DS">
      <action next="RP0415_CheckAccountStatus_JDA"/>
    </decision-state>

    <decision-state id="RP0230_MetricsNoChangesAllowed_DS">
      <action next="RP0605_PayFirstYN_DM"/>
    </decision-state>

    <play-state id="RP0304_PlayFutureDatedTransition_PP">
      <audio>
        <prompt id="RP0304_out_01">
          <prompt-segments>
            <audiofile text="Now, before we continue " src="RP0304_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="RP0305_OfferFutureDatedPlan_DM"/>
    </play-state>

    <dm-state id="RP0305_OfferFutureDatedPlan_DM" type="CUST">
      <session-mapping key="futureDate" value="GlobalVars.futureDate" type="String"/>
      <session-mapping key="newPlanPrice" value="GlobalVars.selectedPlanPrice" type="String"/>
      <session-mapping key="currentPlanPrice" value="GlobalVars.GetAccountDetails.ratePlanPrice" type="String"/>
      <success>
        <action label="immediate" next="RP0310_MetricsChoseImmediatePlan_JDA">
          <session-mapping key="GlobalVars.futureDate" expr="undefined"/>
        </action>
        <action label="futuredate" next="RP0315_MetricsChoseFutureDatedPlan_JDA">
          <session-mapping key="GlobalVars.futureChangeOffer" value="true" type="Boolean"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP0305_ini_01">
                <prompt-segments>
                  <audiofile text="I can change your plan right now, or I can schedule it to change starting with your next due date That's" src="RP0305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="parseFloat(newPlanPrice) &gt; parseFloat(currentPlanPrice)">
                <prompt id="RP0305_ini_03">
                  <prompt-segments>
                    <audiofile text="If you want to change it on your next due date, I'll schedule the change and you'll see your new plan price in your next statement So, when would you like it to change? Say 'right now' or 'next due date'" src="RP0305_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="RP0305_ini_02">
                <prompt-segments>
                  <audiofile text="If you want to change it now, there will be an extra charge on your next statement, to cover the rest of this month" src="RP0305_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="RP0305_ini_01">
                <prompt-segments>
                  <audiofile text="I can change your plan right now, or I can schedule it to change starting with your next due date That's" src="RP0305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="parseFloat(newPlanPrice) &gt; parseFloat(currentPlanPrice)">
                <prompt id="RP0305_ini_03">
                  <prompt-segments>
                    <audiofile text="If you want to change it on your next due date, I'll schedule the change and you'll see your new plan price in your next statement So, when would you like it to change? Say 'right now' or 'next due date'" src="RP0305_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="RP0305_ini_02">
                <prompt-segments>
                  <audiofile text="If you want to change it now, there will be an extra charge on your next statement, to cover the rest of this month" src="RP0305_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP0305_ini_01">
                <prompt-segments>
                  <audiofile text="I can change your plan right now, or I can schedule it to change starting with your next due date That's" src="RP0305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="parseFloat(newPlanPrice) &gt; parseFloat(currentPlanPrice)">
                <prompt id="RP0305_ini_03">
                  <prompt-segments>
                    <audiofile text="If you want to change it on your next due date, I'll schedule the change and you'll see your new plan price in your next statement So, when would you like it to change? Say 'right now' or 'next due date'" src="RP0305_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP0305_ini_02">
                    <prompt-segments>
                      <audiofile text="If you want to change it now, there will be an extra charge on your next statement, to cover the rest of this month" src="RP0305_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0305_nm2_01">
                <prompt-segments>
                  <audiofile text="I can change your plan right now, or I can schedule the plan you pick today to start with your next due date That'll be" src="RP0305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0305_nm2_02">
                <prompt-segments>
                  <audiofile text="So when would you like your plan to change? Say 'right now' or press 1, or 'next due date' or press 2" src="RP0305_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0305_nm2_01">
                <prompt-segments>
                  <audiofile text="I can change your plan right now, or I can schedule the plan you pick today to start with your next due date That'll be" src="RP0305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0305_nm2_02">
                <prompt-segments>
                  <audiofile text="So when would you like your plan to change? Say 'right now' or press 1, or 'next due date' or press 2" src="RP0305_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0305_ini_01">
                <prompt-segments>
                  <audiofile text="I can change your plan right now, or I can schedule it to change starting with your next due date That's" src="RP0305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="parseFloat(newPlanPrice) &gt; parseFloat(currentPlanPrice)">
                <prompt id="RP0305_ini_03">
                  <prompt-segments>
                    <audiofile text="If you want to change it on your next due date, I'll schedule the change and you'll see your new plan price in your next statement So, when would you like it to change? Say 'right now' or 'next due date'" src="RP0305_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP0305_ini_02">
                    <prompt-segments>
                      <audiofile text="If you want to change it now, there will be an extra charge on your next statement, to cover the rest of this month" src="RP0305_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0305_nm2_01">
                <prompt-segments>
                  <audiofile text="I can change your plan right now, or I can schedule the plan you pick today to start with your next due date That'll be" src="RP0305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0305_nm2_02">
                <prompt-segments>
                  <audiofile text="So when would you like your plan to change? Say 'right now' or press 1, or 'next due date' or press 2" src="RP0305_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0305_nm2_01">
                <prompt-segments>
                  <audiofile text="I can change your plan right now, or I can schedule the plan you pick today to start with your next due date That'll be" src="RP0305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0305_nm2_02">
                <prompt-segments>
                  <audiofile text="So when would you like your plan to change? Say 'right now' or press 1, or 'next due date' or press 2" src="RP0305_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP0305_ini_01">
                <prompt-segments>
                  <audiofile text="I can change your plan right now, or I can schedule it to change starting with your next due date That's" src="RP0305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="parseFloat(newPlanPrice) &gt; parseFloat(currentPlanPrice)">
                <prompt id="RP0305_ini_03">
                  <prompt-segments>
                    <audiofile text="If you want to change it on your next due date, I'll schedule the change and you'll see your new plan price in your next statement So, when would you like it to change? Say 'right now' or 'next due date'" src="RP0305_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="RP0305_ini_02">
                <prompt-segments>
                  <audiofile text="If you want to change it now, there will be an extra charge on your next statement, to cover the rest of this month" src="RP0305_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP0305_OfferFutureDatedPlan_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP0305_OfferFutureDatedPlan_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <threshold_configuration maxrepeats="4" maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="immediate == 'lastresult'">
                <prompt id="RP0305_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Change your plan right now" src="RP0305_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="futuredate == 'lastresult'">
                  <prompt id="RP0305_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Change your plan on your next due date" src="RP0305_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="immediate == 'lastresult'">
                <prompt id="RP0305_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Change your plan right now" src="RP0305_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="futuredate == 'lastresult'">
                  <prompt id="RP0305_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Change your plan on your next due date" src="RP0305_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="immediate == 'lastresult'">
                <prompt id="RP0305_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Change your plan right now" src="RP0305_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="futuredate == 'lastresult'">
                  <prompt id="RP0305_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Change your plan on your next due date" src="RP0305_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="RP0310_MetricsChoseImmediatePlan_JDA">
      <data-access id="WhatsNext">
        <outputs>
          <output-variable name="label" type="String"/>
        </outputs>
      </data-access>
      <action next="RP0310_MetricsChoseImmediatePlan_DS"/>
    </data-access-state>

    <decision-state id="RP0310_MetricsChoseImmediatePlan_DS">
      <gotodialog next="RatePlan_Change#RP1316_DetectConflictingOffers_SD"/>
    </decision-state>

    <data-access-state id="RP0315_MetricsChoseFutureDatedPlan_JDA">
      <data-access id="WhatsNext">
        <outputs>
          <output-variable name="label" type="String"/>
        </outputs>
      </data-access>
      <action next="RP0315_MetricsChoseFutureDatedPlan_DS"/>
    </data-access-state>

    <decision-state id="RP0315_MetricsChoseFutureDatedPlan_DS">
      <gotodialog next="RatePlan_Change#RP1316_DetectConflictingOffers_SD"/>
    </decision-state>

    <decision-state id="RP0415_CheckAccountStatus_DS">
      <session-mapping key="accountStatus" value="(GlobalVars.GetAccountDetails == null)?'':GlobalVars.GetAccountDetails.accountStatus" type="String"/>
      <if cond="(accountStatus == 'active')">
        <action next="RP0505_CheckNumberPlansAvailable_JDA"/>
        <else>
          <gotodialog next="RatePlan_Change#RP1401_CheckContext_DS"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="RP0505_CheckNumberPlansAvailable_DS">
      <if cond="(GlobalVars.ratePlans.length == 0)">
        <action next="RP0510_PlayNoPlansAvailable_PP"/>
        <elseif cond="(GlobalVars.ratePlans.length == 1)">
          <action next="RP0515_OnePlanAvailable_DM"/>
        </elseif>
        <else>
          <gotodialog next="RatePlan_GetPlan#RP0520_AskRatePlan_DM"/>
        </else>
      </if>
    </decision-state>

    <play-state id="RP0510_PlayNoPlansAvailable_PP">
      <audio>
        <prompt id="RP0510_out_01">
          <prompt-segments>
            <audiofile text="Actually I don't see any other plans available for you at this time " src="RP0510_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <gotodialog next="RatePlan_GetPlan#RP0511_AskAddOrRemoveLines_DM"/>
    </play-state>

    <dm-state id="RP0515_OnePlanAvailable_DM" type="CUST">
      <session-mapping key="soc" value="GlobalVars.ratePlans[0].soc" type="String"/>
      <session-mapping key="ratePlans" value="GlobalVars.ratePlans" type="String"/>
      <success>
        <action label="yes">
          <session-mapping key="GlobalVars.selectedPlan" expr="soc"/>
          <session-mapping key="GlobalVars.newRatePlan" expr="soc"/>
          <action next="RP0301_CheckPlanOptions_JDA"/>
        </action>
        <action label="no">
          <audio>
            <prompt id="RP0515_out_01">
              <prompt-segments>
                <audiofile text="That s fine" src="RP0515_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <gotodialog next="RatePlan_Change#RP1335_GoToAnythingElse_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP0515_ini_02">
                <prompt-segments>
                  <audiofile text="There's one plan I can offer you " src="RP0515_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlanAndFeatures_RP1035"/>
              </prompt>
              <prompt id="RP0515_ini_06">
                <prompt-segments>
                  <audiofile text="Would you like to switch to that plan?" src="RP0515_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="RP0515_ini_02">
                <prompt-segments>
                  <audiofile text="There's one plan I can offer you " src="RP0515_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlanAndFeatures_RP1035"/>
              </prompt>
              <prompt id="RP0515_ini_06">
                <prompt-segments>
                  <audiofile text="Would you like to switch to that plan?" src="RP0515_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP0515_nm1_01">
                <prompt-segments>
                  <audiofile text="Sorry, that s the only plan you can switch to" src="RP0515_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0515_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2 To hear about the plan again, say  repeat  or press star" src="RP0515_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0515_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2 To hear about the plan again, say  repeat  or press star" src="RP0515_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="RP0515_nm1_01">
                <prompt-segments>
                  <audiofile text="Sorry, that s the only plan you can switch to" src="RP0515_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0515_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2 To hear about the plan again, say  repeat  or press star" src="RP0515_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0515_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2 To hear about the plan again, say  repeat  or press star" src="RP0515_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP0515_ini_02">
                <prompt-segments>
                  <audiofile text="There's one plan I can offer you " src="RP0515_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlanAndFeatures_RP1035"/>
              </prompt>
              <prompt id="RP0515_ini_06">
                <prompt-segments>
                  <audiofile text="Would you like to switch to that plan?" src="RP0515_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP0515_OnePlanAvailable_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP0515_OnePlanAvailable_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="RP0605_PayFirstYN_DM" type="YSNO">
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.suspendedRatePlanChange" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentAmount" expr="dueImmediatelyAmount"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'carePlanRestriction'"/>
          <action next="RP0610_MakePayment_SD"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
          <audio>
            <prompt id="RP0605_out_01">
              <prompt-segments>
                <audiofile text="No problem" src="RP0605_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="RP0620_PlayPaymentMethods_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP0605_ini_01">
                <prompt-segments>
                  <audiofile text="Also, it looks like you ll need to make a payment before we can change your plan That would be " src="RP0605_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0605_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to do that now?" src="RP0605_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="RP0605_PayFirstYN_DM_initial"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="RP0605_ini_01">
                <prompt-segments>
                  <audiofile text="Also, it looks like you ll need to make a payment before we can change your plan That would be " src="RP0605_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0605_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to do that now?" src="RP0605_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP0605_nm1_01">
                <prompt-segments>
                  <audiofile text="Before we can change your plan, you'll need to make a payment for " src="RP0605_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="RP0605_nm1_02">
                <prompt-segments>
                  <audiofile text="Do you wanna do that now?" src="RP0605_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0605_nm2_01">
                <prompt-segments>
                  <audiofile text="Do you want to make your payment over the phone now?" src="RP0605_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0605_nm2_01">
                <prompt-segments>
                  <audiofile text="Do you want to make your payment over the phone now?" src="RP0605_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0605_nm1_01">
                <prompt-segments>
                  <audiofile text="Before we can change your plan, you'll need to make a payment for " src="RP0605_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="RP0605_nm1_02">
                <prompt-segments>
                  <audiofile text="Do you wanna do that now?" src="RP0605_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0605_nm2_01">
                <prompt-segments>
                  <audiofile text="Do you want to make your payment over the phone now?" src="RP0605_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0605_nm2_01">
                <prompt-segments>
                  <audiofile text="Do you want to make your payment over the phone now?" src="RP0605_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP0605_PayFirstYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP0605_PayFirstYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="RP0610_MakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="RP0610_MakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="RP0610_MakePayment_SD_return_CS">
      <action next="RP0615_PostPaymentTransition_PP"/>
    </custom-state>

    <play-state id="RP0615_PostPaymentTransition_PP">
      <audio>
        <prompt id="RP0615_out_01">
          <prompt-segments>
            <audiofile text="Okay On with your plan change" src="RP0615_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="RP0205_GetAvailableRatePlanOffers_DB_DA"/>
    </play-state>

    <play-state id="RP0620_PlayPaymentMethods_PP">
      <audio>
        <prompt id="RP0620_out_01">
          <prompt-segments>
            <audiofile text="When youre ready to make a payment you can call back here or use the myMetro app You can also pay online or get a map of our locations near you at metrobyt-mobilecom" src="RP0620_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="RP0625_Goodbye_SD"/>
    </play-state>

    <subdialog-state id="RP0625_Goodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </subdialog-state>
    <subdialog-state id="RP1280_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="RP1280_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="RP1280_GoToCallTransfer_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <decision-state id="RP0301_CheckPlanOptions_DS">
      <session-mapping key="selectedPlan" value="GlobalVars.selectedPlan" type="String"/>
      <session-mapping key="ratePlans" value="GlobalVars.ratePlans" type="String"/>
      <session-mapping key="planChangeTimeOption" value="" type="String"/>
      <session-mapping key="GlobalVars.planChangeTimeOption" expr="planChangeTimeOption"/>
      <if cond="(GlobalVars.planChangeTimeOption == 'immediate')">
        <action next="RP0317_AcceptImmediateOrFutureChange_DM"/>
        <elseif cond="(GlobalVars.planChangeTimeOption == 'future')">
          <action next="RP0317_AcceptImmediateOrFutureChange_DM"/>
        </elseif>
        <else>
          <action next="RP0304_PlayFutureDatedTransition_PP"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="RP0317_AcceptImmediateOrFutureChange_DM" type="CUST">
      <session-mapping key="selectedPlan" value="GlobalVars.selectedPlan" type="String"/>
      <session-mapping key="ratePlans" value="GlobalVars.ratePlans" type="String"/>
      <session-mapping key="futureDate" value="GlobalVars.futureDate" type="String"/>
      <session-mapping key="newPlanPrice" value="GlobalVars.selectedPlanPrice" type="String"/>
      <session-mapping key="currentPlanPrice" value="GlobalVars.GetAccountDetails.ratePlanPrice" type="String"/>
      <session-mapping key="isNewPlanPriceLessThanCurrentPlanPrice" value="(parseFloat(newPlanPrice) &lt; parseFloat(currentPlanPrice))? true : false" type="String"/>
      <session-mapping key="isUpgrade" value="parseFloat(GlobalVars.selectedPlanPrice) &gt; parseFloat(GlobalVars.GetAccountDetails.ratePlanPrice) ? true : false" type="String"/>
      <session-mapping key="planChangeTimeOption" value="" type="String"/>
      <session-mapping key="isUpgrade" expr="parseFloat(GlobalVars.selectedPlanPrice) &gt; parseFloat(GlobalVars.GetAccountDetails.ratePlanPrice) ? true : false"/>
      <success>
        <action label="approve_change">
          <if cond="planChangeTimeOption == 'future'">
            <session-mapping key="GlobalVars.futureChangeOffer" value="true" type="Boolean"/>
            <else>
              <session-mapping key="GlobalVars.futureDate" expr="undefined"/>
            </else>
          </if>
          <gotodialog next="RatePlan_Change#RP1319_PlaySubmitMessage_PP"/>
        </action>
        <action label="cancel">
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="planChangeTimeOption == 'immediate'">
                <prompt id="RP0317_ini_01">
                  <prompt-segments>
                    <audiofile text="For this plan, the change will be effective right away" src="RP0317_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_ini_02" cond="isUpgrade == true">
                  <prompt-segments>
                    <audiofile text="There will also be an extra charge on your next statement, to cover the rest of this month" src="RP0317_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP0317_ini_03">
                    <prompt-segments>
                      <audiofile text="It looks like we can only change you to this plan starting with your next due date That's" src="RP0317_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_ini_04" cond="isNewPlanPriceLessThanCurrentPlanPrice == true">
                    <prompt-segments>
                      <audiofile text="This because it's less expensive than your current plan, so we want to make sure you get to enjoy everything you've already prepaid this month" src="RP0317_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_ini_05" cond="isNewPlanPriceLessThanCurrentPlanPrice == true">
                    <prompt-segments>
                      <audiofile text="You *won't* have to call back or do anything else after today Your plan will just change on its own when it's time" src="RP0317_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="RP0317_ini_06">
                <prompt-segments>
                  <audiofile text="Say 'repeat that''approve change' or 'cancel'" src="RP0317_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="planChangeTimeOption == 'immediate'">
                <prompt id="RP0317_ini_01">
                  <prompt-segments>
                    <audiofile text="For this plan, the change will be effective right away" src="RP0317_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_ini_02" cond="isUpgrade == true">
                  <prompt-segments>
                    <audiofile text="There will also be an extra charge on your next statement, to cover the rest of this month" src="RP0317_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP0317_ini_03">
                    <prompt-segments>
                      <audiofile text="It looks like we can only change you to this plan starting with your next due date That's" src="RP0317_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_ini_04" cond="isNewPlanPriceLessThanCurrentPlanPrice == true">
                    <prompt-segments>
                      <audiofile text="This because it's less expensive than your current plan, so we want to make sure you get to enjoy everything you've already prepaid this month" src="RP0317_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_ini_05" cond="isNewPlanPriceLessThanCurrentPlanPrice == true">
                    <prompt-segments>
                      <audiofile text="You *won't* have to call back or do anything else after today Your plan will just change on its own when it's time" src="RP0317_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="RP0317_ini_06">
                <prompt-segments>
                  <audiofile text="Say 'repeat that''approve change' or 'cancel'" src="RP0317_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="planChangeTimeOption == 'immediate'">
                <prompt id="RP0317_ini_01">
                  <prompt-segments>
                    <audiofile text="For this plan, the change will be effective right away" src="RP0317_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_ini_02" cond="isUpgrade == true">
                  <prompt-segments>
                    <audiofile text="There will also be an extra charge on your next statement, to cover the rest of this month" src="RP0317_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP0317_ini_03">
                    <prompt-segments>
                      <audiofile text="It looks like we can only change you to this plan starting with your next due date That's" src="RP0317_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_ini_04" cond="isNewPlanPriceLessThanCurrentPlanPrice == true">
                    <prompt-segments>
                      <audiofile text="This because it's less expensive than your current plan, so we want to make sure you get to enjoy everything you've already prepaid this month" src="RP0317_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_ini_05" cond="isNewPlanPriceLessThanCurrentPlanPrice == true">
                    <prompt-segments>
                      <audiofile text="You *won't* have to call back or do anything else after today Your plan will just change on its own when it's time" src="RP0317_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="RP0317_ini_06">
                <prompt-segments>
                  <audiofile text="Say 'repeat that''approve change' or 'cancel'" src="RP0317_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="planChangeTimeOption == 'immediate'">
                <prompt id="RP0317_nm2_01">
                  <prompt-segments>
                    <audiofile text="For this plan, the change will be effective right away" src="RP0317_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_nm2_02" cond="isUpgrade == true">
                  <prompt-segments>
                    <audiofile text="There will also be an extra charge on your next statement, to cover the rest of this month" src="RP0317_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_nm2_03">
                  <prompt-segments>
                    <audiofile text="If you'd like to go ahead with this change press 1  If not press 2" src="RP0317_nm2_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP0317_nm2_04">
                    <prompt-segments>
                      <audiofile text="It looks like we can only change you to this plan starting with your next due date That's" src="RP0317_nm2_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="RP0317_nm2_06">
                    <prompt-segments>
                      <audiofile text="This because it's less expensive than your current plan, so we want to make sure you get to enjoy everything you've already prepaid this month" src="RP0317_nm2_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_nm2_07">
                    <prompt-segments>
                      <audiofile text="You *won't* have to call back or do anything else after today Your plan will just change on its own when it's time" src="RP0317_nm2_07.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_nm2_08">
                    <prompt-segments>
                      <audiofile text="If you'd like to go ahead with this change press 1  If not press 2" src="RP0317_nm2_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="planChangeTimeOption == 'immediate'">
                <prompt id="RP0317_nm2_01">
                  <prompt-segments>
                    <audiofile text="For this plan, the change will be effective right away" src="RP0317_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_nm2_02" cond="isUpgrade == true">
                  <prompt-segments>
                    <audiofile text="There will also be an extra charge on your next statement, to cover the rest of this month" src="RP0317_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_nm2_03">
                  <prompt-segments>
                    <audiofile text="If you'd like to go ahead with this change press 1  If not press 2" src="RP0317_nm2_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP0317_nm2_04">
                    <prompt-segments>
                      <audiofile text="It looks like we can only change you to this plan starting with your next due date That's" src="RP0317_nm2_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="RP0317_nm2_06">
                    <prompt-segments>
                      <audiofile text="This because it's less expensive than your current plan, so we want to make sure you get to enjoy everything you've already prepaid this month" src="RP0317_nm2_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_nm2_07">
                    <prompt-segments>
                      <audiofile text="You *won't* have to call back or do anything else after today Your plan will just change on its own when it's time" src="RP0317_nm2_07.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_nm2_08">
                    <prompt-segments>
                      <audiofile text="If you'd like to go ahead with this change press 1  If not press 2" src="RP0317_nm2_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="planChangeTimeOption == 'immediate'">
                <prompt id="RP0317_ini_01">
                  <prompt-segments>
                    <audiofile text="For this plan, the change will be effective right away" src="RP0317_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_ini_02" cond="isUpgrade == true">
                  <prompt-segments>
                    <audiofile text="There will also be an extra charge on your next statement, to cover the rest of this month" src="RP0317_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP0317_ini_03">
                    <prompt-segments>
                      <audiofile text="It looks like we can only change you to this plan starting with your next due date That's" src="RP0317_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_ini_04" cond="isNewPlanPriceLessThanCurrentPlanPrice == true">
                    <prompt-segments>
                      <audiofile text="This because it's less expensive than your current plan, so we want to make sure you get to enjoy everything you've already prepaid this month" src="RP0317_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_ini_05" cond="isNewPlanPriceLessThanCurrentPlanPrice == true">
                    <prompt-segments>
                      <audiofile text="You *won't* have to call back or do anything else after today Your plan will just change on its own when it's time" src="RP0317_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="RP0317_ini_06">
                <prompt-segments>
                  <audiofile text="Say 'repeat that''approve change' or 'cancel'" src="RP0317_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="planChangeTimeOption == 'immediate'">
                <prompt id="RP0317_nm2_01">
                  <prompt-segments>
                    <audiofile text="For this plan, the change will be effective right away" src="RP0317_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_nm2_02" cond="isUpgrade == true">
                  <prompt-segments>
                    <audiofile text="There will also be an extra charge on your next statement, to cover the rest of this month" src="RP0317_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_nm2_03">
                  <prompt-segments>
                    <audiofile text="If you'd like to go ahead with this change press 1  If not press 2" src="RP0317_nm2_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP0317_nm2_04">
                    <prompt-segments>
                      <audiofile text="It looks like we can only change you to this plan starting with your next due date That's" src="RP0317_nm2_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="RP0317_nm2_06">
                    <prompt-segments>
                      <audiofile text="This because it's less expensive than your current plan, so we want to make sure you get to enjoy everything you've already prepaid this month" src="RP0317_nm2_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_nm2_07">
                    <prompt-segments>
                      <audiofile text="You *won't* have to call back or do anything else after today Your plan will just change on its own when it's time" src="RP0317_nm2_07.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_nm2_08">
                    <prompt-segments>
                      <audiofile text="If you'd like to go ahead with this change press 1  If not press 2" src="RP0317_nm2_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="planChangeTimeOption == 'immediate'">
                <prompt id="RP0317_nm2_01">
                  <prompt-segments>
                    <audiofile text="For this plan, the change will be effective right away" src="RP0317_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_nm2_02" cond="isUpgrade == true">
                  <prompt-segments>
                    <audiofile text="There will also be an extra charge on your next statement, to cover the rest of this month" src="RP0317_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_nm2_03">
                  <prompt-segments>
                    <audiofile text="If you'd like to go ahead with this change press 1  If not press 2" src="RP0317_nm2_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP0317_nm2_04">
                    <prompt-segments>
                      <audiofile text="It looks like we can only change you to this plan starting with your next due date That's" src="RP0317_nm2_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="RP0317_nm2_06">
                    <prompt-segments>
                      <audiofile text="This because it's less expensive than your current plan, so we want to make sure you get to enjoy everything you've already prepaid this month" src="RP0317_nm2_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_nm2_07">
                    <prompt-segments>
                      <audiofile text="You *won't* have to call back or do anything else after today Your plan will just change on its own when it's time" src="RP0317_nm2_07.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_nm2_08">
                    <prompt-segments>
                      <audiofile text="If you'd like to go ahead with this change press 1  If not press 2" src="RP0317_nm2_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="planChangeTimeOption == 'immediate'">
                <prompt id="RP0317_ini_01">
                  <prompt-segments>
                    <audiofile text="For this plan, the change will be effective right away" src="RP0317_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP0317_ini_02" cond="isUpgrade == true">
                  <prompt-segments>
                    <audiofile text="There will also be an extra charge on your next statement, to cover the rest of this month" src="RP0317_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP0317_ini_03">
                    <prompt-segments>
                      <audiofile text="It looks like we can only change you to this plan starting with your next due date That's" src="RP0317_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_ini_04" cond="isNewPlanPriceLessThanCurrentPlanPrice == true">
                    <prompt-segments>
                      <audiofile text="This because it's less expensive than your current plan, so we want to make sure you get to enjoy everything you've already prepaid this month" src="RP0317_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP0317_ini_05" cond="isNewPlanPriceLessThanCurrentPlanPrice == true">
                    <prompt-segments>
                      <audiofile text="You *won't* have to call back or do anything else after today Your plan will just change on its own when it's time" src="RP0317_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="RP0317_ini_06">
                <prompt-segments>
                  <audiofile text="Say 'repeat that''approve change' or 'cancel'" src="RP0317_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP0317_AcceptImmediateOrFutureChange_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP0317_AcceptImmediateOrFutureChange_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="RP0326_CheckDeclineNextSteps_DS">
      <if cond="(GlobalVars.declineChangeCounter == 2)">
        <action next="RP1280_GoToCallTransfer_SD"/>
        <elseif cond="(GlobalVars.ratePlans.length &gt; 1)">
          <if cond="(GlobalVars.GetAccountDetails.accountStatus == 'active')">
            <gotodialog next="RatePlan_GetPlan#RP0520_AskRatePlan_DM"/>
            <else>
              <action next="RP0415_CheckAccountStatus_JDA"/>
            </else>
          </if>
        </elseif>
        <else>
          <action next="RP1280_GoToCallTransfer_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="RP0155_GoToDataUsage_SD">
      <gotodialog next="DataUsage_Main_Dialog"/>
      <action next="RP0155_GoToDataUsage_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="RP0155_GoToDataUsage_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="RP0150_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="RP0150_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="RP0150_GoToCallTransfer_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <dm-state id="RP0024_AskAddRemoveLines_DM" type="CUST">
      <success>
        <action label="change-plan_rp">
          <action next="RP0026_AskThisLineOnly_DM"/>
        </action>
        <action label="add-line">
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'add_line'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="false" type="Boolean"/>
          <action next="RP1280_GoToCallTransfer_SD"/>
        </action>
        <action label="cancel-line">
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'cancel_line'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="false" type="Boolean"/>
          <action next="RP1280_GoToCallTransfer_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP0024_ini_01">
                <prompt-segments>
                  <audiofile text="You can say 'change rate plan', 'add a line' or 'cancel a line'" src="RP0024_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="RP0024_ini_01">
                <prompt-segments>
                  <audiofile text="You can say 'change rate plan', 'add a line' or 'cancel a line'" src="RP0024_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP0024_nm1_01">
                <prompt-segments>
                  <audiofile text="Say 'change rate plan', or press 1  'add a line', or press 2  'cancel a line' or press 3" src="RP0024_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0024_nm2_01">
                <prompt-segments>
                  <audiofile text="Say 'change rate plan', or press 1  'add a line', or press 2  'cancel a line' or press 3" src="RP0024_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0024_nm2_01">
                <prompt-segments>
                  <audiofile text="Say 'change rate plan', or press 1  'add a line', or press 2  'cancel a line' or press 3" src="RP0024_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0024_nm1_01">
                <prompt-segments>
                  <audiofile text="Say 'change rate plan', or press 1  'add a line', or press 2  'cancel a line' or press 3" src="RP0024_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0024_nm2_01">
                <prompt-segments>
                  <audiofile text="Say 'change rate plan', or press 1  'add a line', or press 2  'cancel a line' or press 3" src="RP0024_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0024_nm2_01">
                <prompt-segments>
                  <audiofile text="Say 'change rate plan', or press 1  'add a line', or press 2  'cancel a line' or press 3" src="RP0024_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP0024_ini_01">
                <prompt-segments>
                  <audiofile text="You can say 'change rate plan', 'add a line' or 'cancel a line'" src="RP0024_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP0024_AskAddRemoveLines_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP0024_AskAddRemoveLines_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'change_plan'">
                <prompt id="RP0024_cnf_ini_03">
                  <prompt-segments>
                    <audiofile text="change rate plan" src="RP0024_cnf_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'add_line'">
                <prompt id="RP0024_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="add a line" src="RP0024_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'cancel_line'">
                <prompt id="RP0024_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="cancel a line" src="RP0024_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="RP0026_AskThisLineOnly_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="true" type="Boolean"/>
          <action next="RP0025_CheckNeedPlayCurrentPlan_JDA"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="true" type="Boolean"/>
          <action next="RP1280_GoToCallTransfer_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP0026_ini_01">
                <prompt-segments>
                  <audiofile text="Since you have a multiline account changing only this line could affect any bundled savings you have on your accountWould you like to change the plan for this line *only*?" src="RP0026_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="RP0026_ini_01">
                <prompt-segments>
                  <audiofile text="Since you have a multiline account changing only this line could affect any bundled savings you have on your accountWould you like to change the plan for this line *only*?" src="RP0026_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP0026_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to change the plan for this line *only*?" src="RP0026_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0026_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to change the rate plan on this line only say yes or press 1  Otherwise say 'no', or press 2" src="RP0026_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0026_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to change the rate plan on this line only say yes or press 1  Otherwise say 'no', or press 2" src="RP0026_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0026_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to change the plan for this line *only*?" src="RP0026_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0026_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to change the rate plan on this line only say yes or press 1  Otherwise say 'no', or press 2" src="RP0026_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0026_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to change the rate plan on this line only say yes or press 1  Otherwise say 'no', or press 2" src="RP0026_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP0026_ini_01">
                <prompt-segments>
                  <audiofile text="Since you have a multiline account changing only this line could affect any bundled savings you have on your accountWould you like to change the plan for this line *only*?" src="RP0026_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP0026_AskThisLineOnly_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP0026_AskThisLineOnly_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  