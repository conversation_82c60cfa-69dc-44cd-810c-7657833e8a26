<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Activation_GetName_Dialog">
    <play-state id="AC1400_TransitionToAddress_PP">
      <audio>
        <prompt id="AC1400_out_01">
          <prompt-segments>
            <audiofile text="Next I already have your zip code, now I ll need the rest of your mailing address" src="AC1400_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AC1410_AddressModule_SD"/>
    </play-state>

    <subdialog-state id="AC1410_AddressModule_SD">
      <gotodialog next="AdressCapture_Main_Dialog"/>
      <action next="AC1410_AddressModule_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AC1410_AddressModule_SD_return_CS">
      <if cond="GlobalVars.addressreturnCode == 'SUCCESS'">
        <session-mapping key="GlobalVars.haveActivationAddress" value="true" type="Boolean"/>
        <gotodialog next="Activation_ValidateAddress#AC1420_ValidateAddress_DB"/>
        <else>
          <action next="AC1415_PlayAddressFailure_PP"/>
        </else>
      </if>
    </custom-state>

    <play-state id="AC1415_PlayAddressFailure_PP">
      <audio>
        <prompt id="AC1415_out_01">
          <prompt-segments>
            <audiofile src="AC1415_out_01.wav" text="I'm sorry I'm having trouble Let s move on without your address for now"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <gotodialog next="Activation_Common#AC1425_PlaySecurityTransition_PP"/>
    </play-state>

  </dialog>
  