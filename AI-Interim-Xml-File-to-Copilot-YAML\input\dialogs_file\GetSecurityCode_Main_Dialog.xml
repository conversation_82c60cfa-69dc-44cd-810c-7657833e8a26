<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="GetSecurityCode_Main_Dialog">
    <custom-state id="GC1000_setLocalVariables_CS">
      <session-mapping key="GetSecurityCodeVars.invalidSecCodeAttempts" expr="1"/>
      <session-mapping key="GetSecurityCodeVars.securityCode" expr="0"/>
      <action next="GC1000_GetSecurityCode_DM"/>
    </custom-state>

    <dm-state id="GC1000_GetSecurityCode_DM" type="CUST">
      <session-mapping key="reentry" value="GlobalVars.GC1000_visited ? GlobalVars.GC1000_visited : false" type="String"/>
      <session-mapping key="FromGC1010" value="GlobalVars.FromGC1010 ? GlobalVars.FromGC1010 : false" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="askSQEntry" value="GlobalVars.askSQEntry" type="String"/>
      <session-mapping key="collection_maxnoinputs" value="3" type="String"/>
      <session-mapping key="heardCodeInfo" value="GlobalVars.heardCodeInfo" type="String"/>
      <session-mapping key="lastPinTry" value="GlobalVars.lastPinTry" type="String"/>
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorGC1000?GlobalVars.saidOperatorGC1000:false" type="String"/>
      <session-mapping key="continue228TaskAfterPayment" value="GlobalVars.continue228TaskAfterPayment" type="String"/>
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <if cond="GlobalVars.heardCodeInfo">
        <session-mapping key="collection_maxnoinputs" expr="0"/>
      </if>
      <session-mapping key="GlobalVars.GC1000_visited" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.FromGC1010" value="false" type="Boolean"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorGC1000" value="false" type="Boolean"/>
        <action label="dont_know">
          <if cond="GlobalVars.lastPinTry == true">
            <session-mapping key="GlobalVars.dontKnowPIN" value="true" type="Boolean"/>
            <action next="GC1090_GoToTransfer_SD"/>
            <else>
              <session-mapping key="GlobalVars.heardCodeInfo" value="false" type="Boolean"/>
              <session-mapping key="GlobalVars.transferReason" expr="'getPIN.dontKnow'"/>
              <action next="GC1085_GoToAccountPinReset_SD"/>
            </else>
          </if>
        </action>
        <action label="wait" next="GC1010_AskLoginWait_DM">
          <audio>
            <prompt id="GC1000_out_02">
              <prompt-segments>
                <audiofile text="No problem I'll wait" src="GC1000_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.heardCodeInfo" value="false" type="Boolean"/>
        </action>
        <action label="code_info" next="GC1000_GetSecurityCode_DM">
          <session-mapping key="GlobalVars.heardCodeInfo" value="true" type="Boolean"/>
        </action>
        <action label="default">
          <session-mapping key="GetSecurityCodeVars.securityCode" expr="GC1000_GetSecurityCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.heardCodeInfo" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.verificationType" expr="'pin'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="GC1000_GetSecurityCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.cti_PIN" expr="GC1000_GetSecurityCode_DM.returnvalue"/>
          <action next="GC1029_Authenticate_DB_DA"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperatorGC1000" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.saidOperatorGC1000" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.heardCodeInfo" value="false" type="Boolean"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="GC1000_ini_03">
                  <prompt-segments>
                    <audiofile text="Now, to help you with that, I also need your 6-to-15-digit account PIN  Say or enter it now" src="GC1000_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="GC1000_ini_01">
                    <prompt-segments>
                      <audiofile text="Now, to help you with that, I also need your 8-digit account PIN  Say or enter it now" src="GC1000_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="GC1000_GetSecurityCode_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="GC1000_GetSecurityCode_DM_help"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="GC1000_ini_03">
                  <prompt-segments>
                    <audiofile text="Now, to help you with that, I also need your 6-to-15-digit account PIN  Say or enter it now" src="GC1000_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="GC1000_ini_01">
                    <prompt-segments>
                      <audiofile text="Now, to help you with that, I also need your 8-digit account PIN  Say or enter it now" src="GC1000_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="GC1000_nm1_02">
                  <prompt-segments>
                    <audiofile text="Please say or enter your 6-to-15-digit account PIN  If you need to find it, say 'wait a minute' You can also say 'where can I find it" src="GC1000_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="GC1000_nm1_01">
                    <prompt-segments>
                      <audiofile text="Please say or enter your 8-digit account PIN  If you need to find it, say 'wait a minute' You can also say 'where can I find it' " src="GC1000_nm1_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="GC1000_nm2_02">
                  <prompt-segments>
                    <audiofile text="Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'" src="GC1000_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="GC1000_nm2_01">
                    <prompt-segments>
                      <audiofile text="Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'" src="GC1000_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="GC1000_nm2_02">
                  <prompt-segments>
                    <audiofile text="Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'" src="GC1000_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="GC1000_nm2_01">
                    <prompt-segments>
                      <audiofile text="Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'" src="GC1000_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="GC1000_nm1_02">
                  <prompt-segments>
                    <audiofile text="Please say or enter your 6-to-15-digit account PIN  If you need to find it, say 'wait a minute' You can also say 'where can I find it" src="GC1000_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="GC1000_nm1_01">
                    <prompt-segments>
                      <audiofile text="Please say or enter your 8-digit account PIN  If you need to find it, say 'wait a minute' You can also say 'where can I find it' " src="GC1000_nm1_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="GC1000_nm2_02">
                  <prompt-segments>
                    <audiofile text="Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'" src="GC1000_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="GC1000_nm2_01">
                    <prompt-segments>
                      <audiofile text="Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'" src="GC1000_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="GC1000_nm2_02">
                  <prompt-segments>
                    <audiofile text="Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'" src="GC1000_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="GC1000_nm2_01">
                    <prompt-segments>
                      <audiofile text="Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you need information on where to look for it, say 'where can I find it'" src="GC1000_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="GC1000_out_01" cond="heardCodeInfo == true">
                <prompt-segments>
                  <audiofile text="If you don't remember your account PIN, you can find it along with your phone number on your Start of Service form That's the form you got when you signed up for your Metro account If you don't have it handy, you can hang up and call back when you've got it Or if you want to look for it now, say 'wait a minute'" src="GC1000_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1000_rin_04" cond="lastPinTry == true &amp;&amp; heardCodeInfo != true">
                <prompt-segments>
                  <audiofile text="Ok, please say or enter your pin one more time " src="GC1000_rin_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1000_rin_01" cond="FromGC1010 == true &amp;&amp; lastPinTry != true &amp;&amp; heardCodeInfo != true &amp;&amp; accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="Ok, say or enter your 6-to-15-digit account PIN " src="GC1000_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1000_rin_05" cond="FromGC1010 == true &amp;&amp; lastPinTry != true &amp;&amp; heardCodeInfo != true &amp;&amp; accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text=" Ok, say or enter your 8-digit account PIN" src="GC1000_rin_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1000_rin_03" cond="(saidOperator == true) &amp;&amp; FromGC1010 != true &amp;&amp; heardCodeInfo != true &amp;&amp; lastPinTry != true &amp;&amp; accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="Go ahead and say or enter your 6-to-15-digit account PIN  You can also say 'wait a minute', or 'where can I find it'" src="GC1000_rin_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1000_rin_06" cond="(saidOperator == true) &amp;&amp; FromGC1010 != true &amp;&amp; heardCodeInfo != true &amp;&amp; lastPinTry != true &amp;&amp; accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text=" Go ahead and say or enter your 8-digit account PIN  You can also say 'wait a minute', or 'where can I find it' " src="GC1000_rin_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1000_rin_02" cond="(saidOperator == false) &amp;&amp; FromGC1010 != true &amp;&amp; heardCodeInfo != true &amp;&amp; lastPinTry != true">
                <prompt-segments>
                  <audiofile text="Please say or enter your security code account PIN again  You can also say 'wait a minute', or 'where can I find it' " src="GC1000_rin_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="GC1000_GetSecurityCode_DM.grxml" count="1"/>
          <dtmfgrammars filename="GC1000_GetSecurityCode_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="2000ms" completetimeout="0ms" maxspeechtimeout="15000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms">
        </vxml_properties>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxrepeats="1" maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="GC1000_GetSecurityCode_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="GC1010_AskLoginWait_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <success>
        <action label="ready" next="GC1000_GetSecurityCode_DM">
          <session-mapping key="GlobalVars.FromGC1010" value="true" type="Boolean"/>
        </action>
        <action label="dont_know">
          <audio>
            <prompt id="GC1010_out_01">
              <prompt-segments>
                <audiofile text="Allright" src="GC1010_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="GlobalVars.lastPinTry == true">
            <session-mapping key="GlobalVars.dontKnowPIN" value="true" type="Boolean"/>
            <action next="GC1090_GoToTransfer_SD"/>
            <else>
              <session-mapping key="GlobalVars.transferReason" expr="'getPIN.dontKnow'"/>
              <action next="GC1085_GoToAccountPinReset_SD"/>
            </else>
          </if>
        </action>
        <action label="default">
          <session-mapping key="GetSecurityCodeVars.securityCode" expr="GC1010_AskLoginWait_DM.returnvalue"/>
          <session-mapping key="GlobalVars.verificationType" expr="'pin'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="GC1010_AskLoginWait_DM.returnvalue"/>
          <action next="GC1029_Authenticate_DB_DA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="GC1010_ini_02">
                  <prompt-segments>
                    <audiofile text="Once you're ready with your 6-to-15-digit account PIN, just say 'Continue' Or say 'I can't find it' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your 6-to-15-digit account PIN, say 'Continue' or press 1, or say 'I can't find it' or press 2  " src="GC1010_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="GC1010_ini_01">
                    <prompt-segments>
                      <audiofile text="Once you're ready with your 8-digit account PIN, just say 'Continue'Or say 'I can't find it' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your 8-digit account PIN, say 'Continue' or press 1, or say 'I can't find it' or press 2 " src="GC1010_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="GC1010_ini_03">
                <prompt-segments>
                  <audiofile text=" You can say 'Continue' or press 1, or 'I can't find it' or press 2 Hmmm, I seem to be having some trouble Let's move on " src="GC1010_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="GC1010_AskLoginWait_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="GC1010_ini_02">
                  <prompt-segments>
                    <audiofile text="Once you're ready with your 6-to-15-digit account PIN, just say 'Continue' Or say 'I can't find it' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your 6-to-15-digit account PIN, say 'Continue' or press 1, or say 'I can't find it' or press 2  " src="GC1010_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="GC1010_ini_01">
                    <prompt-segments>
                      <audiofile text="Once you're ready with your 8-digit account PIN, just say 'Continue'Or say 'I can't find it' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your 8-digit account PIN, say 'Continue' or press 1, or say 'I can't find it' or press 2 " src="GC1010_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="GC1010_ini_03">
                <prompt-segments>
                  <audiofile text=" You can say 'Continue' or press 1, or 'I can't find it' or press 2 Hmmm, I seem to be having some trouble Let's move on " src="GC1010_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="GC1010_ini_02">
                  <prompt-segments>
                    <audiofile text="Once you're ready with your 6-to-15-digit account PIN, just say 'Continue' Or say 'I can't find it' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your 6-to-15-digit account PIN, say 'Continue' or press 1, or say 'I can't find it' or press 2  " src="GC1010_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="GC1010_ini_01">
                    <prompt-segments>
                      <audiofile text="Once you're ready with your 8-digit account PIN, just say 'Continue'Or say 'I can't find it' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your 8-digit account PIN, say 'Continue' or press 1, or say 'I can't find it' or press 2 " src="GC1010_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="GC1010_ini_03">
                <prompt-segments>
                  <audiofile text=" You can say 'Continue' or press 1, or 'I can't find it' or press 2 Hmmm, I seem to be having some trouble Let's move on " src="GC1010_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="GC1010_AskLoginWait_DM.grxml" count="1"/>
          <dtmfgrammars filename="GC1010_AskLoginWait_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="1000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="1000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxrepeats="1" maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="GC1000_GetSecurityCode_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="GC1029_Authenticate_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="accessToken" value="GlobalVars.accessToken" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="verificationType" value="GlobalVars.verificationType" type="String"/>
      <session-mapping key="verificationValue" value="GlobalVars.verificationValue" type="String"/>
      <session-mapping key="pin" value="GlobalVars.verificationValue" type="String"/>
      <data-access id="Authenticate" classname="com.nuance.metro.dataaccess.ValidatePinForAuthenticate">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="pin" mask="true"/>
          <input-variable name="accessToken" mask="false"/>
          <input-variable name="verificationType"/>
          <input-variable name="verificationValue" mask="true"/>
          <input-variable name="sessionId"/>
          <input-variable name="providerId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="JWTToken"/>
          <output-variable name="expiresIn"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="Authenticate.status == 'FAILURE'">
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          <gotodialog next="CallTransfer_Main_Dialog"/>
          <elseif cond="Authenticate.acctLocked == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.acctLocked" value="true" type="Boolean"/>
            <action next="GC1040_InvalidSecCode_PP"/>
          </elseif>
          <elseif cond="Authenticate.onePinTryRemaining  == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.onePinTryRemaining" value="true" type="Boolean"/>
            <action next="GC1045_AskResetInformation_DM"/>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <action next="GC1030_CheckSecurityCode_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="GC1030_CheckSecurityCode_DS">
      <if cond="(GlobalVars.Authenticate &amp;&amp; GlobalVars.Authenticate.status == 'SUCCESS')">
        <session-mapping key="GlobalVars.loggedIn" value="true" type="Boolean"/>
        <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'authenticated_by_pin')"/>
        <action next="getReturnLink()"/>
        <else>
          <if cond="GlobalVars.onePinTryRemaining == true">
            <action next="GC1045_AskResetInformation_DM"/>
            <else>
              <action next="GC1040_InvalidSecCode_PP"/>
            </else>
          </if>
        </else>
      </if>
      <action next="GC1040_InvalidSecCode_PP"/>
    </decision-state>

    <play-state id="GC1040_InvalidSecCode_PP">
      <audio>
        <if cond="GlobalVars.acctLocked == true">
          <prompt id="GC1040_out_04">
            <prompt-segments>
              <audiofile text="Sorry, I'm unable to access your account" src="GC1040_out_04.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="GetSecurityCodeVars.invalidSecCodeAttempts &lt; 2">
            <prompt id="GC1040_out_01">
              <prompt-segments>
                <audiofile text="Sorry, that account PIN's  not right" src="GC1040_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="GC1040_out_02">
              <prompt-segments>
                <audiofile text="Sorry, that account PIN's  still not right" src="GC1040_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="GlobalVars.acctLocked == true">
        <action next="GC1090_GoToTransfer_SD"/>
        <else>
          <action next="GC1060_ReconfirmCodeYN_DM"/>
        </else>
      </if>
    </play-state>

    <dm-state id="GC1060_ReconfirmCodeYN_DM" type="CUST">
      <session-mapping key="securityCode" value="GetSecurityCodeVars.securityCode" type="String"/>
      <success>
        <action label="no" next="GC1070_PostRecoPrompt_PP">
          <session-mapping key="GetSecurityCodeVars.invalidSecCodeAttempts" expr="GetSecurityCodeVars.invalidSecCodeAttempts + 1"/>
          <action next="GC1070_PostRecoPrompt_PP"/>
        </action>
        <action label="yes">
          <if cond="GlobalVars.lastPinTry == true">
            <session-mapping key="GlobalVars.dontKnowPIN" value="true" type="Boolean"/>
            <action next="GC1090_GoToTransfer_SD"/>
            <else>
              <action next="GC1085_GoToAccountPinReset_SD"/>
            </else>
          </if>
        </action>
        <action label="dont_know">
          <action next="GC1085_GoToAccountPinReset_SD"/>
        </action>
        <action label="dont_know" next="GC1085_GoToAccountPinReset_SD"/>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="GC1060_ini_01">
                <prompt-segments>
                  <audiofile text="I just want to make sure Your account PIN is" src="GC1060_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_ini_04">
                <prompt-segments>
                  <audiofile text="Is that right?" src="GC1060_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="GC1060_ini_01">
                <prompt-segments>
                  <audiofile text="I just want to make sure Your account PIN is" src="GC1060_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_ini_04">
                <prompt-segments>
                  <audiofile text="Is that right?" src="GC1060_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="GC1060_ni1_01">
                <prompt-segments>
                  <audiofile text="Just say yes or no Your security code is" src="GC1060_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_ni1_04">
                <prompt-segments>
                  <audiofile text="Right?" src="GC1060_ni1_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="GC1060_nm2_01">
                <prompt-segments>
                  <audiofile text="If your account PIN is" src="GC1060_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_2500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm2_04">
                <prompt-segments>
                  <audiofile text="press 1 If not, press 2" src="GC1060_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_2500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm2_06">
                <prompt-segments>
                  <audiofile text="If you don t know your PIN, just press the star key" src="GC1060_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="GC1060_nm2_01">
                <prompt-segments>
                  <audiofile text="If your account PIN is" src="GC1060_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_2500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm2_04">
                <prompt-segments>
                  <audiofile text="press 1 If not, press 2" src="GC1060_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_2500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm2_06">
                <prompt-segments>
                  <audiofile text="If you don t know your PIN, just press the star key" src="GC1060_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say yes or no Your account PIN is" src="GC1060_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm1_04">
                <prompt-segments>
                  <audiofile text="Right?" src="GC1060_nm1_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_2500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm1_06">
                <prompt-segments>
                  <audiofile text="You can also say  I don t know " src="GC1060_nm1_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm2_01">
                <prompt-segments>
                  <audiofile text="If your account PIN is" src="GC1060_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_2500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm2_04">
                <prompt-segments>
                  <audiofile text="press 1 If not, press 2" src="GC1060_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_2500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm2_06">
                <prompt-segments>
                  <audiofile text="If you don t know your PIN, just press the star key" src="GC1060_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm2_01">
                <prompt-segments>
                  <audiofile text="If your account PIN is" src="GC1060_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_2500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm2_04">
                <prompt-segments>
                  <audiofile text="press 1 If not, press 2" src="GC1060_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_2500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_nm2_06">
                <prompt-segments>
                  <audiofile text="If you don t know your PIN, just press the star key" src="GC1060_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="GC1060_ini_01">
                <prompt-segments>
                  <audiofile text="I just want to make sure Your account PIN is" src="GC1060_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1060_ini_04">
                <prompt-segments>
                  <audiofile text="Is that right?" src="GC1060_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="GC1060_ReconfirmCodeYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="GC1060_ReconfirmCodeYN_DM_dtmf.grxml" count="1"/>
          <grammars filename="GlobalCommands_DontKnow.grxml" count="2"/>
          <dtmfgrammars filename="GlobalCommands_GC1060_dtmf.grxml" count="2"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms">
        </vxml_properties>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="GC1070_PostRecoPrompt_PP">
      <audio>
        <prompt id="GC1070_out_02">
          <prompt-segments>
            <audiofile text="Sorry, my mistake" src="GC1070_out_02.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="GC1000_GetSecurityCode_DM"/>
    </play-state>

    <subdialog-state id="GC1085_GoToAccountPinReset_SD">
      <gotodialog next="AccountPINReset_Main_Dialog"/>
      <action next="GC1085_GoToAccountPinReset_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="GC1085_GoToAccountPinReset_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="GC1090_GoToTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="GC1090_GoToTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="GC1090_GoToTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <dm-state id="GC1045_AskResetInformation_DM" type="YSNO">
      <success>
        <action label="true">
          <action next="GC1085_GoToAccountPinReset_SD"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.lastPinTry" value="true" type="Boolean"/>
          <action next="GC1000_GetSecurityCode_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="GC1045_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, that still didn't match Would you like information on how to reset your pin online?" src="GC1045_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="GC1045_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, that still didn't match Would you like information on how to reset your pin online?" src="GC1045_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="GC1045_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes Otherwise say No" src="GC1045_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1045_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1 If you'd like to try your pin once more say 'no' or press 2" src="GC1045_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1045_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1 If you'd like to try your pin once more say 'no' or press 2" src="GC1045_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1045_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes Otherwise say No" src="GC1045_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1045_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1 If you'd like to try your pin once more say 'no' or press 2" src="GC1045_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="GC1045_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1 If you'd like to try your pin once more say 'no' or press 2" src="GC1045_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="GC1045_AskResetInformation_DM.grxml" count="1"/>
          <dtmfgrammars filename="GC1045_AskResetInformation_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  