<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="DataUsage_Main_Dialog">
    <decision-state id="DU1001_CheckContext_DS">
      <session-mapping key="dataUsage_dataFound" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataFound:false" type="String"/>
      <if cond="dataUsage_dataFound == true">
        <session-mapping key="getTopUpHistory" value="true" type="Boolean"/>
        <session-mapping key="offerDataMaximizer" value="false" type="Boolean"/>
        <session-mapping key="willExceedDataLimit" value="false" type="Boolean"/>
        <action next="DU1006_CalculateDataUsage_DB_DA"/>
        <else>
          <session-mapping key="getTopUpHistory" value="true" type="Boolean"/>
          <session-mapping key="offerDataMaximizer" value="false" type="Boolean"/>
          <session-mapping key="willExceedDataLimit" value="false" type="Boolean"/>
          <action next="DU1005_GetDataUsage_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="DU1005_GetDataUsage_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.mdn:''" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.payDate:''" type="String"/>
      <session-mapping key="billCycleLength" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.billCycleLength:0" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="isBARTET" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.isBARTET:false" type="String"/>
      <session-mapping key="isHint" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.parentDeviceType == 'INT')? true : false" type="String"/>
      <session-mapping key="isSuspended" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" type="String"/>
      <session-mapping key="getTopUpHistory" value="isHint == true ? false : true" type="String"/>
      <data-access id="GetDataUsage" classname="com.nuance.metro.dataaccess.GetDataUsage">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="getTopUpHistory"/>
          <input-variable name="payDate"/>
          <input-variable name="sessionId"/>
          <input-variable name="isSuspended"/>
          <input-variable name="isBARTET"/>
        </inputs>
        <outputs>
          <output-variable name="dataFound"/>
          <output-variable name="dataUsed"/>
          <output-variable name="dataCap"/>
          <output-variable name="isUnlimited"/>
          <output-variable name="dataPercentage"/>
          <output-variable name="dataUsage_dataFound"/>
          <output-variable name="dataUsage_dataUsed"/>
          <output-variable name="dataUsage_dataCap"/>
          <output-variable name="dataUsage_dataUsed_KB"/>
          <output-variable name="dataUsage_dataCap_KB"/>
          <output-variable name="dataUsage_isUnlimited"/>
          <output-variable name="dataUsage_dataPercentage"/>
          <output-variable name="topUpHotSpot_dataFound"/>
          <output-variable name="topUpHotSpot_dataUsed"/>
          <output-variable name="topUpHotSpot_dataCap"/>
          <output-variable name="topUpHotSpot_dataUsed_KB"/>
          <output-variable name="topUpHotSpot_dataCap_KB"/>
          <output-variable name="topUpHotSpot_isUnlimited"/>
          <output-variable name="topUpHotSpot_dataPercentage"/>
          <output-variable name="topUp_feature1_code"/>
          <output-variable name="topUp_feature1_featureName_en"/>
          <output-variable name="topUp_feature1_featureName_es"/>
          <output-variable name="topUp_feature1_description_en"/>
          <output-variable name="topUp_feature1_description_es"/>
          <output-variable name="topUp_feature1_price"/>
          <output-variable name="numPaidTopUps"/>
          <output-variable name="numGoodWillTopUps "/>
          <output-variable name="featureDetailsPromptURL_en"/>
          <output-variable name="featureDetailsPromptURL_es "/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetDataUsage.status == 'Success'">
          <session-mapping key="GlobalVars.GetDataUsageInfo" expr="GetDataUsage"/>
          <session-mapping key="numPaidTopUps" expr="GetDataUsage.numPaidTopUps"/>
          <session-mapping key="numGoodWillTopUps" expr="GetDataUsage.numGoodWillTopUps"/>
          <session-mapping key="topUpHotSpot_dataFound" expr="GetDataUsage.topUpHotSpot_dataFound"/>
          <action next="DU1006_CalculateDataUsage_DB_DA"/>
        </if>
        <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
        <gotodialog next="CallTransfer_Main_Dialog"/>
      </action>
      </data-access-state>

    <data-access-state id="DU1006_CalculateDataUsage_DB_DA">
      <session-mapping key="dataUsage_dataCap_KB" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataCap_KB:0" type="String"/>
      <session-mapping key="topUpHotSpot_dataCap_KB" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataCap_KB:0" type="String"/>
      <session-mapping key="dataUsage_dataUsed_KB" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataUsed_KB:0" type="String"/>
      <session-mapping key="topUpHotSpot_dataUsed_KB" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataUsed_KB:0" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.payDate:''" type="String"/>
      <session-mapping key="billCycleLength" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.billCycleLength:0" type="String"/>
      <session-mapping key="topUpHotSpot_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataPercentage:0" type="String"/>
      <session-mapping key="dataUsage_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataPercentage:0" type="String"/>
      <session-mapping key="dataUsage_isUnlimited" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_isUnlimited:false" type="String"/>
      <data-access classname="com.nuance.metro.dataaccess.CalculateDataUsage" id="CalculateDataUsage">
        <inputs>
          <input-variable mask="false" name="dataUsage_dataCap_KB"/>
          <input-variable mask="false" name="topUpHotSpot_dataCap_KB"/>
          <input-variable mask="false" name="dataUsage_dataUsed_KB"/>
          <input-variable mask="false" name="topUpHotSpot_dataUsed_KB"/>
        </inputs>
        <outputs>
          <output-variable mask="false" name="calculateDataUsage_dataLeftMegabytes"/>
          <output-variable mask="false" name="calculateDataUsage_dataLeftGigabytes"/>
          <output-variable mask="false" name="calculateDataUsage_hotspotDataLeftMegabytes"/>
          <output-variable mask="false" name="calculateDataUsage_hotspotDataLeftGigabytes"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.CalculateDataUsage" expr="CalculateDataUsage"/>
        <session-mapping key="GlobalVars.calculateDataUsage_dataLeftMegabytes" expr="CalculateDataUsage.calculateDataUsage_dataLeftMegabytes"/>
        <session-mapping key="GlobalVars.calculateDataUsage_dataLeftGigabytes" expr="CalculateDataUsage.calculateDataUsage_dataLeftGigabytes"/>
        <session-mapping key="GlobalVars.calculateDataUsage_hotspotDataLeftMegabytes" expr="CalculateDataUsage.calculateDataUsage_hotspotDataLeftMegabytes"/>
        <session-mapping key="GlobalVars.calculateDataUsage_hotspotDataLeftGigabytes" expr="CalculateDataUsage.calculateDataUsage_hotspotDataLeftGigabytes"/>
        <session-mapping key="willExceedDataLimit" expr="CalculateDataUsage.calculateDataUsage_willExceedDataLimit"/>
        <action next="DU1020_CheckPlanType_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="DU1020_CheckPlanType_DS">
      <if cond="GlobalVars.tag == 'home-internet_active'">
        <if cond="(GlobalVars.GetDataUsageInfo.dataUsage_isUnlimited == true)">
          <action next="DU1207_InternetUnlimitedPlayCurrentPlan_PP"/>
          <else>
            <action next="DU1107_InternetCappedPlayCurrentPlan_PP"/>
          </else>
        </if>
        <else>
          <if cond="(GlobalVars.GetDataUsageInfo.dataUsage_isUnlimited == true)">
            <action next="DU1205_UnlimitedPlayCurrentPlan_PP"/>
            <else>
              <action next="DU1105_CappedPlayCurrentPlan_PP"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="DU1105_CappedPlayCurrentPlan_PP">
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.payDate:''" type="String"/>
      <session-mapping key="dataUsage_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataPercentage:0" type="String"/>
      <session-mapping key="dataUsage_dataCap" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataCap:0" type="String"/>
      <session-mapping key="billCycleLength" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.billCycleLength:0" type="String"/>
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.metro.audio.custom.CappedPlayCurrentPlanRenderer"/>
        </prompt>
      </audio>
      <action next="DU1110_CappedCheckHaveTopUps_JDA"/>
    </play-state>

    <play-state id="DU1107_InternetCappedPlayCurrentPlan_PP">
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.payDate:''" type="String"/>
      <session-mapping key="dataUsage_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataPercentage:0" type="String"/>
      <session-mapping key="dataUsage_dataCap" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataCap:0" type="String"/>
      <session-mapping key="billCycleLength" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.billCycleLength:0" type="String"/>
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.metro.audio.custom.InternetCappedPlayCurrentPlanRenderer"/>
        </prompt>
      </audio>
      <action next="DU1125_CappedRepeatYN_DM"/>
    </play-state>

    <decision-state id="DU1110_CappedCheckHaveTopUps_DS">
      <if cond="((topUpHotSpot_dataFound == true) &amp;&amp; (numPaidTopUps !=0 || numGoodWillTopUps !=0))">
        <action next="DU1115_CappedPlayTopUpUsage_PP"/>
        <else>
          <action next="DU1120_CappedUpsellDmaxInfo_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="DU1115_CappedPlayTopUpUsage_PP">
      <session-mapping key="topUpHotSpot_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataPercentage:0" type="String"/>
      <session-mapping key="topUpHotSpot_dataCap" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataCap:0" type="String"/>
      <session-mapping key="dataUsedPercentMedialXX" value="" type="String"/>
      <audio>
        <prompt id="DU1115_out_02">
          <prompt-segments>
            <audiofile text="Also " src="DU1115_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="(topUpHotSpot_dataPercentage == 0)">
          <prompt id="DU1115_out_03">
            <prompt-segments>
              <audiofile text="You haven't used *any* " src="DU1115_out_03.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="(topUpHotSpot_dataPercentage &gt;= 100)">
            <prompt id="DU1115_out_04">
              <prompt-segments>
                <audiofile text="You've used up *all* " src="DU1115_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt type="custom" expr="dataUsedPercentMedialXX" scope="request">
              <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.VXMLDynamicPromptNameRendererImpl"/>
              <param name="tts" value="dataUsedPercentMedialXX" scope="request"/>
            </prompt>
          </else>
        </if>
        <if cond="(topUpHotSpot_dataCap == 1)">
          <prompt id="DU1115_out_05">
            <prompt-segments>
              <audiofile text="of your one gigabytes of top-up data " src="DU1115_out_05.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="(topUpHotSpot_dataCap == 2)">
            <prompt id="DU1115_out_06">
              <prompt-segments>
                <audiofile text="of your two gigabytes of top-up data " src="DU1115_out_06.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="(topUpHotSpot_dataCap == 3)">
            <prompt id="DU1115_out_07">
              <prompt-segments>
                <audiofile text="of your three gigabytes of top-up data " src="DU1115_out_07.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="DU1115_out_08">
              <prompt-segments>
                <audiofile text="of your " src="DU1115_out_08.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="natural" expr="topUpHotSpot_dataCap" scope="request">
              <param name="intonation" value="m"/>
              <param name="playZeroUnits" value="true"/>
            </prompt>
            <prompt id="DU1115_out_09">
              <prompt-segments>
                <audiofile text="gigabytes of top-up data " src="DU1115_out_09.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="DU1120_CappedUpsellDmaxInfo_PP"/>
    </play-state>

    <play-state id="DU1120_CappedUpsellDmaxInfo_PP">
      <audio>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="(numPaidTopUps !=0 || numGoodWillTopUps !=0 || topupEligibility=='NOT-ELIGIBLE')">
          <prompt id="DU1120_out_03">
            <prompt-segments>
              <audiofile text="You can also upgrade your plan for even more included data to keep you at top speed " src="DU1120_out_03.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="DU1120_out_04">
              <prompt-segments>
                <audiofile text="You can always update your plan or get a Data Top Up to keep you at top speed" src="DU1120_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="DU1125_CappedRepeatYN_DM"/>
    </play-state>

    <dm-state id="DU1125_CappedRepeatYN_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="DU1125_out_01">
              <prompt-segments>
                <audiofile text="Sure! " src="DU1125_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="GlobalVars.tag == 'home-internet_active'">
            <action next="DU1107_InternetCappedPlayCurrentPlan_PP"/>
            <else>
              <action next="DU1105_CappedPlayCurrentPlan_PP"/>
            </else>
          </if>
        </action>
        <action label="false">
          <audio>
            <prompt id="DU1125_out_02">
              <prompt-segments>
                <audiofile text="Alright " src="DU1125_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="GlobalVars.tag == 'home-internet_active'">
            <action next="DU1315_InternetUsageWrapMenu_DM"/>
            <else>
              <action next="DU1301_CheckContext_JDA"/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DU1125_ini_01">
                <prompt-segments>
                  <audiofile text="Before we continue, would you like to hear that again? " src="DU1125_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="DU1125_CappedRepeatYN_DM_reinvoke"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DU1125_ini_01">
                <prompt-segments>
                  <audiofile text="Before we continue, would you like to hear that again? " src="DU1125_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DU1125_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to hear your data usage again before we continue? " src="DU1125_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1125_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1125_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1125_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1125_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1125_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to hear your data usage again before we continue? " src="DU1125_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1125_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1125_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1125_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1125_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DU1125_ini_01">
                <prompt-segments>
                  <audiofile text="Before we continue, would you like to hear that again? " src="DU1125_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="DU1125_CappedRepeatYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="DU1125_CappedRepeatYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="DU1205_UnlimitedPlayCurrentPlan_PP">
      <session-mapping key="dataUsage_dataUsed" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataUsed:0" type="String"/>
      <audio>
        <prompt id="DU1205_out_02">
          <prompt-segments>
            <audiofile text="Your monthly plan comes with unlimited high-speed mobile data " src="DU1205_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DU1205_out_03">
          <prompt-segments>
            <audiofile text="As of now, you've used around " src="DU1205_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="natural" expr="dataUsage_dataUsed" scope="request">
          <param name="intonation" value="m"/>
          <param name="playZeroUnits" value="true"/>
        </prompt>
        <prompt id="DU1205_out_04">
          <prompt-segments>
            <audiofile text="gigabytes " src="DU1205_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DU1205_out_01">
          <prompt-segments>
            <audiofile text="Note that if you use over 35 gigabytes, you may see slower connection speeds And video streams at up to 480 pixels" src="DU1205_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="(topUpHotSpot_dataFound == true)">
        <action next="DU1210_UnlimitedPlayHotSpotUsage_PP"/>
        <else>
          <action next="DU1215_UnlimitedUpsellDmaxInfo_PP"/>
        </else>
      </if>
    </play-state>

    <play-state id="DU1207_InternetUnlimitedPlayCurrentPlan_PP">
      <session-mapping key="dataUsage_dataUsed" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataUsed:0" type="String"/>
      <audio>
        <prompt id="DU1207_out_02">
          <prompt-segments>
            <audiofile text="Your monthly plan comes with unlimited internet" src="DU1207_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DU1207_out_03">
          <prompt-segments>
            <audiofile text="As of now, you've used around " src="DU1207_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="natural" expr="dataUsage_dataUsed" scope="request">
          <param name="intonation" value="m"/>
          <param name="playZeroUnits" value="true"/>
        </prompt>
        <prompt id="DU1207_out_04">
          <prompt-segments>
            <audiofile text="gigabytes" src="DU1207_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="DU1220_UnlimitedRepeatYN_DM"/>
    </play-state>

    <play-state id="DU1210_UnlimitedPlayHotSpotUsage_PP">
      <session-mapping key="topUpHotSpot_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataPercentage:0" type="String"/>
      <session-mapping key="topUpHotSpot_dataCap" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataCap:0" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.payDate:''" type="String"/>
      <session-mapping key="billCycleLength" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.billCycleLength:0" type="String"/>
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.metro.audio.custom.UnlimitedPlayHotSpotUsage"/>
        </prompt>
      </audio>
      <action next="DU1215_UnlimitedUpsellDmaxInfo_PP"/>
    </play-state>

    <play-state id="DU1215_UnlimitedUpsellDmaxInfo_PP">
      <session-mapping key="topUpHotSpot_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataPercentage:0" type="String"/>
      <audio>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DU1215_out_03" cond="(topUpHotSpot_dataPercentage != 0 &amp;&amp; topupEligibility =='ELIGIBLE' &amp;&amp; numPaidTopUps == 0 &amp;&amp; numGoodWillTopUps == 0)">
          <prompt-segments>
            <audiofile text="You can always get a Data Top Up to keep you at top speed " src="DU1215_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DU1215_out_04" cond="(topUpHotSpot_dataPercentage != 0 &amp;&amp; topupEligibility =='ELIGIBLE' &amp;&amp; !(numPaidTopUps == 0 &amp;&amp; numGoodWillTopUps == 0))">
          <prompt-segments>
            <audiofile text="You can always get another Top-Up to keep you at top speed " src="DU1215_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="DU1220_UnlimitedRepeatYN_DM"/>
    </play-state>

    <dm-state id="DU1220_UnlimitedRepeatYN_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="DU1220_out_01">
              <prompt-segments>
                <audiofile text="Sure!  " src="DU1220_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="GlobalVars.tag == 'home-internet_active'">
            <action next="DU1207_InternetUnlimitedPlayCurrentPlan_PP"/>
            <else>
              <action next="DU1205_UnlimitedPlayCurrentPlan_PP"/>
            </else>
          </if>
        </action>
        <action label="false">
          <audio>
            <prompt id="DU1220_out_02">
              <prompt-segments>
                <audiofile text="Alright  " src="DU1220_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="GlobalVars.tag == 'home-internet_active'">
            <action next="DU1315_InternetUsageWrapMenu_DM"/>
            <else>
              <action next="DU1301_CheckContext_JDA"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DU1220_ini_01">
                <prompt-segments>
                  <audiofile text="Before we continue, would you like to hear that again? " src="DU1220_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="DU1220_UnlimitedRepeatYN_DM_reinvoke"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DU1220_ini_01">
                <prompt-segments>
                  <audiofile text="Before we continue, would you like to hear that again? " src="DU1220_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DU1220_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to hear your data usage again before we continue?  " src="DU1220_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1220_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1220_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1220_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1220_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1220_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to hear your data usage again before we continue?  " src="DU1220_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1220_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1220_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1220_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear your usage information again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1220_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DU1220_ini_01">
                <prompt-segments>
                  <audiofile text="Before we continue, would you like to hear that again? " src="DU1220_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="DU1220_UnlimitedRepeatYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="DU1220_UnlimitedRepeatYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="DU1301_CheckContext_DS">
      <if cond="GlobalVars.tag == 'home-internet_active'">
        <action next="DU1315_InternetUsageWrapMenu_DM"/>
        <else>
          <action next="DU1305_DataUsageWrapMenu_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="DU1305_DataUsageWrapMenu_DM" type="CUST">
      <success>
        <action label="add-data">
          <session-mapping key="GlobalVars.callType" expr="'add-data'"/>
          <session-mapping key="GlobalVars.tag" expr="'buy-data_topup'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="DU1310_AddData_SD"/>
        </action>
        <action label="datatips">
          <action next="DU1505_DataTipsInfo_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DU1305_ini_01">
                <prompt-segments>
                  <audiofile text="What would you like to do next? Say 'add more data', or 'data tips'" src="DU1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1305_ini_06">
                <prompt-segments>
                  <audiofile text="If you're done, you can simply hang up " src="DU1305_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="DU1305_DataUsageWrapMenu_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="DU1305_DataUsageWrapMenu_DM_initial"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DU1305_ini_01">
                <prompt-segments>
                  <audiofile text="What would you like to do next? Say 'add more data', or 'data tips'" src="DU1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1305_ini_06">
                <prompt-segments>
                  <audiofile text="If you're done, you can simply hang up " src="DU1305_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DU1305_ini_01">
                <prompt-segments>
                  <audiofile text="What would you like to do next? Say 'add more data', or 'data tips'" src="DU1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1305_ini_06">
                <prompt-segments>
                  <audiofile text="If you're done, you can simply hang up " src="DU1305_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1305_nm2_06">
                <prompt-segments>
                  <audiofile text="Say 'add more data' or press 1, or 'data tips' or press 2 " src="DU1305_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1305_nm2_06">
                <prompt-segments>
                  <audiofile text="Say 'add more data' or press 1, or 'data tips' or press 2 " src="DU1305_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1305_ini_01">
                <prompt-segments>
                  <audiofile text="What would you like to do next? Say 'add more data', or 'data tips'" src="DU1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1305_ini_06">
                <prompt-segments>
                  <audiofile text="If you're done, you can simply hang up " src="DU1305_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1305_nm2_06">
                <prompt-segments>
                  <audiofile text="Say 'add more data' or press 1, or 'data tips' or press 2 " src="DU1305_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1305_nm2_06">
                <prompt-segments>
                  <audiofile text="Say 'add more data' or press 1, or 'data tips' or press 2 " src="DU1305_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DU1305_ini_01">
                <prompt-segments>
                  <audiofile text="What would you like to do next? Say 'add more data', or 'data tips'" src="DU1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1305_ini_06">
                <prompt-segments>
                  <audiofile text="If you're done, you can simply hang up " src="DU1305_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="DU1305_DataUsageWrapMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="DU1305_DataUsageWrapMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="DU1305_DataUsageWrapMenu_DM_confirmation_initial"/>
          <repeatprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="DU1310_AddData_SD">
      <gotodialog next="HighSpeedDataAvailable_Main_Dialog"/>
      <action next="DU1310_AddData_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DU1310_AddData_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <play-state id="DU1505_DataTipsInfo_PP">
      <session-mapping key="dataUsage_isUnlimited" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_isUnlimited:false" type="String"/>
      <session-mapping key="topUpHotSpot_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataPercentage:0" type="String"/>
      <session-mapping key="dataUsage_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataPercentage:0" type="String"/>
      <audio>
        <prompt id="DU1505_out_01">
          <prompt-segments>
            <audiofile text="To make your data last longer, first make sure you're connected to a Wifi network whenever you can When you're on Wifi, you're not using your mobile data " src="DU1505_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DU1505_out_08">
          <prompt-segments>
            <audiofile text="You ll still have a data connection when you use up your data bucket but the connection will be slower" src="DU1505_out_08.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="(dataUsage_isUnlimited == true &amp;&amp; topUpHotSpot_dataPercentage &gt;= 100 &amp;&amp; topupEligibility=='ELIGIBLE')">
          <prompt id="DU1505_out_03">
            <prompt-segments>
              <audiofile text="Since you're out of high-speed Hotspot data, you can also buy a data top-up " src="DU1505_out_03.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="(dataUsage_isUnlimited != true &amp;&amp; dataUsage_dataPercentage &gt;= 100)">
            <prompt id="DU1505_out_04" cond="topupEligibility == 'ELIGIBLE'">
              <prompt-segments>
                <audiofile text="Since you're out of high-speed data, you can also buy a data top-up, or switch to a plan with more included data " src="DU1505_out_04.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="DU1505_out_09" cond="topupEligibility != 'ELIGIBLE'">
              <prompt-segments>
                <audiofile text="Since you're out of high-speed data, you can also switch to a plan with more included data" src="DU1505_out_09.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="(willExceedDataLimit == true)">
            <prompt id="DU1505_out_05" cond="(dataUsage_isUnlimited == true &amp;&amp; topupEligibility == 'ELIGIBLE')">
              <prompt-segments>
                <audiofile text="Since you might run out of High Speed Hotspot data this month, you can also buy a data top-up" src="DU1505_out_05.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="DU1505_out_06" cond="(!(dataUsage_isUnlimited == true &amp;&amp; topupEligibility == 'ELIGIBLE')) &amp;&amp; (topupEligibility == 'ELIGIBLE')">
              <prompt-segments>
                <audiofile text="Since you might run out of High Speed data this month, you can also buy a data top-up, or switch to a plan with more included data" src="DU1505_out_06.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="DU1505_out_10" cond="(!(dataUsage_isUnlimited == true &amp;&amp; topupEligibility == 'ELIGIBLE')) &amp;&amp; (topupEligibility != 'ELIGIBLE')">
              <prompt-segments>
                <audiofile text="Since you might run out of High Speed data this month, you can also switch to a plan with more included data" src="DU1505_out_10.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="topupEligibility == 'ELIGIBLE'">
            <prompt id="DU1505_out_07">
              <prompt-segments>
                <audiofile text="If you think you'll run out of High Speed data this month, you can also buy a data top-up, or switch to a plan with more included data" src="DU1505_out_07.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="DU1505_out_11">
              <prompt-segments>
                <audiofile text="If you think you'll run out of High Speed data this month, you can also switch to a plan with more included data" src="DU1505_out_11.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="DU1510_DataTipsRepeatYN_DM"/>
    </play-state>

    <dm-state id="DU1510_DataTipsRepeatYN_DM" type="YSNO">
      <success>
        <action label="true" next="DU1505_DataTipsInfo_PP">
          <audio>
            <prompt id="DU1510_out_01">
              <prompt-segments>
                <audiofile text="Sure! " src="DU1510_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="false" next="DU1305_DataUsageWrapMenu_DM">
          <audio>
            <prompt id="DU1510_out_02">
              <prompt-segments>
                <audiofile text="Alright " src="DU1510_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DU1510_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? " src="DU1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DU1510_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? " src="DU1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DU1510_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? " src="DU1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1510_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to hear the data tips again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1510_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1510_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to hear the data tips again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1510_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1510_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? " src="DU1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1510_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to hear the data tips again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1510_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1510_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to hear the data tips again, say 'yes' or press 1 Otherwise, say 'no' or press 2, and we'll continue " src="DU1510_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DU1510_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? " src="DU1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="DU1510_DataTipsRepeatYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="DU1510_DataTipsRepeatYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="DU1315_InternetUsageWrapMenu_DM" type="CUST">
      <success>
        <action label="main_menu">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.tag" expr="undefined"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="hear-plan_details">
          <session-mapping key="GlobalVars.callType" expr="'plan_details'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.ratePlanAction" expr="undefined"/>
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <action next="DU1318_GoTo_RatePlan_SD"/>
        </action>
        <action label="operator"/>
        <action label="repeat">
          <if cond="(GlobalVars.GetDataUsageInfo.dataUsage_isUnlimited == true)">
            <action next="DU1207_InternetUnlimitedPlayCurrentPlan_PP"/>
            <else>
              <action next="DU1107_InternetCappedPlayCurrentPlan_PP"/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DU1315_ini_01">
                <prompt-segments>
                  <audiofile text="Ok, you can say 'plan information,  or main menu" src="DU1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="DU1315_InternetUsageWrapMenu_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="DU1315_InternetUsageWrapMenu_DM_initial"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DU1315_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say plan information, or main menu" src="DU1315_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1315_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say ''plan information, or press 1, 'main menu' or press 2" src="DU1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1315_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say ''plan information, or press 1, 'main menu' or press 2" src="DU1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1315_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say plan information, or main menu" src="DU1315_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1315_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say ''plan information, or press 1, 'main menu' or press 2" src="DU1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DU1315_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say ''plan information, or press 1, 'main menu' or press 2" src="DU1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DU1315_ini_01">
                <prompt-segments>
                  <audiofile text="Ok, you can say 'plan information,  or main menu" src="DU1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="DU1315_InternetUsageWrapMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="DU1315_InternetUsageWrapMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.506" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="DU1315_InternetUsageWrapMenu_DM_confirmation_initial"/>
          <repeatprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="DU1318_GoTo_RatePlan_SD">
      <gotodialog next="RatePlan_Main_Dialog"/>
      <action next="DU1318_GoTo_RatePlan_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DU1318_GoTo_RatePlan_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="DU1319_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="DU1319_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DU1319_GoToCallTransfer_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  