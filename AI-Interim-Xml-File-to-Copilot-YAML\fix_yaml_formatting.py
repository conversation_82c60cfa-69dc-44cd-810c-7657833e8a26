#!/usr/bin/env python3
"""
Script to fix the YAML indentation issues and reprocess all failed files
"""

import os
import sys
from pathlib import Path

def fixed_topic_yaml_indentation(input_file, output_file):
    """
    Properly indent YAML files by moving content after actions: null under the actions key
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as infile:
            lines = infile.readlines()
        
        if len(lines) == 0:
            # Handle empty files - just copy empty content
            with open(output_file, 'w', encoding='utf-8') as outfile:
                outfile.write('')
            return False  # Indicate this was an empty file
        
        # Find the line with "actions: null" and replace it with "actions: "
        actions_line_index = -1
        for i, line in enumerate(lines):
            if 'actions: null' in line:
                lines[i] = line.replace('actions: null', 'actions: ')
                actions_line_index = i
                break
        
        # If no "actions: null" found, write file as-is
        if actions_line_index == -1:
            with open(output_file, 'w', encoding='utf-8') as outfile:
                outfile.writelines(lines)
            return True  # File was processed but no actions found
        
        # Calculate the base indentation for the actions content
        actions_line = lines[actions_line_index]
        base_indent = len(actions_line) - len(actions_line.lstrip())
        new_indent = ' ' * (base_indent + 2)
        
        # Write the output file
        with open(output_file, 'w', encoding='utf-8') as outfile:
            # Write everything up to and including the actions line
            for i in range(actions_line_index + 1):
                outfile.write(lines[i])
            
            # Write the remaining lines with proper indentation under actions
            for i in range(actions_line_index + 1, len(lines)):
                line = lines[i]
                if line.strip():  # Only process non-empty lines
                    content = line.lstrip()
                    if content.startswith('- '):
                        # This is a list item, use the calculated indentation
                        outfile.write(new_indent + content)
                    else:
                        # This is a continuation, add 2 more spaces
                        outfile.write(new_indent + '  ' + content)
                else:
                    # Empty line, write as-is
                    outfile.write(line)
        
        return True  # Successfully processed
                    
    except Exception as e:
        print(f"Error processing {input_file}: {e}")
        # If there's an error, try to copy the input to output
        try:
            with open(input_file, 'r', encoding='utf-8') as infile:
                content = infile.read()
            with open(output_file, 'w', encoding='utf-8') as outfile:
                outfile.write(content)
        except:
            # If even copying fails, create an empty file
            with open(output_file, 'w', encoding='utf-8') as outfile:
                outfile.write('')
        return False

def remove_null_from_action(fileName):
    """Replace null with empty for key:ACTION"""
    try:
        with open(fileName, 'r', encoding='utf-8') as yml_file:
            yml_content = yml_file.read()
        
        # Replace 'actions' key with an empty string
        updated_content = yml_content.replace('actions: null', 'actions: ')
        
        with open(fileName, 'w', encoding='utf-8') as yml_file:
            yml_file.write(updated_content)
        
        return True
    except Exception as e:
        print(f"Error in remove_null_from_action for {fileName}: {e}")
        return False

def fix_all_formatting_issues():
    """
    Process all unformatted YAML files and create properly formatted versions
    """
    unformatted_dir = Path("output/unformatted_topic_yaml")
    formatted_dir = Path("output/topic_yaml")
    
    # Ensure output directory exists
    formatted_dir.mkdir(parents=True, exist_ok=True)
    
    # Get all .yml files from unformatted directory
    unformatted_files = list(unformatted_dir.glob("*.yml"))
    
    print(f"Found {len(unformatted_files)} unformatted YAML files")
    
    successful_count = 0
    empty_file_count = 0
    error_count = 0
    
    for unformatted_file in unformatted_files:
        # Generate output filename
        base_name = unformatted_file.stem
        if not base_name.endswith('_topic'):
            formatted_filename = f"{base_name}_topic.yml"
        else:
            formatted_filename = f"{base_name}.yml"
        
        formatted_file = formatted_dir / formatted_filename
        
        print(f"Processing: {unformatted_file.name} -> {formatted_filename}")
        
        # Check if input file is empty
        if unformatted_file.stat().st_size == 0:
            print(f"  SKIP: {unformatted_file.name} is empty")
            empty_file_count += 1
            continue
        
        try:
            # Apply fixed indentation
            success = fixed_topic_yaml_indentation(str(unformatted_file), str(formatted_file))
            
            if success:
                # Apply null removal
                remove_null_from_action(str(formatted_file))
                print(f"  SUCCESS: {formatted_filename}")
                successful_count += 1
            else:
                print(f"  ERROR: Failed to process {unformatted_file.name}")
                error_count += 1
                
        except Exception as e:
            print(f"  ERROR: Exception processing {unformatted_file.name}: {e}")
            error_count += 1
    
    print(f"\n=== PROCESSING COMPLETE ===")
    print(f"Total files found: {len(unformatted_files)}")
    print(f"Successfully formatted: {successful_count}")
    print(f"Empty files skipped: {empty_file_count}")
    print(f"Errors: {error_count}")
    print(f"Final formatted files: {successful_count}")
    
    # List the final files
    final_files = list(formatted_dir.glob("*.yml"))
    print(f"\nFinal topic_yaml directory contains {len(final_files)} files:")
    for f in sorted(final_files):
        size = f.stat().st_size
        print(f"  {f.name} ({size} bytes)")

if __name__ == "__main__":
    print("Starting YAML formatting fix process...")
    fix_all_formatting_issues()
    print("Process completed!")
