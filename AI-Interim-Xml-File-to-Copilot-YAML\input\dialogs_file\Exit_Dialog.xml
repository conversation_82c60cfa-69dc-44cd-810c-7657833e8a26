<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Exit_Dialog">
    <decision-state id="CheckIfBillingData_DS">
      <if cond="GlobalVars.callType == 'switch_phone' &amp;&amp; ActivationTable.ACTIVATION_STATUS == '130'">
        <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'132'"/>
      </if>
      <if cond="ActivationTable.ACTIVATION_STATUS !='' ">
        <action next="BillingData_DB_DA"/>
        <elseif cond="PaymentTable.ACTIVATION_STATUS !='' ">
          <action next="BillingData2_DB_DA"/>
        </elseif>
        <elseif cond="(haveMDN == true) &amp;&amp; (GlobalVars.IOMGetPromotion != null)">
          <action next="EndCall_DB_DA"/>
        </elseif>
        <else>
          <action next="EndApplication_CS"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="BillingData_DB_DA">
      <session-mapping key="sessionId" value="ActivationTable.SESSION_ID" type="String"/>
      <session-mapping key="ACCOUNT_NUM" value="ActivationTable.ACCOUNT_NUM" type="String"/>
      <session-mapping key="ACTIVATION_TYPE" value="ActivationTable.ACTIVATION_TYPE" type="String"/>
      <session-mapping key="ACTIVATION_MODE" value="ActivationTable.ACTIVATION_MODE" type="String"/>
      <session-mapping key="ACTIVATION_STARTED" value="ActivationTable.ACTIVATION_STARTED" type="String"/>
      <session-mapping key="ACTIVATION_STATUS" value="ActivationTable.ACTIVATION_STATUS" type="String"/>
      <session-mapping key="ESN" value="ActivationTable.ESN" type="String"/>
      <session-mapping key="ANI" value="ActivationTable.ANI" type="String"/>
      <session-mapping key="DID" value="ActivationTable.DID" type="String"/>
      <session-mapping key="MDN" value="ActivationTable.MDN" type="String"/>
      <session-mapping key="RATE_PLAN" value="GlobalVars.selectedPlan" type="String"/>
      <session-mapping key="TRF_SWITCH_ON" value="ActivationTable.TRF_SWITCH_ON" type="String"/>
      <session-mapping key="CITY" value="ActivationTable.CITY" type="String"/>
      <session-mapping key="STATE" value="ActivationTable.STATE" type="String"/>
      <session-mapping key="ZIP" value="ActivationTable.ZIP" type="String"/>
      <session-mapping key="BILLABLE_DATE" value="ActivationTable.BILLABLE_DATE" type="String"/>
      <session-mapping key="OLD_PHONE_TYPE" value="ActivationTable.OLD_PHONE_TYPE" type="String"/>
      <session-mapping key="OLD_MOBILE_NUM" value="GlobalVars.oldNum" type="String"/>
      <session-mapping key="ERROR_TEXT" value="ActivationTable.ERROR_TEXT" type="String"/>
      <session-mapping key="NETWORK_TYPE" value="GlobalVars.networkType" type="String"/>
      <session-mapping key="DEVICE_ID" value="GlobalVars.deviceID" type="String"/>
      <data-access id="InsertActivationBillingInfo" classname="com.nuance.metro.dataaccess.InsertActivationBillingInfo">
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="ACCOUNT_NUM"/>
          <input-variable name="ACTIVATION_TYPE"/>
          <input-variable name="ACTIVATION_MODE"/>
          <input-variable name="ACTIVATION_STARTED"/>
          <input-variable name="ACTIVATION_STATUS"/>
          <input-variable name="ESN"/>
          <input-variable name="ANI"/>
          <input-variable name="DID"/>
          <input-variable name="MDN"/>
          <input-variable name="RATE_PLAN"/>
          <input-variable name="TRF_SWITCH_ON"/>
          <input-variable name="CITY"/>
          <input-variable name="STATE"/>
          <input-variable name="ZIP"/>
          <input-variable name="BILLABLE_DATE"/>
          <input-variable name="OLD_PHONE_TYPE"/>
          <input-variable name="OLD_MOBILE_NUM"/>
          <input-variable name="ERROR_TEXT"/>
          <input-variable name="NETWORK_TYPE"/>
          <input-variable name="DEVICE_ID"/>
          <input-variable name="ACTIVATION_ENDED_GMT"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="message"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="PaymentTable.ACTIVATION_STATUS !=''">
          <action next="BillingData2_DB_DA"/>
        </if>
        <if cond="(GlobalVars.IOMGetPromotion != null) || (GlobalVars.GetAccountDetails != null)">
          <action next="EndCall_DB_DA"/>
          <else>
            <action next="EndApplication_CS"/>
          </else>
        </if>
      </action>
      </data-access-state>

    <data-access-state id="BillingData2_DB_DA">
      <session-mapping key="sessionId" value="PaymentTable.SESSION_ID" type="String"/>
      <session-mapping key="ACCOUNT_NUM" value="ActivationTable.ACCOUNT_NUM" type="String"/>
      <session-mapping key="ACTIVATION_TYPE" value="PaymentTable.ACTIVATION_TYPE" type="String"/>
      <session-mapping key="ACTIVATION_MODE" value="PaymentTable.ACTIVATION_MODE" type="String"/>
      <session-mapping key="ACTIVATION_STATUS" value="PaymentTable.ACTIVATION_STATUS" type="String"/>
      <session-mapping key="CARD_TYPE" value="PaymentTable.CARD_TYPE" type="String"/>
      <session-mapping key="TRANSACTION_ID" value="PaymentTable.TRANSACTION_ID" type="String"/>
      <session-mapping key="TRANSACTION_AMOUNT" value="PaymentTable.TRANSACTION_AMOUNT" type="String"/>
      <session-mapping key="PAYMENT_STARTED_GMT" value="PaymentTable.ACTIVATION_STARTED" type="String"/>
      <session-mapping key="PAYMENT_COMPLETED_GMT" value="PaymentTable.ACTIVATION_ENDED_GMT" type="String"/>
      <session-mapping key="ESN" value="ActivationTable.ESN" type="String"/>
      <session-mapping key="ANI" value="ActivationTable.ANI" type="String"/>
      <session-mapping key="DID" value="ActivationTable.DID" type="String"/>
      <session-mapping key="MDN" value="ActivationTable.MDN" type="String"/>
      <session-mapping key="CITY" value="ActivationTable.CITY" type="String"/>
      <session-mapping key="STATE" value="ActivationTable.STATE" type="String"/>
      <session-mapping key="ZIP" value="ActivationTable.ZIP" type="String"/>
      <session-mapping key="ERROR_TEXT" value="PaymentTable.ERROR_TEXT" type="String"/>
      <data-access id="InsertIVRPaymentsInfo" classname="com.nuance.metro.dataaccess.InsertIVRPaymentsInfo">
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="ACCOUNT_NUM"/>
          <input-variable name="ACTIVATION_TYPE"/>
          <input-variable name="ACTIVATION_MODE"/>
          <input-variable name="ACTIVATION_STATUS"/>
          <input-variable name="CARD_TYPE"/>
          <input-variable name="TRANSACTION_ID"/>
          <input-variable name="TRANSACTION_AMOUNT"/>
          <input-variable name="PAYMENT_STARTED_GMT"/>
          <input-variable name="PAYMENT_COMPLETED_GMT"/>
          <input-variable name="ESN"/>
          <input-variable name="ANI"/>
          <input-variable name="DID"/>
          <input-variable name="MDN"/>
          <input-variable name="CITY"/>
          <input-variable name="STATE"/>
          <input-variable name="ZIP"/>
          <input-variable name="ERROR_TEXT"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="message"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="(GlobalVars.IOMGetPromotion != null) || (GlobalVars.GetAccountDetails != null)">
          <action next="EndCall_DB_DA"/>
          <else>
            <action next="EndApplication_CS"/>
          </else>
        </if>
      </action>
      </data-access-state>

    <data-access-state id="EndCall_DB_DA">
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="min" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <data-access id="CARE_EndCall" classname="com.nuance.metro.dataaccess.CARE_EndCall">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="min"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="message"/>
        </outputs>
      </data-access>
      <action label="default">
        <action next="EndApplication_CS"/>
      </action>
      </data-access-state>

    <custom-state id="EndApplication_CS">
      <if cond="(GlobalVars.GetBCSParameters != null &amp;&amp; GlobalVars.GetBCSParameters != undefined) &amp;&amp;((GlobalVars.GetBCSParameters.ivr_monitoring_enabled != null &amp;&amp; GlobalVars.GetBCSParameters.ivr_monitoring_enabled != undefined)&amp;&amp; (GlobalVars.GetBCSParameters.ivr_monitoring_enabled == 'true' || GlobalVars.GetBCSParameters.ivr_monitoring_enabled == true))">
        <action next="InvokeMessagingService_DB_Pre_CS"/>
        <else>
          <action next="EndApplication2_CS"/>
        </else>
      </if>
    </custom-state>

    <custom-state id="InvokeMessagingService_DB_Pre_CS">
      <if cond="(monitoringFullString == '' || monitoringFullString == undefined || monitoringFullString == 'undefined')">
        <action next="EndApplication2_CS"/>
        <else>
          <action next="InvokeMessagingService_DB_DA"/>
        </else>
      </if>
    </custom-state>

    <data-access-state id="InvokeMessagingService_DB_DA">
      <data-access id="SendFrontendEvent" classname="com.nuance.metro.dataaccess.SendFrontendEvents">
        <inputs>
          <input-variable name="monitoringFullString"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="message"/>
        </outputs>
      </data-access>
      <action label="default">
        <action next="EndApplication2_CS"/>
      </action>
    </data-access-state>

    <custom-state id="EndApplication2_CS">
      <exit/>
      <event cond="exitErrorCount==0">
        <session-mapping key="exitErrorCount" expr="exitErrorCount + 1"/>
        <exit/>
      </event>
      <event>
        <exit/>
      </event>
    </custom-state>

  </dialog>
  