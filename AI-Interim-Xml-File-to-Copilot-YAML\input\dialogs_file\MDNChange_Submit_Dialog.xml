<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="MDNChange_Submit_Dialog">
    <data-access-state id="MC1100_ChangePhoneNumber_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="trn" value="GlobalVars.trn" type="String"/>
      <session-mapping key="sessionId" value="GlobalVars.sessionId" type="String"/>
      <session-mapping key="nxx" value="GlobalVars.NXX" type="String"/>
      <session-mapping key="npa" value="GlobalVars.NPA" type="String"/>
      <session-mapping key="wait" value="true" type="String"/>
      <session-mapping key="did" value="dnis" type="String"/>
      <session-mapping key="ani" value="GlobalVars.trn" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <data-access id="ChangeNumber" classname="com.nuance.metro.dataaccess.ChangeNumber">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="trn"/>
          <input-variable name="nxx"/>
          <input-variable name="npa"/>
          <input-variable name="wait"/>
          <input-variable name="did"/>
          <input-variable name="ani"/>
          <input-variable name="sessionId"/>
          <input-variable name="JWTToken"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="activationComplete"/>
          <output-variable name="newMdn"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.ChangeNumber" expr="ChangeNumber"/>
        <if cond="ChangeNumber.status == 'Success'">
          <session-mapping key="ChangeNumber.status" expr="'Success'"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          </else>
        </if>
        <action next="MC1110_CheckMDNChangeResult_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="MC1110_CheckMDNChangeResult_DS">
      <if cond="(GlobalVars.ChangeNumber.status == 'Success')&amp;&amp; (GlobalVars.ChangeNumber.activationComplete == true)&amp;&amp; ! (GlobalVars.ChangeNumber.newMdn == null || GlobalVars.ChangeNumber.newMdn == undefined || GlobalVars.ChangeNumber.newMdn == 'null' || GlobalVars.ChangeNumber.newMdn == '')">
        <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'44'"/>
        <session-mapping key="ActivationTable.ERROR_TEXT" expr="''"/>
        <session-mapping key="ActivationTable.MDN" expr="GlobalVars.ChangeNumber.newMdn"/>
        <session-mapping key="mdnChangeVars.eventTypeGMT" expr="getEventTime()"/>
        <session-mapping key="mdnChangeVars.status" expr="'success'"/>
        <session-mapping key="mdnChangeVars.eventType" expr="'mdn_change'"/>
        <action next="MC1130_NewNumberWait_DM"/>
        <else>
          <session-mapping key="ActivationTable.ERROR_TEXT" value="GlobalVars.ChangeNumber.message" type="String"/>
          <session-mapping key="ActivationTable.MDN" expr="''"/>
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          <session-mapping key="GlobalVars.playTransferPrompt" value="true" type="Boolean"/>
          <session-mapping key="mdnChangeVars.eventTypeGMT" expr="getEventTime()"/>
          <session-mapping key="mdnChangeVars.status" expr="'failure'"/>
          <session-mapping key="mdnChangeVars.eventType" expr="'mdn_change'"/>
          <action next="MC1190_CallTransfer_SD"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="MC1130_NewNumberWait_DM" type="CUST">
      <success>
        <action label="continue" next="MC1150_ReadNewNum_DM">
          <audio>
            <prompt id="MC1130_out_01">
              <prompt-segments>
                <audiofile text="Okay, here we go " src="MC1130_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <action next="MC1150_ReadNewNum_DM"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MC1130_ini_02">
                <prompt-segments>
                  <audiofile text="I ve got it" src="MC1130_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1130_ini_01">
                <prompt-segments>
                  <audiofile text="When you're ready for your new number, say 'continue' 8 seconds wait music When you're ready, say 'Continue', or press 1 8 seconds wait music You can say 'Continue', or press 1, at any time 8 seconds wait music If you're ready to hear your new number, say Continue or press 1  8 seconds wait music If you're ready,  say 'Continue' or press 1 8 seconds wait music When you're ready say 'Continue' or press 1 8 seconds wait music I'm having some trouble Let's move on" src="MC1130_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="MC1130_NewNumberWait_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MC1130_ini_01">
                <prompt-segments>
                  <audiofile text="When you're ready for your new number, say 'continue' 8 seconds wait music When you're ready, say 'Continue', or press 1 8 seconds wait music You can say 'Continue', or press 1, at any time 8 seconds wait music If you're ready to hear your new number, say Continue or press 1  8 seconds wait music If you're ready,  say 'Continue' or press 1 8 seconds wait music When you're ready say 'Continue' or press 1 8 seconds wait music I'm having some trouble Let's move on" src="MC1130_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="MC1130_NewNumberWait_DM.grxml" count="1"/>
          <dtmfgrammars filename="MC1130_NewNumberWait_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="0ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MC1150_ReadNewNum_DM" type="YSNO">
      <session-mapping key="NewMdn" value="GlobalVars.ChangeNumber.newMdn" type="String"/>
      <session-mapping key="GlobalVars.mdn" expr="GlobalVars.ChangeNumber.newMdn"/>
      <success>
        <action label="true" next="MC1150_ReadNewNum_DM">
          <audio>
            <prompt id="MC1150_out_01">
              <prompt-segments>
                <audiofile text="Sure" src="MC1150_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="false">
          <audio>
            <prompt id="MC1150_out_02">
              <prompt-segments>
                <audiofile text="All right" src="MC1150_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MC1150_ini_01">
                <prompt-segments>
                  <audiofile text="Your new phone number is " src="MC1150_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="NewMdn">
                <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="MC1150_ini_03">
                <prompt-segments>
                  <audiofile text="That phone number again is " src="MC1150_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="NewMdn">
                <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="MC1150_ini_02">
                <prompt-segments>
                  <audiofile text="Once you have paid the $12 fee plus tax, you may need to restart your phone If you have any problems, you can call 888 8METRO8 for customer service" src="MC1150_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1150_ini_12">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? " src="MC1150_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="" text="" id="MC1150_ReadNewNum_DM_help"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MC1150_ini_01">
                <prompt-segments>
                  <audiofile text="Your new phone number is " src="MC1150_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="NewMdn">
                <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="MC1150_ini_03">
                <prompt-segments>
                  <audiofile text="That phone number again is " src="MC1150_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="NewMdn">
                <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="MC1150_ini_02">
                <prompt-segments>
                  <audiofile text="Once you have paid the $12 fee plus tax, you may need to restart your phone If you have any problems, you can call 888 8METRO8 for customer service" src="MC1150_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1150_ini_12">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? " src="MC1150_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MC1150_ni1_01" cond="false">
                <prompt-segments>
                  <audiofile text="When you re ready to write down the information, say  continue   Otherwise, I ll keep waiting" src="MC1150_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1150_nm1_02">
                <prompt-segments>
                  <audiofile text="To hear your new account number again, say  repeat  Otherwise, please wait and someone will be right with you" src="MC1150_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1150_nm1_03">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no  Would you like to hear your new telephone number again?" src="MC1150_nm1_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1150_nm2_02">
                <prompt-segments>
                  <audiofile text="To hear your new account number again, say  repeat  Otherwise, please wait while I connect you" src="MC1150_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1150_nm2_03">
                <prompt-segments>
                  <audiofile text="If you d like me to repeat your new telephone number, say  yes  or press one If not, say  no  or press two" src="MC1150_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1150_nm3_02">
                <prompt-segments>
                  <audiofile text="If you d like to hear your new account number again, say  repeat  or press seven Otherwise, please wait and an activation specialist will be with you shortly" src="MC1150_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1150_nm2_03">
                <prompt-segments>
                  <audiofile text="If you d like me to repeat your new telephone number, say  yes  or press one If not, say  no  or press two" src="MC1150_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MC1150_ini_01">
                <prompt-segments>
                  <audiofile text="Your new phone number is " src="MC1150_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="NewMdn">
                <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="MC1150_ini_03">
                <prompt-segments>
                  <audiofile text="That phone number again is " src="MC1150_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="NewMdn">
                <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="MC1150_ini_02">
                <prompt-segments>
                  <audiofile text="Once you have paid the $12 fee plus tax, you may need to restart your phone If you have any problems, you can call 888 8METRO8 for customer service" src="MC1150_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1150_ini_12">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? " src="MC1150_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_MC1150_dtmf.grxml">
          <grammars filename="MC1150_ReadNewNum_DM.grxml" count="1"/>
          <dtmfgrammars filename="MC1150_ReadNewNum_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="MC1190_CallTransfer_SD">
      <if cond="GlobalVars.isCareTransfer == 'true'">
        <gotodialog next="CallTransfer_Main_Dialog"/>
        <else>
          <gotodialog next="Transfer_Main"/>
        </else>
      </if>
      <action next="MC1190_CallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MC1190_CallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <event name="maxretries"/>
    </custom-state>

  </dialog>
  