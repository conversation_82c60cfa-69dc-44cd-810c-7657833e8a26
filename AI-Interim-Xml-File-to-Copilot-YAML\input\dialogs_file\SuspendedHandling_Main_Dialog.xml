<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="SuspendedHandling_Main_Dialog">
    <decision-state id="SH1001_CheckContext_DS">
      <if cond="GlobalVars.callType == 'auto_pay' || GlobalVars.callType == 'autopay'">
        <action next="SH1029_CheckHasAutopay_JDA"/>
      </if>
      <if cond="GlobalVars.extensionEntryPoint == undefined">
        <session-mapping key="GlobalVars.extensionEntryPoint" expr="'suspended_misc'"/>
      </if>
      <if cond="GlobalVars.attemptedCheckUnhotlineEligibility == undefined">
        <session-mapping key="GlobalVars.attemptedCheckUnhotlineEligibility" value="false" type="Boolean"/>
      </if>
      <if cond="dueImmediatelyAmount &lt;= 3">
        <action next="SH1015_CallTransfer_SD"/>
        <elseif cond="(GlobalVars.callType == 'extension' &amp;&amp; isEligibleForUnhotlineCheck)">
          <action next="SH1006_CheckUnhotlineEligibility_DB_DA"/>
        </elseif>
        <elseif cond="(GlobalVars.callType == 'extension')">
          <gotodialog next="SuspendedHandlingExtension_Main#SH1310_ApplyExtension_SD"/>
        </elseif>
        <else>
          <session-mapping key="GlobalVars.extensionEntryPoint" expr="'suspended_misc'"/>
          <session-mapping key="GlobalVars.attemptedCheckUnhotlineEligibility" value="false" type="Boolean"/>
          <action next="SH1005_Suspended_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="SH1005_Suspended_PP">
      <session-mapping key="hasAutopay" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="balance" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="paymentStatus" value="GlobalVars.GetAccountDetails.paymentStatus" type="String"/>
      <session-mapping key="payDateDay" value="GlobalVars.GetAccountDetails.payDateDay" type="String"/>
      <session-mapping key="autopay_amount" value="(parseFloat(balance) + parseFloat(dueImmediatelyAmount)).toString()" type="String"/>
      <session-mapping key="isLoggedIn" value="GlobalVars.loggedIn" type="String"/>
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="switchLinesSuccess" value="GlobalVars.switchLinesSuccess" type="String"/>
      <audio>
        <if cond="(aniMatch == 'false' || aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')">
          <prompt id="SH1005_out_02">
            <prompt-segments>
              <audiofile text="It looks like this account has a payment due now" src="SH1005_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="SH1005_out_03" cond="switchLinesSuccess == true">
              <prompt-segments>
                <audiofile text="It looks like this account is suspended, and a payment of " src="SH1005_out_03.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="SH1005_ini_03" cond="switchLinesSuccess != true">
              <prompt-segments>
                <audiofile text="It looks like your account is suspended, and your payment of" src="SH1005_ini_03.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="dueImmediatelyAmount">
              <param value="false" name="playZeroCents"/>
            </prompt>
            <prompt id="SH1005_out_01">
              <prompt-segments>
                <audiofile text="due today" src="SH1005_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <if cond="isEligibleForUnhotlineCheck == 'true' || isEligibleForUnhotlineCheck == true">
        <action next="SH1006_CheckUnhotlineEligibility_DB_DA"/>
        <else>
          <action next="SH1007_PlayNotEligibleExtension_PP"/>
        </else>
      </if>
    </play-state>

    <data-access-state id="SH1006_CheckUnhotlineEligibility_DB_DA">
      <session-mapping key="accountNumber" value="GlobalVars.GetAccountDetails.accountNumber" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="CheckUnhotlineEligibility" classname="com.nuance.metro.dataaccess.CheckUnhotlineEligibility">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="accountNumber" mask="true"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="isEligible"/>
          <output-variable name="numberOfCases"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.CheckUnhotlineEligibility" expr="CheckUnhotlineEligibility"/>
        <session-mapping key="GlobalVars.attemptedCheckUnhotlineEligibility" value="true" type="Boolean"/>
        <if cond="(CheckUnhotlineEligibility.isEligible == 'true' || CheckUnhotlineEligibility.isEligible == true)">
          <session-mapping key="GlobalVars.extensionAllowed" value="true" type="Boolean"/>
        </if>
        <if cond="(GlobalVars.callType == 'extension')">
          <gotodialog next="SuspendedHandlingExtension_Main#SH1310_ApplyExtension_SD"/>
          <else>
            <if cond="(CheckUnhotlineEligibility.isEligible == 'true' || CheckUnhotlineEligibility.isEligible == true)">
              <action next="SH1008_CheckNLUMenuConfig_JDA"/>
              <else>
                <action next="SH1007_PlayNotEligibleExtension_PP"/>
              </else>
            </if>
          </else>
        </if>
      </action>
    </data-access-state>

    <play-state id="SH1007_PlayNotEligibleExtension_PP">
      <session-mapping key="isLoggedIn" value="GlobalVars.loggedIn" type="String"/>
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="numberOfCases" value="GlobalVars.CheckUnhotlineEligibility != undefined ? GlobalVars.CheckUnhotlineEligibility.numberOfCases:0" type="String"/>
      <audio>
        <prompt id="SH1007_out_03" cond="numberOfCases &gt; 0">
          <prompt-segments>
            <audiofile text="You've already been granted an extension in the last 30 days So, you're not eligible for another one right now Our agents wouldn't be able to give you one either" src="SH1007_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="(aniMatch == 'false' || aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')">
          <prompt id="SH1007_out_01" cond="!(numberOfCases &gt; 0)">
            <prompt-segments>
              <audiofile text="And its NOT currently eligible for an extension " src="SH1007_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="SH1007_out_02" cond="!(numberOfCases &gt; 0)">
              <prompt-segments>
                <audiofile text="And I see your account is NOT eligible for an extension right now" src="SH1007_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SH1009_AskPayNow_DM"/>
    </play-state>

    <dm-state id="SH1009_AskPayNow_DM" type="YSNO">
      <session-mapping key="collection_commandgrammar" value="SH1009_AskPayNow_DM.grxml" type="String"/>
      <session-mapping key="collection_dtmfcommandgrammar" value="SH1009_AskPayNow_DM_dtmf.grxml" type="String"/>
      <session-mapping key="collection_commandgrammar" expr="collection_commandgrammar + '?SWI_vars.disallow=repeat^operator'"/>
      <session-mapping key="collection_dtmfcommandgrammar" expr="collection_dtmfcommandgrammar + '?SWI_vars.disallow=repeat^operator'"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <action next="SH1010_MakePayment_SD"/>
        </action>
        <action label="false">
          <action next="SH1008_CheckNLUMenuConfig_JDA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SH1009_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment now?" src="SH1009_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SH1009_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment now?" src="SH1009_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SH1009_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment now?" src="SH1009_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SH1009_AskPayNow_DM_noinput_2"/>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SH1009_AskPayNow_DM_noinput_3"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1009_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment now?" src="SH1009_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="SH1009_AskPayNow_DM_nomatch_2"/>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="SH1009_AskPayNow_DM_nomatch_3"/>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SH1009_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment now?" src="SH1009_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SH1009_AskPayNow_DM.grxml" count="1"/>
          <dtmfgrammars filename="SH1009_AskPayNow_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="SH1008_CheckNLUMenuConfig_DS">
      <if cond="(GlobalVars.GetBCSParameters.care_nlu_enabled == 'true') &amp;&amp; (language == 'en-US')">
        <action next="getReturnLink()"/>
        <else>
          <action next="SH1040_MainMenu_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="SH1010_MakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="SH1010_MakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SH1010_MakePayment_SD_return_CS">
      <if cond="GlobalVars.callType == 'extension' || GlobalVars.paymentFailure == true">
        <gotodialog next="SuspendedHandlingExtension_Main#SH1301_CheckContext_DS"/>
        <else>
          <action next="SH1008_CheckNLUMenuConfig_JDA"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="SH1040_MainMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
      <action next="SH1040_MainMenu_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SH1040_MainMenu_SD_return_CS">
      <action next="SH1040_MainMenu_SD"/>
    </custom-state>

    <subdialog-state id="SH1015_CallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="SH1015_CallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SH1015_CallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <dm-state id="SH1020_SuspendedOptions_DM" type="CUST">
      <session-mapping key="isAutopayEnabled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="extensionAllowed" value="GlobalVars.extensionAllowed == undefined ? false : GlobalVars.extensionAllowed" type="String"/>
      <session-mapping key="allowedResponses" value="" type="String"/>
      <session-mapping key="allowedResponsesDtmf" value="" type="String"/>
      <session-mapping key="isLoggedIn" value="GlobalVars.loggedIn" type="String"/>
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="switchLinesSuccess" value="GlobalVars.switchLinesSuccess" type="String"/>
      <session-mapping key="collection_grammar1" value="SH1020_SuspendedOptions_DM.jsp" type="String"/>
      <session-mapping key="collection_dtmfgrammar1" value="SH1020_SuspendedOptions_DM_dtmf.jsp" type="String"/>
      <success>
        <action label="make-payment">
          <audio>
            <prompt id="SH1020_out_01">
              <prompt-segments>
                <audiofile text="Alright" src="SH1020_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careSuspended'"/>
          <action next="SH1010_MakePayment_SD"/>
        </action>
        <action label="change-plan">
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.suspendedRatePlanChange" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <gotodialog next="SuspendedHandlingExtension_Main#SH1101_CheckContext_DS"/>
        </action>
        <action label="vague-autopay_sh">
          <session-mapping key="GlobalVars.tag" expr="'vague-autopay'"/>
          <action next="SH1029_CheckHasAutopay_JDA"/>
        </action>
        <action label="switch-lines">
          <audio>
            <prompt id="SH1020_out_05">
              <prompt-segments>
                <audiofile text="Sure" src="SH1020_out_05.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.switchLinesEntryPoint" expr="'suspended'"/>
          <action next="SH1505_SwitchLines_SD"/>
        </action>
        <action label="request-extension">
          <if cond="extensionAllowed == true">
            <audio>
              <prompt id="SH1020_out_03">
                <prompt-segments>
                  <audiofile text="Sure" src="SH1020_out_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <audio>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <session-mapping key="GlobalVars.callType" expr="'extension'"/>
            <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
            <gotodialog next="SuspendedHandlingExtension_Main#SH1301_CheckContext_DS"/>
            <else>
              <audio>
                <prompt id="SH1020_out_02">
                  <prompt-segments>
                    <audiofile text="I'm sorry, your account's not eligible for an extension right now You can reduce your payment due now by changing your plan and your add-ons in the myMetro app You'll also be able to make a payment there when you're ready If you DON'T need anything else, you can hang up Otherwise " src="SH1020_out_02.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="SH1020_SuspendedOptions_DM"/>
            </else>
          </if>
        </action>
        <action label="change-payment_date">
          <session-mapping key="GlobalVars.callType" expr="'billcyclereset'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careSuspended'"/>
          <audio>
            <prompt id="SH1020_out_04">
              <prompt-segments>
                <audiofile text="Sure!" src="SH1020_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="SH1405_BillCyclereset_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.suspendedOperatorRequest" value="true" type="Boolean"/>
          <submit expr="operatorSubmitFunction('SuspendedHandling_Main.dvxml','SH1020_SuspendedOptions_DM',SH1020_SuspendedOptions_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SH1020_ini_02" cond="(aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false') &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="To hear the balance and make a payment, say 'pay now' Or say 'get an extension', 'change my rate plan',  or 'autopay' " src="SH1020_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_05" cond="(aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false') &amp;&amp; (extensionAllowed == false)">
                <prompt-segments>
                  <audiofile text="To hear the balance and make a payment, say 'pay now' Or say 'change my rate plan',  or 'autopay'" src="SH1020_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_01" cond="(!((aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')))  &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="Please say 'pay now', 'get an extension', 'change my rate plan',  or 'autopay' " src="SH1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_07" cond="(!((aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')))  &amp;&amp; (extensionAllowed == false)">
                <prompt-segments>
                  <audiofile text="Please say 'pay now', 'change rate plan',  or 'autopay' " src="SH1020_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_03" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date', or 'switch lines' " src="SH1020_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_04" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'switch lines'" src="SH1020_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_06" cond="(!(aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date'" src="SH1020_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="(!(aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true'))">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SH1020_ini_02" cond="(aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false') &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="To hear the balance and make a payment, say 'pay now' Or say 'get an extension', 'change my rate plan',  or 'autopay' " src="SH1020_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_05" cond="(aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false') &amp;&amp; (extensionAllowed == false)">
                <prompt-segments>
                  <audiofile text="To hear the balance and make a payment, say 'pay now' Or say 'change my rate plan',  or 'autopay'" src="SH1020_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_01" cond="(!((aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')))  &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="Please say 'pay now', 'get an extension', 'change my rate plan',  or 'autopay' " src="SH1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_07" cond="(!((aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')))  &amp;&amp; (extensionAllowed == false)">
                <prompt-segments>
                  <audiofile text="Please say 'pay now', 'change rate plan',  or 'autopay' " src="SH1020_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_03" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date', or 'switch lines' " src="SH1020_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_04" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'switch lines'" src="SH1020_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_06" cond="(!(aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date'" src="SH1020_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="(!(aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true'))">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SH1020_ini_02" cond="(aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false') &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="To hear the balance and make a payment, say 'pay now' Or say 'get an extension', 'change my rate plan',  or 'autopay' " src="SH1020_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_05" cond="(aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false') &amp;&amp; (extensionAllowed == false)">
                <prompt-segments>
                  <audiofile text="To hear the balance and make a payment, say 'pay now' Or say 'change my rate plan',  or 'autopay'" src="SH1020_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_01" cond="(!((aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')))  &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="Please say 'pay now', 'get an extension', 'change my rate plan',  or 'autopay' " src="SH1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_07" cond="(!((aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')))  &amp;&amp; (extensionAllowed == false)">
                <prompt-segments>
                  <audiofile text="Please say 'pay now', 'change rate plan',  or 'autopay' " src="SH1020_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_03" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date', or 'switch lines' " src="SH1020_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_04" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'switch lines'" src="SH1020_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_06" cond="(!(aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date'" src="SH1020_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="(!(aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true'))">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_01" cond="extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="Please say 'pay now' or press 1 'Get an extension' or press 2 'Change my rate plan' or press 3 or 'Autopay' - 4 " src="SH1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_02" cond="extensionAllowed == false">
                <prompt-segments>
                  <audiofile text="Please say 'pay now' or press 1 'Change rate plan' or press 2 or 'Autopay' - 3 " src="SH1020_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_03" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say change my due date or press 5, or switch account or press 6" src="SH1020_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_05" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date' or press 4, or 'switch lines' or press 5 " src="SH1020_nm2_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_04" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say switch account, or press 4" src="SH1020_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_07" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'switch lines', or press 4" src="SH1020_nm2_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_06" cond="(aniMatch != true &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say change my due date or press 5" src="SH1020_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_08" cond="(aniMatch != true &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date' or press 4 " src="SH1020_nm2_08.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_01" cond="extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="Please say 'pay now' or press 1 'Get an extension' or press 2 'Change my rate plan' or press 3 or 'Autopay' - 4 " src="SH1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_02" cond="extensionAllowed == false">
                <prompt-segments>
                  <audiofile text="Please say 'pay now' or press 1 'Change rate plan' or press 2 or 'Autopay' - 3 " src="SH1020_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_03" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say change my due date or press 5, or switch account or press 6" src="SH1020_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_05" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date' or press 4, or 'switch lines' or press 5 " src="SH1020_nm2_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_04" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say switch account, or press 4" src="SH1020_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_07" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'switch lines', or press 4" src="SH1020_nm2_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_06" cond="(aniMatch != true &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say change my due date or press 5" src="SH1020_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_08" cond="(aniMatch != true &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date' or press 4 " src="SH1020_nm2_08.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_02" cond="(aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false') &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="To hear the balance and make a payment, say 'pay now' Or say 'get an extension', 'change my rate plan',  or 'autopay' " src="SH1020_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_05" cond="(aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false') &amp;&amp; (extensionAllowed == false)">
                <prompt-segments>
                  <audiofile text="To hear the balance and make a payment, say 'pay now' Or say 'change my rate plan',  or 'autopay'" src="SH1020_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_01" cond="(!((aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')))  &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="Please say 'pay now', 'get an extension', 'change my rate plan',  or 'autopay' " src="SH1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_07" cond="(!((aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')))  &amp;&amp; (extensionAllowed == false)">
                <prompt-segments>
                  <audiofile text="Please say 'pay now', 'change rate plan',  or 'autopay' " src="SH1020_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_03" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date', or 'switch lines' " src="SH1020_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_04" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'switch lines'" src="SH1020_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_06" cond="(!(aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date'" src="SH1020_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="(!(aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true'))">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_01" cond="extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="Please say 'pay now' or press 1 'Get an extension' or press 2 'Change my rate plan' or press 3 or 'Autopay' - 4 " src="SH1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_02" cond="extensionAllowed == false">
                <prompt-segments>
                  <audiofile text="Please say 'pay now' or press 1 'Change rate plan' or press 2 or 'Autopay' - 3 " src="SH1020_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_03" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say change my due date or press 5, or switch account or press 6" src="SH1020_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_05" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date' or press 4, or 'switch lines' or press 5 " src="SH1020_nm2_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_04" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say switch account, or press 4" src="SH1020_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_07" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'switch lines', or press 4" src="SH1020_nm2_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_06" cond="(aniMatch != true &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say change my due date or press 5" src="SH1020_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_08" cond="(aniMatch != true &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date' or press 4 " src="SH1020_nm2_08.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_01" cond="extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="Please say 'pay now' or press 1 'Get an extension' or press 2 'Change my rate plan' or press 3 or 'Autopay' - 4 " src="SH1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_02" cond="extensionAllowed == false">
                <prompt-segments>
                  <audiofile text="Please say 'pay now' or press 1 'Change rate plan' or press 2 or 'Autopay' - 3 " src="SH1020_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_03" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say change my due date or press 5, or switch account or press 6" src="SH1020_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_05" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date' or press 4, or 'switch lines' or press 5 " src="SH1020_nm2_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_04" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say switch account, or press 4" src="SH1020_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_07" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'switch lines', or press 4" src="SH1020_nm2_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_06" cond="(aniMatch != true &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == true))">
                <prompt-segments>
                  <audiofile text="You can also say change my due date or press 5" src="SH1020_nm2_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_nm2_08" cond="(aniMatch != true &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true') &amp;&amp; (extensionAllowed == false))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date' or press 4 " src="SH1020_nm2_08.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SH1020_ini_02" cond="(aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false') &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="To hear the balance and make a payment, say 'pay now' Or say 'get an extension', 'change my rate plan',  or 'autopay' " src="SH1020_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_05" cond="(aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false') &amp;&amp; (extensionAllowed == false)">
                <prompt-segments>
                  <audiofile text="To hear the balance and make a payment, say 'pay now' Or say 'change my rate plan',  or 'autopay'" src="SH1020_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_01" cond="(!((aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')))  &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="Please say 'pay now', 'get an extension', 'change my rate plan',  or 'autopay' " src="SH1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_07" cond="(!((aniMatch == false) &amp;&amp; (isLoggedIn == false || isLoggedIn == 'false')))  &amp;&amp; (extensionAllowed == false)">
                <prompt-segments>
                  <audiofile text="Please say 'pay now', 'change rate plan',  or 'autopay' " src="SH1020_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_03" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date', or 'switch lines' " src="SH1020_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_04" cond="((aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'switch lines'" src="SH1020_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1020_ini_06" cond="(!(aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset == true || eligibleForBillCycleReset == 'true'))">
                <prompt-segments>
                  <audiofile text="You can also say 'change my due date'" src="SH1020_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="(!(aniMatch == true &amp;&amp; switchLinesSuccess == false) &amp;&amp; (eligibleForBillCycleReset != true &amp;&amp; eligibleForBillCycleReset != 'true'))">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SH1020_SuspendedOptions_DM.jsp" count="1">
	  </grammars>
          <dtmfgrammars filename="SH1020_SuspendedOptions_DM_dtmf.jsp" count="1">
	  </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="pay_now == 'lastresult'">
                <prompt id="SH1020_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Make a payment now" src="SH1020_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="change_plan == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Change your rate plan" src="SH1020_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="autopay == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_03" cond="(isAutopayEnabled == true)">
                    <prompt-segments>
                      <audiofile text="Autopay" src="SH1020_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="SH1020_cnf_ini_04" cond="(isAutopayEnabled != true)">
                    <prompt-segments>
                      <audiofile text="Signing up for Autopay" src="SH1020_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="billcycle_reset == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Changing your due date" src="SH1020_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="switch_lines == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Work with a different line" src="SH1020_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="extension == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="A payment extension " src="SH1020_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="pay_now == 'lastresult'">
                <prompt id="SH1020_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Make a payment now" src="SH1020_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="change_plan == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Change your rate plan" src="SH1020_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="autopay == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_03" cond="(isAutopayEnabled == true)">
                    <prompt-segments>
                      <audiofile text="Autopay" src="SH1020_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="SH1020_cnf_ini_04" cond="(isAutopayEnabled != true)">
                    <prompt-segments>
                      <audiofile text="Signing up for Autopay" src="SH1020_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="billcycle_reset == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Changing your due date" src="SH1020_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="switch_account == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Work with a different line" src="SH1020_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="extension == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="A payment extension " src="SH1020_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="pay_now == 'lastresult'">
                <prompt id="SH1020_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Make a payment now" src="SH1020_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="change_plan == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Change your rate plan" src="SH1020_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="autopay == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_03" cond="(isAutopayEnabled == true)">
                    <prompt-segments>
                      <audiofile text="Autopay" src="SH1020_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="SH1020_cnf_ini_04" cond="(isAutopayEnabled != true)">
                    <prompt-segments>
                      <audiofile text="Signing up for Autopay" src="SH1020_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="billcycle_reset == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Changing your due date" src="SH1020_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="switch_account == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Work with a different line" src="SH1020_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="extension == 'lastresult'">
                  <prompt id="SH1020_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="A payment extension " src="SH1020_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="SH1030_PayNowYN_DM" type="CUST">
      <session-mapping key="isAutopayEnabled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="extensionAllowed" value="GlobalVars.extensionAllowed" type="String"/>
      <session-mapping key="firstTimeAt1030" value="GlobalVars.firstTimeAtSH1030" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <success>
        <action label="yes">
          <audio>
            <prompt id="SH1030_out_01">
              <prompt-segments>
                <audiofile text="Alright" src="SH1030_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'make_pmt_auto_pay'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="repromptAutopay" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careSuspended'"/>
          <action next="SH1010_MakePayment_SD"/>
        </action>
        <action label="no">
          <session-mapping key="repromptAutopay" value="false" type="Boolean"/>
          <audio>
            <prompt id="SH1030_out_02">
              <prompt-segments>
                <audiofile text="No problem You can make a payment online, or at one of our stores or authorized dealers To access your account or for a map of our locations near you,visit us at metrobyt-mobilecom Don't forget you won't be able to use your phone until you make a payment And if you wait for more than 30 days after your due dtae, your account'll be closed, and you may not be able to keep your phone number   " src="SH1030_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="SH1035_Goodbye_SD"/>
        </action>
        <action label="extension">
          <session-mapping key="GlobalVars.callType" expr="'extension'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <audio>
            <prompt id="SH1030_out_03">
              <prompt-segments>
                <audiofile text="sure" src="SH1030_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <gotodialog next="SuspendedHandlingExtension_Main#SH1301_CheckContext_DS"/>
        </action>
        <action label="go_back">
          <action next="SH1020_SuspendedOptions_DM"/>
        </action>
        <action label="operator">
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.suspendedOperatorRequest" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.firstTimeAtSH1030" value="true" type="Boolean"/>
          <submit expr="operatorSubmitFunction('SuspendedHandling_Main.dvxml','SH1030_PayNowYN_DM',SH1030_PayNowYN_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="firstTimeAt1030 == true">
                <prompt id="SH1030_ini_01">
                  <prompt-segments>
                    <audiofile text="We can set up your Autopay after you make a payment Would you like to pay now? To hear your other options, say 'go back' " src="SH1030_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="tag == 'vague-autopay'">
                  <prompt id="SH1030_ini_03">
                    <prompt-segments>
                      <audiofile text="Before we can work with autopay you still need make a payment on your suspended account" src="SH1030_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="SH1030_rin_01">
                    <prompt-segments>
                      <audiofile text="Before we can set you up for Autopay, we'll still need to get your payment After that, we can set you up would you like to go ahead and pay now? " src="SH1030_rin_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="SH1030_ini_02">
                    <prompt-segments>
                      <audiofile text="To hear all your options again, say go back" src="SH1030_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="firstTimeAt1030 == true">
                <prompt id="SH1030_ini_01">
                  <prompt-segments>
                    <audiofile text="We can set up your Autopay after you make a payment Would you like to pay now? To hear your other options, say 'go back' " src="SH1030_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="tag == 'vague-autopay'">
                  <prompt id="SH1030_ini_03">
                    <prompt-segments>
                      <audiofile text="Before we can work with autopay you still need make a payment on your suspended account" src="SH1030_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="SH1030_rin_01">
                    <prompt-segments>
                      <audiofile text="Before we can set you up for Autopay, we'll still need to get your payment After that, we can set you up would you like to go ahead and pay now? " src="SH1030_rin_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="SH1030_ini_02">
                    <prompt-segments>
                      <audiofile text="To hear all your options again, say go back" src="SH1030_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SH1030_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay over the phone *now*?" src="SH1030_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to pay by phone now, say yes or press 1 Otherwise, say no or press 2 To hear all you options again, say go back or press 3" src="SH1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to pay by phone now, say yes or press 1 Otherwise, say no or press 2 To hear all you options again, say go back or press 3" src="SH1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1030_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay over the phone *now*?" src="SH1030_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to pay by phone now, say yes or press 1 Otherwise, say no or press 2 To hear all you options again, say go back or press 3" src="SH1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to pay by phone now, say yes or press 1 Otherwise, say no or press 2 To hear all you options again, say go back or press 3" src="SH1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="firstTimeAt1030 == true">
                <prompt id="SH1030_ini_01">
                  <prompt-segments>
                    <audiofile text="We can set up your Autopay after you make a payment Would you like to pay now? To hear your other options, say 'go back' " src="SH1030_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="tag == 'vague-autopay'">
                  <prompt id="SH1030_ini_03">
                    <prompt-segments>
                      <audiofile text="Before we can work with autopay you still need make a payment on your suspended account" src="SH1030_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="SH1030_rin_01">
                    <prompt-segments>
                      <audiofile text="Before we can set you up for Autopay, we'll still need to get your payment After that, we can set you up would you like to go ahead and pay now? " src="SH1030_rin_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="SH1030_ini_02">
                    <prompt-segments>
                      <audiofile text="To hear all your options again, say go back" src="SH1030_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommandsNoOperator_dtmf.grxml">
          <grammars filename="SH1030_PayNowYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="SH1030_PayNowYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="SH1030_cnf_ini_01">
                <prompt-segments>
                  <audiofile text="You want a payment extension" src="SH1030_cnf_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1030_cnf_ini_01">
                <prompt-segments>
                  <audiofile text="You want a payment extension" src="SH1030_cnf_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1030_cnf_ini_01">
                <prompt-segments>
                  <audiofile text="You want a payment extension" src="SH1030_cnf_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="SH1035_Goodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="SH1035_Goodbye_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SH1035_Goodbye_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <subdialog-state id="SH1035_GoToAutoPay_SD">
      <gotodialog next="AutoPay_Main_Dialog"/>
      <action next="SH1035_GoToAutoPay_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SH1035_GoToAutoPay_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="SH1029_CheckHasAutopay_DS">
      <session-mapping key="isAutopayEnabled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <if cond="isAutopayEnabled==true">
        <session-mapping key="GlobalVars.callType" expr="'autopay'"/>
        <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        <action next="SH1015_CallTransfer_SD"/>
        <else>
          <action next="SH1030_PayNowYN_DM"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="SH1405_BillCyclereset_SD">
      <gotodialog next="BillCycleReset_Care_Dialog"/>
      <action next="SH1405_BillCyclereset_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SH1405_BillCyclereset_SD_return_CS">
      <action next="SH1410_PlayDeclinedBCRTransition_PP"/>
    </custom-state>

    <play-state id="SH1410_PlayDeclinedBCRTransition_PP">
      <audio>
        <prompt id="SH1410_out_01">
          <prompt-segments>
            <audiofile text="If you re done, you can hang up Otherwise, here are all your options again " src="SH1410_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SH1020_SuspendedOptions_DM"/>
    </play-state>

    <subdialog-state id="SH1505_SwitchLines_SD">
      <gotodialog next="SwitchLines_Main_Dialog"/>
      <action next="SH1505_SwitchLines_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SH1505_SwitchLines_SD_return_CS">
      <if cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'active'">
        <action next="getReturnLink()"/>
        <else>
          <action next="SH1001_CheckContext_JDA"/>
        </else>
      </if>
    </custom-state>

  </dialog>
  