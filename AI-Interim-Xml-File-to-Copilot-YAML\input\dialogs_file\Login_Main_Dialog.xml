<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Login_Main_Dialog">
    <decision-state id="LG1005_CheckHaveAccountNumber_DS">
      <session-mapping key="GlobalVars.SaidActivateAtLogin" value="false" type="Boolean"/>
      <if cond="(GlobalVars.loggedIn == true || GlobalVars.loggedIn == 'true')">
        <session-mapping key="GlobalVars.guestPayment" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.dontKnowPIN" value="false" type="Boolean"/>
        <action next="getReturnLink()"/>
        <elseif cond="(GlobalVars.callType == 'dsgExtension')">
          <session-mapping key="GlobalVars.guestPayment" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.dontKnowPIN" value="false" type="Boolean"/>
          <gotodialog next="Login_Security#LG1401_CheckContext_DS"/>
        </elseif>
        <elseif cond="!(GlobalVars.GetAccountDetails == null || GlobalVars.GetAccountDetails == '' || GlobalVars.GetAccountDetails == undefined)">
          <if cond="GlobalVars.pinFromIDUpfront == true">
            <session-mapping key="GlobalVars.playTransitionalPINprompt" value="true" type="Boolean"/>
            <gotodialog next="Login_Security#LG1113_GetOTP_SD"/>
            <else>
              <session-mapping key="GlobalVars.playTransitionalPINprompt" value="false" type="Boolean"/>
              <gotodialog next="Login_Security#LG1110_CheckAccountDetails_DS"/>
            </else>
          </if>
        </elseif>
        <elseif cond="GlobalVars.maxTriesAttempted != null &amp;&amp; GlobalVars.maxTriesAttempted == true">
          <session-mapping key="GlobalVars.guestPayment" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.dontKnowPIN" value="false" type="Boolean"/>
          <action next="LG1025_LoginFailure_DM"/>
        </elseif>
        <elseif cond="GlobalVars.securityRequired == true">
          <session-mapping key="GlobalVars.guestPayment" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.dontKnowPIN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.playTransitionalPINprompt" value="true" type="Boolean"/>
          <action next="LG1010_GetAccountNumber_DM"/>
        </elseif>
        <else>
          <session-mapping key="GlobalVars.guestPayment" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.dontKnowPIN" value="false" type="Boolean"/>
          <action next="LG1010_GetAccountNumber_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="LG1010_GetAccountNumber_DM" type="CUST">
      <session-mapping key="numTries" value="getAccountNumberTries" type="String"/>
      <session-mapping key="from611orStar99" value="GlobalVars.serviceDialed == '611' || GlobalVars.serviceDialed == '99'" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="GlobalVars.comingFromLG1010LG1025" value="true" type="Boolean"/>
      <success>
        <action label="report-phone_lost">
          <session-mapping key="GlobalVars.callType" expr="'lost_phone'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'lost_phone'"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <gotodialog next="Login_Security_Dialog"/>
        </action>
        <action label="sign_up">
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <gotodialog next="Login_Security_Dialog"/>
        </action>
        <action label="activate-new_account">
          <session-mapping key="GlobalVars.callType" expr="'new_customer'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentInfoRequired" value="false" type="Boolean"/>
          <gotodialog next="Login_Security_Dialog"/>
        </action>
        <action label="activate-phone_lg">
          <session-mapping key="GlobalVars.callType" expr="'activate_unknownDevice'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'activate'"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <gotodialog next="Login_Security_Dialog"/>
        </action>
        <action label="default" next="LG1015_GetAccountDetails_DB_DA">
          <session-mapping key="getAccountNumberTries" expr="getAccountNumberTries + 1"/>
          <session-mapping key="GlobalVars.mdn" expr="LG1010_GetAccountNumber_DM.returnvalue"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.transferFrom" expr="'LoginSD_XR'"/>
          <submit expr="operatorSubmitFunction('Login_Main.dvxml','LG1010_GetAccountNumber_DM',LG1010_GetAccountNumber_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="callType == 'make_pmt'">
                <prompt id="LG1010_ini_01">
                  <prompt-segments>
                    <audiofile src="LG1010_ini_01.wav" text="I ll just need the Metro Phone number you want to pay for Say or enter it now"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="callType == 'switch_phone'">
                  <prompt id="LG1010_ini_03">
                    <prompt-segments>
                      <audiofile src="LG1010_ini_03.wav" text="First, what s the Metro  phone number you want to work with?"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="LG1010_ini_02">
                    <prompt-segments>
                      <audiofile src="LG1010_ini_02.wav" text="First, say or enter your Metro Phone number"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="LG1010_GetAccountNumber_DM_reinvoke"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="callType == 'make_pmt'">
                <prompt id="LG1010_ini_01">
                  <prompt-segments>
                    <audiofile src="LG1010_ini_01.wav" text="I ll just need the Metro Phone number you want to pay for Say or enter it now"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="callType == 'switch_phone'">
                  <prompt id="LG1010_ini_03">
                    <prompt-segments>
                      <audiofile src="LG1010_ini_03.wav" text="First, what s the Metro  phone number you want to work with?"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="LG1010_ini_02">
                    <prompt-segments>
                      <audiofile src="LG1010_ini_02.wav" text="First, say or enter your Metro Phone number"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="LG1010_nm1_01" cond="from611orStar99 == true">
                <prompt-segments>
                  <audiofile src="LG1010_nm1_01.wav" text="Please enter your Metro area code and phone number"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1010_nm1_02" cond="from611orStar99 != true">
                <prompt-segments>
                  <audiofile src="LG1010_nm1_02.wav" text="Starting with the area code, enter your Metro phone number If you don t have an account yet, you can say  getting started with Metro  You can also say  my phone is missing or damaged "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1010_nm2_01">
                <prompt-segments>
                  <audiofile src="LG1010_nm2_01.wav" text="Starting with the area code, enter your metro phone number using the telephone keypad Or if you ve just bought a Metro phone and need to activate it, say  activate a phone  You can also say  my phone is missing or damaged  or  if you don t have an account yet, you can say  getting started with Metro "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1010_nm2_01">
                <prompt-segments>
                  <audiofile src="LG1010_nm2_01.wav" text="Starting with the area code, enter your metro phone number using the telephone keypad Or if you ve just bought a Metro phone and need to activate it, say  activate a phone  You can also say  my phone is missing or damaged  or  if you don t have an account yet, you can say  getting started with Metro "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1010_nm1_01" cond="from611orStar99 == true">
                <prompt-segments>
                  <audiofile src="LG1010_nm1_01.wav" text="Please enter your Metro area code and phone number"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1010_nm1_02" cond="from611orStar99 != true">
                <prompt-segments>
                  <audiofile src="LG1010_nm1_02.wav" text="Starting with the area code, enter your Metro phone number If you don t have an account yet, you can say  getting started with Metro  You can also say  my phone is missing or damaged "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1010_nm2_01">
                <prompt-segments>
                  <audiofile src="LG1010_nm2_01.wav" text="Starting with the area code, enter your metro phone number using the telephone keypad Or if you ve just bought a Metro phone and need to activate it, say  activate a phone  You can also say  my phone is missing or damaged  or  if you don t have an account yet, you can say  getting started with Metro "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1010_nm2_01">
                <prompt-segments>
                  <audiofile src="LG1010_nm2_01.wav" text="Starting with the area code, enter your metro phone number using the telephone keypad Or if you ve just bought a Metro phone and need to activate it, say  activate a phone  You can also say  my phone is missing or damaged  or  if you don t have an account yet, you can say  getting started with Metro "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="callType == 'make_pmt'">
                <prompt id="LG1010_ini_01">
                  <prompt-segments>
                    <audiofile src="LG1010_ini_01.wav" text="I ll just need the Metro Phone number you want to pay for Say or enter it now"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="callType == 'switch_phone'">
                  <prompt id="LG1010_ini_03">
                    <prompt-segments>
                      <audiofile src="LG1010_ini_03.wav" text="First, what s the Metro  phone number you want to work with?"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="LG1010_ini_02">
                    <prompt-segments>
                      <audiofile src="LG1010_ini_02.wav" text="First, say or enter your Metro Phone number"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="LG1010_GetAccountNumber_DM.grxml" count="1"/>
          <dtmfgrammars filename="LG1010_GetAccountNumber_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="LG1015_GetAccountDetails_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <data-access id="GetSubscriberDetails" classname="com.nuance.metro.dataaccess.GetSubscriberDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="languageCode"/>
          <input-variable name="sessionId"/>
          <input-variable name="imei"/>
          <input-variable name="clearCache"/>
          <input-variable name="multiLineDetails"/>
        </inputs>
        <outputs>
          <output-variable name="mdn"/>
          <output-variable name="accountNumber" mask="true"/>
          <output-variable name="active"/>
          <output-variable name="serialNumber"/>
          <output-variable name="features"/>
          <output-variable name="thirdPartyFeatures"/>
          <output-variable name="payDate"/>
          <output-variable name="taxInclusive"/>
          <output-variable name="accountStatus"/>
          <output-variable name="dueImmediatelyAmount"/>
          <output-variable name="highSecurity"/>
          <output-variable name="ratePlan"/>
          <output-variable name="marketID"/>
          <output-variable name="language"/>
          <output-variable name="isAutopayEnabled"/>
          <output-variable name="hasPhoneInsurance"/>
          <output-variable name="balance"/>
          <output-variable name="isDeviceAllowed"/>
          <output-variable name="isSuspended"/>
          <output-variable name="accountType"/>
          <output-variable name="validAccount"/>
          <output-variable name="roamingBucket"/>
          <output-variable name="paymentStatus"/>
          <output-variable name="ratePlanAttributesMap"/>
          <output-variable name="networkType"/>
          <output-variable name="securityQuestionCode"/>
          <output-variable name="hasEWallet"/>
          <output-variable name="eligibleForBillCycleReset"/>
          <output-variable name="billCycleLength"/>
          <output-variable name="firstName"/>
          <output-variable name="lastName"/>
          <output-variable name="eligibleForUnhotlineCheck"/>
          <output-variable name="securityQuestionString"/>
          <output-variable name="iccid"/>
          <output-variable name="imei"/>
          <output-variable name="hasAutopayPending"/>
          <output-variable name="insuranceCode"/>
          <output-variable name="subscriberId"/>
          <output-variable name="usedESNAllowed"/>
          <output-variable name="emailVerificationStatus"/>
          <output-variable name="emailAddress"/>
          <output-variable name="currentPromo"/>
          <output-variable name="futurePromo"/>
          <output-variable name="featureCodes"/>
          <output-variable name="superRegion"/>
          <output-variable name="accountFutureRequestInd"/>
          <output-variable name="subscriberFuturePricePlanInd"/>
          <output-variable name="ratePlanPrice"/>
          <output-variable name="currentPlanPromptURL"/>
          <output-variable name="currentPlanPromptTTS"/>
          <output-variable name="familyPlanIndicator"/>
          <output-variable name="topupEligibility"/>
          <output-variable name="customerTreatmentLevel"/>
          <output-variable name="HasHighSpeedData"/>
          <output-variable name="includedFeatures"/>
          <output-variable name="hasDataMaximizer"/>
          <output-variable name="eligibleForUpgrade"/>
          <output-variable name="upgradeEligibilityDate"/>
          <output-variable name="arBalance"/>
          <output-variable name="multilineList"/>
          <output-variable name="planMedialPromptURL"/>
          <output-variable name="planMedialPromptTTS"/>
          <output-variable name="hasDefaultAccountValues"/>
          <output-variable name="zipCode" mask="true"/>
          <output-variable name="accountRestrictIndicator"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetSubscriberDetails &amp;&amp; GetSubscriberDetails.validAccount">
          <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
          <session-mapping key="GlobalVars.trn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.unCoopMaxRequest" expr="GetSubscriberDetails.unCoopMaxRequest"/>
          <session-mapping key="GlobalVars.coopMaxRequest" expr="GetSubscriberDetails.coopMaxRequest"/>
          <session-mapping key="ActivationTable.OLD_MDN_NUM" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.networkType" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="ActivationTable.NETWORK_TYPE" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="GlobalVars.marketID" expr="GetSubscriberDetails.marketID"/>
          <session-mapping key="GlobalVars.zipCode" expr="GetSubscriberDetails.zipCode"/>
          <session-mapping key="GlobalVars.mdn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.networkType" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="GlobalVars.isOnFamilyPlan" expr="GetSubscriberDetails.isOnFamilyPlan"/>
          <session-mapping key="GlobalVars.accountFutureRequestInd" expr="GetSubscriberDetails.accountFutureRequestInd"/>
          <session-mapping key="GlobalVars.subscriberFuturePricePlanInd" expr="GetSubscriberDetails.subscriberFuturePricePlanInd"/>
          <session-mapping key="GlobalVars.eligibleForUpgrade" expr="GetSubscriberDetails.isCurrentlyEligibleForDeviceUpgrade"/>
          <session-mapping key="GlobalVars.upgradeEligibilityDate" expr="GetSubscriberDetails.upgradeEligibilityDate"/>
        </if>
        <action next="LG1020_CheckAccountValidated_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="LG1020_CheckAccountValidated_DS">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <if cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.validAccount">
        <if cond="GlobalVars.shortSecurityCodePrompt == true">
          <audio>
            <prompt id="LG1020_out_01">
              <prompt-segments>
                <audiofile text="Okay" src="LG1020_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </if>
        <session-mapping key="GlobalVars.cti_MDN" expr="GlobalVars.mdn"/>
        <gotodialog next="Login_Security_Dialog"/>
        <else>
          <session-mapping key="GlobalVars.mdn" expr="undefined"/>
          <if cond="getAccountNumberTries &lt; 3">
            <action next="LG1010_GetAccountNumber_DM"/>
            <else>
              <if cond="callType == 'switch_phone'">
                <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'131'"/>
              </if>
              <session-mapping key="GlobalVars.maxTriesAttempted" value="true" type="Boolean"/>
              <session-mapping key="GlobalVars.noAccountFound" value="true" type="Boolean"/>
              <action next="LG1025_LoginFailure_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <dm-state id="LG1025_LoginFailure_DM" type="CUST">
      <session-mapping key="GlobalVars.comingFromLG1010LG1025" value="true" type="Boolean"/>
      <success>
        <action label="activate-new_phone">
          <session-mapping key="GlobalVars.callType " expr="'activate_unknownDevice'"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentInfoRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <gotodialog next="Login_Security_Dialog"/>
        </action>
        <action label="inquire-store_location">
          <session-mapping key="GlobalVars.callType " expr="'find_store'"/>
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'sign up'"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentInfoRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <gotodialog next="Login_Security_Dialog"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.transferFrom" expr="'LoginSD_XR'"/>
          <submit expr="operatorSubmitFunction('Login_Main.dvxml','LG1025_LoginFailure_DM',LG1025_LoginFailure_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="LG1025_ini_01">
                <prompt-segments>
                  <audiofile text="I just can t seem to find that phone number" src="LG1025_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1025_ini_02">
                <prompt-segments>
                  <audiofile src="LG1025_ini_02.wav" text="One possibility is that your phone isn t active on the Metro network You can say  activate my phone  or  find a store  If you re done here, just hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="LG1025_ini_01">
                <prompt-segments>
                  <audiofile text="I just can t seem to find that phone number" src="LG1025_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1025_ini_02">
                <prompt-segments>
                  <audiofile src="LG1025_ini_02.wav" text="One possibility is that your phone isn t active on the Metro network You can say  activate my phone  or  find a store  If you re done here, just hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="LG1025_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  activate a phone  or  find a store " src="LG1025_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1025_nm2_01">
                <prompt-segments>
                  <audiofile src="LG1025_nm2_01.wav" text="Without your login information, there are only a couple of things I can help you with If you bought a Metro phone and need to activate it, say  activate a phone  You can also say  find a store "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1025_nm2_01">
                <prompt-segments>
                  <audiofile src="LG1025_nm2_01.wav" text="Without your login information, there are only a couple of things I can help you with If you bought a Metro phone and need to activate it, say  activate a phone  You can also say  find a store "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1025_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  activate a phone  or  find a store " src="LG1025_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1025_nm2_01">
                <prompt-segments>
                  <audiofile src="LG1025_nm2_01.wav" text="Without your login information, there are only a couple of things I can help you with If you bought a Metro phone and need to activate it, say  activate a phone  You can also say  find a store "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1025_nm2_01">
                <prompt-segments>
                  <audiofile src="LG1025_nm2_01.wav" text="Without your login information, there are only a couple of things I can help you with If you bought a Metro phone and need to activate it, say  activate a phone  You can also say  find a store "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="LG1025_ini_01">
                <prompt-segments>
                  <audiofile text="I just can t seem to find that phone number" src="LG1025_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1025_ini_02">
                <prompt-segments>
                  <audiofile src="LG1025_ini_02.wav" text="One possibility is that your phone isn t active on the Metro network You can say  activate my phone  or  find a store  If you re done here, just hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="LG1025_LoginFailure_DM.grxml" count="1"/>
          <dtmfgrammars filename="LG1025_LoginFailure_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  