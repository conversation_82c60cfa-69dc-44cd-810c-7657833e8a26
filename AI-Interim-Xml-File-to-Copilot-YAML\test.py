import yaml
from app_run import send_email

def validate_and_fix_yaml(yaml_path, reference_yaml_path):
    with open(reference_yaml_path, 'r') as ref_file:
        reference_yaml = yaml.safe_load(ref_file)

    with open(yaml_path, 'r') as yaml_file:
        try:
            yaml.safe_load(yaml_file)
            print("YAML is valid.")
        except yaml.YAMLError as exc:
            print("YAML has indentation issues. Fixing using reference YAML...")
            with open(yaml_path, 'w') as yaml_file:
                yaml.dump(reference_yaml, yaml_file, default_flow_style=False)
            print("YAML has been fixed.")

# Example usage
yaml_path = 'output/formatted_yaml.yml'
reference_yaml_path = 'output/reference_yaml.yaml'
output = 'output/bot_yaml/final.yml'

send_email('<EMAIL>',output)
#validate_and_fix_yaml(yaml_path, reference_yaml_path)