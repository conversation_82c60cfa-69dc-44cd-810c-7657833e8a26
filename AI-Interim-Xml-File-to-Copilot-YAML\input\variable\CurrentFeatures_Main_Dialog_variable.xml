<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="features" value="empty" type="String"/>
  <session-mapping key="thirdPartyfeatures" value="empty" type="String"/>
  <session-mapping key="GetAccountDetails.features" value="null" type="string"/>
  <session-mapping key="GetAccountDetails.thirdPartyFeatures" value="null" type="string"/>
  <session-mapping key="features.length" value="0" type="integer"/>
  <session-mapping key="thirdPartyFeatures.length" value="0" type="string"/>
  <session-mapping key="GetAccountDetails.featuresAreMissing" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.subscriberFuturePricePlanInd" value="true" type="string"/>
  <session-mapping key="GetFutureTransactionDetails_CF1006.status" value="Success" type="string"/>
  <session-mapping key="GlobalVars.isFutureAddOnsAdded" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.isFutureAddOnsRemoved" value="true" type="boolean"/>
  <session-mapping key="GetFutureTransactionDetails_CF1006.isFutureAddOnsMissing" value="true" type="boolean"/>
  <session-mapping key="hasThirdPartyFeatures" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="active" type="string"/>
  <session-mapping key="heardThirdPartyInfo" value="true" type="boolean"/>
  <session-mapping key="fromDisamg" value="true" type="boolean"/>
  <session-mapping key="hasFeatures" value="true" type="boolean"/>
</session-mappings>
