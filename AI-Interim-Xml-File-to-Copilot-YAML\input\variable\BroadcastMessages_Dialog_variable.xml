<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="campaignId" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="GlobalVars.broadcastMessageKey" value="IH" type="string"/>
  <session-mapping key="GlobalVars.isMarketOutageFlag" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.marketId" value="null" type="string"/>
  <session-mapping key="broadcastMessage" value="Custom" type="string"/>
  <session-mapping key="broadcastMessageAction" value="null" type="string"/>
  <session-mapping key="GlobalVars.messagePlayed_IH" value="true" type="string"/>
  <session-mapping key="GlobalVars.messagePlayed_RP" value="true" type="string"/>
  <session-mapping key="GlobalVars.messagePlayed_MP" value="true" type="string"/>
  <session-mapping key="GlobalVars.messagePlayed_VS" value="true" type="string"/>
  <session-mapping key="GlobalVars.messagePlayed_MC" value="true" type="string"/>
  <session-mapping key="GlobalVars.MarketOutagemessagePlayed_IH" value="true" type="boolean"/>
  <session-mapping key="MarketOutageplayReturningMessage" value="true" type="boolean"/>
  <session-mapping key="playReturningMessage" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.MetroFlexSMSSuccess" value="true" type="boolean"/>
</session-mappings>
