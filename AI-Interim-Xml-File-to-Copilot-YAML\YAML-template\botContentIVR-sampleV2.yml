kind: BotDefinition
entity:
  displayName: Sample_Bot
  cdsBotId: 4a1f5002-3044-ef11-8409-6045bd0303b3
  configuration:
    channels:
      - id: Telephony
        channelId: Directline
        channelSpecifier: Telephony
        displayName: Telephony        

    isTelephonyEnabled: true

  language: 1033
  supportedLanguages:
    - 1033

components:
  - kind: DialogComponent
    displayName: Conversation Start
    parentBotId: 4a1f5002-3044-ef11-8409-6045bd0303b3
    description: Conversation Starter
    schemaName: topic.ConversationStart
    dialog:
      beginDialog:
        kind: OnConversationStart
        id: main
        actions:
          - kind: SendActivity
            id: sendMessage_M0LuhV
            activity:
              text:
                - Hello, I'm {System.Bot.Name}. How can I help?
              speak:
                - Hello. Thank you for calling {System.Bot.Name}. Please listen closely to the following options.
          - kind: BeginDialog
            id: DUtdtc
            dialog: topic.abc
  - kind: DialogComponent
    displayName: Escalate
    parentBotId: 4a1f5002-3044-ef11-8409-6045bd0303b3
    description: This system topic is triggered when the user indicates they would like to speak to a human agent. You can configure how the bot will handle human hand-off scenarios in the bot settings. If your bot does not handle escalations, this topic should be disabled.
    schemaName: topic.Escalate
    dialog:
      startBehavior: CancelOtherTopics
      beginDialog:
        kind: OnEscalate
        id: main
        intent:
          displayName: Escalate
          includeInOnSelectIntent: false
          triggerQueries:
            - Talk to agent
            - Talk to a person
            - Talk to someone
            - Call back
            - Call customer service
            - Call me please
            - Call support
            - Call technical support
            - Can an agent call me
            - operator
            - representative
            - Can I get in touch with someone else
            - Can I get real agent support
            - Can I get transferred to a person to call
            - Can I have a call in number Or can I be called
            - Can I have a representative call me
            - Can I schedule a call
            - Can I speak to a representative
            - Can I talk to a human
            - Can I talk to a human assistant
            - Can someone call me
            - Chat with a human
            - Chat with a representative
            - Chat with agent
            - Chat with someone please
            - Connect me to a live agent
            - Connect me to a person
            - Could some one contact me by phone
            - Customer agent
            - Customer representative
            - Customer service
            - I need a manager to contact me
            - I need customer service
            - I need help from a person
            - I need to speak with a live argent
            - I need to talk to a specialist please
            - I want to talk to customer service
            - I want to proceed with live support
            - I want to speak with a consultant
            - I want to speak with a live tech
            - I would like to speak with an associate
            - I would like to talk to a technician
            - Talk with tech support member
        actions:
          - kind: SendActivity
            id: sendMessage_s39DCt
            conversationOutcome: Escalated
            activity: Escalating to a live agent is not currently configured for this bot, however this is where the bot could provide information about how to get in touch with someone another way.  Is there anything else I can help you with?
  - kind: DialogComponent
    displayName: Fallback
    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3
    description: This system topic triggers when the user's utterance does not match any existing topics.
    schemaName: topic.Fallback
    dialog:
      beginDialog:
        kind: OnUnknownIntent
        id: main
        actions:
          - kind: ConditionGroup
            id: conditionGroup_LktzXw
            conditions:
              - id: conditionItem_tlGIVo
                condition: |
                  =If(System.FallbackCount < 3, true, false)
                actions:
                  - kind: SendActivity
                    id: sendMessage_QZreqo
                    activity: I'm sorry, I'm not sure how to help with that. Can you try rephrasing?
            elseActions:
              - kind: BeginDialog
                id: 5aXj5M
                dialog: topic.Escalate
  - kind: DialogComponent
    displayName: Start Over
    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3
    schemaName: topic.StartOver
    dialog:
      beginDialog:
        kind: OnRecognizedIntent
        id: main
        intent:
          displayName: Start Over
          includeInOnSelectIntent: false
          triggerQueries:
            - let's begin again
            - start over
            - start again
            - restart
            - main menu
            - i need something else
        actions:
          - kind: Question
            id: question_zguoVV
            alwaysPrompt: false
            variable: init:Topic.Confirm
            prompt: Are you sure you want to restart the conversation?
            entity: BooleanPrebuiltEntity
          - kind: ConditionGroup
            id: conditionGroup_lvx2zV
            conditions:
              - id: conditionItem_sVQtHa
                condition: |
                  =If(Topic.Confirm = true, true, false)
                actions:
                  - kind: BeginDialog
                    id: 0YKYsy
                    dialog: topic.ResetConversation
            elseActions:
              - kind: SendActivity
                id: sendMessage_lk2CyQ
                activity: Ok. Let's carry on.
  - kind: DialogComponent
    displayName: Reset Conversation
    parentBotId: 68a3648c-534b-ef11-a317-6045bd0303b3
    schemaName: topic.ResetConversation
    dialog:
      startBehavior: UseLatestPublishedContentAndCancelOtherTopics
      beginDialog:
        kind: OnSystemRedirect
        id: main
        actions:
          - kind: ClearAllVariables
            id: clearAllVariables_73bTFR
            variables: ConversationScopedVariables
          - kind: BeginDialog
            id: 21ppQE
            dialog: topic.abc

