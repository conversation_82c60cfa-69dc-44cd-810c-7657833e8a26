<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="JWTToken" value="empty" type="String"/>
  <session-mapping key="ratePlan" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="eligibleForDataAddons" value="empty" type="String"/>
  <session-mapping key="GetAvailableFeatureOffers_VS1001.status" value="Success" type="string"/>
  <session-mapping key="GlobalVars.subscriberFuturePricePlanInd" value="true" type="string"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="suspended" type="string"/>
  <session-mapping key="ifFromCurrentFeatures" value="true" type="boolean"/>
  <session-mapping key="isThirdPartyFeature" value="true" type="boolean"/>
  <session-mapping key="addFeatureAction" value="Custom after selected" type="string"/>
  <session-mapping key="playOneFeature" value="true" type="string"/>
  <session-mapping key="heardThirdPartyInfo" value="true" type="string"/>
  <session-mapping key="fromIOM" value="true" type="string"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="boolean"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="serviceIndicatorAddMessageEnglish" value="" type="string"/>
  <session-mapping key="serviceIndicatorAddMessageSpanish" value="" type="string"/>
  <session-mapping key="addServiceIndicator" value="Transfer after selected" type="string"/>
</session-mappings>
