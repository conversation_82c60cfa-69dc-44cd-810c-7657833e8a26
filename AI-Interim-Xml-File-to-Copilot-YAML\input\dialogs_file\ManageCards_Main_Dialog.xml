<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="ManageCards_Main_Dialog">
    <decision-state id="MW0999_CheckIfNeedPIN_DS">
      <if cond="GlobalVars.loggedIn == true">
        <action next="MW1000_ManageCardStart_JDA"/>
        <else>
          <session-mapping key="GlobalVars.fromManageCards" value="true" type="Boolean"/>
          <action next="MW1110_GoToAutoPay_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="MW1110_GoToAutoPay_SD">
      <gotodialog next="AutoPay_Main_Dialog"/>
      <action next="MW1110_GoToAutoPay_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MW1110_GoToAutoPay_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="MW1000_ManageCardStart_DS">
      <session-mapping key="GlobalVars.expDateInPast" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.isAutopayEnrolled" expr="GlobalVars.GetAccountDetails.isAutopayEnabled"/>
      <if cond="GlobalVars.GetAccountDetails.hasAutopayPending ==  true">
        <action next="MW1012_UpdateCardsNotAllowed_PP"/>
        <elseif cond="GlobalVars.useLastPaymentCard == true">
          <action next="MW1005_GetWalletItems_DB_DA"/>
        </elseif>
        <elseif cond="GlobalVars.tag == 'cancel-autopay'">
          <action next="MW1005_GetWalletItems_DB_DA"/>
        </elseif>
        <else>
          <action next="MW1005_GetWalletItems_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="MW1005_GetWalletItems_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <data-access id="GetWalletItems" classname="com.nuance.metro.dataaccess.GetWalletItems">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="JWTToken"/>
          <input-variable name="sessionID"/>
        </inputs>
        <outputs>
          <output-variable name="brand"/>
          <output-variable name="lastFourDigitsOfCard"/>
          <output-variable name="savedCardPreference"/>
          <output-variable name="walletItems[n].displayName" mask="true"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.GetWalletItems" expr="GetWalletItems"/>
        <session-mapping key="GlobalVars.GetWalletInfo" expr="GetWalletItems"/>
        <if cond="GetWalletItems.status.toUpperCase() == 'SUCCESS'">
          <if cond="GlobalVars.useLastPaymentCard == true">
            <action next="MW1602_CreateSPMItem_DB_DA"/>
          </if>
          <if cond="GlobalVars.tag == 'cancel-autopay'">
            <if cond="GlobalVars.GetWalletItems.walletItems.length == 0">
              <action next="MW1099_GoToTransfer_SD"/>
              <else>
                <session-mapping key="GlobalVars.autopay" value="false" type="Boolean"/>
                <action next="MW1600_UpdateSPMItem_DB_DA"/>
              </else>
            </if>
            <else>
              <action next="MW1010_CheckIntentTask_JDA"/>
            </else>
          </if>
          <else>
            <action next="MW1099_GoToTransfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="MW1010_CheckIntentTask_DS">
      <if cond="GlobalVars.manageCardTask ==  'saveMyCard'">
        <action next="MW1602_CreateSPMItem_DB_DA"/>
        <elseif cond="GlobalVars.manageCardTask == 'MainMC'">
          <action next="MW1020_ManageCardsMenu_DM"/>
        </elseif>
        <else>
          <if cond="GlobalVars.GetWalletItems.walletItems.length == 1">
            <action next="MW1015_OneCardInWallet_DM"/>
            <elseif cond="GlobalVars.GetWalletItems.walletItems.length &gt; 1">
              <action next="MW1070_ChooseFromCardList_DM"/>
            </elseif>
            <else>
              <action next="MW1200_AskDebitOrCredit_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="MW1012_UpdateCardsNotAllowed_PP">
      <audio>
        <prompt id="MW1012_out_01">
          <prompt-segments>
            <audiofile src="MW1012_out_01.wav" text="You currently have an auto payment in process Once that payment has been completed you will able to update card information"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <dm-state id="MW1015_OneCardInWallet_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.paymentOptionId" expr="GlobalVars.GetWalletItems.walletItems[0].paymentOptionId"/>
          <session-mapping key="GlobalVars.autopay" value="true" type="Boolean"/>
          <action next="MW1600_UpdateSPMItem_DB_DA"/>
        </action>
        <action label="false">
          <action next="MW1200_AskDebitOrCredit_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayOneCardInWallet"/>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayOneCardInWallet"/>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayOneCardInWallet"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1015_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1015_nm2_01.wav" text="If you d like to use the payment card you have saved as your autopay card say  yes  or press 1  Or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1015_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1015_nm2_01.wav" text="If you d like to use the payment card you have saved as your autopay card say  yes  or press 1  Or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayOneCardInWallet"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1015_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1015_nm2_01.wav" text="If you d like to use the payment card you have saved as your autopay card say  yes  or press 1  Or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1015_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1015_nm2_01.wav" text="If you d like to use the payment card you have saved as your autopay card say  yes  or press 1  Or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayOneCardInWallet"/>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MW1015_OneCardInWallet_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1015_OneCardInWallet_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MW1020_ManageCardsMenu_DM" type="CUST">
      <session-mapping key="numDaysBtwPayAndCurrentDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String"/>
      <success>
        <session-mapping key="firstTimeInMW1020" value="false" type="Boolean"/>
        <action label="remove-card">
          <session-mapping key="GlobalVars.manageCardTask" expr="'removeCard'"/>
          <if cond="GlobalVars.GetWalletItems.walletItems.length == 0">
            <action next="MW1045_NoSavedCards_PP"/>
            <elseif cond="GlobalVars.GetWalletItems.walletItems.length &gt; 1">
              <action next="MW1070_ChooseFromCardList_DM"/>
            </elseif>
            <else>
              <if cond="GlobalVars.isAutopayEnrolled == true">
                <action next="MW1065_RemoveAutopayCard_DM"/>
                <else>
                  <action next="MW1075_ConfirmCard_DM"/>
                </else>
              </if>
            </else>
          </if>
        </action>
        <action label="change-primary_card">
          <session-mapping key="GlobalVars.manageCardTask" expr="'changePrimaryCard'"/>
          <if cond="GlobalVars.isAutopayEnrolled == true">
            <if cond="numDaysBtwPayAndCurrentDate &lt; 7">
              <audio>
                <prompt id="MW1020_out_01">
                  <prompt-segments>
                    <audiofile src="MW1020_out_01.wav" text="You are too close to your next autopayment to update your primary card at this time  You can do this after  your next auto payment is complete"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="MW1020_ManageCardsMenu_DM"/>
              <elseif cond="GlobalVars.GetWalletItems.walletItems.length &gt; 1">
                <action next="MW1070_ChooseFromCardList_DM"/>
              </elseif>
              <else>
                <action next="MW1200_AskDebitOrCredit_DM"/>
              </else>
            </if>
            <else>
              <action next="MW1200_AskDebitOrCredit_DM"/>
            </else>
          </if>
        </action>
        <action label="update-exp">
          <session-mapping key="GlobalVars.manageCardTask" expr="'updateExp'"/>
          <if cond="GlobalVars.GetWalletItems.walletItems.length == 0">
            <action next="MW1045_NoSavedCards_PP"/>
            <elseif cond="GlobalVars.GetWalletItems.walletItems.length &gt; 1">
              <action next="MW1070_ChooseFromCardList_DM"/>
            </elseif>
            <else>
              <action next="MW1075_ConfirmCard_DM"/>
            </else>
          </if>
        </action>
        <action label="add-card">
          <session-mapping key="GlobalVars.manageCardTask" expr="'addCard'"/>
          <action next="MW1200_AskDebitOrCredit_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1020_ini_01" cond="firstTimeInMW1020 == true">
                <prompt-segments>
                  <audiofile src="MW1020_ini_01.wav" text="Manage Card Menu  You can say  replace primary payment card    add a card ,  remove a card , or  update expiration date ,"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1020_ini_02" cond="firstTimeInMW1020 == false">
                <prompt-segments>
                  <audiofile src="MW1020_ini_02.wav" text="If you re finished you can simply hang up  Otherwise you can say  replace primary payment card ,  add a card ,  remove a card ,  or  update expiration date "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1020_ini_01" cond="firstTimeInMW1020 == true">
                <prompt-segments>
                  <audiofile src="MW1020_ini_01.wav" text="Manage Card Menu  You can say  replace primary payment card    add a card ,  remove a card , or  update expiration date ,"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1020_ini_02" cond="firstTimeInMW1020 == false">
                <prompt-segments>
                  <audiofile src="MW1020_ini_02.wav" text="If you re finished you can simply hang up  Otherwise you can say  replace primary payment card ,  add a card ,  remove a card ,  or  update expiration date "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1020_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1020_nm1_01.wav" text="Please say  replace primary payment card    add a card ,  remove a card , or  update expiration date ,"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1020_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1020_nm2_01.wav" text="Please say  replace primary payment card  or press 1  add a card  or press 2  remove a card  or press 3  update expiration date or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1020_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1020_nm2_01.wav" text="Please say  replace primary payment card  or press 1  add a card  or press 2  remove a card  or press 3  update expiration date or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1020_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1020_nm1_01.wav" text="Please say  replace primary payment card    add a card ,  remove a card , or  update expiration date ,"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1020_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1020_nm2_01.wav" text="Please say  replace primary payment card  or press 1  add a card  or press 2  remove a card  or press 3  update expiration date or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1020_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1020_nm2_01.wav" text="Please say  replace primary payment card  or press 1  add a card  or press 2  remove a card  or press 3  update expiration date or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1020_ManageCardsMenu_DM_reinvoke"/>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MW1020_ManageCardsMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1020_ManageCardsMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="confirmation_nomatch_1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="MW1045_NoSavedCards_PP">
      <audio>
        <prompt id="MW1045_out_01">
          <prompt-segments>
            <audiofile src="MW1045_out_01.wav" text="You currently don t have any cards saved on your account"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="MW1020_ManageCardsMenu_DM"/>
    </play-state>

    <dm-state id="MW1065_RemoveAutopayCard_DM" type="CUST">
      <session-mapping key="isAutopayEligPlanExists" value="GlobalVars.GetAccountDetails.isAutopayEligPlanExists" type="String"/>
      <session-mapping key="paymentOptionId" value="GlobalVars.GetWalletItems.walletItems[0].paymentOptionId" type="String"/>
      <session-mapping key="numDaysBtwPayAndCurrentDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String"/>
      <success>
        <action label="replace-card">
          <session-mapping key="GlobalVars.manageCardTask" expr="'ReplaceAutopayCard'"/>
          <audio>
            <prompt id="MW1065_out_01">
              <prompt-segments>
                <audiofile src="MW1065_out_01.wav" text="Ok, we ll need to remove this card from your account before we can add a new card One Moment"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="MW1601_DeleteSPMItem_DB_DA"/>
        </action>
        <action label="cancel-autopay">
          <session-mapping key="GlobalVars.tag" expr="'cancel-autopay'"/>
          <if cond="numDaysBtwPayAndCurrentDate &lt;= 7">
            <action next="MW1099_GoToTransfer_SD"/>
            <else>
              <session-mapping key="GlobalVars.autopay" value="false" type="Boolean"/>
              <action next="MW1601_DeleteSPMItem_DB_DA"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayRemoveAutopayCardDM"/>
              </prompt>
              <prompt id="MW1065_ini_03" cond="isAutopayEligPlanExists != true">
                <prompt-segments>
                  <audiofile src="MW1065_ini_03.wav" text="If you remove this card, your autopay will deactivate  Would you rather  replace the card  or  cancel autopay ?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1065_ini_04" cond="isAutopayEligPlanExists == true">
                <prompt-segments>
                  <audiofile src="MW1065_ini_04.wav" text="If you remove this card, your autopay will be deactivated and you would lose your autopay discount  Would you rather replace the card or cancel autopay? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayRemoveAutopayCardDM"/>
              </prompt>
              <prompt id="MW1065_ini_03" cond="isAutopayEligPlanExists != true">
                <prompt-segments>
                  <audiofile src="MW1065_ini_03.wav" text="If you remove this card, your autopay will deactivate  Would you rather  replace the card  or  cancel autopay ?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1065_ini_04" cond="isAutopayEligPlanExists == true">
                <prompt-segments>
                  <audiofile src="MW1065_ini_04.wav" text="If you remove this card, your autopay will be deactivated and you would lose your autopay discount  Would you rather replace the card or cancel autopay? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1065_ni1_01">
                <prompt-segments>
                  <audiofile src="MW1065_ni1_01.wav" text="You have one card saved and it is used  for your automatic payments If you remove this card, your autopay will deactivate  Would you rather  replace the card  or  cancel autopay ?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1065_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1065_nm2_01.wav" text="Please say  replace the card  or press 1   Cancel autopay  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1065_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1065_nm2_01.wav" text="Please say  replace the card  or press 1   Cancel autopay  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1065_nm1_01" cond="isAutopayEligPlanExists != true">
                <prompt-segments>
                  <audiofile src="MW1065_nm1_01.wav" text="If you remove this card, your autopay will deactivate  please say  replace the card  or  cancel autopay ?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1065_nm1_02" cond="isAutopayEligPlanExists == true">
                <prompt-segments>
                  <audiofile src="MW1065_nm1_02.wav" text="If you remove this card, your autopay will be deactivated and you would lose your autopay discount  Would you rather 'replace the card' or 'cancel autopay'? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1065_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1065_nm2_01.wav" text="Please say  replace the card  or press 1   Cancel autopay  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1065_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1065_nm2_01.wav" text="Please say  replace the card  or press 1   Cancel autopay  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayRemoveAutopayCardDM"/>
              </prompt>
              <prompt id="MW1065_ini_03" cond="isAutopayEligPlanExists != true">
                <prompt-segments>
                  <audiofile src="MW1065_ini_03.wav" text="If you remove this card, your autopay will deactivate  Would you rather  replace the card  or  cancel autopay ?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1065_ini_04" cond="isAutopayEligPlanExists == true">
                <prompt-segments>
                  <audiofile src="MW1065_ini_04.wav" text="If you remove this card, your autopay will be deactivated and you would lose your autopay discount  Would you rather replace the card or cancel autopay? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MW1065_RemoveAutopayCard_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1065_RemoveAutopayCard_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="confirmation_nomatch_1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MW1066_RemoveAutopayCardMoreCards_DM" type="CUST">
      <session-mapping key="isAutopayEligPlanExists" value="GlobalVars.GetAccountDetails.isAutopayEligPlanExists" type="String"/>
      <session-mapping key="numDaysBtwPayAndCurrentDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String"/>
      <success>
        <action label="cancel-autopay">
          <session-mapping key="GlobalVars.tag" expr="'cancel-autopay'"/>
          <if cond="numDaysBtwPayAndCurrentDate &lt;= 7">
            <action next="MW1099_GoToTransfer_SD"/>
            <else>
              <session-mapping key="GlobalVars.autopay" value="false" type="Boolean"/>
              <action next="MW1601_DeleteSPMItem_DB_DA"/>
            </else>
          </if>
        </action>
        <action label="remove-different_card">
          <action next="MW1070_ChooseFromCardList_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1066_ini_01" cond="isAutopayEligPlanExists != true">
                <prompt-segments>
                  <audiofile src="MW1066_ini_01.wav" text="The card you re trying to remove is being used as your primary auto payment card  If you remove this card, your autopay will deactivate  You can say   cancel autopay  or  remove a different card "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1066_ini_02" cond="isAutopayEligPlanExists == true">
                <prompt-segments>
                  <audiofile src="MW1066_ini_02.wav" text="The card you're trying to remove is being used as your primary auto payment card  If you remove this card, your autopay will be deactivated and you will lose your monthly discount  You can say  cancel autopay or remove a different card "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1066_ini_01" cond="isAutopayEligPlanExists != true">
                <prompt-segments>
                  <audiofile src="MW1066_ini_01.wav" text="The card you re trying to remove is being used as your primary auto payment card  If you remove this card, your autopay will deactivate  You can say   cancel autopay  or  remove a different card "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1066_ini_02" cond="isAutopayEligPlanExists == true">
                <prompt-segments>
                  <audiofile src="MW1066_ini_02.wav" text="The card you're trying to remove is being used as your primary auto payment card  If you remove this card, your autopay will be deactivated and you will lose your monthly discount  You can say  cancel autopay or remove a different card "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1066_ni1_01">
                <prompt-segments>
                  <audiofile src="MW1066_ni1_01.wav" text="You have one card saved and it is used  for your automatic payments If you remove this card, your autopay will deactivate  Would you rather 'replace the card' or' cancel autopay'? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1066_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1066_nm2_01.wav" text="Please say  remove a different card  or press 1   Cancel autopay  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1066_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1066_nm2_01.wav" text="Please say  remove a different card  or press 1   Cancel autopay  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1066_nm1_01" cond="isAutopayEligPlanExists != true">
                <prompt-segments>
                  <audiofile src="MW1066_nm1_01.wav" text="If you remove this card, your autopay will deactivate Please say  cancel auto pay  or  remove a different card "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1066_nm1_02" cond="isAutopayEligPlanExists == true">
                <prompt-segments>
                  <audiofile src="MW1066_nm1_02.wav" text="If you remove this card, your autopay will be deactivated and you will lose your monthly discount  You can say  'cancel autopay' or 'remove a different card' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1066_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1066_nm2_01.wav" text="Please say  remove a different card  or press 1   Cancel autopay  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1066_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1066_nm2_01.wav" text="Please say  remove a different card  or press 1   Cancel autopay  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1066_ini_01" cond="isAutopayEligPlanExists != true">
                <prompt-segments>
                  <audiofile src="MW1066_ini_01.wav" text="The card you re trying to remove is being used as your primary auto payment card  If you remove this card, your autopay will deactivate  You can say   cancel autopay  or  remove a different card "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1066_ini_02" cond="isAutopayEligPlanExists == true">
                <prompt-segments>
                  <audiofile src="MW1066_ini_02.wav" text="The card you're trying to remove is being used as your primary auto payment card  If you remove this card, your autopay will be deactivated and you will lose your monthly discount  You can say  cancel autopay or remove a different card "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MW1066_RemoveAutopayCardMoreCards_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1066_RemoveAutopayCardMoreCards_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MW1070_ChooseFromCardList_DM" type="CUST">
      <session-mapping key="manageCardTask" value="GlobalVars.manageCardTask" type="String"/>
      <session-mapping key="paymentOptions" value="" type="String"/>
      <session-mapping key="isAutopayCard" value="false" type="Boolean"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <success>
        <action label="add-new_card">
          <audio>
            <prompt id="MW1070_out_02">
              <prompt-segments>
                <audiofile src="MW1070_out_02.wav" text="Ok, let s add a new card"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="MW1200_AskDebitOrCredit_DM"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.paymentOptionId" expr="MW1070_ChooseFromCardList_DM.returnvalue"/>
          <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'106'"/>
          <if cond="manageCardTask == 'removeCard'">
            <if cond="isAutopayCard == true">
              <action next="MW1066_RemoveAutopayCardMoreCards_DM"/>
              <else>
                <action next="MW1601_DeleteSPMItem_DB_DA"/>
              </else>
            </if>
            <elseif cond="manageCardTask == 'updateExp'">
              <action next="MW1300_GetExpirationDate_DM"/>
            </elseif>
            <elseif cond="manageCardTask == 'changePrimaryCard'">
              <if cond="GlobalVars.isAutopayEnrolled == true">
                <session-mapping key="GlobalVars.autopay" value="true" type="Boolean"/>
                <action next="MW1600_UpdateSPMItem_DB_DA"/>
                <else>
                  <session-mapping key="GlobalVars.defaultPaymentMethod" value="true" type="Boolean"/>
                  <action next="MW1600_UpdateSPMItem_DB_DA"/>
                </else>
              </if>
            </elseif>
            <else>
              <audio>
                <prompt id="MW1070_out_01">
                  <prompt-segments>
                    <audiofile src="MW1070_out_01.wav" text="Thanks"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.autopay" value="true" type="Boolean"/>
              <action next="MW1600_UpdateSPMItem_DB_DA"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1070_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_01.wav" text="Here s what you have saved  When you hear the card you d like to remove just tell me the last 4 digits"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_02" cond="manageCardTask == 'updateExp'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_02.wav" text="Here s what you have saved   When you hear the card you d like to update just tell me the last 4 digits"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_03" cond="manageCardTask == 'changePrimaryCard'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_03.wav" text="Here s what you have saved  When you hear the card you d like to use as your primary just tell me the last 4 digits  Or say Add new card"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_04" cond="!((manageCardTask == 'changePrimaryCard') || (manageCardTask == 'updateExp') || (manageCardTask == 'removeCard'))">
                <prompt-segments>
                  <audiofile src="MW1070_ini_04.wav" text="Here are the cards you have saved When you hear the one you d like to use as your primary payment card just tell me the last 4 digits  Or say Add a new card"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayChooseFromCardList"/>
              </prompt>
              <prompt id="MW1070_ini_08" cond="manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_08.wav" text="Just say or enter the four digits for the card you want to use today  You can also say   repeat  or  add a new card "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_09" cond="!(manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1070_ini_09.wav" text="Just say or enter the four digits for the card you re calling about  You can also say  repeat "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1070_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_01.wav" text="Here s what you have saved  When you hear the card you d like to remove just tell me the last 4 digits"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_02" cond="manageCardTask == 'updateExp'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_02.wav" text="Here s what you have saved   When you hear the card you d like to update just tell me the last 4 digits"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_03" cond="manageCardTask == 'changePrimaryCard'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_03.wav" text="Here s what you have saved  When you hear the card you d like to use as your primary just tell me the last 4 digits  Or say Add new card"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_04" cond="!((manageCardTask == 'changePrimaryCard') || (manageCardTask == 'updateExp') || (manageCardTask == 'removeCard'))">
                <prompt-segments>
                  <audiofile src="MW1070_ini_04.wav" text="Here are the cards you have saved When you hear the one you d like to use as your primary payment card just tell me the last 4 digits  Or say Add a new card"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayChooseFromCardList"/>
              </prompt>
              <prompt id="MW1070_ini_08" cond="manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_08.wav" text="Just say or enter the four digits for the card you want to use today  You can also say   repeat  or  add a new card "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_09" cond="!(manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1070_ini_09.wav" text="Just say or enter the four digits for the card you re calling about  You can also say  repeat "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1070_nm1_04">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_04.wav" text="Here's the list again "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_02" cond="manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_02.wav" text="When you hear the one you d like to use as your primary card just say or enter the last 4 digits  Or say Add new card"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_05" cond="(manageCardTask == 'removeCard') &amp;&amp; !(manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_05.wav" text="Please Say or enter the last four digits for the card you'd like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_03" cond="!(manageCardTask == 'removeCard') &amp;&amp; !(manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_03.wav" text="Please Ssay or enter the last four digits for the card you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayChooseFromCardList"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_04">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_04.wav" text="Here's the list again "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_02" cond="manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_02.wav" text="When you hear the one you d like to use as your primary card just say or enter the last 4 digits  Or say Add new card"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_05" cond="(manageCardTask == 'removeCard') &amp;&amp; !(manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_05.wav" text="Please Say or enter the last four digits for the card you'd like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_03" cond="!(manageCardTask == 'removeCard') &amp;&amp; !(manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_03.wav" text="Please Ssay or enter the last four digits for the card you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayChooseFromCardList"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="MW1070_ChooseFromCardList_DM_noinput_3"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_04">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_04.wav" text="Here's the list again "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_02" cond="manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_02.wav" text="When you hear the one you d like to use as your primary card just say or enter the last 4 digits  Or say Add new card"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_05" cond="(manageCardTask == 'removeCard') &amp;&amp; !(manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_05.wav" text="Please Say or enter the last four digits for the card you'd like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_03" cond="!(manageCardTask == 'removeCard') &amp;&amp; !(manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_03.wav" text="Please Ssay or enter the last four digits for the card you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayChooseFromCardList"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_04">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_04.wav" text="Here's the list again "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_02" cond="manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_02.wav" text="When you hear the one you d like to use as your primary card just say or enter the last 4 digits  Or say Add new card"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_05" cond="(manageCardTask == 'removeCard') &amp;&amp; !(manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_05.wav" text="Please Say or enter the last four digits for the card you'd like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_nm1_03" cond="!(manageCardTask == 'removeCard') &amp;&amp; !(manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1070_nm1_03.wav" text="Please Ssay or enter the last four digits for the card you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayChooseFromCardList"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="MW1070_ChooseFromCardList_DM_nomatch_3"/>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1070_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_01.wav" text="Here s what you have saved  When you hear the card you d like to remove just tell me the last 4 digits"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_02" cond="manageCardTask == 'updateExp'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_02.wav" text="Here s what you have saved   When you hear the card you d like to update just tell me the last 4 digits"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_03" cond="manageCardTask == 'changePrimaryCard'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_03.wav" text="Here s what you have saved  When you hear the card you d like to use as your primary just tell me the last 4 digits  Or say Add new card"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_04" cond="!((manageCardTask == 'changePrimaryCard') || (manageCardTask == 'updateExp') || (manageCardTask == 'removeCard'))">
                <prompt-segments>
                  <audiofile src="MW1070_ini_04.wav" text="Here are the cards you have saved When you hear the one you d like to use as your primary payment card just tell me the last 4 digits  Or say Add a new card"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayChooseFromCardList"/>
              </prompt>
              <prompt id="MW1070_ini_08" cond="manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1070_ini_08.wav" text="Just say or enter the four digits for the card you want to use today  You can also say   repeat  or  add a new card "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1070_ini_09" cond="!(manageCardTask == 'changePrimaryCard' || tag == 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1070_ini_09.wav" text="Just say or enter the four digits for the card you re calling about  You can also say  repeat "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MW1070_ChooseFromCardList_DM.jsp" count="1">
            <param name="paymentOptions" value="paymentOptionsVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="MW1070_ChooseFromCardList_DM_dtmf.jsp" count="1">
            <param name="paymentOptions" value="paymentOptionsVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" cond="lastresult.interpretation.dm_root != 'add-new_card'">
                <param name="className" value="com.nuance.metro.audio.custom.PlayChooseFromCardListConfirmation"/>
                <param name="paymentOptionId" value="lastresult" scope="request" path="interpretation.dm_root"/>
              </prompt>
              <prompt type="custom" cond="lastresult.interpretation.dm_root == 'add-new_card'" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MW1070_ChooseFromCardList_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" cond="lastresult.interpretation.dm_root != 'add-new_card'">
                <param name="className" value="com.nuance.metro.audio.custom.PlayChooseFromCardListConfirmation"/>
                <param name="paymentOptionId" value="lastresult" scope="request" path="interpretation.dm_root"/>
              </prompt>
              <prompt type="custom" cond="lastresult.interpretation.dm_root == 'add-new_card'" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" cond="lastresult.interpretation.dm_root != 'add-new_card'">
                <param name="className" value="com.nuance.metro.audio.custom.PlayChooseFromCardListConfirmation"/>
                <param name="paymentOptionId" value="lastresult" scope="request" path="interpretation.dm_root"/>
              </prompt>
              <prompt type="custom" cond="lastresult.interpretation.dm_root == 'add-new_card'" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MW1075_ConfirmCard_DM" type="YSNO">
      <session-mapping key="manageCardTask" value="GlobalVars.manageCardTask" type="String"/>
      <success>
        <action label="true">
          <if cond="GlobalVars.manageCardTask == 'removeCard'">
            <action next="MW1601_DeleteSPMItem_DB_DA"/>
            <else>
              <action next="MW1300_GetExpirationDate_DM"/>
            </else>
          </if>
        </action>
        <action label="false">
          <audio>
            <prompt id="MW1075_out_01">
              <prompt-segments>
                <audiofile src="MW1075_out_01.wav" text="Ok no problem"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="MW1020_ManageCardsMenu_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1075_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_01.wav" text="You have one saved card  So to confirm, you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_02" cond="manageCardTask != 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_02.wav" text="You have one saved card  You d like to update your"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayConfirmCardDM"/>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1075_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_01.wav" text="You have one saved card  So to confirm, you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_02" cond="manageCardTask != 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_02.wav" text="You have one saved card  You d like to update your"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayConfirmCardDM"/>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1075_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_01.wav" text="You have one saved card  So to confirm, you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_02" cond="manageCardTask != 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_02.wav" text="You have one saved card  You d like to update your"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayConfirmCardDM"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_01.wav" text="You have one saved card  So to confirm, you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_02" cond="manageCardTask != 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_02.wav" text="You have one saved card  You d like to update your"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayConfirmCardDM"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_01.wav" text="You have one saved card  So to confirm, you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_02" cond="manageCardTask != 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_02.wav" text="You have one saved card  You d like to update your"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayConfirmCardDM"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_01.wav" text="You have one saved card  So to confirm, you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_02" cond="manageCardTask != 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_02.wav" text="You have one saved card  You d like to update your"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayConfirmCardDM"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_01.wav" text="You have one saved card  So to confirm, you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_02" cond="manageCardTask != 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_02.wav" text="You have one saved card  You d like to update your"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayConfirmCardDM"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_01.wav" text="You have one saved card  So to confirm, you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_02" cond="manageCardTask != 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_02.wav" text="You have one saved card  You d like to update your"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayConfirmCardDM"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1075_ini_01" cond="manageCardTask == 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_01.wav" text="You have one saved card  So to confirm, you d like to remove"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1075_ini_02" cond="manageCardTask != 'removeCard'">
                <prompt-segments>
                  <audiofile src="MW1075_ini_02.wav" text="You have one saved card  You d like to update your"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayConfirmCardDM"/>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MW1075_ConfirmCard_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1075_ConfirmCard_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MW1200_AskDebitOrCredit_DM" type="CUST">
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="tryNewCard" value="GlobalVars.tryNewCard" type="String"/>
      <session-mapping key="manageCardTask" value="GlobalVars.manageCardTask" type="String"/>
      <success>
        <action label="debit">
          <session-mapping key="GlobalVars.cardType" expr="'debit'"/>
          <action next="MW1205_GetCardNumber_DM"/>
        </action>
        <action label="credit">
          <session-mapping key="GlobalVars.cardType" expr="'credit'"/>
          <action next="MW1205_GetCardNumber_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1200_ini_01" cond="tag == 'setup-autopay' &amp;&amp; tryNewCard == true">
                <prompt-segments>
                  <audiofile src="MW1200_ini_01.wav" text="Will this be a debit or credit card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_ini_02" cond="tag == 'setup-autopay' &amp;&amp; tryNewCard != true">
                <prompt-segments>
                  <audiofile src="MW1200_ini_02.wav" text="Will your autopayment card be a debit or a credit card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_ini_03" cond="(manageCardTask == 'ReplaceAutopayCard') &amp;&amp;  (tag != 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1200_ini_03.wav" text="Ok, I have removed that card  Now is the new card going to be a debit or credit card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_ini_04" cond="!(manageCardTask == 'ReplaceAutopayCard') &amp;&amp;  (tag != 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1200_ini_04.wav" text="Is this card a debit or credit card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1200_ini_01" cond="tag == 'setup-autopay' &amp;&amp; tryNewCard == true">
                <prompt-segments>
                  <audiofile src="MW1200_ini_01.wav" text="Will this be a debit or credit card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_ini_02" cond="tag == 'setup-autopay' &amp;&amp; tryNewCard != true">
                <prompt-segments>
                  <audiofile src="MW1200_ini_02.wav" text="Will your autopayment card be a debit or a credit card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_ini_03" cond="(manageCardTask == 'ReplaceAutopayCard') &amp;&amp;  (tag != 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1200_ini_03.wav" text="Ok, I have removed that card  Now is the new card going to be a debit or credit card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_ini_04" cond="!(manageCardTask == 'ReplaceAutopayCard') &amp;&amp;  (tag != 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1200_ini_04.wav" text="Is this card a debit or credit card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1200_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1200_nm1_01.wav" text="Please tell me if this card is a debit or credit card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1200_nm2_01.wav" text="For debit press 1  Credit press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1200_nm2_01.wav" text="For debit press 1  Credit press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1200_nm1_01.wav" text="Please tell me if this card is a debit or credit card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1200_nm2_01.wav" text="For debit press 1  Credit press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1200_nm2_01.wav" text="For debit press 1  Credit press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1200_ini_01" cond="tag == 'setup-autopay' &amp;&amp; tryNewCard == true">
                <prompt-segments>
                  <audiofile src="MW1200_ini_01.wav" text="Will this be a debit or credit card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_ini_02" cond="tag == 'setup-autopay' &amp;&amp; tryNewCard != true">
                <prompt-segments>
                  <audiofile src="MW1200_ini_02.wav" text="Will your autopayment card be a debit or a credit card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_ini_03" cond="(manageCardTask == 'ReplaceAutopayCard') &amp;&amp;  (tag != 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1200_ini_03.wav" text="Ok, I have removed that card  Now is the new card going to be a debit or credit card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1200_ini_04" cond="!(manageCardTask == 'ReplaceAutopayCard') &amp;&amp;  (tag != 'setup-autopay')">
                <prompt-segments>
                  <audiofile src="MW1200_ini_04.wav" text="Is this card a debit or credit card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MW1200_AskDebitOrCredit_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1200_AskDebitOrCredit_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MW1205_GetCardNumber_DM" type="CUST">
      <session-mapping key="failedChecksum" value="GlobalVars.failedChecksum" type="String"/>
      <success>
        <action label="default">
          <session-mapping key="GlobalVars.bankCardNumber" expr="MW1205_GetCardNumber_DM.returnvalue"/>
          <if cond="GlobalVars.bankCardNumber.substring(0,2) == '34' || GlobalVars.bankCardNumber.substring(0,2) == '37'">
            <session-mapping key="GlobalVars.cardTypeAmex" value="true" type="Boolean"/>
            <else>
              <session-mapping key="GlobalVars.cardTypeAmex" value="false" type="Boolean"/>
            </else>
          </if>
          <if cond="GlobalVars.bankCardNumber.substring(0,2) == '65' || GlobalVars.bankCardNumber.substring(0,2) == '64' || GlobalVars.bankCardNumber.substring(0,2) == '62' || GlobalVars.bankCardNumber.substring(0,4) == '6011'">
            <session-mapping key="GlobalVars.unsupportedCardIssuer" expr="'discover'"/>
            <else>
              <session-mapping key="GlobalVars.unsupportedCardIssuer" expr="''"/>
            </else>
          </if>
          <action next="MW1208_EvaluateCardEntered_JDA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1205_ini_01" cond="failedChecksum == true">
                <prompt-segments>
                  <audiofile src="MW1205_ini_01.wav" text="Hmm, I had trouble with that card  Please tell me the card number once more"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1205_ini_02" cond="failedChecksum != true">
                <prompt-segments>
                  <audiofile src="MW1205_ini_02.wav" text="Ok, what s the card number?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1205_ini_01" cond="failedChecksum == true">
                <prompt-segments>
                  <audiofile src="MW1205_ini_01.wav" text="Hmm, I had trouble with that card  Please tell me the card number once more"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1205_ini_02" cond="failedChecksum != true">
                <prompt-segments>
                  <audiofile src="MW1205_ini_02.wav" text="Ok, what s the card number?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1205_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1205_nm1_01.wav" text="What s the card number?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1205_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1205_nm2_01.wav" text="Please say or enter your card number"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1205_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1205_nm2_01.wav" text="Please say or enter your card number"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1205_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1205_nm1_01.wav" text="What s the card number?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1205_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1205_nm2_01.wav" text="Please say or enter your card number"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1205_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1205_nm2_01.wav" text="Please say or enter your card number"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1205_ini_01" cond="failedChecksum == true">
                <prompt-segments>
                  <audiofile src="MW1205_ini_01.wav" text="Hmm, I had trouble with that card  Please tell me the card number once more"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1205_ini_02" cond="failedChecksum != true">
                <prompt-segments>
                  <audiofile src="MW1205_ini_02.wav" text="Ok, what s the card number?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
          <grammars filename="MW1205_GetCardNumber_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1205_GetCardNumber_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration reco_suppress_logs="true" highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1205_cnf_ini_01">
                <prompt-segments>
                  <audiofile src="MW1205_cnf_ini_01.wav" text="that was"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1205_cnf_ini_03">
                <prompt-segments>
                  <audiofile src="MW1205_cnf_ini_03.wav" text="Right?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MW1205_GetCardNumber_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1205_cnf_ini_03">
                <prompt-segments>
                  <audiofile src="MW1205_cnf_ini_03.wav" text="Right?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1205_cnf_ini_03">
                <prompt-segments>
                  <audiofile src="MW1205_cnf_ini_03.wav" text="Right?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="MW1208_EvaluateCardEntered_DS">
      <session-mapping key="isEnteredCardIsSameAsInWallet" value="false" type="Boolean"/>
      <session-mapping key="bankCardNumber" value="GlobalVars.bankCardNumber" type="String"/>
      <if cond="GlobalVars.unsupportedCardIssuer == 'discover'">
        <action next="MW1215_UseOtherCardYN_DM"/>
        <elseif cond="GlobalVars.cardTypeAmex == true">
          <action next="MW1210_Checksum_JDA"/>
        </elseif>
        <else>
          <action next="MW1210_Checksum_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="MW1210_Checksum_DS">
      <if cond="passMod10Checksum(GlobalVars.bankCardNumber)">
        <session-mapping key="GlobalVars.failedChecksum" value="false" type="Boolean"/>
        <action next="MW1300_GetExpirationDate_DM"/>
        <else>
          <if cond="GlobalVars.failedChecksum != true">
            <session-mapping key="GlobalVars.failedChecksum" value="true" type="Boolean"/>
            <action next="MW1205_GetCardNumber_DM"/>
            <else>
              <action next="MW1215_UseOtherCardYN_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <dm-state id="MW1215_UseOtherCardYN_DM" type="YSNO">
      <session-mapping key="unsupportedCardIssuer" value="GlobalVars.unsupportedCardIssuer" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.failedChecksum" value="false" type="Boolean"/>
          <audio>
            <prompt id="MW1215_out_01">
              <prompt-segments>
                <audiofile src="MW1215_out_01.wav" text="Alright"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.tryNewCard" value="true" type="Boolean"/>
          <action next="MW1200_AskDebitOrCredit_DM"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="MW1215_out_02">
              <prompt-segments>
                <audiofile src="MW1215_out_02.wav" text="ok"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperator" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1215_ini_01" cond="unsupportedCardIssuer == 'discover'">
                <prompt-segments>
                  <audiofile src="MW1215_ini_01.wav" text="It looks like you re using a Discover card, and we don t take those right now We *do* take Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1215_ini_02" cond="unsupportedCardIssuer != 'discover'">
                <prompt-segments>
                  <audiofile src="MW1215_ini_02.wav" text="I couldn t validate the card number you gave me"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1215_ini_03">
                <prompt-segments>
                  <audiofile src="MW1215_ini_03.wav" text="Would you like to use a different card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1215_ini_01" cond="unsupportedCardIssuer == 'discover'">
                <prompt-segments>
                  <audiofile src="MW1215_ini_01.wav" text="It looks like you re using a Discover card, and we don t take those right now We *do* take Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1215_ini_02" cond="unsupportedCardIssuer != 'discover'">
                <prompt-segments>
                  <audiofile src="MW1215_ini_02.wav" text="I couldn t validate the card number you gave me"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1215_ini_03">
                <prompt-segments>
                  <audiofile src="MW1215_ini_03.wav" text="Would you like to use a different card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1215_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1215_nm1_01.wav" text="I was unable to validate this card Would you like to try  a different card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1215_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1215_nm2_01.wav" text="I couldn t validate the card number you gave me If you have a different card you can use, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1215_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1215_nm2_01.wav" text="I couldn t validate the card number you gave me If you have a different card you can use, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1215_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1215_nm1_01.wav" text="I was unable to validate this card Would you like to try  a different card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1215_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1215_nm2_01.wav" text="I couldn t validate the card number you gave me If you have a different card you can use, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1215_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1215_nm2_01.wav" text="I couldn t validate the card number you gave me If you have a different card you can use, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1215_ini_01" cond="unsupportedCardIssuer == 'discover'">
                <prompt-segments>
                  <audiofile src="MW1215_ini_01.wav" text="It looks like you re using a Discover card, and we don t take those right now We *do* take Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1215_ini_02" cond="unsupportedCardIssuer != 'discover'">
                <prompt-segments>
                  <audiofile src="MW1215_ini_02.wav" text="I couldn t validate the card number you gave me"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1215_ini_03">
                <prompt-segments>
                  <audiofile src="MW1215_ini_03.wav" text="Would you like to use a different card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MW1215_UseOtherCardYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1215_UseOtherCardYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MW1225_CardAlreadyInWallet_DM" type="YSNO">
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.fromManageCardNode" expr="'MW1225'"/>
          <if cond="tag == 'setup-autopay'">
            <session-mapping key="GlobalVars.autopay" value="true" type="Boolean"/>
            <action next="MW1600_UpdateSPMItem_DB_DA"/>
            <else>
              <session-mapping key="GlobalVars.defaultPaymentMethod" value="true" type="Boolean"/>
              <action next="MW1600_UpdateSPMItem_DB_DA"/>
            </else>
          </if>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.tryNewCard" value="true" type="Boolean"/>
          <audio>
            <prompt id="MW1225_out_01">
              <prompt-segments>
                <audiofile src="MW1225_out_01.wav" text="Ok, let s try another card"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="MW1200_AskDebitOrCredit_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1225_ini_01" cond="tag == 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1225_ini_01.wav" text="This card is already saved in your account So to confirm you d like to use this as your primary autopay card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1225_ini_02" cond="tag != 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1225_ini_02.wav" text="This card is already saved in your account To confirm, you d like this to be your primary payment card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1225_ini_01" cond="tag == 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1225_ini_01.wav" text="This card is already saved in your account So to confirm you d like to use this as your primary autopay card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1225_ini_02" cond="tag != 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1225_ini_02.wav" text="This card is already saved in your account To confirm, you d like this to be your primary payment card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1225_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1225_nm1_01.wav" text="This card is already saved  Would you like to make this your primary payment card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1225_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1225_nm2_01.wav" text="To make this your primary payment card say  yes  or press 1  or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1225_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1225_nm2_01.wav" text="To make this your primary payment card say  yes  or press 1  or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1225_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1225_nm1_01.wav" text="This card is already saved  Would you like to make this your primary payment card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1225_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1225_nm2_01.wav" text="To make this your primary payment card say  yes  or press 1  or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1225_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1225_nm2_01.wav" text="To make this your primary payment card say  yes  or press 1  or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1225_ini_01" cond="tag == 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1225_ini_01.wav" text="This card is already saved in your account So to confirm you d like to use this as your primary autopay card?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1225_ini_02" cond="tag != 'setup-autopay'">
                <prompt-segments>
                  <audiofile src="MW1225_ini_02.wav" text="This card is already saved in your account To confirm, you d like this to be your primary payment card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MW1225_CardAlreadyInWallet_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1225_CardAlreadyInWallet_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="MW1235_GoToNLU_SD">
      <session-mapping key="GlobalVars.tag" expr="undefined"/>
      <gotodialog next="NLUMainMenu_Start_Dialog"/>
      <action next="MW1235_GoToNLU_SD_return"/>
    </subdialog-state>
    <play-state id="MW1230_CardAlreadySaved_PP">
      <audio>
        <prompt id="MW1230_out_02">
          <prompt-segments>
            <audiofile src="MW1230_out_02.wav" text="This card is already saved in your account"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="MW1020_ManageCardsMenu_DM"/>
    </play-state>

    <dm-state id="MW1300_GetExpirationDate_DM" type="CUST">
      <session-mapping key="expDateInPast" value="GlobalVars.expDateInPast" type="String"/>
      <session-mapping key="manageCardTask" value="GlobalVars.manageCardTask" type="String"/>
      <session-mapping key="mw1300Help" value="GlobalVars.MW1300Help" type="String"/>
      <success>
        <session-mapping key="GlobalVars.MW1300Help" value="false" type="Boolean"/>
        <action label="use-different_card">
          <session-mapping key="GlobalVars.tryNewCard" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.expDateInPast" value="false" type="Boolean"/>
          <action next="MW1200_AskDebitOrCredit_DM"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.bankCardDate" expr="MW1300_GetExpirationDate_DM.returnvalue"/>
          <session-mapping key="GlobalVars.bankCardDateIsValid" expr="MW1300_GetExpirationDate_DM.nbestresults[0].interpretation.isValid"/>
          <action next="MW1310_CheckDateFuture_JDA"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperator" value="true" type="Boolean"/>
        </action>
        <action label="help">
          <session-mapping key="GlobalVars.MW1300Help" value="true" type="Boolean"/>
          <audio>
            <prompt id="MW1300_hlp_01">
              <prompt-segments>
                <audiofile src="MW1300_hlp_01.wav" text="Sure, here's more information The expiration date is usually right below your credit card number There are two digits for the month and two for the year We'll need it to make sure your card is active "/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="MW1300_hlp_02">
              <prompt-segments>
                <audiofile src="MW1300_hlp_02.wav" text="Go ahead and enter it now"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="MW1300_GetExpirationDate_DM"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1300_ini_01" cond="mw1300Help != true &amp;&amp; expDateInPast == true">
                <prompt-segments>
                  <audiofile src="MW1300_ini_01.wav" text="Sorry, that expiration date is in the past  What s the expiration date again?  You can also say  use a different card "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1300_ini_02" cond="mw1300Help != true &amp;&amp; manageCardTask == 'updateExp' &amp;&amp; expDateInPast != true">
                <prompt-segments>
                  <audiofile src="MW1300_ini_02.wav" text="Ok, what s the new expiration date?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1300_ini_03" cond="mw1300Help != true &amp;&amp; manageCardTask != 'updateExp' &amp;&amp; expDateInPast != true">
                <prompt-segments>
                  <audiofile src="MW1300_ini_03.wav" text="And the expiration date?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1300_ini_01" cond="mw1300Help != true &amp;&amp; expDateInPast == true">
                <prompt-segments>
                  <audiofile src="MW1300_ini_01.wav" text="Sorry, that expiration date is in the past  What s the expiration date again?  You can also say  use a different card "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1300_ini_02" cond="mw1300Help != true &amp;&amp; manageCardTask == 'updateExp' &amp;&amp; expDateInPast != true">
                <prompt-segments>
                  <audiofile src="MW1300_ini_02.wav" text="Ok, what s the new expiration date?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1300_ini_03" cond="mw1300Help != true &amp;&amp; manageCardTask != 'updateExp' &amp;&amp; expDateInPast != true">
                <prompt-segments>
                  <audiofile src="MW1300_ini_03.wav" text="And the expiration date?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1300_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1300_nm1_01.wav" text="What's the expiration date?  or say 'use a different card' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1300_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1300_nm2_01.wav" text="Please enter the four-digit expiration date  To use a different card press 9"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1300_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1300_nm2_01.wav" text="Please enter the four-digit expiration date  To use a different card press 9"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1300_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1300_nm1_01.wav" text="What's the expiration date?  or say 'use a different card' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1300_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1300_nm2_01.wav" text="Please enter the four-digit expiration date  To use a different card press 9"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1300_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1300_nm2_01.wav" text="Please enter the four-digit expiration date  To use a different card press 9"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1300_ini_01" cond="mw1300Help != true &amp;&amp; expDateInPast == true">
                <prompt-segments>
                  <audiofile src="MW1300_ini_01.wav" text="Sorry, that expiration date is in the past  What s the expiration date again?  You can also say  use a different card "/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1300_ini_02" cond="mw1300Help != true &amp;&amp; manageCardTask == 'updateExp' &amp;&amp; expDateInPast != true">
                <prompt-segments>
                  <audiofile src="MW1300_ini_02.wav" text="Ok, what s the new expiration date?"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1300_ini_03" cond="mw1300Help != true &amp;&amp; manageCardTask != 'updateExp' &amp;&amp; expDateInPast != true">
                <prompt-segments>
                  <audiofile src="MW1300_ini_03.wav" text="And the expiration date?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_mw1310.grxml" dtmfcommandgrammar="GlobalCommands_mw1310_dtmf.grxml">
          <grammars filename="MW1300_GetExpirationDate_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1300_GetExpirationDate_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration reco_suppress_logs="true" highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt type="date" expr="lastresult" path="interpretation.dm_root" cond="lastresult.interpretation.dm_root != 'use-different_card'">
                <param name="dateFormat" value="MMyy"/>
                <param name="playDayOfMonth" value="false"/>
                <param name="playDayOfTheWeek" value="false"/>
                <param name="playYear" value="true"/>
                <param name="intonation" value="m"/>
                <param name="mask" value="true"/>
              </prompt>
              <prompt type="custom" cond="lastresult.interpretation.dm_root == 'use-different_card'" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="gl_cnf_ini_06">
                <prompt-segments>
                  <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MW1300_GetExpirationDate_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="lastresult" path="interpretation.dm_root" cond="lastresult.interpretation.dm_root != 'use-different_card'">
                <param name="dateFormat" value="MMyy"/>
                <param name="playDayOfMonth" value="false"/>
                <param name="playDayOfTheWeek" value="false"/>
                <param name="playYear" value="true"/>
                <param name="intonation" value="m"/>
                <param name="mask" value="true"/>
              </prompt>
              <prompt type="custom" cond="lastresult.interpretation.dm_root == 'use-different_card'" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="MW1300_cnf_ini_04">
                  <prompt-segments>
                    <audiofile src="MW1300_cnf_ini_04.wav" text="Yes?"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="lastresult" path="interpretation.dm_root" cond="lastresult.interpretation.dm_root != 'use-different_card'">
                <param name="dateFormat" value="MMyy"/>
                <param name="playDayOfMonth" value="false"/>
                <param name="playDayOfTheWeek" value="false"/>
                <param name="playYear" value="true"/>
                <param name="intonation" value="m"/>
                <param name="mask" value="true"/>
              </prompt>
              <prompt type="custom" cond="lastresult.interpretation.dm_root == 'use-different_card'" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="MW1300_cnf_ini_04">
                  <prompt-segments>
                    <audiofile src="MW1300_cnf_ini_04.wav" text="Yes?"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="MW1310_CheckDateFuture_DS">
      <if cond="GlobalVars.bankCardDateIsValid == 1">
        <if cond="GlobalVars.manageCardTask == 'updateExp'">
          <action next="MW1600_UpdateSPMItem_DB_DA"/>
          <else>
            <action next="MW1315_GetVerificationCode_DM"/>
          </else>
        </if>
        <else>
          <session-mapping key="GlobalVars.expDateTryCounter" expr="GlobalVars.expDateTryCounter != undefined ? GlobalVars.expDateTryCounter : 1"/>
          <session-mapping key="GlobalVars.expDateInPast" value="true" type="Boolean"/>
          <action next="MW1311_ExpDateCounter_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="MW1311_ExpDateCounter_DS">
      <if cond="GlobalVars.expDateTryCounter == 1">
        <session-mapping key="GlobalVars.expDateTryCounter" expr="GlobalVars.expDateTryCounter +1"/>
        <action next="MW1300_GetExpirationDate_DM"/>
        <else>
          <action next="MW1099_GoToTransfer_SD"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="MW1315_GetVerificationCode_DM" type="CUST">
      <session-mapping key="cardTypeAmex" value="GlobalVars.cardTypeAmex" type="String"/>
      <success>
        <action label="default">
          <session-mapping key="GlobalVars.bankCardCVV" expr="MW1315_GetVerificationCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.VerificationCode_voiceOrDtmf" expr="application.lastresult$.inputmode"/>
          <action next="MW1400_GetBillingZipCode_DM"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.callerSaidOperator" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1315_ini_01" cond="cardTypeAmex == true">
                <prompt-segments>
                  <audiofile src="MW1315_ini_01.wav" text="Now, the four-digit verification code"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_ini_02" cond="cardTypeAmex != true">
                <prompt-segments>
                  <audiofile src="MW1315_ini_02.wav" text="Now, the three digit verification code"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1315_ini_01" cond="cardTypeAmex == true">
                <prompt-segments>
                  <audiofile src="MW1315_ini_01.wav" text="Now, the four-digit verification code"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_ini_02" cond="cardTypeAmex != true">
                <prompt-segments>
                  <audiofile src="MW1315_ini_02.wav" text="Now, the three digit verification code"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1315_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1315_nm1_01.wav" text="Please say or enter the verification code on the card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_nm2_01" cond="cardTypeAmex == true">
                <prompt-segments>
                  <audiofile src="MW1315_nm2_01.wav" text="Please enter the four-digit verification code on the front of your card"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_nm2_02" cond="cardTypeAmex != true">
                <prompt-segments>
                  <audiofile src="MW1315_nm2_02.wav" text="Please enter the three-digit verification code on the back of your card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_nm2_01" cond="cardTypeAmex == true">
                <prompt-segments>
                  <audiofile src="MW1315_nm2_01.wav" text="Please enter the four-digit verification code on the front of your card"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_nm2_02" cond="cardTypeAmex != true">
                <prompt-segments>
                  <audiofile src="MW1315_nm2_02.wav" text="Please enter the three-digit verification code on the back of your card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1315_nm1_01.wav" text="Please say or enter the verification code on the card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_nm2_01" cond="cardTypeAmex == true">
                <prompt-segments>
                  <audiofile src="MW1315_nm2_01.wav" text="Please enter the four-digit verification code on the front of your card"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_nm2_02" cond="cardTypeAmex != true">
                <prompt-segments>
                  <audiofile src="MW1315_nm2_02.wav" text="Please enter the three-digit verification code on the back of your card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_nm2_01" cond="cardTypeAmex == true">
                <prompt-segments>
                  <audiofile src="MW1315_nm2_01.wav" text="Please enter the four-digit verification code on the front of your card"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_nm2_02" cond="cardTypeAmex != true">
                <prompt-segments>
                  <audiofile src="MW1315_nm2_02.wav" text="Please enter the three-digit verification code on the back of your card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1315_ini_01" cond="cardTypeAmex == true">
                <prompt-segments>
                  <audiofile src="MW1315_ini_01.wav" text="Now, the four-digit verification code"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1315_ini_02" cond="cardTypeAmex != true">
                <prompt-segments>
                  <audiofile src="MW1315_ini_02.wav" text="Now, the three digit verification code"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
          <grammars filename="MW1315_GetVerificationCode_DM.jsp" count="1">
            <param name="cardTypeAmex" value="cardTypeAmexVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="MW1315_GetVerificationCode_DM_dtmf.jsp" count="1">
            <param name="cardTypeAmex" value="cardTypeAmexVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration reco_suppress_logs="true" highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="gl_cnf_ini_04">
                <prompt-segments>
                  <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_ini_08">
                <prompt-segments>
                  <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MW1315_GetVerificationCode_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MW1400_GetBillingZipCode_DM" type="CUST">
      <success>
        <action label="default">
          <session-mapping key="GlobalVars.bankCardZip" expr="MW1400_GetBillingZipCode_DM.returnvalue"/>
          <action next="MW1405_PlayTransition_PP"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperator" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1400_ini_01">
                <prompt-segments>
                  <audiofile src="MW1400_ini_01.wav" text="And finally, what s the billing zip code for the card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1400_ini_01">
                <prompt-segments>
                  <audiofile src="MW1400_ini_01.wav" text="And finally, what s the billing zip code for the card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1400_ni1_01">
                <prompt-segments>
                  <audiofile src="MW1400_ni1_01.wav" text="What s the billing zip code where you get the bill for this card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1400_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1400_nm2_01.wav" text="Please enter the billing zip code for the card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1400_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1400_nm2_01.wav" text="Please enter the billing zip code for the card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1400_nm1_01">
                <prompt-segments>
                  <audiofile src="MW1400_nm1_01.wav" text="What s the zip code again?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1400_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1400_nm2_01.wav" text="Please enter the billing zip code for the card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1400_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1400_nm2_01.wav" text="Please enter the billing zip code for the card"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1400_ini_01">
                <prompt-segments>
                  <audiofile src="MW1400_ini_01.wav" text="And finally, what s the billing zip code for the card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
          <grammars filename="MW1400_GetBillingZipCode_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1400_GetBillingZipCode_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration reco_suppress_logs="true" highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="gl_cnf_ini_04">
                <prompt-segments>
                  <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_ini_06">
                <prompt-segments>
                  <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MW1400_GetBillingZipCode_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="MW1405_PlayTransition_PP">
      <audio>
        <prompt id="MW1405_out_01">
          <prompt-segments>
            <audiofile src="MW1405_out_01.wav" text="Thanks, one moment"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="MW1500_ValidateCardOptionsRest_DB_DA"/>
    </play-state>

    <data-access-state id="MW1500_ValidateCardOptionsRest_DB_DA">
      <session-mapping key="paymentType" value="(GlobalVars.callType == 'activate')?'activation':'service'" type="String"/>
      <session-mapping key="paymentMethod" value="GlobalVars.payingWithEWallet?'wallet':'card'" type="String"/>
      <session-mapping key="cardNumber" value="GlobalVars.bankCardNumber" type="String"/>
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount != undefined ? GlobalVars.paymentAmount : 0.0" type="String"/>
      <session-mapping key="expirationDate" value="GlobalVars.bankCardDate" type="String"/>
      <session-mapping key="securityCode" value="GlobalVars.bankCardCVV" type="String"/>
      <session-mapping key="billingZip" value="GlobalVars.bankCardZip" type="String"/>
      <session-mapping key="dueImmediately" value="(GlobalVars.callType == 'activate')?'0.00':GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="serviceAccountBalance" value="(GlobalVars.callType == 'activate')?'0.00':GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="ValidateCardOptionsREST" classname="com.nuance.metro.dataaccess.ValidateCardOptionsREST">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="paymentType"/>
          <input-variable name="paymentMethod"/>
          <input-variable name="cardNumber" mask="true"/>
          <input-variable name="paymentAmount"/>
          <input-variable name="expirationDate" mask="true"/>
          <input-variable name="securityCode" mask="true"/>
          <input-variable name="billingZip" mask="true"/>
          <input-variable name="dueImmediately"/>
          <input-variable name="serviceAccountBalance"/>
          <input-variable name="validatePaymentAmountOnly"/>
        </inputs>
        <outputs>
          <output-variable name="minLimit"/>
          <output-variable name="maxLimit"/>
          <output-variable name="cardStatus"/>
          <output-variable name="cardType"/>
          <output-variable name="cardBrand"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="ValidateCardOptionsREST.status == 'Success'">
          <session-mapping key="GlobalVars.ValidateCardOptions" expr="ValidateCardOptionsREST"/>
          <action next="MW1505_CheckValidationResults_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
            <action next="MW1099_GoToTransfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="MW1505_CheckValidationResults_DS">
      <session-mapping key="manageCardTask" value="GlobalVars.manageCardTask" type="String"/>
      <if cond="GlobalVars.ValidateCardOptions.cardType == 'credit'">
        <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'credit'"/>
        <elseif cond="GlobalVars.ValidateCardOptions.cardType == 'debit'">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'debit'"/>
        </elseif>
      </if>
      <session-mapping key="GlobalVars.cardStatus" expr="GlobalVars.ValidateCardOptions ? GlobalVars.ValidateCardOptions.cardStatus : ''"/>
      <if cond="manageCardTask == 'changePrimaryCard' &amp;&amp; GlobalVars.isAutopayEnrolled == true">
        <session-mapping key="GlobalVars.autopay" value="true" type="Boolean"/>
        <action next="MW1602_CreateSPMItem_DB_DA"/>
        <elseif cond="manageCardTask == 'changePrimaryCard' &amp;&amp; GlobalVars.isAutopayEnrolled != true">
          <session-mapping key="GlobalVars.defaultPaymentMethod" value="true" type="Boolean"/>
          <action next="MW1602_CreateSPMItem_DB_DA"/>
        </elseif>
        <elseif cond="manageCardTask == 'ReplaceAutopayCard'">
          <session-mapping key="GlobalVars.autopay" value="true" type="Boolean"/>
          <action next="MW1602_CreateSPMItem_DB_DA"/>
        </elseif>
        <elseif cond="manageCardTask == 'addCard' &amp;&amp; GlobalVars.isAutopayEnrolled == true">
          <action next="MW1510_AskIfPrimary_DM"/>
        </elseif>
        <elseif cond="manageCardTask == 'addCard' &amp;&amp; GlobalVars.isAutopayEnrolled != true">
          <action next="MW1602_CreateSPMItem_DB_DA"/>
        </elseif>
        <else>
          <session-mapping key="GlobalVars.autopay" value="true" type="Boolean"/>
          <action next="MW1602_CreateSPMItem_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="MW1510_AskIfPrimary_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.autopay" value="true" type="Boolean"/>
          <action next="MW1602_CreateSPMItem_DB_DA"/>
        </action>
        <action label="false">
          <action next="MW1602_CreateSPMItem_DB_DA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MW1510_ini_01">
                <prompt-segments>
                  <audiofile src="MW1510_ini_01.wav" text="Would you like this new card to be used as your new primary autopay card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MW1510_ini_01">
                <prompt-segments>
                  <audiofile src="MW1510_ini_01.wav" text="Would you like this new card to be used as your new primary autopay card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MW1510_ini_01">
                <prompt-segments>
                  <audiofile src="MW1510_ini_01.wav" text="Would you like this new card to be used as your new primary autopay card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1510_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1510_nm2_01.wav" text="If you d like to use this card as you primary autopay pay card say  yes  or press 1  or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1510_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1510_nm2_01.wav" text="If you d like to use this card as you primary autopay pay card say  yes  or press 1  or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1510_ini_01">
                <prompt-segments>
                  <audiofile src="MW1510_ini_01.wav" text="Would you like this new card to be used as your new primary autopay card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1510_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1510_nm2_01.wav" text="If you d like to use this card as you primary autopay pay card say  yes  or press 1  or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MW1510_nm2_01">
                <prompt-segments>
                  <audiofile src="MW1510_nm2_01.wav" text="If you d like to use this card as you primary autopay pay card say  yes  or press 1  or say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MW1510_ini_01">
                <prompt-segments>
                  <audiofile src="MW1510_ini_01.wav" text="Would you like this new card to be used as your new primary autopay card?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MW1510_AskIfPrimary_DM.grxml" count="1"/>
          <dtmfgrammars filename="MW1510_AskIfPrimary_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="MW1600_UpdateSPMItem_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="paymentOptionId" value="GlobalVars.paymentOptionId" type="String"/>
      <session-mapping key="autopay" value="GlobalVars.autopay" type="String"/>
      <session-mapping key="expirationDate" value="GlobalVars.bankCardDate" type="String"/>
      <session-mapping key="defaultPaymentMethod" value="GlobalVars.defaultPaymentMethod" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="manageCardTask" value="GlobalVars.manageCardTask" type="String"/>
      <session-mapping key="isAutopayEnrolled" value="GlobalVars.isAutopayEnrolled" type="String"/>
      <session-mapping key="preferredMethod" value="GlobalVars.cardType" type="String"/>
      <session-mapping key="cardNumber" value="GlobalVars.bankCardNumber" type="String"/>
      <data-access id="UpdateSPMItem" classname="com.nuance.metro.dataaccess.UpdateSPMItem">
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
          <input-variable name="autopay"/>
          <input-variable name="defaultPaymentMethod"/>
          <input-variable name="paymentOptionId" mask="true"/>
          <input-variable name="preferredMethod"/>
          <input-variable name="tag"/>
          <input-variable name="manageCardTask"/>
          <input-variable name="isAutopayEnrolled"/>
          <input-variable name="JWTToken" mask="true"/>
          <input-variable name="cardNumber" mask="true"/>
          <input-variable name="expirationDate" mask="true"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.paymentOptionId" expr=""/>
        <if cond="UpdateSPMItem.status.toUpperCase() == 'SUCCESS'">
          <action next="MW1611_GetAccountDetails_DB_DA"/>
          <else>
            <session-mapping key="GlobalVars.isSPMItemDBFailed" value="true" type="Boolean"/>
            <action next="MW1610_StatusOfUpdate_PP"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <data-access-state id="MW1601_DeleteSPMItem_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="paymentOptionId" value="GlobalVars.paymentOptionId" type="String"/>
      <data-access id="DeleteSPMItem" classname="com.nuance.metro.dataaccess.DeleteSPMItem">
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
          <input-variable name="paymentOptionId" mask="true"/>
          <input-variable name="JWTToken"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.paymentOptionId" expr=""/>
        <if cond="DeleteSPMItem.status.toUpperCase() == 'SUCCESS' ">
          <action next="MW1611_GetAccountDetails_DB_DA"/>
          <else>
            <if cond="DeleteSPMItem.status != 'Success'">
              <session-mapping key="GlobalVars.isSPMItemDBFailed" value="true" type="Boolean"/>
            </if>
            <action next="MW1610_StatusOfUpdate_PP"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <data-access-state id="MW1602_CreateSPMItem_DB_DA">
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="autopay" value="GlobalVars.autopay" type="String"/>
      <session-mapping key="defaultPaymentMethod" value="GlobalVars.defaultPaymentMethod" type="String"/>
      <session-mapping key="paymentType" value="card" type="String"/>
      <session-mapping key="preferredMethod" value="GlobalVars.cardType != undefined ? GlobalVars.cardType : GlobalVars.preferredPaymentMethod" type="String"/>
      <session-mapping key="cardNumber" value="GlobalVars.bankCardNumber" type="String"/>
      <session-mapping key="expirationDate" value="GlobalVars.bankCardDate" type="String"/>
      <session-mapping key="billingZip" value="GlobalVars.bankCardZip" type="String"/>
      <session-mapping key="securityCode" value="GlobalVars.bankCardCVV" type="String"/>
      <data-access id="CreateSPMItem" classname="com.nuance.metro.dataaccess.CreateSPMItem">
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
          <input-variable name="autopay"/>
          <input-variable name="defaultPaymentMethod"/>
          <input-variable name="displayName" mask="true"/>
          <input-variable name="paymentType"/>
          <input-variable name="preferredMethod"/>
          <input-variable name="nameOnCard" mask="true"/>
          <input-variable name="cardNumber" mask="true"/>
          <input-variable name="expirationDate" mask="true"/>
          <input-variable name="billingZip" mask="true"/>
          <input-variable name="securityCode" mask="true"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.recentPayment" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.useLastPaymentCard" value="false" type="Boolean"/>
        <if cond="CreateSPMItem.status.toUpperCase() == 'SUCCESS'">
          <action next="MW1611_GetAccountDetails_DB_DA"/>
          <else>
            <session-mapping key="GlobalVars.isSPMItemDBFailed" value="true" type="Boolean"/>
            <action next="MW1610_StatusOfUpdate_PP"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <play-state id="MW1610_StatusOfUpdate_PP">
      <session-mapping key="isSPMItemDBFailed" value="GlobalVars.isSPMItemDBFailed" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="manageCardTask" value="GlobalVars.manageCardTask" type="String"/>
      <session-mapping key="isAutopayEnrolled" value="GlobalVars.isAutopayEnrolled" type="String"/>
      <audio>
        <if cond="(isSPMItemDBFailed == true) ">
          <prompt id="MW1610_out_01">
            <prompt-segments>
              <audiofile src="MW1610_out_01.wav" text="Sorry, I had some difficulty updating your card information"/>
            </prompt-segments>
          </prompt>
          <elseif cond="(tag == 'cancel-autopay')">
            <prompt id="MW1610_out_02">
              <prompt-segments>
                <audiofile src="MW1610_out_02.wav" text="Ok, your auto pay has been cancelled  Your next payment will need to be paid manually  Please note it may take up to 2 hours for the change to be reflected on your account "/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="(manageCardTask == 'ReplaceAutopayCard')">
            <prompt id="MW1610_out_03">
              <prompt-segments>
                <audiofile src="MW1610_out_03.wav" text="Ok your card has now been replaced with this new card as your primary autopay card"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="(manageCardTask == 'removeCard')">
            <prompt id="MW1610_out_04">
              <prompt-segments>
                <audiofile src="MW1610_out_04.wav" text="Alright, this card has been removed from your account"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="(manageCardTask == 'changePrimaryCard' &amp;&amp; isAutopayEnrolled == true)">
            <prompt id="MW1610_out_05">
              <prompt-segments>
                <audiofile src="MW1610_out_05.wav" text="Ok, this card is now set up as your Primary Payment Card and will be used for your next auto payment"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="(manageCardTask == 'changePrimaryCard' &amp;&amp; isAutopayEnrolled != true)">
            <prompt id="MW1610_out_06">
              <prompt-segments>
                <audiofile src="MW1610_out_06.wav" text="Ok, this card is now set up as your Primary Payment Card"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="(manageCardTask == 'updateExp')">
            <prompt id="MW1610_out_07">
              <prompt-segments>
                <audiofile src="MW1610_out_07.wav" text="Ok, your card s expiration date has been updated"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="MW1610_out_08">
              <prompt-segments>
                <audiofile src="MW1610_out_08.wav" text="Alright, this card has been added to your account"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <action next="MW1615_CheckNextStep_JDA"/>
    </play-state>

    <data-access-state id="MW1611_GetAccountDetails_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.trn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <session-mapping key="haveMDN" value="false" type="Boolean"/>
      <session-mapping key="MDN" value="" type="String"/>
      <data-access id="GetSubscriberDetails" classname="com.nuance.metro.dataaccess.GetSubscriberDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="languageCode"/>
          <input-variable name="sessionId"/>
          <input-variable name="imei"/>
          <input-variable name="clearCache"/>
          <input-variable name="multiLineDetails"/>
        </inputs>
        <outputs>
          <output-variable name="mdn"/>
          <output-variable name="accountNumber" mask="true"/>
          <output-variable name="active"/>
          <output-variable name="serialNumber"/>
          <output-variable name="features"/>
          <output-variable name="thirdPartyFeatures"/>
          <output-variable name="payDate"/>
          <output-variable name="taxInclusive"/>
          <output-variable name="accountStatus"/>
          <output-variable name="dueImmediatelyAmount"/>
          <output-variable name="highSecurity"/>
          <output-variable name="ratePlan"/>
          <output-variable name="marketID"/>
          <output-variable name="language"/>
          <output-variable name="isAutopayEnabled"/>
          <output-variable name="hasPhoneInsurance"/>
          <output-variable name="balance"/>
          <output-variable name="isDeviceAllowed"/>
          <output-variable name="isSuspended"/>
          <output-variable name="accountType"/>
          <output-variable name="validAccount"/>
          <output-variable name="roamingBucket"/>
          <output-variable name="paymentStatus"/>
          <output-variable name="ratePlanAttributesMap"/>
          <output-variable name="networkType"/>
          <output-variable name="securityQuestionCode"/>
          <output-variable name="hasEWallet"/>
          <output-variable name="eligibleForBillCycleReset"/>
          <output-variable name="billCycleLength"/>
          <output-variable name="firstName"/>
          <output-variable name="lastName"/>
          <output-variable name="eligibleForUnhotlineCheck"/>
          <output-variable name="securityQuestionString"/>
          <output-variable name="iccid"/>
          <output-variable name="imei"/>
          <output-variable name="hasAutopayPending"/>
          <output-variable name="insuranceCode"/>
          <output-variable name="subscriberId"/>
          <output-variable name="usedESNAllowed"/>
          <output-variable name="emailVerificationStatus"/>
          <output-variable name="emailAddress"/>
          <output-variable name="currentPromo"/>
          <output-variable name="futurePromo"/>
          <output-variable name="featureCodes"/>
          <output-variable name="superRegion"/>
          <output-variable name="accountFutureRequestInd"/>
          <output-variable name="subscriberFuturePricePlanInd"/>
          <output-variable name="ratePlanPrice"/>
          <output-variable name="currentPlanPromptURL"/>
          <output-variable name="currentPlanPromptTTS"/>
          <output-variable name="familyPlanIndicator"/>
          <output-variable name="topupEligibility"/>
          <output-variable name="customerTreatmentLevel"/>
          <output-variable name="HasHighSpeedData"/>
          <output-variable name="includedFeatures"/>
          <output-variable name="hasDataMaximizer"/>
          <output-variable name="eligibleForUpgrade"/>
          <output-variable name="upgradeEligibilityDate"/>
          <output-variable name="arBalance"/>
          <output-variable name="multilineList"/>
          <output-variable name="planMedialPromptURL"/>
          <output-variable name="planMedialPromptTTS"/>
          <output-variable name="hasDefaultAccountValues"/>
          <output-variable name="zipCode" mask="true"/>
          <output-variable name="accountRestrictIndicator"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetSubscriberDetails.status &amp;&amp; GetSubscriberDetails.status.toUpperCase() == 'SUCCESS' &amp;&amp; GetSubscriberDetails.validAccount">
          <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
          <if cond="GlobalVars.tag == 'setup-autopay'">
            <action next="getReturnLink()"/>
            <elseif cond="GlobalVars.manageCardTask == 'ReplaceAutopayCard'">
              <if cond="GlobalVars.replacedAutopayCard != true">
                <session-mapping key="GlobalVars.replacedAutopayCard" value="true" type="Boolean"/>
                <action next="MW1200_AskDebitOrCredit_DM"/>
                <else>
                  <action next="MW1610_StatusOfUpdate_PP"/>
                </else>
              </if>
            </elseif>
            <else>
              <action next="MW1610_StatusOfUpdate_PP"/>
            </else>
          </if>
          <else>
            <action next="MW1099_GoToTransfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <subdialog-state id="MW1099_GoToTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="MW1099_GoToTransfer_SD_return"/>
    </subdialog-state>
    <decision-state id="MW1615_CheckNextStep_DS">
      <session-mapping key="isSPMItemDBFailed" value="GlobalVars.isSPMItemDBFailed" type="String"/>
      <session-mapping key="GlobalVars.callType" expr="undefined"/>
      <if cond="GlobalVars.manageCardTask == 'ReplaceAutopayCard'">
        <session-mapping key="GlobalVars.replacedAutopayCard" value="false" type="Boolean"/>
        <action next="getReturnLink()"/>
      </if>
      <if cond="isSPMItemDBFailed  == true">
        <if cond="GlobalVars.tag == 'cancel-autopay'">
          <elseif cond="GlobalVars.tag == 'setup-autopay'"/>
          <elseif cond="GlobalVars.manageCardTask == 'removeCard'"/>
          <elseif cond="GlobalVars.manageCardTask == 'changePrimaryCard'"/>
          <elseif cond="GlobalVars.manageCardTask == 'updateExp'"/>
          <elseif cond="GlobalVars.manageCardTask == 'addCard'"/>
          <elseif cond="GlobalVars.manageCardTask == 'saveMyCard'"/>
          <elseif cond="GlobalVars.manageCardTask == 'ReplaceAutopayCard'"/>
        </if>
        <action next="MW1099_GoToTransfer_SD"/>
        <else>
          <if cond="GlobalVars.tag == 'cancel-autopay'">
            <action next="MW1612_GoToWrap_SD"/>
            <elseif cond="GlobalVars.tag == 'setup-autopay'">
              <action next="getReturnLink()"/>
            </elseif>
            <elseif cond="GlobalVars.manageCardTask == 'removeCard'">
              <action next="getReturnLink()"/>
            </elseif>
            <elseif cond="GlobalVars.manageCardTask == 'changePrimaryCard'">
              <action next="getReturnLink()"/>
            </elseif>
            <elseif cond="GlobalVars.manageCardTask == 'updateExp'">
              <action next="getReturnLink()"/>
            </elseif>
            <elseif cond="GlobalVars.manageCardTask == 'addCard'">
              <action next="getReturnLink()"/>
            </elseif>
            <elseif cond="GlobalVars.manageCardTask == 'saveMyCard'">
              <action next="getReturnLink()"/>
            </elseif>
            <elseif cond="GlobalVars.manageCardTask == 'ReplaceAutopayCard'">
              <action next="getReturnLink()"/>
            </elseif>
          </if>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="MW1612_GoToWrap_SD">
      <gotodialog next="WR_AutopayManageCardWrap_Main_Dialog"/>
      <action next="MW1612_GoToWrap_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MW1612_GoToWrap_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  