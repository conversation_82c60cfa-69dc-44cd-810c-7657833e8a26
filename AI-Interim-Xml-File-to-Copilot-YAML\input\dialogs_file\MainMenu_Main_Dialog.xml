<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="MainMenu_Main_Dialog">
    <decision-state id="MM1000_CheckVariables_DS">
      <if cond="GlobalVars.backOffMenuResult == 'BalAndPayments'">
        <action next="MM1032_BalanceandPaymentsMenu_DM"/>
        <elseif cond="GlobalVars.backOffMenuResult == 'PaymentExtension'">
          <gotodialog next="MainMenu_Routing#MM1595_ApplyExtension_SD"/>
        </elseif>
        <elseif cond="GlobalVars.backOffMenuResult == 'PlansAndServices'">
          <action next="MM1037_PlansandAddonsMenu_DM"/>
        </elseif>
        <elseif cond="GlobalVars.backOffMenuResult == 'Activations'">
          <action next="MM1033_ActivatePhoneMenu_DM"/>
        </elseif>
        <elseif cond="GlobalVars.backOffMenuResult == 'DataOptions'">
          <action next="MM1034_DataMenu_DM"/>
        </elseif>
        <elseif cond="GlobalVars.backOffMenuResult == 'RequestExtension'">
          <gotodialog next="MainMenu_Routing#MM1595_ApplyExtension_SD"/>
        </elseif>
        <else>
          <action next="MM1005_CheckCallerType_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="MM1005_CheckCallerType_DS">
      <if cond="GlobalVars.tag == 'home-internet_active'">
        <action next="MM1045_HomeInternetMenu_DM"/>
        <elseif cond="GlobalVars.callType != undefined">
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </elseif>
        <elseif cond="GlobalVars.outageByColo">
          <action next="MM1410_PlayOutageInfo_PP"/>
        </elseif>
        <elseif cond="GlobalVars.metrics_hasVisitedMainMenu == true">
          <action next="MM1031_MainMenu_DM"/>
        </elseif>
        <else>
          <action next="MM1006_MetricsMainMenuFirstTime_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="MM1006_MetricsMainMenuFirstTime_DS">
      <session-mapping key="GlobalVars.metrics_hasVisitedMainMenu" value="true" type="Boolean"/>
      <action next="MM1031_MainMenu_DM"/>
    </decision-state>

    <dm-state id="MM1035_MyAccount_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="hasHighSpeedData" value="GlobalVars.GetAccountDetails.hasHighSpeedData" type="String"/>
      <session-mapping key="offerDataUsage" value="(hasHighSpeedData=='No')?false:true" type="String"/>
      <session-mapping key="collection_grammar1" value="'MM1035_MyAccount_DM.jsp?stateVar='+offerDataUsage" type="String"/>
      <session-mapping key="collection_dtmfgrammar1" value="'MM1035_MyAccount_DM_dtmf.jsp?stateVar='+offerDataUsage" type="String"/>
      <success>
        <action label="acct_bal">
          <if cond="GlobalVars.aniMatch == true &amp;&amp; GlobalVars.switchLinesSuccess == false">
            <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
            <else>
              <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
            </else>
          </if>
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'acct_bal'"/>
          <session-mapping key="GlobalVars.callType" expr="'acct_bal'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="add_line">
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'add_line'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'add_line'"/>
        </action>
        <action label="main_menu">
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="undefined"/>
          <action next="MM1030_MainMenu_DM"/>
        </action>
        <action label="plan_details">
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'plan_details'"/>
          <session-mapping key="GlobalVars.callType" expr="'plan_details'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.ratePlanAction" expr="undefined"/>
        </action>
        <action label="mdn_change">
          <session-mapping key="mdnChangeVars.eventTypeGMT" expr="getEventTime()"/>
          <session-mapping key="mdnChangeVars.status" expr="'incomplete'"/>
          <session-mapping key="mdnChangeVars.eventType" expr="'mdn_change'"/>
          <session-mapping key="GlobalVars.callType" expr="'mdn_change'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'mdn_change'"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.billableTask" value="true" type="Boolean"/>
          <session-mapping key="ActivationTable.ACTIVATION_TYPE" expr="'5'"/>
          <session-mapping key="ActivationTable.ACTIVATION_STARTED" expr="getGMTTime()"/>
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'47'"/>
          <session-mapping key="ActivationTable.ERROR_TEXT" expr="'FAILURE OR HANG UP'"/>
          <if cond="GlobalVars.GetAccountDetails">
            <session-mapping key="GlobalVars.paymentInfoRequired" value="true" type="Boolean"/>
            <else>
              <session-mapping key="GlobalVars.paymentInfoRequired" value="false" type="Boolean"/>
            </else>
          </if>
        </action>
        <action label="my_features">
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'my_features'"/>
          <session-mapping key="GlobalVars.callType" expr="'my_features'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="reset_pin">
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'reset_pin'"/>
          <session-mapping key="GlobalVars.callType" expr="'reset_pin'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="MM1036_MetricsPreLoginVoicemailPIN_JDA"/>
        </action>
        <action label="make_pmt">
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'make_pmt'"/>
        </action>
        <action label="data_usage">
          <session-mapping key="GlobalVars.callType" expr="'dataUsage'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'dataUsage'"/>
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
        </action>
        <action label="tmomoney">
          <session-mapping key="GlobalVars.callType" expr="'tmomoney'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.tag" expr="'T-MobileMoney'"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
        </action>
        <action label="default">
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MM1035_ini_03">
                <prompt-segments>
                  <audiofile text="To manage your set-up, say 'my plan' or 'my features' " src="MM1035_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="hasHighSpeedData == 'FromRatePlan' || hasHighSpeedData == 'FromFeature'">
                <prompt id="MM1035_ini_01">
                  <prompt-segments>
                    <audiofile text="For data info and options, say 'data usage' " src="MM1035_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="MM1035_ini_02">
                <prompt-segments>
                  <audiofile text="You can also say 'my balance,' 'reset my voicemail PIN,' 'change my phone number,' or 'add a line' " src="MM1035_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MM1035_ini_03">
                <prompt-segments>
                  <audiofile text="To manage your set-up, say 'my plan' or 'my features' " src="MM1035_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="hasHighSpeedData == 'FromRatePlan' || hasHighSpeedData == 'FromFeature'">
                <prompt id="MM1035_ini_01">
                  <prompt-segments>
                    <audiofile text="For data info and options, say 'data usage' " src="MM1035_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="MM1035_ini_02">
                <prompt-segments>
                  <audiofile text="You can also say 'my balance,' 'reset my voicemail PIN,' 'change my phone number,' or 'add a line' " src="MM1035_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="hasHighSpeedData == 'FromRatePlan' || hasHighSpeedData == 'FromFeature'">
                <prompt id="MM1035_nm1_01">
                  <prompt-segments>
                    <audiofile text="Please say 'My plan', 'My features', or 'Data usage' You can also say 'My balance,' or  'Reset my voicemail PIN,' 'Change my phone number,'  or 'Add a line' " src="MM1035_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1035_nm1_03">
                    <prompt-segments>
                      <audiofile text="To hear or to change your current set-up, say 'My plan' or 'My features'  You can also say 'My balance,' or  'Reset my voicemail PIN,' 'Change my phone number,'  or 'Add a line'" src="MM1035_nm1_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="hasHighSpeedData == 'FromRatePlan' || hasHighSpeedData == 'FromFeature'">
                <prompt id="MM1035_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'My plan' or press 1; 'My features' or press 2; 'Data Usage', 3; 'My balance,' 4; 'Reset my voicemail PIN' - 5; 'Change my phone number' - 6  Or 'Add a line' - 7 " src="MM1035_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1035_nm2_03">
                    <prompt-segments>
                      <audiofile text="You can say 'My plan' or press 1; 'My features' or press 2; 'My balance,' 3; 'Reset my voicemail PIN' - 4; 'Change my phone number' - 5  Or 'Add a line' - 6 " src="MM1035_nm2_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="hasHighSpeedData == 'FromRatePlan' || hasHighSpeedData == 'FromFeature'">
                <prompt id="MM1035_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'My plan' or press 1; 'My features' or press 2; 'Data Usage', 3; 'My balance,' 4; 'Reset my voicemail PIN' - 5; 'Change my phone number' - 6  Or 'Add a line' - 7 " src="MM1035_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1035_nm2_03">
                    <prompt-segments>
                      <audiofile text="You can say 'My plan' or press 1; 'My features' or press 2; 'My balance,' 3; 'Reset my voicemail PIN' - 4; 'Change my phone number' - 5  Or 'Add a line' - 6 " src="MM1035_nm2_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="hasHighSpeedData == 'FromRatePlan' || hasHighSpeedData == 'FromFeature'">
                <prompt id="MM1035_nm1_01">
                  <prompt-segments>
                    <audiofile text="Please say 'My plan', 'My features', or 'Data usage' You can also say 'My balance,' or  'Reset my voicemail PIN,' 'Change my phone number,'  or 'Add a line' " src="MM1035_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1035_nm1_03">
                    <prompt-segments>
                      <audiofile text="To hear or to change your current set-up, say 'My plan' or 'My features'  You can also say 'My balance,' or  'Reset my voicemail PIN,' 'Change my phone number,'  or 'Add a line'" src="MM1035_nm1_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="hasHighSpeedData == 'FromRatePlan' || hasHighSpeedData == 'FromFeature'">
                <prompt id="MM1035_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'My plan' or press 1; 'My features' or press 2; 'Data Usage', 3; 'My balance,' 4; 'Reset my voicemail PIN' - 5; 'Change my phone number' - 6  Or 'Add a line' - 7 " src="MM1035_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1035_nm2_03">
                    <prompt-segments>
                      <audiofile text="You can say 'My plan' or press 1; 'My features' or press 2; 'My balance,' 3; 'Reset my voicemail PIN' - 4; 'Change my phone number' - 5  Or 'Add a line' - 6 " src="MM1035_nm2_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="hasHighSpeedData == 'FromRatePlan' || hasHighSpeedData == 'FromFeature'">
                <prompt id="MM1035_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'My plan' or press 1; 'My features' or press 2; 'Data Usage', 3; 'My balance,' 4; 'Reset my voicemail PIN' - 5; 'Change my phone number' - 6  Or 'Add a line' - 7 " src="MM1035_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1035_nm2_03">
                    <prompt-segments>
                      <audiofile text="You can say 'My plan' or press 1; 'My features' or press 2; 'My balance,' 3; 'Reset my voicemail PIN' - 4; 'Change my phone number' - 5  Or 'Add a line' - 6 " src="MM1035_nm2_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MM1035_ini_03">
                <prompt-segments>
                  <audiofile text="To manage your set-up, say 'my plan' or 'my features' " src="MM1035_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="hasHighSpeedData == 'FromRatePlan' || hasHighSpeedData == 'FromFeature'">
                <prompt id="MM1035_ini_01">
                  <prompt-segments>
                    <audiofile text="For data info and options, say 'data usage' " src="MM1035_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="MM1035_ini_02">
                <prompt-segments>
                  <audiofile text="You can also say 'my balance,' 'reset my voicemail PIN,' 'change my phone number,' or 'add a line' " src="MM1035_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="MM1035_MyAccount_DM.jsp" count="1"/>
          <dtmfgrammars filename="MM1035_MyAccount_DM_dtmf.jsp" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MM1035_MyAccount_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="MM1036_MetricsPreLoginVoicemailPIN_JDA">
      <data-access id="WhatsNext">
        <outputs>
          <output-variable name="label" type="String"/>
        </outputs>
      </data-access>
      <action next="MM1036_MetricsPreLoginVoicemailPIN_DS"/>
    </data-access-state>

    <decision-state id="MM1036_MetricsPreLoginVoicemailPIN_DS">
      <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
    </decision-state>

    <play-state id="MM1410_PlayOutageInfo_PP">
      <session-mapping key="visitedMM1410" value="GlobalVars.visitedMM1410 ? GlobalVars.visitedMM1410 : false" type="String"/>
      <audio>
        <if cond="visitedMM1410 == false">
          <prompt id="MM1410_ini_03">
            <prompt-segments>
              <audiofile text="Actually, we re updating our systems right now, so I can t give you any information about your account or make any changes to it But here s what I can do" src="MM1410_ini_03.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="MM1410_out_02">
              <prompt-segments>
                <audiofile text="Since we re updating our systems, I can t give you any information about your account or make any changes to it right now  But here s what I can help with" src="MM1410_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.visitedMM1410" value="true" type="Boolean"/>
      <action next="MM1415_Outage_DM"/>
    </play-state>

    <dm-state id="MM1415_Outage_DM" type="CUST">
      <session-mapping key="valid_account" value="false" type="Boolean"/>
      <success>
        <session-mapping key="GlobalVars.returningFromPayments" value="false" type="Boolean"/>
        <action label="inquire-store_location">
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'find_store'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'unknown'"/>
        </action>
        <action label="open_account">
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'activate_unknownDevice'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="vague-troubleshooting_disambig">
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'troubleshoot'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="default">
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MM1415_ini_02">
                <prompt-segments>
                  <audiofile text="Say Troubleshooting Tips, Open an account, or Find a store" src="MM1415_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MM1415_ini_02">
                <prompt-segments>
                  <audiofile text="Say Troubleshooting Tips, Open an account, or Find a store" src="MM1415_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MM1415_nm1_02">
                <prompt-segments>
                  <audiofile text="You can say Troubleshooting Tips, Open an account, or Find a store For anything else, just call us back in a few hours!" src="MM1415_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1415_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say Troubleshooting tips, or press 1  Open an account, or press 2 Or for help in person, say Find a store, or press 3 If you need something else, please call us back in a couple of hours when we re done updating!" src="MM1415_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1415_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say Troubleshooting tips, or press 1  Open an account, or press 2 Or for help in person, say Find a store, or press 3 If you need something else, please call us back in a couple of hours when we re done updating!" src="MM1415_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1415_nm1_02">
                <prompt-segments>
                  <audiofile text="You can say Troubleshooting Tips, Open an account, or Find a store For anything else, just call us back in a few hours!" src="MM1415_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1415_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say Troubleshooting tips, or press 1  Open an account, or press 2 Or for help in person, say Find a store, or press 3 If you need something else, please call us back in a couple of hours when we re done updating!" src="MM1415_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1415_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say Troubleshooting tips, or press 1  Open an account, or press 2 Or for help in person, say Find a store, or press 3 If you need something else, please call us back in a couple of hours when we re done updating!" src="MM1415_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MM1415_ini_02">
                <prompt-segments>
                  <audiofile text="Say Troubleshooting Tips, Open an account, or Find a store" src="MM1415_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MM1415_Outage_DM.grxml" count="1"/>
          <dtmfgrammars filename="MM1415_Outage_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MM1031_MainMenu_DM" type="CUST">
      <session-mapping key="operatorPromptEnabled" value="GlobalVars.GetBCSParameters?GlobalVars.GetBCSParameters.care_operator_prompt_enabled == 'true': false" type="String"/>
      <session-mapping key="returningFromPayments" value="GlobalVars.returningFromPayments" type="String"/>
      <session-mapping key="switchLinesSuccess" value="GlobalVars.switchLinesSuccess != undefined ? GlobalVars.switchLinesSuccess : false" type="String"/>
      <success>
        <session-mapping key="GlobalVars.returningFromPayments" value="false" type="Boolean"/>
        <action label="inquire-balance_mm">
          <action next="MM1032_BalanceandPaymentsMenu_DM"/>
        </action>
        <action label="activate-phone_mm">
          <action next="MM1033_ActivatePhoneMenu_DM"/>
        </action>
        <action label="vague-data_mm">
          <if cond="GlobalVars.GetAccountDetails.parentDeviceType == 'INT'">
            <action next="MM1047_Disambig_Home_Internet_DM"/>
            <else>
              <action next="MM1034_DataMenu_DM"/>
            </else>
          </if>
        </action>
        <action label="plan_addon">
          <if cond="GlobalVars.GetAccountDetails.parentDeviceType == 'INT'">
            <action next="MM1047_Disambig_Home_Internet_DM"/>
            <else>
              <action next="MM1037_PlansandAddonsMenu_DM"/>
            </else>
          </if>
        </action>
        <action label="switch-lines_mm">
          <audio>
            <prompt id="mm1031_out_01">
              <prompt-segments>
                <audiofile src="mm1031_out_01.wav" text="Sure "/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'switch_lines'"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="request-extension">
          <audio>
            <prompt id="mm1031_out_01">
              <prompt-segments>
                <audiofile src="mm1031_out_01.wav" text="Sure "/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'extension'"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="home-internet">
          <if cond="GlobalVars.GetAccountDetails.parentDeviceType == 'INT'">
            <session-mapping key="GlobalVars.tag" expr="'home-internet_active'"/>
            <action next="MM1045_HomeInternetMenu_DM"/>
            <else>
              <session-mapping key="GlobalVars.tag" expr="'home-internet_transfer'"/>
              <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
            </else>
          </if>
        </action>
        <action label="more_options">
          <action next="MM1038_MoreOptionsMenu_DM"/>
        </action>
        <action label="inquire-acp">
          <action next="MM1600_GoToACP_SD"/>
        </action>
        <action label="tmomoney">
          <session-mapping key="GlobalVars.tag" expr="'T-MobileMoney'"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'tmomoney'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="make-payment">
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.returningFromPayments" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MM1031_ini_02">
                <prompt-segments>
                  <audiofile src="MM1031_ini_02.wav" text="How can I help you? You can say 'Balance and payments', 'Plans and services' , 'Activations', 'Payment extensions', 'Data options', or say 'More options'"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_ini_03" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile src="MM1031_ini_03.wav" text="And, you can say  Operator  at any time"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="MM1031_MainMenu_DM_reentry"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MM1031_rin_01">
                <prompt-segments>
                  <audiofile src="MM1031_rin_01.wav" text="Say 'Balance and payments', 'Plans and services' , 'Activations' 'Payment extension', 'Data options', or say 'More options'"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_rin_03" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile src="MM1031_rin_03.wav" text="And, you can say  Operator  at any time"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MM1031_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1031_nm1_01.wav" text="To check your balance or make a payment, say 'Balance and payments', or press 1For things like change plan, plan details or services, say 'Plans and services', or press 2             For activating a device say 'Activations', or press 3 To request an extension say 'payment extension', or press 4For help with data, say 'Data options',press 5You can also say 'More options', or press 6"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_100ms" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_100ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_nm1_03" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile src="MM1031_nm1_03.wav" text="And, you can always say  Operator "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1031_nm2_01.wav" text="For 'Balance and payments'  press 1 ' Plans and services'  press 2, Activate a device', 3  Payment extension', 4 'Data options' 5  'More options' or press 6 "/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_100ms" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_100ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_nm2_03" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile src="MM1031_nm2_03.wav" text="And, you can always say  Operator "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1031_nm2_01.wav" text="For 'Balance and payments'  press 1 ' Plans and services'  press 2, Activate a device', 3  Payment extension', 4 'Data options' 5  'More options' or press 6 "/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_100ms" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_100ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_nm2_03" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile src="MM1031_nm2_03.wav" text="And, you can always say  Operator "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1031_nm1_01.wav" text="To check your balance or make a payment, say 'Balance and payments', or press 1For things like change plan, plan details or services, say 'Plans and services', or press 2             For activating a device say 'Activations', or press 3 To request an extension say 'payment extension', or press 4For help with data, say 'Data options',press 5You can also say 'More options', or press 6"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_100ms" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_100ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_nm1_03" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile src="MM1031_nm1_03.wav" text="And, you can always say  Operator "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1031_nm2_01.wav" text="For 'Balance and payments'  press 1 ' Plans and services'  press 2, Activate a device', 3  Payment extension', 4 'Data options' 5  'More options' or press 6 "/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_100ms" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_100ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_nm2_03" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile src="MM1031_nm2_03.wav" text="And, you can always say  Operator "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1031_nm2_01.wav" text="For 'Balance and payments'  press 1 ' Plans and services'  press 2, Activate a device', 3  Payment extension', 4 'Data options' 5  'More options' or press 6 "/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_100ms" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_100ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_nm2_03" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile src="MM1031_nm2_03.wav" text="And, you can always say  Operator "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MM1031_rin_01">
                <prompt-segments>
                  <audiofile src="MM1031_rin_01.wav" text="Say 'Balance and payments', 'Plans and services' , 'Activations' 'Payment extension', 'Data options', or say 'More options'"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1031_rin_03" cond="operatorPromptEnabled == true">
                <prompt-segments>
                  <audiofile src="MM1031_rin_03.wav" text="And, you can say  Operator  at any time"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="MM1031_MainMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="MM1031_MainMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MM1031_MainMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MM1032_BalanceandPaymentsMenu_DM" type="CUST">
      <session-mapping key="isAutopayEligPlanExists" value="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.isAutopayEligPlanExists" type="String"/>
      <session-mapping key="isSuspended" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" type="String"/>
      <success>
        <action label="make-payment">
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
        </action>
        <action label="request-extension">
          <audio>
            <prompt id="mm1032_out_01">
              <prompt-segments>
                <audiofile src="mm1032_out_01.wav" text="Sure"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'extension'"/>
        </action>
        <action label="payment-help">
          <session-mapping key="GlobalVars.callType" expr="'pmt_help'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
        </action>
        <action label="hear-plan_details">
          <session-mapping key="GlobalVars.callType" expr="'acct_bal'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <if cond="isAutopayEligPlanExists == true">
            <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
            <elseif cond="GlobalVars.aniMatch == true &amp;&amp; GlobalVars.switchLinesSuccess == false">
              <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
            </elseif>
            <else>
              <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
            </else>
          </if>
        </action>
        <action label="vague-autopay">
          <session-mapping key="GlobalVars.callType" expr="'auto_pay'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.tag" expr="'vague-autopay'"/>
          <if cond="isSuspended == true">
            <gotodialog next="MainMenu_Routing#MM1599_GoToSuspended_SD"/>
            <else>
              <action next="MM1610_GoToAutoPay_SD"/>
            </else>
          </if>
        </action>
        <action label="default">
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MM1032_ini_01">
                <prompt-segments>
                  <audiofile src="MM1032_ini_01.wav" text="Say  make a payment ,  payment help  or  balance details "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="MM1032_BalanceandPaymentsMenu_DM_initial"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MM1032_ini_01">
                <prompt-segments>
                  <audiofile src="MM1032_ini_01.wav" text="Say  make a payment ,  payment help  or  balance details "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MM1032_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1032_nm1_01.wav" text="Please say 'auto pay', 'make a payment', 'payment help' or 'balance details' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1032_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1032_nm2_01.wav" text="You can say 'auto pay', or press 1;  'make a payment' or press 2; 'Payment help' or press 3 or 'Balance details' or press 4 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1032_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1032_nm2_01.wav" text="You can say 'auto pay', or press 1;  'make a payment' or press 2; 'Payment help' or press 3 or 'Balance details' or press 4 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1032_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1032_nm1_01.wav" text="Please say 'auto pay', 'make a payment', 'payment help' or 'balance details' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1032_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1032_nm2_01.wav" text="You can say 'auto pay', or press 1;  'make a payment' or press 2; 'Payment help' or press 3 or 'Balance details' or press 4 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1032_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1032_nm2_01.wav" text="You can say 'auto pay', or press 1;  'make a payment' or press 2; 'Payment help' or press 3 or 'Balance details' or press 4 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MM1032_ini_01">
                <prompt-segments>
                  <audiofile src="MM1032_ini_01.wav" text="Say  make a payment ,  payment help  or  balance details "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MM1032_BalanceandPaymentsMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="MM1032_BalanceandPaymentsMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MM1032_BalanceandPaymentsMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MM1033_ActivatePhoneMenu_DM" type="CUST">
      <success>
        <action label="add-line">
          <session-mapping key="GlobalVars.callType" expr="'add_line'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <gotodialog next="MainMenu_Routing#MM1555_GoToDeviceHandling_SD"/>
        </action>
        <action label="activate-new_account_mm">
          <audio>
            <prompt id="MM1033_out_01">
              <prompt-segments>
                <audiofile src="MM1033_out_01.wav" text="Ok, a new account!"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'activate_unknownDevice'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="reactivate-mm">
          <session-mapping key="GlobalVars.callType" expr="'reactivate'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
        </action>
        <action label="switch-phone">
          <session-mapping key="GlobalVars.callType" expr="'switch_phone'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'Activations_Support_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'Activations_Support_Spanish'"/>
            </else>
          </if>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MM1033_ini_01">
                <prompt-segments>
                  <audiofile src="MM1033_ini_01.wav" text="OkIf you're changing your device, Say 'I have a new device'   If your device is suspended, say 'reactivate', ' Or, if you're adding a line to an existing account say 'add a line"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="MM1033_ActivatePhoneMenu_DM_initial"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MM1033_ini_01">
                <prompt-segments>
                  <audiofile src="MM1033_ini_01.wav" text="OkIf you're changing your device, Say 'I have a new device'   If your device is suspended, say 'reactivate', ' Or, if you're adding a line to an existing account say 'add a line"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MM1033_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1033_nm1_01.wav" text="Please say  switch phone ,  add a line , reactivate an account , or  open a new account "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1033_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1033_nm2_01.wav" text="You can say  switch phone  or press 1  Add a line  or press 2  reactivate an account , or press 3 To open an account, say  Open a new account,  or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1033_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1033_nm2_01.wav" text="You can say  switch phone  or press 1  Add a line  or press 2  reactivate an account , or press 3 To open an account, say  Open a new account,  or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1033_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1033_nm1_01.wav" text="Please say  switch phone ,  add a line , reactivate an account , or  open a new account "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1033_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1033_nm2_01.wav" text="You can say  switch phone  or press 1  Add a line  or press 2  reactivate an account , or press 3 To open an account, say  Open a new account,  or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1033_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1033_nm2_01.wav" text="You can say  switch phone  or press 1  Add a line  or press 2  reactivate an account , or press 3 To open an account, say  Open a new account,  or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MM1033_ini_01">
                <prompt-segments>
                  <audiofile src="MM1033_ini_01.wav" text="OkIf you're changing your device, Say 'I have a new device'   If your device is suspended, say 'reactivate', ' Or, if you're adding a line to an existing account say 'add a line"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MM1033_ActivatePhoneMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="MM1033_ActivatePhoneMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MM1033_ActivatePhoneMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MM1038_MoreOptionsMenu_DM" type="CUST">
      <success>
        <action label="change-phone_number">
          <session-mapping key="GlobalVars.callType" expr="'mdn_change'"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="ActivationTable.ACTIVATION_STARTED" expr="getGMTTime()"/>
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'47'"/>
          <session-mapping key="ActivationTable.ERROR_TEXT" expr="'FAILURE OR HANG UP'"/>
          <session-mapping key="GlobalVars.paymentInfoRequired" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.billableTask" value="true" type="Boolean"/>
          <if cond="GlobalVars.GetAccountDetails == null || GlobalVars.GetAccountDetails == undefined">
            <session-mapping key="GlobalVars.paymentInfoRequired" value="false" type="Boolean"/>
          </if>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="mdnChangeVars.eventTypeGMT" expr="getEventTime()"/>
          <session-mapping key="mdnChangeVars.status" expr="'incomplete'"/>
          <session-mapping key="mdnChangeVars.eventType" expr="'mdn_change'"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="suspend-service">
          <session-mapping key="GlobalVars.callType" expr="'lost_phone'"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="request-extension">
          <session-mapping key="GlobalVars.callType" expr="'extension'"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="go_back">
          <action next="MM1031_MainMenu_DM"/>
        </action>
        <action label="voicemail">
          <action next="MM1040_VoicemailMenu_DM"/>
        </action>
        <action label="vague-unlock_phone">
          <session-mapping key="GlobalVars.callType" expr="'unlock_phone'"/>
          <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
        </action>
        <action label="inquire-account_number">
          <session-mapping key="GlobalVars.callType" expr="'acct_number'"/>
          <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
        </action>
        <action label="home-internet">
          <session-mapping key="GlobalVars.callType" expr="'home_internet'"/>
          <action next="MM1045_HomeInternetMenu_DM"/>
        </action>
        <action label="forgot-account_pin">
          <session-mapping key="GlobalVars.callType" expr="'forgot_acct_pin'"/>
          <gotodialog next="MainMenu_Routing#MM1615_AccountPinReset_SD"/>
        </action>
        <action label="switch-lines">
          <gotodialog next="MainMenu_Routing#MM1598_GoToSwitchLines_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MM1038_ini_01">
                <prompt-segments>
                  <audiofile src="MM1038_ini_01.wav" text="Sure you can say, 'suspend my service', 'get my account number', ' forgot my account PIN', 'unlock a phone', 'change my phone number', 'switch lines' or say 'go back'"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="MM1038_MoreOptionsMenu_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MM1038_ini_01">
                <prompt-segments>
                  <audiofile src="MM1038_ini_01.wav" text="Sure you can say, 'suspend my service', 'get my account number', ' forgot my account PIN', 'unlock a phone', 'change my phone number', 'switch lines' or say 'go back'"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MM1038_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1038_nm1_01.wav" text="Please say 'suspend my service', 'get my account number', 'I forgot my account PIN', 'unlock a phone', 'change my phone number', 'switch lines'  you can also say 'go back'"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1038_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1038_nm2_01.wav" text="Say 'suspend my service', or press 1  'get my account number', or press 2 'I forgot my account PIN', 3  'unlock a phone', 4  'change my phone number', 5  'switch lines, 6  You can also say 'go back' or press 7"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1038_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1038_nm2_01.wav" text="Say 'suspend my service', or press 1  'get my account number', or press 2 'I forgot my account PIN', 3  'unlock a phone', 4  'change my phone number', 5  'switch lines, 6  You can also say 'go back' or press 7"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1038_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1038_nm1_01.wav" text="Please say 'suspend my service', 'get my account number', 'I forgot my account PIN', 'unlock a phone', 'change my phone number', 'switch lines'  you can also say 'go back'"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1038_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1038_nm2_01.wav" text="Say 'suspend my service', or press 1  'get my account number', or press 2 'I forgot my account PIN', 3  'unlock a phone', 4  'change my phone number', 5  'switch lines, 6  You can also say 'go back' or press 7"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1038_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1038_nm2_01.wav" text="Say 'suspend my service', or press 1  'get my account number', or press 2 'I forgot my account PIN', 3  'unlock a phone', 4  'change my phone number', 5  'switch lines, 6  You can also say 'go back' or press 7"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MM1038_ini_01">
                <prompt-segments>
                  <audiofile src="MM1038_ini_01.wav" text="Sure you can say, 'suspend my service', 'get my account number', ' forgot my account PIN', 'unlock a phone', 'change my phone number', 'switch lines' or say 'go back'"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf_MM1038.grxml">
          <grammars filename="MM1038_MoreOptionsMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="MM1038_MoreOptionsMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MM1038_MoreOptionsMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MM1039_ReactivateMenu_DM" type="CUST">
      <success>
        <action label="unsuspend">
          <audio>
            <prompt id="MM1039_out_01">
              <prompt-segments>
                <audiofile src="MM1039_out_01.wav" text="Got it!"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.tag" expr="'lift-suspension'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
        </action>
        <action label="reactivate-old_account_mm">
          <audio>
            <prompt id="MM1039_out_02">
              <prompt-segments>
                <audiofile src="MM1039_out_02.wav" text="Sure"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.tag" expr="'reactivate-old_account'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
        </action>
        <action label="activate-new_account">
          <audio>
            <prompt id="MM1039_out_03">
              <prompt-segments>
                <audiofile src="MM1039_out_03.wav" text="Great!"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'activate_unknownDevice'"/>
          <session-mapping key="GlobalVars.tag" expr="'activate-new_account'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MM1039_ini_01">
                <prompt-segments>
                  <audiofile src="MM1039_ini_01.wav" text="If you re reactivating an account you ASKED to suspend, say  lift a suspension  If your account s been closed because of missed payments, say  get my number back , or  open a new account "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="MM1039_ReactivateMenu_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MM1039_ini_01">
                <prompt-segments>
                  <audiofile src="MM1039_ini_01.wav" text="If you re reactivating an account you ASKED to suspend, say  lift a suspension  If your account s been closed because of missed payments, say  get my number back , or  open a new account "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MM1039_ini_01">
                <prompt-segments>
                  <audiofile src="MM1039_ini_01.wav" text="If you re reactivating an account you ASKED to suspend, say  lift a suspension  If your account s been closed because of missed payments, say  get my number back , or  open a new account "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1039_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1039_nm2_01.wav" text="If you ve asked to have your account suspended, for example because your phone was lost, say  lift a suspension  or press 1 If your account s been closed because of missed payments and you d like to try to get your phone number back, say  get my number back  or press 2 If you d like to get a new phone number, say   open a new account  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1039_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1039_nm2_01.wav" text="If you ve asked to have your account suspended, for example because your phone was lost, say  lift a suspension  or press 1 If your account s been closed because of missed payments and you d like to try to get your phone number back, say  get my number back  or press 2 If you d like to get a new phone number, say   open a new account  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1039_ini_01">
                <prompt-segments>
                  <audiofile src="MM1039_ini_01.wav" text="If you re reactivating an account you ASKED to suspend, say  lift a suspension  If your account s been closed because of missed payments, say  get my number back , or  open a new account "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1039_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1039_nm2_01.wav" text="If you ve asked to have your account suspended, for example because your phone was lost, say  lift a suspension  or press 1 If your account s been closed because of missed payments and you d like to try to get your phone number back, say  get my number back  or press 2 If you d like to get a new phone number, say   open a new account  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1039_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1039_nm2_01.wav" text="If you ve asked to have your account suspended, for example because your phone was lost, say  lift a suspension  or press 1 If your account s been closed because of missed payments and you d like to try to get your phone number back, say  get my number back  or press 2 If you d like to get a new phone number, say   open a new account  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MM1039_ini_01">
                <prompt-segments>
                  <audiofile src="MM1039_ini_01.wav" text="If you re reactivating an account you ASKED to suspend, say  lift a suspension  If your account s been closed because of missed payments, say  get my number back , or  open a new account "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MM1039_ReactivateMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="MM1039_ReactivateMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MM1039_ReactivateMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MM1040_VoicemailMenu_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.callType" expr="'reset_pin'"/>
          <session-mapping key="GlobalVars.tag" expr="'lift-suspension'"/>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="MM1036_MetricsPreLoginVoicemailPIN_JDA"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.callType" expr="'voicemail'"/>
          <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MM1040_ini_01">
                <prompt-segments>
                  <audiofile src="MM1040_ini_01.wav" text="Is it to reset your voicemail PIN?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MM1040_ini_01">
                <prompt-segments>
                  <audiofile src="MM1040_ini_01.wav" text="Is it to reset your voicemail PIN?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MM1040_ini_01">
                <prompt-segments>
                  <audiofile src="MM1040_ini_01.wav" text="Is it to reset your voicemail PIN?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1040_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1040_nm2_01.wav" text="To reset your voicemail pin, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1040_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1040_nm2_01.wav" text="To reset your voicemail pin, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1040_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1040_nm1_01.wav" text="Would you like to reset your voicemail PIN?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1040_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1040_nm2_01.wav" text="To reset your voicemail pin, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1040_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1040_nm2_01.wav" text="To reset your voicemail pin, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MM1040_VoicemailMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="MM1040_VoicemailMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MM1034_DataMenu_DM" type="CUST">
      <success>
        <action label="hear-data_usage">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'data_usage'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="false" type="Boolean"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="add-data">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'add_data'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="false" type="Boolean"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="vague-troubleshooting_disambig">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'troubleshoot'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="false" type="Boolean"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MM1034_ini_01">
                <prompt-segments>
                  <audiofile text="Say 'Data Usage', 'Add Data', or 'Troubleshooting' " src="MM1034_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MM1034_ini_01">
                <prompt-segments>
                  <audiofile text="Say 'Data Usage', 'Add Data', or 'Troubleshooting' " src="MM1034_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MM1034_nm1_01">
                <prompt-segments>
                  <audiofile text="Say 'Data Usage', 'Add Data', or 'Troubleshooting' " src="MM1034_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1034_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'Data Usage' or press 1; 'Add Data', or press 2; or 'Troubleshooting' or press 3" src="MM1034_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1034_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'Data Usage' or press 1; 'Add Data', or press 2; or 'Troubleshooting' or press 3" src="MM1034_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1034_nm1_01">
                <prompt-segments>
                  <audiofile text="Say 'Data Usage', 'Add Data', or 'Troubleshooting' " src="MM1034_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1034_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'Data Usage' or press 1; 'Add Data', or press 2; or 'Troubleshooting' or press 3" src="MM1034_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1034_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'Data Usage' or press 1; 'Add Data', or press 2; or 'Troubleshooting' or press 3" src="MM1034_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MM1034_DataMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="MM1034_DataMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MM1034_DataMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MM1037_PlansandAddonsMenu_DM" type="CUST">
      <session-mapping key="isOnFamilyPlan" value="GlobalVars.GetAccountDetails.isOnFamilyPlan" type="String"/>
      <session-mapping key="collection_dtmfgrammar" value="MM1037_PlansandAddonsMenu_DM_dtmf.grxml" type="String"/>
      <session-mapping key="collection_grammar" value="MM1037_PlansandAddonsMenu_DM.grxml" type="String"/>
      <if cond="(isOnFamilyPlan != true)">
        <session-mapping key="collection_dtmfgrammar" expr="collection_dtmfgrammar + '?SWI_vars.disallow=cancel-line'"/>
        <session-mapping key="collection_grammar" expr="collection_grammar + '?SWI_vars.disallow=cancel-line'"/>
      </if>
      <success>
        <action label="add-feature">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'add_feature'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="true" type="Boolean"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="change-plan">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="true" type="Boolean"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="hear-plan_details">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'plan_details'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="true" type="Boolean"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="add-line">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'add_line'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="true" type="Boolean"/>
          <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
        </action>
        <action label="remove-feature">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'remove_feature'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="true" type="Boolean"/>
          <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
        </action>
        <action label="cancel-line">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'cancel_line'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="true" type="Boolean"/>
          <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="isOnFamilyPlan == true">
                <prompt id="MM1037_ini_01">
                  <prompt-segments>
                    <audiofile text="Say 'Current plan and services', 'Change plan', 'Add services', 'Remove services', Add a line' or 'cancel a line'" src="MM1037_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1037_ini_02">
                    <prompt-segments>
                      <audiofile text="Say 'Current plan and services', 'Change plan', 'Add services', 'Remove services', or Add a line'" src="MM1037_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="isOnFamilyPlan == true">
                <prompt id="MM1037_ini_01">
                  <prompt-segments>
                    <audiofile text="Say 'Current plan and services', 'Change plan', 'Add services', 'Remove services', Add a line' or 'cancel a line'" src="MM1037_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1037_ini_02">
                    <prompt-segments>
                      <audiofile text="Say 'Current plan and services', 'Change plan', 'Add services', 'Remove services', or Add a line'" src="MM1037_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="isOnFamilyPlan == true">
                <prompt id="MM1037_nm1_01">
                  <prompt-segments>
                    <audiofile text="To check your plan and included services, say 'Current plan and services' You can also say 'Change plan', 'Add services', 'Remove services' Or say 'Add a line' or 'cancel a line'" src="MM1037_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1037_nm1_02">
                    <prompt-segments>
                      <audiofile text="To check your plan and included services, say 'Current plan and services' You can also say 'Change plan', 'Add services', 'Remove services' Or say 'Add a line'" src="MM1037_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isOnFamilyPlan == true">
                <prompt id="MM1037_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'Current plan and services' or press 1; 'Change plan' or press 2; 'Add services' - 3; 'Remove services' - 4; 'Add a line' - 5; or 'Cancel a line' - 6 " src="MM1037_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1037_nm2_02">
                    <prompt-segments>
                      <audiofile text="You can say 'Current plan and services' or press 1; 'Change plan' or press 2; 'Add services' - 3, 'Remove services' - 4 Or 'Add a line' - 5 " src="MM1037_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isOnFamilyPlan == true">
                <prompt id="MM1037_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'Current plan and services' or press 1; 'Change plan' or press 2; 'Add services' - 3; 'Remove services' - 4; 'Add a line' - 5; or 'Cancel a line' - 6 " src="MM1037_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1037_nm2_02">
                    <prompt-segments>
                      <audiofile text="You can say 'Current plan and services' or press 1; 'Change plan' or press 2; 'Add services' - 3, 'Remove services' - 4 Or 'Add a line' - 5 " src="MM1037_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isOnFamilyPlan == true">
                <prompt id="MM1037_nm1_01">
                  <prompt-segments>
                    <audiofile text="To check your plan and included services, say 'Current plan and services' You can also say 'Change plan', 'Add services', 'Remove services' Or say 'Add a line' or 'cancel a line'" src="MM1037_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1037_nm1_02">
                    <prompt-segments>
                      <audiofile text="To check your plan and included services, say 'Current plan and services' You can also say 'Change plan', 'Add services', 'Remove services' Or say 'Add a line'" src="MM1037_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isOnFamilyPlan == true">
                <prompt id="MM1037_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'Current plan and services' or press 1; 'Change plan' or press 2; 'Add services' - 3; 'Remove services' - 4; 'Add a line' - 5; or 'Cancel a line' - 6 " src="MM1037_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1037_nm2_02">
                    <prompt-segments>
                      <audiofile text="You can say 'Current plan and services' or press 1; 'Change plan' or press 2; 'Add services' - 3, 'Remove services' - 4 Or 'Add a line' - 5 " src="MM1037_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isOnFamilyPlan == true">
                <prompt id="MM1037_nm2_01">
                  <prompt-segments>
                    <audiofile text="You can say 'Current plan and services' or press 1; 'Change plan' or press 2; 'Add services' - 3; 'Remove services' - 4; 'Add a line' - 5; or 'Cancel a line' - 6 " src="MM1037_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1037_nm2_02">
                    <prompt-segments>
                      <audiofile text="You can say 'Current plan and services' or press 1; 'Change plan' or press 2; 'Add services' - 3, 'Remove services' - 4 Or 'Add a line' - 5 " src="MM1037_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="isOnFamilyPlan == true">
                <prompt id="MM1037_nm1_01">
                  <prompt-segments>
                    <audiofile text="To check your plan and included services, say 'Current plan and services' You can also say 'Change plan', 'Add services', 'Remove services' Or say 'Add a line' or 'cancel a line'" src="MM1037_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MM1037_nm1_02">
                    <prompt-segments>
                      <audiofile text="To check your plan and included services, say 'Current plan and services' You can also say 'Change plan', 'Add services', 'Remove services' Or say 'Add a line'" src="MM1037_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MM1037_PlansandAddonsMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="MM1037_PlansandAddonsMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MM1037_PlansandAddonsMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="MM1045_HomeInternetMenu_DM" type="CUST">
      <session-mapping key="enteredHintLine" value="GlobalVars.enteredHintLine" type="String"/>
      <session-mapping key="fromHINTDisambig" value="GlobalVars.fromHINTDisambig" type="String"/>
      <success>
        <action label="internet-usage_information">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'data_usage'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="true" type="Boolean"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="hear-plan_details">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'plan_details'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.ratePlanAction " expr="undefined"/>
          <gotodialog next="MainMenu_CheckSecurity#MM1050_CheckContinueToDestination_DS"/>
        </action>
        <action label="something-else_homeinternet">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'home_internet'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)"/>
          <session-mapping key="GlobalVars.securityRequired " value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.ratePlanAction " expr="undefined"/>
          <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="fromHINTDisambig == true">
                <prompt id="mm1045_ini_01">
                  <prompt-segments>
                    <audiofile src="mm1045_ini_01.wav" text="OK, You can say 'plan details', 'usage information' or say, 'it's something else' "/>
                  </prompt-segments>
                </prompt>
                <elseif cond="enteredHintLine == true">
                  <prompt id="mm1045_ini_03">
                    <prompt-segments>
                      <audiofile src="mm1045_ini_03.wav" text="Ok you entered your home internet number   Here are your choices You can say 'plan details', 'usage information' or say, 'it's something else' "/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="mm1045_ini_02">
                    <prompt-segments>
                      <audiofile src="mm1045_ini_02.wav" text="Home internet menu  You can say 'plan details', 'usage information' or say, 'it's something else' "/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="fromHINTDisambig == true">
                <prompt id="mm1045_ini_01">
                  <prompt-segments>
                    <audiofile src="mm1045_ini_01.wav" text="OK, You can say 'plan details', 'usage information' or say, 'it's something else' "/>
                  </prompt-segments>
                </prompt>
                <elseif cond="enteredHintLine == true">
                  <prompt id="mm1045_ini_03">
                    <prompt-segments>
                      <audiofile src="mm1045_ini_03.wav" text="Ok you entered your home internet number   Here are your choices You can say 'plan details', 'usage information' or say, 'it's something else' "/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="mm1045_ini_02">
                    <prompt-segments>
                      <audiofile src="mm1045_ini_02.wav" text="Home internet menu  You can say 'plan details', 'usage information' or say, 'it's something else' "/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MM1045_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1045_nm1_01.wav" text="Please say 'plan details' or press 1  'usage information' or press 2 or say, 'it's something else' or press 3 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1045_ni2_01">
                <prompt-segments>
                  <audiofile src="MM1045_ni2_01.wav" text="Please say 'plan details' or press 1  'usage information' or press 2 or say, 'it's something else' or press 3 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1045_ni2_01">
                <prompt-segments>
                  <audiofile src="MM1045_ni2_01.wav" text="Please say 'plan details' or press 1  'usage information' or press 2 or say, 'it's something else' or press 3 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1045_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1045_nm1_01.wav" text="Please say 'plan details' or press 1  'usage information' or press 2 or say, 'it's something else' or press 3 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1045_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1045_nm2_01.wav" text="Please say 'plan details' or press 1  'usage information' or press 2 or say, 'it's something else' or press 3 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1045_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1045_nm2_01.wav" text="Please say 'plan details' or press 1  'usage information' or press 2 or say, 'it's something else' or press 3 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="MM1045_HomeInternetMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="MM1045_HomeInternetMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.506" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MM1045_HomeInternetMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="MM1600_GoToACP_SD">
      <gotodialog next="AP_ACP_Dialog"/>
      <action next="MM1600_GoToACP_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MM1600_GoToACP_SD_return_CS">
      <action next="MM1031_MainMenu_DM"/>
    </custom-state>

    <subdialog-state id="MM1610_GoToAutoPay_SD">
      <gotodialog next="AutoPay_Main_Dialog"/>
      <action next="MM1610_GoToAutoPay_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MM1610_GoToAutoPay_SD_return_CS">
      <session-mapping key="GlobalVars.callType" expr="undefined"/>
      <action next="getReturnLink()"/>
    </custom-state>

    <dm-state id="MM1047_Disambig_Home_Internet_DM" type="CUST">
      <success>
        <action label="home-internet">
          <session-mapping key="GlobalVars.fromHINTDisambig" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.tag" expr="'home-internet_active'"/>
          <action next="MM1045_HomeInternetMenu_DM"/>
        </action>
        <action label="mobile-phone">
          <action next="MM1037_PlansandAddonsMenu_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MM1047_ini_01">
                <prompt-segments>
                  <audiofile src="MM1047_ini_01.wav" text="For which service? say 'home internet' or 'mobile "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MM1047_ini_01">
                <prompt-segments>
                  <audiofile src="MM1047_ini_01.wav" text="For which service? say 'home internet' or 'mobile "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MM1047_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1047_nm1_01.wav" text="Please say 'home internet' or 'mobile phone' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1047_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1047_nm2_01.wav" text="Say 'home interent', or press 1 'mobile phone' or press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1047_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1047_nm2_01.wav" text="Say 'home interent', or press 1 'mobile phone' or press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1047_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1047_nm1_01.wav" text="Please say 'home internet' or 'mobile phone' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1047_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1047_nm2_01.wav" text="Say 'home interent', or press 1 'mobile phone' or press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1047_nm2_01">
                <prompt-segments>
                  <audiofile src="MM1047_nm2_01.wav" text="Say 'home interent', or press 1 'mobile phone' or press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MM1047_nm1_01">
                <prompt-segments>
                  <audiofile src="MM1047_nm1_01.wav" text="Please say 'home internet' or 'mobile phone' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="MM1047_Disambig_Home_Internet_DM.grxml" count="1"/>
          <dtmfgrammars filename="MM1047_Disambig_Home_Internet_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.507" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MM1047_Disambig_Home_Internet_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  