<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="AutoPay_Main_Dialog">
    <decision-state id="AU1000_AutoPayStart_DS">
      <if cond="GlobalVars.paymentFailure == true">
        <action next="AU1340_GoToTransfer_SD"/>
      </if>
      <if cond="GlobalVars.GetAccountDetails.hasAutopayPending == true">
        <action next="AU1012_PlayAutopayStatus_PP"/>
        <elseif cond="(GlobalVars.loggedIn == true || GlobalVars.loggedIn == 'true')">
          <action next="AU1125_GetAutopayMessage_DB_DA"/>
        </elseif>
        <else>
          <action next="AU1001_GetOTP_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="AU1001_GetOTP_SD">
      <gotodialog next="TwoFactorAuth_Main_Dialog"/>
      <action next="AU1001_GetOTP_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="AU1001_GetOTP_SD_Return_CS">
      <action next="AU1100_GetSecurityCode_DM"/>
    </custom-state>

    <decision-state id="AU1002_CheckAutoPayTag_DS">
      <session-mapping key="isAutopayEnabled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="amountDue" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="numDaysBtwPayAndCurrentDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String"/>
      <if cond="GlobalVars.callType == 'make_pmt_auto_pay'">
        <action next="AU1015_PlayAPInfoConfirmSetup_DM"/>
        <elseif cond="(GlobalVars.tag == 'vague-autopay')">
          <if cond="isAutopayEnabled==true">
            <action next="AU1020_AutoPayMenu_DM"/>
            <else>
              <action next="AU1010_AskSetUpAutoPay_DM"/>
            </else>
          </if>
        </elseif>
        <elseif cond="(GlobalVars.tag == 'setup-autopay')">
          <if cond="isAutopayEnabled==true">
            <action next="AU1012_PlayAutopayStatus_PP"/>
            <else>
              <if cond="numDaysBtwPayAndCurrentDate &lt; 7 &amp;&amp; amountDue &gt; 0">
                <action next="AU1303_AskMakePayment_DM"/>
                <else>
                  <action next="AU1015_PlayAPInfoConfirmSetup_DM"/>
                </else>
              </if>
            </else>
          </if>
        </elseif>
        <else>
          <if cond="isAutopayEnabled==true">
            <action next="AU1302_CheckNextDueDate_JDA"/>
            <else>
              <action next="AU1012_PlayAutopayStatus_PP"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <dm-state id="AU1010_AskSetUpAutoPay_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.tag" expr="'setup-autopay'"/>
          <action next="AU1302_CheckNextDueDate_JDA"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="AU1010_out_01">
              <prompt-segments>
                <audiofile text="Ok" src="AU1010_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AU1010_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to set up autopay now?" src="AU1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AU1010_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to set up autopay now?" src="AU1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AU1010_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to set up autopay?" src="AU1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1010_nm2_01">
                <prompt-segments>
                  <audiofile text="To set up autopay say 'yes' or press 1  or say 'no' or press 2" src="AU1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1010_nm2_01">
                <prompt-segments>
                  <audiofile text="To set up autopay say 'yes' or press 1  or say 'no' or press 2" src="AU1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1010_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to set up autopay?" src="AU1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1010_nm2_01">
                <prompt-segments>
                  <audiofile text="To set up autopay say 'yes' or press 1  or say 'no' or press 2" src="AU1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1010_nm2_01">
                <prompt-segments>
                  <audiofile text="To set up autopay say 'yes' or press 1  or say 'no' or press 2" src="AU1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AU1010_AskSetUpAutoPay_DM.grxml" count="1"/>
          <dtmfgrammars filename="AU1010_AskSetUpAutoPay_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="AU1012_PlayAutopayStatus_PP">
      <session-mapping key="hasAutopayPending" value="GlobalVars.GetAccountDetails.hasAutopayPending" type="String"/>
      <session-mapping key="isAutopayEnabled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <audio>
        <if cond="hasAutopayPending == true">
          <prompt id="AU1012_out_01">
            <prompt-segments>
              <audiofile text="You currently have an auto payment in process  Any changes to your current autopay set up would need to be made after payment is complete" src="AU1012_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="isAutopayEnabled == true">
            <prompt id="AU1012_out_02">
              <prompt-segments>
                <audiofile text="You are already enrolled In autopay" src="AU1012_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="AU1012_out_03">
              <prompt-segments>
                <audiofile text="You're currently not set up on autopay" src="AU1012_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <if cond="hasAutopayPending == true">
        <session-mapping key="GlobalVars.callType" expr="undefined"/>
        <action next="getReturnLink()"/>
        <elseif cond="isAutopayEnabled==true">
          <session-mapping key="GlobalVars.tag" expr="undefined"/>
          <action next="AU1020_AutoPayMenu_DM"/>
        </elseif>
        <else>
          <action next="AU1010_AskSetUpAutoPay_DM"/>
        </else>
      </if>
    </play-state>

    <dm-state id="AU1015_PlayAPInfoConfirmSetup_DM" type="YSNO">
      <session-mapping key="payingWithPrepaid" value="GlobalVars.payingWithPrepaid != undefined ? GlobalVars.payingWithPrepaid : false" type="String"/>
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet != undefined ? GlobalVars.payingWithEWallet : false" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.tag" expr="'setup-autopay'"/>
          <if cond="GlobalVars.recentPayment == true">
            <action next="AU1305_AskUsePaymentCard_DM"/>
            <else>
              <action next="AU1320_GoToManageCards_SD"/>
            </else>
          </if>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AU1015_ini_01">
                <prompt-segments>
                  <audiofile text="Before we set up autopay, please note that auto payments will be processed 2 days in advance of your due date to ensure there is no service interruptionPrior to the due date you will receive a text of with the payment amount that will be charged to the card you set up as your primary autopay cardOnce the payment goes through you will receive another text with your confirmation numberWould you like to continue with setting up autopay?" src="AU1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AU1015_ini_01">
                <prompt-segments>
                  <audiofile text="Before we set up autopay, please note that auto payments will be processed 2 days in advance of your due date to ensure there is no service interruptionPrior to the due date you will receive a text of with the payment amount that will be charged to the card you set up as your primary autopay cardOnce the payment goes through you will receive another text with your confirmation numberWould you like to continue with setting up autopay?" src="AU1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AU1015_nm1_01">
                <prompt-segments>
                  <audiofile text="Just to confirm, you would like to continue with setting up autopay, right?" src="AU1015_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1015_nm2_01">
                <prompt-segments>
                  <audiofile text="To continue with autopay set up say 'yes' or press 1  Or say 'no' or press 2" src="AU1015_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1015_nm2_01">
                <prompt-segments>
                  <audiofile text="To continue with autopay set up say 'yes' or press 1  Or say 'no' or press 2" src="AU1015_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1015_nm1_01">
                <prompt-segments>
                  <audiofile text="Just to confirm, you would like to continue with setting up autopay, right?" src="AU1015_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1015_nm2_01">
                <prompt-segments>
                  <audiofile text="To continue with autopay set up say 'yes' or press 1  Or say 'no' or press 2" src="AU1015_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1015_nm2_01">
                <prompt-segments>
                  <audiofile text="To continue with autopay set up say 'yes' or press 1  Or say 'no' or press 2" src="AU1015_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AU1015_PlayAPInfoConfirmSetup_DM.grxml" count="1"/>
          <dtmfgrammars filename="AU1015_PlayAPInfoConfirmSetup_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AU1020_AutoPayMenu_DM" type="CUST">
      <success>
        <action label="cancel-autopay">
          <session-mapping key="GlobalVars.tag" expr="'cancel-autopay'"/>
          <action next="AU1302_CheckNextDueDate_JDA"/>
        </action>
        <action label="manage-cards">
          <session-mapping key="GlobalVars.manageCardTask" expr="'MainMC'"/>
          <action next="AU1320_GoToManageCards_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AU1020_ini_01">
                <prompt-segments>
                  <audiofile text="Alright, you can 'cancel autopay', or 'manage payment cards'  Which would you like?" src="AU1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AU1020_ini_01">
                <prompt-segments>
                  <audiofile text="Alright, you can 'cancel autopay', or 'manage payment cards'  Which would you like?" src="AU1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AU1020_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'cancel autopay', or 'manage payment cards'" src="AU1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1020_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'cancel autopay' or press 1'manage payment cards' or press 2" src="AU1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1020_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'cancel autopay' or press 1'manage payment cards' or press 2" src="AU1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1020_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'cancel autopay', or 'manage payment cards'" src="AU1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1020_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'cancel autopay' or press 1'manage payment cards' or press 2" src="AU1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1020_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'cancel autopay' or press 1'manage payment cards' or press 2" src="AU1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AU1020_ini_01">
                <prompt-segments>
                  <audiofile text="Alright, you can 'cancel autopay', or 'manage payment cards'  Which would you like?" src="AU1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AU1020_AutoPayMenu_DM.grxml" count="1">
      </grammars>
          <dtmfgrammars filename="AU1020_AutoPayMenu_DM_dtmf.grxml" count="1">
      </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AU1100_GetSecurityCode_DM" type="CUST">
      <session-mapping key="reentry" value="GlobalVars.AU1100reentry != undefined ? GlobalVars.AU1100reentry : false " type="String"/>
      <session-mapping key="lastPinTry" value="GlobalVars.lastPinTry" type="String"/>
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <success>
        <session-mapping key="GlobalVars.playTransitionalPINprompt" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.AU1100reentry" value="true" type="Boolean"/>
        <action label="dont_know">
          <audio>
            <prompt id="AU1100_out_01">
              <prompt-segments>
                <audiofile src="AU1100_out_01.wav" text="No problem"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.dontKnowPIN" value="true" type="Boolean"/>
          <action next="AU1340_GoToTransfer_SD"/>
        </action>
        <action label="default" next="AU1110_Authenticate_DB_DA">
          <session-mapping key="pinAttempts" expr="pinAttempts + 1"/>
          <session-mapping key="pin" expr="AU1100_GetSecurityCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.verificationType" expr="'pin'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="AU1100_GetSecurityCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.securityCode" expr="AU1100_GetSecurityCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.cti_PIN" expr="AU1100_GetSecurityCode_DM.returnvalue"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration reco_suppress_logs="true" highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AU1100_ini_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="Now, I need your 6-to-15-digit account PIN" src="AU1100_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_ini_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="Now, I need your 8-digit account PIN" src="AU1100_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AU1100_GetSecurityCode_DM_reinvoke"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AU1100_ini_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="Now, I need your 6-to-15-digit account PIN" src="AU1100_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_ini_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="Now, I need your 8-digit account PIN" src="AU1100_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AU1100_nm1_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="Please say or enter your  6-to-15-digit Metro account PIN one digit at a time You can also say 'I don't' know it' " src="AU1100_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_nm1_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="Please say or enter your  8-digit Metro account PIN one digit at a time You can also say 'I don't' know it" src="AU1100_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_nm2_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="We are looking for the account PIN you set up when you first got your phone  Please enter your 6-to-15-digit account PIN or say 'I don't' know it' " src="AU1100_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_nm2_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="We are looking for the account PIN you set up when you first got your phone Please enter your 8-digit account PIN or say 'I don't' know it' " src="AU1100_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_nm2_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="We are looking for the account PIN you set up when you first got your phone  Please enter your 6-to-15-digit account PIN or say 'I don't' know it' " src="AU1100_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_nm2_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="We are looking for the account PIN you set up when you first got your phone Please enter your 8-digit account PIN or say 'I don't' know it' " src="AU1100_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_nm1_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="Please say or enter your  6-to-15-digit Metro account PIN one digit at a time You can also say 'I don't' know it' " src="AU1100_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_nm1_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="Please say or enter your  8-digit Metro account PIN one digit at a time You can also say 'I don't' know it" src="AU1100_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_nm2_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="We are looking for the account PIN you set up when you first got your phone  Please enter your 6-to-15-digit account PIN or say 'I don't' know it' " src="AU1100_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_nm2_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="We are looking for the account PIN you set up when you first got your phone Please enter your 8-digit account PIN or say 'I don't' know it' " src="AU1100_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_nm2_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="We are looking for the account PIN you set up when you first got your phone  Please enter your 6-to-15-digit account PIN or say 'I don't' know it' " src="AU1100_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_nm2_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="We are looking for the account PIN you set up when you first got your phone Please enter your 8-digit account PIN or say 'I don't' know it' " src="AU1100_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AU1100_ini_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="Now, I need your 6-to-15-digit account PIN" src="AU1100_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1100_ini_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="Now, I need your 8-digit account PIN" src="AU1100_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AU1100_GetSecurityCode_DM.grxml" count="1">
      </grammars>
          <dtmfgrammars filename="AU1100_GetSecurityCode_DM_dtmf.grxml" count="1">
      </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration reco_suppress_logs="true" highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="AU1110_Authenticate_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="verificationType" value="GlobalVars.verificationType" type="String"/>
      <session-mapping key="verificationValue" value="GlobalVars.verificationValue" type="String"/>
      <session-mapping key="accessToken" value="GlobalVars.accessToken" type="String"/>
      <session-mapping key="pin" value="GlobalVars.verificationValue" type="String"/>
      <data-access id="Authenticate" classname="com.nuance.metro.dataaccess.ValidatePinForAuthenticate">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="pin" mask="true"/>
          <input-variable name="accessToken" mask="false"/>
          <input-variable name="verificationType"/>
          <input-variable name="verificationValue" mask="true"/>
          <input-variable name="sessionId"/>
          <input-variable name="providerId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="JWTToken"/>
          <output-variable name="expiresIn"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="Authenticate.status == 'FAILURE'">
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          <action next="AU1340_GoToTransfer_SD"/>
          <elseif cond="Authenticate.acctLocked == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.acctLocked" value="true" type="Boolean"/>
            <action next="AU1120_PINMaxErrors_PP"/>
          </elseif>
          <elseif cond="Authenticate.onePinTryRemaining == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.onePinTryRemaining" value="true" type="Boolean"/>
            <action next="AU1115_CheckSecurityCodeMatch_JDA"/>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <action next="AU1115_CheckSecurityCodeMatch_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="AU1115_CheckSecurityCodeMatch_DS">
      <if cond="(GlobalVars.Authenticate.status != 'SUCCESS')">
        <if cond="(GlobalVars.acctLocked == true)">
          <action next="AU1340_GoToTransfer_SD"/>
          <elseif cond="(GlobalVars.onePinTryRemaining == true)">
            <action next="AU1117_AskResetInformation_DM"/>
          </elseif>
          <else>
            <action next="AU1100_GetSecurityCode_DM"/>
          </else>
        </if>
        <else>
          <if cond="GlobalVars.fromManageCards == true">
            <session-mapping key="GlobalVars.loggedIn" value="true" type="Boolean"/>
            <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
            <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'authenticated_by_pin')"/>
            <action next="AU1320_GoToManageCards_SD"/>
          </if>
          <session-mapping key="GlobalVars.loggedIn" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'authenticated_by_pin')"/>
          <action next="AU1125_GetAutopayMessage_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="AU1117_AskResetInformation_DM" type="YSNO">
      <success>
        <action label="true">
          <action next="AU1118_GoToPasswordReset_SD"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.lastPinTry" value="true" type="Boolean"/>
          <action next="AU1100_GetSecurityCode_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AU1117_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, that still didnt matchWould you like information on how to reset your pin online?" src="AU1117_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AU1117_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, that still didnt matchWould you like information on how to reset your pin online?" src="AU1117_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AU1117_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes  Otherwise say No" src="AU1117_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1117_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1If you'd like to try your pin once more say 'no' or press 2" src="AU1117_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1117_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1If you'd like to try your pin once more say 'no' or press 2" src="AU1117_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1117_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes  Otherwise say No" src="AU1117_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1117_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1If you'd like to try your pin once more say 'no' or press 2" src="AU1117_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1117_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1If you'd like to try your pin once more say 'no' or press 2" src="AU1117_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AU1117_AskResetInformation_DM.grxml" count="1"/>
          <dtmfgrammars filename="AU1117_AskResetInformation_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="AU1118_GoToPasswordReset_SD">
      <gotodialog next="AccountPINReset_Main_Dialog"/>
      <action next="AU1118_GoToPasswordReset_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AU1118_GoToPasswordReset_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <play-state id="AU1120_PINMaxErrors_PP">
      <session-mapping key="GlobalVars.playedAcctLocked" value="true" type="Boolean"/>
      <audio>
        <prompt id="AU1120_out_01">
          <prompt-segments>
            <audiofile src="AU1120_out_01.wav" text="Sorry, I am unable to access your account"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AU1340_GoToTransfer_SD"/>
    </play-state>

    <data-access-state id="AU1125_GetAutopayMessage_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn != undefined ? GlobalVars.mdn  : GlobalVars.trn" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="GetAutoPayMessages" classname="com.nuance.metro.dataaccess.GetAutoPayMessages">
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetAutoPayMessages &amp;&amp; GetAutoPayMessages.status &amp;&amp; GetAutoPayMessages.status.toUpperCase() == 'SUCCESS'">
          <session-mapping key="GlobalVars.GetAutoPayMessages" expr="GetAutoPayMessages"/>
        </if>
        <action next="AU1002_CheckAutoPayTag_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="AU1302_CheckNextDueDate_DS">
      <session-mapping key="numDaysBtwPayAndCurrentDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String"/>
      <if cond="GlobalVars.tag == 'setup-autopay'">
        <if cond="numDaysBtwPayAndCurrentDate &lt; 7">
          <action next="AU1303_AskMakePayment_DM"/>
          <else>
            <if cond="GlobalVars.recentPayment == true">
              <action next="AU1305_AskUsePaymentCard_DM"/>
              <else>
                <action next="AU1320_GoToManageCards_SD"/>
              </else>
            </if>
          </else>
        </if>
        <else>
          <if cond="numDaysBtwPayAndCurrentDate &lt; 7">
            <action next="AU1340_GoToTransfer_SD"/>
            <else>
              <action next="AU1304_ConfirmCancel_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <dm-state id="AU1303_AskMakePayment_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.callType" expr="'make_pmt_auto_pay'"/>
          <action next="AU1310_GoToMakePayment_SD"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <audio>
            <prompt id="AU1303_out_01">
              <prompt-segments>
                <audiofile src="AU1303_out_01.wav" text="Ok, please call back later to set up autopay"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AU1303_ini_01">
                <prompt-segments>
                  <audiofile src="AU1303_ini_01.wav" text="Since you are so close to your next due date you'll need to make one more manual payment before autopay would take effect  If you make a manual payment now, we can then set up autopay    Would you like to pay now? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AU1303_ini_01">
                <prompt-segments>
                  <audiofile src="AU1303_ini_01.wav" text="Since you are so close to your next due date you'll need to make one more manual payment before autopay would take effect  If you make a manual payment now, we can then set up autopay    Would you like to pay now? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AU1303_nm1_01">
                <prompt-segments>
                  <audiofile src="AU1303_nm1_01.wav" text="Since you are so close to your due date, you'll need to make one more manual payment before autopay would go into effect  If you'd like to make a payment now press 1  if not press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1303_nm2_01">
                <prompt-segments>
                  <audiofile src="AU1303_nm2_01.wav" text="To make a payment before setting up autopay press 1  if not press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1303_nm2_01">
                <prompt-segments>
                  <audiofile src="AU1303_nm2_01.wav" text="To make a payment before setting up autopay press 1  if not press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1303_nm1_01">
                <prompt-segments>
                  <audiofile src="AU1303_nm1_01.wav" text="Since you are so close to your due date, you'll need to make one more manual payment before autopay would go into effect  If you'd like to make a payment now press 1  if not press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1303_nm2_01">
                <prompt-segments>
                  <audiofile src="AU1303_nm2_01.wav" text="To make a payment before setting up autopay press 1  if not press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1303_nm2_01">
                <prompt-segments>
                  <audiofile src="AU1303_nm2_01.wav" text="To make a payment before setting up autopay press 1  if not press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AU1303_ini_01">
                <prompt-segments>
                  <audiofile src="AU1303_ini_01.wav" text="Since you are so close to your next due date you'll need to make one more manual payment before autopay would take effect  If you make a manual payment now, we can then set up autopay    Would you like to pay now? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AU1303_AskMakePayment_DM.grxml" count="1"/>
          <dtmfgrammars filename="AU1303_AskMakePayment_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AU1304_ConfirmCancel_DM" type="YSNO">
      <session-mapping key="isAutopayEligPlanExists" value="GlobalVars.GetAccountDetails.isAutopayEligPlanExists" type="String"/>
      <session-mapping key="messageCode" value="GlobalVars.GetAutoPayMessages &amp;&amp; GlobalVars.GetAutoPayMessages.messageCode  ? GlobalVars.GetAutoPayMessages.messageCode:''" type="String"/>
      <session-mapping key="discountAmount" value="(GlobalVars.GetAutoPayMessages  &amp;&amp; GlobalVars.GetAutoPayMessages.discountAmount) ? GlobalVars.GetAutoPayMessages.discountAmount: 0.0" type="String"/>
      <session-mapping key="amountAfterDiscount" value="(GlobalVars.GetAutoPayMessages &amp;&amp; GlobalVars.GetAutoPayMessages.amountAfterDiscount) ? GlobalVars.GetAutoPayMessages.amountAfterDiscount: 0.0" type="String"/>
      <session-mapping key="calculatedDiscountAmount" value="" type="String"/>
      <session-mapping key="balance" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <if cond="(GlobalVars.GetAutoPayMessages ) &amp;&amp; (messageCode == '9001')">
        <session-mapping key="calculatedDiscountAmount" expr="(Math.abs(parseFloat(balance) - parseFloat(amountAfterDiscount))).toString()"/>
      </if>
      <success>
        <action label="true">
          <action next="AU1320_GoToManageCards_SD"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <audio>
            <prompt id="AU1304_out_01">
              <prompt-segments>
                <audiofile src="AU1304_out_01.wav" text="Ok, your autopay will NOT be cancelled "/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AU1304_ini_01" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_01.wav" text="Keep in mind that cancelling autopay will remove your monthly autopay discount of "/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="discountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="calculatedDiscountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="AU1304_ini_03" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_03.wav" text="Would you like to continue with the cancellation? "/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_04" cond="!(isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003'))">
                <prompt-segments>
                  <audiofile src="AU1304_ini_04.wav" text="To confirm, you'd like to cancel autopay right?  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AU1304_ini_01" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_01.wav" text="Keep in mind that cancelling autopay will remove your monthly autopay discount of "/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="discountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="calculatedDiscountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="AU1304_ini_03" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_03.wav" text="Would you like to continue with the cancellation? "/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_04" cond="!(isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003'))">
                <prompt-segments>
                  <audiofile src="AU1304_ini_04.wav" text="To confirm, you'd like to cancel autopay right?  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AU1304_ini_01" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_01.wav" text="Keep in mind that cancelling autopay will remove your monthly autopay discount of "/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="discountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="calculatedDiscountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="AU1304_ini_03" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_03.wav" text="Would you like to continue with the cancellation? "/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_04" cond="!(isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003'))">
                <prompt-segments>
                  <audiofile src="AU1304_ini_04.wav" text="To confirm, you'd like to cancel autopay right?  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_01" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_01.wav" text="Keep in mind that cancelling autopay will remove your monthly autopay discount of "/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="discountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="calculatedDiscountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="AU1304_ini_03" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_03.wav" text="Would you like to continue with the cancellation? "/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_04" cond="!(isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003'))">
                <prompt-segments>
                  <audiofile src="AU1304_ini_04.wav" text="To confirm, you'd like to cancel autopay right?  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_01" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_01.wav" text="Keep in mind that cancelling autopay will remove your monthly autopay discount of "/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="discountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="calculatedDiscountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="AU1304_ini_03" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_03.wav" text="Would you like to continue with the cancellation? "/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_04" cond="!(isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003'))">
                <prompt-segments>
                  <audiofile src="AU1304_ini_04.wav" text="To confirm, you'd like to cancel autopay right?  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_01" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_01.wav" text="Keep in mind that cancelling autopay will remove your monthly autopay discount of "/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="discountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="calculatedDiscountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="AU1304_ini_03" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_03.wav" text="Would you like to continue with the cancellation? "/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_04" cond="!(isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003'))">
                <prompt-segments>
                  <audiofile src="AU1304_ini_04.wav" text="To confirm, you'd like to cancel autopay right?  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_01" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_01.wav" text="Keep in mind that cancelling autopay will remove your monthly autopay discount of "/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="discountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="calculatedDiscountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="AU1304_ini_03" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_03.wav" text="Would you like to continue with the cancellation? "/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_04" cond="!(isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003'))">
                <prompt-segments>
                  <audiofile src="AU1304_ini_04.wav" text="To confirm, you'd like to cancel autopay right?  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_01" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_01.wav" text="Keep in mind that cancelling autopay will remove your monthly autopay discount of "/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="discountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="calculatedDiscountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="AU1304_ini_03" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_03.wav" text="Would you like to continue with the cancellation? "/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_04" cond="!(isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003'))">
                <prompt-segments>
                  <audiofile src="AU1304_ini_04.wav" text="To confirm, you'd like to cancel autopay right?  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AU1304_ini_01" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_01.wav" text="Keep in mind that cancelling autopay will remove your monthly autopay discount of "/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="discountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9002' || messageCode == '9003')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="calculatedDiscountAmount" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001')">
                <param value="false" name="playZeroCents"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="AU1304_ini_03" cond="isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003')">
                <prompt-segments>
                  <audiofile src="AU1304_ini_03.wav" text="Would you like to continue with the cancellation? "/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1304_ini_04" cond="!(isAutopayEligPlanExists == true &amp;&amp; (messageCode == '9001' || messageCode == '9002' ||messageCode == '9003'))">
                <prompt-segments>
                  <audiofile src="AU1304_ini_04.wav" text="To confirm, you'd like to cancel autopay right?  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AU1304_ConfirmCancel_DM.grxml" count="1"/>
          <dtmfgrammars filename="AU1304_ConfirmCancel_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AU1305_AskUsePaymentCard_DM" type="YSNO">
      <session-mapping key="GlobalVars.visitedAu1305" value="true" type="Boolean"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.useLastPaymentCard" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.autopay" value="true" type="Boolean"/>
          <action next="AU1320_GoToManageCards_SD"/>
        </action>
        <action label="false">
          <action next="AU1320_GoToManageCards_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AU1305_ini_01">
                <prompt-segments>
                  <audiofile src="AU1305_ini_01.wav" text="Would you like to save the card you just paid with as your primary autopay card? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AU1305_ini_01">
                <prompt-segments>
                  <audiofile src="AU1305_ini_01.wav" text="Would you like to save the card you just paid with as your primary autopay card? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AU1305_nm1_01">
                <prompt-segments>
                  <audiofile src="AU1305_nm1_01.wav" text="Would you like to use the card you just made a payment with as your primary autopay card? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1305_nm2_01">
                <prompt-segments>
                  <audiofile src="AU1305_nm2_01.wav" text="If you want to use the card you just made a payment with as your primary autopay card say 'yes' or press 1   To enter a different card say 'no' or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1305_nm2_01">
                <prompt-segments>
                  <audiofile src="AU1305_nm2_01.wav" text="If you want to use the card you just made a payment with as your primary autopay card say 'yes' or press 1   To enter a different card say 'no' or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1305_nm1_01">
                <prompt-segments>
                  <audiofile src="AU1305_nm1_01.wav" text="Would you like to use the card you just made a payment with as your primary autopay card? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1305_nm2_01">
                <prompt-segments>
                  <audiofile src="AU1305_nm2_01.wav" text="If you want to use the card you just made a payment with as your primary autopay card say 'yes' or press 1   To enter a different card say 'no' or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AU1305_nm2_01">
                <prompt-segments>
                  <audiofile src="AU1305_nm2_01.wav" text="If you want to use the card you just made a payment with as your primary autopay card say 'yes' or press 1   To enter a different card say 'no' or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AU1305_ini_01">
                <prompt-segments>
                  <audiofile src="AU1305_ini_01.wav" text="Would you like to save the card you just paid with as your primary autopay card? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AU1305_AskUsePaymentCard_DM.grxml" count="1"/>
          <dtmfgrammars filename="AU1305_AskUsePaymentCard_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="AU1310_GoToMakePayment_SD">
      <gotodialog next="StartServicePayment_Main_Dialog"/>
      <action next="AU1310_GoToMakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AU1310_GoToMakePayment_SD_return_CS">
      <session-mapping key="payingWithPrepaid" value="GlobalVars.payingWithPrepaid != undefined ? GlobalVars.payingWithPrepaid : false" type="String"/>
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet != undefined ? GlobalVars.payingWithEWallet : false" type="String"/>
      <if cond="GlobalVars.callType == 'goodbye'">
        <action next="AU1345_GoToGoodby_SD"/>
      </if>
      <if cond="GlobalVars.paymentFailure == true">
        <action next="AU1340_GoToTransfer_SD"/>
      </if>
      <if cond="GlobalVars.callType == undefined">
        <action next="getReturnLink()"/>
      </if>
      <if cond="GlobalVars.visitedAu1305 == true">
        <action next="getReturnLink()"/>
      </if>
      <if cond="GlobalVars.recentPayment == true">
        <action next="AU1305_AskUsePaymentCard_DM"/>
        <else>
          <action next="AU1320_GoToManageCards_SD"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="AU1320_GoToManageCards_SD">
      <gotodialog next="ManageCards_Main_Dialog"/>
      <action next="AU1320_GoToManageCards_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AU1320_GoToManageCards_SD_return_CS">
      <if cond="GlobalVars.failedChecksum == true">
        <action next="getReturnLink()"/>
        <elseif cond="GlobalVars.tag  == 'setup-autopay'">
          <action next="AU1335_PlaySuccess_PP"/>
        </elseif>
        <else>
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </custom-state>

    <play-state id="AU1335_PlaySuccess_PP">
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails.payDate" type="String"/>
      <session-mapping key="isAutopayEligPlanExists" value="GlobalVars.GetAccountDetails.isAutopayEligPlanExists" type="String"/>
      <audio>
        <prompt id="AU1335_out_04">
          <prompt-segments>
            <audiofile src="AU1335_out_04.wav" text="Ok you're all set up on autopay starting with your next payment  Please note, it may take up to 2 hours for the change to be reflected on your account"/>
          </prompt-segments>
        </prompt>
        <prompt id="AU1335_out_01" cond="isAutopayEligPlanExists == true">
          <prompt-segments>
            <audiofile src="AU1335_out_01.wav" text="Please allow 1-2 autopay cycles for the autopay discount to be applied"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.nluReEntryAfterSelfService" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.tag" expr="undefined"/>
      <session-mapping key="GlobalVars.callType" expr="undefined"/>
      <action next="AU1350_GoToWrap_SD"/>
    </play-state>

    <subdialog-state id="AU1340_GoToTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="AU1340_GoToTransfer_SD_return"/>
    </subdialog-state>
    <subdialog-state id="AU1345_GoToGoodby_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="AU1345_GoToGoodby_SD_return"/>
    </subdialog-state>
    <subdialog-state id="AU1350_GoToWrap_SD">
      <gotodialog next="WR_AutopayManageCardWrap_Main_Dialog"/>
      <action next="AU1350_GoToWrap_SD_return"/>
    </subdialog-state>
  </dialog>
  