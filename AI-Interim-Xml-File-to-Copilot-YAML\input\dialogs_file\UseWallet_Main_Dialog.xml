<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="UseWallet_Main_Dialog">
    <decision-state id="UW1001_CheckContext_DS">
      <action next="UW1005_UseSavedCard_YN_DM"/>
    </decision-state>

    <dm-state id="UW1005_UseSavedCard_YN_DM" type="YSNO">
      <success>
        <action label="true">
          <if cond="GlobalVars.loggedIn == true">
            <action next="UW1105_GetWalletItems_DB_DA"/>
            <else>
              <session-mapping key="GlobalVars.askSQEntry" expr="'EWallet'"/>
              <session-mapping key="GlobalVars.abandonEWalletAuth" value="false" type="Boolean"/>
              <action next="UW1004_GetOPT_SD"/>
            </else>
          </if>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="UW1005_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay with a card you've saved?" src="UW1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="UW1005_UseSavedCard_YN_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="UW1005_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay with a card you've saved?" src="UW1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="UW1005_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay with a card you've already saved?" src="UW1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="UW1005_nm2_01">
                <prompt-segments>
                  <audiofile text="To pay with a card you've saved, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="UW1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="UW1005_nm3_01">
                <prompt-segments>
                  <audiofile text="To pay with a card you've saved to your account, press 1 To pay with a new card, press 2" src="UW1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1005_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay with a card you've already saved?" src="UW1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1005_nm2_01">
                <prompt-segments>
                  <audiofile text="To pay with a card you've saved, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="UW1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1005_nm3_01">
                <prompt-segments>
                  <audiofile text="To pay with a card you've saved to your account, press 1 To pay with a new card, press 2" src="UW1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="UW1005_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay with a card you've saved?" src="UW1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="UW1005_UseSavedCard_YN_DM.grxml" count="1"/>
          <dtmfgrammars filename="UW1005_UseSavedCard_YN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="UW1004_GetOPT_SD">
      <gotodialog next="TwoFactorAuth_Main_Dialog"/>
      <action next="UW1004_GetOPT_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="UW1004_GetOPT_SD_return_CS">
      <action next="UW1010_GetSecurityCode_SD"/>
    </custom-state>

    <subdialog-state id="UW1010_GetSecurityCode_SD">
      <gotodialog next="GetSecurityCode_Main_Dialog"/>
      <action next="UW1010_GetSecurityCode_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="UW1010_GetSecurityCode_SD_return_CS">
      <if cond="GlobalVars.loggedIn == true">
        <action next="UW1105_GetWalletItems_DB_DA"/>
        <else>
          <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
          <if cond="GlobalVars.transferReason == 'dbfail'">
            <session-mapping key="GlobalVars.transferReason" expr="undefined"/>
          </if>
          <if cond="GlobalVars.abandonEWalletAuth == true">
            <action next="UW1125_UseNewCard_PP"/>
            <else>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </else>
      </if>
    </custom-state>

    <data-access-state id="UW1105_GetWalletItems_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.mdn:''" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <data-access id="GetWalletItems" classname="com.nuance.metro.dataaccess.GetWalletItems">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="JWTToken"/>
          <input-variable name="sessionID"/>
        </inputs>
        <outputs>
          <output-variable name="brand"/>
          <output-variable name="lastFourDigitsOfCard"/>
          <output-variable name="savedCardPreference"/>
          <output-variable name="walletItems[n].displayName" mask="true"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.GetWalletInfo" expr="GetWalletItems"/>
        <action next="UW1110_CheckCards_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="UW1110_CheckCards_DS">
      <session-mapping key="numberOfCards" value="0" type="String"/>
      <if cond="GlobalVars.GetWalletInfo &amp;&amp; GlobalVars.GetWalletInfo.status == 'Success' &amp;&amp; GlobalVars.GetWalletInfo.walletItems != undefined">
        <session-mapping key="numberOfCards" expr="GlobalVars.GetWalletInfo.walletItems.length"/>
      </if>
      <if cond="numberOfCards &gt; 1">
        <action next="UW1115_ChooseCard_DM"/>
        <elseif cond="numberOfCards == 1">
          <action next="UW1120_ConfirmCardYN_DM"/>
        </elseif>
        <else>
          <action next="UW1125_UseNewCard_PP"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="UW1115_ChooseCard_DM" type="CUST">
      <session-mapping key="paymentOptions" value="" type="String"/>
      <success>
        <action label="choose-card_none">
          <action next="UW1125_UseNewCard_PP"/>
        </action>
        <action label="default" next="UW1130_CheckHaveMethodSet_JDA">
          <session-mapping key="GlobalVars.paymentOptionId" expr="UW1115_ChooseCard_DM.returnvalue"/>
          <session-mapping key="GlobalVars.payingWithEWallet" value="true" type="Boolean"/>
          <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'106'"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="UW1115_ini_01">
                <prompt-segments>
                  <audiofile text="Here are the cards I have for you When you hear the one you want, tell me the last 4 digits" src="UW1115_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAllWalletItems"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1115_ini_05">
                <prompt-segments>
                  <audiofile text="Just say or enter the four digits for the card you want to use today Or say 'none of those'" src="UW1115_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="UW1115_ChooseCard_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="UW1115_ini_01">
                <prompt-segments>
                  <audiofile text="Here are the cards I have for you When you hear the one you want, tell me the last 4 digits" src="UW1115_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAllWalletItems"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1115_ini_05">
                <prompt-segments>
                  <audiofile text="Just say or enter the four digits for the card you want to use today Or say 'none of those'" src="UW1115_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="UW1115_nm1_01">
                <prompt-segments>
                  <audiofile text="Say or enter the last four digits for the card you want to use Or say 'none of those'" src="UW1115_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="UW1115_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say or enter the last four digits for the card you want to use You can also say 'none of those' or press star" src="UW1115_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="UW1115_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter the last four digits for the card you want to use If you want to pay with a new card, press star" src="UW1115_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1115_nm1_01">
                <prompt-segments>
                  <audiofile text="Say or enter the last four digits for the card you want to use Or say 'none of those'" src="UW1115_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1115_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say or enter the last four digits for the card you want to use You can also say 'none of those' or press star" src="UW1115_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1115_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter the last four digits for the card you want to use If you want to pay with a new card, press star" src="UW1115_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="UW1115_ini_01">
                <prompt-segments>
                  <audiofile text="Here are the cards I have for you When you hear the one you want, tell me the last 4 digits" src="UW1115_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAllWalletItems"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1115_ini_05">
                <prompt-segments>
                  <audiofile text="Just say or enter the four digits for the card you want to use today Or say 'none of those'" src="UW1115_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="UW1115_ChooseCard_DM.jsp" count="1">
            <param name="paymentOptions" value="paymentOptionsVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="UW1115_ChooseCard_DM_dtmf.jsp" count="1">
            <param name="paymentOptions" value="paymentOptionsVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="7000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="2000ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySingleWalletItem"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="UW1115_ChooseCard_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySingleWalletItem"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySingleWalletItem"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="UW1120_ConfirmCardYN_DM" type="YSNO">
      <session-mapping key="paymentOptionId" value="GlobalVars.GetWalletInfo.walletItems[0].paymentOptionId" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.paymentOptionId" expr="GlobalVars.GetWalletInfo.walletItems[0].paymentOptionId"/>
          <session-mapping key="GlobalVars.payingWithEWallet" value="true" type="Boolean"/>
          <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'106'"/>
          <session-mapping key="GlobalVars.savedCardPreference" expr="GlobalVars.GetWalletInfo.walletItems[0].savedCardPreference"/>
          <session-mapping key="GlobalVars.lastFourDigitsOfCard" expr="GlobalVars.GetWalletInfo.walletItems[0].lastFourDigitsOfCard"/>
          <action next="UW1130_CheckHaveMethodSet_JDA"/>
        </action>
        <action label="false">
          <action next="UW1125_UseNewCard_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="UW1120_ini_01">
                <prompt-segments>
                  <audiofile text="The card I have for you is" src="UW1120_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="paymentOptionId">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySingleWalletItem"/>
                <param name="variableName" value="paymentOptionId"/>
                <param name="variableScope" value="request"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1120_ini_03">
                <prompt-segments>
                  <audiofile text="Is that the one you want to use?" src="UW1120_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="UW1120_ConfirmCardYN_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="UW1120_ini_01">
                <prompt-segments>
                  <audiofile text="The card I have for you is" src="UW1120_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="paymentOptionId">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySingleWalletItem"/>
                <param name="variableName" value="paymentOptionId"/>
                <param name="variableScope" value="request"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1120_ini_03">
                <prompt-segments>
                  <audiofile text="Is that the one you want to use?" src="UW1120_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="UW1120_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to use that card?" src="UW1120_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="UW1120_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to use this saved card, say 'yes' or press 1 Otherwise, to choose another card, say 'no' or press 2" src="UW1120_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="UW1120_nm3_01">
                <prompt-segments>
                  <audiofile text="To pay with the card I have on file for you, press 1 To pay with a different card, press 2" src="UW1120_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1120_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to use that card?" src="UW1120_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1120_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to use this saved card, say 'yes' or press 1 Otherwise, to choose another card, say 'no' or press 2" src="UW1120_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1120_nm3_01">
                <prompt-segments>
                  <audiofile text="To pay with the card I have on file for you, press 1 To pay with a different card, press 2" src="UW1120_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="UW1120_ini_01">
                <prompt-segments>
                  <audiofile text="The card I have for you is" src="UW1120_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="paymentOptionId">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySingleWalletItem"/>
                <param name="variableName" value="paymentOptionId"/>
                <param name="variableScope" value="request"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1120_ini_03">
                <prompt-segments>
                  <audiofile text="Is that the one you want to use?" src="UW1120_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="UW1120_ConfirmCardYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="UW1120_ConfirmCardYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="UW1125_UseNewCard_PP">
      <session-mapping key="numberOfCards" value="-1" type="String"/>
      <session-mapping key="dbError" value="true" type="Boolean"/>
      <session-mapping key="abandonEWalletAuth" value="GlobalVars.abandonEWalletAuth" type="String"/>
      <if cond="GlobalVars.GetWalletInfo &amp;&amp; GlobalVars.GetWalletInfo.status == 'Success' &amp;&amp; GlobalVars.GetWalletInfo.walletItems">
        <session-mapping key="numberOfCards" expr="GlobalVars.GetWalletInfo.walletItems.length"/>
        <session-mapping key="dbError" value="false" type="Boolean"/>
      </if>
      <audio>
        <if type="vxml" cond="abandonEWalletAuth == true">
          <prompt id="UW1125_out_04">
            <prompt-segments>
              <audiofile text="I'm sorry We can't go on without your security information So I'll just ask you for your card details" src="UW1125_out_04.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="numberOfCards == 0">
            <prompt id="UW1125_out_01">
              <prompt-segments>
                <audiofile text="Actually, I don't see any saved cards on your account Let's go ahead and use a new one" src="UW1125_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="dbError == true">
            <prompt id="UW1125_out_02">
              <prompt-segments>
                <audiofile text="I'm sorry, I'm having some trouble looking up your saved cards So I'll just ask you for your card details" src="UW1125_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="UW1125_out_03">
              <prompt-segments>
                <audiofile text="No problem Let's use a new one" src="UW1125_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
      <action next="getReturnLink()"/>
    </play-state>

    <decision-state id="UW1130_CheckHaveMethodSet_DS">
      <if cond="GlobalVars.savedCardPreference == 'UNKNOWN' || GlobalVars.savedCardPreference == 'unknown'">
        <action next="UW1135_DebitOrCredit_DM"/>
        <else>
          <if cond="GlobalVars.savedCardPreference == 'DEBIT' || GlobalVars.savedCardPreference == 'debit'">
            <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'debit'"/>
            <session-mapping key="PaymentTable.CARD_TYPE" expr="'debit'"/>
            <else>
              <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'credit'"/>
              <session-mapping key="PaymentTable.CARD_TYPE" expr="'credit'"/>
            </else>
          </if>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="UW1135_DebitOrCredit_DM" type="CUST">
      <success>
        <action label="credit">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'credit'"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'credit'"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="debit">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'debit'"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'debit'"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="UW1135_ini_01">
                <prompt-segments>
                  <audiofile text="Is this a 'credit' or a 'debit' card?" src="UW1135_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="UW1135_ini_01">
                <prompt-segments>
                  <audiofile text="Is this a 'credit' or a 'debit' card?" src="UW1135_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="UW1135_nm1_01">
                <prompt-segments>
                  <audiofile text="Is the card you chose a 'credit' card, or a 'debit' card?" src="UW1135_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="UW1135_nm2_01">
                <prompt-segments>
                  <audiofile text="If the card you chose is a credit card, say 'credit' or press 1 Or  say 'debit' or press 2" src="UW1135_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="UW1135_nm3_01">
                <prompt-segments>
                  <audiofile text="I need to know the type of card you're using today If the card you chose is a credit card, press 1 If it's a debit card, press 2" src="UW1135_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1135_nm1_01">
                <prompt-segments>
                  <audiofile text="Is the card you chose a 'credit' card, or a 'debit' card?" src="UW1135_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1135_nm2_01">
                <prompt-segments>
                  <audiofile text="If the card you chose is a credit card, say 'credit' or press 1 Or  say 'debit' or press 2" src="UW1135_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UW1135_nm3_01">
                <prompt-segments>
                  <audiofile text="I need to know the type of card you're using today If the card you chose is a credit card, press 1 If it's a debit card, press 2" src="UW1135_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="UW1135_ini_01">
                <prompt-segments>
                  <audiofile text="Is this a 'credit' or a 'debit' card?" src="UW1135_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="UW1135_DebitOrCredit_DM.grxml" count="1"/>
          <dtmfgrammars filename="UW1135_DebitOrCredit_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  