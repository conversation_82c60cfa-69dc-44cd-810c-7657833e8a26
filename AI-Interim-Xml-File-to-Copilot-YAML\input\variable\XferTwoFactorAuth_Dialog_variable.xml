<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="JWTToken" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="tempCode" value="empty" type="String"/>
  <session-mapping key="GlobalVars.care_enable_twofactorauth" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.twoFactorAuthEntryPoint" value="228_esn" type="string"/>
  <session-mapping key="GlobalVars.star228_enable_twofactorauth" value="true" type="boolean"/>
  <session-mapping key="aniMatch" value="true" type="boolean"/>
  <session-mapping key="switchLinesSuccess" value="true" type="boolean"/>
  <session-mapping key="switchLinesMDNMatch" value="true" type="string"/>
  <session-mapping key="usingOldSIMForSwap" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.aniMatch" value="true" type="string"/>
  <session-mapping key="GlobalVars.switchLinesSuccess" value="false" type="string"/>
  <session-mapping key="GetBCSParameters.care_transfer_twofactorauth_nophone" value="true" type="boolean"/>
  <session-mapping key="fromMultilineFallBack" value="true" type="string"/>
  <session-mapping key="GenerateTempCode.status" value="Success" type="string"/>
  <session-mapping key="twoFactorHoldCounter" value="0" type="integer"/>
  <session-mapping key="GlobalVars.twoFactorHoldCounter" value="1" type="integer"/>
  <session-mapping key="ValidateTempCode.status" value="Failure" type="string"/>
  <session-mapping key="ValidateTempCode.acctLocked" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.validateTempCodeStatus" value="Success" type="string"/>
  <session-mapping key="GlobalVars.twoFactorAuthNumberRetries" value="0" type="integer"/>
  <session-mapping key="GlobalVars.acctLocked" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.TF1405Status" value="INVALID_OTP" type="string"/>
  <session-mapping key="visitedTFRetry_counter" value="0" type="integer"/>
  <session-mapping key="GlobalVars.isOnFamilyPlan" value="true" type="boolean"/>
  <session-mapping key="matchCaseTrue" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.multilineFallBackAttempts" value="1" type="string"/>
</session-mappings>
