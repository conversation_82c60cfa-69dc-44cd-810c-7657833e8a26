<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="JWTToken" value="empty" type="String"/>
  <session-mapping key="sessionID" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="paymentType" value="empty" type="String"/>
  <session-mapping key="paymentMethod" value="empty" type="String"/>
  <session-mapping key="cardNumber" value="empty" type="String"/>
  <session-mapping key="paymentAmount" value="empty" type="String"/>
  <session-mapping key="expirationDate" value="empty" type="String"/>
  <session-mapping key="securityCode" value="empty" type="String"/>
  <session-mapping key="billingZip" value="empty" type="String"/>
  <session-mapping key="dueImmediately" value="empty" type="String"/>
  <session-mapping key="serviceAccountBalance" value="empty" type="String"/>
  <session-mapping key="validatePaymentAmountOnly" value="empty" type="String"/>
  <session-mapping key="autopay" value="empty" type="String"/>
  <session-mapping key="defaultPaymentMethod" value="empty" type="String"/>
  <session-mapping key="paymentOptionId" value="empty" type="String"/>
  <session-mapping key="preferredMethod" value="empty" type="String"/>
  <session-mapping key="tag" value="empty" type="String"/>
  <session-mapping key="manageCardTask" value="empty" type="String"/>
  <session-mapping key="isAutopayEnrolled" value="empty" type="String"/>
  <session-mapping key="displayName" value="empty" type="String"/>
  <session-mapping key="nameOnCard" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="clearCache" value="empty" type="String"/>
  <session-mapping key="multiLineDetails" value="empty" type="String"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.hasAutopayPending" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.useLastPaymentCard" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.tag" value="cancel-autopay" type="string"/>
  <session-mapping key="walletItems.length" value="0" type="integer"/>
  <session-mapping key="GlobalVars.manageCardTask" value="saveMyCard" type="string"/>
  <session-mapping key="GlobalVars.isAutopayEnrolled" value="true" type="boolean"/>
  <session-mapping key="numDaysBtwPayAndCurrentDate" value="7" type="integer"/>
  <session-mapping key="firstTimeInMW1020" value="true" type="boolean"/>
  <session-mapping key="isAutopayEligPlanExists" value="true" type="boolean"/>
  <session-mapping key="manageCardTask" value="removeCard" type="string"/>
  <session-mapping key="isAutopayCard" value="true" type="boolean"/>
  <session-mapping key="tag" value="setup-autopay" type="string"/>
  <session-mapping key="interpretation.dm_root" value="add-new_card" type="string"/>
  <session-mapping key="tryNewCard" value="true" type="boolean"/>
  <session-mapping key="failedChecksum" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.unsupportedCardIssuer" value="discover" type="string"/>
  <session-mapping key="GlobalVars.cardTypeAmex" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.failedChecksum" value="true" type="boolean"/>
  <session-mapping key="unsupportedCardIssuer" value="discover" type="string"/>
  <session-mapping key="mw1300Help" value="true" type="boolean"/>
  <session-mapping key="expDateInPast" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.bankCardDateIsValid" value="1" type="integer"/>
  <session-mapping key="GlobalVars.expDateTryCounter" value="1" type="integer"/>
  <session-mapping key="cardTypeAmex" value="true" type="boolean"/>
  <session-mapping key="ValidateCardOptionsREST.status" value="Success" type="string"/>
  <session-mapping key="ValidateCardOptions.cardType" value="credit" type="string"/>
  <session-mapping key="DeleteSPMItem.status" value="Success" type="string"/>
  <session-mapping key="isSPMItemDBFailed" value="true" type="string"/>
  <session-mapping key="isAutopayEnrolled" value="true" type="string"/>
  <session-mapping key="GlobalVars.replacedAutopayCard" value="true" type="boolean"/>
</session-mappings>
