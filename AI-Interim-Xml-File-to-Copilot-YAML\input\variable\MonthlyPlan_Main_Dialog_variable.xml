<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="noToOnlyPlan_MP1010_SinglePlanYN_DM" value="2" type="string"/>
  <session-mapping key="reentry_MP1010_SinglePlanYN_DM" value="true" type="boolean"/>
  <session-mapping key="MP1030_SelectRatePlanShort_DM.nbestresults" value="undefined" type="string"/>
  <session-mapping key="value.dm_command" value="operator" type="string"/>
  <session-mapping key="MP1040_SelectRatePlanMoreInfo_DM.nbestresults" value="undefined" type="string"/>
  <session-mapping key="reentry_MP1040_SelectRatePlanMoreInfo_DM" value="true" type="boolean"/>
  <session-mapping key="numberOfPlans" value="2" type="integer"/>
  <session-mapping key="isOnFamilyPlan" value="true" type="boolean"/>
  <session-mapping key="lowestPrice" value="highestPrice" type="string"/>
  <session-mapping key="choseInvalidPlan" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.ratePlanSelectionType" value="price" type="string"/>
  <session-mapping key="availablePlansArray.length" value="3" type="integer"/>
  <session-mapping key="GlobalVars.disambigNoMatches" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.noToConfirm_MP1050" value="2" type="integer"/>
  <session-mapping key="onlyOneOrAmbiguous_MP1050" value="true" type="boolean"/>
</session-mappings>
