<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="clearCache" value="empty" type="String"/>
  <session-mapping key="multiLineDetails" value="empty" type="String"/>
  <session-mapping key="aniMatch" value="false" type="boolean"/>
  <session-mapping key="providedFindMDNhint" value="true" type="boolean"/>
  <session-mapping key="requestOperatorUnidentified" value="0" type="integer"/>
  <session-mapping key="identifyUpfrontMDNAttempts" value="0" type="integer"/>
  <session-mapping key="IU1005saidOperator" value="true" type="boolean"/>
  <session-mapping key="haveMDN" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.parentDeviceType" value="INT" type="string"/>
  <session-mapping key="GetAccountDetails.accountRestrictIndicator" value="true" type="string"/>
  <session-mapping key="GetAccountDetails.securityQuestionCode" value="SQ9" type="string"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="suspended" type="string"/>
  <session-mapping key="IU1006saidOperator" value="true" type="boolean"/>
  <session-mapping key="IU1048saidOperator" value="true" type="boolean"/>
  <session-mapping key="IU1051saidOperator" value="true" type="boolean"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="IU1050saidOperator" value="true" type="boolean"/>
  <session-mapping key="IU1055saidOperator" value="true" type="boolean"/>
</session-mappings>
