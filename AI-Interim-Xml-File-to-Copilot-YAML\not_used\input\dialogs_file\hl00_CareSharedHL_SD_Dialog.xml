<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="hl00_CareSharedHL_SD_Dialog">
    	<script className="com.nuance.ps.telefonica.scripts.Initialize"/>
    	 
    	<custom-state id="setCareShareVariables_CS">
    		<action next="hl0001_GetGenesys_KVP_CS"/>
    	</custom-state>
    	
    	<custom-state id="hl0001_GetGenesys_KVP_CS">
    	 	<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
    	 	<script className="com.nuance.ps.telefonica.scripts.HandleDirectEntries"/>
            <action label="default" next="hl0002_lookup_init_CDP_DB_DA"/>
        </custom-state>
       
        <data-access-state id="hl0002_lookup_init_CDP_DB_DA">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
        	<var name="msisdn" path="msisdn" scope="session" isNamelist="true"/>
        	<var name="simnumber" path="simnumber" scope="session" isNamelist="true"/>
        	<var name="brandId" path="urBrand" scope="session" isNamelist="true"/>
        	<var name="iConnId" path="urFirstConnId" scope="session" isNamelist="true"/>
            <data-access id="LookupInitCDP" classname="com.nuance.ps.telefonica.dataaccess.LookupInitCDP">
            <inputs>
				<input-variable name="msisdn" mask="false"/>
				<input-variable name="simnumber" mask="false"/>
				<input-variable name="brandId" mask="false"/>
				<input-variable name="iConnId" mask="false"/>
            </inputs>
            <outputs>
                <output-variable name="pkk" mask="false"/>
                <output-variable name="returnCode" mask="false"/>
            </outputs>
        </data-access>
        
        <action next="hl0010_WhatsNext_DS"/>
        </data-access-state>
        
        <decision-state id="hl0010_WhatsNext_DS">
            <action label="goToAgent" next="hl0020_TransferToAgent_PP"/>
            <action label="hangupRequired" next="hl0030_CallTerminate_PP"/>
			<action label="callsteering" next="hl0040_NLCS_SD_CS"/>
			<action label="simcard" next="hl0060_Sim_SD_CS"/>
			<action label="authenticate" next="hl0090_Authentication_SD_CS"/>
			<action label="k2" next="hl0080_K2_SD_CS"/>
			<action label="dsl" next="hl0050_DSL_SD_CS"/>
			<action label="webinfo" next="hl0045_WebInfo_PP"/>
			<action label="sendsms" next="hl0070_SendSMS_SD"/>
			<action label="usageInfo" next="hl0120_UsageInfo_SD"/>
			<action label="addressTopics" next="hl0140_AdressTopics_SD_CS"/>
			<action label="dataContractUpgrade" next="hl0110_DataContractUpgrade_SD_CS"/>
			<action label="thirdPartyBlock" next="hl0130_ThirdPartyBlocking_SD_CS"/>
			<action label="other" next="hl0100_OtherService_DM"/>
			<action label="vssPreMenu" next="hl0150_VSS_PreMenu_DM"/>
			
			
			<action label="error_CS" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
			</action>

			
			<action label="hungup" next="Hangup_DA">
			    <session-mapping key="hungup" value="true"/>
			    <session-mapping key="returncode" value="HANGUP"/>
			</action>
						
			
			<action label="default" next="Return_DA"/>
        </decision-state>
        
        <custom-state id="CustomerAgentQueue_CS">
            <action next="Return_DA"/>
        </custom-state>
        
        <data-access-state id="Return_DA">
            <session-mapping key="returncode" value="SUCCESS"/>
            <script className="com.nuance.ps.telefonica.scripts.PrepareReturn"/>
            <data-access id="PublishReport" classname="com.nuance.ps.telefonica.dataaccess.PublishReport">
            <inputs>
				<input-variable name="appsessionID" mask="false"/>
				<input-variable name="json" mask="false"/>
            </inputs>
            <outputs>
                <output-variable name="returnCode" mask="false"/>
            </outputs>
        </data-access>
        
        <action next="exit_CS"/>
        </data-access-state>
        
        <data-access-state id="Hangup_DA">
            <session-mapping key="returncode" value="HANGUP"/>
            <script className="com.nuance.ps.telefonica.scripts.PrepareReturn"/>
            <data-access id="PublishReport" classname="com.nuance.ps.telefonica.dataaccess.PublishReport">
            <inputs>
				<input-variable name="appsessionID" mask="false"/>
				<input-variable name="json" mask="false"/>
            </inputs>
            <outputs>
                <output-variable name="returnCode" mask="false"/>
            </outputs>
        </data-access>
        
        <action next="exit_CS"/>
        </data-access-state>
        
        <play-state id="hl0020_TransferToAgent_PP">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
    		<script className="com.nuance.ps.telefonica.scripts.SetRoutingValues"/>
            <audio><if type="java">
            <condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
                <param name="compare" value="true"/><param name="value" scope="session" value="forceCallerToHangup"/></condition><prompt id="silence_3500ms"><prompt-segments>
            <audiofile src="silence_3500ms.wav" text=""/>
        </prompt-segments>
    </prompt><prompt id="hl0020_out_01"><prompt-segments>
            <audiofile src="hl0020_out_01.wav" text="Vielen dank für ihren Anruf"/>
        </prompt-segments>
    </prompt><prompt id="silence_6000ms"><prompt-segments>
            <audiofile src="silence_6000ms.wav" text=""/>
        </prompt-segments>
    </prompt></if><if type="java">
            <condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
                <param name="compare" value="0"/><param name="value" scope="session" value="authenticated"/></condition><condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
                <param name="compare" value="VSS"/><param name="value" scope="session" value="ivrOriginalEntry"/></condition><prompt id="hl0020_out_03"><prompt-segments>
            <audiofile src="hl0020_out_03.wav" text="Wir verbinden Sie jetzt mit der Kundenhotline"/>
        </prompt-segments>
    </prompt></if><if type="java">
            <condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
                <param name="compare" value="kuendigungMobil"/><param name="value" scope="session" value="applicationtag"/></condition><condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
                <param name="compare" value="N"/><param name="value" scope="session" value="winback"/></condition><condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
                <param name="compare" value="SendSMS_DA"/><param name="value" scope="session" value="comingFrom"/><param name="operator" value="NOT"/></condition><prompt id="hl0020_out_02"><prompt-segments>
            <audiofile src="hl0020_out_02.wav" text="Heute kann es leider ein wenig länger dauern Falls Sie Ihren Vertrag kündigen möchten, können Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen"/>
        </prompt-segments>
    </prompt><elseif>
            <condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
                <param name="compare" value="kuendigungInfoMobil"/><param name="value" scope="session" value="applicationtag"/></condition><condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
                <param name="compare" value="N"/><param name="value" scope="session" value="winback"/></condition><condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
                <param name="compare" value="SendSMS_DA"/><param name="value" scope="session" value="comingFrom"/><param name="operator" value="NOT"/></condition><prompt id="hl0020_out_02"><prompt-segments>
            <audiofile src="hl0020_out_02.wav" text="Heute kann es leider ein wenig länger dauern Falls Sie Ihren Vertrag kündigen möchten, können Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen"/>
        </prompt-segments>
    </prompt></elseif></if></audio><action next="CustomerAgentQueue_CS">
            	<if cond="('kuendigungMobil'==applicationtag || 'kuendigungInfoMobil'==applicationtag)                 &amp;&amp; ('MSISDN' == ivrCliType || ( 'MSISDN' != ivrCliType &amp;&amp; 'MSISDN' == ivrIdType &amp;&amp; '1' == authenticated ))                 &amp;&amp; 'Y' != winback                &amp;&amp; CorbaAccessible                &amp;&amp; 'SendSMS' != comingFrom">
					<session-mapping key="goToSMS" value="true"/>
					<action next="hl0070_SendSMS_SD"/>
	            <else>
		        	<action next="CustomerAgentQueue_CS"/>
	            </else>
	       		</if>
	       	</action>
        </play-state>
        
        <play-state id="hl0030_CallTerminate_PP">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <audio><if type="java">
			<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
				<param name="compare" value="VSS"/><param name="value" scope="session" value="ivrOriginalEntry"/></condition><condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
				<param name="compare" value="EASY"/><param name="value" scope="session" value="ivrCustomerSegment"/><param name="operator" value="NOT"/></condition><condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
				<param name="compare" value="UNQ"/><param name="value" scope="session" value="ivrIdResult"/></condition><prompt id="hl0030_out_02"><prompt-segments>
            <audiofile src="hl0030_out_02.wav" text="Wir haben Sie leider nicht identifizieren können Sie können Ihre Kundenbetreuung anrufen oder auf unserem Online Portal einfach und bequem ihre Änderungen durchführen oder mit uns chatten Auf Wiederhören und vielen Dank für Ihren Anruf"/>
        </prompt-segments>
    </prompt><else>
		    <prompt id="hl0030_out_01"><prompt-segments>
            <audiofile src="hl0030_out_01.wav" text="Vielen Dank für Ihren Anruf"/>
        </prompt-segments>
    </prompt></else></if></audio><action next="hl0035_general_call_terminate_CS"/>
        </play-state>
        
        <custom-state id="hl0035_general_call_terminate_CS">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <action next="Hangup_DA"/>
        </custom-state>
       
        <play-state id="hl0045_WebInfo_PP">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <script className="com.nuance.ps.telefonica.scripts.SelfServiceStart">
				<param name="name" value="INFOTAINMENT"/>
			</script>
            <audio><if type="java">
            <condition classname="com.nuance.ps.telefonica.prompts.HasWebInfoPrompt">
                <param name="name" value="dynamic"/></condition><prompt type="custom" expr="dynamic" scope="request">
      			<param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.VXMLDynamicPromptNameRendererImpl"/></prompt></if></audio><action next="hl0010_WhatsNext_DS">
                <if cond="hangupRequired == 'false' &amp;&amp; goToSMS == 'false' &amp;&amp; goToOtherService == 'false' &amp;&amp; (serviceTarget == 'undefined' || serviceTarget == '' || serviceTarget == '#')">
            		<session-mapping key="forceCallerToHangup" value="true"/>
            		<session-mapping key="goToAgent" value="true"/>
                	<session-mapping key="webInfoRequired" value="false"/>
		            <script className="com.nuance.ps.telefonica.scripts.SelfServiceComplete">
						<param name="name" value="INFOTAINMENT"/>
					</script>
            		<action next="hl0010_WhatsNext_DS"/>
            	<else>
            		<session-mapping key="webInfoRequired" value="false"/>
		            <script className="com.nuance.ps.telefonica.scripts.SelfServiceComplete">
						<param name="name" value="INFOTAINMENT"/>
					</script>
            		<action next="hl0010_WhatsNext_DS"/>
            	</else>	
            	</if>
            </action>
        </play-state>

    		
	

        <custom-state id="hl0040_NLCS_SD_CS">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
            <session-mapping key="authenticationRequired" value="true"/>
            <session-mapping key="formID" value="Invoke_NLCS_SD"/>
        	<view name="/InvokeCS.dvxml"/>
    	</custom-state>
        
    	<custom-state id="ReturnFromNclsCs_CS">
        	<session-mapping key="comingFrom" value="CallSteering"/>
        	<session-mapping key="returncode" scope="request" path="returncode"/>
        	<session-mapping key="event_CS" scope="request" path="event_CS"/>

        	<script className="com.nuance.ps.telefonica.scripts.UpdateFromNLCS"/>
        	<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
        	<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
        	<script className="com.nuance.ps.telefonica.scripts.ProcessMIS"/>	
            <script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
        	<script className="com.nuance.ps.telefonica.scripts.ChangeBrandColor"/>
        	<script className="com.nuance.ps.telefonica.scripts.ChangeExitTags"/>
        	<script className="com.nuance.ps.telefonica.scripts.PostProcessNLCS"/>
			<script className="com.nuance.ps.telefonica.scripts.ReportCallSteeringResult"/>
        	
        	<action next="hl0010_WhatsNext_DS"/>

        	<event name="connection.disconnect.hangup" next="Hangup_DA">
        	    <session-mapping key="hungup" value="true"/>
        	    <session-mapping key="returncode" value="HANGUP"/>
        	    <script className="com.nuance.ps.telefonica.scripts.ProcessMIS">
        	        <param name="cause" value="hangup_CS"/>
        	    </script>
        	    <script className="com.nuance.ps.telefonica.scripts.ReportCallSteeringResult"/>
        	</event>
        	<event name="error_CS" next="hl0020_TransferToAgent_PP">
        	    <session-mapping key="goToAgent" value="true"/>
        	    <script className="com.nuance.ps.telefonica.scripts.ProcessMIS">
        	        <param name="cause" value="error_CS"/>
        	    </script>
        	</event>
		</custom-state>
       
        
   
   
        <custom-state id="hl0050_DSL_SD_CS">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
            <session-mapping key="formID" value="Invoke_DSL_SD"/>
        	<view name="/InvokeCS.dvxml"/>
    	</custom-state>
   
    	<decision-state id="ReturnFromDSL_DS">
    	    <session-mapping key="comingFrom" value="DSL"/>
        	<session-mapping key="serviceTarget" value="#"/>

        	<script className="com.nuance.ps.telefonica.scripts.UpdateFromDSL"/>
        	<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
        	<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
	        
	        <action next="hl0010_WhatsNext_DS">
	        	<session-mapping key="serviceTarget" value="undefined"/>
	        </action>
	        
	        <action label="hangup_CS" next="Hangup_DA">
	            <session-mapping key="hungup" value="true"/>
	            <session-mapping key="returncode" value="HANGUP"/>
	        </action>
	        
        	<event name="connection.disconnect.hangup" next="Hangup_DA">
        	    <session-mapping key="hungup" value="true"/>
        	    <session-mapping key="returncode" value="HANGUP"/>
        	</event>
        	<event name="error_CS" next="hl0020_TransferToAgent_PP">
        	    <session-mapping key="goToAgent" value="true"/>
        	</event>
    	</decision-state>
    	
    	
    

        <custom-state id="hl0060_Sim_SD_CS">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            
            <script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
            <session-mapping key="formID" value="Invoke_Sim_SD"/>
        	<view name="/InvokeCS.dvxml"/>
    	</custom-state>
        
    	<decision-state id="ReturnFromSim_DS">
        	<session-mapping key="comingFrom" value="SimCard"/>
        	
        	<script className="com.nuance.ps.telefonica.scripts.UpdateFromSim"/>
        	<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
        	<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
        	
        	
        	<action label="default" next="hl0010_WhatsNext_DS">
        		<if cond="authenticated == '0' &amp;&amp; authenticationRequired == 'true'">
        			<action label="reauthentication" next="hl0002_lookup_init_CDP_DB_DA"/>
        		<else>
        		    <session-mapping key="serviceTarget" value="undefined"/>
        			<action label="routing" next="hl0010_WhatsNext_DS"/>
        		</else>	
        		</if>
        	</action>
        	
        	<action label="hangup_CS" next="Hangup_DA">
        	    <session-mapping key="hungup" value="true"/>
        	    <session-mapping key="returncode" value="HANGUP"/>
			</action>

        	<event name="connection.disconnect.hangup" next="Hangup_DA">
        	    <session-mapping key="hungup" value="true"/>
        	    <session-mapping key="returncode" value="HANGUP"/>
        	</event>
        	<event name="error_CS" next="hl0020_TransferToAgent_PP">
        	    <session-mapping key="goToAgent" value="true"/>
        	</event>
    	</decision-state>
    	

   	
   	
   		<subdialog-state id="hl0070_SendSMS_SD">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <gotodialog next="SendSMS_SD_Dialog"/>
            <action next="hl0010_WhatsNext_DS">
                <session-mapping key="comingFrom" value="SendSMS_DA"/>
            	<if cond="hangupRequired == 'false' &amp;&amp; goToOtherService == 'false' &amp;&amp; (serviceTarget == 'undefined' || serviceTarget == '' || serviceTarget == '#')">
            		<session-mapping key="forceCallerToHangup" value="true"/>
            		<session-mapping key="goToAgent" value="true"/>
                	<session-mapping key="goToSMS" value="false"/>
            		<action next="hl0010_WhatsNext_DS"/>
            	<else>
            		<session-mapping key="goToSMS" value="false"/>
            		<action next="hl0010_WhatsNext_DS"/>
            	</else>	
            	</if>
            </action>
   		</subdialog-state>


   
   
        <custom-state id="hl0080_K2_SD_CS">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
            <session-mapping key="formID" value="Invoke_K2_SD"/>
        	<view name="/InvokeCS.dvxml"/>
    	</custom-state>

    	<custom-state id="ReturnFromK2_CS">
    	    <session-mapping key="comingFrom" value="K2"/>
        	
        	<script className="com.nuance.ps.telefonica.scripts.UpdateFromK2"/>
        	<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
        	<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
	        <action next="hl0010_WhatsNext_DS"/>
	        
        	<event name="connection.disconnect.hangup" next="Hangup_DA">
        	    <session-mapping key="hungup" value="true"/>
        	    <session-mapping key="returncode" value="HANGUP"/>
        	</event>
        	<event name="error_CS" next="hl0020_TransferToAgent_PP">
        	    <session-mapping key="goToAgent" value="true"/>
        	</event>
    	</custom-state>


	
        
        <custom-state id="hl0090_Authentication_SD_CS">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
        	<session-mapping key="formID" value="Invoke_Authentication_SD"/>
        	<view name="/InvokeCS.dvxml"/>
    	</custom-state>
    	
    	<decision-state id="ReturnFromAuthentication_DS">
        	<session-mapping key="comingFrom" value="Authentication"/>
        	<session-mapping key="authenticationRequired" value="false"/>
        	
        	<script className="com.nuance.ps.telefonica.scripts.UpdateFromAuthentication"/>
        	<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
        	<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
        	
        	<action label="default" next="hl0010_WhatsNext_DS"/>
        	
        	<action label="hangup_CS" next="Hangup_DA">
        	    <session-mapping key="hungup" value="true"/>
        	    <session-mapping key="returncode" value="HANGUP"/>
			</action>
        	
        	<event name="connection.disconnect.hangup" next="Hangup_DA">
        	    <session-mapping key="hungup" value="true"/>
        	    <session-mapping key="returncode" value="HANGUP"/>
        	</event>
        	<event name="error_CS" next="hl0020_TransferToAgent_PP">
        	    <session-mapping key="goToAgent" value="true"/>
        	</event>
    	</decision-state>
        
	
		<subdialog-state id="hl0120_UsageInfo_SD">
			<gotodialog next="UsageInfo_SD_Dialog"/>
    	    <session-mapping key="comingFrom" value="UsageInfo"/>
			<action next="hl0010_WhatsNext_DS">
				<session-mapping key="serviceTarget" value="#"/>
			    <if cond="hungup == 'true'">
					<action label="callerHangup" next="Hangup_DA"/>
				<else>
					<action label="gotoRouting" next="hl0010_WhatsNext_DS"/>
				</else>	
				</if>
			</action>
		</subdialog-state>

	
		<custom-state id="hl0140_AdressTopics_SD_CS">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
            <script className="com.nuance.ps.telefonica.scripts.EncodeParams">
            	<param name="encode" value="firstName,lastName,addressId"/>
            </script>

        	<session-mapping key="formID" value="Invoke_AdressTopics_SD"/>
        	<view name="/InvokeCS.dvxml"/>
    	</custom-state>
    	
    	<decision-state id="ReturnFromAdressTopics_DS">
    	    <session-mapping key="comingFrom" value="AdressTopics"/>
        	
        	<script className="com.nuance.ps.telefonica.scripts.UpdateFromAdressTopics"/>
        	<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
        	<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
	        
        	<action label="default" next="hl0010_WhatsNext_DS"/>
        	<action label="hangup_CS" next="Hangup_DA">
        	    <session-mapping key="hungup" value="true"/>
        	    <session-mapping key="returncode" value="HANGUP"/>
			</action>
	        
        	<event name="connection.disconnect.hangup" next="Hangup_DA">
        	    <session-mapping key="hungup" value="true"/>
        	    <session-mapping key="returncode" value="HANGUP"/>
        	</event>
        	<event name="error_CS" next="hl0020_TransferToAgent_PP">
        	    <session-mapping key="goToAgent" value="true"/>
        	</event>
    	</decision-state>
	
		
 	
   
        <custom-state id="hl0110_DataContractUpgrade_SD_CS">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
            <session-mapping key="formID" value="Invoke_DataContractUpgrade_SD"/>
        	<view name="/InvokeCS.dvxml"/>
    	</custom-state>
   
    	<decision-state id="ReturnFromDataContractUpgrade_DS">
    	    <session-mapping key="comingFrom" value="DataContractUpgrade"/>
        	<session-mapping key="serviceTarget" value="#"/>

        	
        	<script className="com.nuance.ps.telefonica.scripts.UpdateFromDataContractUpgrade"/>
        	<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
        	<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
	        
	        <action label="default" next="hl0010_WhatsNext_DS">
	        	<session-mapping key="serviceTarget" value="undefined"/>
	        </action>
			<action label="hangup_CS" next="Hangup_DA">
			    <session-mapping key="hungup" value="true"/>
	            <session-mapping key="returncode" value="HANGUP"/>
	        </action>
	        
        	<event name="connection.disconnect.hangup" next="Hangup_DA">
        	    <session-mapping key="hungup" value="true"/>
        	    <session-mapping key="returncode" value="HANGUP"/>
        	</event>
        	<event name="error_CS" next="hl0020_TransferToAgent_PP">
        	    <session-mapping key="goToAgent" value="true"/>
        	</event>
    	</decision-state>
    	
    
   
        <custom-state id="hl0130_ThirdPartyBlocking_SD_CS">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
            <session-mapping key="formID" value="Invoke_ThirdPartyBlocking_SD"/>
        	<view name="/InvokeCS.dvxml"/>
    	</custom-state>
   
    	<decision-state id="ReturnFromThirdPartyBlocking_DS">
    	    <session-mapping key="comingFrom" value="ThirdPartyBlocking"/>

        	<script className="com.nuance.ps.telefonica.scripts.UpdateFromThirdPartyBlocking"/>
        	<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
        	<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
            <script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
	        
	        <action label="default" next="hl0010_WhatsNext_DS"/>
	        <action label="hangup_CS" next="Hangup_DA">
	            <session-mapping key="hungup" value="true"/>
	            <session-mapping key="returncode" value="HANGUP"/>
	        </action>

        	<event name="connection.disconnect.hangup" next="Hangup_DA">
        	    <session-mapping key="hungup" value="true"/>
        	    <session-mapping key="returncode" value="HANGUP"/>
        	</event>
        	<event name="error_CS" next="hl0020_TransferToAgent_PP">
        	    <session-mapping key="goToAgent" value="true"/>
        	</event>
    	</decision-state>	
		
		
	
    	
        <dm-state id="hl0100_OtherService_DM">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <success>
                <action label="true" next="hl0010_WhatsNext_DS">
                	<session-mapping key="goToNLCS" value="true"/>
                </action>
                <action label="false" next="hl0010_WhatsNext_DS">
                	<session-mapping key="hangupRequired" value="true"/>
                </action>
            </success>
            
			<event name="event.nuance.dialog.ndm.maxnomatches">
			    <action next="hl0010_WhatsNext_DS">
				    <script className="com.nuance.ps.telefonica.scripts.SetReturnValues"/>
			    </action>
			</event>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
			    <action next="hl0010_WhatsNext_DS">
				    <script className="com.nuance.ps.telefonica.scripts.SetReturnValues"/>
			    </action>
			</event>
        	<event name="error_CS" next="hl0020_TransferToAgent_PP">
        	    <session-mapping key="goToAgent" value="true"/>
        	</event>
        <collection_configuration inputmodes="voice">
        <threshold_configuration maxturns="6" maxnomatches="3" maxnoinputs="3"/>
		<grammar_configuration commandgrammar="none" dtmfcommandgrammar="none" supportsadaptivegrammar="false" adaptivewordlist="none" adaptivegrammar="none">					
			<grammars filename="hl0100_OtherService_DM.gram" count="1"/>
		</grammar_configuration>
        <vxml_properties incompletetimeout="1200ms"/>
        <prompt_configuration>
            <initialprompt count="1"><audio><prompt id="hl0100_ini_01"><prompt-segments>
            <audiofile src="hl0100_ini_01.wav" text="Haben Sie noch ein weiteres Anliegen?"/>
        </prompt-segments>
    </prompt></audio></initialprompt><repeatprompts count="1"><audio><prompt id="hl0100_ini_01"><prompt-segments>
            <audiofile src="hl0100_ini_01.wav" text="Haben Sie noch ein weiteres Anliegen?"/>
        </prompt-segments>
    </prompt></audio></repeatprompts><repeatprompts count="2"/><nomatchprefixes count="1"/><nomatchprefixes count="2"/><nomatchprefixes count="3"/><noinputprefixes count="1"/><noinputprefixes count="2"/><noinputprefixes count="3"/><noinputprompts count="1"><audio><prompt id="hl0100_retry1_01"><prompt-segments>
            <audiofile src="hl0100_retry1_01.wav" text="Können wir jetzt noch etwas für Sie tun? Sagen Sie bitte  ja  oder  nein "/>
        </prompt-segments>
    </prompt></audio></noinputprompts><noinputprompts count="2"><audio><prompt id="gl_ni2_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht hören" src="gl_ni2_01.wav"/>
        </prompt-segments>
    </prompt><prompt id="hl0100_retry2_01"><prompt-segments>
            <audiofile src="hl0100_retry2_01.wav" text="Haben Sie noch ein weiteres Anliegen?  Sagen Sie bitte  ja  oder  nein "/>
        </prompt-segments>
    </prompt></audio></noinputprompts><noinputprompts count="3"><audio><prompt id="gl_ni3_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht hören" src="gl_ni3_01.wav"/>
        </prompt-segments>
    </prompt><prompt id="hl0100_retry1_01"><prompt-segments>
            <audiofile src="hl0100_retry1_01.wav" text="Können wir jetzt noch etwas für Sie tun? Sagen Sie bitte  ja  oder  nein "/>
        </prompt-segments>
    </prompt></audio></noinputprompts><nomatchprompts count="1"><audio><prompt id="gl_nm1_01"><prompt-segments>
            <audiofile text="Entschuldigung" src="gl_nm1_01.wav"/>
        </prompt-segments>
    </prompt><prompt id="hl0100_retry1_01"><prompt-segments>
            <audiofile src="hl0100_retry1_01.wav" text="Können wir jetzt noch etwas für Sie tun? Sagen Sie bitte  ja  oder  nein "/>
        </prompt-segments>
    </prompt></audio></nomatchprompts><nomatchprompts count="2"><audio><prompt id="gl_nm2_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht verstehen" src="gl_nm2_01.wav"/>
        </prompt-segments>
    </prompt><prompt id="hl0100_retry2_01"><prompt-segments>
            <audiofile src="hl0100_retry2_01.wav" text="Haben Sie noch ein weiteres Anliegen?  Sagen Sie bitte  ja  oder  nein "/>
        </prompt-segments>
    </prompt></audio></nomatchprompts><nomatchprompts count="3"><audio><prompt id="gl_nm3_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht verstehen" src="gl_nm3_01.wav"/>
        </prompt-segments>
    </prompt><prompt id="hl0100_retry2_01"><prompt-segments>
            <audiofile src="hl0100_retry2_01.wav" text="Haben Sie noch ein weiteres Anliegen?  Sagen Sie bitte  ja  oder  nein "/>
        </prompt-segments>
    </prompt></audio></nomatchprompts><notoconfirmprefixes count="2"/><notoconfirmprefixes count="3"/><notoconfirmprefixes count="1"/><notoconfirmprompts count="1"/></prompt_configuration>
    </collection_configuration>
    <global_configuration confirmationmode="Never">
        <threshold_configuration maxturns="8"/>
        <vxml_properties maxspeechtimeout="12000ms" completetimeout="0ms" timeout="7000ms" bargein="true"/>
        <failureprompt count="1"/><successprompts count="1"/><successprompts count="2"/><successprompts count="3"/><successcorrectedprompt count="1"/></global_configuration>
</dm-state>
        
        
    
    
        <dm-state id="hl0150_VSS_PreMenu_DM">
            <script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
            <success>
                <action label="sim" next="hl0010_WhatsNext_DS">
		            <session-mapping key="comingFrom" value="VSS_PreMenu"/>
				</action>
				
                <action label="usage">
                	<session-mapping key="comingFrom" value="VSS_PreMenu"/>
                	<session-mapping key="transferTo" value="AgentCare"/>
                	<if cond="authenticated == 1 || (ivrCLI == ivrIdValue &amp;&amp; ivrIdType == 'MSISDN')">
                	    <action next="hl0120_UsageInfo_SD"/>
                	<else>
                	    <action next="hl0010_WhatsNext_DS" audio="hl0150_out_01">
                	        <session-mapping key="hangupRequired" value="true"/>
						</action>
                	</else>
                	</if>
                </action>
			</success>
            
			<event name="event.nuance.dialog.ndm.maxnomatches">
			    <action next="hl0010_WhatsNext_DS">
				    <script className="com.nuance.ps.telefonica.scripts.SetReturnValues"/>
				    <session-mapping key="comingFrom" value="VSS_PreMenu"/>
			    </action>
			</event>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
			    <action next="hl0010_WhatsNext_DS">
				    <script className="com.nuance.ps.telefonica.scripts.SetReturnValues"/>
				    <session-mapping key="comingFrom" value="VSS_PreMenu"/>
			    </action>
			</event>
        	<event name="error_CS" next="hl0020_TransferToAgent_PP">
        	    <session-mapping key="goToAgent" value="true"/>
        	    <session-mapping key="comingFrom" value="VSS_PreMenu"/>
        	</event>
        <collection_configuration confirmationmode="Never" highconfidencelevel="0.021" inputmodes="voice">
		<threshold_configuration maxinvalidanswers="2" maxturns="6" maxnoinputs="3" maxnomatches="3" maxrepeats="2" maxhelps="2"/>
		<grammar_configuration commandgrammar="none" dtmfcommandgrammar="none" supportsadaptivegrammar="false" adaptivewordlist="none" adaptivegrammar="none">					
			<grammars filename="hl0150_VSS_PreMenu_DM.gram" count="1"/>
		</grammar_configuration>
		<vxml_properties confidencelevel="0.450" timeout="5000ms" incompletetimeout="1500ms" maxspeechtimeout="10000ms" termtimeout="10ms" interdigittimeout="2000ms" inputmodes="voice"/>
		<prompt_configuration>
            <initialprompt count="1"><audio><prompt id="hl0150_ini_01"><prompt-segments>
            <audiofile src="hl0150_ini_01.wav" text="Worum geht es bei Ihrem Anruf? Um eine Verbrauchsinformation - oder um Ihre SIM-Karte?"/>
        </prompt-segments>
    </prompt></audio></initialprompt><repeatprompts count="1"><audio><prompt id="hl0150_ini_01"><prompt-segments>
            <audiofile src="hl0150_ini_01.wav" text="Worum geht es bei Ihrem Anruf? Um eine Verbrauchsinformation - oder um Ihre SIM-Karte?"/>
        </prompt-segments>
    </prompt></audio></repeatprompts><repeatprompts count="2"/><nomatchprefixes count="1"/><nomatchprefixes count="2"/><nomatchprefixes count="3"/><noinputprefixes count="1"/><noinputprefixes count="2"/><noinputprefixes count="3"/><noinputprompts count="1"><audio><prompt id="hl0150_retry1_01"><prompt-segments>
            <audiofile src="hl0150_retry1_01.wav" text="Für Fragen zu Ihrem Verbrauch sagen Sie bitte - 'Verbrauchsinfo' und bei Anliegen rund um Ihre SIM-Karte sagen Sie - 'SIM'"/>
        </prompt-segments>
    </prompt></audio></noinputprompts><noinputprompts count="2"><audio><prompt id="gl_ni2_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht hören" src="gl_ni2_01.wav"/>
        </prompt-segments>
    </prompt><prompt id="hl0150_retry2_01"><prompt-segments>
            <audiofile src="hl0150_retry2_01.wav" text="Bitte wählen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM"/>
        </prompt-segments>
    </prompt></audio></noinputprompts><noinputprompts count="3"><audio><prompt id="gl_ni3_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht hören" src="gl_ni3_01.wav"/>
        </prompt-segments>
    </prompt><prompt id="hl0150_retry2_01"><prompt-segments>
            <audiofile src="hl0150_retry2_01.wav" text="Bitte wählen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM"/>
        </prompt-segments>
    </prompt></audio></noinputprompts><nomatchprompts count="1"><audio><prompt id="gl_nm1_01"><prompt-segments>
            <audiofile text="Entschuldigung" src="gl_nm1_01.wav"/>
        </prompt-segments>
    </prompt><prompt id="hl0150_retry1_01"><prompt-segments>
            <audiofile src="hl0150_retry1_01.wav" text="Für Fragen zu Ihrem Verbrauch sagen Sie bitte - 'Verbrauchsinfo' und bei Anliegen rund um Ihre SIM-Karte sagen Sie - 'SIM'"/>
        </prompt-segments>
    </prompt></audio></nomatchprompts><nomatchprompts count="2"><audio><prompt id="gl_nm2_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht verstehen" src="gl_nm2_01.wav"/>
        </prompt-segments>
    </prompt><prompt id="hl0150_retry2_01"><prompt-segments>
            <audiofile src="hl0150_retry2_01.wav" text="Bitte wählen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM"/>
        </prompt-segments>
    </prompt></audio></nomatchprompts><nomatchprompts count="3"><audio><prompt id="gl_nm3_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht verstehen" src="gl_nm3_01.wav"/>
        </prompt-segments>
    </prompt><prompt id="hl0150_retry2_01"><prompt-segments>
            <audiofile src="hl0150_retry2_01.wav" text="Bitte wählen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM"/>
        </prompt-segments>
    </prompt></audio></nomatchprompts><notoconfirmprefixes count="2"/><notoconfirmprefixes count="3"/><notoconfirmprefixes count="1"/><notoconfirmprompts count="1"/></prompt_configuration>
	</collection_configuration>
    <global_configuration confirmationmode="Never">
        <failureprompt count="1"/><successprompts count="1"/><successprompts count="2"/><successprompts count="3"/><successcorrectedprompt count="1"/></global_configuration>
</dm-state>
        
    </dialog>

