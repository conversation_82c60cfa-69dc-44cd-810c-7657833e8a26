<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="JWTToken" value="empty" type="String"/>
  <session-mapping key="currentRatePlan" value="empty" type="String"/>
  <session-mapping key="newRatePlan" value="empty" type="String"/>
  <session-mapping key="autoCorrectApproved" value="empty" type="String"/>
  <session-mapping key="futureDate" value="empty" type="String"/>
  <session-mapping key="isUpgrade" value="empty" type="String"/>
  <session-mapping key="removeFeatures" value="empty" type="String"/>
  <session-mapping key="addFuturePromo" value="empty" type="String"/>
  <session-mapping key="addRegularPromo" value="empty" type="String"/>
  <session-mapping key="removeFuturePromoSoc" value="empty" type="String"/>
  <session-mapping key="removeRegularPromoSoc" value="empty" type="String"/>
  <session-mapping key="orderId" value="empty" type="String"/>
  <session-mapping key="GlobalVars.newRatePlan" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.addFeatures" value="undefined" type="string"/>
  <session-mapping key="SubmitChangeOffer.status" value="Success" type="string"/>
  <session-mapping key="SubmitChangeOffer.triedToUpdateSubscriberDetails" value="true" type="boolean"/>
  <session-mapping key="newRatePlan" value="undefined" type="string"/>
  <session-mapping key="GetBCSParameters.care_enable_rateplan_special_error_handling" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.care_rateplan_special_error_planlist" value="null" type="string"/>
  <session-mapping key="GlobalVars.ratePlanChangeDBErrorCounter" value="0" type="integer"/>
  <session-mapping key="addFeatures" value="undefined" type="string"/>
  <session-mapping key="isAutoCorrectRequired" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.autoCorrectApproved" value="false" type="string"/>
  <session-mapping key="GlobalVars.care_enable_autocorrect_offer" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.newPlanPrice" value="GlobalVars.oldPlanPrice" type="string"/>
  <session-mapping key="broadcastMessage" value="Custom" type="string"/>
  <session-mapping key="broadcastMessageAction" value="null" type="string"/>
  <session-mapping key="SubmitChangeOffer.dueImmediatlyAmount" value="0" type="string"/>
  <session-mapping key="SubmitChangeOffer.priorDueImmediatelyAmt" value="0" type="string"/>
  <session-mapping key="SubmitChangeOffer.amountDue" value="GlobalVars.GetAccountDetails.balance" type="string"/>
  <session-mapping key="SubmitChangeOffer.isAutoCorrectRequired" value="false" type="string"/>
  <session-mapping key="interpretation.dm_root" value="approve" type="string"/>
</session-mappings>
