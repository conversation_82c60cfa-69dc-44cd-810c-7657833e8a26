<states-library xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../xsd/states.xsd"><dialog id="ApplicationRoot"><session-mapping key="errorCount" value="0" type="String" /><session-mapping key="dataaccessErrorCount" value="0" type="String" /><session-mapping key="badFetchErrorCount" value="0" type="String" /><session-mapping key="id1" value="" type="String" /><session-mapping key="id2" value="" type="String" /><session-mapping key="id3" value="" type="String" /><session-mapping key="language" value="en-US" type="String" /><session-mapping key="library" value="default" type="String" /><session-mapping key="version" value="1.0" type="String" /><session-mapping key="mdnChangeVars" value="new Object()" type="String" /><session-mapping key="paymentVars" value="new Object()" type="String" /><session-mapping key="esnChangeVars" value="new Object()" type="String" /><session-mapping key="ctiAttachVars" value="new Object()" type="String" /><session-mapping key="ctiTransferVars" value="new Object()" type="String" /><session-mapping key="newPlanVars" value="new Object()" type="String" /><session-mapping key="addFeatureVars" value="new Object()" type="String" /><session-mapping key="GlobalVars" value="new Object()" type="String" /><session-mapping key="NumberPortInVars" value="new Object()" type="String" /><session-mapping key="GetSecurityCodeVars" value="new Object()" type="String" /><session-mapping key="activationVars" value="new Object()" type="String" /><session-mapping key="ActivationTable" value="resetActivationsTable()" type="String" /><session-mapping key="PaymentTable" value="resetActivationsTable()" type="String" /><session-mapping key="TransferTag" value="Customer_Support_English" type="String" /><session-mapping key="disambiguationForm" value="" type="String" /><session-mapping key="nluOperatorCount" value="0" type="String" /><event name="connection.disconnect.hangup"><gotodialog next="Exit" /></event><event name="connection.disconnect.transfer"><gotodialog next="Exit" /></event><event name="event.nuance.dialog.ndm.internalerror event.nuance.dialog.ndm.no_matching_command_reco_option event.nuance.dialog.ndm.no_matching_success_reco_option"><gotodialog next="CallTransfer_Main" /></event><event name="event.nuance.dialog.ndm.maxnoinputs"><gotodialog next="Exit" /></event><event name="event.nuance.dialog.ndm.maxretries"><gotodialog next="Exit" /></event><event name="event.nuance.dialog.ndm.maxnomatches"><gotodialog next="Exit" /></event><event name="event.nuance.dialog.ndm.maxnotoconfirms"><gotodialog next="Exit" /></event><event name="event.nuance.dialog.ndm.maxturns"><gotodialog next="Exit" /></event><event name="event.nuance.dialog.ndm.maxrepeats"><gotodialog next="Exit" /></event><event name="event.nuance.dialog.ndm.maxhelps"><gotodialog next="Exit" /></event><event name="event.nuance.dialog"><gotodialog next="Exit" /></event><event name="error.nuance.dataaccess.system"><session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1" /><gotodialog next="CallTransfer_Main" /></event><event name="error.nuance.dataaccess.system"><session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1" /><gotodialog next="Exit" /></event><event name="error.nuance.dataaccess.system"><exit /></event><event name="event.nuance.dataaccess.business"><session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1" /><gotodialog next="CallTransfer_Main" /></event><event name="event.nuance.dataaccess.business"><session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1" /><gotodialog next="Exit" /></event><event name="event.nuance.dataaccess.business"><exit /></event><event name="error.badfetch"><session-mapping key="badFetchErrorCount" expr="badFetchErrorCount + 1" /><if cond="_message != undefined &amp;&amp; _message != ''"><else /></if><gotodialog next="CallTransfer_Main" /></event><event name="error.badfetch"><session-mapping key="badFetchErrorCount" expr="badFetchErrorCount + 1" /><if cond="_message != undefined &amp;&amp; _message != ''"><else /></if><gotodialog next="Exit" /></event><event name="error.badfetch"><exit /></event><event name="error.internal"><session-mapping key="errorCount" expr="errorCount + 1" /><if cond="_message != undefined &amp;&amp; _message != ''"><else /></if><gotodialog next="CallTransfer_Main" /></event><event name="error.internal"><session-mapping key="errorCount" expr="errorCount + 1" /><if cond="_message != undefined &amp;&amp; _message != ''"><else /></if><gotodialog next="Exit" /></event><event name="error.internal"><exit /></event><event cond="errorCount == 0"><session-mapping key="errorCount" expr="errorCount + 1" /><if cond="_message != undefined &amp;&amp; _message != ''"><else /></if><gotodialog next="CallTransfer_Main" /></event><event cond="errorCount == 1"><session-mapping key="errorCount" expr="errorCount + 1" /><if cond="_message != undefined &amp;&amp; _message != ''"><else /></if><gotodialog next="Exit" /></event><event cond="errorCount &gt;= 1"><exit /></event></dialog><dialog id="CallTransfer_Main"><decision-state id="XR0005_CheckEntryMethod_DS"><session-mapping key="GlobalVars.isSuspended" expr="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" /><if cond="TransferTag == 'New_Customer_Sales_En' || TransferTag == 'New_Customer_Sales_Es'"><action next="XR0025_PlayTransferPrompt_PP" /></if><if cond="GlobalVars.acctLocked == true"><action next="XR0023_GoToBroadcastMessages_SD" /></if><if cond="GlobalVars.tag == 'request-representative_sales'"><session-mapping key="TransferTag" expr="'sales'" /></if><if cond="GlobalVars.transferReason == 'dbfail'"><session-mapping key="GlobalVars.cti_TransferReason" expr="getctiTransferReason(GlobalVars.GetCTIParameters.ctiTransferReason, 'xferred_backend_failure')" /></if><if cond="GlobalVars.tag == 'purchase-phone' || GlobalVars.tag == 'purchase-accessory'"><if cond="language == 'en-US'"><session-mapping key="TransferTag" expr="'purchasing_support_english'" /><else><session-mapping key="TransferTag" expr="'purchasing_support_spanish'" /></else></if><elseif cond="GlobalVars.tag == 'T-MobileMoney'"><session-mapping key="TransferTag" expr="'tmobile_money_all'" /></elseif><elseif cond="GlobalVars.tag == 'home-internet'"><session-mapping key="TransferTag" expr="'home_internet_all'" /></elseif></if><if cond="GlobalVars.fromMyMetroCustomerSupport == true"><action next="XR0025_PlayTransferPrompt_PP" /><elseif cond="GlobalVars.enteredFrom == 'RCC'"><action next="XR0030_GetTransferDestination_DB" /></elseif><elseif cond="callerSaidOperator == true"><session-mapping key="callerSaidOperator" value="false" type="Boolean" /><gotodialog next="CallTransfer_OperatorRequestHandling" /></elseif><elseif cond="GlobalVars.callType == 'auto_pay'"><action next="XR0006_MetricsAutopayXfer_DS" /></elseif><elseif cond="GlobalVars.callType == 'ThirdPartyFeaturesXfer' || GlobalVars.callType == 'transfer' || GlobalVars.troubleshootingTransfer == true || GlobalVars.dataCollectionTransfer == true"><gotodialog next="CallTransfer_Main" /></elseif><elseif cond="GlobalVars.saidSomethingElse == true"><gotodialog next="CallTransfer_OperatorRequestHandling" /></elseif><elseif cond="(isMDE == true || GlobalVars.otherGhostCaller == true) &amp;&amp; !(GlobalVars.tag == 'vague-agent_destination')"><session-mapping key="isMDE" value="false" type="Boolean" /><gotodialog next="CallTransfer_MDEHandling" /></elseif><elseif cond="GlobalVars.skipBroadcastMessage == true"><action next="XR0025_PlayTransferPrompt_PP" /></elseif><elseif cond="GlobalVars.playTransferMessage == false &amp;&amp; GlobalVars.twoFactorAuthOutcome == 'not_attempted'"><action next="XR0030_GetTransferDestination_DB" /></elseif><else><action next="XR0023_GoToBroadcastMessages_SD" /></else></if></decision-state><decision-state id="XR0006_MetricsAutopayXfer_DS"><action next="XR0023_GoToBroadcastMessages_SD" /></decision-state><subdialog-state id="XR0023_GoToBroadcastMessages_SD"><session-mapping key="GlobalVars.broadcastMessageKey" expr="'XR'" /><gotodialog next="BroadcastMessages" /><action next="XR0023_GoToBroadcastMessages_SD_return" /></subdialog-state><custom-state id="XR0023_GoToBroadcastMessages_SD_return"><action next="XR0025_PlayTransferPrompt_PP" /></custom-state><play-state id="XR0025_PlayTransferPrompt_PP"><session-mapping key="transferReason" value="GlobalVars.transferReason" type="String" /><session-mapping key="playNoPINTransferPrompt" value="GlobalVars.playNoPINTransferPrompt" type="String" /><session-mapping key="playTransferMessage" value="GlobalVars.playTransferMessage" type="String" /><session-mapping key="paymentsEntryPoint" value="GlobalVars.paymentsEntryPoint" type="String" /><session-mapping key="securityQuestionAttempts" value="GlobalVars.securityQuestionAttempts" type="String" /><session-mapping key="callType" value="GlobalVars.callType" type="String" /><session-mapping key="tag" value="GlobalVars.tag" type="String" /><audio><if cond="transferReason == 'dbfail'">
	   		<prompt id="XR0025_out_01" /><elseif cond="GlobalVars.acctLocked == true &amp;&amp; GlobalVars.playedAcctLocked == false">
	   		<prompt id="XR0025_out_07" /></elseif><elseif cond="playNoPINTransferPrompt == true &amp;&amp; securityQuestionAttempts == 3">
	   		<prompt id="XR0025_out_04" /></elseif><elseif cond="playNoPINTransferPrompt == true &amp;&amp; securityQuestionAttempts != 3">
	   		<prompt id="XR0025_out_02" /></elseif><elseif cond="paymentsEntryPoint != undefined">
	    	<prompt id="XR0025_out_03" /></elseif></if><prompt id="silence_750ms" cond="playTransferMessage == true" /><prompt id="XR0025_out_08" cond="playTransferMessage == true &amp;&amp; tag == 'purchase-phone_nc'" /><prompt id="XR0025_ini_02" cond="playTransferMessage == true &amp;&amp; tag != 'purchase-phone_nc'" /><prompt id="XR0025_out_05" cond="callType == 'troubleshoot' || callType == 'help_me_out' || tag == 'transfer-disambig_troubleshooting'" /><prompt id="XR0025_out_06" /></audio><session-mapping key="GlobalVars.FromXR1510" value="false" type="Boolean" /><action next="XR0030_GetTransferDestination_DB" /></play-state><custom-state id="PromptBeforeTransfer"><field name="ForcePromptQueueBeforeExiting"><prompt timeout="100ms" /><grammar version="1.0" xml:lang="en-US" tag-format="semantics/1.0" root="empty" xml:base="CallTransfer_Main.dvxml"><rule id="empty" scope="public"><one-of><item>dummy item</item></one-of></rule></grammar><noinput><action next="XR0030_GetTransferDestination_DB" /></noinput><nomatch><action next="XR0030_GetTransferDestination_DB" /></nomatch><action next="XR0030_GetTransferDestination_DB" /></field><event name="connection.disconnect.hangup"><gotodialog next="Exit" /></event><catch><action next="XR0030_GetTransferDestination_DB" /></catch></custom-state><decision-state id="XR0029_CheckCTIEnabled_DS"><if cond="(GlobalVars.enteredFrom != 'DSG' &amp;&amp; GlobalVars.enteredFrom != 'NRH' &amp;&amp; GlobalVars.enteredFrom != 'RCC') &amp;&amp; (GlobalVars.GetBCSParameters.care_enable_cti_transfer == true || GlobalVars.GetBCSParameters.care_enable_cti_transfer == 'true') &amp;&amp; (GlobalVars.enteredFrom != 'PWR')"><action next="XR0031_AttachCTIData_DB" /><else><action next="XR0035_TransferToQueue_CT" /></else></if></decision-state><data-access-state id="XR0030_GetTransferDestination_DB"><session-mapping key="transferTag" value="TransferTag" type="String" /><data-access id="GetTransferDestination" classname="com.nuance.metro.dataaccess.GetTransferDestination">
		<inputs>
	 		<input-variable name="transferTag" />
		</inputs>
		<outputs>
	 		<output-variable name="status" />
	 		<output-variable name="transferNumber" />
		</outputs>
   	</data-access>
	<if cond="GetTransferDestination.status == 'Success'"><session-mapping key="GlobalVars.transferNumber" expr="GetTransferDestination.transferNumber" /><else><if cond="language == 'en-US'"><session-mapping key="GlobalVars.transferNumber" expr="'tel:8552246938#'" /><else><session-mapping key="GlobalVars.transferNumber" expr="'tel:8552246920#'" /></else></if></else></if><if cond="TransferTag == 'Payment_Care_English' || TransferTag== 'Payment_Care_Spanish'" /><action next="XR0029_CheckCTIEnabled_DS" /></data-access-state><data-access-state id="XR0031_AttachCTIData_DB"><session-mapping key="cti_MDN" value="GlobalVars.cti_MDN" type="String" /><session-mapping key="cti_PIN" value="GlobalVars.cti_PIN" type="String" /><session-mapping key="cti_AuthStatus" value="GlobalVars.cti_AuthStatus" type="String" /><session-mapping key="cti_Intent" value="GlobalVars.cti_Intent" type="String" /><session-mapping key="cti_TransferReason" value="GlobalVars.cti_TransferReason" type="String" /><data-access id="AttachCTIData" classname="com.nuance.metro.dataaccess.AttachCTIData">
		<inputs>
	 		<input-variable name="cti_MDN" />
	 		<input-variable name="cti_PIN" mask="true" />
	 		<input-variable name="cti_AuthStatus" />
	 		<input-variable name="cti_Intent" />
	 		<input-variable name="cti_TransferReason" />
	 		<input-variable name="id1" />
	 		<input-variable name="id2" />
	 		<input-variable name="id3" />
		</inputs>
		<outputs>
	 		<output-variable name="status" />
		</outputs>
   	</data-access>   
   	<if cond="AttachCTIData.status == 'Success'"><session-mapping key="ctiAttachVars.eventTypeGMT" expr="getEventTime()" /><session-mapping key="ctiAttachVars.status" expr="'success'" /><session-mapping key="ctiAttachVars.eventType" expr="'attach_cti_data'" /><action next="XR0032_CTITransfer_DB" /><else><session-mapping key="ctiAttachVars.eventTypeGMT" expr="getEventTime()" /><session-mapping key="ctiAttachVars.status" expr="'failure'" /><session-mapping key="ctiAttachVars.eventType" expr="'attach_cti_data'" /><action next="XR0035_TransferToQueue_CT" /></else></if></data-access-state><data-access-state id="XR0032_CTITransfer_DB"><session-mapping key="transferNumber" value="GlobalVars.transferNumber" type="String" /><data-access id="CTITransfer" classname="com.nuance.metro.dataaccess.CTITransfer">
		<inputs>
	 		<input-variable name="transferNumber" />
	 		<input-variable name="id1" />
	 		<input-variable name="id2" />
	 		<input-variable name="id3" />
		</inputs>
		<outputs>
	 		<output-variable name="status" />
		</outputs>
   	</data-access>
   	<if cond="CTITransfer.status == 'Success'"><session-mapping key="ctiTransferVars.eventTypeGMT" expr="getEventTime()" /><session-mapping key="ctiTransferVars.status" expr="'success'" /><session-mapping key="ctiTransferVars.eventType" expr="'cti_transfer'" /><gotodialog next="Exit" /><else><session-mapping key="ctiTransferVars.eventTypeGMT" expr="getEventTime()" /><session-mapping key="ctiTransferVars.status" expr="'failure'" /><session-mapping key="ctiTransferVars.eventType" expr="'cti_transfer'" /><action next="XR0035_TransferToQueue_CT" /></else></if></data-access-state><custom-state id="XR0035_TransferToQueue_CT"><transfer name="transfer" destexpr="GlobalVars.transferNumber" type="blind"><gotodialog next="Exit" /></transfer><gotodialog next="Exit" /><event name="error"><gotodialog next="Exit" /></event></custom-state><custom-state id="XR0036_TransferToQueueCTI_CT"><transfer name="transfer" destexpr="GlobalVars.transferNumber" type="blind"><gotodialog next="Exit" /></transfer><gotodialog next="Exit" /><event name="error"><gotodialog next="Exit" /></event></custom-state><subdialog-state id="XR0040_GoToGoodbye_SD"><gotodialog next="Goodbye_Main" /><action next="XR0040_GoToGoodbye_SD_return" /></subdialog-state><custom-state id="XR0040_GoToGoodbye_SD_return"><gotodialog next="Exit" /></custom-state></dialog><dialog id="CollectData"><decision-state id="DC1001_CheckContext_DS"><session-mapping key="GlobalVars.dataCollectionTransfer" value="true" type="Boolean" /><action next="DC0030_AskIntent_DM" /></decision-state><dm-state id="DC0030_AskIntent_DM" type="CUST"><success><action label="dtmf_entry"><session-mapping key="GlobalVars.playTransferMessage " value="false" type="Boolean" /><audio><prompt id="DC0030_nm1_01" /></audio><action next="DC0050_CallTransfer_SD" /></action><action label="operator"><session-mapping key="agentCounter" expr="agentCounter + 1" /><if cond="agentCounter == 1"><session-mapping key="IntentMSG" expr="'operator_request'" /><action next="DC0030_AskIntent_DM" /><else><session-mapping key="GlobalVars.playTransferMessage " value="false" type="Boolean" /><if cond="agentCounter &lt; 1"><audio><prompt id="DC0030_out_01" /></audio><action next="DC0050_CallTransfer_SD" /><else><audio><prompt id="DC0030_out_02" /></audio><action next="DC0050_CallTransfer_SD" /></else></if></else></if></action></success><event name="event.nuance.dialog.ndm.maxnoinputs"><session-mapping key="GlobalVars.playTransferMessage " value="false" type="Boolean" /><audio><prompt id="DC0030_ni1_01" /></audio><action next="DC0050_CallTransfer_SD" /></event><event name="event.nuance.dialog.ndm.maxnomatches"><session-mapping key="GlobalVars.playTransferMessage " value="false" type="Boolean" /><if cond="agentCounter &lt; 1"><audio><prompt id="DC0030_out_01" /></audio><action next="DC0050_CallTransfer_SD" /><else><audio><prompt id="DC0030_out_02" /></audio><action next="DC0050_CallTransfer_SD" /></else></if></event><collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0" maxturns="2">
        </threshold_configuration>
        <prompt_configuration>
            <initialprompt count="1"><audio><if type="java">
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                        <param name="numToMatch" value="1" /><param name="startNum" value="1" /><param name="endNum" value="4" /></condition><prompt id="DC0030_ini_01_few_calling_about" cond="IntentMSG == 'Default'" /><prompt id="DC0030_re_07_right_rep" cond="IntentMSG == 'operator_request'" /><elseif>
            <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                        <param name="numToMatch" value="2" /><param name="startNum" value="1" /><param name="endNum" value="4" /></condition><prompt id="DC0030_ini_02_calling_about" cond="IntentMSG == 'Default'" /><prompt id="DC0030_re_08_right_here" cond="IntentMSG == 'operator_request'" /></elseif><elseif>
            <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                        <param name="numToMatch" value="3" /><param name="startNum" value="1" /><param name="endNum" value="4" /></condition><prompt id="DC0030_ini_04_few_help" cond="IntentMSG == 'Default'" /><prompt id="DC0030_re_09_help_better" cond="IntentMSG == 'operator_request'" /></elseif><else>
            <prompt id="DC0030_ini_05_help" cond="IntentMSG == 'Default'" /><prompt id="DC0030_re_10_understand" cond="IntentMSG == 'operator_request'" /></else></if></audio></initialprompt><reinvokeprompt text="" id="DC0030_AskIntent_DM_reinvoke" filename="" count="1" bargein="true" />
        </prompt_configuration>
    <grammar_configuration>
      <grammars filename="DC0030_AskIntent_DM.grxml" count="1" />
      <dtmfgrammars filename="DC0030_AskIntent_DM_dtmf.grxml" count="1" />
    </grammar_configuration>

    <vxml_properties confidencelevel="0.450" incompletetimeout="2500ms" completetimeout="0ms" maxspeechtimeout="15000ms" timeout="7000ms" />

  </collection_configuration>
    <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="3" maxnoinputs="3">
        </threshold_configuration>
        <failureprompt count="1" /><successprompts count="1" /><successprompts count="2" /><successprompts count="3" /><successcorrectedprompt count="1" /><vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms" />

  </global_configuration>
    <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
        <grammars filename="confirmation.grxml" count="1" />
    </grammar_configuration>
    <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms" />

  </confirmation_configuration>
</dm-state><subdialog-state id="DC0050_CallTransfer_SD"><gotodialog next="CallTransfer_Main" /></subdialog-state></dialog><dialog id="Exit"><decision-state id="CheckIfBillingData_DS"><if cond="GlobalVars.callType == 'switch_phone' &amp;&amp; ActivationTable.ACTIVATION_STATUS == '130'"><session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'132'" /></if><if cond="ActivationTable.ACTIVATION_STATUS !='' "><action next="BillingData_DB" /><elseif cond="PaymentTable.ACTIVATION_STATUS !='' "><action next="BillingData2_DB" /></elseif><elseif cond="(haveMDN == true) &amp;&amp; (GlobalVars.IOMGetPromotion != null)"><action next="EndCall_DB" /></elseif><else><action next="EndApplication" /></else></if></decision-state><data-access-state id="BillingData_DB"><session-mapping key="sessionId" value="ActivationTable.SESSION_ID" type="String" /><session-mapping key="ACCOUNT_NUM" value="ActivationTable.ACCOUNT_NUM" type="String" /><session-mapping key="ACTIVATION_TYPE" value="ActivationTable.ACTIVATION_TYPE" type="String" /><session-mapping key="ACTIVATION_MODE" value="ActivationTable.ACTIVATION_MODE" type="String" /><session-mapping key="ACTIVATION_STARTED" value="ActivationTable.ACTIVATION_STARTED" type="String" /><session-mapping key="ACTIVATION_STATUS" value="ActivationTable.ACTIVATION_STATUS" type="String" /><session-mapping key="ESN" value="ActivationTable.ESN" type="String" /><session-mapping key="ANI" value="ActivationTable.ANI" type="String" /><session-mapping key="DID" value="ActivationTable.DID" type="String" /><session-mapping key="MDN" value="ActivationTable.MDN" type="String" /><session-mapping key="RATE_PLAN" value="GlobalVars.selectedPlan" type="String" /><session-mapping key="TRF_SWITCH_ON" value="ActivationTable.TRF_SWITCH_ON" type="String" /><session-mapping key="CITY" value="ActivationTable.CITY" type="String" /><session-mapping key="STATE" value="ActivationTable.STATE" type="String" /><session-mapping key="ZIP" value="ActivationTable.ZIP" type="String" /><session-mapping key="BILLABLE_DATE" value="ActivationTable.BILLABLE_DATE" type="String" /><session-mapping key="OLD_PHONE_TYPE" value="ActivationTable.OLD_PHONE_TYPE" type="String" /><session-mapping key="OLD_MOBILE_NUM" value="GlobalVars.oldNum" type="String" /><session-mapping key="ERROR_TEXT" value="ActivationTable.ERROR_TEXT" type="String" /><session-mapping key="NETWORK_TYPE" value="GlobalVars.networkType" type="String" /><session-mapping key="DEVICE_ID" value="GlobalVars.deviceID" type="String" /><data-access id="InsertActivationBillingInfo" classname="com.nuance.metro.dataaccess.InsertActivationBillingInfo">
		<inputs>
			<input-variable name="sessionId" />
			<input-variable name="ACCOUNT_NUM" />
			<input-variable name="ACTIVATION_TYPE" />
			<input-variable name="ACTIVATION_MODE" />
			<input-variable name="ACTIVATION_STARTED" />
			<input-variable name="ACTIVATION_STATUS" />
			<input-variable name="ESN" />
			<input-variable name="ANI" />
			<input-variable name="DID" />
			<input-variable name="MDN" />
			<input-variable name="RATE_PLAN" />
			<input-variable name="TRF_SWITCH_ON" />
			<input-variable name="CITY" />
			<input-variable name="STATE" />
			<input-variable name="ZIP" />
			<input-variable name="BILLABLE_DATE" />
			<input-variable name="OLD_PHONE_TYPE" />
			<input-variable name="OLD_MOBILE_NUM" />
			<input-variable name="ERROR_TEXT" />
			<input-variable name="NETWORK_TYPE" />
			<input-variable name="DEVICE_ID" />
			<input-variable name="ACTIVATION_ENDED_GMT" /> 
		</inputs>
		<outputs>
			<output-variable name="status" />
			<output-variable name="message" />
		</outputs>
	</data-access>
    <if cond="PaymentTable.ACTIVATION_STATUS !=''"><action next="BillingData2_DB" /></if><if cond="(GlobalVars.IOMGetPromotion != null) || (GlobalVars.GetAccountDetails != null)"><action next="EndCall_DB" /><else><action next="EndApplication" /></else></if><event><if cond="PaymentTable.ACTIVATION_STATUS !=''"><action next="BillingData2_DB" /></if><if cond="(haveMDN == true) &amp;&amp; (GlobalVars.IOMGetPromotion != null)"><action next="EndCall_DB" /><else><action next="EndApplication" /></else></if></event></data-access-state><data-access-state id="BillingData2_DB"><session-mapping key="sessionId" value="PaymentTable.SESSION_ID" type="String" /><session-mapping key="ACCOUNT_NUM" value="ActivationTable.ACCOUNT_NUM" type="String" /><session-mapping key="ACTIVATION_TYPE" value="PaymentTable.ACTIVATION_TYPE" type="String" /><session-mapping key="ACTIVATION_MODE" value="PaymentTable.ACTIVATION_MODE" type="String" /><session-mapping key="ACTIVATION_STATUS" value="PaymentTable.ACTIVATION_STATUS" type="String" /><session-mapping key="CARD_TYPE" value="PaymentTable.CARD_TYPE" type="String" /><session-mapping key="TRANSACTION_ID" value="PaymentTable.TRANSACTION_ID" type="String" /><session-mapping key="TRANSACTION_AMOUNT" value="PaymentTable.TRANSACTION_AMOUNT" type="String" /><session-mapping key="PAYMENT_STARTED_GMT" value="PaymentTable.ACTIVATION_STARTED" type="String" /><session-mapping key="PAYMENT_COMPLETED_GMT" value="PaymentTable.ACTIVATION_ENDED_GMT" type="String" /><session-mapping key="ESN" value="ActivationTable.ESN" type="String" /><session-mapping key="ANI" value="ActivationTable.ANI" type="String" /><session-mapping key="DID" value="ActivationTable.DID" type="String" /><session-mapping key="MDN" value="ActivationTable.MDN" type="String" /><session-mapping key="CITY" value="ActivationTable.CITY" type="String" /><session-mapping key="STATE" value="ActivationTable.STATE" type="String" /><session-mapping key="ZIP" value="ActivationTable.ZIP" type="String" /><session-mapping key="ERROR_TEXT" value="PaymentTable.ERROR_TEXT" type="String" /><data-access id="InsertIVRPaymentsInfo" classname="com.nuance.metro.dataaccess.InsertIVRPaymentsInfo">
		<inputs>
			<input-variable name="sessionId" />
			<input-variable name="ACCOUNT_NUM" />
			<input-variable name="ACTIVATION_TYPE" />
			<input-variable name="ACTIVATION_MODE" />
			<input-variable name="ACTIVATION_STATUS" />
			<input-variable name="CARD_TYPE" /> 
			<input-variable name="TRANSACTION_ID" /> 
			<input-variable name="TRANSACTION_AMOUNT" /> 
			<input-variable name="PAYMENT_STARTED_GMT" />
			<input-variable name="PAYMENT_COMPLETED_GMT" /> 
			<input-variable name="ESN" />
			<input-variable name="ANI" />
			<input-variable name="DID" />
			<input-variable name="MDN" />
			<input-variable name="CITY" />
			<input-variable name="STATE" />
			<input-variable name="ZIP" />
			<input-variable name="ERROR_TEXT" />
		</inputs>
		<outputs>
			<output-variable name="status" />
			<output-variable name="message" />
		</outputs>
	</data-access>
	<if cond="(GlobalVars.IOMGetPromotion != null) || (GlobalVars.GetAccountDetails != null)"><action next="EndCall_DB" /><else><action next="EndApplication" /></else></if><event><if cond="(haveMDN == true) &amp;&amp; (GlobalVars.IOMGetPromotion != null)"><action next="EndCall_DB" /><else><action next="EndApplication" /></else></if></event></data-access-state><data-access-state id="EndCall_DB"><session-mapping key="sessionId" value="appsessionID" type="String" /><session-mapping key="min" value="GlobalVars.GetAccountDetails.mdn" type="String" /><data-access id="CARE_EndCall" classname="com.nuance.metro.dataaccess.CARE_EndCall">
	    	<fetchAudio>fetch_audio_30s</fetchAudio>
		      <inputs>
		        <input-variable name="min" />
		        <input-variable name="sessionId" />
		      </inputs>
		      <outputs>
		        <output-variable name="status" />
		        <output-variable name="message" />
		      </outputs>
    </data-access>  
  <action next="EndApplication" /><event><action next="EndApplication" /></event></data-access-state><custom-state id="EndApplication"><if cond="(GlobalVars.GetBCSParameters != null &amp;&amp; GlobalVars.GetBCSParameters != undefined) &amp;&amp;((GlobalVars.GetBCSParameters.ivr_monitoring_enabled != null &amp;&amp; GlobalVars.GetBCSParameters.ivr_monitoring_enabled != undefined)&amp;&amp; (GlobalVars.GetBCSParameters.ivr_monitoring_enabled == 'true' || GlobalVars.GetBCSParameters.ivr_monitoring_enabled == true))"><action next="InvokeMessagingService_DB_Pre" /><else><action next="EndApplication2" /></else></if></custom-state><custom-state id="InvokeMessagingService_DB_Pre"><if cond="(monitoringFullString == '' || monitoringFullString == undefined || monitoringFullString == 'undefined')"><action next="EndApplication2" /><else><action next="InvokeMessagingService_DB" /></else></if></custom-state><data-access-state id="InvokeMessagingService_DB"><data-access id="SendFrontendEvent" classname="com.nuance.metro.dataaccess.SendFrontendEvents">
		<inputs>
			<input-variable name="monitoringFullString" />
		</inputs>
		<outputs>
			<output-variable name="status" />
			<output-variable name="message" />
		</outputs>
	</data-access>
	<action next="EndApplication2" /></data-access-state><custom-state id="EndApplication2"><exit /><event cond="exitErrorCount==0"><session-mapping key="exitErrorCount" expr="exitErrorCount + 1" /><exit /></event><event><exit /></event></custom-state></dialog><dialog id="Goodbye"><custom-state id="Goodbye_Form"><action next="PromptBeforeTransfer" /></custom-state><custom-state id="PromptBeforeTransfer"><field name="ForcePromptQueueBeforeExiting"><prompt timeout="50ms" /><grammar version="1.0" xml:lang="en-US" tag-format="semantics/1.0" root="empty" xml:base="Goodbye.dvxml"><rule id="empty" scope="public"><one-of><item>dummy item</item></one-of></rule></grammar><noinput><action next="end" /></noinput><nomatch><action next="end" /></nomatch><action next="end" /></field><catch><action next="end" /></catch></custom-state><custom-state id="end"><gotodialog next="Exit" /></custom-state></dialog><dialog id="Goodbye_Main"><play-state id="GB1005_PlayGoodbyeMessage_PP"><session-mapping key="otherGhostCaller" value="GlobalVars.otherGhostCaller" type="String" /><session-mapping key="playGoodbyeMessage" value="GlobalVars.playGoodbyeMessage" type="String" /><session-mapping key="skipGoodbye" value="GlobalVars.skipGoodbye" type="String" /><session-mapping key="playFailureGoodbye" value="GlobalVars.playFailureGoodbye" type="String" /><session-mapping key="securityQuestionCode" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityQuestionCode:''" type="String" /><session-mapping key="maxRequests" value="GlobalVars.maxRequests" type="String" /><session-mapping key="accountStatus" value="(GlobalVars.GetAccountDetails)?GlobalVars.GetAccountDetails.accountStatus :''" type="String" /><session-mapping key="care_allow_suspended_transfers" value="GlobalVars.GetBCSParameters.care_allow_suspended_transfers == 'true'" type="String" /><audio><if cond="(skipGoodbye == true)">
    		<prompt id="silence_100ms" /><elseif cond="(operatorReqCount &gt; maxRequests) &amp;&amp; (accountStatus == 'suspended') &amp;&amp; (care_allow_suspended_transfers == false)">
           <prompt id="GB1005_out_03" /></elseif><elseif cond="securityQuestionCode == 'SQ8'">
    		<prompt id="GB1005_out_02" /></elseif><elseif cond="playFailureGoodbye == true">
    		<prompt id="GB1005_out_04" /></elseif><elseif cond="isMDE == true || otherGhostCaller == true">
    		<prompt id="GB1005_out_01" /></elseif><else>
    		<prompt id="GB1005_ini_01" cond="playGoodbyeMessage == true" /></else></if></audio><action next="GB1010_EndCall_CT" /></play-state><custom-state id="GB1010_EndCall_CT"><gotodialog next="Goodbye" /></custom-state></dialog><dialog id="InitialHandling_IOMPromo"><session-mapping key="isIOMToPlay" value="false" type="Boolean" /><session-mapping key="accepted" value="false" type="Boolean" /><session-mapping key="startTime" value="" type="String" /></dialog><dialog id="Initialization"><custom-state id="toTestPage"><session-mapping key="subAni" expr="result.ani" /><session-mapping key="isTesting" value="true" type="Boolean" /><action next="StartApplication" /></custom-state><custom-state id="StartApplication"><session-mapping key="mdnChangeVars" expr="initializeMonitoringObject(appsessionID, ani)" /><session-mapping key="paymentVars" expr="initializeMonitoringObject(appsessionID, ani)" /><session-mapping key="esnChangeVars" expr="initializeMonitoringObject(appsessionID, ani)" /><session-mapping key="ctiAttachVars" expr="initializeMonitoringObject(appsessionID, ani)" /><session-mapping key="ctiTransferVars" expr="initializeMonitoringObject(appsessionID, ani)" /><session-mapping key="newPlanVars" expr="initializeMonitoringObject(appsessionID, ani)" /><session-mapping key="addFeatureVars" expr="initializeMonitoringObject(appsessionID, ani)" /><session-mapping key="activationVars" expr="initializeMonitoringObject(appsessionID, ani)" /><session-mapping key="GlobalVars.trn" expr="ani" /><session-mapping key="GlobalVars.providerId" expr="'providerId'" /><session-mapping key="GlobalVars.sessionId" expr="appsessionID" /><session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean" /><session-mapping key="GlobalVars.unCoopMaxRequest" expr="4" /><session-mapping key="GlobalVars.coopMaxRequest" expr="4" /><session-mapping key="GlobalVars.customerRequestedOperatorInCustomerScreen" value="false" type="Boolean" /><session-mapping key="GlobalVars.FirstTimeDH1060" value="true" type="Boolean" /><session-mapping key="GlobalVars.FirstTimeRP1040" value="true" type="Boolean" /><session-mapping key="GlobalVars.FirstTimeRP1415" value="true" type="Boolean" /><session-mapping key="GlobalVars.FirstTimeRP0520" value="true" type="Boolean" /><session-mapping key="ActivationTable.ANI" expr="ani" /><session-mapping key="ActivationTable.DID" expr="dnis" /><session-mapping key="ActivationTable.SESSION_ID" expr="appsessionID" /><session-mapping key="ActivationTable.ACTIVATION_MODE" expr="'0'" /><session-mapping key="ActivationTable.ACCOUNT_NUM" expr="''" /><session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="''" /><session-mapping key="ActivationTable.ACTIVATION_TYPE" expr="''" /><session-mapping key="ActivationTable.ACTIVATION_STARTED" expr="''" /><session-mapping key="ActivationTable.ESN" expr="''" /><session-mapping key="ActivationTable.MDN" expr="''" /><session-mapping key="ActivationTable.RATE_PLAN" expr="''" /><session-mapping key="ActivationTable.TRF_SWITCH_ON" expr="'F'" /><session-mapping key="ActivationTable.CITY" expr="''" /><session-mapping key="ActivationTable.STATE" expr="''" /><session-mapping key="ActivationTable.ZIP" expr="''" /><session-mapping key="ActivationTable.BILLABLE_DATE" expr="''" /><session-mapping key="ActivationTable.OLD_PHONE_TYPE" expr="'0'" /><session-mapping key="ActivationTable.ERROR_TEXT" expr="''" /><session-mapping key="PaymentTable.ANI" expr="ani" /><session-mapping key="PaymentTable.DID" expr="dnis" /><session-mapping key="PaymentTable.SESSION_ID" expr="appsessionID" /><session-mapping key="PaymentTable.ACTIVATION_MODE" expr="'0'" /><session-mapping key="PaymentTable.ACCOUNT_NUM" expr="''" /><session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="''" /><session-mapping key="PaymentTable.ACTIVATION_TYPE" expr="''" /><session-mapping key="PaymentTable.ACTIVATION_STARTED" expr="''" /><session-mapping key="PaymentTable.ESN" expr="''" /><session-mapping key="PaymentTable.MDN" expr="''" /><session-mapping key="PaymentTable.RATE_PLAN" expr="''" /><session-mapping key="PaymentTable.TRF_SWITCH_ON" expr="'F'" /><session-mapping key="PaymentTable.CITY" expr="''" /><session-mapping key="PaymentTable.STATE" expr="''" /><session-mapping key="PaymentTable.ZIP" expr="''" /><session-mapping key="PaymentTable.BILLABLE_DATE" expr="''" /><session-mapping key="PaymentTable.OLD_PHONE_TYPE" expr="'0'" /><session-mapping key="PaymentTable.ERROR_TEXT" expr="''" /><session-mapping key="PaymentTable.CARD_TYPE" expr="''" /><session-mapping key="PaymentTable.ACTIVATION_ENDED_GMT" expr="''" /><session-mapping key="PaymentTable.TRANSACTION_ID" expr="''" /><session-mapping key="PaymentTable.TRANSACTION_AMOUNT" expr="''" /><session-mapping key="GlobalVars.XR1020_visit_count" expr="0" /><session-mapping key="GlobalVars.operatorPaymentsReqCount" expr="0" /><session-mapping key="GlobalVars.VS1135initialEntry" value="true" type="Boolean" /><session-mapping key="GlobalVars.mp2025_reentry" value="false" type="Boolean" /><session-mapping key="GlobalVars.acceptPayByPhone" expr="undefined" /><session-mapping key="GlobalVars.otherGhostCaller" value="false" type="Boolean" /><session-mapping key="GlobalVars.metroCardFail" value="false" type="Boolean" /><session-mapping key="GlobalVars.acceptBCR" value="false" type="Boolean" /><session-mapping key="GlobalVars.playFailureGoodbye" value="false" type="Boolean" /><session-mapping key="GlobalVars.maxNoInput" value="false" type="Boolean" /><session-mapping key="GlobalVars.eventCapture" expr="''" /><session-mapping key="GlobalVars.clearCache" value="false" type="Boolean" /><gotodialog next="Init_Main" /></custom-state></dialog><dialog id="Init_Main"><custom-state id="HL1005_Start"><session-mapping key="GlobalVars.outageByColo" value="false" type="Boolean" /><session-mapping key="GlobalVars.fromMyMetroCustomerSupport" value="false" type="Boolean" /><session-mapping key="GlobalVars.playGoodbyeMessage" value="true" type="Boolean" /><session-mapping key="GlobalVars.metrics_hasVisitedMainMenu" value="false" type="Boolean" /><session-mapping key="GlobalVars.cti_AuthStatus" expr="'00'" /><session-mapping key="GlobalVars.cti_TransferReason" expr="'01'" /><session-mapping key="GlobalVars.cti_Intent" expr="'000'" /><session-mapping key="GlobalVars.playedCCPA" value="false" type="Boolean" /><session-mapping key="GlobalVars.langAskedAlready" value="false" type="Boolean" /><if cond="language == 'en-US'"><session-mapping key="TransferTag" expr="'Customer_Support_English'" /><else><session-mapping key="TransferTag" expr="'Customer_Support_Spanish'" /></else></if><action next="HL1007_GetCTIParameters_DB" /></custom-state><data-access-state id="HL1007_GetCTIParameters_DB"><session-mapping key="sessionId" value="appsessionID" type="String" /><data-access id="GetCTIParameters" classname="com.nuance.metro.dataaccess.GetCTIParameters">
	 	<inputs />
     	<outputs>
      	   <output-variable name="status" />
      	   <output-variable name="ctiAuthStatus" />
      	   <output-variable name="ctiTransferReason" />
      	   <output-variable name="ctiIntent" />
    	</outputs>
    </data-access>
    
	
    <if cond="GetCTIParameters.status == 'Success'"><session-mapping key="GlobalVars.GetCTIParameters" expr="GetCTIParameters" /><else><session-mapping key="GlobalVars.isCTISuccess" value="false" type="Boolean" /></else></if><action next="HL1006_LookupConfigs_DB" /></data-access-state><data-access-state id="HL1006_LookupConfigs_DB"><session-mapping key="sessionId" value="appsessionID" type="String" /><session-mapping key="min" value="GlobalVars.trn" type="String" /><session-mapping key="did" value="dnis" type="String" /><data-access id="GetBCSParameters" classname="com.nuance.metro.dataaccess.GetBCSParameters">
      <inputs>
	              <input-variable name="min" />
	              <input-variable name="sessionId" />
	              <input-variable name="did" />
	        </inputs>
      <outputs>
        <output-variable name="status" />
      </outputs>
    </data-access>

    <if cond="GetBCSParameters.status == 'Success'"><session-mapping key="GlobalVars.GetBCSParameters" expr="GetBCSParameters" /><session-mapping key="GlobalVars.isBCSSuccess" value="true" type="Boolean" /><session-mapping key="GlobalVars.outageByColo" expr="GlobalVars.GetBCSParameters.isOutage == 'true' || GlobalVars.GetBCSParameters.isOutage==true" /><session-mapping key="GlobalVars.enteredFrom" expr="getEnteredFrom(dnis,GlobalVars.GetBCSParameters)" /><session-mapping key="GlobalVars.unCoopMaxRequest" expr="GlobalVars.GetBCSParameters.unCoopMaxRequest" /><session-mapping key="GlobalVars.coopMaxRequest" expr="GlobalVars.GetBCSParameters.coopMaxRequest" /><session-mapping key="GlobalVars.care_enable_autocorrect_offer" expr="GlobalVars.GetBCSParameters.care_enable_autocorrect_offer" /><session-mapping key="GlobalVars.care_enable_twofactorauth" expr="GlobalVars.GetBCSParameters.care_enable_twofactorauth" /><session-mapping key="GlobalVars.care_rateplan_special_error_planlist" expr="GlobalVars.GetBCSParameters.care_rateplan_special_error_planlist" /><else><session-mapping key="GlobalVars.isBCSSuccess" value="false" type="Boolean" /></else></if><action next="HL1010_CheckDSG_DS" /></data-access-state><data-access-state id="HL1008_GetAccountAndSubscriberLite_DB"><session-mapping key="sessionId" value="appsessionID" type="String" /><session-mapping key="mdn" value="GlobalVars.trn" type="String" /><data-access id="GetAccountAndSubscriberLite" classname="com.nuance.metro.dataaccess.GetAccountAndSubscriberLite">
		<inputs>
	 		<input-variable name="mdn" />
	 		<input-variable name="sessionId" />
		</inputs>
		<outputs>
	 		<output-variable name="accountType" />
	 		<output-variable name="accountTypeCode" />
	 		<output-variable name="accountSubtype" />
	 		<output-variable name="accountSubtypeCode" />
	 		<output-variable name="subStatus" />
	 		<output-variable name="accountRestrictIndicator" />
		</outputs>
   	</data-access>            
    
      <if cond="GetAccountAndSubscriberLite.status == 'Success'"><session-mapping key="GlobalVars.GetAccountAndSubscriberLite" expr="GetAccountAndSubscriberLite" /><if cond="GlobalVars.GetAccountAndSubscriberLite.accountRestrictIndicator == true"><action next="HL1045_FraudSuspension_PP" /><else><action next="HL1010_CheckDSG_DS" /></else></if><else><action next="HL1010_CheckDSG_DS" /></else></if></data-access-state><play-state id="HL1045_FraudSuspension_PP"><audio><prompt id="HL1045_out_01" /></audio><action next="MN1105_GoToCallTransfer_SD" /></play-state><decision-state id="HL1010_CheckDSG_DS"><session-mapping key="did" value="dnis" type="String" /><if cond="GlobalVars.enteredFrom == 'DSG' || GlobalVars.enteredFrom == 'NRH'"><action next="HL1015_GoToDSG_SD" /><elseif cond="GlobalVars.enteredFrom == 'RCC'"><action next="HL1025_GoToRCC_SD" /></elseif><elseif cond="GlobalVars.enteredFrom == 'WNP'"><action next="HL1030_GoToWPN_SD" /></elseif><elseif cond="GlobalVars.enteredFrom == 'PWR'"><action next="HL1035_GoToPWR_SD" /></elseif><elseif cond="GlobalVars.enteredFrom == 'PC'"><action next="HL1040_GoToPC_SD" /></elseif><else><action next="HL1020_GoToInitialHandling_SD" /></else></if></decision-state><subdialog-state id="HL1015_GoToDSG_SD"><gotodialog next="DSG_Main" /><action next="HLInit_TerminalSD_return" /></subdialog-state><subdialog-state id="HL1020_GoToInitialHandling_SD"><gotodialog next="InitialHandling_Main" /><action next="HLInit_TerminalSD_return" /></subdialog-state><custom-state id="HLInit_TerminalSD_return"><if cond="GlobalVars.enteredFrom == 'WNP'"><action next="HL1030_GoToWPN_SD" /></if></custom-state><subdialog-state id="HL1030_GoToWPN_SD"><gotodialog next="NumberPortStatusLine_Dialog" /><action next="HL1030_GoToWPN_SD_return" /></subdialog-state><custom-state id="HL1030_GoToWPN_SD_return"><gotodialog next="Exit" /></custom-state><subdialog-state id="HL1035_GoToPWR_SD"><gotodialog next="PasswordResetLine_Dialog" /><action next="HL1035_GoToPWR_SD_return" /></subdialog-state><custom-state id="HL1035_GoToPWR_SD_return"><gotodialog next="Exit" /></custom-state><subdialog-state id="HL1040_GoToPC_SD"><gotodialog next="PromotionCenterLine_Dialog" /><action next="HL1040_GoToPC_SD_return" /></subdialog-state><custom-state id="HL1040_GoToPC_SD_return"><gotodialog next="Exit" /></custom-state><subdialog-state id="MN1105_GoToCallTransfer_SD"><gotodialog next="CallTransfer_Main" /><action next="MN1105_GoToCallTransfer_SD_return" /></subdialog-state><custom-state id="MN1105_GoToCallTransfer_SD_return"><gotodialog next="Exit" /></custom-state><subdialog-state id="HL1025_GoToRCC_SD"><gotodialog next="RuralCallCompletionLine" /><action next="HL1025_GoToRCC_SD_return" /></subdialog-state><custom-state id="HL1025_GoToRCC_SD_return"><gotodialog next="Exit" /></custom-state></dialog><dialog id="IntentPrediction"><decision-state id="IP1001_InitializeIntentPrediction_DS"><action next="IP1005_SSVPProfiles_DB" /></decision-state><data-access-state id="IP1005_SSVPProfiles_DB"><session-mapping key="mdn" value="GlobalVars.trn" type="String" /><data-access id="SSVPProfiles" classname="com.nuance.metro.dataaccess.SSVPProfiles">
    	<fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn" />
        </inputs>
        <outputs>
          <output-variable name="lastCustomerIntent" />
          <output-variable name="status" />
        </outputs>
    </data-access>
    
    <if cond="SSVPProfiles.status &amp;&amp; SSVPProfiles.status.toUpperCase() == 'SUCCESS' "><session-mapping key="GlobalVars.lastCustomerIntent_logging" expr="SSVPProfiles.lastCustomerIntent" /><session-mapping key="GlobalVars.lastCustomerIntent" expr="SSVPProfiles.lastCustomerIntent" /></if><if cond="(SSVPProfiles.lastCustomerIntent == undefined || SSVPProfiles.lastCustomerIntent == '')"><action next="getReturnLink()" /><else><action next="IP1010_SSVPCustomEvent_DB" /></else></if></data-access-state><data-access-state id="IP1010_SSVPCustomEvent_DB"><session-mapping key="mdn" value="GlobalVars.trn" type="String" /><session-mapping key="lastCustomerIntent" value="GlobalVars.lastCustomerIntent_logging" type="String" /><data-access id="SSVPCustomEvent" classname="com.nuance.metro.dataaccess.SSVPCustomEvent">
    	<fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn" />
          <input-variable name="lastCustomerIntent" />
        </inputs>
        <outputs>
          <output-variable name="isLastIntentUpdated" />
          <output-variable name="status" />
        </outputs>
    </data-access>
    <action next="getReturnLink()" /></data-access-state></dialog><dialog id="MainMenu_CheckSecurity"><decision-state id="MM1050_CheckContinueToDestination_DS"><if cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ9')"><session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'not_authenticated_high_security_account')" /><gotodialog next="MainMenu_Routing" /><elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ8')"><action next="MM1070_GoToGoodbye_SD" /></elseif><elseif cond="GlobalVars.securityRequired == true"><action next="MM1055_CheckAlreadyHavePIN_DS" /></elseif><else><gotodialog next="MainMenu_Routing" /></else></if></decision-state><decision-state id="MM1055_CheckAlreadyHavePIN_DS"><if cond="GlobalVars.collectedPIN"><gotodialog next="MainMenu_Routing" /><else><action next="MM1060_GoToLogin_SD" /></else></if></decision-state><subdialog-state id="MM1060_GoToLogin_SD"><gotodialog next="Login_Main" /><action next="MM1060_GoToLogin_SD_return" /></subdialog-state><custom-state id="MM1060_GoToLogin_SD_return"><if cond="GlobalVars.callType == 'my_account'"><gotodialog next="MainMenu_Main" /><else><gotodialog next="MainMenu_Routing" /></else></if></custom-state><subdialog-state id="MM1070_GoToGoodbye_SD"><gotodialog next="Goodbye_Main" /></subdialog-state><custom-state id="MM1070_GoToGoodbye_SD_return"><gotodialog next="Exit" /></custom-state></dialog><dialog id="MainMenu_Routing"><decision-state id="MM1510_RouteToDestination_DS"><session-mapping key="accountStatus" value="(GlobalVars.GetAccountDetails)?GlobalVars.GetAccountDetails.accountStatus :''" type="String" /><if cond="false"><session-mapping key="GlobalVars.playTransferMessage " value="true" type="Boolean" /><action next="MM1545_GoToCallTransfer_SD" /><elseif cond="GlobalVars.callType == 'extension'"><action next="MM1595_ApplyExtension_SD" /></elseif><elseif cond="GlobalVars.callType == 'switch_lines'"><action next="MM1598_GoToSwitchLines_SD" /></elseif><elseif cond="GlobalVars.callType == 'auto_pay'"><session-mapping key="GlobalVars.callType" expr="'auto_pay'" /><gotodialog next="MainMenu_Main" /></elseif><elseif cond="GlobalVars.callType == 'make_pmt'"><action next="MM1520_GoToMakePayment_SD" /></elseif><elseif cond="GlobalVars.callType== 'my_features'"><action next="MM1565_GoToCurrentFeatures_SD" /></elseif><elseif cond="GlobalVars.callType == 'new_customer'"><action next="MM1570_GoToGettingStarted_SD" /></elseif><elseif cond="GlobalVars.callType == 'acct_bal'"><if cond="GlobalVars.tag == 'home-internet_active'"><action next="MM1525_GoToRatePlan_SD" /><else><action next="MM1575_GoToBalanceBreakdown_SD" /></else></if></elseif><elseif cond="GlobalVars.callType == 'plan_details'"><if cond="GlobalVars.tag == 'home-internet_active'"><action next="MM1525_GoToRatePlan_SD" /><else><action next="MM1575_GoToBalanceBreakdown_SD" /></else></if></elseif><elseif cond="GlobalVars.callType == 'change_plan'"><action next="MM1525_GoToRatePlan_SD" /></elseif><elseif cond="GlobalVars.callType == 'add_feature'"><action next="MM1530_GoToVoiceStore_SD" /></elseif><elseif cond="GlobalVars.callType == 'reset_pin'"><action next="MM1560_GoToVoicemailPIN_SD" /></elseif><elseif cond="GlobalVars.callType == 'troubleshoot'"><action next="MM1535_GoToTroubleshooting_SD" /></elseif><elseif cond="GlobalVars.callType == 'find_store'"><action next="MM1540_GoToStoreLocator_SD" /></elseif><elseif cond="GlobalVars.callType == 'help_me_out'"><action next="MM1585_GoToFAQ_SD" /></elseif><elseif cond="GlobalVars.callType == 'troubleshoot' || GlobalVars.callType == 'payment_tip'"><action next="MM1535_GoToTroubleshooting_SD" /></elseif><elseif cond="GlobalVars.callType == 'data_usage'"><action next="MM1590_GoToDataUsage_SD" /></elseif><elseif cond="GlobalVars.callType == 'transfer' || GlobalVars.callType == 'tmomoney'"><action next="MM1545_GoToCallTransfer_SD" /></elseif><elseif cond="GlobalVars.callType == 'lost_phone'"><action next="MM1550_GoToLostPhone_SD" /></elseif><elseif cond="GlobalVars.callType == 'pmt_help'"><action next="MM1596_GoToPaymentHelp_SD" /></elseif><elseif cond="GlobalVars.callType == 'bal_details'"><action next="MM1575_GoToBalanceBreakdown_SD" /></elseif><elseif cond="GlobalVars.callType == 'add_data'"><action next="MM1597_GoToHighSpeedDataAvailable_SD" /></elseif><else><action next="MM1555_GoToDeviceHandling_SD" /></else></if></decision-state><subdialog-state id="MM1515_GoToAccountBalance_SD"><gotodialog next="AccountBalance_Main" /><action next="MM1515_GoToAccountBalance_SD_return" /></subdialog-state><custom-state id="MM1515_GoToAccountBalance_SD_return"><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1520_GoToMakePayment_SD"><gotodialog next="MakePayment_Main" /><action next="MM1520_GoToMakePayment_SD_Return" /></subdialog-state><custom-state id="MM1520_GoToMakePayment_SD_Return"><if cond="(GlobalVars.tag == 'request-extension')"><action next="MM1595_ApplyExtension_SD" /><else><gotodialog next="MainMenu_Main" /></else></if></custom-state><subdialog-state id="MM1595_ApplyExtension_SD"><gotodialog next="ApplyExtension_Main" /><action next="MM1595_ApplyExtension_SD_return" /></subdialog-state><custom-state id="MM1595_ApplyExtension_SD_return"><if cond="(GlobalVars.tag == 'make-payment')"><action next="MM1520_GoToMakePayment_SD" /><elseif cond="(GlobalVars.callType == 'extension')"><session-mapping key="GlobalVars.callType" expr="undefined" /><gotodialog next="MainMenu_Main" /></elseif><else><gotodialog next="MainMenu_Main" /></else></if></custom-state><subdialog-state id="MM1525_GoToRatePlan_SD"><gotodialog next="RatePlan_Main" /><action next="MM1525_GoToRatePlan_SD_return" /></subdialog-state><custom-state id="MM1525_GoToRatePlan_SD_return"><session-mapping key="GlobalVars.callType" expr="undefined" /><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1530_GoToVoiceStore_SD"><gotodialog next="Voicestore_Main" /><action next="MM1530_GoToVoiceStore_SD_return" /></subdialog-state><custom-state id="MM1530_GoToVoiceStore_SD_return"><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1535_GoToTroubleshooting_SD"><gotodialog next="TechnicalSupport" /><action next="MM1535_GoToTroubleshooting_SD_Return" /></subdialog-state><custom-state id="MM1535_GoToTroubleshooting_SD_Return"><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1545_GoToCallTransfer_SD"><gotodialog next="CallTransfer_Main" /><action next="MM1545_GoToCallTransfer_SD_Return" /></subdialog-state><custom-state id="MM1545_GoToCallTransfer_SD_Return"><gotodialog next="Goodbye" /></custom-state><subdialog-state id="MM1540_GoToStoreLocator_SD"><gotodialog next="StoreLocator_Main" /><action next="MM1540_GoToStoreLocator_SD_Return" /></subdialog-state><custom-state id="MM1540_GoToStoreLocator_SD_Return"><action next="MM1580_GoToAnythingElse_SD" /></custom-state><subdialog-state id="MM1550_GoToLostPhone_SD"><gotodialog next="LostPhone_Main" /><action next="MM1550_GoToLostPhone_SD_Return" /></subdialog-state><custom-state id="MM1550_GoToLostPhone_SD_Return"><gotodialog next="Goodbye" /></custom-state><subdialog-state id="MM1555_GoToDeviceHandling_SD"><gotodialog next="DeviceHandling_Main" /><action next="MM1555_GoToDeviceHandling_SD_Return" /></subdialog-state><custom-state id="MM1555_GoToDeviceHandling_SD_Return"><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1560_GoToVoicemailPIN_SD"><gotodialog next="VoiceMailPin_Main" /><action next="MM1560_GoToVoicemailPIN_SD_Return" /></subdialog-state><custom-state id="MM1560_GoToVoicemailPIN_SD_Return"><gotodialog next="Goodbye" /></custom-state><subdialog-state id="MM1565_GoToCurrentFeatures_SD"><gotodialog next="CurrentFeatures_Main" /><action next="MM1565_GoToCurrentFeatures_SD_return" /></subdialog-state><custom-state id="MM1565_GoToCurrentFeatures_SD_return"><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1570_GoToGettingStarted_SD"><gotodialog next="GettingStarted_Main" /><action next="MM1570_GoToGettingStarted_SDReturn" /></subdialog-state><custom-state id="MM1570_GoToGettingStarted_SDReturn"><gotodialog next="Goodbye" /></custom-state><subdialog-state id="MM1580_GoToAnythingElse_SD"><gotodialog next="AnythingElse_Main" /><action next="MM1580_GoToAnythingElse_SDReturn" /></subdialog-state><custom-state id="MM1580_GoToAnythingElse_SDReturn"><gotodialog next="Goodbye" /></custom-state><subdialog-state id="MM1585_GoToFAQ_SD"><gotodialog next="FAQ_Main" /><action next="MM1585_GoToFAQ_SDReturn" /></subdialog-state><custom-state id="MM1585_GoToFAQ_SDReturn"><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1590_GoToDataUsage_SD"><gotodialog next="DataUsage_Main" /><action next="MM1590_GoToDataUsage_SDReturn" /></subdialog-state><custom-state id="MM1590_GoToDataUsage_SDReturn"><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1575_GoToBalanceBreakdown_SD"><gotodialog next="BalanceBreakdown" /><action next="MM1575_GoToBalanceBreakdown_SDReturn" /></subdialog-state><custom-state id="MM1575_GoToBalanceBreakdown_SDReturn"><session-mapping key="GlobalVars.callType" expr="undefined" /><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1596_GoToPaymentHelp_SD"><gotodialog next="Payment_Help" /><action next="MM1596_GoToPaymentHelp_SDReturn" /></subdialog-state><custom-state id="MM1596_GoToPaymentHelp_SDReturn"><session-mapping key="GlobalVars.callType" expr="undefined" /><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1597_GoToHighSpeedDataAvailable_SD"><gotodialog next="HighSpeedDataAvailable_Main" /><action next="MM1597_GoToHighSpeedDataAvailable_SDReturn" /></subdialog-state><custom-state id="MM1597_GoToHighSpeedDataAvailable_SDReturn"><session-mapping key="GlobalVars.callType" expr="undefined" /><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1599_GoToSuspended_SD"><gotodialog next="SuspendedHandling_Main" /><action next="MM1599_GoToSuspended_SD_return" /></subdialog-state><custom-state id="MM1599_GoToSuspended_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="MM1598_GoToSwitchLines_SD"><gotodialog next="SwitchLines_Main" /><action next="MM1598_GoToSwitchLines_SD_return" /></subdialog-state><custom-state id="MM1598_GoToSwitchLines_SD_return"><gotodialog next="MainMenu_Main" /></custom-state><subdialog-state id="MM1615_AccountPinReset_SD"><gotodialog next="AccountPINReset_Main" /><action next="MM1615_AccountPinReset_SD_return" /></subdialog-state><custom-state id="MM1615_AccountPinReset_SD_return"><action next="getReturnLink()" /></custom-state></dialog><dialog id="NLUDisambiguation_Start"><dm-state id="nlu0100_NLU_Disambiguation_DM"><session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String" /><session-mapping key="hasAccountDetails" value="GlobalVars.GetAccountDetails != undefined ? true : false" type="String" /><session-mapping key="unCoopMaxRequest" value="GlobalVars.unCoopMaxRequest" type="String" /><session-mapping key="accountStatus" value="GlobalVars.GetAccountDetails.accountStatus" type="String" /><session-mapping key="loggedIn" value="GlobalVars.loggedIn" type="String" /><session-mapping key="isOnFamilyPlan" value="GlobalVars.GetAccountDetails.isOnFamilyPlan" type="String" /><success><action label="default"><assign name="disambigTag" expr=".returnvalue" /><assign name="GlobalVars.confidencescoreDisambigDM" expr=".confidencescore" /><assign name="GlobalVars.ssmScore" expr="(.confidencescore)*100" /><session-mapping key="GlobalVars.tag" expr="disambigTag" /><assign name="dm_name" expr="''" /><if cond="dm_name == 'ND1435_CancelService_DM'"><if cond="GlobalVars.tag == 'true'"><session-mapping key="GlobalVars.tag" expr="'cancel-service_transfer'" /></if></if><if cond="GlobalVars.tag == 'false'"><session-mapping key="disambiguationForm" expr="'ND1436_CancelWhatService_DM'" /><gotodialog next="NLUDisambiguation_Start" /></if><if cond="dm_name == 'ND1440_ChangeAccount_DM'"><if cond="disambigTag == 'hear-current_features'"><session-mapping key="GlobalVars.fromDisamg" value="true" type="Boolean" /></if></if><if cond="disambigTag == 'change-lines'"><session-mapping key="disambiguationForm" expr="'ND1441_AddOrRemoveLines_DM'" /><gotodialog next="NLUDisambiguation_Start" /></if><if cond="dm_name == 'ND1436_CancelWhatService_DM'"><if cond="disambigTag == 'close'"><action next="ND1437_CloseAccount_PP" /></if></if><if cond="disambigTag == 'something-else_cancelservice'"><action next="ND1475_GoToTransfer_SD" /></if><if cond="dm_name == 'ND1420_VagueAutoFeature_DM'"><if cond="disambigTag == 'true'"><session-mapping key="GlobalVars.tag" expr="'vague-autopay'" /></if></if><if cond="disambigTag == 'false'"><session-mapping key="GlobalVars.tag" expr="'vague-auto_feature_reprompt'" /></if><if cond="dm_name == 'ND1455_PrepaidPinPreCheck_DM'"><if cond="disambigTag == 'true'"><if cond="GlobalVars.GetBCSParameters.payments_enable_prepaid_methods == true"><session-mapping key="GlobalVars.tag" expr="'make-payment'" /><session-mapping key="GlobalVars.payingWithPrepaid" value="true" type="Boolean" /><audio><prompt id="ND1455_out_01" /></audio><action next="getReturnLink()" /><else><session-mapping key="GlobalVars.tag" expr="'inquire-prepaid_pin'" /><action next="getReturnLink()" /></else></if></if></if><if cond="disambigTag == 'false'"><session-mapping key="disambiguationForm" expr="'ND1456_PrepaidInfo_DM'" /><gotodialog next="NLUDisambiguation_Start" /></if><if cond="dm_name == 'ND1456_PrepaidInfo_DM'"><if cond="disambigTag == 'true'"><action next="nlu0100_NLU_Disambiguation_DM" /></if></if><if cond="dm_name == 'ND1460_ChangeSim_DM'"><if cond="disambigTag == 'true'"><session-mapping key="GlobalVars.tag" expr="'change-sim_transfer'" /></if></if><if cond="disambigTag == 'false'"><session-mapping key="GlobalVars.tag" expr="'switch-phone'" /></if><if cond="dm_name == 'ND1465_VagueBenefits_DM'"><if cond="disambigTag == 'true'"><session-mapping key="GlobalVars.tag" expr="'inquire-acp'" /></if></if><if cond="disambigTag == 'false'"><audio><prompt id="ND1465_out_01" /></audio><session-mapping key="GlobalVars.tag" expr="undefined" /></if><if cond="dm_name == 'ND1470_VagueUnlock_DM'"><if cond="disambigTag == 'unlock-sim'"><session-mapping key="GlobalVars.tag" expr="'unlock-sim'" /></if></if><if cond="dm_name == 'ND1010_VagueForgotPinDisambig_DM'"><if cond="disambigTag == 'payment-pin'"><session-mapping key="GlobalVars.tag" expr="'pay-prepaid_PIN_Precheck'" /></if></if><if cond="disambigTag == 'request-transfer_pin'" /><if cond="disambigTag == 'something-else_forgotpin'"><action next="ND1475_GoToTransfer_SD" /></if><if cond="disambigTag == 'forgot-account_pin'"><session-mapping key="GlobalVars.tag" expr="'forgot-account_pin'" /><action next="getReturnLink()" /></if><if cond="dm_name == 'ND1305_VagueAgentDestination_DM'"><session-mapping key="GlobalVars.cti_TransferReason" expr="getctiTransferReason(GlobalVars.GetCTIParameters.ctiTransferReason, 'xferred_max_agent_requests')" /><session-mapping key="callerSaidOperator" value="true" type="Boolean" /><session-mapping key="operatorReqCount" expr="operatorReqCount+1" /><if cond="GlobalVars.tag == 'transfer-disambig_troubleshooting'"><session-mapping key="TransferTag" expr="'Technical_Support_English'" /><else><session-mapping key="TransferTag" expr="'Customer_Support_English'" /></else></if><action next="getReturnLink()" /></if><if cond="dm_name == 'ND1015_VaguePlanDisambig_DM'"><session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')" /><session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean" /><session-mapping key="GlobalVars.needMDN" value="false" type="Boolean" /><if cond="GlobalVars.tag == 'add_line'"><session-mapping key="GlobalVars.callType" expr="'add_line'" /><elseif cond="GlobalVars.tag == 'cancel_line'"><session-mapping key="GlobalVars.callType" expr="'cancel_line'" /></elseif></if><action next="getReturnLink()" /></if><if cond="dm_name == 'ND1020_VagueAddServiceDisambig_DM'"><if cond="disambigTag == 'something-else_addservice'"><action next="ND1475_GoToTransfer_SD" /></if></if><if cond="dm_name == 'ND1120_VagueTransferNumberDisambig_DM'"><if cond="disambigTag == 'something-else_xfernum'"><action next="ND1475_GoToTransfer_SD" /></if></if><if cond="dm_name == 'ND1310_VagueActivatePhone_DM'"><if cond="disambigTag == 'something-else_activatephone'"><action next="ND1475_GoToTransfer_SD" /></if></if><if cond="dm_name == 'ND1450_VagueSim_DM'"><if cond="disambigTag == 'something-else_vaguesim'"><action next="ND1475_GoToTransfer_SD" /></if></if><if cond="dm_name == 'ND1315_VagueReactivatePhoneActive_DM'"><if cond="disambigTag == 'something-else_reactivatephone'"><action next="ND1475_GoToTransfer_SD" /></if></if><if cond="dm_name == 'ND1430_ChangePhone_DM'"><if cond="disambigTag == 'something-else_changephone'"><action next="ND1475_GoToTransfer_SD" /></if></if><if cond="dm_name == 'ND1024_ConfirmExistingCustomer_DM'"><if cond="GlobalVars.tag == 'true'"><session-mapping key="disambiguationForm" expr="'ND1020_VagueAddServiceDisambig_DM'" /><gotodialog next="NLUDisambiguation_Start" /></if></if><if cond="GlobalVars.tag == 'false'"><session-mapping key="GlobalVars.callType" expr="'transfer'" /><session-mapping key="TransferTag" expr="'New_Customer_Sales_En'" /><action next="ND1030_GoToTransfer_SD" /></if><action next="getReturnLink()" /></action></success><command><action label="request-representative"><assign name="DMName" expr="''" /><if cond="DMName == 'ND1010_VagueForgotPinDisambig_DM'"><session-mapping key="GlobalVars.tag" expr="'vague-forgot_pin_transfer'" /><action next="getReturnLink()" /></if><session-mapping key="disambig_operator_counter" expr="disambig_operator_counter+1" /><if cond="DMName == 'ND1305_VagueAgentDestination_DM' &amp;&amp; disambig_operator_counter &lt; 2"><session-mapping key="operatorReqCount" expr="operatorReqCount+1" /><action next="nlu0100_NLU_Disambiguation_DM" /><elseif cond="DMName == 'ND1305_VagueAgentDestination_DM' &amp;&amp; (disambig_operator_counter == 2)"><session-mapping key="operatorReqCount" expr="operatorReqCount+1" /><session-mapping key="TransferTag" expr="'Customer_Support_English'" /><session-mapping key="GlobalVars.tag" expr="'transfer-disambig_other'" /><session-mapping key="GlobalVars.cti_TransferReason" expr="getctiTransferReason(GlobalVars.GetCTIParameters.ctiTransferReason, 'xferred_max_agent_requests')" /><session-mapping key="callerSaidOperator" value="true" type="Boolean" /><action next="getReturnLink()" /></elseif></if><session-mapping key="GlobalVars.tag" expr="'request-representative'" /><session-mapping key="callerSaidOperator" value="true" type="Boolean" /><action next="getReturnLink()" /></action><action label="operator"><assign name="DMName" expr="''" /><if cond="DMName == 'ND1010_VagueForgotPinDisambig_DM'"><session-mapping key="GlobalVars.tag" expr="'vague-forgot_pin_transfer'" /><action next="getReturnLink()" /></if><session-mapping key="GlobalVars.tag" expr="'request-representative'" /><session-mapping key="callerSaidOperator" value="true" type="Boolean" /><action next="getReturnLink()" /></action></command><event name="event.nuance.dialog.ndm.maxnomatches"><session-mapping key="GlobalVars.otherGhostCaller" value="true" type="Boolean" /><assign name="dm_maxretries" expr="''" /><if cond="dm_maxretries == 'ND1305_VagueAgentDestination_DM'"><session-mapping key="GlobalVars.tag" expr="'transfer-disambig_other'" /></if><gotodialog next="CallTransfer_Main" /></event><event name="event.nuance.dialog.ndm.maxnoinputs"><session-mapping key="GlobalVars.otherGhostCaller" value="true" type="Boolean" /><session-mapping key="GlobalVars.maxNoInput" value="true" type="Boolean" /><assign name="dm_maxretries" expr="''" /><if cond="dm_maxretries == 'ND1305_VagueAgentDestination_DM'"><session-mapping key="GlobalVars.tag" expr="'transfer-disambig_other'" /></if><gotodialog next="CallTransfer_Main" /></event></dm-state><play-state id="ND1437_CloseAccount_PP"><audio><prompt id="ND1437_out_01" /><prompt id="silence_3000ms" /></audio><session-mapping key="GlobalVars.tag" expr="'cancel-service_transfer'" /><action next="ND1480_GoToGoodBye_SD" /></play-state><play-state id="ND1425_InquireImeiNum_PP"><audio><prompt id="ND1425_out_01" /></audio><action next="getReturnLink()" /></play-state><subdialog-state id="ND1030_GoToTransfer_SD"><gotodialog next="CallTransfer_Main" /><action next="ND1030_GoToTransfer_SD_return" /></subdialog-state><subdialog-state id="ND1475_GoToTransfer_SD"><gotodialog next="CallTransfer_Main" /><action next="ND1475_GoToTransfer_SDreturn" /></subdialog-state><subdialog-state id="ND1480_GoToGoodBye_SD"><gotodialog next="Goodbye_Main" /><action next="ND1480_GoToGoodBye_SDreturn" /></subdialog-state></dialog><dialog id="NLURouting_Start"><decision-state id="NR0001_CheckIntentMetrics_DS"><if cond="(GlobalVars.tag == 'buy-data_topup')"><action next="NR0005_MetricsPreLoginDataTopup_DS" /><elseif cond="(GlobalVars.tag == 'request-extension')"><action next="NR0010_MetricsPreLoginExtension_DS" /></elseif><elseif cond="(GlobalVars.tag == 'reset-voicemail_pin')"><action next="NR0015_MetricsPreLoginVoicemailPIN_DS" /></elseif><elseif cond="(GlobalVars.tag == 'payment-help' || GlobalVars.tag == 'vague-billing')"><action next="NR1005_CheckNeedMDN_DS" /></elseif><elseif cond="GlobalVars.tag == 'inquire-rebate'"><action next="NR1525_GoToPromotionCenterLine_SD" /></elseif><else><action next="NR1001_CheckContext_DS" /></else></if></decision-state><decision-state id="NR0005_MetricsPreLoginDataTopup_DS"><action next="NR1001_CheckContext_DS" /></decision-state><decision-state id="NR0010_MetricsPreLoginExtension_DS"><action next="NR1001_CheckContext_DS" /></decision-state><decision-state id="NR0015_MetricsPreLoginVoicemailPIN_DS"><action next="NR1001_CheckContext_DS" /></decision-state><decision-state id="NR1001_CheckContext_DS"><action next="NR1005_CheckNeedMDN_DS" /></decision-state><decision-state id="NR1005_CheckNeedMDN_DS"><session-mapping key="needMDN" value="GlobalVars.GetTagData.tagVariables != undefined ? GlobalVars.GetTagData.tagVariables.needMDN : 'false'" type="String" /><if cond="((needMDN == true || needMDN == 'true') &amp;&amp; GlobalVars.GetAccountDetails == undefined)"><action next="NR1015_Login_SD" /><else><action next="NR1010_CheckNeedPIN_DS" /></else></if></decision-state><decision-state id="NR1010_CheckNeedPIN_DS"><session-mapping key="securityRequired" value="GlobalVars.GetTagData.tagVariables != undefined ? GlobalVars.GetTagData.tagVariables.securityRequired : 'false'" type="String" /><if cond="((securityRequired == true || securityRequired == 'true') &amp;&amp; GlobalVars.loggedIn != true)"><action next="NR1015_Login_SD" /><else><action next="NR1101_NLUIntentRouting1_DS" /></else></if></decision-state><subdialog-state id="NR1015_Login_SD"><gotodialog next="Login_Main" /><action next="NR1015_Login_SD_return" /></subdialog-state><custom-state id="NR1015_Login_SD_return"><if cond="GlobalVars.tag == undefined"><action next="getReturnLink()" /><else><action next="NR1101_NLUIntentRouting1_DS" /></else></if></custom-state><decision-state id="NR1101_NLUIntentRouting1_DS"><session-mapping key="isSuspended" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" type="String" /><if cond="(GlobalVars.tag == 'forgot-account_pin')"><action next="NR1150_AccountPINReset_SD" /><elseif cond="(GlobalVars.tag == 'vague-autopay') || (GlobalVars.tag == 'cancel-autopay') || (GlobalVars.tag == 'setup-autopay')"><if cond="isSuspended == true"><action next="NR1145_SuspendedHandling_SD" /><else><action next="NR1135_AutoPay_SD" /></else></if></elseif><elseif cond="GlobalVars.tag == 'metro-flex'"><if cond="(GlobalVars.GetBCSParameters.metroFlexEnabled == true ||GlobalVars.GetBCSParameters.metroFlexEnabled == 'true')&amp;&amp; (GlobalVars.GetBCSParameters.metroFlexMessageToPlay == true ||GlobalVars.GetBCSParameters.metroFlexMessageToPlay == 'true')"><session-mapping key="GlobalVars.broadcastMessageCallingDialog" expr="'MF'" /><action next="NR1105_BroadcastMessage_SD" /><else><action next="NR1540_GoToTransfer_SD" /></else></if></elseif><elseif cond="(GlobalVars.tag == 'change-payment_card')"><action next="NR1140_ManageCards_SD" /></elseif><elseif cond="(GlobalVars.tag == 'make-payment')"><action next="NR1110_MakePayment_SD" /></elseif><elseif cond="GlobalVars.tag == 'pay-prepaid_pin'"><session-mapping key="GlobalVars.tag" expr="'make-payment'" /><session-mapping key="GlobalVars.payingWithPrepaid" value="true" type="Boolean" /><action next="NR1110_MakePayment_SD" /></elseif><elseif cond="(GlobalVars.tag == 'hear-plan_details' || GlobalVars.tag == 'change-plan')"><action next="NR1115_RatePlan_SD" /></elseif><elseif cond="(GlobalVars.tag == 'reset-voicemail_pin')"><action next="NR1120_VoicemailPIN_SD" /></elseif><elseif cond="(GlobalVars.tag == 'add-feature_active')"><action next="NR1125_VoiceStore_SD" /></elseif><elseif cond="(GlobalVars.GetTagData.destinationName == 'NR1130_Goodbye_SD')"><action next="NR1130_Goodbye_SD" /></elseif><else><action next="NR1201_NLUIntentRouting2_DS" /></else></if></decision-state><subdialog-state id="NR1145_SuspendedHandling_SD"><gotodialog next="SuspendedHandling_Main" /><action next="NR1145_SuspendedHandling_SD_Return" /></subdialog-state><custom-state id="NR1145_SuspendedHandling_SD_Return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1105_BroadcastMessage_SD"><session-mapping key="GlobalVars.broadcastMessageKey" expr="'MF'" /><gotodialog next="BroadcastMessages" /><action next="NR1105_BroadcastMessage_SD_Return" /></subdialog-state><custom-state id="NR1105_BroadcastMessage_SD_Return"><session-mapping key="GlobalVars.tag" expr="undefined" /><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1110_MakePayment_SD"><gotodialog next="MakePayment_Main" /><action next="NR1110_MakePayment_SD_Return" /></subdialog-state><custom-state id="NR1110_MakePayment_SD_Return"><if cond="GlobalVars.tag == 'request-extension'"><action next="NR1210_ApplyExtension_SD" /><else><action next="getReturnLink()" /></else></if></custom-state><subdialog-state id="NR1115_RatePlan_SD"><gotodialog next="RatePlan_Main" /><action next="NR1115_RatePlan_SD_return" /></subdialog-state><custom-state id="NR1115_RatePlan_SD_return"><if cond="GlobalVars.tag == 'request-extension'"><action next="NR1210_ApplyExtension_SD" /><else><action next="getReturnLink()" /></else></if></custom-state><subdialog-state id="NR1120_VoicemailPIN_SD"><gotodialog next="VoiceMailPin_Main" /><action next="NR1120_VoicemailPIN_SD_Return" /></subdialog-state><custom-state id="NR1120_VoicemailPIN_SD_Return"><gotodialog next="Goodbye_Main" /></custom-state><subdialog-state id="NR1125_VoiceStore_SD"><gotodialog next="Voicestore_Main" /><action next="NR1125_VoiceStore_SD_return" /></subdialog-state><custom-state id="NR1125_VoiceStore_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1130_Goodbye_SD"><gotodialog next="Goodbye_Main" /><action next="NR1130_Goodbye_SD_return" /></subdialog-state><subdialog-state id="NR1135_AutoPay_SD"><gotodialog next="AutoPay_Main" /><action next="NR1135_AutoPay_SD_Return" /></subdialog-state><custom-state id="NR1135_AutoPay_SD_Return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1140_ManageCards_SD"><gotodialog next="ManageCards_Main" /><action next="NR1140_ManageCards_SD_Return" /></subdialog-state><custom-state id="NR1140_ManageCards_SD_Return"><action next="getReturnLink()" /></custom-state><decision-state id="NR1201_NLUIntentRouting2_DS"><if cond="(GlobalVars.tag == 'change-phone_number')"><session-mapping key="mdnChangeVars.eventTypeGMT" expr="getEventTime()" /><session-mapping key="mdnChangeVars.status" expr="'incomplete'" /><session-mapping key="mdnChangeVars.eventType" expr="'mdn_change'" /><if cond="GlobalVars.GetAccountDetails"><session-mapping key="GlobalVars.paymentInfoRequired" value="true" type="Boolean" /><else><session-mapping key="GlobalVars.paymentInfoRequired" value="false" type="Boolean" /></else></if><session-mapping key="GlobalVars.billableTask" value="true" type="Boolean" /><session-mapping key="ActivationTable.ACTIVATION_TYPE" expr="'5'" /><session-mapping key="ActivationTable.ACTIVATION_STARTED" expr="getGMTTime()" /><session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'47'" /><session-mapping key="ActivationTable.ERROR_TEXT" expr="'FAILURE OR HANG UP'" /><action next="NR1205_DeviceHandling_SD" /><elseif cond="(GlobalVars.tag == 'add-line' || GlobalVars.tag == 'switch-phone' || GlobalVars.tag == 'change-phone_number' || GlobalVars.tag == 'activate-new_account')"><action next="NR1205_DeviceHandling_SD" /></elseif><elseif cond="(GlobalVars.tag == 'request-extension')"><session-mapping key="GlobalVars.extensionEntryPoint" expr="'NLU'" /><action next="NR1210_ApplyExtension_SD" /></elseif><elseif cond="(GlobalVars.tag == 'change-payment_date')"><action next="NR1215_BillCycleReset_SD" /></elseif><elseif cond="(GlobalVars.tag == 'troubleshooting-internet' || GlobalVars.tag == 'vague-troubleshooting_disambig' || GlobalVars.tag == 'troubleshooting-hotspot' )"><action next="NR1220_Troubleshooting_SD" /></elseif><else><action next="NR1301_NLUIntentRouting3_DS" /></else></if></decision-state><subdialog-state id="NR1205_DeviceHandling_SD"><gotodialog next="DeviceHandling_Main" /><action next="NR1205_DeviceHandling_SD_return" /></subdialog-state><custom-state id="NR1205_DeviceHandling_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1210_ApplyExtension_SD"><gotodialog next="ApplyExtension_Main" /><action next="NR1210_ApplyExtension_SD_return" /></subdialog-state><custom-state id="NR1210_ApplyExtension_SD_return"><if cond="(GlobalVars.tag == 'make-payment')"><action next="NR1110_MakePayment_SD" /></if><if cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'active')"><action next="getReturnLink()" /><else><action next="NR1130_Goodbye_SD" /></else></if></custom-state><subdialog-state id="NR1215_BillCycleReset_SD"><gotodialog next="BillCycleReset_Care" /><action next="NR1215_BillCycleReset_SD_return" /></subdialog-state><custom-state id="NR1215_BillCycleReset_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1220_Troubleshooting_SD"><gotodialog next="TechnicalSupport" /><action next="NR1220_Troubleshooting_SD_return" /></subdialog-state><custom-state id="NR1220_Troubleshooting_SD_return"><action next="getReturnLink()" /></custom-state><decision-state id="NR1301_NLUIntentRouting3_DS"><if cond="(GlobalVars.tag == 'open-account')"><action next="NR1305_GettingStarted_SD" /><elseif cond="(GlobalVars.tag == 'report-phone_broken' || GlobalVars.tag == 'report-phone_lost' || GlobalVars.tag == 'replace-phone')"><action next="NR1310_LostPhone_SD" /></elseif><elseif cond="(GlobalVars.tag == 'buy-data_topup' || GlobalVars.tag == 'hear-data_usage')"><action next="NR1315_CheckHighSpeedDataAvailable_SD" /></elseif><elseif cond="(GlobalVars.tag == 'hear-current_features')"><action next="NR1320_CurrentFeatures_SD" /></elseif><else><action next="NR1401_NLUIntentRouting4_DS" /></else></if></decision-state><subdialog-state id="NR1305_GettingStarted_SD"><gotodialog next="GettingStarted_Main" /><action next="NR1305_GettingStarted_SD_return" /></subdialog-state><custom-state id="NR1305_GettingStarted_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1310_LostPhone_SD"><gotodialog next="LostPhone_Main" /><action next="NR1310_LostPhone_SD_return" /></subdialog-state><custom-state id="NR1310_LostPhone_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1315_CheckHighSpeedDataAvailable_SD"><gotodialog next="HighSpeedDataAvailable_Main" /><action next="NR1315_CheckHighSpeedDataAvailable_SD_return" /></subdialog-state><custom-state id="NR1315_CheckHighSpeedDataAvailable_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1320_CurrentFeatures_SD"><gotodialog next="CurrentFeatures_Main" /><action next="NR1320_CurrentFeatures_SD_return" /></subdialog-state><custom-state id="NR1320_CurrentFeatures_SD_return"><action next="getReturnLink()" /></custom-state><decision-state id="NR1401_NLUIntentRouting4_DS"><if cond="(GlobalVars.tag == 'inquire-store_location')"><action next="NR1410_StoreLocator_SD" /><elseif cond="(GlobalVars.tag == 'faq-voicemail' || GlobalVars.tag == 'faq-payment_methods')"><action next="NR1415_FAQ_SD" /></elseif><elseif cond="(GlobalVars.tag == 'switch-lines_ANIMatch' || GlobalVars.tag == 'make-payment_other_account_ANIMatch')"><session-mapping key="GlobalVars.switchLinesEntryPoint" expr="'nlu'" /><action next="NR1420_SwitchLines_SD" /></elseif><elseif cond="GlobalVars.tag == 'reactivate-old_account'"><action next="NR1420_SwitchLines_SD" /></elseif><else><action next="NR1501_NLUIntentRouting5_DS" /></else></if></decision-state><subdialog-state id="NR1405_AccountBalance_SD"><gotodialog next="AccountBalance_Main" /><action next="NR1405_AccountBalance_SD_return" /></subdialog-state><custom-state id="NR1405_AccountBalance_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1410_StoreLocator_SD"><gotodialog next="StoreLocator_Main" /><action next="NR1410_StoreLocator_SD_return" /></subdialog-state><custom-state id="NR1410_StoreLocator_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1415_FAQ_SD"><gotodialog next="FAQ_Main" /><action next="NR1415_FAQ_SD_return" /></subdialog-state><custom-state id="NR1415_FAQ_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1420_SwitchLines_SD"><gotodialog next="SwitchLines_Main" /><action next="NR1420_SwitchLines_SD_return" /></subdialog-state><custom-state id="NR1420_SwitchLines_SD_return"><if cond="GlobalVars.tag == 'make-payment_other_account_ANIMatch'"><session-mapping key="GlobalVars.callType" expr="'make_pmt'" /><session-mapping key="GlobalVars.tag" expr="'make-payment'" /><session-mapping key="GlobalVars.cti_Intent" expr="getctiIntentByAppTag(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.tag,GlobalVars.callType)" /><action next="NR1001_CheckContext_DS" /><else><action next="getReturnLink()" /></else></if></custom-state><decision-state id="NR1501_NLUIntentRouting5_DS"><if cond="(GlobalVars.tag == 'payment-help' || GlobalVars.tag == 'vague-billing')"><action next="NR1505_PaymentHelp_SD" /><elseif cond="(GlobalVars.tag == 'upgrade-phone_purchase_NoAuth' || GlobalVars.tag == 'upgrade-phone_purchase_NeedAuth')"><action next="NR1510_UpgradePhone_SD" /></elseif><elseif cond="(GlobalVars.tag == 'inquire-balance_ANIMatch' || GlobalVars.tag == 'inquire-balance_NONANIMatch')"><action next="NR1515_BalanceBreakdown_SD" /></elseif><elseif cond="(GlobalVars.tag == 'inquire-select_protect') || (GlobalVars.tag == 'unlock-sim') || (GlobalVars.tag == 'cancel-service_transfer')"><action next="NR1530_Select_Protect_SD" /></elseif><elseif cond="(GlobalVars.tag == 'inquire-acp_flow')"><action next="NR1535_ACP_SD" /></elseif><elseif cond="(GlobalVars.tag == 'inquire-acp_transfer')"><gotodialog next="AP_ACP" /></elseif><elseif cond="(GlobalVars.tag == 'home-internet_active')"><action next="NR1520_GoToHomeInternet_SD" /></elseif></if></decision-state><subdialog-state id="NR1505_PaymentHelp_SD"><session-mapping key="extensionAllowed" value="GlobalVars.extensionAllowed != undefined ? GlobalVars.extensionAllowed : false" type="String" /><session-mapping key="eligibleForBillCycleReset" value="(GlobalVars.GetAccountDetails)?GlobalVars.GetAccountDetails.eligibleForBillCycleReset : false" type="String" /><session-mapping key="tag" value="GlobalVars.tag" type="String" /><gotodialog next="Payment_Help" /><action next="NR1505_PaymentHelp_SD_return" /></subdialog-state><custom-state id="NR1505_PaymentHelp_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1540_GoToTransfer_SD"><gotodialog next="CallTransfer_Main" /><action next="NR1540_GoToTransfer_SD_return" /></subdialog-state><subdialog-state id="NR1510_UpgradePhone_SD"><gotodialog next="UpgradePhone" /><action next="NR1510_UpgradePhone_SD_return" /></subdialog-state><custom-state id="NR1510_UpgradePhone_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1515_BalanceBreakdown_SD"><gotodialog next="BalanceBreakdown" /><action next="NR1515_BalanceBreakdown_SD_return" /></subdialog-state><custom-state id="NR1515_BalanceBreakdown_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1520_GoToHomeInternet_SD"><gotodialog next="MainMenu_Main" /><action next="NR1520_GoToHomeInternet_SD_return" /></subdialog-state><custom-state id="NR1520_GoToHomeInternet_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1525_GoToPromotionCenterLine_SD"><gotodialog next="PromotionCenterLine_Dialog" /><action next="NR1525_GoToPromotionCenterLine_SD_return" /></subdialog-state><custom-state id="NR1525_GoToPromotionCenterLine_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1530_Select_Protect_SD"><gotodialog next="SelectProtectDialog" /><action next="NR1530_Select_Protect_SD_return" /></subdialog-state><custom-state id="NR1530_Select_Protect_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1535_ACP_SD"><gotodialog next="AP_ACP" /><action next="NR1535_ACP_SD_return" /></subdialog-state><custom-state id="NR1535_ACP_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="NR1150_AccountPINReset_SD"><gotodialog next="AccountPINReset_Main" /><action next="NR1150_AccountPINReset_SD_return" /></subdialog-state><custom-state id="NR1150_AccountPINReset_SD_return"><if cond="GlobalVars.tag == undefined"><gotodialog next="NLUMainMenu_Start" /><else><action next="NR0001_CheckIntentMetrics_DS" /></else></if></custom-state></dialog><dialog id="SwitchLines_Security"><play-state id="SW1305_PlayPINInvalid_PP"><session-mapping key="switchLinesPINAttempts" value="GlobalVars.switchLinesPINAttempts" type="String" /><session-mapping key="acctLocked" value="GlobalVars.acctLocked" type="String" /><audio><if cond="acctLocked == true">
                  <prompt id="SW1305_out_04" /><elseif cond="switchLinesPINAttempts == 1">
    		<prompt id="SW1305_out_01" /></elseif><elseif cond="switchLinesPINAttempts == 2">
    		<prompt id="SW1305_out_02" /></elseif><else>
    		<prompt id="SW1305_out_03" /></else></if></audio><if cond="GlobalVars.acctLocked == true"><session-mapping key="GlobalVars.playedAcctLocked" value="true" type="Boolean" /><action next="SW1335_AuthFailTransfer_SD" /><else><action next="SW1310_CheckPINErrorCounter_DS" /></else></if></play-state><decision-state id="SW1310_CheckPINErrorCounter_DS"><if cond="GlobalVars.switchLinesPINAttempts == 1"><action next="SW1315_ReplayNewLineMDN_PP" /><else><gotodialog next="SwitchLines_Main" /></else></if></decision-state><play-state id="SW1315_ReplayNewLineMDN_PP"><session-mapping key="newLine_MDN" value="GlobalVars.newLine_MDN" type="String" /><audio><prompt id="SW1315_out_02" /><prompt type="custom" expr="newLine_MDN">  
        		<param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber" /><param name="intonation" value="f" /></prompt><prompt id="silence_250ms" /></audio><gotodialog next="SwitchLines_Main" /></play-state><decision-state id="SW1320_CheckEligibleForSecurityQuestion_DS"><session-mapping key="securityQuestionAnswerIsValid" value="(GlobalVars.GetAccountDetails)?GlobalVars.GetAccountDetails.securityQuestionAnswerIsValid : false" type="String" /><if cond="language == 'en-US' &amp;&amp; securityQuestionAnswerIsValid == true"><action next="SW1325_PlaySecurityQuestionTransition_PP" /><else><action next="SW1505_SwitchNotAllowedReason_DS" /></else></if></decision-state><play-state id="SW1325_PlaySecurityQuestionTransition_PP"><audio><prompt id="SW1325_out_01" /><prompt id="silence_750ms" /></audio><action next="SW1330_AskSecurityQuestion_SD" /></play-state><subdialog-state id="SW1330_AskSecurityQuestion_SD"><gotodialog next="AskSecurityQuestion_Main" /><action next="SW1330_AskSecurityQuestion_SD_return" /></subdialog-state><custom-state id="SW1330_AskSecurityQuestion_SD_return"><if cond="GlobalVars.loggedIn == true"><gotodialog next="SwitchLines_Main" /><else><action next="SW1335_AuthFailTransfer_SD" /></else></if></custom-state><subdialog-state id="SW1335_AuthFailTransfer_SD"><gotodialog next="CallTransfer_Main" /><action next="SW1335_AuthFailTransfer_SD" /></subdialog-state><decision-state id="SW1405_CheckNeedBalance_DS"><if cond="GlobalVars.accountNumber == GlobalVars.originalLine_accountNumber"><action next="getReturnLink()" /><else><action next="SW1410_CheckAccountStatus_DS" /></else></if></decision-state><decision-state id="SW1410_CheckAccountStatus_DS"><session-mapping key="accountStatus" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.accountStatus:''" type="String" /><if cond="accountStatus == 'active'"><if cond="GlobalVars.GetAccountDetails.parentDeviceType == 'INT'"><session-mapping key="GlobalVars.callType" expr="'home-internet_active'" /><session-mapping key="GlobalVars.enteredHintLine" value="true" type="Boolean" /><action next="SW1426_GoToHomeInternetMenu_SD" /><else><action next="SW1415_CheckOrigin_DS" /></else></if><else><action next="SW1416_CheckSuspendedRouting_DS" /></else></if></decision-state><subdialog-state id="SW1426_GoToHomeInternetMenu_SD"><gotodialog next="MainMenu_Main" /><action next="SW1426_GoToHomeInternetMenu_SD_return" /></subdialog-state><custom-state id="SW1426_GoToHomeInternetMenu_SD_return"><action next="getReturnLink()" /></custom-state><decision-state id="SW1415_CheckOrigin_DS"><if cond="GlobalVars.switchLinesEntryPoint == 'esn_swap'"><action next="getReturnLink()" /><else><action next="SW1420_PlayNewLineBalance_PP" /></else></if></decision-state><decision-state id="SW1416_CheckSuspendedRouting_DS"><if cond="GlobalVars.switchLinesEntryPoint == 'suspended'"><action next="getReturnLink()" /><else><action next="SW1425_GoToSuspendedHandling_SD" /></else></if></decision-state><play-state id="SW1420_PlayNewLineBalance_PP"><session-mapping key="balance" value="GlobalVars.GetAccountDetails.balance" type="String" /><session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String" /><session-mapping key="hasAutopay" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String" /><session-mapping key="dayEveryMonthX" value="" type="String" /><session-mapping key="remainingDays" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String" /><session-mapping key="payDate" value="GlobalVars.GetAccountDetails.payDate" type="String" /><audio><if cond="(balance == 0 &amp;&amp; dueImmediatelyAmount == 0)">
			<prompt id="SW1420_out_01" cond="(hasAutopay == true)" /><prompt id="SW1420_out_02" cond="(hasAutopay != true)" /><prompt type="custom" expr="dayEveryMonthX" scope="request">
		      	<param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.VXMLDynamicPromptNameRendererImpl" /><param name="tts" value="dayEveryMonthX" scope="request" /></prompt><elseif cond="(hasAutopay)">
			<prompt id="SW1420_out_03" cond="(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)" /><prompt id="SW1420_out_04" cond="!(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)" /><prompt type="currency" expr="balance">
				<param value="false" name="playZeroCents" /></prompt><prompt id="SW1420_out_05" cond="(remainingDays == 0)" /><prompt id="SW1420_out_06" cond="(remainingDays == 1)" /><prompt id="SW1420_out_07" cond="(remainingDays == 2)" /><prompt id="SW1420_out_08" cond="(remainingDays &gt; 2)" /><prompt type="date" expr="payDate" cond="(remainingDays &gt; 2)">
				<param name="dateFormat" value="MMM dd, yyyy" /></prompt></elseif><else>
			<prompt id="SW1420_out_10" cond="(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)" /><prompt id="SW1420_out_11" cond="!(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)" /><prompt type="currency" expr="balance">
				<param value="false" name="playZeroCents" /></prompt><prompt id="SW1420_out_12" cond="(remainingDays == 0)" /><prompt id="SW1420_out_13" cond="(remainingDays == 1)" /><prompt id="SW1420_out_14" cond="(remainingDays == 2)" /><prompt id="SW1420_out_15" cond="(remainingDays &gt; 2)" /><prompt type="date" expr="payDate" cond="(remainingDays &gt; 2)">
				<param name="dateFormat" value="MMM dd, yyyy" /></prompt></else></if><if cond="(dueImmediatelyAmount &gt; 0)">
			<prompt id="SW1420_out_17" /><prompt type="currency" expr="dueImmediatelyAmount">
				<param value="false" name="playZeroCents" /></prompt></if><prompt id="silence_500ms" /></audio><action next="getReturnLink()" /></play-state><subdialog-state id="SW1425_GoToSuspendedHandling_SD"><gotodialog next="SuspendedHandling_Main" /></subdialog-state><decision-state id="SW1505_SwitchNotAllowedReason_DS"><if cond="((GlobalVars.aniMatch == false || GlobalVars.aniMatch == 'false') || (GlobalVars.GetBCSParameters.care_enable_switch_lines == false || GlobalVars.GetBCSParameters.care_enable_switch_lines == 'false'))"><action next="SW1510_PlaySwitchDeniedBCSOff_PP" /><elseif cond="GlobalVars.switchLinesSuccess == true"><action next="SW1515_PlaySwitchDeniedSecondRequest_PP" /></elseif><else><action next="SW1520_PlaySwitchedDeniedLoginFail_PP" /></else></if></decision-state><play-state id="SW1510_PlaySwitchDeniedBCSOff_PP"><audio><prompt id="SW1510_out_01" /></audio><action next="SW1525_Goodbye_SD" /></play-state><play-state id="SW1515_PlaySwitchDeniedSecondRequest_PP"><audio><prompt id="SW1515_out_01" /><prompt id="silence_250ms" /><prompt id="SW1515_out_02" /></audio><action next="SW1525_Goodbye_SD" /></play-state><play-state id="SW1520_PlaySwitchedDeniedLoginFail_PP"><audio><prompt id="SW1520_out_01" /></audio><action next="SW1525_Goodbye_SD" /></play-state><subdialog-state id="SW1525_Goodbye_SD"><gotodialog next="Goodbye_Main" /></subdialog-state></dialog><dialog id="TEST_ANIEntry"><custom-state id="Initial_Form"><action next="TEST_ANIEntry_DM" /></custom-state><dm-state id="TEST_ANIEntry_DM"><success><action label="228"><action next="TEST_ANIEntry_DM" /></action><action label="228Part"><action next="TEST_ANIEntry_DM" /></action><action label="99"><action next="TEST_ANIEntry_DM" /></action><action label="DSG"><return namelist="dnis ani" /></action><action label="RCC"><return namelist="dnis ani" /></action><action label="NRH"><return namelist="dnis ani" /></action><action label="611"><action next="TEST_ANIEntry_DM" /></action><action label="888"><action next="TEST_ANIEntry_DM" /></action><action label="289"><action next="TEST_ANIEntry_DM" /></action><action label="FiServ"><action next="TEST_ANIEntry_DM" /></action><action label="TestCallMin"><action next="TEST_ANIEntry_DM" /></action><action label="490"><return namelist="dnis ani" /></action><action label="491"><return namelist="dnis ani" /></action><action label="611_228_Entry_English"><action next="TEST_ANIEntry_DM" /></action><action label="611_228_Entry_Spanish"><action next="TEST_ANIEntry_DM" /></action><action label="888_data_collection"><return namelist="dnis ani" /></action><action label="Star_99_English"><action next="TEST_ANIEntry_DM" /></action><action label="Star_99_Spanish"><action next="TEST_ANIEntry_DM" /></action><action label="611_GSM"><action next="TEST_ANIEntry_DM" /></action><action label="611_Data_Collection"><action next="TEST_ANIEntry_DM" /></action><action label="OMNI"><action next="TEST_ANIEntry_DM" /></action><action label="WNP"><action next="TEST_ANIEntry_DM" /></action><action label="PWR"><return namelist="dnis ani" /></action><action label="PC"><return namelist="dnis ani" /></action><action label="VR"><action next="TEST_ANIEntry_DM" /></action><action label="default"><assign name="ani" expr="TEST_ANIEntry_DM.returnvalue" /><assign name="GlobalVars.trn" expr="TEST_ANIEntry_DM.returnvalue" /><return namelist="dnis ani" /></action></success><event name="event.nuance.dialog.ndm"><action next="TEST_ANIEntry_DM" /></event></dm-state></dialog><dialog id="VoiceMailPin_Main"><data-access-state id="VP1015_SubmitPasswordReset_DB"><session-mapping key="min" value="GlobalVars.GetAccountDetails.mdn" type="String" /><session-mapping key="sessionId" value="appsessionID" type="String" /><property name="fetchaudiominimum" value="1.5s" /><property name="fetchaudiodelay" value="0" /><data-access id="ResetVoiceMailPIN" classname="com.nuance.metro.dataaccess.ResetVoiceMailPIN">
		<fetchAudio>fetch_audio_30s</fetchAudio>
		<inputs>
			<input-variable name="min" />
			<input-variable name="sessionId" />
		</inputs>
		<outputs>
			<output-variable name="status" />
		</outputs>
	</data-access>
    <if cond="ResetVoiceMailPIN.status == 'Success'"><action next="VP1020_PlayResetPasswordSuccess_PP" /><elseif cond="ResetVoiceMailPIN.status == 'NOVOICEMAIL'"><action next="VP1030_NoVoicemail_PP" /></elseif><else><action next="VP1025_PlayResetPasswordFailure_PP" /></else></if></data-access-state><play-state id="VP1020_PlayResetPasswordSuccess_PP"><audio><prompt id="VP1020_ini_01" /><prompt id="VP1020_ini_02" /><prompt id="silence_1000ms" /></audio><action next="VP1035_GoToAnythingElse_SD" /></play-state><play-state id="VP1025_PlayResetPasswordFailure_PP"><audio><prompt id="VP1025_ini_01" /><prompt id="silence_1000ms" /></audio><action next="VP1040_GoToCallTransfer_SD" /></play-state><play-state id="VP1030_NoVoicemail_PP"><audio><prompt id="VP1030_ini_01" /><prompt id="silence_1000ms" /></audio><action next="VP1035_GoToAnythingElse_SD" /></play-state><subdialog-state id="VP1035_GoToAnythingElse_SD"><gotodialog next="AnythingElse_Main" /><action next="VP1035_GoToAnythingElse_SD" /></subdialog-state><custom-state id="VP1035_GoToAnythingElse_SD_return"><gotodialog next="Goodbye_Main" /></custom-state><subdialog-state id="VP1040_GoToCallTransfer_SD"><gotodialog next="CallTransfer_Main" /><action next="VP1040_GoToCallTransfer_SD_return" /></subdialog-state><custom-state id="VP1040_GoToCallTransfer_SD_return"><gotodialog next="Goodbye_Main" /></custom-state></dialog><dialog id="Voicestore_Routing"><subdialog-state id="VS1315_GoToCallTransfer_SD"><gotodialog next="CallTransfer_Main" /><action next="VS1315_GoToCallTransfer_SD_return" /></subdialog-state><custom-state id="VS1315_GoToCallTransfer_SD_return" /><subdialog-state id="VS1320_GoToAnythingElse_SD"><gotodialog next="AnythingElse_Main" /><action next="VS1320_GoToAnythingElse_SD_return" /></subdialog-state><custom-state id="VS1320_GoToAnythingElse_SD_return" /><subdialog-state id="VS1330_GoToMakePayments_SD"><session-mapping key="GlobalVars.IsAddingFeature" value="true" type="Boolean" /><session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careVoiceStore'" /><session-mapping key="GlobalVars.FromCareVoiceStore" value="true" type="Boolean" /><gotodialog next="MakePayment_Main" /><action next="VS1330_GoToMakePayments_SD_return" /></subdialog-state><custom-state id="VS1330_GoToMakePayments_SD_return"><action next="getReturnLink()" /></custom-state><subdialog-state id="VS1340_GoToGoodbye_SD"><gotodialog next="Goodbye_Main" /><action next="VS1340_GoToGoodbye_SD_return" /></subdialog-state><custom-state id="VS1340_GoToGoodbye_SD_return" /></dialog></states-library>