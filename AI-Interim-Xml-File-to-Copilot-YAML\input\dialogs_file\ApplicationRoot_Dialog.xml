<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="ApplicationRoot_Dialog">
    <session-mapping key="errorCount" value="0" type="String"/>
    <session-mapping key="dataaccessErrorCount" value="0" type="String"/>
    <session-mapping key="badFetchErrorCount" value="0" type="String"/>
    <session-mapping key="id1" value="" type="String"/>
    <session-mapping key="id2" value="" type="String"/>
    <session-mapping key="id3" value="" type="String"/>
    <session-mapping key="language" value="en-US" type="String"/>
    <session-mapping key="library" value="default" type="String"/>
    <session-mapping key="version" value="1.0" type="String"/>
    <session-mapping key="mdnChangeVars" value="new Object()" type="String"/>
    <session-mapping key="paymentVars" value="new Object()" type="String"/>
    <session-mapping key="esnChangeVars" value="new Object()" type="String"/>
    <session-mapping key="ctiAttachVars" value="new Object()" type="String"/>
    <session-mapping key="ctiTransferVars" value="new Object()" type="String"/>
    <session-mapping key="newPlanVars" value="new Object()" type="String"/>
    <session-mapping key="addFeatureVars" value="new Object()" type="String"/>
    <session-mapping key="GlobalVars" value="new Object()" type="String"/>
    <session-mapping key="NumberPortInVars" value="new Object()" type="String"/>
    <session-mapping key="GetSecurityCodeVars" value="new Object()" type="String"/>
    <session-mapping key="activationVars" value="new Object()" type="String"/>
    <session-mapping key="ActivationTable" value="resetActivationsTable()" type="String"/>
    <session-mapping key="PaymentTable" value="resetActivationsTable()" type="String"/>
    <session-mapping key="TransferTag" value="Customer_Support_English" type="String"/>
    <session-mapping key="disambiguationForm" value="" type="String"/>
    <session-mapping key="nluOperatorCount" value="0" type="String"/>
    </dialog>
  