import xml.etree.ElementTree as ET
import os
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import time
import threading
from llm_response import convertXmltoYaml
import logging
from util import read_filenames_in_folder, delete_existing_files, log_failed_file, remove_null_from_action
from indentation import topic_yaml_indentation
import llm_response
import glob
import yaml

# Configure logging
logging.basicConfig(filename='dm_agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')

# File lock for shared resources
file_lock = threading.Lock()

def dm_state_agent(content, prompt_path, topic_template, process_id, file_name):
    """Processes dm-state XML elements with process isolation"""
    logging.info(f'step4_dm_only:dm_state_agent (PID: {process_id})')
    logging.info(f'Input: {content}')
    
    # Create process-specific output file path
    timestamp = int(time.time() * 1000)  # Millisecond precision
    unique_output_file = f"output/unformatted_topic_yaml/output_{timestamp}_{process_id}_{file_name}.yml"
    
    try:
        # Call convertXmltoYaml with process-specific output
        convertXmltoYaml(content, prompt_path, topic_template, unique_output_file)
        
        # Validate YAML syntax
        if os.path.exists(unique_output_file):
            try:
                with open(unique_output_file, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)  # This will raise an exception if YAML is invalid
                logging.info(f"Valid YAML created: {unique_output_file}")
                return unique_output_file
            except yaml.YAMLError as e:
                logging.error(f"Invalid YAML generated: {e}")
                # Try to clean up malformed file
                try:
                    os.remove(unique_output_file)
                except:
                    pass
                raise Exception(f"Invalid YAML generated: {e}")
        else:
            raise Exception("No output file was created")
            
    except Exception as e:
        logging.error(f'Error in convertXmltoYaml: {e}. Retrying once...')
        try:
            # Second attempt with different timestamp
            timestamp = int(time.time() * 1000)
            unique_output_file = f"output/unformatted_topic_yaml/output_{timestamp}_{process_id}_{file_name}_retry.yml"
            convertXmltoYaml(content, prompt_path, topic_template, unique_output_file)
            
            # Validate retry attempt
            if os.path.exists(unique_output_file):
                with open(unique_output_file, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)
                return unique_output_file
            else:
                raise Exception("Retry failed - no output file created")
                
        except Exception as e2:
            logging.error(f'Second attempt failed: {e2}. Aborting...')
            raise

def process_dialog_file_dm_only(file_path, topic_template, process_id, file_name):
    """
    Processes XML dialog file but only looks for dm-state elements with process isolation
    """
    logging.info(f'step4_dm_only:process_dialog_file_dm_only (PID: {process_id})')
    tree = ET.parse(file_path)
    root = tree.getroot()
    llm_response.is_first_time = True
    
    dm_states_found = 0
    prompt_path = 'prompts/few_shot_CPS_DM.md'
    output_files = []
    
    # Debug: Log root element and all child element tags
    logging.info(f"Root element: {root.tag}, Total child elements: {len(root)}")
    all_tags = [element.tag for element in root]
    logging.info(f"All element tags in {file_name}: {set(all_tags)}")
    
    # Check if any elements contain 'dm' in their name (case insensitive)
    dm_related_tags = [tag for tag in all_tags if 'dm' in tag.lower()]
    if dm_related_tags:
        logging.info(f"DM-related tags found in {file_name}: {dm_related_tags}")
    
    for element in root:
        tag_name = element.tag
        
        # Only process dm-state elements
        if tag_name == "dm-state":
            content = ET.tostring(element, encoding='unicode')
            try:
                output_file = dm_state_agent(content, prompt_path, topic_template, process_id, file_name)
                if output_file:
                    output_files.append(output_file)
                dm_states_found += 1
            except Exception as e:
                logging.error(f"Failed to process dm-state in {file_name}: {e}")
                # Continue processing other dm-states instead of failing completely
                continue
        else:
            # Log first few non-DM elements for debugging
            if dm_states_found == 0 and len(output_files) == 0:
                logging.debug(f"Skipping non-DM element: {tag_name}")
    
    logging.info(f"Found and processed {dm_states_found} dm-state elements")
    
    # If no dm-states found, let's also check for variations
    if dm_states_found == 0:
        # Try case-insensitive search
        for element in root:
            if element.tag.lower() == "dm-state":
                logging.warning(f"Found case variation: {element.tag} in {file_name}")
        
        # Check if there are any nested dm-state elements
        for element in root:
            nested_dm_states = element.findall(".//dm-state")
            if nested_dm_states:
                logging.warning(f"Found {len(nested_dm_states)} nested dm-state elements in {file_name}")
    
    return dm_states_found, output_files

def find_timestamped_file(file_base_name, folder_path, process_id=None, max_wait_time=30):
    """Find the timestamped file that matches the base name pattern with retry logic"""
    import time
    
    # Try multiple patterns to find the file
    patterns = [
        f"*{file_base_name}.yml",
        f"output_*{file_base_name}.yml",
        f"*{process_id}*{file_base_name}.yml" if process_id else None
    ]
    
    # Remove None patterns
    patterns = [p for p in patterns if p is not None]
    
    start_time = time.time()
    while time.time() - start_time < max_wait_time:
        for pattern in patterns:
            pattern_path = os.path.join(folder_path, pattern)
            matching_files = glob.glob(pattern_path)
            if matching_files:
                # Return the most recent file
                latest_file = max(matching_files, key=os.path.getctime)
                logging.info(f"Found timestamped file: {latest_file} for pattern: {pattern}")
                return latest_file
        
        # Wait a bit before retrying
        time.sleep(0.5)
    
    # If no file found, log all files in directory for debugging
    all_files = os.listdir(folder_path) if os.path.exists(folder_path) else []
    logging.warning(f"Could not find timestamped file for {file_base_name}. Files in {folder_path}: {all_files}")
    return None

def safe_file_operation(operation, *args, **kwargs):
    """Wrapper for safe file operations with retry logic"""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            return operation(*args, **kwargs)
        except (FileNotFoundError, PermissionError, OSError) as e:
            if attempt == max_retries - 1:
                logging.error(f"File operation failed after {max_retries} attempts: {e}")
                raise
            time.sleep(0.1 * (attempt + 1))  # Exponential backoff

def process_single_dialog_file_worker(args):
    """Worker function that processes a single dialog file - designed for multiprocessing"""
    file, config = args
    process_id = os.getpid()
    
    # Extract config
    input_dialog_folder = config['input_dialog_folder']
    output_yaml_path = config['output_yaml_path']
    final_topic_yaml = config['final_topic_yaml']
    topic_template_path = config['topic_template_path']
    
    try:
        file_path = os.path.join(input_dialog_folder, file + '.xml')
        topic_template = topic_template_path + file + '.yml'
        
        logging.info(f'Processing dialog file for DM states only: {file} (PID: {process_id})')
        
        # Process the file with process isolation
        dm_count, output_files = process_dialog_file_dm_only(file_path, topic_template, process_id, file)
        
        if dm_count > 0 and output_files:
            # Ensure output directory exists
            os.makedirs(final_topic_yaml, exist_ok=True)
            
            # Use the first valid output file (or combine if multiple)
            timestamped_file = output_files[0]
            
            if os.path.exists(timestamped_file):
                logging.info(f'Using process-isolated file: {timestamped_file}')
                
                final_output_file = os.path.join(final_topic_yaml, file + '_topic.yml')
                
                # Use safe file operations
                try:
                    safe_file_operation(topic_yaml_indentation, timestamped_file, final_output_file)
                    safe_file_operation(remove_null_from_action, final_output_file)
                    
                    logging.info(f'Successfully processed dialog file: {file} with {dm_count} DM states')
                    return {'success': True, 'file': file, 'dm_count': dm_count, 'message': 'Success'}
                    
                except Exception as e:
                    logging.error(f'Error in post-processing for {file}: {e}')
                    return {'success': False, 'file': file, 'dm_count': dm_count, 'message': f'Post-processing error: {str(e)}'}
            else:
                logging.warning(f'Output file not found: {timestamped_file}')
                return {'success': False, 'file': file, 'dm_count': dm_count, 'message': 'Output file not accessible'}
        else:
            logging.info(f'No DM states found in dialog file: {file}')
            return {'success': True, 'file': file, 'dm_count': 0, 'message': 'No DM states found'}
        
    except Exception as e:
        logging.error(f'Failed to process dialog-file {file}: {e}')
        log_failed_file(file)
        return {'success': False, 'file': file, 'dm_count': 0, 'message': f'Error: {str(e)}'}

def process_files_multiprocessing(input_dialog_files, config, max_workers=None, timeout_per_file=300):
    """Process multiple files using multiprocessing"""
    
    if max_workers is None:
        max_workers = min(len(input_dialog_files), mp.cpu_count() - 1, 4)  # Leave one core free, max 4 workers
    
    print(f'Starting multiprocessing with {max_workers} workers')
    logging.info(f'Starting multiprocessing with {max_workers} workers, timeout: {timeout_per_file}s per file')
    
    # Ensure output directories exist
    for folder in [config['output_yaml_path'], config['final_topic_yaml']]:
        os.makedirs(folder, exist_ok=True)
    
    # Prepare arguments for worker processes
    worker_args = [(file, config) for file in input_dialog_files]
    
    results = {'successful': 0, 'failed': 0, 'total_dm_states': 0}
    completed_files = []
    failed_files = []
    
    start_time = time.time()
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_file = {
            executor.submit(process_single_dialog_file_worker, args): args[0] 
            for args in worker_args
        }
        
        # Process completed tasks
        try:
            for future in as_completed(future_to_file, timeout=timeout_per_file * len(input_dialog_files)):
                file = future_to_file[future]
                try:
                    result = future.result(timeout=timeout_per_file)
                    
                    if result['success']:
                        results['successful'] += 1
                        results['total_dm_states'] += result['dm_count']
                        completed_files.append(file)
                        print(f'✓ Completed: {file} ({result["dm_count"]} DM states) - {result["message"]}')
                    else:
                        results['failed'] += 1
                        failed_files.append(file)
                        print(f'✗ Failed: {file} - {result["message"]}')
                        
                except Exception as e:
                    results['failed'] += 1
                    failed_files.append(file)
                    logging.error(f'Process failed for {file}: {e}')
                    print(f'✗ Process failed: {file} - {str(e)}')
        
        except Exception as e:
            logging.error(f'Error in process completion handling: {e}')
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    # Log summary
    logging.info(f'Multiprocessing completed in {processing_time:.2f}s')
    logging.info(f'Results: {results["successful"]} successful, {results["failed"]} failed')
    logging.info(f'Total DM states processed: {results["total_dm_states"]}')
    
    if failed_files:
        logging.error(f'Failed files: {failed_files}')
    
    return results, completed_files, failed_files, processing_time

# Configuration
config = {
    'input_dialog_folder': 'input/dialogs_file/',
    'output_yaml_path': 'output/unformatted_topic_yaml/',
    'final_topic_yaml': 'output/topic_yaml/',
    'topic_template_path': 'YAML-template/topic-yaml/'
}

if __name__ == '__main__':
    # Delete existing files
    delete_existing_files(config['output_yaml_path'])
    delete_existing_files(config['final_topic_yaml'])

    # Process dialog files
    input_dialog_files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(config['input_dialog_folder'])]

    print(f'Input dialog folder: {config["input_dialog_folder"]}')
    print(f'Found {len(input_dialog_files)} dialog files: {input_dialog_files}')
    logging.info(f'Found {len(input_dialog_files)} dialog files: {input_dialog_files}')

    # Run multiprocessing
    results, completed_files, failed_files, processing_time = process_files_multiprocessing(
        input_dialog_files, 
        config, 
        max_workers=4,  # Adjust based on your system
        timeout_per_file=300  # 5 minutes per file
    )

    # Final summary
    print(f'\n=== PROCESSING COMPLETE ===')
    print(f'Time taken: {processing_time:.2f} seconds')
    print(f'Successful: {results["successful"]}')
    print(f'Failed: {results["failed"]}')
    print(f'Total DM states processed: {results["total_dm_states"]}')
    
    if failed_files:
        print(f'Failed files: {failed_files}')

    logging.info(f'DM-only multiprocessing completed. Success: {results["successful"]}, Failed: {results["failed"]}, Time: {processing_time:.2f}s')
