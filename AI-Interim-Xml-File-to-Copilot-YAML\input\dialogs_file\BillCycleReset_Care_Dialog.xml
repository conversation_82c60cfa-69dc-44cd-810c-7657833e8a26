<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="BillCycleReset_Care_Dialog">
    <decision-state id="BR1001_CheckContext_DS">
      <if cond="accountStatus == 'active'">
        <action next="BR1002_ConfirmTransferYN_DM"/>
        <elseif cond="GlobalVars.accountFutureRequestInd == true">
          <action next="BR1035_CallTransfer_SD"/>
        </elseif>
        <elseif cond="(GlobalVars.tag == 'change-payment_date' &amp;&amp; (GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended' &amp;&amp; GlobalVars.GetAccountDetails.eligibleForBillCycleReset != true))">
          <action next="BR1003_PlayNotEligible_PP"/>
        </elseif>
        <elseif cond="(GlobalVars.tag == 'change-payment_date' &amp;&amp; GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'active')">
          <action next="BR1035_CallTransfer_SD"/>
        </elseif>
        <else>
          <session-mapping key="GlobalVars.heardBCRTerms" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.acceptedBCR" value="false" type="Boolean"/>
          <action next="BR1005_CheckOriginForPrompting_JDA"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="BR1002_ConfirmTransferYN_DM" type="YSNO">
      <session-mapping key="audioMessageKey" value="care_bcr_charge_audio" type="String"/>
      <success>
        <action label="true">
          <audio>
            <prompt id="BR1002_out_01">
              <prompt-segments>
                <audiofile text="Great!" src="BR1002_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="BR1035_CallTransfer_SD"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="BR1002_out_02">
              <prompt-segments>
                <audiofile text="No problem " src="BR1002_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="BR1030_RepeatCurrentDueDate_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BR1002_ini_01">
                <prompt-segments>
                  <audiofile text="To do that, I'll transfer you to an agent They'll ask you to pick a new monthly due date, and they'll explain how this will affect your amount due They'll also charge you a one-time fee of " src="BR1002_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage_ES1505"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1002_ini_03">
                <prompt-segments>
                  <audiofile text="You'll need to make the full payment right away to avoid your service getting suspended" src="BR1002_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1002_ini_02">
                <prompt-segments>
                  <audiofile text="Do you want to go ahead and talk to someone? " src="BR1002_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="BR1002_ConfirmTransferYN_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BR1002_ini_01">
                <prompt-segments>
                  <audiofile text="To do that, I'll transfer you to an agent They'll ask you to pick a new monthly due date, and they'll explain how this will affect your amount due They'll also charge you a one-time fee of " src="BR1002_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage_ES1505"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1002_ini_03">
                <prompt-segments>
                  <audiofile text="You'll need to make the full payment right away to avoid your service getting suspended" src="BR1002_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1002_ini_02">
                <prompt-segments>
                  <audiofile text="Do you want to go ahead and talk to someone? " src="BR1002_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BR1002_nm1_01">
                <prompt-segments>
                  <audiofile text="To change your due date, our agents will need to charge you a fee of " src="BR1002_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage_ES1505"/>
              </prompt>
              <prompt id="BR1002_nm1_02">
                <prompt-segments>
                  <audiofile text="Your payment will be due right away Should I take you to someone now?" src="BR1002_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BR1002_nm2_01">
                <prompt-segments>
                  <audiofile text="Sorry, I *still* didn't get that" src="BR1002_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1002_nm2_01">
                <prompt-segments>
                  <audiofile text="Sorry, I *still* didn't get that" src="BR1002_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1002_nm1_01">
                <prompt-segments>
                  <audiofile text="To change your due date, our agents will need to charge you a fee of " src="BR1002_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage_ES1505"/>
              </prompt>
              <prompt id="BR1002_nm1_02">
                <prompt-segments>
                  <audiofile text="Your payment will be due right away Should I take you to someone now?" src="BR1002_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="BR1002_ConfirmTransferYN_DM_nomatch_2"/>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="BR1002_ConfirmTransferYN_DM_nomatch_3"/>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BR1002_ini_01">
                <prompt-segments>
                  <audiofile text="To do that, I'll transfer you to an agent They'll ask you to pick a new monthly due date, and they'll explain how this will affect your amount due They'll also charge you a one-time fee of " src="BR1002_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage_ES1505"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1002_ini_03">
                <prompt-segments>
                  <audiofile text="You'll need to make the full payment right away to avoid your service getting suspended" src="BR1002_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1002_ini_02">
                <prompt-segments>
                  <audiofile text="Do you want to go ahead and talk to someone? " src="BR1002_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="BR1002_ConfirmTransferYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="BR1002_ConfirmTransferYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="BR1003_PlayNotEligible_PP">
      <audio>
        <prompt id="BR1003_out_01">
          <prompt-segments>
            <audiofile text="I'm sorry, it doesn't look like we can change your due date Your account will become eligible when it's been suspended for at least 7 days Our agents won't be able to change your due date either So please call us back in a few days" src="BR1003_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <decision-state id="BR1005_CheckOriginForPrompting_DS">
      <if cond="GlobalVars.callType == 'make_pmt'">
        <action next="BR1015_PlayTermsOfferTransition_PP"/>
        <else>
          <action next="BR1010_PlayTermsCallerChoseBCR_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="BR1010_PlayTermsCallerChoseBCR_PP">
      <session-mapping key="currentDate" value="" type="String"/>
      <session-mapping key="dayEveryMonthX" value="" type="String"/>
      <session-mapping key="loggedIn" value="GlobalVars.loggedIn" type="String"/>
      <session-mapping key="audioMessageKey" value="care_bcr_charge_audio" type="String"/>
      <audio>
        <if cond="currentDate == 31">
          <prompt id="BR1010_out_01">
            <prompt-segments>
              <audiofile text="I can change your due date to today It s the 31st, so your payments will be due on the last day of every month" src="BR1010_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="BR1010_out_02">
              <prompt-segments>
                <audiofile text="I can change your due date to today, so your payments will be due" src="BR1010_out_02.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="custom" expr="dayEveryMonthX" scope="request">
              <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.VXMLDynamicPromptNameRendererImpl"/>
              <param name="tts" value="dayEveryMonthX" scope="request"/>
            </prompt>
          </else>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BR1010_out_03">
          <prompt-segments>
            <audiofile text="That way, you won t have to pay for the days your account was suspended this month " src="BR1010_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BR1010_out_06">
          <prompt-segments>
            <audiofile text="Since your account is suspended, our agents and I can ONLY change your due date to today If you'd like a different date, you'll need to call back on that day Or, make a payment to resume your service, then call us back when your account is active to make the change  " src="BR1010_out_06.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BR1010_out_04">
          <prompt-segments>
            <audiofile text="To make the change, there s a one-time charge of" src="BR1010_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="audioMessageKey" bargein="false">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayAudioMessage"/>
        </prompt>
        <if cond="loggedIn != true">
          <prompt id="BR1010_out_05">
            <prompt-segments>
              <audiofile text="And I ll need your account PIN to do it" src="BR1010_out_05.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BR1025_AcceptBCRTermsYN_DM"/>
    </play-state>

    <play-state id="BR1015_PlayTermsOfferTransition_PP">
      <audio>
        <prompt id="BR1015_out_01">
          <prompt-segments>
            <audiofile text="Actually, if you want" src="BR1015_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BR1020_PlayTermsOfferToCaller_PP"/>
    </play-state>

    <play-state id="BR1020_PlayTermsOfferToCaller_PP">
      <session-mapping key="currentDate" value="" type="String"/>
      <session-mapping key="dayEveryMonthX" value="" type="String"/>
      <session-mapping key="loggedIn" value="GlobalVars.loggedIn" type="String"/>
      <session-mapping key="audioMessageKey" value="care_bcr_charge_audio" type="String"/>
      <audio>
        <if cond="currentDate == 31">
          <prompt id="BR1020_out_01">
            <prompt-segments>
              <audiofile text="I can reset your due date to today  It s the 31st, so your payments would be due on the last day of every month " src="BR1020_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="BR1020_out_02">
              <prompt-segments>
                <audiofile text="I can reset your due date to today, so your payments would be due " src="BR1020_out_02.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="custom" expr="dayEveryMonthX" scope="request">
              <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.VXMLDynamicPromptNameRendererImpl"/>
              <param name="tts" value="dayEveryMonthX" scope="request"/>
            </prompt>
          </else>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BR1020_out_06">
          <prompt-segments>
            <audiofile text="It costs" src="BR1020_out_06.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="audioMessageKey" bargein="false">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayAudioMessage"/>
        </prompt>
        <prompt id="BR1020_out_03">
          <prompt-segments>
            <audiofile text="But it ll *save* you money, since you won t have to pay for the days your account was suspended this month" src="BR1020_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="loggedIn == true">
          <prompt id="BR1020_out_04">
            <prompt-segments>
              <audiofile text="If you go for it, I ll make the change and we ll continue with your payment" src="BR1020_out_04.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="BR1020_out_05">
              <prompt-segments>
                <audiofile text="I ll just need your account PIN to make the change, then we ll continue with your payment" src="BR1020_out_05.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BR1025_AcceptBCRTermsYN_DM"/>
    </play-state>

    <dm-state id="BR1025_AcceptBCRTermsYN_DM" type="YSNO">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="collection_maxnoinputs" value="GlobalVars.callType == 'make_pmt' ? 1 : 3" type="String"/>
      <session-mapping key="collection_maxnomatches" value="GlobalVars.callType == 'make_pmt' ? 2 : 3" type="String"/>
      <session-mapping key="audioMessageKey" value="care_bcr_charge_audio" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.acceptedBCR" value="true" type="Boolean"/>
          <audio>
            <prompt id="BR1025_out_01">
              <prompt-segments>
                <audiofile text="Great" src="BR1025_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="GlobalVars.loggedIn != true">
            <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          </if>
          <action next="BR1101_CheckNeedPIN_JDA"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="BR1025_out_02">
              <prompt-segments>
                <audiofile text="No problem" src="BR1025_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="BR1030_RepeatCurrentDueDate_PP"/>
        </action>
        <action label="operator"/>
        <action label="repeat" next="BR1010_PlayTermsCallerChoseBCR_PP"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="callType == 'make_pmt'">
                <prompt id="BR1025_ini_01">
                  <prompt-segments>
                    <audiofile text="Would you like to do that?" src="BR1025_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BR1025_ini_02">
                    <prompt-segments>
                      <audiofile text="Are you ready to change your due date?" src="BR1025_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BR1025_nm1_02">
                <prompt-segments>
                  <audiofile text="To change your due date to today would be" src="BR1025_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="BR1025_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to do that? To hear all the terms again, say repeat that" src="BR1025_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <if cond="callType == 'make_pmt'">
                <prompt id="BR1025_ni2_01">
                  <prompt-segments>
                    <audiofile text="I didn t hear you Let s go back to your payment" src="BR1025_ni2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BR1025_ni2_02">
                    <prompt-segments>
                      <audiofile text="I didn t hear you" src="BR1025_ni2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="BR1025_nm2_01">
                <prompt-segments>
                  <audiofile text="To change your due date to today would be" src="BR1025_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="BR1025_nm2_02">
                <prompt-segments>
                  <audiofile text="To do that now, say yes or press 1 Otherwise, say no or press 2, or to hear all the terms again sat repeat or press 3" src="BR1025_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1025_nm2_01">
                <prompt-segments>
                  <audiofile text="To change your due date to today would be" src="BR1025_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="BR1025_nm2_02">
                <prompt-segments>
                  <audiofile text="To do that now, say yes or press 1 Otherwise, say no or press 2, or to hear all the terms again sat repeat or press 3" src="BR1025_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1025_nm1_02">
                <prompt-segments>
                  <audiofile text="To change your due date to today would be" src="BR1025_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="BR1025_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to do that? To hear all the terms again, say repeat that" src="BR1025_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1025_nm2_01">
                <prompt-segments>
                  <audiofile text="To change your due date to today would be" src="BR1025_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="BR1025_nm2_02">
                <prompt-segments>
                  <audiofile text="To do that now, say yes or press 1 Otherwise, say no or press 2, or to hear all the terms again sat repeat or press 3" src="BR1025_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <if cond="callType == 'make_pmt'">
                <prompt id="BR1025_nm3_01">
                  <prompt-segments>
                    <audiofile text="I m having trouble understanding Let s go back to your payment" src="BR1025_nm3_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BR1025_nm3_02">
                    <prompt-segments>
                      <audiofile text="Let s try one more time" src="BR1025_nm3_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="BR1025_nm2_01">
                <prompt-segments>
                  <audiofile text="To change your due date to today would be" src="BR1025_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="BR1025_nm2_02">
                <prompt-segments>
                  <audiofile text="To do that now, say yes or press 1 Otherwise, say no or press 2, or to hear all the terms again sat repeat or press 3" src="BR1025_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="callType == 'make_pmt'">
                <prompt id="BR1025_ini_01">
                  <prompt-segments>
                    <audiofile text="Would you like to do that?" src="BR1025_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BR1025_ini_02">
                    <prompt-segments>
                      <audiofile text="Are you ready to change your due date?" src="BR1025_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsBR1025.grxml" dtmfcommandgrammar="GlobalCommandsBR1025_dtmf.grxml">
          <grammars filename="BR1025_AcceptBCRTermsYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="BR1025_AcceptBCRTermsYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="BR1030_RepeatCurrentDueDate_PP">
      <session-mapping key="currentDate" value="" type="String"/>
      <session-mapping key="dayEveryMonthX" value="" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <audio>
        <prompt id="BR1030_out_01">
          <prompt-segments>
            <audiofile text="Your balance will continue to be due" src="BR1030_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="dayEveryMonthX" scope="request">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.VXMLDynamicPromptNameRendererImpl"/>
          <param name="tts" value="dayEveryMonthX" scope="request"/>
        </prompt>
        <if cond="callType == 'make_pmt'">
          <prompt id="BR1030_out_02">
            <prompt-segments>
              <audiofile text="Now, back to your payment!" src="BR1030_out_02.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <subdialog-state id="BR1035_CallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="BR1035_CallTransfer_SD_return"/>
    </subdialog-state>
    <decision-state id="BR1101_CheckNeedPIN_DS">
      <if cond="GlobalVars.securityRequired == true">
        <action next="BR1105_Login_SD"/>
        <else>
          <action next="BR1205_BillCycleReset_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="BR1105_Login_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="BR1105_Login_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BR1105_Login_SD_return_CS">
      <action next="BR1110_PlayThanks_PP"/>
    </custom-state>

    <play-state id="BR1110_PlayThanks_PP">
      <audio>
        <prompt id="BR1110_out_01">
          <prompt-segments>
            <audiofile text="Thanks!" src="BR1110_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BR1205_BillCycleReset_DB_DA"/>
    </play-state>

    <data-access-state id="BR1205_BillCycleReset_DB_DA">
      <session-mapping key="banId" value="GlobalVars.GetAccountDetails.accountNumber" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="BillCycleReset" classname="com.nuance.metro.dataaccess.BillCycleReset">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="banId"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="newbillCycle"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="BillCycleReset.status == 'Success'">
          <session-mapping key="GlobalVars.newBillCycle" expr="BillCycleReset.newBillCycle"/>
          <action next="BR1210_GetAccountDetails_DB_DA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <data-access-state id="BR1210_GetAccountDetails_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <data-access id="GetSubscriberDetails" classname="com.nuance.metro.dataaccess.GetSubscriberDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="languageCode"/>
          <input-variable name="sessionId"/>
          <input-variable name="imei"/>
          <input-variable name="clearCache"/>
          <input-variable name="multiLineDetails"/>
        </inputs>
        <outputs>
          <output-variable name="mdn"/>
          <output-variable name="accountNumber" mask="true"/>
          <output-variable name="active"/>
          <output-variable name="serialNumber"/>
          <output-variable name="features"/>
          <output-variable name="thirdPartyFeatures"/>
          <output-variable name="payDate"/>
          <output-variable name="taxInclusive"/>
          <output-variable name="accountStatus"/>
          <output-variable name="dueImmediatelyAmount"/>
          <output-variable name="highSecurity"/>
          <output-variable name="ratePlan"/>
          <output-variable name="marketID"/>
          <output-variable name="language"/>
          <output-variable name="isAutopayEnabled"/>
          <output-variable name="hasPhoneInsurance"/>
          <output-variable name="balance"/>
          <output-variable name="isDeviceAllowed"/>
          <output-variable name="isSuspended"/>
          <output-variable name="accountType"/>
          <output-variable name="validAccount"/>
          <output-variable name="roamingBucket"/>
          <output-variable name="paymentStatus"/>
          <output-variable name="ratePlanAttributesMap"/>
          <output-variable name="networkType"/>
          <output-variable name="securityQuestionCode"/>
          <output-variable name="hasEWallet"/>
          <output-variable name="eligibleForBillCycleReset"/>
          <output-variable name="billCycleLength"/>
          <output-variable name="firstName"/>
          <output-variable name="lastName"/>
          <output-variable name="eligibleForUnhotlineCheck"/>
          <output-variable name="securityQuestionString"/>
          <output-variable name="iccid"/>
          <output-variable name="imei"/>
          <output-variable name="hasAutopayPending"/>
          <output-variable name="insuranceCode"/>
          <output-variable name="subscriberId"/>
          <output-variable name="usedESNAllowed"/>
          <output-variable name="emailVerificationStatus"/>
          <output-variable name="emailAddress"/>
          <output-variable name="currentPromo"/>
          <output-variable name="futurePromo"/>
          <output-variable name="featureCodes"/>
          <output-variable name="superRegion"/>
          <output-variable name="accountFutureRequestInd"/>
          <output-variable name="subscriberFuturePricePlanInd"/>
          <output-variable name="ratePlanPrice"/>
          <output-variable name="currentPlanPromptURL"/>
          <output-variable name="currentPlanPromptTTS"/>
          <output-variable name="familyPlanIndicator"/>
          <output-variable name="topupEligibility"/>
          <output-variable name="customerTreatmentLevel"/>
          <output-variable name="HasHighSpeedData"/>
          <output-variable name="includedFeatures"/>
          <output-variable name="hasDataMaximizer"/>
          <output-variable name="eligibleForUpgrade"/>
          <output-variable name="upgradeEligibilityDate"/>
          <output-variable name="arBalance"/>
          <output-variable name="multilineList"/>
          <output-variable name="planMedialPromptURL"/>
          <output-variable name="planMedialPromptTTS"/>
          <output-variable name="hasDefaultAccountValues"/>
          <output-variable name="zipCode" mask="true"/>
          <output-variable name="accountRestrictIndicator"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetSubscriberDetails &amp;&amp; GetSubscriberDetails.validAccount">
          <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
          <session-mapping key="GlobalVars.trn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.unCoopMaxRequest" expr="GetSubscriberDetails.unCoopMaxRequest"/>
          <session-mapping key="GlobalVars.coopMaxRequest" expr="GetSubscriberDetails.coopMaxRequest"/>
          <session-mapping key="ActivationTable.OLD_MDN_NUM" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.networkType" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="ActivationTable.NETWORK_TYPE" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="GlobalVars.marketID" expr="GetSubscriberDetails.marketID"/>
          <session-mapping key="GlobalVars.zipCode" expr="GetSubscriberDetails.zipCode"/>
          <session-mapping key="GlobalVars.mdn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.isOnFamilyPlan" expr="GetSubscriberDetails.isOnFamilyPlan"/>
          <session-mapping key="GlobalVars.accountStatus" expr="GetSubscriberDetails.accountStatus"/>
          <session-mapping key="GlobalVars.eligibleForUpgrade" expr="GetSubscriberDetails.isCurrentlyEligibleForDeviceUpgrade"/>
          <session-mapping key="GlobalVars.upgradeEligibilityDate" expr="GetSubscriberDetails.upgradeEligibilityDate"/>
          <session-mapping key="GlobalVars.accountFutureRequestInd" expr="GetSubscriberDetails.accountFutureRequestInd"/>
          <session-mapping key="GlobalVars.subscriberFuturePricePlanInd" expr="GetSubscriberDetails.subscriberFuturePricePlanInd"/>
          <if cond="GetSubscriberDetails.status == 'Success'">
            <action next="BR1215_PlayAccountUpdated_PP"/>
            <else>
              <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
              <gotodialog next="CallTransfer_Main_Dialog"/>
            </else>
          </if>
        </if>
      </action>
    </data-access-state>

    <play-state id="BR1215_PlayAccountUpdated_PP">
      <session-mapping key="newbillCycle" value="GlobalVars.newBillCycle" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="dayEveryMonthX" value="" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <audio>
        <prompt id="BR1215_out_01">
          <prompt-segments>
            <audiofile text="I changed your due date to" src="BR1215_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="dayEveryMonthX" scope="request">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.VXMLDynamicPromptNameRendererImpl"/>
          <param name="tts" value="dayEveryMonthX" scope="request"/>
        </prompt>
        <if cond="(newbillCycle == 29 || newbillCycle == 30)">
          <prompt id="silence_500ms">
            <prompt-segments>
              <audiofile text="test" src="silence_500ms.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="BR1215_out_02">
            <prompt-segments>
              <audiofile text="Watch out for February! It s shorter, so your payment will be due on *the last day*" src="BR1215_out_02.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <if cond="(callType == 'billcyclereset')">
          <prompt id="silence_500ms">
            <prompt-segments>
              <audiofile text="test" src="silence_500ms.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="BR1215_out_03">
            <prompt-segments>
              <audiofile text="With the one-time charge, your payment due today is" src="BR1215_out_03.wav"/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="dueImmediatelyAmount">
            <param value="f" name="intonation"/>
            <param value="false" name="playZeroCents"/>
          </prompt>
          <prompt id="silence_750ms">
            <prompt-segments>
              <audiofile text="test" src="silence_750ms.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="BR1215_out_05">
              <prompt-segments>
                <audiofile text="Now, back to your payment!" src="BR1215_out_05.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <if cond="GlobalVars.callType == 'billcyclereset'">
        <action next="BR1220_PlayCanHangup_PP"/>
        <else>
          <action next="BR1225_CheckOriginToReturn_JDA"/>
        </else>
      </if>
    </play-state>

    <play-state id="BR1220_PlayCanHangup_PP">
      <audio>
        <prompt id="BR1220_out_01">
          <prompt-segments>
            <audiofile text="If you d like to pay your balance online or through myMetro, you can hang up now - the changes are already on your account" src="BR1220_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BR1220_out_02">
          <prompt-segments>
            <audiofile text="Otherwise, hold the line and I'll take your payment right here" src="BR1220_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_3000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_3000ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BR1220_out_03">
          <prompt-segments>
            <audiofile text="Okay, let's take care of your payment!" src="BR1220_out_03.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BR1225_CheckOriginToReturn_JDA"/>
    </play-state>

    <decision-state id="BR1225_CheckOriginToReturn_DS">
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <if cond="GlobalVars.callType == 'make_pmt' || GlobalVars.callType == 'make_pmt_auto_pay'">
        <action next="getReturnLink()"/>
        <else>
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentAmount" expr="dueImmediatelyAmount"/>
          <action next="BR1230_MakePayment_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="BR1230_MakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="BR1230_MakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BR1230_MakePayment_SD_return_CS">
      <gotodialog next="CallTransfer_Main_Dialog"/>
    </custom-state>

  </dialog>
  