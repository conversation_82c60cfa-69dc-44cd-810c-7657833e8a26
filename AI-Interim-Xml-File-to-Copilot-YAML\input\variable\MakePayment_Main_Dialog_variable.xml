<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="monitoringFullString" value="empty" type="String"/>
  <session-mapping key="GlobalVars.firstStepFrom99Entry" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.tag" value="make-payment" type="string"/>
  <session-mapping key="GlobalVars.callType" value="make_pmt" type="string"/>
  <session-mapping key="GlobalVars.GetAccountDetails" value="undefined" type="string"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.IsAddingFeature" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.paymentsEntryPoint" value="careVoiceStore" type="string"/>
  <session-mapping key="dueImmediatelyAmount" value="3" type="integer"/>
  <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="boolean"/>
  <session-mapping key="aniMatch" value="true" type="boolean"/>
  <session-mapping key="callType" value="make_pmt" type="string"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="boolean"/>
  <session-mapping key="isReentry" value="false" type="boolean"/>
  <session-mapping key="acceptPayByPhone" value="false" type="boolean"/>
  <session-mapping key="offeredPayByPhone" value="true" type="string"/>
  <session-mapping key="switchLinesSuccess" value="false" type="string"/>
  <session-mapping key="care_allow_payment_transfers" value="false" type="boolean"/>
  <session-mapping key="operatorPaymentsReqCount" value="0" type="integer"/>
  <session-mapping key="accountStatus" value="suspended" type="string"/>
  <session-mapping key="PaymentTable.ACTIVATION_STATUS" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.aniMatch" value="true" type="boolean"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="GlobalVars.authFailure" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.paymentFailure" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.bcrPendingPmtTransfer" value="true" type="boolean"/>
</session-mappings>
