<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="CompatibilityIssues_Dialog">
    <decision-state id="CM0105_CheckCompatibilityIssue_DS">
      <if cond="GlobalVars.byodCompatibility.toUpperCase() == 'PARTIALLY COMPATIBLE'">
        <action next="CM0110_OfferContinueOrAgent_DM"/>
        <elseif cond="GlobalVars.byodCompatibility.toUpperCase() == 'NOT COMPATIBLE'">
          <action next="CM0115_PlayDeviceIncompatible_PP"/>
        </elseif>
        <else>
          <action next="CM0120_PlayCompatibilityNotFound_PP"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="CM0110_OfferContinueOrAgent_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <action label="agent">
          <audio>
            <prompt id="CM0110_out_01">
              <prompt-segments>
                <audiofile text="Sure" src="CM0110_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
            </else>
          </if>
          <if cond="GlobalVars.callType == 'activate'">
            <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
            <else>
              <session-mapping key="GlobalVars.compatibilityTransfer" value="true" type="Boolean"/>
            </else>
          </if>
          <action next="getReturnLink()"/>
        </action>
        <action label="continue">
          <action next="getReturnLink()"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="CM0110_ini_01">
                <prompt-segments>
                  <audiofile text="It looks like your phone's not fully compatible with our network, so you might get lower signal quality on it Would you like to 'continue', or 'talk to an agent'? " src="CM0110_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="CM0110_ini_01">
                <prompt-segments>
                  <audiofile text="It looks like your phone's not fully compatible with our network, so you might get lower signal quality on it Would you like to 'continue', or 'talk to an agent'? " src="CM0110_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="CM0110_ini_01">
                <prompt-segments>
                  <audiofile text="It looks like your phone's not fully compatible with our network, so you might get lower signal quality on it Would you like to 'continue', or 'talk to an agent'? " src="CM0110_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="CM0110_nm2_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="To continue activating your new phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0 " src="CM0110_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CM0110_maxnomatch2_01" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="To continue switching to this phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0" src="CM0110_maxnomatch2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="CM0110_nm2_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="To continue activating your new phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0 " src="CM0110_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CM0110_maxnomatch2_01" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="To continue switching to this phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0" src="CM0110_maxnomatch2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CM0110_ini_01">
                <prompt-segments>
                  <audiofile text="It looks like your phone's not fully compatible with our network, so you might get lower signal quality on it Would you like to 'continue', or 'talk to an agent'? " src="CM0110_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="CM0110_nm2_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="To continue activating your new phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0 " src="CM0110_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CM0110_maxnomatch2_01" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="To continue switching to this phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0" src="CM0110_maxnomatch2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="CM0110_nm2_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="To continue activating your new phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0 " src="CM0110_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CM0110_maxnomatch2_01" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="To continue switching to this phone, say 'continue' or press 1 To speak to someone now, say 'agent' or press 0" src="CM0110_maxnomatch2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="CM0110_ini_01">
                <prompt-segments>
                  <audiofile text="It looks like your phone's not fully compatible with our network, so you might get lower signal quality on it Would you like to 'continue', or 'talk to an agent'? " src="CM0110_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommandsNoOperator_dtmf.grxml">
          <grammars filename="CM0110_OfferContinueOrAgent_DM.grxml?SWI_vars.callType" count="1"/>
          <dtmfgrammars filename="CM0110_OfferContinueOrAgent_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'continue' &amp;&amp; callType == 'activate'">
                <prompt id="CM0110_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You wanna activate this phone anyway" src="CM0110_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'continue' &amp;&amp; callType != 'activate'">
                <prompt id="CM0110_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="You wanna switch to this phone anyway" src="CM0110_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="CM0110_OfferContinueOrAgent_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="lastresult.interpretation.dm_root == 'continue' &amp;&amp; callType == 'activate'">
                <prompt id="CM0110_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You wanna activate this phone anyway" src="CM0110_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'continue' &amp;&amp; callType != 'activate'">
                <prompt id="CM0110_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="You wanna switch to this phone anyway" src="CM0110_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="lastresult.interpretation.dm_root == 'continue' &amp;&amp; callType == 'activate'">
                <prompt id="CM0110_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You wanna activate this phone anyway" src="CM0110_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'continue' &amp;&amp; callType != 'activate'">
                <prompt id="CM0110_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="You wanna switch to this phone anyway" src="CM0110_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="CM0115_PlayDeviceIncompatible_PP">
      <if cond="language == 'en-US'">
        <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
        <else>
          <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
        </else>
      </if>
      <if cond="GlobalVars.callType == 'activate'">
        <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
        <else>
          <session-mapping key="GlobalVars.compatibilityTransfer" value="true" type="Boolean"/>
        </else>
      </if>
      <audio>
        <prompt id="CM0115_out_01">
          <prompt-segments>
            <audiofile text="Actually, it doesn't look like your phone's compatible with our network " src="CM0115_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="CM0120_PlayCompatibilityNotFound_PP">
      <if cond="language == 'en-US'">
        <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
        <else>
          <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
        </else>
      </if>
      <if cond="GlobalVars.callType == 'activate'">
        <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
        <else>
          <session-mapping key="GlobalVars.compatibilityTransfer" value="true" type="Boolean"/>
        </else>
      </if>
      <audio>
        <prompt id="CM0120_out_01">
          <prompt-segments>
            <audiofile text="Actually, I can't tell if your phone's compatible with our network " src="CM0120_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

  </dialog>
  