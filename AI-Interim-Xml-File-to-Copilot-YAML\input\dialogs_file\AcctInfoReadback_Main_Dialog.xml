<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="AcctInfoReadback_Main_Dialog">
    <data-access-state id="AI0999_GetAccountDetails_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="imeiSerialNumber" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <data-access id="GetSubscriberDetails" classname="com.nuance.metro.dataaccess.GetSubscriberDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="languageCode"/>
          <input-variable name="sessionId"/>
          <input-variable name="imei"/>
          <input-variable name="clearCache"/>
          <input-variable name="multiLineDetails"/>
        </inputs>
        <outputs>
          <output-variable name="mdn"/>
          <output-variable name="accountNumber" mask="true"/>
          <output-variable name="active"/>
          <output-variable name="serialNumber"/>
          <output-variable name="features"/>
          <output-variable name="thirdPartyFeatures"/>
          <output-variable name="payDate"/>
          <output-variable name="taxInclusive"/>
          <output-variable name="accountStatus"/>
          <output-variable name="dueImmediatelyAmount"/>
          <output-variable name="highSecurity"/>
          <output-variable name="ratePlan"/>
          <output-variable name="marketID"/>
          <output-variable name="language"/>
          <output-variable name="isAutopayEnabled"/>
          <output-variable name="hasPhoneInsurance"/>
          <output-variable name="balance"/>
          <output-variable name="isDeviceAllowed"/>
          <output-variable name="isSuspended"/>
          <output-variable name="accountType"/>
          <output-variable name="validAccount"/>
          <output-variable name="roamingBucket"/>
          <output-variable name="paymentStatus"/>
          <output-variable name="ratePlanAttributesMap"/>
          <output-variable name="networkType"/>
          <output-variable name="securityQuestionCode"/>
          <output-variable name="hasEWallet"/>
          <output-variable name="eligibleForBillCycleReset"/>
          <output-variable name="billCycleLength"/>
          <output-variable name="firstName"/>
          <output-variable name="lastName"/>
          <output-variable name="eligibleForUnhotlineCheck"/>
          <output-variable name="securityQuestionString"/>
          <output-variable name="iccid"/>
          <output-variable name="imei"/>
          <output-variable name="hasAutopayPending"/>
          <output-variable name="insuranceCode"/>
          <output-variable name="subscriberId"/>
          <output-variable name="usedESNAllowed"/>
          <output-variable name="emailVerificationStatus"/>
          <output-variable name="emailAddress"/>
          <output-variable name="currentPromo"/>
          <output-variable name="futurePromo"/>
          <output-variable name="featureCodes"/>
          <output-variable name="superRegion"/>
          <output-variable name="accountFutureRequestInd"/>
          <output-variable name="subscriberFuturePricePlanInd"/>
          <output-variable name="ratePlanPrice"/>
          <output-variable name="currentPlanPromptURL"/>
          <output-variable name="currentPlanPromptTTS"/>
          <output-variable name="familyPlanIndicator"/>
          <output-variable name="topupEligibility"/>
          <output-variable name="customerTreatmentLevel"/>
          <output-variable name="HasHighSpeedData"/>
          <output-variable name="includedFeatures"/>
          <output-variable name="hasDataMaximizer"/>
          <output-variable name="eligibleForUpgrade"/>
          <output-variable name="upgradeEligibilityDate"/>
          <output-variable name="arBalance"/>
          <output-variable name="multilineList"/>
          <output-variable name="planMedialPromptURL"/>
          <output-variable name="planMedialPromptTTS"/>
          <output-variable name="hasDefaultAccountValues"/>
          <output-variable name="zipCode" mask="true"/>
          <output-variable name="accountRestrictIndicator"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetSubscriberDetails.status &amp;&amp; GetSubscriberDetails.status.toUpperCase() == 'SUCCESS' &amp;&amp; GetSubscriberDetails.validAccount">
          <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
        </if>
        <action next="AI1000_ReadyToWriteNumberYN_DM"/>
      </action>
    </data-access-state>

    <dm-state id="AI1000_ReadyToWriteNumberYN_DM" type="CUST">
      <session-mapping key="npi_flag" value="GlobalVars.npi_flag" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="acceptedAPNSMS" value="GlobalVars.acceptedAPNSMS != undefined ? GlobalVars.acceptedAPNSMS : false" type="String"/>
      <session-mapping key="APNSMSSuccess" value="GlobalVars.APNSMSSuccess != undefined ? GlobalVars.APNSMSSuccess : false" type="String"/>
      <session-mapping key="acceptedAcctDetsSMS" value="GlobalVars.acceptedAcctDetsSMS != undefined ? GlobalVars.acceptedAcctDetsSMS : false " type="String"/>
      <session-mapping key="acctDetsSMSSuccess" value="GlobalVars.acctDetsSMSSuccess != undefined ? GlobalVars.acctDetsSMSSuccess : false" type="String"/>
      <session-mapping key="operatorRequest" value="false" type="Boolean"/>
      <success>
        <action label="yes" next="AI1015_PhoneNumber_PP">
          <audio>
            <prompt id="silence_600ms">
              <prompt-segments>
                <audiofile text="" src="silence_600ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="no" next="AI1010_NewNumberWait_DM">
          <audio>
            <prompt id="AI1000_out_01">
              <prompt-segments>
                <audiofile text="No problem, I'll wait while you get set up!" src="AI1000_out_01.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_600ms">
              <prompt-segments>
                <audiofile text="" src="silence_600ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="operator">
          <audio>
            <prompt id="AI1000_out_03">
              <prompt-segments>
                <audiofile text="Just so you know our agents wouldnt have your new your phone number yet So please stay on this call until you ve heard it" src="AI1000_out_03.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_1000ms">
              <prompt-segments>
                <audiofile text="test" src="silence_1000ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="operatorRequest" value="true" type="Boolean"/>
          <action next="AI1000_ReadyToWriteNumberYN_DM"/>
        </action>
      </success>
      <catch/>
      <collection_configuration playreinvokeprompt="true" highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1000_ini_03">
                <prompt-segments>
                  <audiofile text="Okay, your new account's ready! " src="AI1000_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(acceptedAPNSMS == true &amp;&amp; acceptedAcctDetsSMS == true)">
                <prompt id="AI1000_ini_07" cond="(APNSMSSuccess == true &amp;&amp; acctDetsSMSSuccess == true)">
                  <prompt-segments>
                    <audiofile text="You'll get the text messages about the APN settings and your security details in a few minutes" src="AI1000_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AI1000_ini_08" cond="!(APNSMSSuccess == true &amp;&amp; acctDetsSMSSuccess == true)">
                  <prompt-segments>
                    <audiofile text="I wasn't able to send *all* of your text messages, so make sure you check metro by t dash mobile dot com " src="AI1000_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="acceptedAPNSMS == true">
                  <prompt id="AI1000_ini_04" cond="APNSMSSuccess == true">
                    <prompt-segments>
                      <audiofile text="You'll get the text message about APN settings in a few minutes" src="AI1000_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AI1000_ini_05" cond="APNSMSSuccess == false">
                    <prompt-segments>
                      <audiofile text="I couldn't text you about the APN settings though, so please check metro by t dash mobile dot com" src="AI1000_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="acceptedAcctDetsSMS == true">
                  <prompt id="AI1000_ini_09" cond="acctDetsSMSSuccess == true">
                    <prompt-segments>
                      <audiofile text="You'll get the text message about your security details in a few minutes " src="AI1000_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AI1000_ini_10" cond="acctDetsSMSSuccess == false">
                    <prompt-segments>
                      <audiofile text="I couldn't text you about the your security details, so please check metro by t dash mobile dot com " src="AI1000_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="AI1000_ini_06">
                <prompt-segments>
                  <audiofile text="For now" src="AI1000_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="npi_flag == true">
                <prompt id="AI1000_ini_02">
                  <prompt-segments>
                    <audiofile text="I've got your account details ready Are you ready to write them down?" src="AI1000_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AI1000_ini_01">
                    <prompt-segments>
                      <audiofile text="I've got your new phone number  Are you ready to write it down?" src="AI1000_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AI1000_ReadyToWriteNumberYN_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="AI1000_ReadyToWriteNumberYN_DM_initial"/>
          <helpprompts count="2" filename="none" text="none" id="AI1000_ReadyToWriteNumberYN_DM_initial"/>
          <repeatprompts count="1">
            <audio>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1000_ini_03">
                <prompt-segments>
                  <audiofile text="Okay, your new account's ready! " src="AI1000_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(acceptedAPNSMS == true &amp;&amp; acceptedAcctDetsSMS == true)">
                <prompt id="AI1000_ini_07" cond="(APNSMSSuccess == true &amp;&amp; acctDetsSMSSuccess == true)">
                  <prompt-segments>
                    <audiofile text="You'll get the text messages about the APN settings and your security details in a few minutes" src="AI1000_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AI1000_ini_08" cond="!(APNSMSSuccess == true &amp;&amp; acctDetsSMSSuccess == true)">
                  <prompt-segments>
                    <audiofile text="I wasn't able to send *all* of your text messages, so make sure you check metro by t dash mobile dot com " src="AI1000_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="acceptedAPNSMS == true">
                  <prompt id="AI1000_ini_04" cond="APNSMSSuccess == true">
                    <prompt-segments>
                      <audiofile text="You'll get the text message about APN settings in a few minutes" src="AI1000_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AI1000_ini_05" cond="APNSMSSuccess == false">
                    <prompt-segments>
                      <audiofile text="I couldn't text you about the APN settings though, so please check metro by t dash mobile dot com" src="AI1000_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="acceptedAcctDetsSMS == true">
                  <prompt id="AI1000_ini_09" cond="acctDetsSMSSuccess == true">
                    <prompt-segments>
                      <audiofile text="You'll get the text message about your security details in a few minutes " src="AI1000_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AI1000_ini_10" cond="acctDetsSMSSuccess == false">
                    <prompt-segments>
                      <audiofile text="I couldn't text you about the your security details, so please check metro by t dash mobile dot com " src="AI1000_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="AI1000_ini_06">
                <prompt-segments>
                  <audiofile text="For now" src="AI1000_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="npi_flag == true">
                <prompt id="AI1000_ini_02">
                  <prompt-segments>
                    <audiofile text="I've got your account details ready Are you ready to write them down?" src="AI1000_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AI1000_ini_01">
                    <prompt-segments>
                      <audiofile text="I've got your new phone number  Are you ready to write it down?" src="AI1000_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2">
            <audio>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1000_ini_03">
                <prompt-segments>
                  <audiofile text="Okay, your new account's ready! " src="AI1000_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(acceptedAPNSMS == true &amp;&amp; acceptedAcctDetsSMS == true)">
                <prompt id="AI1000_ini_07" cond="(APNSMSSuccess == true &amp;&amp; acctDetsSMSSuccess == true)">
                  <prompt-segments>
                    <audiofile text="You'll get the text messages about the APN settings and your security details in a few minutes" src="AI1000_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AI1000_ini_08" cond="!(APNSMSSuccess == true &amp;&amp; acctDetsSMSSuccess == true)">
                  <prompt-segments>
                    <audiofile text="I wasn't able to send *all* of your text messages, so make sure you check metro by t dash mobile dot com " src="AI1000_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="acceptedAPNSMS == true">
                  <prompt id="AI1000_ini_04" cond="APNSMSSuccess == true">
                    <prompt-segments>
                      <audiofile text="You'll get the text message about APN settings in a few minutes" src="AI1000_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AI1000_ini_05" cond="APNSMSSuccess == false">
                    <prompt-segments>
                      <audiofile text="I couldn't text you about the APN settings though, so please check metro by t dash mobile dot com" src="AI1000_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="acceptedAcctDetsSMS == true">
                  <prompt id="AI1000_ini_09" cond="acctDetsSMSSuccess == true">
                    <prompt-segments>
                      <audiofile text="You'll get the text message about your security details in a few minutes " src="AI1000_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AI1000_ini_10" cond="acctDetsSMSSuccess == false">
                    <prompt-segments>
                      <audiofile text="I couldn't text you about the your security details, so please check metro by t dash mobile dot com " src="AI1000_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="AI1000_ini_06">
                <prompt-segments>
                  <audiofile text="For now" src="AI1000_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="npi_flag == true">
                <prompt id="AI1000_ini_02">
                  <prompt-segments>
                    <audiofile text="I've got your account details ready Are you ready to write them down?" src="AI1000_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AI1000_ini_01">
                    <prompt-segments>
                      <audiofile text="I've got your new phone number  Are you ready to write it down?" src="AI1000_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="3">
            <audio>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1000_ini_03">
                <prompt-segments>
                  <audiofile text="Okay, your new account's ready! " src="AI1000_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(acceptedAPNSMS == true &amp;&amp; acceptedAcctDetsSMS == true)">
                <prompt id="AI1000_ini_07" cond="(APNSMSSuccess == true &amp;&amp; acctDetsSMSSuccess == true)">
                  <prompt-segments>
                    <audiofile text="You'll get the text messages about the APN settings and your security details in a few minutes" src="AI1000_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AI1000_ini_08" cond="!(APNSMSSuccess == true &amp;&amp; acctDetsSMSSuccess == true)">
                  <prompt-segments>
                    <audiofile text="I wasn't able to send *all* of your text messages, so make sure you check metro by t dash mobile dot com " src="AI1000_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="acceptedAPNSMS == true">
                  <prompt id="AI1000_ini_04" cond="APNSMSSuccess == true">
                    <prompt-segments>
                      <audiofile text="You'll get the text message about APN settings in a few minutes" src="AI1000_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AI1000_ini_05" cond="APNSMSSuccess == false">
                    <prompt-segments>
                      <audiofile text="I couldn't text you about the APN settings though, so please check metro by t dash mobile dot com" src="AI1000_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="acceptedAcctDetsSMS == true">
                  <prompt id="AI1000_ini_09" cond="acctDetsSMSSuccess == true">
                    <prompt-segments>
                      <audiofile text="You'll get the text message about your security details in a few minutes " src="AI1000_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AI1000_ini_10" cond="acctDetsSMSSuccess == false">
                    <prompt-segments>
                      <audiofile text="I couldn't text you about the your security details, so please check metro by t dash mobile dot com " src="AI1000_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="AI1000_ini_06">
                <prompt-segments>
                  <audiofile text="For now" src="AI1000_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="npi_flag == true">
                <prompt id="AI1000_ini_02">
                  <prompt-segments>
                    <audiofile text="I've got your account details ready Are you ready to write them down?" src="AI1000_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AI1000_ini_01">
                    <prompt-segments>
                      <audiofile text="I've got your new phone number  Are you ready to write it down?" src="AI1000_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AI1000_nm1_01">
                <prompt-segments>
                  <audiofile text="If you have a pen and paper handy to write down your account information, say 'yes' If not, say 'no' and I'll wait while you get something to write with" src="AI1000_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AI1000_nm2_01">
                <prompt-segments>
                  <audiofile text="Say  yes  or press one, or  no  or press two  Do you have a pen and paper handy?" src="AI1000_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AI1000_nm3_01">
                <prompt-segments>
                  <audiofile text="If you re ready to copy down your new account details, say  yes  or press one  If you d like me to wait, say  no  or press two" src="AI1000_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1000_nm1_01">
                <prompt-segments>
                  <audiofile text="If you have a pen and paper handy to write down your account information, say 'yes' If not, say 'no' and I'll wait while you get something to write with" src="AI1000_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1000_nm2_01">
                <prompt-segments>
                  <audiofile text="Say  yes  or press one, or  no  or press two  Do you have a pen and paper handy?" src="AI1000_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1000_nm3_01">
                <prompt-segments>
                  <audiofile text="If you re ready to copy down your new account details, say  yes  or press one  If you d like me to wait, say  no  or press two" src="AI1000_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1000_ini_03">
                <prompt-segments>
                  <audiofile text="Okay, your new account's ready! " src="AI1000_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(acceptedAPNSMS == true &amp;&amp; acceptedAcctDetsSMS == true)">
                <prompt id="AI1000_ini_07" cond="(APNSMSSuccess == true &amp;&amp; acctDetsSMSSuccess == true)">
                  <prompt-segments>
                    <audiofile text="You'll get the text messages about the APN settings and your security details in a few minutes" src="AI1000_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AI1000_ini_08" cond="!(APNSMSSuccess == true &amp;&amp; acctDetsSMSSuccess == true)">
                  <prompt-segments>
                    <audiofile text="I wasn't able to send *all* of your text messages, so make sure you check metro by t dash mobile dot com " src="AI1000_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="acceptedAPNSMS == true">
                  <prompt id="AI1000_ini_04" cond="APNSMSSuccess == true">
                    <prompt-segments>
                      <audiofile text="You'll get the text message about APN settings in a few minutes" src="AI1000_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AI1000_ini_05" cond="APNSMSSuccess == false">
                    <prompt-segments>
                      <audiofile text="I couldn't text you about the APN settings though, so please check metro by t dash mobile dot com" src="AI1000_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="acceptedAcctDetsSMS == true">
                  <prompt id="AI1000_ini_09" cond="acctDetsSMSSuccess == true">
                    <prompt-segments>
                      <audiofile text="You'll get the text message about your security details in a few minutes " src="AI1000_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AI1000_ini_10" cond="acctDetsSMSSuccess == false">
                    <prompt-segments>
                      <audiofile text="I couldn't text you about the your security details, so please check metro by t dash mobile dot com " src="AI1000_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="AI1000_ini_06">
                <prompt-segments>
                  <audiofile text="For now" src="AI1000_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="npi_flag == true">
                <prompt id="AI1000_ini_02">
                  <prompt-segments>
                    <audiofile text="I've got your account details ready Are you ready to write them down?" src="AI1000_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AI1000_ini_01">
                    <prompt-segments>
                      <audiofile text="I've got your new phone number  Are you ready to write it down?" src="AI1000_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_noOperator.grxml" dtmfcommandgrammar="GlobalCommands_noOperator_dtmf.grxml">
          <grammars filename="AI1000_ReadyToWriteNumberYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="AI1000_ReadyToWriteNumberYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AI1010_NewNumberWait_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <action label="ready" next="AI1015_PhoneNumber_PP">
          <audio>
            <prompt id="AI1010_out_01">
              <prompt-segments>
                <audiofile text="Let s continue" src="AI1010_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="operator">
          <audio>
            <prompt id="AI1010_out_02">
              <prompt-segments>
                <audiofile text="Just so you know our agents wouldnt be able to give you your phone number So please stay on the call until youve gotten it " src="AI1010_out_02.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_1000ms">
              <prompt-segments>
                <audiofile text="test" src="silence_1000ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="AI1010_NewNumberWait_DM"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AI1010_ini_02">
                <prompt-segments>
                  <audiofile text="No problem  I'll wait  Once you re ready to write down your account information, say continue or press 1  When you re ready, say continue, or press one  At any time, you can say Continue, or press one  If you re ready to hear your account information, say Continue, or press one   If you re ready,  say continue, or press one  You can say Continue, or press one when you re ready  Hmmm, I seem to be having some trouble Let s move on " src="AI1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AI1010_ini_02">
                <prompt-segments>
                  <audiofile text="No problem  I'll wait  Once you re ready to write down your account information, say continue or press 1  When you re ready, say continue, or press one  At any time, you can say Continue, or press one  If you re ready to hear your account information, say Continue, or press one   If you re ready,  say continue, or press one  You can say Continue, or press one when you re ready  Hmmm, I seem to be having some trouble Let s move on " src="AI1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AI1010_ini_02">
                <prompt-segments>
                  <audiofile text="No problem  I'll wait  Once you re ready to write down your account information, say continue or press 1  When you re ready, say continue, or press one  At any time, you can say Continue, or press one  If you re ready to hear your account information, say Continue, or press one   If you re ready,  say continue, or press one  You can say Continue, or press one when you re ready  Hmmm, I seem to be having some trouble Let s move on " src="AI1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_noOperator.grxml" dtmfcommandgrammar="GlobalCommands_noOperator_dtmf.grxml">
          <grammars filename="AI1010_NewNumberWait_DM.grxml" count="1"/>
          <dtmfgrammars filename="AI1010_NewNumberWait_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="AI1015_PhoneNumber_PP">
      <session-mapping key="npi_flag" value="GlobalVars.npi_flag" type="String"/>
      <session-mapping key="reentry" value="GlobalVars.AI1015reentry" type="String"/>
      <session-mapping key="phoneNum" value="formatPhoneNumber(GlobalVars.mdn)" type="String"/>
      <audio>
        <prompt id="AI1015_out_01" cond="reentry == true" bargein="false">
          <prompt-segments>
            <audiofile text="Here re those details again" src="AI1015_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="npi_flag == false">
          <prompt id="AI1015_out_02" bargein="false">
            <prompt-segments>
              <audiofile text="Your new phone number is" src="AI1015_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <prompt type="custom" expr="phoneNum" bargein="false">
            <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
            <param name="intonation" value="f"/>
          </prompt>
          <prompt id="AI1015_out_04" bargein="false">
            <prompt-segments>
              <audiofile text="Again thats" src="AI1015_out_04.wav"/>
            </prompt-segments>
          </prompt>
          <prompt type="custom" expr="phoneNum" bargein="false">
            <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
            <param name="intonation" value="f"/>
          </prompt>
          <else>
            <prompt id="AI1015_out_03" bargein="false">
              <prompt-segments>
                <audiofile text="the phone number you ve given me to port over is" src="AI1015_out_03.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_300ms" bargein="false">
              <prompt-segments>
                <audiofile text="" src="silence_300ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="phone" expr="phoneNum" bargein="false">
              <param value="f" name="intonation"/>
            </prompt>
          </else>
        </if>
      </audio>
      <session-mapping key="GlobalVars.AI1015reentry" value="true" type="Boolean"/>
      <action next="AI1016_PlayAcctNumberPlan_PP"/>
    </play-state>

    <play-state id="AI1016_PlayAcctNumberPlan_PP">
      <session-mapping key="ratePlanSoc" value="GlobalVars.selectedPlan" type="String"/>
      <session-mapping key="ratePlanPrice" value="GlobalVars.selectedPlanPrice" type="String"/>
      <session-mapping key="accountNum" value="GlobalVars.banId" type="String"/>
      <audio>
        <prompt id="AI1016_out_01">
          <prompt-segments>
            <audiofile text="Your phone number is " src="AI1016_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="alphanum" expr="accountNum">
          <param value="f" name="intonation"/>
          <param value="SHORT" name="charPause"/>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="ratePlanSoc">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayCurrentRatePlan_AI"/>
        </prompt>
        <prompt id="silence_600ms">
          <prompt-segments>
            <audiofile text="" src="silence_600ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AI1016_out_02">
          <prompt-segments>
            <audiofile text="Your total payment each month will be " src="AI1016_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="currency" expr="ratePlanPrice">
          <param value="m" name="intonation"/>
          <param value="false" name="playZeroCents"/>
        </prompt>
        <prompt id="AI1016_out_03">
          <prompt-segments>
            <audiofile text=" which includes taxes and regulatory " src="AI1016_out_03.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AI1017_CheckPrepaid_JDA"/>
    </play-state>

    <decision-state id="AI1017_CheckPrepaid_DS">
      <session-mapping key="dueImmediatelyAmount" value="(GlobalVars.GetAccountDetails != undefined &amp;&amp; GlobalVars.GetAccountDetails != null)? GlobalVars.GetAccountDetails.dueImmediatelyAmount:0" type="String"/>
      <session-mapping key="payingWithPrepaid" value="GlobalVars.payingWithPrepaid" type="String"/>
      <session-mapping key="arBalance" value="(GlobalVars.GetAccountDetails != undefined &amp;&amp; GlobalVars.GetAccountDetails != null)? GlobalVars.GetAccountDetails.arBalance : 0" type="String"/>
      <session-mapping key="arBalanceCredit" value="(Math.abs(arBalance)).toString()" type="String"/>
      <if cond="payingWithPrepaid == true">
        <if cond="parseFloat(dueImmediatelyAmount) &gt; 0">
          <action next="AI1018_PlayPrepaidDues_PP"/>
          <elseif cond="parseFloat(arBalance) &lt; 0">
            <action next="AI1019_PlayPrepaidCredit_PP"/>
          </elseif>
          <else>
            <action next="AI1020_RepeatAcctDetails_DM"/>
          </else>
        </if>
        <else>
          <action next="AI1020_RepeatAcctDetails_DM"/>
        </else>
      </if>
    </decision-state>

    <play-state id="AI1018_PlayPrepaidDues_PP">
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <audio>
        <prompt id="AI1018_out_01">
          <prompt-segments>
            <audiofile text="To activate your service for this month, you still need to pay " src="AI1018_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="currency" expr="dueImmediatelyAmount">
          <param value="false" name="playZeroCents"/>
          <param value="f" name="intonation"/>
        </prompt>
      </audio>
      <action next="AI1020_RepeatAcctDetails_DM"/>
    </play-state>

    <play-state id="AI1019_PlayPrepaidCredit_PP">
      <session-mapping key="arBalance" value="GlobalVars.GetAccountDetails.arBalance" type="String"/>
      <session-mapping key="arBalanceCredit" value="(Math.abs(arBalance)).toString()" type="String"/>
      <audio>
        <prompt id="AI1019_out_01">
          <prompt-segments>
            <audiofile text="For next month, you have a credit of " src="AI1019_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="currency" expr="arBalanceCredit">
          <param value="f" name="intonation"/>
          <param value="false" name="playZeroCents"/>
        </prompt>
      </audio>
      <action next="AI1020_RepeatAcctDetails_DM"/>
    </play-state>

    <dm-state id="AI1020_RepeatAcctDetails_DM" type="CUST">
      <success>
        <action label="yes" next="AI1015_PhoneNumber_PP"/>
        <action label="no" next="AI1030_CheckNPI_JDA">
          <audio>
            <prompt id="AI1020_out_01">
              <prompt-segments>
                <audiofile text="Okay" src="AI1020_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="repeat" next="AI1015_PhoneNumber_PP"/>
        <action label="repeat" next="AI1015_PhoneNumber_PP"/>
        <action label="operator">
          <submit expr="operatorSubmitFunction('AcctInfoReadback_Main.dvxml','AI1020_RepeatAcctDetails_DM',AI1020_RepeatAcctDetails_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1020_ini_30">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="AI1020_ini_30.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AI1020_nm1_02">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   Would you like to hear your telephone number and account details again?" src="AI1020_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AI1020_nm2_02">
                <prompt-segments>
                  <audiofile text="Please say  yes  or press one, or  no  or press two  Would you like to hear those account details again?" src="AI1020_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AI1020_nm3_02">
                <prompt-segments>
                  <audiofile text="To hear your phone number and account details again, say  yes  or press one  To continue, say  no  or press two" src="AI1020_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1020_nm1_02">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   Would you like to hear your telephone number and account details again?" src="AI1020_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1020_nm2_02">
                <prompt-segments>
                  <audiofile text="Please say  yes  or press one, or  no  or press two  Would you like to hear those account details again?" src="AI1020_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1020_nm3_02">
                <prompt-segments>
                  <audiofile text="To hear your phone number and account details again, say  yes  or press one  To continue, say  no  or press two" src="AI1020_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1020_ini_30">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="AI1020_ini_30.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="AI1020_RepeatAcctDetails_DM.grxml" count="1"/>
          <dtmfgrammars filename="AI1020_RepeatAcctDetails_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="false">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="AI1030_CheckNPI_DS">
      <if cond="GlobalVars.npi_flag">
        <action next="AI1040_RepeatNPIInformationYN_DM"/>
        <else>
          <action next="AI1045_PlayWrapPrompt_PP"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="AI1040_RepeatNPIInformationYN_DM" type="YSNO">
      <session-mapping key="npi_type" value="NumberPortInVars.npi_type" type="String"/>
      <success>
        <action label="true" next="AI1040_RepeatNPIInformationYN_DM">
          <audio>
            <prompt id="AI1040_out_01">
              <prompt-segments>
                <audiofile text="Okay" src="AI1040_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="false" next="return">
          <audio>
            <prompt id="AI1040_out_02">
              <prompt-segments>
                <audiofile text="That s fine" src="AI1040_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AI1040_ini_01" cond="npi_type == 'cellphone'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number within about two hours If you re still not getting incoming calls on that number after two hours, please visit a Metro store or authorized dealer for help" src="AI1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_02" cond="npi_type == 'landline'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number in one to two weeks If you re still not getting incoming calls on that number after two weeks, please visit a Metro store or authorized dealer for help" src="AI1040_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_04">
                <prompt-segments>
                  <audiofile text="Now, would you like to hear that again?" src="AI1040_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="" text="" id="AI1040_RepeatNPIInformationYN_DM_initial"/>
          <helpprompts count="2" filename="none" text="none" id="AI1040_RepeatNPIInformationYN_DM_initial"/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AI1040_ini_01" cond="npi_type == 'cellphone'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number within about two hours If you re still not getting incoming calls on that number after two hours, please visit a Metro store or authorized dealer for help" src="AI1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_02" cond="npi_type == 'landline'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number in one to two weeks If you re still not getting incoming calls on that number after two weeks, please visit a Metro store or authorized dealer for help" src="AI1040_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_04">
                <prompt-segments>
                  <audiofile text="Now, would you like to hear that again?" src="AI1040_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2">
            <audio>
              <prompt id="AI1040_ini_01" cond="npi_type == 'cellphone'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number within about two hours If you re still not getting incoming calls on that number after two hours, please visit a Metro store or authorized dealer for help" src="AI1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_02" cond="npi_type == 'landline'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number in one to two weeks If you re still not getting incoming calls on that number after two weeks, please visit a Metro store or authorized dealer for help" src="AI1040_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_04">
                <prompt-segments>
                  <audiofile text="Now, would you like to hear that again?" src="AI1040_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="3">
            <audio>
              <prompt id="AI1040_ini_01" cond="npi_type == 'cellphone'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number within about two hours If you re still not getting incoming calls on that number after two hours, please visit a Metro store or authorized dealer for help" src="AI1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_02" cond="npi_type == 'landline'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number in one to two weeks If you re still not getting incoming calls on that number after two weeks, please visit a Metro store or authorized dealer for help" src="AI1040_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_04">
                <prompt-segments>
                  <audiofile text="Now, would you like to hear that again?" src="AI1040_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="4">
            <audio>
              <prompt id="AI1040_ini_01" cond="npi_type == 'cellphone'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number within about two hours If you re still not getting incoming calls on that number after two hours, please visit a Metro store or authorized dealer for help" src="AI1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_02" cond="npi_type == 'landline'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number in one to two weeks If you re still not getting incoming calls on that number after two weeks, please visit a Metro store or authorized dealer for help" src="AI1040_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_04">
                <prompt-segments>
                  <audiofile text="Now, would you like to hear that again?" src="AI1040_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AI1040_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   Would you like to hear that information again?" src="AI1040_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AI1040_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or press one, or  no  or press two  Do you want to hear that information again?" src="AI1040_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AI1040_nm3_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say  yes  or press one  To continue, say  no  or press two" src="AI1040_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   Would you like to hear that information again?" src="AI1040_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or press one, or  no  or press two  Do you want to hear that information again?" src="AI1040_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_nm3_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say  yes  or press one  To continue, say  no  or press two" src="AI1040_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AI1040_ini_01" cond="npi_type == 'cellphone'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number within about two hours If you re still not getting incoming calls on that number after two hours, please visit a Metro store or authorized dealer for help" src="AI1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_02" cond="npi_type == 'landline'">
                <prompt-segments>
                  <audiofile text="You should be able to make calls out right away You will start receiving calls on that number in one to two weeks If you re still not getting incoming calls on that number after two weeks, please visit a Metro store or authorized dealer for help" src="AI1040_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AI1040_ini_04">
                <prompt-segments>
                  <audiofile text="Now, would you like to hear that again?" src="AI1040_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AI1040_RepeatNPIInformationYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="AI1040_RepeatNPIInformationYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="AI1045_PlayWrapPrompt_PP">
      <session-mapping key="payingWithPrepaid" value="GlobalVars.payingWithPrepaid != undefined ? GlobalVars.payingWithPrepaid : false" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails != undefined ? GlobalVars.GetAccountDetails.dueImmediatelyAmount : 0" type="String"/>
      <session-mapping key="amountPaidOnCall" value="GlobalVars.amountPaidOnCall" type="String"/>
      <session-mapping key="price" value="GlobalVars.selectedPlanPrice" type="String"/>
      <audio>
        <if cond="(payingWithPrepaid == true) &amp;&amp; ((parseFloat(dueImmediatelyAmount) &gt; 0) || ((parseFloat(amountPaidOnCall) &lt; (parseFloat(price)))))">
          <prompt id="AI1045_out_01">
            <prompt-segments>
              <audiofile text="If you want to talk to an agent to make the rest of your first payment, stay on the line If you'll be paying online or in the myMetro app, you can hang up " src="AI1045_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="silence_1000ms">
            <prompt-segments>
              <audiofile text="test" src="silence_1000ms.wav"/>
            </prompt-segments>
          </prompt>
        </if>
      </audio>
      <if cond="(payingWithPrepaid == true) &amp;&amp; ((parseFloat(dueImmediatelyAmount) &gt; 0) || ((parseFloat(amountPaidOnCall) &lt; (parseFloat(price)))))">
        <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
      </if>
      <action next="getReturnLink()"/>
    </play-state>

    <subdialog-state id="AI1090_CallTransfer_SD">
      <gotodialog next="Transfer_Main"/>
      <action next="AI1090_CallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AI1090_CallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

  </dialog>
  