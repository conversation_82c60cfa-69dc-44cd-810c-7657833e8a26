<states-library xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../xsd/states.xsd">
	<dialog id="ACSMenu">
		<dm-state id="dlhd0012_ACSMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="1" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'ACS_1'"/>
				</action>
				<action label="2" next="dlhd0025_ACSTicketingProceduresMenu_DM">
					<session-mapping key="menuSelection" expr="'ACS_2'"/>
				</action>
				<action label="3" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'ACS_3'"/>
				</action>
				<action label="4" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'ACS_4'"/>
				</action>
				<action label="5" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'ACS_5'"/>
				</action>
				<action label="7" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'ACS_7'"/>
				</action>
				<action label="8" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'ACS_8'"/>
				</action>
			</success>
			<command>
				<action label="password" next="PasswordSupport.dvxml">
					<session-mapping key="menuSelection" expr="'ACS_0'"/>
				</action>
			</command>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0012_ACSMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For Common Use Airport locations, press 1. For ACS ticketing and procedural questions, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For Common Use Airport locations, press 1. For ACS ticketing and procedural questions, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For Common Use Airport locations, press 1. For ACS ticketing and procedural questions, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For Common Use Airport locations, press 1. For ACS ticketing and procedural questions, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For Common Use Airport locations, press 1. For ACS ticketing and procedural questions, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For Common Use Airport locations, press 1. For ACS ticketing and procedural questions, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
		<dm-state id="dlhd0025_ACSTicketingProceduresMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfgrammar1" value="" type="String"/>
			<command/>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="0"/>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0025_ACSTicketingProceduresMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="Please hang up and call the Global Assistance Center at 8 7 7 4 1 6 6 4 6 1 or 4 0 4 7 1 4 7 9 8 1.  To hear that number again, press 9.  Otherwise, thanks for calling Deltas Information Technology Service Desk.  Goodbye." src="tcc/dlhd0025_ACSTicketingProceduresMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0025_ACSTicketingProceduresMenu_DM_ree_01">
								<prompt-segments>
									<audiofile text="Once again, the phone number for the Global Assistance Center is 1 8 7 7 4 1 6 6 4 6 1, or 4 0 4 7 1 4 7 9 8 1.  To hear that number again, press 9. Otherwise, thanks for calling Deltas Information Technology Service Desk.  Goodbye." src="tcc/dlhd0025_ACSTicketingProceduresMenu_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0025_ACSTicketingProceduresMenu_DM_ree_01">
								<prompt-segments>
									<audiofile text="Once again, the phone number for the Global Assistance Center is 1 8 7 7 4 1 6 6 4 6 1, or 4 0 4 7 1 4 7 9 8 1.  To hear that number again, press 9. Otherwise, thanks for calling Deltas Information Technology Service Desk.  Goodbye." src="tcc/dlhd0025_ACSTicketingProceduresMenu_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0025_ACSTicketingProceduresMenu_DM_ree_01">
								<prompt-segments>
									<audiofile text="Once again, the phone number for the Global Assistance Center is 1 8 7 7 4 1 6 6 4 6 1, or 4 0 4 7 1 4 7 9 8 1.  To hear that number again, press 9. Otherwise, thanks for calling Deltas Information Technology Service Desk.  Goodbye." src="tcc/dlhd0025_ACSTicketingProceduresMenu_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
	</dialog>
	<dialog id="ApplicationRoot">
		<session-mapping key="errorCount" value="0" type="String"/>
		<session-mapping key="dataaccessErrorCount" value="0" type="String"/>
		<session-mapping key="badFetchErrorCount" value="0" type="String"/>
		<session-mapping key="LAST_STATE_ID" value="" type="String"/>
		<session-mapping key="alarmCode" value="" type="String"/>
		<session-mapping key="alarmSeverity" value="" type="String"/>
		<session-mapping key="alarmMessage" value="" type="String"/>
		<var name="ALARM_CODE_DATAACCESS" expr="'deltaair.tcc.da.dataaccess'"/>
		<var name="ALARM_CODE_BADFETCH" expr="'deltaair.tcc.fe.badfetch'"/>
		<var name="ALARM_CODE_ERROR" expr="'deltaair.tcc.fe.error'"/>
		<var name="ALARM_CODE_MAX_NOMATCH" expr="'deltaair.tcc.event.nuance.dialog,ndm.maxnomatches'"/>
		<var name="ALARM_CODE_MAX_NOINPUT" expr="'deltaair.tcc.event.nuance.dialog,ndm.maxnoinputs'"/>
		<var name="ALARM_CODE_MAX_NOTOCONFIRMS" expr="'deltaair.tcc.event.nuance.dialog,ndm.maxnotoconfirms'"/>
		<var name="ALARM_CODE_MAX_TURNS" expr="'deltaair.tcc.event.nuance.dialog,ndm.maxturns'"/>
		<var name="ALARM_CODE_MAX_REPEATS" expr="'deltaair.tcc.event.nuance.dialog,ndm.maxrepeats'"/>
		<var name="ALARM_SEVERITY_ERROR" expr="'error'"/>
		<var name="ALARM_SEVERITY_WARN" expr="'warn'"/>
		<session-mapping key="language" value="en-US" type="String"/>
		<session-mapping key="library" value="default" type="String"/>
		<session-mapping key="version" value="0.0" type="String"/>
		<session-mapping key="smEmpID" value="" type="String"/>
		<session-mapping key="result" value="" type="String"/>
		<session-mapping key="returnCode" value="" type="String"/>
		<session-mapping key="menuSelection" value="" type="String"/>
		<session-mapping key="PPRAttemptCount" value="0" type="String"/>
		<session-mapping key="pprNumber" value="" type="String"/>
		<session-mapping key="customMenu" value="" type="String"/>
		<session-mapping key="employeeName" value="" type="String"/>
		<session-mapping key="transferVDN" value="2024354" type="String"/>
		<session-mapping key="departmentNumber" value="" type="String"/>
		<session-mapping key="employeeNumber" value="" type="String"/>
		<session-mapping key="confirm" value="" type="String"/>
		<session-mapping key="playPrompt" value="" type="String"/>
		<session-mapping key="playPromptTTS" value="" type="String"/>
		<session-mapping key="inboundVDN" value="" type="String"/>
		<session-mapping key="portNumber" value="" type="String"/>
		<session-mapping key="callInd" value="" type="String"/>
		<session-mapping key="ani" value="" type="String"/>
		<session-mapping key="dnis" value="" type="String"/>
		<session-mapping key="ucid" value="" type="String"/>
		<session-mapping key="isBusinessHours" value="false" type="Boolean"/>
		<session-mapping key="isHoliday" value="false" type="Boolean"/>
		<session-mapping key="testMode" value="" type="String"/>
		<session-mapping key="pwdResetMsgCounter" value="0" type="String"/>
		<session-mapping key="allowPwdResetXfer" value="false" type="Boolean"/>
		<session-mapping key="generic" value="DEFAULT" type="String"/>
		<session-mapping key="acs" value="ACS" type="String"/>
		<session-mapping key="techOps" value="TECHOPS" type="String"/>
		<session-mapping key="reservations" value="RES" type="String"/>
		<session-mapping key="officeSupport" value="OFFICE" type="String"/>
		<session-mapping key="fltOps" value="FLTOPS" type="String"/>
		<session-mapping key="intlSupport" value="INTL" type="String"/>
		<session-mapping key="regionalElite" value="RE" type="String"/>
		<session-mapping key="mltSupport" value="MLT" type="String"/>
		<session-mapping key="endeavorAir" value="ENDAIR" type="String"/>
		<session-mapping key="air4" value="AIR4" type="String"/>
		<event name="event.nuance.logic.transfer">
			<gotodialog next="Transfer.dvxml"/>
		</event>
		<event name="event.nuance.operator">
			<gotodialog next="Transfer.dvxml"/>
		</event>
		<event name="connection.disconnect.hangup">
			<gotodialog next="Exit.dvxml"/>
		</event>
		<event name="event.nuance.dialog.ndm.internalerror event.nuance.dialog.ndm.no_matching_command_reco_option event.nuance.dialog.ndm.no_matching_success_reco_option">
			<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
			<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
			<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
			<gotodialog next="Transfer.dvxml"/>
		</event>
		<event name="event.nuance.dialog">
			<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
			<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
			<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
			<gotodialog next="Transfer.dvxml"/>
		</event>
		<event name="error.nuance.dataaccess.system">
			<session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1"/>
			<session-mapping key="alarmCode" expr="ALARM_CODE_DATAACCESS"/>
			<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
			<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
			<gotodialog next="Transfer.dvxml"/>
		</event>
		<event name="error.nuance.dataaccess.system">
			<session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1"/>
			<session-mapping key="alarmCode" expr="ALARM_CODE_DATAACCESS"/>
			<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
			<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
			<gotodialog next="Exit.dvxml"/>
		</event>
		<event name="error.nuance.dataaccess.system">
			<exit/>
		</event>
		<event name="event.nuance.dataaccess.business">
			<session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1"/>
			<session-mapping key="alarmCode" expr="ALARM_CODE_DATAACCESS"/>
			<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
			<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
			<gotodialog next="Transfer.dvxml"/>
		</event>
		<event name="event.nuance.dataaccess.business">
			<session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1"/>
			<session-mapping key="alarmCode" expr="ALARM_CODE_DATAACCESS"/>
			<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
			<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
			<gotodialog next="Exit.dvxml"/>
		</event>
		<event name="event.nuance.dataaccess.business">
			<exit/>
		</event>
		<event name="error.badfetch">
			<session-mapping key="badFetchErrorCount" expr="badFetchErrorCount + 1"/>
			<if cond="_message != undefined &amp;&amp; _message != ''">
				<else/>
			</if>
			<session-mapping key="alarmCode" expr="ALARM_CODE_BADFETCH"/>
			<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
			<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
			<gotodialog next="Transfer.dvxml"/>
		</event>
		<event name="error.badfetch">
			<session-mapping key="badFetchErrorCount" expr="badFetchErrorCount + 1"/>
			<if cond="_message != undefined &amp;&amp; _message != ''">
				<else/>
			</if>
			<session-mapping key="alarmCode" expr="ALARM_CODE_BADFETCH"/>
			<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
			<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
			<gotodialog next="Exit.dvxml"/>
		</event>
		<event name="error.badfetch">
			<exit/>
		</event>
		<event cond="errorCount == 0">
			<session-mapping key="errorCount" expr="errorCount + 1"/>
			<if cond="_message != undefined &amp;&amp; _message != ''">
				<else/>
			</if>
			<session-mapping key="alarmCode" expr="ALARM_CODE_BADFETCH"/>
			<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
			<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
			<gotodialog next="Transfer.dvxml"/>
		</event>
		<event cond="errorCount == 1">
			<session-mapping key="errorCount" expr="errorCount + 1"/>
			<if cond="_message != undefined &amp;&amp; _message != ''">
				<else/>
			</if>
			<session-mapping key="alarmCode" expr="ALARM_CODE_BADFETCH"/>
			<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
			<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
			<gotodialog next="Exit.dvxml"/>
		</event>
		<event cond="errorCount &gt;= 1">
			<exit/>
		</event>
	</dialog>
	<dialog id="CollectPPRNumber">
		<data-access-state id="dlhd0008_ValidateBusinessHours_DB">
			<data-access id="ValidateBusinessHours" classname="com.nuance.delta.dataaccess.tcc.ValidateBusinessHours">
				<fetchAudio>percolate</fetchAudio>
				<inputs>
					<input-variable name="ani" mask="false">
                </input-variable>
					<input-variable name="dnis" mask="false">
                </input-variable>
					<input-variable name="ucid" mask="false">
                </input-variable>
				</inputs>
				<outputs>
					<output-variable name="isBusinessHours" mask="false">
                </output-variable>
					<output-variable name="isHoliday" mask="false">
                </output-variable>
					<output-variable name="returnCode" mask="false">
                </output-variable>
					<output-variable name="errorMessage" mask="false">
                </output-variable>
				</outputs>
			</data-access>
			<if cond="ValidateBusinessHours.returnCode == 0">
				<session-mapping key="isBusinessHours" expr="ValidateBusinessHours.isBusinessHours"/>
			</if>
			<action next="dlhd0003_GetPPRNumber_DM"/>
			<event name="error.nuance.dataaccess.system">
				<session-mapping key="alarmCode" expr="ALARM_CODE_DATAACCESS"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<action next="dlhd0003_GetPPRNumber_DM"/>
			</event>
			<event name="event.nuance.dataaccess.business">
				<session-mapping key="alarmCode" expr="ALARM_CODE_DATAACCESS"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<action next="dlhd0003_GetPPRNumber_DM"/>
			</event>
			<event name="error.badfetch">
				<session-mapping key="alarmCode" expr="ALARM_CODE_BADFETCH"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<action next="dlhd0003_GetPPRNumber_DM"/>
			</event>
		</data-access-state>
		<dm-state id="dlhd0003_GetPPRNumber_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="default" next="dlhd0005_GetEmployeeInfo_DB">
					<session-mapping key="PPRAttemptCount" expr="PPRAttemptCount+1"/>
					<session-mapping key="pprNumber" expr="dlhd0003_GetPPRNumber_DM.returnvalue"/>
				</action>
			</success>
			<command>
				<action label="dont_know" next="GenericMenu.dvxml">
					<session-mapping key="customMenu" expr="generic"/>
				</action>
			</command>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="dlhd0003_GetPPRNumber_DM_nm3_01">
						<prompt-segments>
							<audiofile text="I still didnt get that but lets continue." src="tcc/dlhd0003_GetPPRNumber_DM_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
					<prompt id="dlhd0003_GetPPRNumber_DM_sil_01">
						<prompt-segments>
							<audiofile text="..." src="tcc/dlhd0003_GetPPRNumber_DM_sil_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="customMenu" expr="generic"/>
				<gotodialog next="GenericMenu.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="dlhd0003_GetPPRNumber_DM_nm3_01">
						<prompt-segments>
							<audiofile text="I still didnt get that but lets continue." src="tcc/dlhd0003_GetPPRNumber_DM_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="customMenu" expr="generic"/>
				<gotodialog next="GenericMenu.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2" maxrepeats="2"/>
				<grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands_dtmf.jsp">
					<dtmfgrammars filename="dlhd0003_GetPPRNumber_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0003_GetPPRNumber_DM_ini_01" cond="PPRAttemptCount == 0">
								<prompt-segments>
									<audiofile text="Please enter your 9 digit PPR number. If you dont know your PPR number, press pound." src="tcc/dlhd0003_GetPPRNumber_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0003_GetPPRNumber_DM_ree_02" cond="PPRAttemptCount != 0">
								<prompt-segments>
									<audiofile text="Please re-enter your 9 digit PPR number.  If you dont know it, press pound." src="tcc/dlhd0003_GetPPRNumber_DM_ree_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0003_GetPPRNumber_DM_ree_01" cond="PPRAttemptCount == 0">
								<prompt-segments>
									<audiofile text="If you know your 9 digit PPR number, enter it now.  Otherwise, press the pound key." src="tcc/dlhd0003_GetPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0003_GetPPRNumber_DM_ree_02" cond="PPRAttemptCount != 0">
								<prompt-segments>
									<audiofile text="Please re-enter your 9 digit PPR number.  If you dont know it, press pound." src="tcc/dlhd0003_GetPPRNumber_DM_ree_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0003_GetPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="If you know your 9 digit PPR number, enter it now.  Otherwise, press the pound key." src="tcc/dlhd0003_GetPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0003_GetPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="If you know your 9 digit PPR number, enter it now.  Otherwise, press the pound key." src="tcc/dlhd0003_GetPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0003_GetPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="If you know your 9 digit PPR number, enter it now.  Otherwise, press the pound key." src="tcc/dlhd0003_GetPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0003_GetPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="If you know your 9 digit PPR number, enter it now.  Otherwise, press the pound key." src="tcc/dlhd0003_GetPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
		<dm-state id="dlhd0004_ConfirmPPRNumber_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="yes" next="GenericMenu.dvxml">
					<session-mapping key="customMenu" expr="generic"/>
					<session-mapping key="pprNumber" expr="''"/>
				</action>
				<action label="no" next="dummy">
					<if cond="PPRAttemptCount &lt; 2">
						<audio>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_out_02">
								<prompt-segments>
									<audiofile text="Okay. Lets try again." src="tcc/dlhd0004_ConfirmPPRNumber_DM_out_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
						<audio>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_03">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
						<action next="dlhd0003_GetPPRNumber_DM"/>
						<else>
							<audio>
								<prompt id="dlhd0004_ConfirmPPRNumber_DM_out_03">
									<prompt-segments>
										<audiofile text="Lets try this a different way." src="tcc/dlhd0004_ConfirmPPRNumber_DM_out_03.wav"/>
									</prompt-segments>
								</prompt>
							</audio>
							<audio>
								<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_04">
									<prompt-segments>
										<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_04.wav"/>
									</prompt-segments>
								</prompt>
							</audio>
							<session-mapping key="customMenu" expr="generic"/>
							<gotodialog next="GenericMenu.dvxml"/>
						</else>
					</if>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_ni3_01">
						<prompt-segments>
							<audiofile text="I still didnt get that, but lets continue." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_06">
						<prompt-segments>
							<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_06.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="customMenu" expr="generic"/>
				<gotodialog next="GenericMenu.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_nm3_01">
						<prompt-segments>
							<audiofile text="I still didnt get that, but lets continue." src="tcc/dlhd0004_ConfirmPPRNumber_DM_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_05">
						<prompt-segments>
							<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_05.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="customMenu" expr="generic"/>
				<gotodialog next="GenericMenu.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_nm3_01">
						<prompt-segments>
							<audiofile text="I still didnt get that, but lets continue." src="tcc/dlhd0004_ConfirmPPRNumber_DM_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_05">
						<prompt-segments>
							<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_05.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="customMenu" expr="generic"/>
				<gotodialog next="GenericMenu.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0004_ConfirmPPRNumber_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ini_01">
								<prompt-segments>
									<audiofile text="You entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_01">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ini_03">
								<prompt-segments>
									<audiofile text="If thats right, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ini_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="To continue, I need to confirm your PPR number.  I think you entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_02">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_02.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_03">
								<prompt-segments>
									<audiofile text="If thats correct, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="To continue, I need to confirm your PPR number.  I think you entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_02">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_02.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_03">
								<prompt-segments>
									<audiofile text="If thats correct, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="To continue, I need to confirm your PPR number.  I think you entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_02">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_02.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_03">
								<prompt-segments>
									<audiofile text="If thats correct, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="To continue, I need to confirm your PPR number.  I think you entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_02">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_02.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_03">
								<prompt-segments>
									<audiofile text="If thats correct, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="To continue, I need to confirm your PPR number.  I think you entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_02">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_02.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_03">
								<prompt-segments>
									<audiofile text="If thats correct, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
		<data-access-state id="dlhd0005_GetEmployeeInfo_DB">
			<data-access id="GetEmployeeInformation" classname="com.nuance.delta.dataaccess.tcc.GetEmployeeInformation">
				<fetchAudio>percolate</fetchAudio>
				<inputs>
					<input-variable name="pprNumber" mask="false">
                </input-variable>
				</inputs>
				<outputs>
					<output-variable name="customMenu" mask="false">
                </output-variable>
					<output-variable name="employeeNumber" mask="false">
                </output-variable>
					<output-variable name="departmentNumber" mask="false">
                </output-variable>
					<output-variable name="employeeName" mask="false">
                </output-variable>
					<output-variable name="smEmpID" mask="false">
                </output-variable>
					<output-variable name="returnCode" mask="false">
                </output-variable>
					<output-variable name="errorMessage" mask="false">
                </output-variable>
				</outputs>
			</data-access>
			<if cond="GetEmployeeInformation.returnCode == 0">
				<session-mapping key="customMenu" expr="GetEmployeeInformation.customMenu"/>
				<session-mapping key="employeeName" expr="GetEmployeeInformation.employeeName"/>
				<session-mapping key="employeeNumber" expr="GetEmployeeInformation.employeeNumber"/>
				<session-mapping key="departmentNumber" expr="GetEmployeeInformation.departmentNumber"/>
				<session-mapping key="smEmpID" expr="GetEmployeeInformation.smEmpID"/>
				<gotodialog next="GenericMenu.dvxml"/>
				<else>
					<action next="dlhd0004_ConfirmPPRNumber_DM"/>
				</else>
			</if>
			<event name="error.nuance.dataaccess.system">
				<session-mapping key="customMenu" expr="generic"/>
				<session-mapping key="alarmCode" expr="ALARM_CODE_DATAACCESS"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="GenericMenu.dvxml"/>
			</event>
			<event name="event.nuance.dataaccess.business">
				<session-mapping key="customMenu" expr="generic"/>
				<session-mapping key="alarmCode" expr="ALARM_CODE_DATAACCESS"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="GenericMenu.dvxml"/>
			</event>
			<event name="error.badfetch">
				<session-mapping key="customMenu" expr="generic"/>
				<session-mapping key="alarmCode" expr="ALARM_CODE_BADFETCH"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="GenericMenu.dvxml"/>
			</event>
		</data-access-state>
	</dialog>
	<dialog id="EndeavorAirMenu">
		<dm-state id="dlhd0022_EndeavorAirMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="1" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'EndeavorAir_1'"/>
				</action>
				<action label="2" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'EndeavorAir_2'"/>
				</action>
				<action label="3" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'EndeavorAir_3'"/>
				</action>
				<action label="4" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'EndeavorAir_4'"/>
				</action>
				<action label="7" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'EndeavorAir_7'"/>
				</action>
				<action label="8" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'EndeavorAir_8'"/>
				</action>
			</success>
			<command>
				<action label="password" next="PasswordSupport.dvxml">
					<session-mapping key="menuSelection" expr="'EndeavorAir_0'"/>
				</action>
			</command>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0022_EndeavorAirMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0022_EndeavorAirMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0022_EndeavorAirMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0022_EndeavorAirMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0022_EndeavorAirMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0022_EndeavorAirMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0022_EndeavorAirMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0022_EndeavorAirMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0022_EndeavorAirMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0022_EndeavorAirMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0022_EndeavorAirMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0022_EndeavorAirMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0022_EndeavorAirMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
	</dialog>
	<dialog id="Exit">
		<custom-state id="EndApplication">
			<exit/>
			<event>
				<exit/>
			</event>
		</custom-state>
	</dialog>
	<dialog id="FlightOpsMenu">
		<dm-state id="dlhd0016_FlightOpsMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="1" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'FLTOPS_1'"/>
				</action>
				<action label="2" next="dlhd0020_DeviceMenu_DM">
					<session-mapping key="menuSelection" expr="'FLTOPS_2'"/>
				</action>
				<action label="3" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'FLTOPS_3'"/>
				</action>
				<action label="4" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'FLTOPS_4'"/>
				</action>
				<action label="7" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'FLTOPS_7'"/>
				</action>
				<action label="8" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'FLTOPS_8'"/>
				</action>
			</success>
			<command>
				<action label="password" next="PasswordSupport.dvxml">
					<session-mapping key="menuSelection" expr="'FLTOPS_0'"/>
				</action>
			</command>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0016_FlightOpsMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0016_FlightOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For issues with I-Crew, press 1. For tablets and mobile devices, press 2. For issues with computer-based learning systems, 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0016_FlightOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0016_FlightOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For issues with I-Crew, press 1. For tablets and mobile devices, press 2. For issues with computer-based learning systems, 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0016_FlightOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0016_FlightOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For issues with I-Crew, press 1. For tablets and mobile devices, press 2. For issues with computer-based learning systems, 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0016_FlightOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0016_FlightOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For issues with I-Crew, press 1. For tablets and mobile devices, press 2. For issues with computer-based learning systems, 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0016_FlightOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0016_FlightOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For issues with I-Crew, press 1. For tablets and mobile devices, press 2. For issues with computer-based learning systems, 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0016_FlightOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0016_FlightOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For issues with I-Crew, press 1. For tablets and mobile devices, press 2. For issues with computer-based learning systems, 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0016_FlightOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
		<dm-state id="dlhd0020_DeviceMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="1" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'DEVICE_1'"/>
				</action>
				<action label="2" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'DEVICE_2'"/>
				</action>
				<action label="3" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'DEVICE_3'"/>
				</action>
				<action label="4" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'DEVICE_4'"/>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0020_DeviceMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0020_DeviceMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For InFlight Services, press 1.For Flight Ops Electronic Flight Bag Tablets, press, 2.If you are a pilot calling about issues with FliteDeck Pro or Secure Content Locker applications, 3.For all other devices including Delta-issued phones,  press 4." src="tcc/dlhd0020_DeviceMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0020_DeviceMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For InFlight Services, press 1.For Flight Ops Electronic Flight Bag Tablets, press, 2.If you are a pilot calling about issues with FliteDeck Pro or Secure Content Locker applications, 3.For all other devices including Delta-issued phones,  press 4." src="tcc/dlhd0020_DeviceMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0020_DeviceMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For InFlight Services, press 1.For Flight Ops Electronic Flight Bag Tablets, press, 2.If you are a pilot calling about issues with FliteDeck Pro or Secure Content Locker applications, 3.For all other devices including Delta-issued phones,  press 4." src="tcc/dlhd0020_DeviceMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0020_DeviceMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For InFlight Services, press 1.For Flight Ops Electronic Flight Bag Tablets, press, 2.If you are a pilot calling about issues with FliteDeck Pro or Secure Content Locker applications, 3.For all other devices including Delta-issued phones,  press 4." src="tcc/dlhd0020_DeviceMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0020_DeviceMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For InFlight Services, press 1.For Flight Ops Electronic Flight Bag Tablets, press, 2.If you are a pilot calling about issues with FliteDeck Pro or Secure Content Locker applications, 3.For all other devices including Delta-issued phones,  press 4." src="tcc/dlhd0020_DeviceMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0020_DeviceMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For InFlight Services, press 1.For Flight Ops Electronic Flight Bag Tablets, press, 2.If you are a pilot calling about issues with FliteDeck Pro or Secure Content Locker applications, 3.For all other devices including Delta-issued phones,  press 4." src="tcc/dlhd0020_DeviceMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
	</dialog>
	<dialog id="GenericMenu">
		<dm-state id="dlhd0010_GenericMenu_DM" type="CUST">
			<success>
				<action label="ACS" next="dlhd0011_DetermineCustomMenu_DS">
					<session-mapping key="customMenu" expr="acs"/>
				</action>
				<action label="TechOps" next="dlhd0011_DetermineCustomMenu_DS">
					<session-mapping key="customMenu" expr="techOps"/>
				</action>
				<action label="Reservations" next="dlhd0011_DetermineCustomMenu_DS">
					<session-mapping key="customMenu" expr="reservations"/>
				</action>
				<action label="OfficeSupport" next="dlhd0011_DetermineCustomMenu_DS">
					<session-mapping key="customMenu" expr="officeSupport"/>
				</action>
				<action label="FltOps" next="dlhd0011_DetermineCustomMenu_DS">
					<session-mapping key="customMenu" expr="fltOps"/>
				</action>
				<action label="IntlSupport" next="dlhd0011_DetermineCustomMenu_DS">
					<session-mapping key="customMenu" expr="intlSupport"/>
				</action>
				<action label="MLTSupport" next="dlhd0011_DetermineCustomMenu_DS">
					<session-mapping key="customMenu" expr="mltSupport"/>
				</action>
				<action label="EndeavorAir" next="dlhd0011_DetermineCustomMenu_DS">
					<session-mapping key="customMenu" expr="endeavorAir"/>
				</action>
				<action label="RegionalElite" next="dlhd0011_DetermineCustomMenu_DS">
					<session-mapping key="customMenu" expr="regionalElite"/>
				</action>
				<action label="Air4" next="dlhd0011_DetermineCustomMenu_DS">
					<session-mapping key="customMenu" expr="air4"/>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0010_GenericMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. MLT Support, 7. Endeavor Air support, press 8. Or for Air4 support, press 9." src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. Or, for Regional Elite, press 7." src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. MLT Support, 7. Endeavor Air support, press 8. Or for Air4 support, press 9." src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. Or, for Regional Elite, press 7." src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. MLT Support, 7. Endeavor Air support, press 8. Or for Air4 support, press 9." src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. Or, for Regional Elite, press 7." src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. MLT Support, 7. Endeavor Air support, press 8. Or for Air4 support, press 9." src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. Or, for Regional Elite, press 7." src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. MLT Support, 7. Endeavor Air support, press 8. Or for Air4 support, press 9." src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. Or, for Regional Elite, press 7." src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. MLT Support, 7. Endeavor Air support, press 8. Or for Air4 support, press 9." src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. Or, for Regional Elite, press 7." src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
		<decision-state id="dlhd0011_DetermineCustomMenu_DS">
			<if cond="customMenu == generic">
				<action next="dlhd0010_GenericMenu_DM"/>
				<elseif cond="customMenu == regionalElite">
					<session-mapping key="menuSelection" expr="regionalElite"/>
					<gotodialog next="RouteCaller.dvxml"/>
				</elseif>
				<elseif cond="customMenu == acs">
					<gotodialog next="ACSMenu.dvxml"/>
				</elseif>
				<elseif cond="customMenu == techOps">
					<gotodialog next="TechOpsMenu.dvxml"/>
				</elseif>
				<elseif cond="customMenu == reservations">
					<gotodialog next="ReservationsMenu.dvxml"/>
				</elseif>
				<elseif cond="customMenu == officeSupport">
					<gotodialog next="OfficeCampusSupportMenu.dvxml"/>
				</elseif>
				<elseif cond="customMenu == fltOps">
					<gotodialog next="FlightOpsMenu.dvxml"/>
				</elseif>
				<elseif cond="customMenu == intlSupport">
					<gotodialog next="InternationalSupportMenu.dvxml"/>
				</elseif>
				<elseif cond="customMenu == mltSupport">
					<gotodialog next="MLTSupportMenu.dvxml"/>
				</elseif>
				<elseif cond="customMenu == endeavorAir">
					<session-mapping key="menuSelection" expr="endeavorAir"/>
					<gotodialog next="EndeavorAirMenu.dvxml"/>
				</elseif>
				<elseif cond="customMenu == air4">
					<session-mapping key="menuSelection" expr="'Air4'"/>
					<gotodialog next="RouteCaller.dvxml"/>
				</elseif>
				<else>
					<action next="dlhd0010_GenericMenu_DM"/>
				</else>
			</if>
		</decision-state>
	</dialog>
	<dialog id="Goodbye">
		<custom-state id="Goodbye_Form">
			<action next="PromptBeforeTransfer"/>
		</custom-state>
		<custom-state id="PromptBeforeTransfer">
			<field name="ForcePromptQueueBeforeExiting">
				<prompt timeout="50ms"/>
				<grammar version="1.0" xml:lang="en-US" tag-format="semantics/1.0" root="empty">
					<rule id="empty" scope="public">
						<one-of>
							<item>dummy item</item>
						</one-of>
					</rule>
				</grammar>
				<noinput>
					<action next="end"/>
				</noinput>
				<nomatch>
					<action next="end"/>
				</nomatch>
				<action next="end"/>
			</field>
			<catch>
				<action next="end"/>
			</catch>
		</custom-state>
		<custom-state id="end">
			<gotodialog next="Exit.dvxml"/>
		</custom-state>
	</dialog>
	<dialog id="Initialization">
		<custom-state id="Call_Start">
			<session-mapping key="testMode" expr="${param.testMode}"/>
			<assign name="model.appdata.dnis" expr="dnis"/>
			<assign name="model.appdata.ani" expr="ani"/>
			<assign name="model.appdata.callid" expr="ucid"/>
			<assign name="inboundVDN" expr="dnis"/>
			<action next="Logging_Start"/>
		</custom-state>
		<custom-state id="Logging_Start">
			<gotodialog next="Welcome.dvxml"/>
			<event>
				<gotodialog next="Transfer.dvxml"/>
			</event>
		</custom-state>
	</dialog>
	<dialog id="InternationalSupportMenu">
		<dm-state id="dlhd0017_InternationalSupportlMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="1" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'INTL_1'"/>
				</action>
				<action label="2" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'INTL_2'"/>
				</action>
				<action label="3" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'INTL_3'"/>
				</action>
				<action label="4" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'INTL_4'"/>
				</action>
				<action label="7" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'INTL_7'"/>
				</action>
				<action label="8" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'INTL_8'"/>
				</action>
			</success>
			<command>
				<action label="password" next="PasswordSupport.dvxml">
					<session-mapping key="menuSelection" expr="'INTL_0'"/>
				</action>
			</command>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0017_InternationalSupportlMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0017_InternationalSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For Common Use Airport locations, press 1. For SAP and Ariba, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0017_InternationalSupportlMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0017_InternationalSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For Common Use Airport locations, press 1. For SAP and Ariba, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0017_InternationalSupportlMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0017_InternationalSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For Common Use Airport locations, press 1. For SAP and Ariba, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0017_InternationalSupportlMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0017_InternationalSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For Common Use Airport locations, press 1. For SAP and Ariba, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0017_InternationalSupportlMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0017_InternationalSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For Common Use Airport locations, press 1. For SAP and Ariba, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0017_InternationalSupportlMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0017_InternationalSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For Common Use Airport locations, press 1. For SAP and Ariba, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0017_InternationalSupportlMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
	</dialog>
	<dialog id="MLTSupportMenu">
		<dm-state id="dlhd0018_MLTSupportlMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="1" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'MLT_1'"/>
				</action>
				<action label="2" next="dlhd0019_MLTPasswordsMenu_DM">
					<session-mapping key="menuSelection" expr="'MLT_2'"/>
				</action>
				<action label="3" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'MLT_3'"/>
				</action>
				<action label="4" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'MLT_4'"/>
				</action>
				<action label="5" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'MLT_5'"/>
				</action>
				<action label="6" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'MLT_6'"/>
				</action>
				<action label="7" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'MLT_7'"/>
				</action>
				<action label="8" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'MLT_8'"/>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0018_MLTSupportlMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0018_MLTSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For application support, including passwords for Suntrac, GoRes or Web Systems, press 1. For MLT password Issues or RSA token PIN resets, press 2. If multiple users are experiencing an outage, 3. For issues with corporate owned mobile devices, 4. For issues with printers, computer hardware or telephones, 5. For Financial Systems, press 6. For issues with Office 365, press 7. Or, for all other technical issues, press 8." src="tcc/dlhd0018_MLTSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0018_MLTSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For application support, including passwords for Suntrac, GoRes or Web Systems, press 1. For MLT password Issues or RSA token PIN resets, press 2. If multiple users are experiencing an outage, 3. For issues with corporate owned mobile devices, 4. For issues with printers, computer hardware or telephones, 5. For Financial Systems, press 6. For issues with Office 365, press 7. Or, for all other technical issues, press 8." src="tcc/dlhd0018_MLTSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0018_MLTSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For application support, including passwords for Suntrac, GoRes or Web Systems, press 1. For MLT password Issues or RSA token PIN resets, press 2. If multiple users are experiencing an outage, 3. For issues with corporate owned mobile devices, 4. For issues with printers, computer hardware or telephones, 5. For Financial Systems, press 6. For issues with Office 365, press 7. Or, for all other technical issues, press 8." src="tcc/dlhd0018_MLTSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0018_MLTSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For application support, including passwords for Suntrac, GoRes or Web Systems, press 1. For MLT password Issues or RSA token PIN resets, press 2. If multiple users are experiencing an outage, 3. For issues with corporate owned mobile devices, 4. For issues with printers, computer hardware or telephones, 5. For Financial Systems, press 6. For issues with Office 365, press 7. Or, for all other technical issues, press 8." src="tcc/dlhd0018_MLTSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0018_MLTSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For application support, including passwords for Suntrac, GoRes or Web Systems, press 1. For MLT password Issues or RSA token PIN resets, press 2. If multiple users are experiencing an outage, 3. For issues with corporate owned mobile devices, 4. For issues with printers, computer hardware or telephones, 5. For Financial Systems, press 6. For issues with Office 365, press 7. Or, for all other technical issues, press 8." src="tcc/dlhd0018_MLTSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0018_MLTSupportlMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For application support, including passwords for Suntrac, GoRes or Web Systems, press 1. For MLT password Issues or RSA token PIN resets, press 2. If multiple users are experiencing an outage, 3. For issues with corporate owned mobile devices, 4. For issues with printers, computer hardware or telephones, 5. For Financial Systems, press 6. For issues with Office 365, press 7. Or, for all other technical issues, press 8." src="tcc/dlhd0018_MLTSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
		<dm-state id="dlhd0019_MLTPasswordsMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="1" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'PASS_1'"/>
				</action>
				<action label="2" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'PASS_2'"/>
				</action>
				<action label="3" next="PasswordSupport.dvxml">
					<session-mapping key="menuSelection" expr="'PASS_3'"/>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0019_MLTPasswordsMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0019_MLTPasswordsMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For Ops Center passwords press 1. For MLT passwords, press 2. For Delta passwords, press 3." src="tcc/dlhd0019_MLTPasswordsMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0019_MLTPasswordsMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For Ops Center passwords press 1. For MLT passwords, press 2. For Delta passwords, press 3." src="tcc/dlhd0019_MLTPasswordsMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0019_MLTPasswordsMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For Ops Center passwords press 1. For MLT passwords, press 2. For Delta passwords, press 3." src="tcc/dlhd0019_MLTPasswordsMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0019_MLTPasswordsMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For Ops Center passwords press 1. For MLT passwords, press 2. For Delta passwords, press 3." src="tcc/dlhd0019_MLTPasswordsMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0019_MLTPasswordsMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For Ops Center passwords press 1. For MLT passwords, press 2. For Delta passwords, press 3." src="tcc/dlhd0019_MLTPasswordsMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0019_MLTPasswordsMenu_DM_ini_01">
								<prompt-segments>
									<audiofile text="For Ops Center passwords press 1. For MLT passwords, press 2. For Delta passwords, press 3." src="tcc/dlhd0019_MLTPasswordsMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
	</dialog>
	<dialog id="OfficeCampusSupportMenu">
		<dm-state id="dlhd0015_OfficeCampusSupportMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="1" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'OFFICE_1'"/>
				</action>
				<action label="2" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'OFFICE_2'"/>
				</action>
				<action label="3" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'OFFICE_3'"/>
				</action>
				<action label="4" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'OFFICE_4'"/>
				</action>
				<action label="5" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'OFFICE_5'"/>
				</action>
				<action label="7" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'OFFICE_7'"/>
				</action>
				<action label="8" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'OFFICE_8'"/>
				</action>
			</success>
			<command>
				<action label="password" next="PasswordSupport.dvxml">
					<session-mapping key="menuSelection" expr="'OFFICE_0'"/>
				</action>
			</command>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0015_OfficeCampusSupportMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0015_OfficeCampusSupportMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For issues with your computer equipment, press 1. For PC applications, such as Excel or Word, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0015_OfficeCampusSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0015_OfficeCampusSupportMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For issues with your computer equipment, press 1. For PC applications, such as Excel or Word, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0015_OfficeCampusSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0015_OfficeCampusSupportMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For issues with your computer equipment, press 1. For PC applications, such as Excel or Word, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0015_OfficeCampusSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0015_OfficeCampusSupportMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For issues with your computer equipment, press 1. For PC applications, such as Excel or Word, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0015_OfficeCampusSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0015_OfficeCampusSupportMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For issues with your computer equipment, press 1. For PC applications, such as Excel or Word, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0015_OfficeCampusSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0015_OfficeCampusSupportMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For issues with your computer equipment, press 1. For PC applications, such as Excel or Word, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0015_OfficeCampusSupportMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
	</dialog>
	<dialog id="PasswordSupport">
		<play-state id="dlhd0040_PasswordResetMsg_PP">
			<audio>
				<prompt id="dlhd0040_out_01">
					<prompt-segments>
						<audiofile text="Having trouble signing in because you are locked out or forgot your Delta Log-in password? There is no need to contact the IT Service Desk if you are registered for Multi-Factor Authentication or MFA. You can reset your own account by clicking the Trouble Signing In? button on the Deltanet Sign In page and complete the steps. I can wait while you do this." src="tcc/dlhd0040_out_01.wav"/>
					</prompt-segments>
				</prompt>
			</audio>
			<action next="dlhd0041_PasswordResetHold_DM"/>
		</play-state>
		<dm-state id="dlhd0041_PasswordResetHold_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="continue">
					<session-mapping key="pwdResetMsgCounter" expr="pwdResetMsgCounter+1"/>
					<session-mapping key="dlhd0041_reentry" expr="'continue'"/>
					<if cond="pwdResetMsgCounter == 2 || allowPwdResetXfer == true">
						<gotodialog next="RouteCaller.dvxml"/>
						<else>
							<action next="dlhd0040_PasswordResetMsg_PP"/>
						</else>
					</if>
				</action>
			</success>
			<command>
				<action label="repeat">
					<session-mapping key="pwdResetMsgCounter" expr="pwdResetMsgCounter+1"/>
					<session-mapping key="allowPwdResetXfer" value="true" type="Boolean"/>
					<session-mapping key="dlhd0041_reentry" expr="'repeat'"/>
					<if cond="pwdResetMsgCounter == 2">
						<gotodialog next="RouteCaller.dvxml"/>
						<else>
							<action next="dlhd0040_PasswordResetMsg_PP"/>
						</else>
					</if>
				</action>
			</command>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<session-mapping key="allowPwdResetXfer" value="true" type="Boolean"/>
				<session-mapping key="dlhd0041_reentry" expr="'noinput'"/>
				<session-mapping key="dlhd0041_noinputCount" expr="dlhd0041_noinputCount+1"/>
				<if cond="dlhd0041_noinputCount == 1">
					<audio>
						<prompt id="dlhd0041_ni1_01">
							<prompt-segments>
								<audiofile text="If you've successfully reset your account, you may hang up now. Or, press 9 to hear the instructions again. If you're not registered for MFA and want to continue, press any other key." src="tcc/dlhd0041_ni1_01.wav"/>
							</prompt-segments>
						</prompt>
					</audio>
					<action next="dlhd0041_PasswordResetHold_DM"/>
					<elseif cond="dlhd0041_noinputCount == 2">
						<audio>
							<prompt id="dlhd0041_ni2_01">
								<prompt-segments>
									<audiofile text="Press any key to reach an IT Service Desk agent." src="tcc/dlhd0041_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
						<action next="dlhd0041_PasswordResetHold_DM"/>
					</elseif>
					<else>
						<gotodialog next="RouteCaller.dvxml"/>
					</else>
				</if>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<gotodialog next="RouteCaller.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="dlhd0007_SelfService_DM_nm2_01">
						<prompt-segments>
							<audiofile text="All right." src="tcc/dlhd0007_SelfService_DM_nm2_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="RouteCaller.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="0" maxnoinputs="0" maxrepeats="2"/>
				<grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands_dtmf.jsp">
					<dtmfgrammars filename="dlhd0041_PasswordResetHold_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0041_ini_01" cond="dlhd0041_reentry == 'initial'">
								<prompt-segments>
									<audiofile text="To hear the trouble signing in instructions again, press 9." src="tcc/dlhd0041_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0041_sil_01" cond="(dlhd0041_reentry == 'continue' || dlhd0041_reentry == 'repeat') &amp;&amp; allowPwdResetXfer == true">
								<prompt-segments>
									<audiofile text="silence 1000ms" src="tcc/dlhd0041_sil_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0041_ini_03" cond="(dlhd0041_reentry == 'continue' || dlhd0041_reentry == 'repeat') &amp;&amp; allowPwdResetXfer == true">
								<prompt-segments>
									<audiofile text="If you've successfully reset your account, you may hang up now. Or, if you're not registered for MFA and want to continue, press any other key." src="tcc/dlhd0041_ini_03.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0041_hold_music">
								<prompt-segments>
									<audiofile text="Hold music." src="tcc/dlhd0041_hold_music.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1"/>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1"/>
					<noinputprompts count="2"/>
					<nomatchprompts count="1"/>
					<nomatchprompts count="2"/>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
		<dm-state id="dlhd0007_SelfService_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<command>
				<action label="continue" next="RouteCaller.dvxml"/>
			</command>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<gotodialog next="RouteCaller.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="dlhd0007_SelfService_DM_nm2_01">
						<prompt-segments>
							<audiofile text="All right." src="tcc/dlhd0007_SelfService_DM_nm2_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="RouteCaller.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="dlhd0007_SelfService_DM_nm2_01">
						<prompt-segments>
							<audiofile text="All right." src="tcc/dlhd0007_SelfService_DM_nm2_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="RouteCaller.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="1" maxnoinputs="0"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0007_SelfService_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0007_SelfService_DM_ini_01">
								<prompt-segments>
									<audiofile text="If youre not in front of your computer and at the password login screen, please hang up and call us back when you are.  For answers to frequently asked questions, live chat, or to reset your password yourself, you can visit our online Help page by clicking on the Knowledge link located at the top of any DeltaNet home page and selecting Help." src="tcc/dlhd0007_SelfService_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0007_SelfService_DM_sil_01">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0007_SelfService_DM_sil_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0007_SelfService_DM_ini_02">
								<prompt-segments>
									<audiofile text="To hear that again, press 9. Otherwise press pound to continue." src="tcc/dlhd0007_SelfService_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0007_SelfService_DM_ini_01">
								<prompt-segments>
									<audiofile text="If youre not in front of your computer and at the password login screen, please hang up and call us back when you are.  For answers to frequently asked questions, live chat, or to reset your password yourself, you can visit our online Help page by clicking on the Knowledge link located at the top of any DeltaNet home page and selecting Help." src="tcc/dlhd0007_SelfService_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0007_SelfService_DM_ree_01">
								<prompt-segments>
									<audiofile text="To hear that again, press 9. Otherwise, press pound to continue." src="tcc/dlhd0007_SelfService_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="1" maxnoinputs="0"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
	</dialog>
	<dialog id="ReservationsMenu">
		<dm-state id="dlhd0014_ReservationsMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="1" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'RES_1'"/>
				</action>
				<action label="2" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'RES_2'"/>
				</action>
				<action label="3" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'RES_3'"/>
				</action>
				<action label="4" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'RES_4'"/>
				</action>
				<action label="5" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'RES_5'"/>
				</action>
				<action label="7" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'RES_7'"/>
				</action>
				<action label="8" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'RES_8'"/>
				</action>
			</success>
			<command>
				<action label="password" next="PasswordSupport.dvxml">
					<session-mapping key="menuSelection" expr="'RES_0'"/>
				</action>
			</command>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0014_ReservationsMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0014_ReservationsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. If you're currently working with a customer, press 1. If you're a Work At Home agent, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0014_ReservationsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0014_ReservationsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. If you're currently working with a customer, press 1. If you're a Work At Home agent, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0014_ReservationsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0014_ReservationsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. If you're currently working with a customer, press 1. If you're a Work At Home agent, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0014_ReservationsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0014_ReservationsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. If you're currently working with a customer, press 1. If you're a Work At Home agent, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0014_ReservationsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0014_ReservationsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. If you're currently working with a customer, press 1. If you're a Work At Home agent, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0014_ReservationsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0014_ReservationsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. If you're currently working with a customer, press 1. If you're a Work At Home agent, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0014_ReservationsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
	</dialog>
	<dialog id="RouteCaller">
		<data-access-state id="dlhd0030_GetDestinationInfo_DB">
			<data-access id="SendCTIData" classname="com.nuance.delta.dataaccess.tcc.SendCTIData">
				<fetchAudio>percolate</fetchAudio>
				<inputs>
					<input-variable name="ani" mask="false">
                </input-variable>
					<input-variable name="dnis" mask="false">
                </input-variable>
					<input-variable name="callInd" mask="false">
                </input-variable>
					<input-variable name="pprNumber" mask="false">
                </input-variable>
					<input-variable name="menuSelection" mask="false">
                </input-variable>
					<input-variable name="portNumber" mask="false">
                </input-variable>
					<input-variable name="inboundVDN" mask="false">
                </input-variable>
					<input-variable name="employeeNumber" mask="false">
                </input-variable>
					<input-variable name="departmentNumber" mask="false">
                </input-variable>
					<input-variable name="employeeName" mask="false">
                </input-variable>
					<input-variable name="smEmpID" mask="false">
                </input-variable>
				</inputs>
				<outputs>
					<output-variable name="transferVDN" mask="false">
                </output-variable>
					<output-variable name="promptID" mask="false">
                </output-variable>
					<output-variable name="promptTTS" mask="false">
                </output-variable>
					<output-variable name="returnCode" mask="false">
                </output-variable>
					<output-variable name="errorMessage" mask="false">
                </output-variable>
				</outputs>
			</data-access>
			<if cond="SendCTIData.returnCode == 4">
				<session-mapping key="playPrompt" expr="SendCTIData.promptID"/>
				<session-mapping key="playPromptTTS" expr="SendCTIData.promptTTS"/>
				<action next="dlhd0035_PlayInformation_PP"/>
				<else>
					<session-mapping key="transferVDN" expr="SendCTIData.transferVDN"/>
					<if cond="testMode == true || testMode == 'true'">V D N is<prompt>
							<say-as interpret-as="digits">
								<value expr="transferVDN"/>
							</say-as>
						</prompt>
					</if>
				</else>
				<action next="dlhd0031_TransferMessage_PP"/>
			</if>
			<event name="error.nuance.dataaccess.system">
				<session-mapping key="alarmCode" expr="ALARM_CODE_DATAACCESS"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<action next="dlhd0031_TransferMessage_PP"/>
			</event>
			<event name="event.nuance.dataaccess.business">
				<session-mapping key="alarmCode" expr="ALARM_CODE_DATAACCESS"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<action next="dlhd0031_TransferMessage_PP"/>
			</event>
			<event name="error.badfetch">
				<session-mapping key="alarmCode" expr="ALARM_CODE_BADFETCH"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<action next="dlhd0031_TransferMessage_PP"/>
			</event>
		</data-access-state>
		<play-state id="dlhd0031_TransferMessage_PP">
			<audio>
				<prompt id="dlhd0031_TransferMessage_PP_out_01">
					<prompt-segments>
						<audiofile text="Okay.  One moment while I connect you with someone who can help." src="tcc/dlhd0031_TransferMessage_PP_out_01.wav"/>
					</prompt-segments>
				</prompt>
			</audio>
			<action next="FlushPrompts"/>
		</play-state>
		<custom-state id="TransferCaller">
			<gotodialog next="Transfer.dvxml"/>
		</custom-state>
		<play-state id="dlhd0035_PlayInformation_PP">
			<audio>
				<prompt type="custom">
					<param name="className" value="com.nuance.delta.audio.custom.PlayAudioFile"/>
					<param name="audioPath" value="playPrompt"/>
					<param name="tts" value="playPromptTTS"/>
				</prompt>
			</audio>
			<gotodialog next="Exit.dvxml"/>
		</play-state>
	</dialog>
	<dialog id="TechOpsMenu">
		<dm-state id="dlhd0013_TechOpsMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="1" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'TECHOPS_1'"/>
				</action>
				<action label="2" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'TECHOPS_2'"/>
				</action>
				<action label="3" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'TECHOPS_3'"/>
				</action>
				<action label="4" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'TECHOPS_4'"/>
				</action>
				<action label="5" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'TECHOPS_5'"/>
				</action>
				<action label="7" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'TECHOPS_7'"/>
				</action>
				<action label="8" next="RouteCaller.dvxml">
					<session-mapping key="menuSelection" expr="'TECHOPS_8'"/>
				</action>
			</success>
			<command>
				<action label="password" next="PasswordSupport.dvxml">
					<session-mapping key="menuSelection" expr="'TECHOPS_0'"/>
				</action>
			</command>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0013_TechOpsMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0013_TechOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For an issue that is critical to your operation, or affecting a flight's on-time departure, press 1. For issues related to aircraft records, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0013_TechOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0013_TechOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For an issue that is critical to your operation, or affecting a flight's on-time departure, press 1. For issues related to aircraft records, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0013_TechOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0013_TechOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For an issue that is critical to your operation, or affecting a flight's on-time departure, press 1. For issues related to aircraft records, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0013_TechOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0013_TechOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For an issue that is critical to your operation, or affecting a flight's on-time departure, press 1. For issues related to aircraft records, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0013_TechOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0013_TechOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For an issue that is critical to your operation, or affecting a flight's on-time departure, press 1. For issues related to aircraft records, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0013_TechOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0013_TechOpsMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. For an issue that is critical to your operation, or affecting a flight's on-time departure, press 1. For issues related to aircraft records, 2. For SAP and Ariba, 3. For Corporate-owned *mobile* devices, 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0013_TechOpsMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
	</dialog>
	<dialog id="Transfer">
		<custom-state id="dlhd0032_TransferToVDN_PS">
			<session-mapping key="uui" expr="pprNumber + '~' + smEmpID + '~' + employeeNumber"/>
			<action next="SpeakVDN"/>
		</custom-state>
		<custom-state id="SpeakVDN">
			<if cond="testMode == true || testMode == 'true'">The V D N is<prompt>
					<say-as interpret-as="digits">
						<value expr="transferVDN"/>
					</say-as>
				</prompt>
			</if>
			<action next="ExecuteTransfer"/>
		</custom-state>
		<custom-state id="ExecuteTransfer">
			<data-access id="ConvertUUIToHex"/>
			<session-mapping key="uui" expr="ConvertUUIToHex.hexUUI"/>
			<transfer name="call" type="blind" destexpr="'tel:'+transferVDN" connecttimeout="35s" maxtime="36000s" aaiexpr="uui"/>
			<gotodialog next="Exit.dvxml"/>
		</custom-state>
	</dialog>
	<dialog id="Welcome">
		<play-state id="dlhd0002_Welcome_PP">
			<audio>
				<prompt id="dlhd0002_Welcome_PP_out_01">
					<prompt-segments>
						<audiofile text="Welcome to Deltas Information Technology Service Desk" src="tcc/dlhd0002_Welcome_PP_out_01.wav"/>
					</prompt-segments>
				</prompt>
			</audio>
			<audio>
				<prompt id="dlhd0002_Welcome_PP_sil_01">
					<prompt-segments>
						<audiofile text="..." src="tcc/dlhd0002_Welcome_PP_sil_01.wav"/>
					</prompt-segments>
				</prompt>
			</audio>
			<gotodialog next="CollectPPRNumber.dvxml"/>
		</play-state>
	</dialog>
</states-library>