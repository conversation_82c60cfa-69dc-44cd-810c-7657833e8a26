<states-library xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../framework/stateengine/states.xsd">
	<dialog id="enroll">
		<script className="com.nuance.riyadhbank.vocalpassword.script.ResetEnrollmentVariables" />
		<script className="com.nuance.riyadhbank.vocalpassword.script.SetLanguage" />
		<play-state id="Enroll_PP">
			<audio>
				<prompt id="rb0310_ini_12_part1" bargein="false">
					<prompt-segments>
						<audiofile text="To get you set up, I'll ask you to repeat a phrase several times." src="rb0310_ini_12_part1.wav" />
					</prompt-segments>
				</prompt>
			</audio>
			<action next="rb0210_StartEnrollmentSession_DA" />
		</play-state>
		<data-access-state id="rb0210_StartEnrollmentSession_DA">
			<var name="flow" value="enroll" isNamelist="true" />
			<var name="callerId" path="callerId" scope="session" isNamelist="true" />
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<data-access id="StartVocalPasswordSession" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.StartVocalPasswordSession">
				<inputs>
					<input-variable name="callerLanguage" />
					<input-variable name="callerId" />
				</inputs>
			</data-access>
			<action label="success" next="rb0220_deleteVoiceprintArabic_DA" />
			<action label="default" next="rb0410_CleanUpNeeded_DS" />
		</data-access-state>
		<data-access-state id="rb0220_deleteVoiceprintArabic_DA">
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<var name="configSetName" path="arabicConfigSetName" scope="session" isNamelist="true" />
			<var name="vpVoiceprintTag" path="vpArabicVoiceprintTag" scope="session" isNamelist="true" />
			<var name="callerId" path="callerId" scope="session" isNamelist="true" />
			<data-access id="DeleteVoicePrint" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.DeleteVoicePrint" />
			<action label="success" next="rb0230_deleteVoiceprintEnglish_DA" />
			<action label="default" next="rb0410_CleanUpNeeded_DS" />
		</data-access-state>
		<data-access-state id="rb0230_deleteVoiceprintEnglish_DA">
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<var name="configSetName" path="englishConfigSetName" scope="session" isNamelist="true" />
			<var name="vpVoiceprintTag" path="vpEnglishVoiceprintTag" scope="session" isNamelist="true" />
			<var name="callerId" path="callerId" scope="session" isNamelist="true" />
			<data-access id="DeleteVoicePrint" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.DeleteVoicePrint" />
			<action label="success" next="rb0310_Enrollment_DM" />
			<action label="default" next="rb0410_CleanUpNeeded_DS" />
		</data-access-state>
		<dm-state id="rb0310_Enrollment_DM" className="com.nuance.riyadhbank.vocalpassword.stateengine.DMVocalPasswordRecordTag">
			<var name="collectionCounter" path="collectionCounter" scope="session" isNamelist="true" />
			<var name="collectionNoInputCounter" path="collectionNoInputCounter" scope="session" isNamelist="true" />
			<var name="collectionNoMatchCounter" path="collectionNoMatchCounter" scope="session" isNamelist="true" />
			<var name="isComingFrom_rb0330" path="isComingFrom_rb0330" scope="session" isNamelist="true" />
			<var name="collectionNiOrNm_Temp" path="collectionNiOrNm_Temp" scope="session" isNamelist="true" />
			<var name="repromptAfterTrainError" path="repromptAfterTrainError" scope="session" isNamelist="true" />
			<var name="callComesFromAgent" path="callComesFromAgent" scope="session" isNamelist="true" />
			<success>
				<action next="rb0320_SaveEnrollUtterance_DA">
					<session-mapping key="collectionNiOrNm_Temp" value="undefined" type="String" />
					<session-mapping key="collectionNoInputCounter" value="0" type="Integer" />
					<session-mapping key="collectionNoMatchCounter" value="0" type="Integer" />
					<session-mapping key="repromptAfterTrainError" value="false" type="Boolean" />
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="collectionCounter" />
					</script>
					<session-mapping key="isComingFrom_rb0330" value="false" type="Boolean" />
					<var name="callerLanguage" path="callerLanguage" scope="session" />
					<if cond="callerLanguage == 'ar'">
						<session-mapping key="enrollmentLanguage" value="ar" type="String" />
						<elseif cond="callerLanguage == 'en'">
							<session-mapping key="enrollmentLanguage" value="en" type="String" />
						</elseif>
					</if>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<action next="rb0330_CheckEnrollPassphrase_DS">
					<session-mapping key="collectionNiOrNm_Temp" value="nomatch" type="String" />
					<session-mapping key="repromptAfterTrainError" value="false" type="Boolean" />
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="collectionCounter" />
						<param name="param2" value="collectionNoMatchCounter" />
						<param name="param3" value="collectionMaxErrorCounter" />
					</script>
					<var name="callerLanguage" path="callerLanguage" scope="session" />
					<if cond="callerLanguage == 'ar'">
						<session-mapping key="enrollmentLanguage" value="ar" type="String" />
						<elseif cond="callerLanguage == 'en'">
							<session-mapping key="enrollmentLanguage" value="en" type="String" />
						</elseif>
					</if>
				</action>
			</event>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<action next="rb0330_CheckEnrollPassphrase_DS">
					<session-mapping key="collectionNiOrNm_Temp" value="noinput" type="String" />
					<session-mapping key="repromptAfterTrainError" value="false" type="Boolean" />
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="collectionCounter" />
						<param name="param2" value="collectionNoInputCounter" />
						<param name="param3" value="collectionMaxErrorCounter" />
					</script>
					<var name="callerLanguage" path="callerLanguage" scope="session" />
					<if cond="callerLanguage == 'ar'">
						<session-mapping key="enrollmentLanguage" value="ar" type="String" />
						<elseif cond="callerLanguage == 'en'">
							<session-mapping key="enrollmentLanguage" value="en" type="String" />
						</elseif>
					</if>
				</action>
			</event>
			<collection_configuration inputmodes="voice">
				<threshold_configuration maxnomatches="0" maxnoinputs="0" />
				<vxml_properties inputmodes="voice">
					<property_map>
						<key>recordutterance</key>
						<value>true</value>
					</property_map>
				</vxml_properties>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<if cond="collectionCounter == 0 &amp;&amp; callComesFromAgent == true">
								<prompt id="rb0310_ini_12_part2" bargein="false">
									<prompt-segments>
										<audiofile text="So... after the tone please say: 'At Riyad Bank my voice is my password'." src="rb0310_ini_12_part2.wav" />
									</prompt-segments>
								</prompt>
								<elseif cond="collectionCounter == 0 &amp;&amp; callComesFromAgent == false">
									<prompt id="rb0310_ini_13" bargein="false">
										<prompt-segments>
											<audiofile text="So ... after the tone please say: 'At Riyad Bank my voice is my password'. (alternative: Great! To create your voice print, I'm going to ask you to repeat a phrase a few times so the system can analyze your voice.  So, after the tone please say 'With Riyad Bank my voice is my password#)" src="rb0310_ini_13.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'noinput' &amp;&amp; collectionNoInputCounter == 1">
									<prompt id="rb0310_ini_02" bargein="false">
										<prompt-segments>
											<audiofile text="After the tone please say: 'At Riyad Bank my voice is my password'" src="rb0310_ini_02.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'noinput' &amp;&amp; collectionNoInputCounter == 2">
									<prompt id="rb0310_ini_03" bargein="false">
										<prompt-segments>
											<audiofile text="Sorry, I didn't hear that.  After the tone, please repeat the following phrase: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_03.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'nomatch' &amp;&amp; collectionNoMatchCounter == 1">
									<prompt id="rb0310_ini_04" bargein="false">
										<prompt-segments>
											<audiofile text="Let's try that again.  After the tone, please say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_04.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'nomatch' &amp;&amp; collectionNoMatchCounter == 2">
									<prompt id="rb0310_ini_05" bargein="false">
										<prompt-segments>
											<audiofile text="Sorry, it's important you repeat *exactly* the same phrase.  After the tone, say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_05.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; repromptAfterTrainError == true &amp;&amp; collectionCounter &lt; 4">
									<prompt id="rb0310_ini_06" bargein="false">
										<prompt-segments>
											<audiofile text="I'm having some trouble getting that.  Let's try again.  After the tone say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_06.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 1">
									<prompt id="rb0310_ini_07" bargein="false">
										<prompt-segments>
											<audiofile text="Again, after the tone say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_07.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 2">
									<prompt id="rb0310_ini_08" bargein="false">
										<prompt-segments>
											<audiofile text="And again, after the tone: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_08.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 3">
									<prompt id="rb0310_ini_09" bargein="false">
										<prompt-segments>
											<audiofile text="Almost there ... after the tone: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_09.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false">
									<prompt id="rb0310_ini_10" bargein="false">
										<prompt-segments>
											<audiofile text="And last time: At Riyad Bank my voice is my password." src="rb0310_ini_10.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
							</if>
							<prompt id="rb0310_ini_11" bargein="false">
								<prompt-segments>
									<audiofile text="tone" src="rb0310_ini_11.wav" />
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id="" />
					<helpprompts count="2" filename="none" text="none" id="" />
					<repeatprompts count="1" />
					<repeatprompts count="2" />
					<nomatchprefixes count="1" />
					<nomatchprefixes count="2" />
					<nomatchprefixes count="3" />
					<noinputprefixes count="1" />
					<noinputprefixes count="2" />
					<noinputprefixes count="3" />
					<noinputprompts count="1" />
					<nomatchprompts count="1" />
					<notoconfirmprefixes count="2" />
					<notoconfirmprefixes count="3" />
					<notoconfirmprefixes count="1" />
					<notoconfirmprompts count="1" />
					<notoconfirmprefixes count="2" />
					<notoconfirmprompts count="2" />
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxnomatches="1" maxnoinputs="1" />
				<vxml_properties bargein="false" inputmodes="voice" />
				<failureprompt count="1" />
				<commandconfirmationprompts count="1" bargein="false" filename="none" text="none" id="" />
				<successprompts count="1" />
				<successprompts count="2" />
				<successprompts count="3" />
				<successcorrectedprompt count="1" />
			</global_configuration>
			<confirmation_configuration>
				<threshold_configuration maxnomatches="1" maxnoinputs="1" maxnegativeconfirmations="1" />
				<prompt_configuration>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_noinput_confirm1_01" />
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nomatch_confirm1_01" />
						</audio>
					</nomatchprompts>
				</prompt_configuration>
			</confirmation_configuration>
		</dm-state>
		<data-access-state id="rb0320_SaveEnrollUtterance_DA">
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<var name="configSetName" path="configSetName" scope="session" isNamelist="true" />
			<var name="recording" path="recording" isNamelist="true" />
			<data-access id="SaveEnrollUtterance" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.SaveEnrollUtterance" />
			<action label="success" next="rb0330_CheckEnrollPassphrase_DS" />
			<action label="default" next="rb0410_CleanUpNeeded_DS">
				<session-mapping key="result" value="error" type="String" />
			</action>
		</data-access-state>
		<decision-state id="rb0330_CheckEnrollPassphrase_DS">
			<if cond="collectionMaxErrorCounter  &gt; 2">
				<action label="cleanup" next="rb0410_CleanUpNeeded_DS">
					<session-mapping key="result" value="failure" type="String" />
					<session-mapping key="reason" value="maxCollections" type="String" />
				</action>
				<elseif cond="collectionNiOrNm_Temp == 'undefined'">
					<action label="enroll" next="rb0340_Enroll_DA" />
				</elseif>
				<else>
					<action label="retry" next="rb0310_Enrollment_DM">
						<session-mapping key="isComingFrom_rb0330" value="true" type="Boolean" />
					</action>
				</else>
			</if>
		</decision-state>
		<data-access-state id="rb0340_Enroll_DA">
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<var name="configSetName" path="configSetName" scope="session" isNamelist="true" />
			<data-access id="EnrollSpeaker" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.EnrollSpeaker">
				<inputs>
					<input-variable name="vpSessionId" />
					<input-variable name="callerLanguage" />
					<input-variable name="configSetName" />
				</inputs>
			</data-access>
			<action label="default" next="rb0340_Enroll_DA_DS" />
		</data-access-state>
		<decision-state id="rb0340_Enroll_DA_DS">
			<var name="enrollCounter" path="enrollCounter" scope="session" />
			<if cond="vpEnrollStatus == 'READY_FOR_TRAINING'">
				<action next="rb0350_TrainVoiceprint_DA" />
				<elseif cond="(vpEnrollStatus == 'MORE_AUDIO_REQUIRED' &amp;&amp; (collectionCounter &lt; 5 &amp;&amp; enrollCounter &lt; 4))">
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="enrollCounter" />
					</script>
					<action next="rb0310_Enrollment_DM" />
				</elseif>
				<elseif cond="vpEnrollStatus == 'FULL' || collectionCounter &gt;= 5 || enrollCounter == 4">
					<action next="rb0410_CleanUpNeeded_DS">
						<session-mapping key="result" value="failure" type="String" />
						<session-mapping key="reason" value="maxCollections" type="String" />
					</action>
				</elseif>
				<else>
					<action next="rb0410_CleanUpNeeded_DS">
						<session-mapping key="result" value="error" type="String" />
					</action>
				</else>
			</if>
		</decision-state>
		<data-access-state id="rb0350_TrainVoiceprint_DA">
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<var name="configSetName" path="configSetName" scope="session" isNamelist="true" />
			<var name="callerId" path="callerId" scope="session" isNamelist="true" />
			<data-access id="TrainVoiceprint" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.TrainVoiceprint">
				<inputs>
					<input-variable name="vpSessionId" />
					<input-variable name="callerLanguage" />
					<input-variable name="configSetName" />
					<input-variable name="callerId" />
				</inputs>
			</data-access>
			<action label="default" next="rb0350_TrainVoiceprint_DA_DS" />
		</data-access-state>
		<decision-state id="rb0350_TrainVoiceprint_DA_DS">
			<var name="collectionMaxErrorCounter" path="collectionMaxErrorCounter" scope="session" />
			<var name="collectionCounter" path="collectionCounter" scope="session" />
			<var name="trainErrorCounter" path="trainErrorCounter" scope="session" />
			<if cond="vpTrainStatus == 'SUCCEEDED'">
				<session-mapping key="result" value="success" type="String" />
				<action next="rb0410_CleanUpNeeded_DS" />
				<elseif cond="vpTrainStatus != 'SUCCEEDED' &amp;&amp; (collectionMaxErrorCounter &gt; 2 || collectionCounter == 5)">
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="collectionMaxErrorCounter" />
						<param name="param2" value="trainErrorCounter" />
					</script>
					<session-mapping key="result" value="failure" type="String" />
					<session-mapping key="reason" value="maxcollections" type="String" />
					<action next="rb0410_CleanUpNeeded_DS" />
				</elseif>
				<elseif cond="vpTrainStatus != 'SUCCEEDED' &amp;&amp; trainErrorCounter  == 1">
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="collectionMaxErrorCounter" />
						<param name="param2" value="trainErrorCounter" />
					</script>
					<session-mapping key="repromptAfterTrainError" value="true" type="Boolean" />
					<action next="rb0310_Enrollment_DM" />
				</elseif>
				<elseif cond="vpTrainReason  == 'INCONSISTENCY'">
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="collectionMaxErrorCounter" />
						<param name="param2" value="trainErrorCounter" />
					</script>
					<action next="rb0310_Enrollment_DM" />
					<session-mapping key="collectionNiOrNm_Temp" value="undefined" type="String" />
					<session-mapping key="collectionNoInputCounter" value="0" type="Integer" />
					<session-mapping key="collectionNoMatchCounter" value="0" type="Integer" />
				</elseif>
				<else>
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="collectionMaxErrorCounter" />
						<param name="param2" value="trainErrorCounter" />
					</script>
					<session-mapping key="result" value="error" type="String" />
					<action next="rb0410_CleanUpNeeded_DS" />
				</else>
			</if>
		</decision-state>
		<decision-state id="rb0410_CleanUpNeeded_DS">
			<var name="result" path="result" scope="session" />
			<if cond="result  == 'success'">
				<action next="rb0440_EndSession_DA" />
				<else>
					<action next="rb0430_deleteVoiceprintArabic_DA" />
				</else>
			</if>
		</decision-state>
		<data-access-state id="rb0430_deleteVoiceprint_DA">
			<data-access id="DeleteVoicePrint" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.DeleteVoicePrint" />
			<action next="rb0440_EndSession_DA" />
		</data-access-state>
		<data-access-state id="rb0430_deleteVoiceprintArabic_DA">
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<var name="configSetName" path="arabicConfigSetName" scope="session" isNamelist="true" />
			<var name="vpVoiceprintTag" path="vpArabicVoiceprintTag" scope="session" isNamelist="true" />
			<var name="callerId" path="callerId" scope="session" isNamelist="true" />
			<data-access id="DeleteVoicePrint" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.DeleteVoicePrint" />
			<action next="rb0430_deleteVoiceprintEnglish_DA" />
		</data-access-state>
		<data-access-state id="rb0430_deleteVoiceprintEnglish_DA">
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<var name="configSetName" path="englishConfigSetName" scope="session" isNamelist="true" />
			<var name="vpVoiceprintTag" path="vpEnglishVoiceprintTag" scope="session" isNamelist="true" />
			<var name="callerId" path="callerId" scope="session" isNamelist="true" />
			<data-access id="DeleteVoicePrint" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.DeleteVoicePrint" />
			<action next="rb0440_EndSession_DA" />
		</data-access-state>
		<data-access-state id="sd2220_deleteEnrollSegments_DA">
			<data-access id="DeleteCurrentEnrollSegments" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.DeleteCurrentEnrollSegments" />
			<action next="rb0440_EndSession_DA" />
		</data-access-state>
		<data-access-state id="rb0440_EndSession_DA">
			<data-access id="EndVocalPasswordSession" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.EndVocalPasswordSession">
				<inputs>
					<input-variable name="vpSessionId" />
				</inputs>
			</data-access>
			<action label="success" next="EnrollAnnounceResult_PP" />
			<action label="default" next="EnrollAnnounceResult_PP">
				<session-mapping key="result" value="error" type="String" />
			</action>
		</data-access-state>
		<play-state id="rb0450_EnrollAnnounceResult_PP_Success">
			<audio>
				<prompt id="rb0450_out_01">
					<prompt-segments>
						<audiofile text="Your voice password is now registered.  When you call in the future, we'll ask you to say this phrase, so we can make sure that it's you.  And don't worry, you won't need to remember what to say - we'll remind you each time you call. (alternative: Ok, that's it! The next time you call, I'll use your voice to verify your identity)" src="rb0450_out_01.wav" />
					</prompt-segments>
				</prompt>
			</audio>
			<action next="vptransfer" />
		</play-state>
		<play-state id="rb0450_EnrollAnnounceResult_PP_Failure">
			<audio>
				<prompt id="rb0450_out_02">
					<prompt-segments>
						<audiofile text="Sorry, I wasn't able to create a voice password for you. This is often because of background noise or a bad connection. If you'd like to try again later, please just call us back and say 'add my Voice Password'. (alternative: Sorry - I wasn't able to create a voice print for you. This is often because of background noise or a bad connection. If you'd like to try again later, please just call us back and say 'add my Voice Password'.)" src="rb0450_out_02.wav" />
					</prompt-segments>
				</prompt>
			</audio>
			<action next="vptransfer" />
		</play-state>
		<play-state id="rb0450_EnrollAnnounceResult_PP_Error">
			<audio>
				<prompt id="rb0450_out_03">
					<prompt-segments>
						<audiofile text="Sorry - there seem to some technical issues. So I wasn't able to create a voice password for you.If you'd like to try again later, please just call us back and say 'add my Voice Password'." src="rb0450_out_03.wav" />
					</prompt-segments>
				</prompt>
			</audio>
			<action next="vptransfer" />
		</play-state>
		<decision-state id="EnrollAnnounceResult_PP">
			<var name="result" path="result" scope="session" />
			<if cond="result  == 'success'">
				<action next="rb0450_EnrollAnnounceResult_PP_Success" />
				<elseif cond="result == 'failure'">
					<action next="rb0450_EnrollAnnounceResult_PP_Failure" />
				</elseif>
				<else>
					<action next="rb0450_EnrollAnnounceResult_PP_Error" />
				</else>
			</if>
		</decision-state>
	</dialog>
	<dialog id="start">
		<script className="com.nuance.riyadhbank.vocalpassword.script.LogInputData" />
		<script className="com.nuance.riyadhbank.vocalpassword.script.SetConfigDataInSession" />
		<decision-state id="rb0120_WhichApp_DS">
			<var name="speakerId" path="speakerId" scope="session" />
			<var name="action" path="action" scope="session" />
			<var name="callerLanguage" path="callerLanguage" scope="session" />
			<if cond="speakerId == 'undefined' || speakerId == '' || action == 'undefined' || action == '' || callerLanguage == 'undefined' || callerLanguage == ''">
				<action next="vptransfer" />
				<elseif cond="action == 'verification'">
					<action next="verify" />
				</elseif>
				<else>
					<action next="enroll" />
				</else>
			</if>
		</decision-state>
	</dialog>
	<custom-state id="event" className="com.nuance.framework.service.stateengine.state.GlobalEventHandlerState">
		<event name="event" next="transfer" />
	</custom-state>
	<custom-state id="error" className="com.nuance.framework.service.stateengine.state.GlobalEventHandlerState">
		<event name="error" next="transfer">
			<session-mapping key="reason" value="error" type="String" />
		</event>
	</custom-state>
	<custom-state id="disconnect" className="com.nuance.framework.service.stateengine.state.GlobalEventHandlerState">
		<event name="connection.disconnect.transfer" next="exit">
			<session-mapping key="reason" value="transfer" type="String" />
		</event>
	</custom-state>
	<custom-state id="hangup" className="com.nuance.framework.service.stateengine.state.GlobalEventHandlerState">
		<event name="connection.disconnect.hangup" next="exit">
			<session-mapping key="reason" value="hangup" type="String" />
		</event>
		<event name="telephone.disconnect.hangup" next="exit">
			<session-mapping key="reason" value="hangup" type="String" />
		</event>
	</custom-state>
	<custom-state id="transfer">
		<view name="/exit" />
	</custom-state>
	<custom-state id="exit">
		<script className="com.nuance.riyadhbank.vocalpassword.script.UpdateReturnValue" />
		<view name="/exit" />
	</custom-state>
	<dialog id="verify">
		<script className="com.nuance.riyadhbank.vocalpassword.script.ResetVerificationVariables" />
		<script className="com.nuance.riyadhbank.vocalpassword.script.SetLanguage" />
		<data-access-state id="rb0510_StartVerifySession_DA">
			<var name="flow" value="verify" isNamelist="true" />
			<var name="callerId" path="callerId" scope="session" isNamelist="true" />
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<session-mapping key="myMode" value="''" type="String" scope="session" />
			<session-mapping key="promptCounter" value="0" type="Integer" scope="session" />
			<session-mapping key="pinEntered" value="false" type="Boolean" scope="session" />
			<data-access id="StartVocalPasswordSession" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.StartVocalPasswordSession">
				<inputs>
					<input-variable name="callerLanguage" />
					<input-variable name="callerId" />
				</inputs>
			</data-access>
			<action label="success" next="IsTrained_DA" />
			<action label="default" next="rb0550_EndSession_DA">
				<session-mapping key="result" value="error" type="String" />
			</action>
		</data-access-state>
		<data-access-state id="IsTrained_DA">
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<var name="configSetName" path="configSetName" scope="session" isNamelist="true" />
			<data-access id="IsTrained" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.IsTrained">
				<inputs>
					<input-variable name="vpSessionId" />
					<input-variable name="callerLanguage" />
					<input-variable name="configSetName" />
				</inputs>
			</data-access>
			<action label="success" next="Verify_PP" />
			<action label="error" next="rb0550_EndSession_isTrained">
				<session-mapping key="reason" value="NotTrained" type="String" />
				<session-mapping key="result" value="error" type="String" />
			</action>
		</data-access-state>
		<data-access-state id="CloseSeesionBeforeEnroll_DA">
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<data-access id="EndVocalPasswordSession" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.EndVocalPasswordSession">
				<inputs>
					<input-variable name="vpSessionId" />
				</inputs>
			</data-access>
			<session-mapping key="action" value="enrollment" type="String" />
			<action label="success" next="enroll" />
		</data-access-state>
		<play-state id="Verify_PP">
			<audio>
				<prompt id="rb0520_ini_02" bargein="true">
					<prompt-segments>
						<audiofile text="Now, please say after the tone" src="rb0520_ini_02.wav" />
					</prompt-segments>
				</prompt>
			</audio>
			<action next="rb0520_CollectVerificationPhrase_DM" />
		</play-state>
		<dm-state id="rb0520_CollectVerificationPhrase_DM" className="com.nuance.riyadhbank.vocalpassword.stateengine.DMVocalPassword">
			<var name="collection_grammar1" path="passPhraseGrammar" scope="session" isNamelist="true" />
			<var name="collection_grammar" value="rb0520_CollectVerificationPhrase_DM_dtmf.grxml" scope="session" isNamelist="true" />
			<var name="collectionCounter" path="collectionCounter" scope="session" isNamelist="true" />
			<var name="enrollmentLanguage" path="enrollmentLanguage" scope="session" isNamelist="true" />
			<var name="promptCounter" path="promptCounter" scope="session" isNamelist="true" />
			<success>
				<action label="*" next="rb0520_CollectVerificationPhrase_DM_nomatch">
    			</action>
				<action label="default" next="rb0530_SavePassPhrase_DA">
					<session-mapping key="collectionNoInputCounter" value="0" type="Integer" />
					<session-mapping key="collectionNoMatchCounter" value="0" type="Integer" />
					<session-mapping key="myMode" path="rb0520_CollectVerificationPhrase_DM.collection.inputmode" scope="request" />
					<if cond="myMode == 'dtmf' || myMode == 'DTMF'">
						<session-mapping key="result" value="success" type="String" />
						<session-mapping key="collectedPin" path="rb0520_CollectVerificationPhrase_DM.returnvalue" scope="request" type="String" />
						<session-mapping key="pinEntered" value="true" scope="session" />
						<action next="rb0550_EndSession_DA" />
					</if>
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="collectionCounter" />
					</script>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<action next="rb0520_CollectVerificationPhrase_DM_nomatch" />
			</event>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<action next="rb0520_CollectVerificationPhrase_DM_noinput" />
			</event>
			<collection_configuration inputmodes="voice">
				<threshold_configuration maxnomatches="0" maxnoinputs="0" />
				<vxml_properties inputmodes="voice">
					<property_map>
						<key>recordutterance</key>
						<value>true</value>
					</property_map>
				</vxml_properties>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<if cond="collectionCounter == 0 &amp;&amp; enrollmentLanguage == 'en'">
								<prompt id="rb0520_ini_03">
									<prompt-segments>
										<audiofile text="'At Riyad Bank my voice is my password'" src="rb0520_ini_03.wav" />
									</prompt-segments>
								</prompt>
								<prompt id="rb0310_ini_11">
									<prompt-segments>
										<audiofile text="tone" src="rb0310_ini_11.wav" />
									</prompt-segments>
								</prompt>
								<prompt id="silence_1000">
									<prompt-segments>
										<audiofile text="Silence 1000 milliseconds" src="silence_1000.wav" />
									</prompt-segments>
								</prompt>
								<prompt id="rb0520_ini_08">
									<prompt-segments>
										<audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_08.wav" />
									</prompt-segments>
								</prompt>
								<elseif cond="collectionCounter == 0 &amp;&amp; enrollmentLanguage == 'ar'">
									<prompt id="rb0520_ini_04">
										<prompt-segments>
											<audiofile text="Arabic Passphrase" src="rb0520_ini_04.wav" />
										</prompt-segments>
									</prompt>
									<prompt id="rb0310_ini_11">
										<prompt-segments>
											<audiofile text="tone" src="rb0310_ini_11.wav" />
										</prompt-segments>
									</prompt>
									<prompt id="silence_1000">
										<prompt-segments>
											<audiofile text="Silence 1000 milliseconds" src="silence_1000.wav" />
										</prompt-segments>
									</prompt>
									<prompt id="rb0520_ini_08">
										<prompt-segments>
											<audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_08.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="collectionCounter != 0 &amp;&amp; enrollmentLanguage == 'en'">
									<prompt id="rb0520_ini_05">
										<prompt-segments>
											<audiofile text="One more time please: ..." src="rb0520_ini_05.wav" />
										</prompt-segments>
									</prompt>
									<prompt id="rb0520_ini_06">
										<prompt-segments>
											<audiofile text="'At Riyad Bank my voice is my password'" src="rb0520_ini_06.wav" />
										</prompt-segments>
									</prompt>
									<prompt id="rb0310_ini_11">
										<prompt-segments>
											<audiofile text="tone" src="rb0310_ini_11.wav" />
										</prompt-segments>
									</prompt>
									<prompt id="silence_1000">
										<prompt-segments>
											<audiofile text="Silence 1000 milliseconds" src="silence_1000.wav" />
										</prompt-segments>
									</prompt>
									<prompt id="rb0520_ini_09">
										<prompt-segments>
											<audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_09.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="collectionCounter != 0 &amp;&amp; enrollmentLanguage == 'ar'">
									<prompt id="rb0520_ini_05">
										<prompt-segments>
											<audiofile text="One more time please: ..." src="rb0520_ini_05.wav" />
										</prompt-segments>
									</prompt>
									<prompt id="rb0520_ini_07">
										<prompt-segments>
											<audiofile text="Arabic Passphrase" src="rb0520_ini_07.wav" />
										</prompt-segments>
									</prompt>
									<prompt id="rb0310_ini_11">
										<prompt-segments>
											<audiofile text="tone" src="rb0310_ini_11.wav" />
										</prompt-segments>
									</prompt>
									<prompt id="silence_1000">
										<prompt-segments>
											<audiofile text="Silence 1000 milliseconds" src="silence_1000.wav" />
										</prompt-segments>
									</prompt>
									<prompt id="rb0520_ini_09">
										<prompt-segments>
											<audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_09.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
							</if>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id="" />
					<helpprompts count="2" filename="none" text="none" id="" />
					<repeatprompts count="1" />
					<repeatprompts count="2" />
					<nomatchprefixes count="1" />
					<nomatchprefixes count="2" />
					<nomatchprefixes count="3" />
					<noinputprefixes count="1" />
					<noinputprefixes count="2" />
					<noinputprefixes count="3" />
					<noinputprompts count="1" />
					<nomatchprompts count="1" />
					<notoconfirmprefixes count="2" />
					<notoconfirmprefixes count="3" />
					<notoconfirmprefixes count="1" />
					<notoconfirmprompts count="1" />
					<notoconfirmprefixes count="2" />
					<notoconfirmprompts count="2" />
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxnomatches="1" maxnoinputs="1" />
				<vxml_properties bargein="true" inputmodes="voice" termtimeout="0ms" />
				<failureprompt count="1" />
				<commandconfirmationprompts count="1" bargein="false" filename="none" text="none" id="" />
				<successprompts count="1" />
				<successprompts count="2" />
				<successprompts count="3" />
				<successcorrectedprompt count="1" />
			</global_configuration>
			<confirmation_configuration>
				<threshold_configuration maxnomatches="1" maxnoinputs="1" maxnegativeconfirmations="1" />
				<prompt_configuration>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_noinput_confirm1_01" />
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nomatch_confirm1_01" />
						</audio>
					</nomatchprompts>
				</prompt_configuration>
			</confirmation_configuration>
		</dm-state>
		<decision-state id="rb0520_CollectVerificationPhrase_DM_nomatch">
			<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
				<param name="param1" value="collectionCounter" />
				<param name="param2" value="collectionNoMatchCounter" />
			</script>
			<var name="collectionNoMatchCounter" path="collectionNoMatchCounter" scope="session" />
			<if cond="collectionNoMatchCounter == 2">
				<session-mapping key="result" value="failure" type="String" />
				<session-mapping key="reason" value="maxCollections" type="String" />
				<action next="rb0550_EndSession_DA" />
				<else>
					<action next="rb0520_CollectVerificationPhrase_DM" />
				</else>
			</if>
		</decision-state>
		<decision-state id="rb0520_CollectVerificationPhrase_DM_noinput">
			<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
				<param name="param2" value="collectionNoInputCounter" />
			</script>
			<var name="collectionNoInputCounter" path="collectionNoInputCounter" scope="session" />
			<if cond="collectionNoInputCounter == 1">
				<action next="Verify_PP" />
				<elseif cond="collectionNoInputCounter == 2">
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="collectionCounter" />
					</script>
					<action next="rb0520_CollectVerificationPhrase_DM" />
				</elseif>
				<else>
					<session-mapping key="result" value="failure" type="String" />
					<session-mapping key="reason" value="maxCollections" type="String" />
					<action next="rb0550_EndSession_DA" />
				</else>
			</if>
		</decision-state>
		<data-access-state id="rb0530_SavePassPhrase_DA">
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<var name="configSetName" path="configSetName" scope="session" isNamelist="true" />
			<data-access id="SavePassPhrase" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.SavePassPhrase">
				<inputs>
					<input-variable name="vpSessionId" />
					<input-variable name="callerLanguage" />
					<input-variable name="configSetName" />
				</inputs>
			</data-access>
			<action label="success" next="rb0540_VerifySpeaker_DA" />
			<action next="rb0550_EndSession_DA">
				<session-mapping key="result" value="error" type="String" />
			</action>
		</data-access-state>
		<data-access-state id="rb0540_VerifySpeaker_DA">
			<var name="callerLanguage" path="callerLanguage" scope="session" isNamelist="true" />
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<var name="configSetName" path="configSetName" scope="session" isNamelist="true" />
			<data-access id="VerifySpeaker" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.VerifySpeaker">
				<inputs>
					<input-variable name="vpSessionId" />
					<input-variable name="callerLanguage" />
					<input-variable name="configSetName" />
				</inputs>
			</data-access>
			<action label="default" next="rb0540_VerifySpeaker_DA_DS" />
		</data-access-state>
		<decision-state id="rb0540_VerifySpeaker_DA_DS">
			<var name="result" path="result" scope="session" />
			<if cond="vpVerifyDecision == 'MATCH'">
				<session-mapping key="result" value="success" type="String" />
				<session-mapping key="reason" value="voiceprintMatch" type="String" />
				<action next="rb0550_EndSession_DA" />
				<elseif cond="vpVerifyDecision == 'FAILURE'">
					<session-mapping key="result" value="failure" type="String" />
					<action next="rb0550_EndSession_DA" />
				</elseif>
				<elseif cond="collectionCounter  &lt; 2">
					<action next="rb0520_CollectVerificationPhrase_DM" />
				</elseif>
				<elseif cond="vpVerifyReason == 'VOICE_PRINT_MISMATCH'">
					<session-mapping key="reason" value="voiceprintMismatch" type="String" />
					<session-mapping key="result" value="failure" type="String" />
					<action next="rb0550_EndSession_DA" />
				</elseif>
				<elseif cond="vpVerifyReason == 'AUDIO_TOO_SHORT' || vpVerifyReason == 'AUDIO_TOO_LOUD' || vpVerifyReason == 'AUDIO_TOO_NOISY' || vpVerifyReason == 'AUDIO_TOO_SOFT'">
					<session-mapping key="reason" value="audioIntegrity" type="String" />
					<session-mapping key="result" value="failure" type="String" />
					<action next="rb0550_EndSession_DA" />
				</elseif>
				<elseif cond="vpVerifyReason == 'SYNTHETIC_SPEECH_DETECTED'">
					<session-mapping key="reason" value="syntheticSpeechDetected" type="String" />
					<session-mapping key="result" value="failure" type="String" />
					<action next="rb0550_EndSession_DA" />
				</elseif>
				<elseif cond="vpVerifyReason == 'PLAY_BACK_DETECTION'">
					<session-mapping key="reason" value="syntheticSpeechDetected" type="String" />
					<session-mapping key="result" value="failure" type="String" />
					<action next="rb0550_EndSession_DA" />
				</elseif>
				<else>
					<session-mapping key="reason" value="maxCollections" type="String" />
					<session-mapping key="result" value="failure" type="String" />
					<action next="rb0550_EndSession_DA" />
				</else>
			</if>
		</decision-state>
		<data-access-state id="rb0550_EndSession_DA">
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<data-access id="EndVocalPasswordSession" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.EndVocalPasswordSession">
				<inputs>
					<input-variable name="vpSessionId" />
				</inputs>
			</data-access>
			<action label="success" next="Verify_AnnounceResult_PP" />
			<action label="default" next="Verify_AnnounceResult_PP">
				<session-mapping key="result" value="error" type="String" />
			</action>
		</data-access-state>
		<data-access-state id="rb0550_EndSession_isTrained">
			<var name="vpSessionId" path="vpSessionId" scope="session" isNamelist="true" />
			<data-access id="EndVocalPasswordSession" classname="com.nuance.riyadhbank.vocalpassword.dataaccess.EndVocalPasswordSession">
				<inputs>
					<input-variable name="vpSessionId" />
				</inputs>
			</data-access>
			<action label="success" next="vptransfer" />
			<action label="default" next="vptransfer">
				<session-mapping key="result" value="error" type="String" />
			</action>
		</data-access-state>
		<play-state id="rb0560_Verify_AnnounceResult_PP_Success">
			<audio>
				<prompt id="rb0560_out_01">
					<prompt-segments>
						<audiofile text="Thanks. You are verified (maybe only 'Thanks' is better)" src="rb0560_out_01.wav" />
					</prompt-segments>
				</prompt>
			</audio>
			<action next="vptransfer" />
		</play-state>
		<play-state id="rb0560_Verify_AnnounceResult_PP_Failure">
			<audio>
				<prompt id="rb0560_out_02">
					<prompt-segments>
						<audiofile text="Thanks (maybe in this case instead 'Okay')" src="rb0560_out_02.wav" />
					</prompt-segments>
				</prompt>
			</audio>
			<action next="vptransfer" />
		</play-state>
		<decision-state id="Verify_AnnounceResult_PP">
			<var name="result" path="result" scope="session" />
			<if cond="result  == 'success' &amp;&amp; pinEntered == false">
				<action next="rb0560_Verify_AnnounceResult_PP_Success" />
				<elseif cond="result  == 'success'">
					<action next="vptransfer" />
				</elseif>
				<else>
					<action next="rb0560_Verify_AnnounceResult_PP_Failure" />
				</else>
			</if>
		</decision-state>
	</dialog>
	<dialog id="vptransfer">
		<play-state id="rb0910_PlayTransferMessage_PP">
			<audio>
				<prompt id="rb0910_out_01">
					<prompt-segments>
						<audiofile text="silence" src="rb0910_out_01.wav" />
					</prompt-segments>
				</prompt>
			</audio>
			<action next="exit" />
		</play-state>
	</dialog>
</states-library>