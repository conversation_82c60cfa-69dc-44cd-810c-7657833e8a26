<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="accountNumber" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="GlobalVars.callType" value="auto_pay" type="string"/>
  <session-mapping key="GlobalVars.extensionEntryPoint" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.attemptedCheckUnhotlineEligibility" value="undefined" type="string"/>
  <session-mapping key="dueImmediatelyAmount" value="3" type="integer"/>
  <session-mapping key="aniMatch" value="false" type="boolean"/>
  <session-mapping key="isLoggedIn" value="false" type="boolean"/>
  <session-mapping key="switchLinesSuccess" value="true" type="boolean"/>
  <session-mapping key="isEligibleForUnhotlineCheck" value="true" type="boolean"/>
  <session-mapping key="CheckUnhotlineEligibility.isEligible" value="true" type="boolean"/>
  <session-mapping key="numberOfCases" value="0" type="integer"/>
  <session-mapping key="GetBCSParameters.care_nlu_enabled" value="true" type="boolean"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="GlobalVars.paymentFailure" value="true" type="boolean"/>
  <session-mapping key="extensionAllowed" value="true" type="boolean"/>
  <session-mapping key="eligibleForBillCycleReset" value="true" type="boolean"/>
  <session-mapping key="pay_now" value="lastresult" type="string"/>
  <session-mapping key="change_plan" value="lastresult" type="string"/>
  <session-mapping key="autopay" value="lastresult" type="string"/>
  <session-mapping key="isAutopayEnabled" value="true" type="string"/>
  <session-mapping key="billcycle_reset" value="lastresult" type="string"/>
  <session-mapping key="switch_lines" value="lastresult" type="string"/>
  <session-mapping key="extension" value="lastresult" type="string"/>
  <session-mapping key="switch_account" value="lastresult" type="string"/>
  <session-mapping key="firstTimeAt1030" value="true" type="boolean"/>
  <session-mapping key="tag" value="vague-autopay" type="string"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="active" type="string"/>
</session-mappings>
