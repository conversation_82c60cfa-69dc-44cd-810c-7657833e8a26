memorize below yaml format

- kind: HttpRequestAction
  id: rb0210_StartEnrollmentSession_DA
  displayName: rb0210_StartEnrollmentSession_DA
  method: Post
  url: DATA_ACCESS_URL/LookupInitCDP
  headers:
    Authorization: Basic YWRtaW46YWRtaW4xMjM=
    Content-Type: application/json
    Cookie: =Concatenate("SESSION=",Global.backendSessionId)
    password: =Global.password
    username: =Global.username
  
  body:
    kind: JsonRequestContent
    content: 
      kind: ValueExpression
      literalValue:
        ani: Global.ani
        dnis: Global.dnis
        ucid: Global.ucid
  requestTimeoutInMilliseconds: 30000
  response: Topic.result_from_rb0210_StartEnrollmentSession_DA
  responseSchema: Any

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.pkk
  value: =Topic.result_from_rb0210_StartEnrollmentSession_DA.pkk

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.returnCode
  value: =Topic.result_from_rb0210_StartEnrollmentSession_DA.returnCode

- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0220_deleteVoiceprintArabic_DA

prefix = topic	
url_prefix = DATA_ACCESS_URL/LookupInitCDP

also use these instruction while conversion 
1) kind should always be "HttpRequestAction"  
2) replace "id" and "displayName" value with "data-access-state" tag id.
3) method should always be "post". 
4) append "data-access" tag id in url_prefix for "url" like below.
if "data-access" tag is LookupInitCDP then url will be
DATA_ACCESS_URL/LookupInitCDP
5) pick the "input" tag "name" value and make it in below format and put it under "literalValue" with same format. kind should always be "ValueExpression".

Input :
        <inputs>
            <input-variable name="msisdn" mask="false" />
            <input-variable name="simnumber" mask="false" />
            <input-variable name="brandId" mask="false" />
            <input-variable name="iConnId" mask="false" />
        </inputs>
output : 
    content: 
      kind: ValueExpression
      literalValue:
        msisdn: Global.msisdn
        simnumber: Global.simnumber
        brandId: Global.brandId
        iConnId: Global.iConnId
        
6) assign "data-access-state" tag id into respose with Topic.result_ prefix like below.
response: Topic.result_from_rb0210_StartEnrollmentSession_DA
7) "responseSchema" kind should always be "Any".
8) pick the "output-variable" tag's "name" value and and set it into SetVariable and make it in below format. put the "name" value into variable with Global. prefix and in "value" put a "response" value with name value like, if response: Topic.result_from_rb0210_StartEnrollmentSession_DA then value will be value: Topic.result_from_rb0210_StartEnrollmentSession_DA.output_variable_name
Input :
        <outputs>
            <output-variable name="pkk" mask="false" />
            <output-variable name="returnCode" mask="false" />
        </outputs>
Output:
- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.pkk
  value: =Topic.result_from_rb0210_StartEnrollmentSession_DA.pkk

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.returnCode
  value: =Topic.result_from_rb0210_StartEnrollmentSession_DA.returnCode

9) if there are multiple "next" then pick the 'label="success"' next value, else pick "default" label "next" value or existing "next" value and put it in "actionId".
 - kind: GotoAction
   id: goto_REPLACE_THIS
   actionId: rb0410_CleanUpNeeded_DS
10) replace "GotoAction" id with "goto_REPLACE_THIS" .
11) If the "next" value of "action" tag ends with _DM, _DS, _DA,_DB or _PP, output this format:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0410_CleanUpNeeded_DS

actionId should be next id.
Replace "GotoAction" id with "goto_REPLACE_THIS".

Input:
<action next="rb0520_CollectVerificationPhrase_DM" />
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0520_CollectVerificationPhrase_DM

12) If the next value ends with _Dialog, output this format:
- kind: BeginDialog
  id: begin_REPLACE_THIS
  dialog: topic.enroll_Dialog

"dialog" should be "next" value with prefix.
Replace "BeginDialog" id with "begin_REPLACE_THIS".

Output should be generated based on the next value alone, without considering the label like below.
Example Input:
<action label="retry" next="rb0310_Enrollment_DM">
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0310_Enrollment_DM

13) indentation for next yaml should be same as first one or as memorized yaml
14) do not provide duplicate yaml.. generate only for provided inputs.

### Example 1:
**Input XML:**
```xml
<data-access-state id="rb0210_StartEnrollmentSession_DA">
    <script className="com.nuance.ps.telefonica.scripts.KPIAddNode" />
    <var name="msisdn" path="msisdn" scope="session" isNamelist="true" />
    <var name="simnumber" path="simnumber" scope="session" isNamelist="true" />
    <var name="brandId" path="urBrand" scope="session" isNamelist="true" />
    <var name="iConnId" path="urFirstConnId" scope="session" isNamelist="true" />
    <data-access id="LookupInitCDP" classname="com.nuance.ps.telefonica.dataaccess.LookupInitCDP">
        <inputs>
            <input-variable name="msisdn" mask="false" />
            <input-variable name="simnumber" mask="false" />
            <input-variable name="brandId" mask="false" />
            <input-variable name="iConnId" mask="false" />
        </inputs>
        <outputs>
            <output-variable name="pkk" mask="false" />
            <output-variable name="returnCode" mask="false" />
        </outputs>
    </data-access>
    <action label="success" next="rb0220_deleteVoiceprintArabic_DA" />
    <action label="default" next="rb0410_CleanUpNeeded_DS" />
</data-access-state>
```

**Output yaml:**
```yaml
- kind: HttpRequestAction
  id: rb0210_StartEnrollmentSession_DA
  displayName: rb0210_StartEnrollmentSession_DA
  method: Post
  url: DATA_ACCESS_URL/LookupInitCDP
  headers:
    Content-Type: application/json
    Authorization: Basic YWRtaW46YWRtaW4xMjM=
    Content-Type: application/json
    Cookie: =Concatenate("SESSION=",Global.backendSessionId)
    password: =Global.password
    username: =Global.username
  body:
    kind: JsonRequestContent
    content: 
      kind: ValueExpression
      literalValue:
          msisdn: Global.msisdn
          simnumber: Global.simnumber
          brandId: Global.brandId
          iConnId: Global.iConnId
        
  requestTimeoutInMilliseconds: 30000
  response: Topic.result_from_rb0210_StartEnrollmentSession_DA
  responseSchema: Any

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.pkk
  value: =Topic.result_from_rb0210_StartEnrollmentSession_DA.pkk

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.returnCode
  value: =Topic.result_from_rb0210_StartEnrollmentSession_DA.returnCode
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0220_deleteVoiceprintArabic_DA
```
