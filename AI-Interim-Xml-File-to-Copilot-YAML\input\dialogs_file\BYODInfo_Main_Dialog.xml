<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="BYODInfo_Main_Dialog">
    <decision-state id="BI1001_CheckContext_DS">
      <if cond="GlobalVars.acceptedAPNSMS == true">
        <action next="BI1115_SendAPNSMS_DB_DA"/>
        <else>
          <action next="BI1005_PlayBYODInfoTransition_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="BI1005_PlayBYODInfoTransition_PP">
      <audio>
        <prompt id="BI1005_out_01">
          <prompt-segments>
            <audiofile text="Since you're bringing your own phone there's a few things to know" src="BI1005_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BI1010_PlayMyMetroInfo_PP"/>
    </play-state>

    <play-state id="BI1010_PlayMyMetroInfo_PP">
      <session-mapping key="deviceType" value="GlobalVars.deviceType" type="String"/>
      <audio>
        <prompt id="BI1010_out_01" cond="deviceType == 'BYOD-ANDROID'">
          <prompt-segments>
            <audiofile text="First, we recommend you get the myMetro app from the Google Play Store for your new phone" src="BI1010_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BI1010_out_02" cond="deviceType != 'BYOD-ANDROID'">
          <prompt-segments>
            <audiofile text="First, we recommend you get the myMetro app from the App Store for your new phone" src="BI1010_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BI1010_out_03">
          <prompt-segments>
            <audiofile text="That way, you'll be able to get important notifications, troubleshoot, and manage your account on the fly!" src="BI1010_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BI1105_PlayAPNSMSTransition_PP"/>
    </play-state>

    <play-state id="BI1105_PlayAPNSMSTransition_PP">
      <session-mapping key="deviceType" value="GlobalVars.deviceType" type="String"/>
      <if cond="(deviceType == 'BYOD-IPHONE')">
        <audio>
          <prompt id="BI1105_out_02">
            <prompt-segments>
              <audiofile text="Second, if you have problems getting a data connection, you might need to reset your network settings That's under Settings, General, Reset" src="BI1105_out_02.wav"/>
            </prompt-segments>
          </prompt>
        </audio>
        <action next="getReturnLink()"/>
        <else>
          <audio>
            <prompt id="BI1105_out_01">
              <prompt-segments>
                <audiofile text="Second" src="BI1105_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="BI1110_OfferAPNSMSYN_DM"/>
        </else>
      </if>
    </play-state>

    <dm-state id="BI1110_OfferAPNSMSYN_DM" type="YSNO">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <action label="true">
          <if cond="language == 'en-US'">
            <session-mapping key="GlobalVars.languageCode" expr="'en-US'"/>
            <else>
              <session-mapping key="GlobalVars.languageCode" expr="'es-US'"/>
            </else>
          </if>
          <if cond="GlobalVars.callType == 'activate'">
            <audio>
              <prompt id="BI1110_out_02">
                <prompt-segments>
                  <audiofile text="All right, I'll send that when your phone's activated" src="BI1110_out_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <session-mapping key="GlobalVars.acceptedAPNSMS" value="true" type="Boolean"/>
            <action next="BI1135_PlayAPNWebInfo_PP"/>
            <else>
              <action next="BI1115_SendAPNSMS_DB_DA"/>
            </else>
          </if>
        </action>
        <action label="false">
          <audio>
            <prompt id="BI1110_out_01">
              <prompt-segments>
                <audiofile text="No problem" src="BI1110_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="BI1135_PlayAPNWebInfo_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BI1110_ini_01">
                <prompt-segments>
                  <audiofile text="You'll probably need to change the APN settings on your new phone to make it connect to our network" src="BI1110_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BI1110_ini_03" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="Would you like me to text you all the info for that to your new phone, after we activate it?" src="BI1110_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BI1110_ini_02" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="Would you like me to text you all the info for that? " src="BI1110_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BI1110_ini_01">
                <prompt-segments>
                  <audiofile text="You'll probably need to change the APN settings on your new phone to make it connect to our network" src="BI1110_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BI1110_ini_03" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="Would you like me to text you all the info for that to your new phone, after we activate it?" src="BI1110_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BI1110_ini_02" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="Would you like me to text you all the info for that? " src="BI1110_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BI1110_ni1_01">
                <prompt-segments>
                  <audiofile text="Should I text you the information you need to change your APN settings? Say 'yes' or press 1, or 'no' or press 2" src="BI1110_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BI1110_nm1_01">
                <prompt-segments>
                  <audiofile text="Should I text you the information you need to change your APN settings? Say 'yes' or press 1, or 'no' or press 2" src="BI1110_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BI1110_ini_01">
                <prompt-segments>
                  <audiofile text="You'll probably need to change the APN settings on your new phone to make it connect to our network" src="BI1110_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BI1110_ini_03" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="Would you like me to text you all the info for that to your new phone, after we activate it?" src="BI1110_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BI1110_ini_02" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="Would you like me to text you all the info for that? " src="BI1110_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="BI1110_OfferAPNSMSYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="BI1110_OfferAPNSMSYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="BI1115_SendAPNSMS_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn != undefined ? GlobalVars.mdn  : GlobalVars.trn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="campaignId" value="GlobalVars.GetBCSParameters.apn_info_campaign_id" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <data-access id="SendTextMessage" classname="com.nuance.metro.dataaccess.SendTextMessage">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="mdn"/>
          <input-variable name="campaignId"/>
          <input-variable name="languageCode"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.SendTextMessage" expr="SendTextMessage"/>
        <action next="BI1120_CheckAPNSMSResult_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="BI1120_CheckAPNSMSResult_DS">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <if cond="(GlobalVars.SendTextMessage &amp;&amp; GlobalVars.SendTextMessage.status &amp;&amp; GlobalVars.SendTextMessage.status.toUpperCase() == 'SUCCESS')">
        <if cond="GlobalVars.callType == 'activate'">
          <session-mapping key="GlobalVars.APNSMSSuccess" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
          <else>
            <action next="BI1125_PlayAPNSMSSuccess_PP"/>
          </else>
        </if>
        <else>
          <if cond="GlobalVars.callType == 'activate'">
            <action next="getReturnLink()"/>
            <else>
              <action next="BI1130_PlayAPNSMSFail_PP"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="BI1125_PlayAPNSMSSuccess_PP">
      <audio>
        <prompt id="BI1125_out_01">
          <prompt-segments>
            <audiofile text="It's on its way It could arrive on your current phone *now*, or your new phone in a few minutes" src="BI1125_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BI1135_PlayAPNWebInfo_PP"/>
    </play-state>

    <play-state id="BI1130_PlayAPNSMSFail_PP">
      <audio>
        <prompt id="BI1130_out_01">
          <prompt-segments>
            <audiofile text="I *can't* seem to get that message through Sorry about that!" src="BI1130_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BI1135_PlayAPNWebInfo_PP"/>
    </play-state>

    <play-state id="BI1135_PlayAPNWebInfo_PP">
      <audio>
        <prompt id="BI1135_out_01">
          <prompt-segments>
            <audiofile text="You can also find that information at metro by T dash mobile dot com Just search for A-P-N settings in the search bar up top  Make sure you check those settings, or you might not be able to get a data connection" src="BI1135_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

  </dialog>
  