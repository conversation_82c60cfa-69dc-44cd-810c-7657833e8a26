import xml.etree.ElementTree as ET
import os
from llm_response import convertXmltoYaml
import logging
from util import read_filenames_in_folder, delete_existing_files, log_failed_file, remove_null_from_action
from indentation import topic_yaml_indentation
import llm_response
from ruamel.yaml import YAML
import concurrent.futures
import threading
import math

# Configure logging
logging.basicConfig(filename='agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')

# Global variables for output paths
output_yaml_path = 'output/unformatted_topic_yaml/'
final_topic_yaml = 'output/topic_yaml/'
topic_template_path = 'YAML-template/topic-yaml/'

def data_access_agent(content):
    logging.info('step4_callAgentDecision:data_access_agent')
    logging.info(f'Input: {content}')
    prompt_path = 'prompts/few_shot_CPS_DA.md'
    convertXmltoYaml(content, prompt_path, topic_template)

def decision_state_agent(content):
    logging.info('step4_callAgentDecision:decision_state_agent')
    logging.info(f'Input: {content}')
    prompt_path = 'prompts/few_shot_CPS_DS.md'
    try:
        convertXmltoYaml(content, prompt_path, topic_template)
    except Exception as e:
        logging.error(f'Error in convertXmltoYaml: {e}. Retrying...')
        try:
            convertXmltoYaml(content, prompt_path, topic_template)
        except Exception as e:
            logging.error(f'Second attempt failed: {e}. Aborting...')
            raise

def play_state_agent(content):
    logging.info('step4_callAgentDecision:play_state_agent')
    logging.info(f'Input: {content}')
    prompt_path = 'prompts/few_shot_CPS_PP.md'
    try:
        convertXmltoYaml(content, prompt_path, topic_template)
    except Exception as e:
        logging.error(f'Error in convertXmltoYaml: {e}. Retrying...')
        try:
            convertXmltoYaml(content, prompt_path, topic_template)
        except Exception as e:
            logging.error(f'Second attempt failed: {e}. Aborting...')
            raise

def custom_state_agent(content):
    logging.info('step4_callAgentDecision:custom_state_agent')
    logging.info(f'Input: {content}')
    print(f'custom topic_template : {topic_template}')
    prompt_path = 'prompts/few_shot_CPS_CS.md'
    try:
        convertXmltoYaml(content, prompt_path, topic_template)
    except Exception as e:
        logging.error(f'Error in convertXmltoYaml: {e}. Retrying...')
        try:
            convertXmltoYaml(content, prompt_path, topic_template)
        except Exception as e:
            logging.error(f'Second attempt failed: {e}. Aborting...')
            raise

def dm_state_agent(content):
    logging.info('step4_callAgentDecision:dm_state_agent')
    logging.info(f'Input: {content}')
    prompt_path = 'prompts/few_shot_CPS_DM.md'
    try:
        convertXmltoYaml(content, prompt_path, topic_template)
    except Exception as e:
        logging.error(f'Error in convertXmltoYaml: {e}. Retrying...')
        try:
            convertXmltoYaml(content, prompt_path, topic_template)
        except Exception as e:
            logging.error(f'Second attempt failed: {e}. Aborting...')
            raise

def subdialog_state_agent(content):
    logging.info('step4_callAgentDecision:subdialog_state_agent')
    logging.info(f'Input: {content}')
    prompt_path = 'prompts/few_shot_CPS_SD.md'
    try:
        convertXmltoYaml(content, prompt_path, topic_template)
    except Exception as e:
        logging.error(f'Error in convertXmltoYaml: {e}. Retrying...')
        try:
            convertXmltoYaml(content, prompt_path, topic_template)
        except Exception as e:
            logging.error(f'Second attempt failed: {e}. Aborting...')
            raise

def process_xml_file(file_path):
    # Load and parse the XML data from the file
    logging.info('step4_callAgentDecision:process_xml_file')
    tree = ET.parse(file_path)
    root = tree.getroot()
    llm_response.is_first_time = True
    
    # Dictionary to map tag names to functions
    tag_function_map = {
        "data-access-state": data_access_agent,
        "decision-state": decision_state_agent,
        "play-state": play_state_agent,
        "dm-state": dm_state_agent,
        "subdialog-state": subdialog_state_agent,
        "custom-state": custom_state_agent
    }

    for element in root:
        tag_name = element.tag
        content = ET.tostring(element, encoding='unicode')

        # Call the corresponding function if the tag is in the map
        if tag_name in tag_function_map:
            tag_function_map[tag_name](content)
        else:
            print(f"No function mapped for tag: {tag_name}")

def process_single_dialog_file(file, input_dialog_folder):
    """Process a single dialog file"""
    try:
        file_path = os.path.join(input_dialog_folder, file+'.xml')
        topic_template = topic_template_path + file + '.yml'
        
        # Set thread-local first_time flag
        llm_response.is_first_time = True
        
        process_xml_file(file_path)
        topic_yaml_indentation(output_yaml_path + file + '.yml', final_topic_yaml + file + '_topic.yml')
        
        # Replacing null with empty for key:ACTION
        fileName = final_topic_yaml + file + '_topic.yml'
        remove_null_from_action(fileName)
        
        logging.info(f'Successfully processed dialog file: {file}')
        return True, file, None
        
    except Exception as e:
        logging.error(f'Failed to process dialog-file {file}: {e}')
        log_failed_file(file)
        return False, file, str(e)

def process_dialog_batch(batch_files, batch_number, input_dialog_folder):
    """Process a batch of dialog files"""
    logging.info(f'Starting batch {batch_number} with {len(batch_files)} files')
    results = []
    
    for file in batch_files:
        result = process_single_dialog_file(file, input_dialog_folder)
        results.append(result)
    
    logging.info(f'Completed batch {batch_number}')
    return results

def divide_files_into_batches(files, batch_size=10):
    """Divide files into batches of specified size"""
    batches = []
    for i in range(0, len(files), batch_size):
        batch = files[i:i + batch_size]
        batches.append(batch)
    return batches

def process_dialogs_in_parallel(input_dialog_folder, batch_size=10, max_workers=None):
    """Process dialog files in parallel batches"""
    
    # Get all dialog files
    input_dialog_files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(input_dialog_folder)]
    
    if not input_dialog_files:
        logging.warning("No dialog files found to process")
        return
    
    # Divide files into batches
    batches = divide_files_into_batches(input_dialog_files, batch_size)
    
    logging.info(f'Processing {len(input_dialog_files)} dialog files in {len(batches)} batches of {batch_size} files each')
    print(f'Processing {len(input_dialog_files)} dialog files in {len(batches)} batches of {batch_size} files each')
    
    # Determine number of workers (default to number of batches, but limit to reasonable number)
    if max_workers is None:
        max_workers = min(len(batches), os.cpu_count() or 4)
    
    all_results = []
    
    # Process batches in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all batch processing tasks
        future_to_batch = {
            executor.submit(process_dialog_batch, batch, i+1, input_dialog_folder): (batch, i+1) 
            for i, batch in enumerate(batches)
        }
        
        # Collect results as they complete
        for future in concurrent.futures.as_completed(future_to_batch):
            batch, batch_number = future_to_batch[future]
            try:
                batch_results = future.result()
                all_results.extend(batch_results)
                logging.info(f'Batch {batch_number} completed successfully')
                print(f'Batch {batch_number} completed successfully')
            except Exception as e:
                logging.error(f'Batch {batch_number} failed with error: {e}')
                print(f'Batch {batch_number} failed with error: {e}')
                # Mark all files in this batch as failed
                for file in batch:
                    all_results.append((False, file, str(e)))
                    log_failed_file(file)
    
    # Summary of results
    successful = sum(1 for success, _, _ in all_results if success)
    failed = len(all_results) - successful
    
    logging.info(f'Dialog processing completed: {successful} successful, {failed} failed')
    print(f'Dialog processing completed: {successful} successful, {failed} failed')
    
    return all_results

def dvxml_agent(dvxml_file_path, dvxml_topic_template, dvxml_prompt_path):
    logging.info('step4_callAgentDecision:dvxml_agent')

    # Check if the file is not empty or missing essential data
    try:
        tree = ET.parse(dvxml_file_path)
        root = tree.getroot()
        llm_response.is_first_time = True
        logging.info(f'llm_response.is_first_time: {llm_response.is_first_time}')
        
        # Check if the root has any children (i.e., the file contains data)
        if len(root) == 0:
            logging.warning('The XML file has no data.')

        content = ET.tostring(root, encoding='unicode')
        logging.info(f'Input: {content}')

        convertXmltoYaml(content, dvxml_prompt_path, dvxml_topic_template)

    except ET.ParseError as e:
        logging.error(f'Error parsing XML file: {e}')
        raise
    except FileNotFoundError:
        logging.error(f'File not found: {dvxml_file_path}')
        raise

def default_agent(file_path, topic_template, prompt_path):
    logging.info('step4_callAgentDecision:default_agent')

    # Check if the file is not empty or missing essential data
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        llm_response.is_first_time = True
        logging.info(f'llm_response.is_first_time: {llm_response.is_first_time}')
        
        # Check if the root has any children (i.e., the file contains data)
        if len(root) == 0:
            logging.warning('The XML file has no data.')
            return
            
        content = ET.tostring(root, encoding='unicode')
        logging.info(f'Input: {content}')

        convertXmltoYaml(content, prompt_path, topic_template)

    except ET.ParseError as e:
        logging.error(f'Error parsing XML file: {e}')
        raise
    except FileNotFoundError:
        logging.error(f'File not found: {file_path}')
        raise

def next_transition(unformatted_variable_path):
    yaml = YAML()
    new_yaml_content = [
        {
            "kind": "BeginDialog",
            "id": "BeginDialog_REPLACE_THIS",
            "dialog": "topic.hl00_CareSharedHL_SD_Dialog"
        }
    ]
    
    input_dialog_folder = 'input/dialogs_file/'
    dialog_files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(input_dialog_folder)]
    first_file = dialog_files[0]
    print(f'first_file : {first_file}')

    for item in new_yaml_content:
        if 'dialog' in item:
            item['dialog'] = "topic."+first_file
    
    # Read the existing YAML file
    with open(unformatted_variable_path, 'r') as file:
        yaml_content = yaml.load(file)

    # Append the new content to the YAML
    yaml_content.extend(new_yaml_content)

    # Overwrite the existing file with the updated content
    with open(unformatted_variable_path, 'w') as file:
        yaml.dump(yaml_content, file)

if __name__ == "__main__":
    # ONE GO RUN WITH PARALLEL PROCESSING
    input_dialog_folder = 'input/dialogs_file/'
    
    # Delete existing files
    delete_existing_files(output_yaml_path)
    delete_existing_files(final_topic_yaml)

    # TO CREATE SEPARATE TOPIC FOR ALL VARIABLE
    variable_file_path = 'input/variable/merged_variables.xml'
    variable_template = 'YAML-template/All_Variable.yml'
    variable_prompt_path = 'prompts/few_shot_CPS_Variable.md'
    unformatted_variable_path = output_yaml_path + 'All_Variable.yml'

    print("Processing variables...")
    default_agent(variable_file_path, variable_template, variable_prompt_path)
    next_transition(unformatted_variable_path)
    topic_yaml_indentation(unformatted_variable_path, final_topic_yaml + 'All_Variable.yml')
    fileName = final_topic_yaml + 'All_Variable.yml'
    remove_null_from_action(fileName)

    # TO RUN DIALOG STATE WITH PARALLEL PROCESSING
    print("Starting parallel processing of dialog files...")
    batch_size = 10  # Process 10 files per batch
    max_workers = 4  # Number of parallel workers
    
    process_dialogs_in_parallel(input_dialog_folder, batch_size, max_workers)

    # TO RUN CUSTOM STATE
    print("Processing custom state files...")
    input_custom_folder = 'input/custom_file/'
    custom_state_prompt_path = 'prompts/few_shot_CPS_CS_topic.md'
    files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(input_custom_folder)]
    for file in files:
        try:
            topic_template = topic_template_path + file + '.yml'
            default_agent(input_custom_folder + file + '.xml', topic_template, custom_state_prompt_path)
            topic_yaml_indentation(output_yaml_path + file + '.yml', final_topic_yaml + file + '_topic.yml')

            fileName = final_topic_yaml + file + '_topic.yml'
            remove_null_from_action(fileName)
        except Exception as e:
            logging.error(f'Failed to process custom-file {file}: {e}')
            log_failed_file(file)

    # TO RUN DVXML STATE
    print("Processing DVXML state files...")
    dvxml_prompt_path = 'prompts/few_shot_CPS_DVXML.md'
    input_dvxml_folder = 'input/dvxml_file/'
    files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(input_dvxml_folder)]
    for file in files:
        try:
            dvxml_topic_template = topic_template_path + file + '.yml'
            dvxml_agent(input_dvxml_folder + file + '.xml', dvxml_topic_template, dvxml_prompt_path)
            topic_yaml_indentation(output_yaml_path + file + '.yml', final_topic_yaml + file + '_topic.yml')

            fileName = final_topic_yaml + file + '_topic.yml'
            remove_null_from_action(fileName)

        except Exception as e:
            logging.error(f'Failed to process dvxml-file {file}: {e}')
            log_failed_file(file)

    print("All processing completed!")
