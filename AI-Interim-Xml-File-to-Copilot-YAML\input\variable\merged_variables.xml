<?xml version="1.0" ?>
<conditions>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="GlobalVars.acceptedBCR" value="true" type="boolean"/>
  <session-mapping key="payments_enable_prepaid_methods" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.GetPaymentOptions" value="undefined" type="string"/>
  <session-mapping key="GetPaymentOptions.status" value="BLACKLISTED" type="string"/>
  <session-mapping key="GlobalVars.GetConvenienceFee" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.completedOnePaymentOnCall" value="true|" type="string"/>
  <session-mapping key="GetPaymentOptions.madePaymentInLast24Hours" value="true" type="string"/>
  <session-mapping key="totalDueAmount" value="0" type="string"/>
  <session-mapping key="madePaymentInLast24Hours" value="true" type="boolean"/>
  <session-mapping key="balanceAnddueImmediatelyAmount" value="0" type="integer"/>
  <session-mapping key="GlobalVars.heardBCRTerms" value="true" type="boolean"/>
  <session-mapping key="ProcessPayment.status" value="PENDING" type="string"/>
  <session-mapping key="transactionId" value="undefined" type="string"/>
  <session-mapping key="GetAccountDetails.ratePlan" value="null" type="string"/>
  <session-mapping key="AnonymousRechargeByVoucher.reChargeStatus" value="Y" type="string"/>
  <session-mapping key="isWalletPopulated" value="true" type="boolean"/>
  <session-mapping key="location" value="empty" type="String"/>
  <session-mapping key="filterBy" value="empty" type="String"/>
  <session-mapping key="storeLocReason" value="unknown" type="string"/>
  <session-mapping key="storeLocatorReason" value="invalidEquipment" type="string"/>
  <session-mapping key="firstSearch" value="true" type="boolean"/>
  <session-mapping key="searchCount" value="1" type="integer"/>
  <session-mapping key="GlobalVars.storeLocatorReason" value="invalidequipment" type="string"/>
  <session-mapping key="locations.length" value="0" type="integer"/>
  <session-mapping key="length" value="1" type="integer"/>
  <session-mapping key="playResultsPreamble" value="true" type="string"/>
  <session-mapping key="curStoreIndex" value="0" type="integer"/>
  <session-mapping key="isPreviousAllowed" value="true" type="boolean"/>
  <session-mapping key="isNextAllowed" value="true" type="boolean"/>
  <session-mapping key="isMoreAllowed" value="true" type="boolean"/>
  <session-mapping key="isFirstRepeatCnt" value="false" type="boolean"/>
  <session-mapping key="searchStatus" value="start" type="string"/>
  <session-mapping key="heardNavigationInstr" value="true" type="boolean"/>
  <session-mapping key="isRepeat" value="false" type="boolean"/>
  <session-mapping key="isPrevious" value="false" type="boolean"/>
  <session-mapping key="cust_store_type" value="MetroSt" type="string"/>
  <session-mapping key="autoCorrectApproved" value="empty" type="String"/>
  <session-mapping key="futureDate" value="empty" type="String"/>
  <session-mapping key="addFuturePromo" value="empty" type="String"/>
  <session-mapping key="addRegularPromo" value="empty" type="String"/>
  <session-mapping key="removeFuturePromoSoc" value="empty" type="String"/>
  <session-mapping key="removeRegularPromoSoc" value="empty" type="String"/>
  <session-mapping key="GlobalVars.newRatePlan" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.addFeatures" value="undefined" type="string"/>
  <session-mapping key="SubmitChangeOffer.status" value="Success" type="string"/>
  <session-mapping key="SubmitChangeOffer.triedToUpdateSubscriberDetails" value="true" type="boolean"/>
  <session-mapping key="GetBCSParameters.care_enable_rateplan_special_error_handling" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.care_rateplan_special_error_planlist" value="null" type="string"/>
  <session-mapping key="GlobalVars.ratePlanChangeDBErrorCounter" value="0" type="integer"/>
  <session-mapping key="isAutoCorrectRequired" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.autoCorrectApproved" value="false" type="string"/>
  <session-mapping key="GlobalVars.care_enable_autocorrect_offer" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.newPlanPrice" value="GlobalVars.oldPlanPrice" type="string"/>
  <session-mapping key="SubmitChangeOffer.dueImmediatlyAmount" value="0" type="string"/>
  <session-mapping key="SubmitChangeOffer.priorDueImmediatelyAmt" value="0" type="string"/>
  <session-mapping key="SubmitChangeOffer.amountDue" value="GlobalVars.GetAccountDetails.balance" type="string"/>
  <session-mapping key="SubmitChangeOffer.isAutoCorrectRequired" value="false" type="string"/>
  <session-mapping key="SH1215_operator_counter" value="2" type="integer"/>
  <session-mapping key="isLoggedIn" value="true" type="boolean"/>
  <session-mapping key="SH1305_operator_counter" value="2" type="integer"/>
  <session-mapping key="eligibleForBillCycleReset" value="true" type="boolean"/>
  <session-mapping key="pay_now" value="lastresult" type="string"/>
  <session-mapping key="change_plan" value="lastresult" type="string"/>
  <session-mapping key="billcycle_reset" value="lastresult" type="string"/>
  <session-mapping key="switch_lines" value="lastresult" type="string"/>
  <session-mapping key="firstTimeAt1030" value="true" type="boolean"/>
  <session-mapping key="GetBCSParameters.care_enable_switch_lines" value="false" type="boolean"/>
  <session-mapping key="switchLinesMDNAttempts" value="0" type="integer"/>
  <session-mapping key="requestedMDNChangeSwitchLines" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.switchLinesMDNAttempts" value="1" type="integer"/>
  <session-mapping key="GlobalVars.requestedMDNChangeSwitchLines" value="true" type="boolean"/>
  <session-mapping key="switchLinesPINAttempts" value="1" type="integer"/>
  <session-mapping key="same_one" value="lastresult" type="string"/>
  <session-mapping key="incorrect_MDN" value="lastresult" type="string"/>
  <session-mapping key="acctLocked" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.switchLinesPINAttempts" value="1" type="integer"/>
  <session-mapping key="GlobalVars.accountNumber" value="GlobalVars.originalLine_accountNumber" type="string"/>
  <session-mapping key="GlobalVars.switchLinesEntryPoint" value="esn_swap" type="string"/>
  <session-mapping key="techSupportType" value="mobiledata" type="string"/>
  <session-mapping key="isUnlimited" value="true" type="boolean"/>
  <session-mapping key="dataUsage_dataUsed" value="35" type="integer"/>
  <session-mapping key="heardHotspotExceededInfo" value="true" type="boolean"/>
  <session-mapping key="usingBrokenDevice" value="yes" type="string"/>
  <session-mapping key="fromTT1020" value="true" type="boolean"/>
  <session-mapping key="maxRetryTroubleshooting" value="true" type="boolean"/>
  <session-mapping key="tempCode" value="empty" type="String"/>
  <session-mapping key="GlobalVars.twoFactorAuthEntryPoint" value="228_esn" type="string"/>
  <session-mapping key="GlobalVars.star228_enable_twofactorauth" value="true" type="boolean"/>
  <session-mapping key="switchLinesMDNMatch" value="true" type="string"/>
  <session-mapping key="fromMultilineFallBack" value="true" type="string"/>
  <session-mapping key="GenerateTempCode.status" value="Success" type="string"/>
  <session-mapping key="twoFactorHoldCounter" value="0" type="integer"/>
  <session-mapping key="GlobalVars.twoFactorHoldCounter" value="1" type="integer"/>
  <session-mapping key="ValidateTempCode.status" value="Failure" type="string"/>
  <session-mapping key="ValidateTempCode.acctLocked" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.validateTempCodeStatus" value="Success" type="string"/>
  <session-mapping key="GlobalVars.twoFactorAuthNumberRetries" value="0" type="integer"/>
  <session-mapping key="GlobalVars.TF1405Status" value="INVALID_OTP" type="string"/>
  <session-mapping key="visitedTFRetry_counter" value="0" type="integer"/>
  <session-mapping key="matchCaseTrue" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.multilineFallBackAttempts" value="1" type="string"/>
  <session-mapping key="UA1005_operator_counter" value="3" type="integer"/>
  <session-mapping key="UA1105_operator_counter" value="3" type="integer"/>
  <session-mapping key="GlobalVars.accountStatus" value="suspended" type="string"/>
  <session-mapping key="existingCustomerInfoType" value="lostPhone" type="string"/>
  <session-mapping key="UA1205_operator_counter" value="3" type="integer"/>
  <session-mapping key="eligibleForUpgrade" value="true" type="boolean"/>
  <session-mapping key="GetWalletInfo.status" value="Success" type="string"/>
  <session-mapping key="GetWalletInfo.walletItems" value="undefined" type="string"/>
  <session-mapping key="numberOfCards" value="1" type="integer"/>
  <session-mapping key="abandonEWalletAuth" value="true" type="boolean"/>
  <session-mapping key="dbError" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.savedCardPreference" value="UNKNOWN" type="string"/>
  <session-mapping key="ResetVoiceMailPIN.status" value="Success" type="string"/>
  <session-mapping key="GlobalVars.cancelAddFeature" value="true" type="boolean"/>
  <session-mapping key="VS1135initialEntry" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.heardFutureDatedFeatureInstructions" value="false" type="boolean"/>
  <session-mapping key="heardFutureDatedFeatureInstructions" value="true" type="boolean"/>
  <session-mapping key="GetAvailableFeatureOffers_VS1001.status" value="Success" type="string"/>
  <session-mapping key="ifFromCurrentFeatures" value="true" type="boolean"/>
  <session-mapping key="isThirdPartyFeature" value="true" type="boolean"/>
  <session-mapping key="addFeatureAction" value="Custom after selected" type="string"/>
  <session-mapping key="playOneFeature" value="true" type="string"/>
  <session-mapping key="fromIOM" value="true" type="string"/>
  <session-mapping key="serviceIndicatorAddMessageEnglish" value="" type="string"/>
  <session-mapping key="serviceIndicatorAddMessageSpanish" value="" type="string"/>
  <session-mapping key="addServiceIndicator" value="Transfer after selected" type="string"/>
</conditions>
