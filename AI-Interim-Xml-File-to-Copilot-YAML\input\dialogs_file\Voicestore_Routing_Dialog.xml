<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Voicestore_Routing_Dialog">
    <subdialog-state id="VS1315_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="VS1315_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="VS1315_GoToCallTransfer_SD_return_CS"/>
    <subdialog-state id="VS1320_GoToAnythingElse_SD">
      <gotodialog next="AnythingElse_Main_Dialog"/>
      <action next="VS1320_GoToAnythingElse_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="VS1320_GoToAnythingElse_SD_return_CS"/>
    <subdialog-state id="VS1330_GoToMakePayments_SD">
      <session-mapping key="GlobalVars.IsAddingFeature" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careVoiceStore'"/>
      <session-mapping key="GlobalVars.FromCareVoiceStore" value="true" type="Boolean"/>
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="VS1330_GoToMakePayments_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="VS1330_GoToMakePayments_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="VS1340_GoToGoodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="VS1340_GoToGoodbye_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="VS1340_GoToGoodbye_SD_return_CS"/>
  </dialog>
  