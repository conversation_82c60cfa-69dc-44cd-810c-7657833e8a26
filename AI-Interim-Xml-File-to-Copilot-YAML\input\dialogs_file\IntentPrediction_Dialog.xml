<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="IntentPrediction_Dialog">
    <decision-state id="IP1001_InitializeIntentPrediction_DS">
      <action next="IP1005_SSVPProfiles_DB_DA"/>
    </decision-state>

    <data-access-state id="IP1005_SSVPProfiles_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.trn" type="String"/>
      <data-access id="SSVPProfiles" classname="com.nuance.metro.dataaccess.SSVPProfiles">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
        </inputs>
        <outputs>
          <output-variable name="lastCustomerIntent"/>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="SSVPProfiles.status &amp;&amp; SSVPProfiles.status.toUpperCase() == 'SUCCESS' ">
          <session-mapping key="GlobalVars.lastCustomerIntent_logging" expr="SSVPProfiles.lastCustomerIntent"/>
          <session-mapping key="GlobalVars.lastCustomerIntent" expr="SSVPProfiles.lastCustomerIntent"/>
        </if>
        <if cond="(SSVPProfiles.lastCustomerIntent == undefined || SSVPProfiles.lastCustomerIntent == '')">
          <action next="getReturnLink()"/>
          <else>
            <action next="IP1010_SSVPCustomEvent_DB_DA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <data-access-state id="IP1010_SSVPCustomEvent_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.trn" type="String"/>
      <session-mapping key="lastCustomerIntent" value="GlobalVars.lastCustomerIntent_logging" type="String"/>
      <data-access id="SSVPCustomEvent" classname="com.nuance.metro.dataaccess.SSVPCustomEvent">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="lastCustomerIntent"/>
        </inputs>
        <outputs>
          <output-variable name="isLastIntentUpdated"/>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <action next="getReturnLink()"/>
      </action>
    </data-access-state>

  </dialog>
  