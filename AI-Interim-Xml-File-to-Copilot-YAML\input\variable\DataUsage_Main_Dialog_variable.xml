<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="getTopUpHistory" value="empty" type="String"/>
  <session-mapping key="payDate" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="isSuspended" value="empty" type="String"/>
  <session-mapping key="isBARTET" value="empty" type="String"/>
  <session-mapping key="dataUsage_dataCap_KB" value="empty" type="String"/>
  <session-mapping key="topUpHotSpot_dataCap_KB" value="empty" type="String"/>
  <session-mapping key="dataUsage_dataUsed_KB" value="empty" type="String"/>
  <session-mapping key="topUpHotSpot_dataUsed_KB" value="empty" type="String"/>
  <session-mapping key="dataUsage_dataFound" value="true" type="boolean"/>
  <session-mapping key="GetDataUsage.status" value="Success" type="string"/>
  <session-mapping key="GlobalVars.tag" value="home-internet_active" type="string"/>
  <session-mapping key="GetDataUsageInfo.dataUsage_isUnlimited" value="true" type="string"/>
  <session-mapping key="topUpHotSpot_dataFound" value="true" type="string"/>
  <session-mapping key="numPaidTopUps" value="0" type="integer"/>
  <session-mapping key="numGoodWillTopUps" value="0" type="string"/>
  <session-mapping key="topUpHotSpot_dataPercentage" value="0" type="string"/>
  <session-mapping key="topUpHotSpot_dataCap" value="1" type="string"/>
  <session-mapping key="topupEligibility" value="NOT-ELIGIBLE" type="string"/>
  <session-mapping key="dataUsage_isUnlimited" value="true" type="boolean"/>
  <session-mapping key="dataUsage_dataPercentage" value="100" type="string"/>
  <session-mapping key="willExceedDataLimit" value="true" type="string"/>
</session-mappings>
