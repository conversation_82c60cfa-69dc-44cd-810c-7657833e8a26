from ruamel.yaml import YAML
import logging

logging.basicConfig(filename='agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')

def concatenate_yaml_files(file_path1, file_path2, output_file_path):
    logging.info('merge_topic:concatenate_yaml_files')
    yaml = YAML()
    yaml.preserve_quotes = True
    yaml.width = 8192  # Set a high width to prevent line wrapping
     
    # Load the YAML files
    with open(file_path1, 'r') as file1:
        yaml1 = yaml.load(file1)
        #print(f'yaml1 : {yaml1}')
    with open(file_path2, 'r') as file2:
        yaml2 = yaml.load(file2)
        #print(f'yaml2 : {yaml2}')
    # Concatenate the YAML content
    if isinstance(yaml1, list) and isinstance(yaml2, list):
        combined_yaml = yaml1 + yaml2
    elif isinstance(yaml1, dict) and isinstance(yaml2, dict):
        combined_yaml = {**yaml1, **yaml2}
    else:
        raise ValueError("YAML files must be of the same type (both lists or both dictionaries)")

    # Write the combined YAML to a new file
    with open(output_file_path, 'w') as output_file:
        yaml.dump(combined_yaml, output_file)

    #print(f'combined_yaml: {combined_yaml}')
    print(f"Concatenation is inprogress . . . for {output_file_path}")

# # Example usage
# file_path1 = 'YAML-template/topic-yaml/main.yml'
# file_path2 = 'output/formatted_yaml.yml'
# output_file_path = 'output/intermidiate.yml'

# concatenate_yaml_files(file_path1, file_path2, output_file_path)
