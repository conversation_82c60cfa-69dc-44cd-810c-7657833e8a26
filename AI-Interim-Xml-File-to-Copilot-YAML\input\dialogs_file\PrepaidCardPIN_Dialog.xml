<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="PrepaidCardPIN_Dialog">
    <decision-state id="PC1001_InitializePrepaidCard_DS">
      <session-mapping key="GlobalVars.fromFindPINWait" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.prepaidPINErrorCounter" expr="0"/>
      <session-mapping key="GlobalVars.voucherNumber" expr="''"/>
      <session-mapping key="GlobalVars.errorCode" expr="''"/>
      <if cond="GlobalVars.numberPINPayments == 0">
        <action next="PC1005_PlayPrepaidCardDisclaimer_PP"/>
        <else>
          <action next="PC1010_GetPrepaidCardPIN_DM"/>
        </else>
      </if>
    </decision-state>

    <play-state id="PC1005_PlayPrepaidCardDisclaimer_PP">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <audio>
        <prompt id="PC1005_out_01">
          <prompt-segments>
            <audiofile src="PC1005_out_01.wav" text="Just so you know, we'll apply the *full* value of your  Payment PIN to your account right away If it's more than your amount due, you'll just have a credit for next month! "/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms" cond="callType == 'activate'">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PC1005_out_02" cond="callType == 'activate'">
          <prompt-segments>
            <audiofile src="PC1005_out_02.wav" text="If it's less, we'll take the rest of your payment right after"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PC1010_GetPrepaidCardPIN_DM"/>
    </play-state>

    <dm-state id="PC1010_GetPrepaidCardPIN_DM" type="CUST">
      <session-mapping key="fromFindPINWait" value="GlobalVars.fromFindPINWait" type="String"/>
      <session-mapping key="numberPINPayments" value="GlobalVars.numberPINPayments" type="String"/>
      <session-mapping key="errorCode" value="GlobalVars.errorCode" type="String"/>
      <session-mapping key="prepaidPINErrorCounter" value="GlobalVars.prepaidPINErrorCounter" type="String"/>
      <session-mapping key="prepaidPinToggleOn" value="GlobalVars.GetBCSParameters.prepaidPinToggleOn" type="String"/>
      <success>
        <action label="help">
          <audio>
            <prompt id="PC1010_out_01">
              <prompt-segments>
                <audiofile src="PC1010_out_01.wav" text="Sure"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="PC2005_PlayPINLocation_PP"/>
        </action>
        <action label="default" next="PC1015_ValidatePrepaidPIN_DB_DA">
          <session-mapping key="GlobalVars.fromFindPINWait" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.voucherNumber" expr="PC1010_GetPrepaidCardPIN_DM.returnvalue"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="prepaidPinToggleOn == true">
                <prompt id="PC1010_ini_01">
                  <prompt-segments>
                    <audiofile src="PC1010_ini_01.wav" text="Now, What's the 10 or 17 digit PIN on your card or receipt? Or say 'help me find it' "/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="PC1010_ini_02">
                    <prompt-segments>
                      <audiofile src="PC1010_ini_02.wav" text="Now, What's the 10-digit PIN on your card or receipt? Or say 'help me find it' "/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="PC1010_GetPrepaidCardPIN_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="prepaidPinToggleOn == true">
                <prompt id="PC1010_ini_01">
                  <prompt-segments>
                    <audiofile src="PC1010_ini_01.wav" text="Now, What's the 10 or 17 digit PIN on your card or receipt? Or say 'help me find it' "/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="PC1010_ini_02">
                    <prompt-segments>
                      <audiofile src="PC1010_ini_02.wav" text="Now, What's the 10-digit PIN on your card or receipt? Or say 'help me find it' "/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PC1010_nm1_01" cond="prepaidPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="PC1010_nm1_01.wav" text="What's the 10- or 17-digit PIN on your card or receipt? Or say 'help me find it' "/>
                </prompt-segments>
              </prompt>
              <prompt id="PC1010_nm1_02" cond="prepaidPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="PC1010_nm1_02.wav" text="What's the 10 digit PIN on your card or receipt? Or say 'help me find it' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="PC1010_nm2_01" cond="prepaidPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="PC1010_nm2_01.wav" text="Please enter the 10- or 17-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star"/>
                </prompt-segments>
              </prompt>
              <prompt id="PC1010_nm2_02" cond="prepaidPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="PC1010_nm2_02.wav" text="Please enter the 10-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="PC1010_nm3_01" cond="prepaidPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="PC1010_nm3_01.wav" text="Please enter the 10- or 17-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star"/>
                </prompt-segments>
              </prompt>
              <prompt id="PC1010_nm3_02" cond="prepaidPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="PC1010_nm3_02.wav" text="Please enter the 10-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PC1010_nm1_01" cond="prepaidPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="PC1010_nm1_01.wav" text="What's the 10- or 17-digit PIN on your card or receipt? Or say 'help me find it' "/>
                </prompt-segments>
              </prompt>
              <prompt id="PC1010_nm1_02" cond="prepaidPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="PC1010_nm1_02.wav" text="What's the 10 digit PIN on your card or receipt? Or say 'help me find it' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PC1010_nm2_01" cond="prepaidPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="PC1010_nm2_01.wav" text="Please enter the 10- or 17-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star"/>
                </prompt-segments>
              </prompt>
              <prompt id="PC1010_nm2_02" cond="prepaidPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="PC1010_nm2_02.wav" text="Please enter the 10-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PC1010_nm3_01" cond="prepaidPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="PC1010_nm3_01.wav" text="Please enter the 10- or 17-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star"/>
                </prompt-segments>
              </prompt>
              <prompt id="PC1010_nm3_02" cond="prepaidPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="PC1010_nm3_02.wav" text="Please enter the 10-digit PIN on your prepaid card, or on your receipt If you need help finding it, press star"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="(fromFindPINWait == true)">
                <prompt id="PC1010_rin_01" cond="prepaidPinToggleOn == true">
                  <prompt-segments>
                    <audiofile src="PC1010_rin_01.wav" text="What's the 10- or 17-digit PIN? "/>
                  </prompt-segments>
                </prompt>
                <prompt id="PC1010_rin_06" cond="prepaidPinToggleOn != true">
                  <prompt-segments>
                    <audiofile src="PC1010_rin_06.wav" text="What's the 10-digit PIN? "/>
                  </prompt-segments>
                </prompt>
                <elseif cond="prepaidPINErrorCounter == 1">
                  <prompt id="PC1010_rin_02">
                    <prompt-segments>
                      <audiofile src="PC1010_rin_02.wav" text="Let s try again - go ahead"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="(prepaidPINErrorCounter == 2 || errorCode == 'CLOSEMAXRETRIES')">
                  <prompt id="PC1010_rin_03">
                    <prompt-segments>
                      <audiofile src="PC1010_rin_03.wav" text="Let s try it one last time - go ahead"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="(numberPINPayments &gt;= 1)">
                  <prompt id="PC1010_rin_05">
                    <prompt-segments>
                      <audiofile src="PC1010_rin_05.wav" text="What's the PIN? "/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PC1010_rin_04" cond="prepaidPinToggleOn == true">
                    <prompt-segments>
                      <audiofile src="PC1010_rin_04.wav" text="What's the 10- or 17-digit PIN on your card or receipt? "/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="PC1010_rin_07" cond="prepaidPinToggleOn != true">
                    <prompt-segments>
                      <audiofile src="PC1010_rin_07.wav" text="What's the 10-digit PIN on your card or receipt? "/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="PC1010_GetPrepaidCardPIN_DM.grxml" count="1"/>
          <dtmfgrammars filename="PC1010_GetPrepaidCardPIN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="20000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms">
        </vxml_properties>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
      <confirmation_configuration reco_suppress_logs="true" highconfidencelevel="0.550">
        <threshold_configuration maxrepeats="1" maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.metro.audio.custom.PlayPrepaidPIN"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="PC1010_GetPrepaidCardPIN_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.metro.audio.custom.PlayPrepaidPIN"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.metro.audio.custom.PlayPrepaidPIN"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="PC1015_ValidatePrepaidPIN_DB_DA">
      <session-mapping key="voucherNumber" value="GlobalVars.voucherNumber" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="accountNumber" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.accountNumber:''" type="String"/>
      <data-access id="SearchVoucherInfo" classname="com.nuance.metro.dataaccess.SearchVoucherInfo">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="accountNumber" mask="true"/>
          <input-variable name="voucherNumber" mask="true"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="voucherNumber" mask="true"/>
          <output-variable name="voucherNumberList" mask="true"/>
          <output-variable name="voucherStatus"/>
          <output-variable name="expirationDate" mask="true"/>
          <output-variable name="voucherAmount"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.SearchVoucherInfo" expr="SearchVoucherInfo"/>
        <if cond="(GlobalVars.SearchVoucherInfo &amp;&amp; GlobalVars.SearchVoucherInfo.status &amp;&amp; GlobalVars.SearchVoucherInfo.status.toUpperCase() == 'SUCCESS')">
          <session-mapping key="GlobalVars.voucherNumberListString" expr="GlobalVars.SearchVoucherInfo.voucherNumberList"/>
          <session-mapping key="GlobalVars.errorCode" expr="GlobalVars.SearchVoucherInfo.errorCode"/>
          <session-mapping key="GlobalVars.voucherNumberList" expr="getCommaSeperatedValueAsArray(GlobalVars.voucherNumberListString)"/>
          <action next="PC1020_CheckValidationResult_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <if cond="GlobalVars.isCareTransfer == 'true'">
              <gotodialog next="CallTransfer_Main_Dialog"/>
              <else>
                <gotodialog next="Transfer_Main"/>
              </else>
            </if>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="PC1020_CheckValidationResult_DS">
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount" type="String"/>
      <session-mapping key="voucherAmount" value="GlobalVars.SearchVoucherInfo.voucherAmount" type="String"/>
      <if cond="GlobalVars.SearchVoucherInfo.voucherStatus.toUpperCase() == 'HOT'">
        <session-mapping key="GlobalVars.paymentAmount" expr="(parseFloat(paymentAmount - voucherAmount))"/>
        <session-mapping key="GlobalVars.amountPaidOnCall" expr="parseFloat(GlobalVars.amountPaidOnCall) + parseFloat(voucherAmount)"/>
        <action next="PC1025_PlayCardValue_PP"/>
        <else>
          <session-mapping key="GlobalVars.prepaidPINErrorCounter" expr="GlobalVars.prepaidPINErrorCounter+1"/>
          <action next="PC3005_CheckError_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="PC1025_PlayCardValue_PP">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="voucherAmount" value="GlobalVars.SearchVoucherInfo.voucherAmount" type="String"/>
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount" type="String"/>
      <session-mapping key="activationEntryPoint" value="GlobalVars.activationEntryPoint" type="String"/>
      <session-mapping key="needResidualPayment" value="GlobalVars.needResidualPayment != undefined ? GlobalVars.needResidualPayment : false" type="String"/>
      <audio>
        <prompt id="PC1025_out_01">
          <prompt-segments>
            <audiofile src="PC1025_out_01.wav" text="Okay, That PIN value is"/>
          </prompt-segments>
        </prompt>
        <prompt type="currency" expr="voucherAmount">
          <param value="false" name="playZeroCents"/>
        </prompt>
        <if cond="callType == 'activate'">
          <prompt id="silence_500ms" cond="((parseFloat(paymentAmount) &gt; 0) &amp;&amp; (needResidualPayment == false))">
            <prompt-segments>
              <audiofile text="test" src="silence_500ms.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="PC1025_out_02" cond="((parseFloat(paymentAmount) &gt; 0) &amp;&amp; (needResidualPayment == false))">
            <prompt-segments>
              <audiofile src="PC1025_out_02.wav" text="So you still need to pay at least "/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="paymentAmount" cond="((parseFloat(paymentAmount) &gt; 0) &amp;&amp; (needResidualPayment == false))">
            <param value="false" name="playZeroCents"/>
            <param value="m" name="intonation"/>
          </prompt>
          <prompt id="PC1025_out_05" cond="((parseFloat(paymentAmount) &gt; 0) &amp;&amp; (needResidualPayment == false))">
            <prompt-segments>
              <audiofile src="PC1025_out_05.wav" text="to start your service"/>
            </prompt-segments>
          </prompt>
          <prompt id="PC1025_out_03" cond="((parseFloat(paymentAmount) &gt; 0) &amp;&amp; (needResidualPayment == true)) &amp;&amp; (activationEntryPoint == 'care')">
            <prompt-segments>
              <audiofile src="PC1025_out_03.wav" text="That still doesn't cover your first month's charges, so when we're done, I can take you to an agent to pay the rest "/>
            </prompt-segments>
          </prompt>
          <prompt id="PC1025_out_04" cond="((parseFloat(paymentAmount) &gt; 0) &amp;&amp; (needResidualPayment == true)) &amp;&amp; (activationEntryPoint != 'care')">
            <prompt-segments>
              <audiofile src="PC1025_out_04.wav" text="That doesn t cover your first month s charges, so when we re done, please remember to pay the rest online or in the myMetro app"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="((callType == 'activate') &amp;&amp; (parseFloat(paymentAmount) &gt; 0.0)  &amp;&amp; (needResidualPayment == false))">
        <session-mapping key="GlobalVars.needResidualPayment" value="true" type="Boolean"/>
        <elseif cond="((callType == 'activate') &amp;&amp; (parseFloat(paymentAmount) &gt; 0.0)  &amp;&amp; (needResidualPayment == true))">
          <session-mapping key="GlobalVars.needResidualPayment" value="false" type="Boolean"/>
        </elseif>
        <elseif cond="((callType == 'activate') &amp;&amp; !(parseFloat(paymentAmount) &gt; 0.0))">
          <session-mapping key="GlobalVars.needResidualPayment" value="false" type="Boolean"/>
        </elseif>
      </if>
      <session-mapping key="GlobalVars.numberPINPayments" expr="GlobalVars.numberPINPayments + 1"/>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="PC2005_PlayPINLocation_PP">
      <audio>
        <prompt id="PC2005_out_01">
          <prompt-segments>
            <audiofile src="PC2005_out_01.wav" text="If you have a prepaid *card*, scratch off the paint strip on the back of it to reveal your PIN If you just prepaid an amount at a store register, you should have a separate, smaller receipt from them, with a 10-digit number labelled as the PIN"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PC2010_FindPINWaitSBI_DM"/>
    </play-state>

    <dm-state id="PC2010_FindPINWaitSBI_DM" type="CUST">
      <success>
        <action label="continue">
          <session-mapping key="GlobalVars.fromFindPINWait" value="true" type="Boolean"/>
          <audio>
            <prompt id="PC2010_out_01">
              <prompt-segments>
                <audiofile src="PC2010_out_01.wav" text="All right!"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="PC1010_GetPrepaidCardPIN_DM"/>
        </action>
        <action label="cant_find">
          <audio>
            <prompt id="PC2010_out_02">
              <prompt-segments>
                <audiofile src="PC2010_out_02.wav" text="No problem"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="PC2015_UseBankCardYN_DM"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PC2010_ini_01">
                <prompt-segments>
                  <audiofile src="PC2010_ini_01.wav" text="Once you ve found that number, say  continue  or press 1 10sec wait music When you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait musicAt any time, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait musicWhen you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music At any time, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music When you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="" text="" id="PC2010_FindPINWaitSBI_DM_initial"/>
          <helpprompts count="1" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PC2010_ini_01">
                <prompt-segments>
                  <audiofile src="PC2010_ini_01.wav" text="Once you ve found that number, say  continue  or press 1 10sec wait music When you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait musicAt any time, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait musicWhen you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music At any time, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music When you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PC2010_ini_01">
                <prompt-segments>
                  <audiofile src="PC2010_ini_01.wav" text="Once you ve found that number, say  continue  or press 1 10sec wait music When you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait musicAt any time, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait musicWhen you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music At any time, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music When you have your  Payment PIN, say  continue  or press 1 Or say  I can t find it  or press 2 10sec wait music"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="PC2010_FindPINWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="PC2010_FindPINWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="3000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="selective" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="3000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="PC2015_UseBankCardYN_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.payingWithPrepaid" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.offerPrepaid" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.skipBalance" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.payingWithPrepaid" value="false" type="Boolean"/>
          <if cond="GlobalVars.callType == 'activate'">
            <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
            <else>
              <session-mapping key="GlobalVars.callType" expr="undefined"/>
            </else>
          </if>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PC2015_ini_01">
                <prompt-segments>
                  <audiofile src="PC2015_ini_01.wav" text="Would you like to pay with a bank card instead?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PC2015_ini_01">
                <prompt-segments>
                  <audiofile src="PC2015_ini_01.wav" text="Would you like to pay with a bank card instead?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PC2015_ini_01">
                <prompt-segments>
                  <audiofile src="PC2015_ini_01.wav" text="Would you like to pay with a bank card instead?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="PC2015_nm2_01">
                <prompt-segments>
                  <audiofile src="PC2015_nm2_01.wav" text="Do you wanna make your payment with a bank card instead? Say  yes  or press 1 or  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="PC2015_nm2_01">
                <prompt-segments>
                  <audiofile src="PC2015_nm2_01.wav" text="Do you wanna make your payment with a bank card instead? Say  yes  or press 1 or  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PC2015_ini_01">
                <prompt-segments>
                  <audiofile src="PC2015_ini_01.wav" text="Would you like to pay with a bank card instead?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PC2015_nm2_01">
                <prompt-segments>
                  <audiofile src="PC2015_nm2_01.wav" text="Do you wanna make your payment with a bank card instead? Say  yes  or press 1 or  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PC2015_nm2_01">
                <prompt-segments>
                  <audiofile src="PC2015_nm2_01.wav" text="Do you wanna make your payment with a bank card instead? Say  yes  or press 1 or  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PC2015_ini_01">
                <prompt-segments>
                  <audiofile src="PC2015_ini_01.wav" text="Would you like to pay with a bank card instead?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="PC2015_UseBankCardYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="PC2015_UseBankCardYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="PC3005_CheckError_DS">
      <session-mapping key="voucherStatus" value="GlobalVars.SearchVoucherInfo.voucherStatus?GlobalVars.SearchVoucherInfo.voucherStatus.toUpperCase():GlobalVars.SearchVoucherInfo.voucherStatus" type="String"/>
      <if cond="voucherStatus == 'COLD'">
        <action next="PC3010_CheckErrorCounter_JDA"/>
        <elseif cond="voucherStatus == 'ASSOCIATED'">
          <action next="PC3010_CheckErrorCounter_JDA"/>
        </elseif>
        <elseif cond="voucherStatus == 'VOID'">
          <action next="PC3010_CheckErrorCounter_JDA"/>
        </elseif>
        <elseif cond="voucherStatus == 'EXPIRED'">
          <action next="PC3010_CheckErrorCounter_JDA"/>
        </elseif>
        <else>
          <action next="PC3010_CheckErrorCounter_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="PC3010_CheckErrorCounter_DS">
      <if cond="(GlobalVars.prepaidPINErrorCounter == 3) || (GlobalVars.errorCode == 'MAXRETRIESEXCEEDED') || (GlobalVars.errorCode == 'MAXATTEMPTEXCEEDED')">
        <action next="PC3020_PlayMaxInvalid_PP"/>
        <else>
          <action next="PC3015_PlayValidationError_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="PC3015_PlayValidationError_PP">
      <session-mapping key="prepaidPINErrorCounter" value="GlobalVars.prepaidPINErrorCounter" type="String"/>
      <session-mapping key="voucherStatus" value="GlobalVars.SearchVoucherInfo.voucherStatus.toUpperCase()" type="String"/>
      <session-mapping key="errorCode" value="GlobalVars.errorCode" type="String"/>
      <audio>
        <if cond="prepaidPINErrorCounter == 1">
          <prompt id="PC3015_out_01">
            <prompt-segments>
              <audiofile src="PC3015_out_01.wav" text="Sorry that PIN wasn't valid"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="PC3015_out_02">
              <prompt-segments>
                <audiofile src="PC3015_out_02.wav" text="That didn't work either"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="PC3015_out_03" cond="voucherStatus == 'EXPIRED'">
          <prompt-segments>
            <audiofile src="PC3015_out_03.wav" text="It s expired"/>
          </prompt-segments>
        </prompt>
        <prompt id="PC3015_out_04" cond="voucherStatus == 'ASSOCIATED'">
          <prompt-segments>
            <audiofile src="PC3015_out_04.wav" text="It s already been used"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PC1010_GetPrepaidCardPIN_DM"/>
    </play-state>

    <play-state id="PC3020_PlayMaxInvalid_PP">
      <session-mapping key="errorCode" value="GlobalVars.errorCode" type="String"/>
      <session-mapping key="prepaidPINErrorCounter" value="GlobalVars.prepaidPINErrorCounter" type="String"/>
      <audio>
        <prompt id="PC3020_out_01" cond="errorCode == 'MAXRETRIESEXCEEDED'">
          <prompt-segments>
            <audiofile src="PC3020_out_01.wav" text="Sorry, you've exceeded the number of times you can try the PIN  Your account will be locked for THIS type of payment until tomorrow"/>
          </prompt-segments>
        </prompt>
        <prompt id="PC3020_out_03" cond="(prepaidPINErrorCounter == 3) &amp;&amp; !(errorCode == 'MAXRETRIESEXCEEDED')">
          <prompt-segments>
            <audiofile src="PC3020_out_03.wav" text="Sorry, that’s still  not a valid Payment PIN"/>
          </prompt-segments>
        </prompt>
        <prompt id="PC3020_out_02" cond="!(errorCode == 'MAXRETRIESEXCEEDED' || prepaidPINErrorCounter == 3)">
          <prompt-segments>
            <audiofile src="PC3020_out_02.wav" text="Sorry Your account was locked earlier today for THIS type of payment due to too many invalid PIN entriesYou can try this again tomorrow"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PC2015_UseBankCardYN_DM"/>
    </play-state>

  </dialog>
  