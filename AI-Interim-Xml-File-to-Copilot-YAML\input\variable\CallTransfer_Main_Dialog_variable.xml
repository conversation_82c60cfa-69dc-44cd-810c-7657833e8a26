<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="transferTag" value="empty" type="String"/>
  <session-mapping key="cti_MDN" value="empty" type="String"/>
  <session-mapping key="cti_PIN" value="empty" type="String"/>
  <session-mapping key="cti_AuthStatus" value="empty" type="String"/>
  <session-mapping key="cti_Intent" value="empty" type="String"/>
  <session-mapping key="cti_TransferReason" value="empty" type="String"/>
  <session-mapping key="id1" value="empty" type="String"/>
  <session-mapping key="id2" value="empty" type="String"/>
  <session-mapping key="id3" value="empty" type="String"/>
  <session-mapping key="transferNumber" value="empty" type="String"/>
  <session-mapping key="TransferTag" value="New_Customer_Sales_En" type="string"/>
  <session-mapping key="GlobalVars.acctLocked" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.tag" value="request-representative_sales" type="string"/>
  <session-mapping key="GlobalVars.transferReason" value="dbfail" type="string"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="GlobalVars.fromMyMetroCustomerSupport" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.enteredFrom" value="RCC" type="string"/>
  <session-mapping key="callerSaidOperator" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.callType" value="auto_pay" type="string"/>
  <session-mapping key="GlobalVars.troubleshootingTransfer" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.dataCollectionTransfer" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.saidSomethingElse" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.isOTPMDE" value="true" type="boolean"/>
  <session-mapping key="isMDE" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.otherGhostCaller" value="true" type="string"/>
  <session-mapping key="GlobalVars.skipBroadcastMessage" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.playTransferMessage" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.twoFactorAuthOutcome" value="not_attempted" type="string"/>
  <session-mapping key="transferReason" value="dbfail" type="string"/>
  <session-mapping key="GlobalVars.playedAcctLocked" value="false" type="boolean"/>
  <session-mapping key="playNoPINTransferPrompt" value="true" type="boolean"/>
  <session-mapping key="securityQuestionAttempts" value="3" type="integer"/>
  <session-mapping key="paymentsEntryPoint" value="undefined" type="string"/>
  <session-mapping key="playTransferMessage" value="true" type="boolean"/>
  <session-mapping key="tag" value="purchase-phone_nc" type="string"/>
  <session-mapping key="callType" value="troubleshoot" type="string"/>
  <session-mapping key="GetBCSParameters.care_enable_cti_transfer" value="true" type="boolean"/>
  <session-mapping key="GetTransferDestination.status" value="Success" type="string"/>
  <session-mapping key="AttachCTIData.status" value="Success" type="string"/>
  <session-mapping key="CTITransfer.status" value="Success" type="string"/>
</session-mappings>
