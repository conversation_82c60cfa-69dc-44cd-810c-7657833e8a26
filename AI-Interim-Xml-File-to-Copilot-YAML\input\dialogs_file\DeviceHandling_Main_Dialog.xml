<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="DeviceHandling_Main_Dialog">
    <decision-state id="DH1010_CheckCallType_DS">
      <if cond="GlobalVars.callType == 'mdn_change'">
        <if cond="!(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.status &amp;&amp; GlobalVars.GetAccountDetails.status.toUpperCase() == 'SUCCESS')">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <gotodialog next="DeviceHandling_Routing#DH1071_LoginCollectMDN_SD"/>
          <else>
            <if cond="GlobalVars.networkType == 'G'">
              <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
              <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
              <session-mapping key="GlobalVars.paymentInfoRequired" value="true" type="Boolean"/>
              <action next="DH1040_CheckPaymentInfoRequired_JDA"/>
              <else>
                <action next="DH1040_CheckPaymentInfoRequired_JDA"/>
              </else>
            </if>
          </else>
        </if>
        <elseif cond="GlobalVars.tag == 'activate-new_account' || GlobalVars.callType == 'activate_unknownDevice'">
          <gotodialog next="DeviceHandling_Routing#DH1405_CheckContext_DS"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'switch_phone' || GlobalVars.callType == 'lost_phone' || GlobalVars.callType == 'add_line'">
          <action next="DH1040_CheckPaymentInfoRequired_JDA"/>
        </elseif>
        <else>
          <gotodialog next="DeviceHandling_Main#DH1020_DeviceHandlingMenu_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="DH1020_DeviceHandlingMenu_DM" type="CUST">
      <session-mapping key="helpMeOut" value="" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <action label="change-phone_number">
          <session-mapping key="GlobalVars.callType" expr="'mdn_change'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'mdn_change'"/>
          <if cond="!(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.status &amp;&amp; GlobalVars.GetAccountDetails.status.toUpperCase() == 'SUCCESS')">
            <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
            <gotodialog next="DeviceHandling_Routing#DH1071_LoginCollectMDN_SD"/>
            <else>
              <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
              <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
              <session-mapping key="GlobalVars.paymentInfoRequired" value="true" type="Boolean"/>
              <session-mapping key="GlobalVars.billableTask" value="true" type="Boolean"/>
              <session-mapping key="ActivationTable.ACTIVATION_TYPE" expr="'5'"/>
              <session-mapping key="ActivationTable.ACTIVATION_STARTED" expr="getGMTTime()"/>
              <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'47'"/>
              <session-mapping key="ActivationTable.ERROR_TEXT" expr="'FAILURE OR HANG UP'"/>
              <session-mapping key="mdnChangeVars.eventTypeGMT" expr="getEventTime()"/>
              <session-mapping key="mdnChangeVars.status" expr="'incomplete'"/>
              <session-mapping key="mdnChangeVars.eventType" expr="'mdn_change'"/>
              <action next="DH1040_CheckPaymentInfoRequired_JDA"/>
            </else>
          </if>
        </action>
        <action label="switch-phone">
          <session-mapping key="GlobalVars.callType" expr="'switch_phone'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'Activations_Support_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'Activations_Support_Spanish'"/>
            </else>
          </if>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'switch_phone'"/>
        </action>
        <action label="main_menu">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <gotodialog next="DeviceHandling_Routing#DH1075_CheckNLUMenuConfig_DS"/>
        </action>
        <action label="make-payment">
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'make_pmt'"/>
          <action next="DH1090_GoToMakePayment_SD"/>
        </action>
        <action label="add-line">
          <session-mapping key="GlobalVars.callType" expr="'add_line'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'add_line'"/>
        </action>
        <action label="report-phone_lost">
          <session-mapping key="GlobalVars.callType" expr="'lost_phone'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'lost_phone'"/>
          <gotodialog next="DeviceHandling_Routing#DH1085_GoToLostPhone_SD"/>
        </action>
        <action label="something-else_devicehandling">
          <gotodialog next="DeviceHandling_Routing#DH1290_CallTransfer_SD"/>
        </action>
        <action label="default">
          <action next="DH1040_CheckPaymentInfoRequired_JDA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DH1020_ini_04">
                <prompt-segments>
                  <audiofile text="You can say add a line or switch my phone You can also say change my phone number, my device is lost or damaged, or it s something else " src="DH1020_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DH1020_ini_04">
                <prompt-segments>
                  <audiofile text="You can say add a line or switch my phone You can also say change my phone number, my device is lost or damaged, or it s something else " src="DH1020_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DH1020_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say add a line or switch my phone  You can also say change my phone number, my device is lost or damaged, or it s something else " src="DH1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1020_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say add a line or press 1 switch my phone or press 2 change my phone number, 3 My device is lost or damaged, 4 it s something else , 5 You can also say main menu or press 6 " src="DH1020_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1020_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say add a line or press 1 switch my phone or press 2 change my phone number, 3 My device is lost or damaged, 4 it s something else , 5 You can also say main menu or press 6 " src="DH1020_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1020_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say add a line or switch my phone  You can also say change my phone number, my device is lost or damaged, or it s something else " src="DH1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1020_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say add a line or press 1 switch my phone or press 2 change my phone number, 3 My device is lost or damaged, 4 it s something else , 5 You can also say main menu or press 6 " src="DH1020_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1020_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say add a line or press 1 switch my phone or press 2 change my phone number, 3 My device is lost or damaged, 4 it s something else , 5 You can also say main menu or press 6 " src="DH1020_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DH1020_ini_04">
                <prompt-segments>
                  <audiofile text="You can say add a line or switch my phone You can also say change my phone number, my device is lost or damaged, or it s something else " src="DH1020_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="DH1020_DeviceHandlingMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="DH1020_DeviceHandlingMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="DH1040_CheckPaymentInfoRequired_DS">
      <if cond="GlobalVars.paymentInfoRequired == true &amp;&amp; GlobalVars.heardPaymentInfo != true">
        <action next="DH1050_PaymentInfo_PP"/>
        <else>
          <action next="DH1065_ReadyToProceed_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="DH1050_PaymentInfo_PP">
      <session-mapping key="networkType" value="GlobalVars.networkType" type="String"/>
      <audio>
        <prompt id="DH1050_out_04">
          <prompt-segments>
            <audiofile src="DH1050_out_04.wav" text="Please note, charges *may* apply for changing your phone number"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.heardPaymentInfo" value="true" type="Boolean"/>
      <gotodialog next="DeviceHandling_Routing#DH1230_MDNChangeModule_SD"/>
    </play-state>

    <play-state id="DH1065_ReadyToProceed_PP">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="GlobalVars.firstTimeVisitedDH1230" value="true" type="Boolean"/>
      <audio>
        <if cond="callType == 'mdn_change'">
          <prompt id="DH1065_out_01">
            <prompt-segments>
              <audiofile text="Before we change your number, I'll ask you for your account PIN and to select an area code" src="DH1065_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="silence_750ms">
            <prompt-segments>
              <audiofile text="test" src="silence_750ms.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="DH1065_out_03">
            <prompt-segments>
              <audiofile text="When your phone number is changed, this call will drop as we update our network But be assured that your number will definitely be changed You'll be able to see your new phone number by dialing -6-8-6-  Again, that's 6-8-6  " src="DH1065_out_03.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="DH1065_out_04">
            <prompt-segments>
              <audiofile text="Now, let's get started" src="DH1065_out_04.wav"/>
            </prompt-segments>
          </prompt>
        </if>
      </audio>
      <if cond="GlobalVars.securityRequired == true &amp;&amp; GlobalVars.collectedPIN != true">
        <gotodialog next="DeviceHandling_Routing#DH1070_LoginCollectPIN_SD"/>
        <else>
          <gotodialog next="DeviceHandling_Routing#DH1210_CheckCallType_DS"/>
        </else>
      </if>
    </play-state>

    <subdialog-state id="DH1090_GoToMakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="DH1090_GoToMakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DH1090_GoToMakePayment_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  