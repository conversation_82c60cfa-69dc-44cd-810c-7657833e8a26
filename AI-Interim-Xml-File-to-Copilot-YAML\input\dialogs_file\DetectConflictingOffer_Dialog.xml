<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="DetectConflictingOffer_Dialog">
    <decision-state id="DC1001_CheckContext_DS">
      <if cond="(newRatePlan != null &amp;&amp; newRatePlan != '') &amp;&amp; (switchPlanAction == 'transferAfterSelected')">
        <action next="DC1015_TransferConflictingOffer_SD"/>
        <else>
          <action next="DC1005_DetectConflictingOffers_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="DC1005_DetectConflictingOffers_DB_DA">
      <data-access id="DetectConflictingOffers" classname="com.nuance.metro.dataaccess.DetectConflictingOffers">
        <inputs>
          <input-variable name="features"/>
          <input-variable name="featuresThirdParty"/>
          <input-variable name="ratePlan"/>
          <input-variable name="currentPromo"/>
          <input-variable name="futurePromo"/>
          <input-variable name="newRatePlan"/>
          <input-variable name="addFeatures"/>
          <input-variable name="prohibitSoc"/>
          <input-variable name="compatibleAddons"/>
          <input-variable name="isRatePlanChange"/>
          <input-variable name="isAddFeature"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="isOfferConflictDetected"/>
          <output-variable name="inCompatibleFeatures"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.isOfferConflictDetected" expr="DetectConflictingOffers.isOfferConflictDetected"/>
        <session-mapping key="GlobalVars.DetectConflictingOffers" expr="DetectConflictingOffers"/>
        <action next="DC1010_IsConflictDetected_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="DC1010_IsConflictDetected_DS">
      <session-mapping key="doesAccountHasCurrentPromo" value="(GlobalVars.GetAccountDetails.currentPromo&amp;&amp; GlobalVars.GetAccountDetails.currentPromo != undefined&amp;&amp; GlobalVars.GetAccountDetails.currentPromo.promotionCode != undefined&amp;&amp; GlobalVars.GetAccountDetails.currentPromo.promotionCode != '')?true:false;" type="String"/>
      <session-mapping key="doesAccountHasFuturePromo" value="(GlobalVars.GetAccountDetails.futurePromo&amp;&amp; GlobalVars.GetAccountDetails.futurePromo != undefined&amp;&amp; GlobalVars.GetAccountDetails.futurePromo.promotionCode != undefined&amp;&amp; GlobalVars.GetAccountDetails.futurePromo.promotionCode != '')?true:false;" type="String"/>
      <if cond="doesAccountHasCurrentPromo == false">
        <session-mapping key="compatibleExistingPromo" value="true" type="Boolean"/>
      </if>
      <if cond="doesAccountHasFuturePromo == false">
        <session-mapping key="compatibleFuturePromo" value="true" type="Boolean"/>
      </if>
      <if cond="(newRatePlan != null &amp;&amp; newRatePlan != '')">
        <if cond="(GlobalVars.isOfferConflictDetected == true)&amp;&amp; ((compatibleExistingPromo == false)  || (compatibleFuturePromo ==false))">
          <action next="DC1011_MetricsConflictingOffers_JDA"/>
          <elseif cond="(compatibleExistingPromo == false) || (compatibleFuturePromo == false)">
            <action next="DC1105_CheckPromoConflict_JDA"/>
          </elseif>
          <elseif cond="GlobalVars.isOfferConflictDetected  == true">
            <action next="DC1205_PlayFeatureConflicts_PP"/>
          </elseif>
          <else>
            <action next="getReturnLink()"/>
          </else>
        </if>
        <else>
          <if cond="GlobalVars.isOfferConflictDetected == true">
            <action next="DC1011_MetricsConflictingOffers_JDA"/>
            <else>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <decision-state id="DC1011_MetricsConflictingOffers_DS">
      <action next="DC1015_TransferConflictingOffer_SD"/>
    </decision-state>

    <subdialog-state id="DC1015_TransferConflictingOffer_SD">
      <if cond="GlobalVars.isCareTransfer == 'true'">
        <gotodialog next="CallTransfer_Main_Dialog"/>
        <else>
          <gotodialog next="Transfer_Main"/>
        </else>
      </if>
      <action next="DC1015_TransferConflictingOffer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DC1015_TransferConflictingOffer_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <decision-state id="DC1105_CheckPromoConflict_DS">
      <session-mapping key="isCurrentPromoURLorTTSAvailable" value="(GlobalVars.GetAccountDetails.currentPromo &amp;&amp;((GlobalVars.GetAccountDetails.currentPromo.promoFinalPromptURL != undefined&amp;&amp; GlobalVars.GetAccountDetails.currentPromo.promoFinalPromptURL != '') ||(GlobalVars.GetAccountDetails.currentPromo.promoFinalPromptTTS != undefined&amp;&amp; GlobalVars.GetAccountDetails.currentPromo.promoFinalPromptTTS != '')))?true:false;" type="String"/>
      <session-mapping key="isFuturePromoURLorTTSAvailable" value="(GlobalVars.GetAccountDetails.futurePromo &amp;&amp;((GlobalVars.GetAccountDetails.futurePromo.promoFinalPromptURL != undefined&amp;&amp; GlobalVars.GetAccountDetails.futurePromo.promoFinalPromptURL != '') ||(GlobalVars.GetAccountDetails.futurePromo.promoFinalPromptTTS != undefined&amp;&amp; GlobalVars.GetAccountDetails.futurePromo.promoFinalPromptTTS != '')))?true:false;" type="String"/>
      <if cond="(compatibleExistingPromo == false &amp;&amp; isCurrentPromoURLorTTSAvailable == false)|| (compatibleFuturePromo == false &amp;&amp; isFuturePromoURLorTTSAvailable == false)">
        <action next="DC1015_TransferConflictingOffer_SD"/>
        <elseif cond="compatibleExistingPromo == false &amp;&amp; compatibleFuturePromo == false">
          <action next="DC1120_PlayBothPromoConflicts_PP"/>
        </elseif>
        <elseif cond="compatibleFuturePromo == false">
          <action next="DC1115_PlayFuturePromoConflict_PP"/>
        </elseif>
        <else>
          <action next="DC1110_PlayCurrentPromoConflict_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="DC1110_PlayCurrentPromoConflict_PP">
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.PlayCurrentPromoConflict"/>
        </prompt>
      </audio>
      <action next="DC1125_AskContinueCancelPromo_DM"/>
    </play-state>

    <play-state id="DC1115_PlayFuturePromoConflict_PP">
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.PlayFuturePromoConflict"/>
        </prompt>
      </audio>
      <action next="DC1125_AskContinueCancelPromo_DM"/>
    </play-state>

    <play-state id="DC1120_PlayBothPromoConflicts_PP">
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.PlayBothPromoConflicts"/>
        </prompt>
      </audio>
      <action next="DC1125_AskContinueCancelPromo_DM"/>
    </play-state>

    <dm-state id="DC1125_AskContinueCancelPromo_DM" type="CUST">
      <success>
        <action label="continue">
          <if cond="compatibleExistingPromo == false">
            <session-mapping key="GlobalVars.removeRegularPromoSoc" expr="GlobalVars.GetAccountDetails.currentPromo.promotionCode"/>
          </if>
          <if cond="compatibleFuturePromo == false">
            <session-mapping key="GlobalVars.removeFuturePromoSoc" expr="GlobalVars.GetAccountDetails.futurePromo.promotionCode"/>
          </if>
          <action next="getReturnLink()"/>
        </action>
        <action label="cancel">
          <if cond="GlobalVars.callType == 'switch_phone' || GlobalVars.callType == 'esn_swap'">
            <session-mapping key="GlobalVars.cancelESNSwap" value="true" type="Boolean"/>
            <else>
              <session-mapping key="GlobalVars.cancelPlanChange" value="true" type="Boolean"/>
            </else>
          </if>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DC1125_ini_01">
                <prompt-segments>
                  <audiofile text="Say  continue , or  cancel " src="DC1125_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DC1125_ini_01">
                <prompt-segments>
                  <audiofile text="Say  continue , or  cancel " src="DC1125_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DC1125_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  continue  or  cancel " src="DC1125_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="DC1125_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to continue with your plan change and remove your promotion, say  continue  or press 1 Otherwise, say  cancel  or press 2" src="DC1125_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DC1125_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  continue  or  cancel " src="DC1125_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DC1125_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to continue with your plan change and remove your promotion, say  continue  or press 1 Otherwise, say  cancel  or press 2" src="DC1125_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DC1125_ini_01">
                <prompt-segments>
                  <audiofile text="Say  continue , or  cancel " src="DC1125_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
          <notoconfirmprompts count="2"/>
          <notoconfirmprompts count="3"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="DC1125_AskContinueCancelPromo_DM.grxml" count="1"/>
          <dtmfgrammars filename="DC1125_AskContinueCancelPromo_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'continue'">
                <prompt id="DC1125_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You want to change your plan and remove your promotion" src="DC1125_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'cancel'">
                <prompt id="DC1125_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="You want to cancel this plan change" src="DC1125_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="DC1125_AskContinueCancelPromo_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="DC1205_PlayFeatureConflicts_PP">
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.PlayFeatureConflicts"/>
          <param name="compatibleAddons" value="compatibleAddons" scope="request"/>
          <param name="newRatePlan" value="newRatePlan" scope="request"/>
          <param name="features" value="features" scope="request"/>
          <param name="featuresThirdParty" value="featuresThirdParty" scope="request"/>
        </prompt>
      </audio>
      <action next="DC1210_AskContinueCancelFeature_DM"/>
    </play-state>

    <dm-state id="DC1210_AskContinueCancelFeature_DM" type="CUST">
      <success>
        <action label="continue">
          <session-mapping key="GlobalVars.removeFeatures" expr="GlobalVars.DetectConflictingOffers.inCompatibleFeatures"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="cancel">
          <if cond="GlobalVars.callType == 'switch_phone' || GlobalVars.callType == 'esn_swap'">
            <session-mapping key="GlobalVars.cancelESNSwap" value="true" type="Boolean"/>
            <else>
              <session-mapping key="GlobalVars.cancelPlanChange" value="true" type="Boolean"/>
            </else>
          </if>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DC1210_ini_01">
                <prompt-segments>
                  <audiofile text="Say  continue , or  cancel " src="DC1210_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DC1210_ini_01">
                <prompt-segments>
                  <audiofile text="Say  continue , or  cancel " src="DC1210_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DC1210_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  continue  or  cancel " src="DC1210_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="DC1210_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to continue with your plan change and remove your feature, say  continue  or press 1 Otherwise, say  cancel  or press 2" src="DC1210_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DC1210_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  continue  or  cancel " src="DC1210_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DC1210_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to continue with your plan change and remove your feature, say  continue  or press 1 Otherwise, say  cancel  or press 2" src="DC1210_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DC1210_ini_01">
                <prompt-segments>
                  <audiofile text="Say  continue , or  cancel " src="DC1210_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
          <notoconfirmprompts count="2"/>
          <notoconfirmprompts count="3"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="DC1210_AskContinueCancelFeature_DM.grxml" count="1"/>
          <dtmfgrammars filename="DC1210_AskContinueCancelFeature_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'continue'">
                <prompt id="DC1210_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You want to change your plan and remove your promotion" src="DC1210_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'cancel'">
                <prompt id="DC1210_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="You want to cancel this plan change" src="DC1210_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  