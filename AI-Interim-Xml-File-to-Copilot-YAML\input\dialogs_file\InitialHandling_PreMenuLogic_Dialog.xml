<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="InitialHandling_PreMenuLogic_Dialog">
    <decision-state id="IH1205_CheckAccountStatus_DS">
      <session-mapping key="careAlterHandContainment" value="GlobalVars.GetBCSParameters.care_enable_alternative_handling_containment" type="String"/>
      <session-mapping key="isDataCollectionEnabled" value="GlobalVars.GetBCSParameters.care_data_collection_enabled" type="String"/>
      <session-mapping key="serviceDialed" value="GlobalVars.serviceDialed" type="String"/>
      <session-mapping key="GlobalVars.playPaymentNLUPrompt" value="false" type="Boolean"/>
      <if cond="GlobalVars.enteredFrom == 'WNP'">
        <if cond="GlobalVars.aniMatch == true &amp;&amp; GlobalVars.GetAccountDetails &amp;&amp;  GlobalVars.GetAccountDetails.accountStatus == 'suspended'">
          <action next="IH1210_SuspendedHandling_SD"/>
          <else>
            <action next="getReturnLink()"/>
          </else>
        </if>
        <elseif cond="(GlobalVars.serviceDialed == '888-111' &amp;&amp; isDataCollectionEnabled == 'true') &amp;&amp; language == 'en-US'">
          <action next="IH1220_DataCollection_SD"/>
        </elseif>
        <elseif cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.ratePlan.toLowerCase() == 'emp'">
          <action next="IH1505_EmployeeInfoMenu_DM"/>
        </elseif>
        <elseif cond="GlobalVars.serviceDialed == '99' &amp;&amp; GlobalVars.returningFromPayments == false">
          <action next="IH1701_CheckPaymentsDialContext_JDA"/>
        </elseif>
        <elseif cond="(GlobalVars.GetAccountDetails == undefined || GlobalVars.GetAccountDetails == null) &amp;&amp; (GlobalVars.callType == undefined || GlobalVars.callType == null)">
          <action next="IH1215_IdentifyUpfront_SD"/>
        </elseif>
        <elseif cond="GlobalVars.GetAccountDetails.accountStatus == 'suspended'">
          <action next="IH1210_SuspendedHandling_SD"/>
        </elseif>
        <elseif cond="GlobalVars.GetAccountDetails &amp;&amp;  GlobalVars.GetAccountDetails.accountStatus == 'suspended'">
          <action next="IH1615_MainMenu_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'device_handling' || GlobalVars.callType == 'mdn_change'">
          <action next="IH1615_MainMenu_SD"/>
        </elseif>
        <elseif cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountType &amp;&amp; (GlobalVars.GetAccountDetails.accountType == 'PIA' || GlobalVars.GetAccountDetails.accountType == 'HVM')">
          <action next="IH1301_CheckProactiveBalanceConfig_JDA"/>
        </elseif>
        <else>
          <action next="IH1805_CheckLanguage_JDA"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="IH1210_SuspendedHandling_SD">
      <gotodialog next="SuspendedHandling_Main_Dialog"/>
      <action next="IH1210_SuspendedHandling_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1210_SuspendedHandling_SD_return_CS">
      <if cond="GlobalVars.switchLinesSuccess == true">
        <action next="IH1805_CheckLanguage_JDA"/>
        <elseif cond="GlobalVars.enteredFrom == 'WNP'">
          <action next="getReturnLink()"/>
        </elseif>
        <else>
          <action next="IH1815_NLUMainMenu_SD"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="IH1215_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="IH_PreMenuLogic_TerminalSD_return_CS"/>
    </subdialog-state>
    <subdialog-state id="IH1215_IdentifyUpfront_SD">
      <gotodialog next="IdentifyUpfront_Dialog"/>
      <action next="IH1215_IdentifyUpfront_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1215_IdentifyUpfront_SD_return_CS">
      <if cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'active' &amp;&amp;  GlobalVars.GetAccountDetails.parentDeviceType == 'INT')">
        <session-mapping key="GlobalVars.tag" expr="'home-internet_active'"/>
        <action next="IH1230_GoToHomeInternetMenu_SD"/>
        <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'active' &amp;&amp;  GlobalVars.GetAccountDetails.parentDeviceType != 'INT')">
          <action next="IH1805_CheckLanguage_JDA"/>
        </elseif>
        <else>
          <action next="IH1210_SuspendedHandling_SD"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="IH1230_GoToHomeInternetMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
      <action next="IH1230_GoToHomeInternetMenu_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1230_GoToHomeInternetMenu_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="IH1220_DataCollection_SD">
      <gotodialog next="CollectData_Dialog"/>
      <action next="IH1220_DataCollection_SD_return"/>
    </subdialog-state>
    <decision-state id="IH1301_CheckProactiveBalanceConfig_DS">
      <session-mapping key="hasAutopay" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <if cond="(GlobalVars.enteredFrom == 'OMNI' &amp;&amp; GlobalVars.lastCustomerIntent != undefined &amp;&amp; GlobalVars.lastCustomerIntent != '' )">
        <action next="IH1805_CheckLanguage_JDA"/>
        <elseif cond="((GlobalVars.GetBCSParameters.care_enable_proactive_balance == 'true' || GlobalVars.GetBCSParameters.care_enable_proactive_balance == true) &amp;&amp; hasAutopay == false)">
          <action next="IH1305_PlayProactiveBalance_PP"/>
        </elseif>
        <else>
          <action next="IH1905_CheckProactiveDataConfig_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="IH1305_PlayProactiveBalance_PP">
      <session-mapping key="balance" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="hasAutopay" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="dayEveryMonthX" value="" type="String"/>
      <session-mapping key="remainingDays" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails.payDate" type="String"/>
      <session-mapping key="autoEligPlan" value="GlobalVars.GetAccountDetails.isAutopayEligPlanExists" type="String"/>
      <audio>
        <if cond="(balance == 0 &amp;&amp; dueImmediatelyAmount == 0)">
          <prompt id="IH1305_out_01" cond="(hasAutopay == true)">
            <prompt-segments>
              <audiofile text="You're all paid up, and your auto-payments are scheduled for" src="IH1305_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="IH1305_out_02" cond="(hasAutopay != true)">
            <prompt-segments>
              <audiofile text="You're all paid up, and your payments are due" src="IH1305_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <prompt type="custom" expr="dayEveryMonthX" scope="request">
            <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.VXMLDynamicPromptNameRendererImpl"/>
            <param name="tts" value="dayEveryMonthX" scope="request"/>
          </prompt>
          <elseif cond="(hasAutopay)">
            <prompt id="IH1305_out_03" cond="(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)">
              <prompt-segments>
                <audiofile text="You're all paid up, and your next auto-payment for" src="IH1305_out_03.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="IH1305_out_04" cond="!(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)">
              <prompt-segments>
                <audiofile text="Your auto-payment for" src="IH1305_out_04.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="balance">
              <param value="false" name="playZeroCents"/>
            </prompt>
            <prompt id="IH1305_out_05" cond="(remainingDays == 0)">
              <prompt-segments>
                <audiofile text="is being processed today" src="IH1305_out_05.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="IH1305_out_06" cond="(remainingDays == 1)">
              <prompt-segments>
                <audiofile text="is scheduled for tomorrow" src="IH1305_out_06.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="IH1305_out_07" cond="(remainingDays == 2)">
              <prompt-segments>
                <audiofile text="is scheduled for the day after tomorrow" src="IH1305_out_07.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="IH1305_out_08" cond="(remainingDays &gt; 2)">
              <prompt-segments>
                <audiofile text="is scheduled for" src="IH1305_out_08.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="date" expr="payDate" cond="(remainingDays &gt; 2)">
              <param name="dateFormat" value="MMM dd, yyyy"/>
              <param name="playDayOfMonth" value="true"/>
              <param name="playYear" value="false"/>
              <param name="playDayOfTheWeek" value="false"/>
              <param name="intonation" value="f"/>
            </prompt>
          </elseif>
          <else>
            <prompt id="IH1305_out_09" cond="(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)">
              <prompt-segments>
                <audiofile text="You're all paid up, and your next payment for" src="IH1305_out_09.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="IH1305_out_10" cond="!(remainingDays &gt; 20 &amp;&amp; dueImmediatelyAmount == 0)">
              <prompt-segments>
                <audiofile text="Your payment for" src="IH1305_out_10.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="balance">
              <param value="false" name="playZeroCents"/>
            </prompt>
            <prompt id="IH1305_out_11" cond="(remainingDays == 0)">
              <prompt-segments>
                <audiofile text="is due today" src="IH1305_out_11.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="IH1305_out_12" cond="(remainingDays == 1)">
              <prompt-segments>
                <audiofile text="is due tomorrow" src="IH1305_out_12.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="IH1305_out_13" cond="(remainingDays == 2)">
              <prompt-segments>
                <audiofile text="is due the day after tomorrow" src="IH1305_out_13.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="IH1305_out_14" cond="(remainingDays &gt; 2)">
              <prompt-segments>
                <audiofile text="is due" src="IH1305_out_14.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="date" expr="payDate" cond="(remainingDays &gt; 2)">
              <param name="dateFormat" value="MMM dd, yyyy"/>
              <param name="playDayOfMonth" value="true"/>
              <param name="playYear" value="false"/>
              <param name="playDayOfTheWeek" value="false"/>
              <param name="intonation" value="f"/>
            </prompt>
          </else>
        </if>
        <if cond="(dueImmediatelyAmount &gt; 0)">
          <prompt id="IH1305_out_15">
            <prompt-segments>
              <audiofile text="You also have a charge due right now, for" src="IH1305_out_15.wav"/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="dueImmediatelyAmount">
            <param value="false" name="playZeroCents"/>
          </prompt>
        </if>
        <if cond="(autoEligPlan ==  true &amp;&amp; hasAutopay == false)">
          <prompt id="IH1305_out_21">
            <prompt-segments>
              <audiofile text="By the way, if you enroll in autopay you'll get a discount off your monthly payment" src="IH1305_out_21.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="IH1905_CheckProactiveDataConfig_JDA"/>
    </play-state>

    <dm-state id="IH1505_EmployeeInfoMenu_DM" type="YSNO">
      <session-mapping key="accountBalance" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="paymentStatus" value="GlobalVars.GetAccountDetails.paymentStatus" type="String"/>
      <session-mapping key="isActiveStatus" value="GlobalVars.GetAccountDetails.paymentStatus == 'active' &amp;&amp; accountBalance &gt; 0" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails.payDate" type="String"/>
      <session-mapping key="payDateDay" value="GlobalVars.GetAccountDetails.payDateDay" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="totalAmountDue" value="(parseFloat(accountBalance) + parseFloat(dueImmediatelyAmount)).toString()" type="String"/>
      <success>
        <action label="true">
          <action next="IH1610_GoToMakePayment_SD"/>
        </action>
        <action label="false">
          <action next="IH1620_GoToGoodbye_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="IH1505_ini_01" cond="totalAmountDue &lt;= 0">
                <prompt-segments>
                  <audiofile text="Oh, and just so you know, your account s all paid up" src="IH1505_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                  <param name="optionalBoolean" value="isActiveStatus" scope="request"/>
                </condition>
                <prompt id="IH1505_ini_02" cond="totalAmountDue &gt; 0">
                  <prompt-segments>
                    <audiofile text="Just as a reminder, your payment" src="IH1505_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                    <param name="optionalBoolean" value="isActiveStatus" scope="request"/>
                  </condition>
                  <prompt id="IH1505_ini_03" cond="totalAmountDue &gt; 0">
                    <prompt-segments>
                      <audiofile text="Just to remind you, your payment," src="IH1505_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                    <param name="optionalBoolean" value="isActiveStatus" scope="request"/>
                  </condition>
                  <prompt id="IH1505_ini_04" cond="totalAmountDue &gt; 0">
                    <prompt-segments>
                      <audiofile text="By the way, your payment," src="IH1505_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="IH1505_ini_05" cond="(paymentStatus == 'expire_within_week' || paymentStatus == 'expire_next_day' || paymentStatus == 'expire_today')  &amp;&amp; accountBalance &gt; 0">
                <prompt-segments>
                  <audiofile text="Hmm, I ve pulled up your account, and it looks like your payment," src="IH1505_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="totalAmountDue" cond="(paymentStatus == 'active' || paymentStatus == 'expire_within_week' || paymentStatus == 'expire_next_day' || paymentStatus == 'expire_today')  &amp;&amp; accountBalance &gt; 0">
                <param value="false" name="playZeroCents"/>
              </prompt>
              <if cond="(paymentStatus == 'expire_within_week' || paymentStatus == 'active') &amp;&amp; accountBalance &gt; 0">
                <prompt id="due_1st" cond="payDateDay == '1'">
                  <prompt-segments>
                    <audiofile text=" is due on the first" src="due_1st.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_2nd" cond="payDateDay == '2'">
                  <prompt-segments>
                    <audiofile text=" is due on the 2nd" src="due_2nd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_3rd" cond="payDateDay == '3'">
                  <prompt-segments>
                    <audiofile text=" is due on the 3rd" src="due_3rd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_4th" cond="payDateDay == '4'">
                  <prompt-segments>
                    <audiofile text=" is due on the 4th" src="due_4th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_5th" cond="payDateDay == '5'">
                  <prompt-segments>
                    <audiofile text=" is due on the 5th" src="due_5th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_6th" cond="payDateDay == '6'">
                  <prompt-segments>
                    <audiofile text=" is due on the 6th" src="due_6th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_7th" cond="payDateDay == '7'">
                  <prompt-segments>
                    <audiofile text=" is due on the 7th" src="due_7th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_8th" cond="payDateDay == '8'">
                  <prompt-segments>
                    <audiofile text=" is due on the 8th" src="due_8th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_9th" cond="payDateDay == '9'">
                  <prompt-segments>
                    <audiofile text=" is due on the 9th" src="due_9th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_10th" cond="payDateDay == '10'">
                  <prompt-segments>
                    <audiofile text=" is due on the 10th" src="due_10th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_11th" cond="payDateDay == '11'">
                  <prompt-segments>
                    <audiofile text=" is due on the 11th" src="due_11th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_12th" cond="payDateDay == '12'">
                  <prompt-segments>
                    <audiofile text=" is due on the 12th" src="due_12th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_13th" cond="payDateDay == '13'">
                  <prompt-segments>
                    <audiofile text=" is due on the 13th" src="due_13th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_14th" cond="payDateDay == '14'">
                  <prompt-segments>
                    <audiofile text=" is due on the 14th" src="due_14th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_15th" cond="payDateDay == '15'">
                  <prompt-segments>
                    <audiofile text=" is due on the 15th" src="due_15th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_16th" cond="payDateDay == '16'">
                  <prompt-segments>
                    <audiofile text=" is due on the 16th" src="due_16th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_17th" cond="payDateDay == '17'">
                  <prompt-segments>
                    <audiofile text=" is due on the 17th" src="due_17th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_18th" cond="payDateDay == '18'">
                  <prompt-segments>
                    <audiofile text=" is due on the 18th" src="due_18th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_19th" cond="payDateDay == '19'">
                  <prompt-segments>
                    <audiofile text=" is due on the 19th" src="due_19th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_20th" cond="payDateDay == '20'">
                  <prompt-segments>
                    <audiofile text=" is due on the 20th" src="due_20th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_21st" cond="payDateDay == '21'">
                  <prompt-segments>
                    <audiofile text=" is due on the 21st" src="due_21st.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_22nd" cond="payDateDay == '22'">
                  <prompt-segments>
                    <audiofile text=" is due on the 22nd" src="due_22nd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_23rd" cond="payDateDay == '23'">
                  <prompt-segments>
                    <audiofile text=" is due on the 23rd" src="due_23rd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_24th" cond="payDateDay == '24'">
                  <prompt-segments>
                    <audiofile text=" is due on the 24th" src="due_24th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_25th" cond="payDateDay == '25'">
                  <prompt-segments>
                    <audiofile text=" is due on the 25th" src="due_25th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_26th" cond="payDateDay == '26'">
                  <prompt-segments>
                    <audiofile text=" is due on the 26th" src="due_26th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_27th" cond="payDateDay == '27'">
                  <prompt-segments>
                    <audiofile text=" is due on the 27th" src="due_27th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_28th" cond="payDateDay == '28'">
                  <prompt-segments>
                    <audiofile text=" is due on the 28th" src="due_28th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_29th" cond="payDateDay == '29'">
                  <prompt-segments>
                    <audiofile text=" is due on the 29th" src="due_29th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_30th" cond="payDateDay == '30'">
                  <prompt-segments>
                    <audiofile text=" is due on the 30th" src="due_30th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_31st" cond="payDateDay == '31'">
                  <prompt-segments>
                    <audiofile text=" is due on the 31st" src="due_31st.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="paymentStatus == 'expire_next_day' &amp;&amp; accountBalance &gt; 0">
                <prompt id="IH1505_ini_08">
                  <prompt-segments>
                    <audiofile text=" is due tomorrow," src="IH1505_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="paymentStatus == 'expire_today'">
                <prompt id="IH1505_ini_09">
                  <prompt-segments>
                    <audiofile text=" is due today," src="IH1505_ini_09.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(paymentStatus == 'expire_today' || paymentStatus == 'expire_next_day') &amp;&amp; accountBalance &gt; 0">
                <prompt type="date" expr="payDate">
                  <param name="dateFormat" value="MMM dd, yyyy"/>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IH1505_ini_16">
                <prompt-segments>
                  <audiofile text="Now, would you like to make a payment?" src="IH1505_ini_16.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="IH1505_ini_01" cond="totalAmountDue &lt;= 0">
                <prompt-segments>
                  <audiofile text="Oh, and just so you know, your account s all paid up" src="IH1505_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                  <param name="optionalBoolean" value="isActiveStatus" scope="request"/>
                </condition>
                <prompt id="IH1505_ini_02" cond="totalAmountDue &gt; 0">
                  <prompt-segments>
                    <audiofile text="Just as a reminder, your payment" src="IH1505_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                    <param name="optionalBoolean" value="isActiveStatus" scope="request"/>
                  </condition>
                  <prompt id="IH1505_ini_03" cond="totalAmountDue &gt; 0">
                    <prompt-segments>
                      <audiofile text="Just to remind you, your payment," src="IH1505_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                    <param name="optionalBoolean" value="isActiveStatus" scope="request"/>
                  </condition>
                  <prompt id="IH1505_ini_04" cond="totalAmountDue &gt; 0">
                    <prompt-segments>
                      <audiofile text="By the way, your payment," src="IH1505_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="IH1505_ini_05" cond="(paymentStatus == 'expire_within_week' || paymentStatus == 'expire_next_day' || paymentStatus == 'expire_today')  &amp;&amp; accountBalance &gt; 0">
                <prompt-segments>
                  <audiofile text="Hmm, I ve pulled up your account, and it looks like your payment," src="IH1505_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="totalAmountDue" cond="(paymentStatus == 'active' || paymentStatus == 'expire_within_week' || paymentStatus == 'expire_next_day' || paymentStatus == 'expire_today')  &amp;&amp; accountBalance &gt; 0">
                <param value="false" name="playZeroCents"/>
              </prompt>
              <if cond="(paymentStatus == 'expire_within_week' || paymentStatus == 'active') &amp;&amp; accountBalance &gt; 0">
                <prompt id="due_1st" cond="payDateDay == '1'">
                  <prompt-segments>
                    <audiofile text=" is due on the first" src="due_1st.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_2nd" cond="payDateDay == '2'">
                  <prompt-segments>
                    <audiofile text=" is due on the 2nd" src="due_2nd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_3rd" cond="payDateDay == '3'">
                  <prompt-segments>
                    <audiofile text=" is due on the 3rd" src="due_3rd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_4th" cond="payDateDay == '4'">
                  <prompt-segments>
                    <audiofile text=" is due on the 4th" src="due_4th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_5th" cond="payDateDay == '5'">
                  <prompt-segments>
                    <audiofile text=" is due on the 5th" src="due_5th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_6th" cond="payDateDay == '6'">
                  <prompt-segments>
                    <audiofile text=" is due on the 6th" src="due_6th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_7th" cond="payDateDay == '7'">
                  <prompt-segments>
                    <audiofile text=" is due on the 7th" src="due_7th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_8th" cond="payDateDay == '8'">
                  <prompt-segments>
                    <audiofile text=" is due on the 8th" src="due_8th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_9th" cond="payDateDay == '9'">
                  <prompt-segments>
                    <audiofile text=" is due on the 9th" src="due_9th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_10th" cond="payDateDay == '10'">
                  <prompt-segments>
                    <audiofile text=" is due on the 10th" src="due_10th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_11th" cond="payDateDay == '11'">
                  <prompt-segments>
                    <audiofile text=" is due on the 11th" src="due_11th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_12th" cond="payDateDay == '12'">
                  <prompt-segments>
                    <audiofile text=" is due on the 12th" src="due_12th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_13th" cond="payDateDay == '13'">
                  <prompt-segments>
                    <audiofile text=" is due on the 13th" src="due_13th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_14th" cond="payDateDay == '14'">
                  <prompt-segments>
                    <audiofile text=" is due on the 14th" src="due_14th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_15th" cond="payDateDay == '15'">
                  <prompt-segments>
                    <audiofile text=" is due on the 15th" src="due_15th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_16th" cond="payDateDay == '16'">
                  <prompt-segments>
                    <audiofile text=" is due on the 16th" src="due_16th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_17th" cond="payDateDay == '17'">
                  <prompt-segments>
                    <audiofile text=" is due on the 17th" src="due_17th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_18th" cond="payDateDay == '18'">
                  <prompt-segments>
                    <audiofile text=" is due on the 18th" src="due_18th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_19th" cond="payDateDay == '19'">
                  <prompt-segments>
                    <audiofile text=" is due on the 19th" src="due_19th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_20th" cond="payDateDay == '20'">
                  <prompt-segments>
                    <audiofile text=" is due on the 20th" src="due_20th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_21st" cond="payDateDay == '21'">
                  <prompt-segments>
                    <audiofile text=" is due on the 21st" src="due_21st.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_22nd" cond="payDateDay == '22'">
                  <prompt-segments>
                    <audiofile text=" is due on the 22nd" src="due_22nd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_23rd" cond="payDateDay == '23'">
                  <prompt-segments>
                    <audiofile text=" is due on the 23rd" src="due_23rd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_24th" cond="payDateDay == '24'">
                  <prompt-segments>
                    <audiofile text=" is due on the 24th" src="due_24th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_25th" cond="payDateDay == '25'">
                  <prompt-segments>
                    <audiofile text=" is due on the 25th" src="due_25th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_26th" cond="payDateDay == '26'">
                  <prompt-segments>
                    <audiofile text=" is due on the 26th" src="due_26th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_27th" cond="payDateDay == '27'">
                  <prompt-segments>
                    <audiofile text=" is due on the 27th" src="due_27th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_28th" cond="payDateDay == '28'">
                  <prompt-segments>
                    <audiofile text=" is due on the 28th" src="due_28th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_29th" cond="payDateDay == '29'">
                  <prompt-segments>
                    <audiofile text=" is due on the 29th" src="due_29th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_30th" cond="payDateDay == '30'">
                  <prompt-segments>
                    <audiofile text=" is due on the 30th" src="due_30th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_31st" cond="payDateDay == '31'">
                  <prompt-segments>
                    <audiofile text=" is due on the 31st" src="due_31st.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="paymentStatus == 'expire_next_day' &amp;&amp; accountBalance &gt; 0">
                <prompt id="IH1505_ini_08">
                  <prompt-segments>
                    <audiofile text=" is due tomorrow," src="IH1505_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="paymentStatus == 'expire_today'">
                <prompt id="IH1505_ini_09">
                  <prompt-segments>
                    <audiofile text=" is due today," src="IH1505_ini_09.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(paymentStatus == 'expire_today' || paymentStatus == 'expire_next_day') &amp;&amp; accountBalance &gt; 0">
                <prompt type="date" expr="payDate">
                  <param name="dateFormat" value="MMM dd, yyyy"/>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IH1505_ini_16">
                <prompt-segments>
                  <audiofile text="Now, would you like to make a payment?" src="IH1505_ini_16.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="IH1505_nm1_02">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment?  Please say  yes  or  no " src="IH1505_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IH1505_nm2_02">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment?  Please say  yes  or press 1  Say  no  or press 2" src="IH1505_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IH1505_nm2_02">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment?  Please say  yes  or press 1  Say  no  or press 2" src="IH1505_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IH1505_nm1_02">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment?  Please say  yes  or  no " src="IH1505_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IH1505_nm2_02">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment?  Please say  yes  or press 1  Say  no  or press 2" src="IH1505_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IH1505_nm2_02">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment?  Please say  yes  or press 1  Say  no  or press 2" src="IH1505_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="IH1505_ini_01" cond="totalAmountDue &lt;= 0">
                <prompt-segments>
                  <audiofile text="Oh, and just so you know, your account s all paid up" src="IH1505_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                  <param name="optionalBoolean" value="isActiveStatus" scope="request"/>
                </condition>
                <prompt id="IH1505_ini_02" cond="totalAmountDue &gt; 0">
                  <prompt-segments>
                    <audiofile text="Just as a reminder, your payment" src="IH1505_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                    <param name="optionalBoolean" value="isActiveStatus" scope="request"/>
                  </condition>
                  <prompt id="IH1505_ini_03" cond="totalAmountDue &gt; 0">
                    <prompt-segments>
                      <audiofile text="Just to remind you, your payment," src="IH1505_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                    <param name="optionalBoolean" value="isActiveStatus" scope="request"/>
                  </condition>
                  <prompt id="IH1505_ini_04" cond="totalAmountDue &gt; 0">
                    <prompt-segments>
                      <audiofile text="By the way, your payment," src="IH1505_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="IH1505_ini_05" cond="(paymentStatus == 'expire_within_week' || paymentStatus == 'expire_next_day' || paymentStatus == 'expire_today')  &amp;&amp; accountBalance &gt; 0">
                <prompt-segments>
                  <audiofile text="Hmm, I ve pulled up your account, and it looks like your payment," src="IH1505_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="totalAmountDue" cond="(paymentStatus == 'active' || paymentStatus == 'expire_within_week' || paymentStatus == 'expire_next_day' || paymentStatus == 'expire_today')  &amp;&amp; accountBalance &gt; 0">
                <param value="false" name="playZeroCents"/>
              </prompt>
              <if cond="(paymentStatus == 'expire_within_week' || paymentStatus == 'active') &amp;&amp; accountBalance &gt; 0">
                <prompt id="due_1st" cond="payDateDay == '1'">
                  <prompt-segments>
                    <audiofile text=" is due on the first" src="due_1st.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_2nd" cond="payDateDay == '2'">
                  <prompt-segments>
                    <audiofile text=" is due on the 2nd" src="due_2nd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_3rd" cond="payDateDay == '3'">
                  <prompt-segments>
                    <audiofile text=" is due on the 3rd" src="due_3rd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_4th" cond="payDateDay == '4'">
                  <prompt-segments>
                    <audiofile text=" is due on the 4th" src="due_4th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_5th" cond="payDateDay == '5'">
                  <prompt-segments>
                    <audiofile text=" is due on the 5th" src="due_5th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_6th" cond="payDateDay == '6'">
                  <prompt-segments>
                    <audiofile text=" is due on the 6th" src="due_6th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_7th" cond="payDateDay == '7'">
                  <prompt-segments>
                    <audiofile text=" is due on the 7th" src="due_7th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_8th" cond="payDateDay == '8'">
                  <prompt-segments>
                    <audiofile text=" is due on the 8th" src="due_8th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_9th" cond="payDateDay == '9'">
                  <prompt-segments>
                    <audiofile text=" is due on the 9th" src="due_9th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_10th" cond="payDateDay == '10'">
                  <prompt-segments>
                    <audiofile text=" is due on the 10th" src="due_10th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_11th" cond="payDateDay == '11'">
                  <prompt-segments>
                    <audiofile text=" is due on the 11th" src="due_11th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_12th" cond="payDateDay == '12'">
                  <prompt-segments>
                    <audiofile text=" is due on the 12th" src="due_12th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_13th" cond="payDateDay == '13'">
                  <prompt-segments>
                    <audiofile text=" is due on the 13th" src="due_13th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_14th" cond="payDateDay == '14'">
                  <prompt-segments>
                    <audiofile text=" is due on the 14th" src="due_14th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_15th" cond="payDateDay == '15'">
                  <prompt-segments>
                    <audiofile text=" is due on the 15th" src="due_15th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_16th" cond="payDateDay == '16'">
                  <prompt-segments>
                    <audiofile text=" is due on the 16th" src="due_16th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_17th" cond="payDateDay == '17'">
                  <prompt-segments>
                    <audiofile text=" is due on the 17th" src="due_17th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_18th" cond="payDateDay == '18'">
                  <prompt-segments>
                    <audiofile text=" is due on the 18th" src="due_18th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_19th" cond="payDateDay == '19'">
                  <prompt-segments>
                    <audiofile text=" is due on the 19th" src="due_19th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_20th" cond="payDateDay == '20'">
                  <prompt-segments>
                    <audiofile text=" is due on the 20th" src="due_20th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_21st" cond="payDateDay == '21'">
                  <prompt-segments>
                    <audiofile text=" is due on the 21st" src="due_21st.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_22nd" cond="payDateDay == '22'">
                  <prompt-segments>
                    <audiofile text=" is due on the 22nd" src="due_22nd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_23rd" cond="payDateDay == '23'">
                  <prompt-segments>
                    <audiofile text=" is due on the 23rd" src="due_23rd.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_24th" cond="payDateDay == '24'">
                  <prompt-segments>
                    <audiofile text=" is due on the 24th" src="due_24th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_25th" cond="payDateDay == '25'">
                  <prompt-segments>
                    <audiofile text=" is due on the 25th" src="due_25th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_26th" cond="payDateDay == '26'">
                  <prompt-segments>
                    <audiofile text=" is due on the 26th" src="due_26th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_27th" cond="payDateDay == '27'">
                  <prompt-segments>
                    <audiofile text=" is due on the 27th" src="due_27th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_28th" cond="payDateDay == '28'">
                  <prompt-segments>
                    <audiofile text=" is due on the 28th" src="due_28th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_29th" cond="payDateDay == '29'">
                  <prompt-segments>
                    <audiofile text=" is due on the 29th" src="due_29th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_30th" cond="payDateDay == '30'">
                  <prompt-segments>
                    <audiofile text=" is due on the 30th" src="due_30th.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="due_31st" cond="payDateDay == '31'">
                  <prompt-segments>
                    <audiofile text=" is due on the 31st" src="due_31st.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="paymentStatus == 'expire_next_day' &amp;&amp; accountBalance &gt; 0">
                <prompt id="IH1505_ini_08">
                  <prompt-segments>
                    <audiofile text=" is due tomorrow," src="IH1505_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="paymentStatus == 'expire_today'">
                <prompt id="IH1505_ini_09">
                  <prompt-segments>
                    <audiofile text=" is due today," src="IH1505_ini_09.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(paymentStatus == 'expire_today' || paymentStatus == 'expire_next_day') &amp;&amp; accountBalance &gt; 0">
                <prompt type="date" expr="payDate">
                  <param name="dateFormat" value="MMM dd, yyyy"/>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IH1505_ini_16">
                <prompt-segments>
                  <audiofile text="Now, would you like to make a payment?" src="IH1505_ini_16.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="IH1505_EmployeeInfoMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="IH1505_EmployeeInfoMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="IH1610_GoToMakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="IH_PreMenuLogic_TerminalSD_return_CS"/>
    </subdialog-state>
    <subdialog-state id="IH1615_MainMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
      <action next="IH_PreMenuLogic_TerminalSD_return_CS"/>
    </subdialog-state>
    <subdialog-state id="IH1620_GoToGoodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="IH_PreMenuLogic_TerminalSD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH_PreMenuLogic_TerminalSD_return_CS">
      <action next="IH1805_CheckLanguage_JDA"/>
    </custom-state>

    <decision-state id="IH1701_CheckPaymentsDialContext_DS">
      <session-mapping key="GlobalVars.firstStepFrom99Entry" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <session-mapping key="PaymentTable.ACTIVATION_STARTED" expr="getGMTTime()"/>
      <session-mapping key="PaymentTable.ACTIVATION_TYPE" expr="'9'"/>
      <session-mapping key="PaymentTable.ACTIVATION_MODE" expr="'0'"/>
      <session-mapping key="PaymentTable.CARD_TYPE" expr="'not_set'"/>
      <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'100'"/>
      <action next="IH1710_MakePayment_SD"/>
    </decision-state>

    <subdialog-state id="IH1710_MakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="IH1710_MakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1710_MakePayment_SD_return_CS">
      <if cond="(GlobalVars.tag == 'request-extension')">
        <action next="IH1715_ApplyExtension_SD"/>
        <else>
          <action next="IH1805_CheckLanguage_JDA"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="IH1715_ApplyExtension_SD">
      <gotodialog next="ApplyExtension_Main_Dialog"/>
      <action next="IH1715_ApplyExtension_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1715_ApplyExtension_SD_return_CS">
      <if cond="(GlobalVars.tag == 'make-payment')">
        <action next="IH1710_MakePayment_SD"/>
        <else>
          <action next="IH1805_CheckLanguage_JDA"/>
        </else>
      </if>
    </custom-state>

    <decision-state id="IH1805_CheckLanguage_DS">
      <if cond="(GlobalVars.tag == 'reset-voicemail_pin')">
        <action next="getReturnLink()"/>
        <elseif cond="(language == 'en-US')">
          <action next="IH1810_CheckNLUMenuConfig_JDA"/>
        </elseif>
        <else>
          <action next="IH1825_DirectedDialogMainMenu_SD"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="IH1810_CheckNLUMenuConfig_DS">
      <if cond="(GlobalVars.GetBCSParameters.care_nlu_enabled == 'true')">
        <action next="IH1815_NLUMainMenu_SD"/>
        <else>
          <action next="IH1825_DirectedDialogMainMenu_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="IH1815_NLUMainMenu_SD">
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="hasAccountDetails" value="GlobalVars.GetAccountDetails != undefined ? true : false" type="String"/>
      <session-mapping key="accountStatus" value="(GlobalVars.GetAccountDetails)?GlobalVars.GetAccountDetails.accountStatus :''" type="String"/>
      <session-mapping key="isAutopayEligPlanExists" value="(GlobalVars.GetAccountDetails)? GlobalVars.GetAccountDetails.isAutopayEligPlanExists:false" type="String"/>
      <session-mapping key="loggedIn" value="GlobalVars.loggedIn" type="String"/>
      <gotodialog next="NLUMainMenu_Start_Dialog"/>
      <action next="IH1815_NLUMainMenu_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1815_NLUMainMenu_SD_return_CS">
      <if cond="(GlobalVars.tag == 'request-spanish' || GlobalVars.tag == 'request-spanish_dtmf')">
        <session-mapping key="language" expr="'es-US'"/>
        <session-mapping key="GlobalVars.tag" expr="undefined"/>
        <action next="IH1825_DirectedDialogMainMenu_SD"/>
        <elseif cond="(GlobalVars.tag == 'request-repeat_proactive')">
          <action next="IH1301_CheckProactiveBalanceConfig_JDA"/>
        </elseif>
        <else>
          <if cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ9')">
            <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'not_authenticated_high_security_account')"/>
            <gotodialog next="InitialHandling_Main#IH1605_GoToCallTransfer_SD"/>
            <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ8')">
              <action next="IH1620_GoToGoodbye_SD"/>
            </elseif>
            <else>
              <action next="IH1820_NLUIntentRouting_SD"/>
            </else>
          </if>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="IH1820_NLUIntentRouting_SD">
      <gotodialog next="NLURouting_Start_Dialog"/>
      <action next="IH1820_NLUIntentRouting_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1820_NLUIntentRouting_SD_return_CS">
      <session-mapping key="GlobalVars.nluReEntryAfterSelfService" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.tag" expr="undefined"/>
      <action next="IH1815_NLUMainMenu_SD"/>
    </custom-state>

    <subdialog-state id="IH1825_DirectedDialogMainMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
      <action next="IH_PreMenuLogic_TerminalSD_return_CS"/>
    </subdialog-state>
    <decision-state id="IH1905_CheckProactiveDataConfig_DS">
      <if cond="((GlobalVars.GetBCSParameters.care_enable_proactive_data == 'true' || GlobalVars.GetBCSParameters.care_enable_proactive_data == true) &amp;&amp; GlobalVars.tag == 'request-repeat')">
        <action next="IH1925_CheckPlanType_JDA"/>
        <elseif cond="((GlobalVars.GetBCSParameters.care_enable_proactive_data == 'true' || GlobalVars.GetBCSParameters.care_enable_proactive_data == true) &amp;&amp; GlobalVars.tag != 'request-repeat')">
          <action next="IH1915_CheckHasHighSpeedData_JDA"/>
        </elseif>
        <else>
          <action next="IH1805_CheckLanguage_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="IH1915_CheckHasHighSpeedData_DS">
      <session-mapping key="hasHighSpeedData" value="GlobalVars.GetAccountDetails.hasHighSpeedData" type="String"/>
      <if cond="hasHighSpeedData == 'FromRatePlan' || hasHighSpeedData == 'FromFeature'">
        <action next="IH1920_GetDataUsage_DB_DA"/>
        <else>
          <action next="IH1805_CheckLanguage_JDA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="IH1920_GetDataUsage_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.mdn:''" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.payDate:''" type="String"/>
      <session-mapping key="isBARTET" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.isBARTET:false" type="String"/>
      <session-mapping key="billCycleLength" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.billCycleLength:0" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="isHint" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.parentDeviceType == 'INT')? true : false" type="String"/>
      <session-mapping key="isSuspended" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" type="String"/>
      <session-mapping key="getTopUpHistory" value="isHint == true ? false : true" type="String"/>
      <data-access id="GetDataUsage" classname="com.nuance.metro.dataaccess.GetDataUsage">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="getTopUpHistory"/>
          <input-variable name="payDate"/>
          <input-variable name="sessionId"/>
          <input-variable name="isSuspended"/>
          <input-variable name="isBARTET"/>
        </inputs>
        <outputs>
          <output-variable name="dataFound"/>
          <output-variable name="dataUsed"/>
          <output-variable name="dataCap"/>
          <output-variable name="isUnlimited"/>
          <output-variable name="dataPercentage"/>
          <output-variable name="dataUsage_dataFound"/>
          <output-variable name="dataUsage_dataUsed"/>
          <output-variable name="dataUsage_dataCap"/>
          <output-variable name="dataUsage_dataUsed_KB"/>
          <output-variable name="dataUsage_dataCap_KB"/>
          <output-variable name="dataUsage_isUnlimited"/>
          <output-variable name="dataUsage_dataPercentage"/>
          <output-variable name="topUpHotSpot_dataFound"/>
          <output-variable name="topUpHotSpot_dataUsed"/>
          <output-variable name="topUpHotSpot_dataCap"/>
          <output-variable name="topUpHotSpot_dataUsed_KB"/>
          <output-variable name="topUpHotSpot_dataCap_KB"/>
          <output-variable name="topUpHotSpot_isUnlimited"/>
          <output-variable name="topUpHotSpot_dataPercentage"/>
          <output-variable name="topUp_feature1_code"/>
          <output-variable name="topUp_feature1_featureName_en"/>
          <output-variable name="topUp_feature1_featureName_es"/>
          <output-variable name="topUp_feature1_description_en"/>
          <output-variable name="topUp_feature1_description_es"/>
          <output-variable name="topUp_feature1_price"/>
          <output-variable name="numPaidTopUps"/>
          <output-variable name="numGoodWillTopUps "/>
          <output-variable name="featureDetailsPromptURL_en"/>
          <output-variable name="featureDetailsPromptURL_es "/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetDataUsage.status == 'Success'">
          <session-mapping key="GlobalVars.GetDataUsageInfo" expr="GetDataUsage"/>
          <session-mapping key="dataUsage_isUnlimited" expr="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_isUnlimited:false"/>
          <session-mapping key="remainingDays" expr="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.remainingDays:0"/>
          <action next="IH1924_CalculateDataUsage_DB_DA"/>
          <else>
            <action next="IH1805_CheckLanguage_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <data-access-state id="IH1924_CalculateDataUsage_DB_DA">
      <session-mapping key="dataUsage_dataCap_KB" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataCap_KB:0" type="String"/>
      <session-mapping key="topUpHotSpot_dataCap_KB" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataCap_KB:0" type="String"/>
      <session-mapping key="dataUsage_dataUsed_KB" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataUsed_KB:0" type="String"/>
      <session-mapping key="topUpHotSpot_dataUsed_KB" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataUsed_KB:0" type="String"/>
      <data-access classname="com.nuance.metro.dataaccess.CalculateDataUsage" id="CalculateDataUsage">
        <inputs>
          <input-variable mask="false" name="dataUsage_dataCap_KB"/>
          <input-variable mask="false" name="topUpHotSpot_dataCap_KB"/>
          <input-variable mask="false" name="dataUsage_dataUsed_KB"/>
          <input-variable mask="false" name="topUpHotSpot_dataUsed_KB"/>
        </inputs>
        <outputs>
          <output-variable mask="false" name="calculateDataUsage_dataLeftMegabytes"/>
          <output-variable mask="false" name="calculateDataUsage_dataLeftGigabytes"/>
          <output-variable mask="false" name="calculateDataUsage_hotspotDataLeftMegabytes"/>
          <output-variable mask="false" name="calculateDataUsage_hotspotDataLeftGigabytes"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.CalculateDataUsage" expr="CalculateDataUsage"/>
        <session-mapping key="GlobalVars.calculateDataUsage_dataLeftMegabytes" expr="CalculateDataUsage.calculateDataUsage_dataLeftMegabytes"/>
        <session-mapping key="GlobalVars.calculateDataUsage_dataLeftGigabytes" expr="CalculateDataUsage.calculateDataUsage_dataLeftGigabytes"/>
        <session-mapping key="GlobalVars.calculateDataUsage_hotspotDataLeftMegabytes" expr="CalculateDataUsage.calculateDataUsage_hotspotDataLeftMegabytes"/>
        <session-mapping key="GlobalVars.calculateDataUsage_hotspotDataLeftGigabytes" expr="CalculateDataUsage.calculateDataUsage_hotspotDataLeftGigabytes"/>
        <action next="IH1925_CheckPlanType_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="IH1925_CheckPlanType_DS">
      <if cond="(dataUsage_isUnlimited == 'true' || dataUsage_isUnlimited == true)">
        <action next="IH1930_UnlimitedProactiveData_PP"/>
        <else>
          <action next="IH1935_CappedProactiveData_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="IH1930_UnlimitedProactiveData_PP">
      <session-mapping key="topUpHotSpot_dataFound" value="GlobalVars.GetDataUsageInfo.topUpHotSpot_dataFound" type="String"/>
      <session-mapping key="calculateDataUsage_hotspotDataLeftMegabytes" value="GlobalVars.calculateDataUsage_hotspotDataLeftMegabytes" type="String"/>
      <session-mapping key="calculateDataUsage_hotspotDataLeftGigabytes" value="GlobalVars.calculateDataUsage_hotspotDataLeftGigabytes" type="String"/>
      <session-mapping key="topUpHotSpot_dataUsed_KB" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataUsed_KB:0" type="String"/>
      <audio>
        <prompt id="silence_250ms" cond="(topUpHotSpot_dataFound == true) &amp;&amp; (topUpHotSpot_dataUsed_KB != 0)">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="IH1930_out_02" cond="(topUpHotSpot_dataFound == true) &amp;&amp; (topUpHotSpot_dataUsed_KB != 0) &amp;&amp; (parseInt(calculateDataUsage_hotspotDataLeftMegabytes) &lt; 1)">
          <prompt-segments>
            <audiofile text="It looks like you're running out of high speed *hotspot* data - you have less than one megabyte remaining " src="IH1930_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="IH1930_out_03" cond="(topUpHotSpot_dataFound == true) &amp;&amp; (topUpHotSpot_dataUsed_KB != 0) &amp;&amp; (parseInt(calculateDataUsage_hotspotDataLeftMegabytes) &gt;= 1)">
          <prompt-segments>
            <audiofile text="As of now, you have " src="IH1930_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="(topUpHotSpot_dataFound == true) &amp;&amp; (topUpHotSpot_dataUsed_KB != 0) &amp;&amp; (parseInt(calculateDataUsage_hotspotDataLeftMegabytes) &gt;= 1) &amp;&amp; (parseInt(calculateDataUsage_hotspotDataLeftGigabytes) &gt;= 1)">
          <prompt type="natural" expr="calculateDataUsage_hotspotDataLeftGigabytes">
            <param name="intonation" value="m"/>
            <param name="playZeroUnits" value="false"/>
          </prompt>
          <prompt id="IH1930_out_04">
            <prompt-segments>
              <audiofile text="Gigabytes of high speed *hotspot* data remaining" src="IH1930_out_04.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <if cond="(topUpHotSpot_dataFound == true) &amp;&amp; (topUpHotSpot_dataUsed_KB != 0) &amp;&amp; (parseInt(calculateDataUsage_hotspotDataLeftMegabytes) &gt;= 1) &amp;&amp; (parseInt(calculateDataUsage_hotspotDataLeftGigabytes) &lt; 1)">
          <prompt type="natural" expr="calculateDataUsage_hotspotDataLeftMegabytes">
            <param name="intonation" value="m"/>
            <param name="playZeroUnits" value="false"/>
          </prompt>
          <prompt id="IH1930_out_05">
            <prompt-segments>
              <audiofile text="Megabytes of high speed *hotspot* data remaining " src="IH1930_out_05.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="IH1805_CheckLanguage_JDA"/>
    </play-state>

    <play-state id="IH1935_CappedProactiveData_PP">
      <session-mapping key="calculateDataUsage_dataLeftMegabytes" value="GlobalVars.calculateDataUsage_dataLeftMegabytes" type="String"/>
      <session-mapping key="calculateDataUsage_dataLeftGigabytes" value="GlobalVars.calculateDataUsage_dataLeftGigabytes" type="String"/>
      <audio>
        <prompt id="IH1935_out_01" cond="(parseInt(calculateDataUsage_dataLeftMegabytes) &lt; 1)">
          <prompt-segments>
            <audiofile text="It looks like you're running out of high speed data - you have less than one megabyte remaining " src="IH1935_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="IH1935_out_02" cond="(parseInt(calculateDataUsage_dataLeftMegabytes) &gt;= 1)">
          <prompt-segments>
            <audiofile text="As of now, you have " src="IH1935_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="((parseInt(calculateDataUsage_dataLeftMegabytes) &gt;= 1) &amp;&amp; (parseInt(calculateDataUsage_dataLeftGigabytes) &gt;= 1))">
          <prompt type="natural" expr="calculateDataUsage_dataLeftGigabytes">
            <param name="intonation" value="m"/>
            <param name="playZeroUnits" value="false"/>
          </prompt>
          <prompt id="IH1935_out_03">
            <prompt-segments>
              <audiofile text="Gigabytes of high speed data remaining" src="IH1935_out_03.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <if cond="((parseInt(calculateDataUsage_dataLeftMegabytes) &gt;= 1) &amp;&amp; (parseInt(calculateDataUsage_dataLeftGigabytes) &lt; 1))">
          <prompt type="natural" expr="calculateDataUsage_dataLeftMegabytes">
            <param name="intonation" value="m"/>
            <param name="playZeroUnits" value="false"/>
          </prompt>
          <prompt id="IH1935_out_05">
            <prompt-segments>
              <audiofile text="Megabytes of high speed data remaining " src="IH1935_out_05.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="IH1805_CheckLanguage_JDA"/>
    </play-state>

  </dialog>
  