<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="GlobalVars.cardStatus" value="amount_under_min" type="string"/>
  <session-mapping key="GlobalVars.heardRecentPaymentInfoCare" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.skipBalance" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.authorizationFailureHandling" value="sameAmountDeclined" type="string"/>
  <session-mapping key="GlobalVars.guestPayment" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.fromAnotherPaymentMenu" value="true" type="boolean"/>
  <session-mapping key="suspendedRatePlanChange" value="true" type="boolean"/>
  <session-mapping key="paymentsEntryPoint" value="careESN" type="string"/>
  <session-mapping key="fromAnotherPaymentMenu" value="true" type="string"/>
  <session-mapping key="remainingDays" value="10" type="integer"/>
  <session-mapping key="payDate" value="1" type="string"/>
  <session-mapping key="GlobalVars.paymentsEntryPoint" value="careESN" type="string"/>
  <session-mapping key="GlobalVars.payingWithPrepaid" value="true" type="string"/>
  <session-mapping key="GlobalVars.offerPrepaid" value="true" type="boolean"/>
  <session-mapping key="extension" value="lastresult" type="string"/>
  <session-mapping key="GlobalVars.repeatPA1005" value="true" type="boolean"/>
  <session-mapping key="authorizationFailureHandling" value="sameAmountDeclined" type="string"/>
  <session-mapping key="offerPrepaid" value="true" type="boolean"/>
  <session-mapping key="switch_account" value="lastresult" type="string"/>
  <session-mapping key="prepaid" value="lastresult" type="string"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="suspended" type="string"/>
  <session-mapping key="GlobalVars.serviceDialed" value="99" type="string"/>
  <session-mapping key="extensionAllowed" value="false" type="boolean"/>
  <session-mapping key="card" value="lastresult" type="string"/>
  <session-mapping key="change" value="lastresult" type="string"/>
</session-mappings>
