use these instruction while conversion 

prefix = topic

1)  if there is any "session-mapping" tag within input, convert it into below format. 
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.hangupRequired
      value: true
2) if there is special character # while session-mapping . put it under quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: '#'
2) replace "SetVariable" id with "setVariable_REPLACE_THIS" .
3) if next value or name value is 'transfer' then generate in below format.
- kind: TransferConversationV2
  id: event
  transferType:
    kind: TransferToAgent
    context:
      kind: AutomaticTransferContext

replace id with "custom-state" id.

4) if next value or name value is 'Hangup' or 'exit' then generate in below format.
    - kind: EndConversation
      id: disconnect

replace id with "custom-state" id.

5) if there is "action" tag then add below yaml into in.
    - kind: BeginDialog
      id: begin_REPLACE_THIS
      dialog: topic.main

"dialog" should be next value with prefix.
Replace "BeginDialog" id with "begin_REPLACE_THIS".

6) do not create yaml for other things like script, if/else and so on. 
7) indentation for next yaml should be same as first one or as memorized yaml
8) do not provide duplicate yaml.. generate only for provided inputs.

### Example 1:
**Input XML:**
```xml
<custom-state id="start">
<session-mapping key="ivr_open_services" path="IVR_OpenServices" scope="request"/>
<session-mapping key="ur_clir" path="UR_CLIR" scope="request"/>
<session-mapping key="ur_call_source" path="UR_CALL_SOURCE" scope="request"/>
<session-mapping key="ivr_cli" path="IVR_CLI" scope="request"/>
<action next="main"/>
</custom-state>
```

**Output yaml:**
```yaml
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.ivr_open_services
      value: IVR_OpenServices
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.ur_clir
      value: UR_CLIR
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.ur_call_source
      value: UR_CALL_SOURCE
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.ivr_cli
      value: IVR_CLI
    - kind: BeginDialog
      id: begin_REPLACE_THIS
      dialog: topic.main
```

### Example 2:
**Input XML:**
```xml
<custom-state id="event" className="com.nuance.framework.service.stateengine.state.GlobalEventHandlerState">
<event name="event" next="transfer"/>
</custom-state>
```
**Output yaml:**
```yaml
- kind: TransferConversationV2
  id: event
  transferType:
    kind: TransferToAgent
    context:
      kind: AutomaticTransferContext
```

### Example 3:
**Input XML:**
```xml
<custom-state id="disconnect" className="com.nuance.framework.service.stateengine.state.GlobalEventHandlerState">
<event name="connection.disconnect.transfer" next="exit"/>
</custom-state>
```
**Output yaml:**
```yaml
    - kind: EndConversation
      id: disconnect
```

### Example 4:
**Input XML:**
```xml
  <custom-state id="transfer">
		<view name="/Transfer.dvxml"/>
	</custom-state>
```

**Output yaml:**
```yaml
- kind: TransferConversationV2
  id: transfer
  transferType:
    kind: TransferToAgent
    context:
      kind: AutomaticTransferContext
```
