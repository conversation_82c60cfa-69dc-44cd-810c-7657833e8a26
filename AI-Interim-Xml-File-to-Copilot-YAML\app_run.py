from flask import Flask, request, jsonify, send_file
import threading
import os
from step0_input_cleanup import process_xml
import subprocess
import smtplib
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
from email.mime.text import MIMEText
import time
import run_all_in_one  

global data_access_url
data_access_url = None

app = Flask(__name__)

# Dictionary to track task statuses and results
tasks = {}
OUTPUT_DIR = "output/bot_yaml"
OUTPUT_FILE = os.path.join(OUTPUT_DIR, "final.yml")

def process_in_background(task_id, xml_data):
    """Background function to process XML and execute scripts."""
    try:
        # Save XML to a temporary file
        input_file = "temp_input.xml"
        run_all_in_one.local_run = False
        with open(input_file, "wb") as f:
            f.write(xml_data)

        # Ensure output directory exists
        os.makedirs(OUTPUT_DIR, exist_ok=True)

        # Step 0: Process the XML input
        if os.path.exists('agent.log'):
            os.remove('agent.log')
        process_xml(input_file)

        # List of scripts to execute
        scripts = [
            "step1_separate_dialogs.py",
            "step2_generate_topic_template.py",
            "step3_variable_extraction.py",
            "step4_callAgentDecision.py",
            "step5_generate_bot_yaml.py",
            "step6_post_processing.py"
        ]

        # Run each script sequentially
        for script in scripts:
            if os.path.exists(script):
                result = subprocess.run(["python", script], capture_output=True, text=True)
                if result.returncode != 0:
                    tasks[task_id] = {"status": "failed", "error": f"Error in {script}: {result.stderr}"}
                    return

        # Check if YAML is created
        if os.path.exists(OUTPUT_FILE):
            tasks[task_id]["status"] = "completed"
            tasks[task_id]["output_file"] = OUTPUT_FILE
        else:
            tasks[task_id]["status"] = "failed"
            tasks[task_id]["error"] = "Final YAML file not generated."

    except Exception as e:
        tasks[task_id] = {"status": "failed", "error": str(e)}


@app.route('/process-xml', methods=['POST'])
def process_xml_endpoint():

    # Generate a unique task ID
    task_id = str(len(tasks) + 1)
    tasks[task_id] = {"status": "pending"}
    #print(f"Task {task_id} created")  # Debugging line

    # Get XML data from the request
    xml_data = request.data

    # Get email address from the headers
    email = request.headers.get('Email')
    data_access_url = request.headers.get('DATA_ACCESS_URL')
    #print(f"EmailID : {email}")  # Debugging line
    if not email:
        return jsonify({"status": "error", "message": "Email header is missing"}), 400

    # Start a background thread for processing
    thread = threading.Thread(target=process_in_background, args=(task_id, xml_data))
    thread.start()

            # Start the scheduler in a background thread
    scheduler_thread = threading.Thread(target=task_scheduler, args=(email, OUTPUT_FILE))
    scheduler_thread.start()
    # Return task ID and email to the client
    return jsonify({"task_id": task_id, "email": email}), 202

#Send an email with the final YAML file attached.
def send_email(email, file_path):
    try:
        msg = MIMEMultipart()
        msg['From'] = '<EMAIL>'
        msg['To'] = email
        msg['Subject'] = 'Your Processed YAML File'

        body = 'Please find the attached YAML file.'
        msg.attach(MIMEText(body, 'plain'))

        attachment = open(file_path, "rb")
        part = MIMEBase('application', 'octet-stream')
        part.set_payload((attachment).read())
        encoders.encode_base64(part)
        part.add_header('Content-Disposition', "attachment; filename= final.yml")
        msg.attach(part)

        # Connect to the Outlook SMTP server
        server = smtplib.SMTP('smtp.office365.com', 587)
        server.starttls()
        server.login('<EMAIL>', os.getenv('EMAIL_PASSWORD'))
        text = msg.as_string()
        server.sendmail(msg['From'], msg['To'], text)
        server.quit()
    except Exception as e:
        print(f"Failed to send email: {str(e)}")

## Scheduler to check task status every 10 minutes and send email if completed.
def task_scheduler(email, output_file):
    while True:
        all_tasks_completed = True
        for task_id, task in tasks.items():
            print(f"Checking task {task_id} status: {task['status']}")  # Debugging line
            if task["status"] == "completed" and "email_sent" not in task:
                print(f"Sending email for task {task_id}")  # Debugging line
                #send_email(email, output_file)
                task["email_sent"] = True
            if task["status"] != "completed":
                all_tasks_completed = False
        if all_tasks_completed:
            print("All tasks completed. Stopping scheduler.")  # Debugging line
            break
        time.sleep(60)  # Wait for 1 minute



@app.route('/task/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """Check the status of a task."""
    task_id = str(task_id)  # Ensure task_id is a string
    print(f'TaskId : {task_id}')
    if task_id not in tasks:
        return jsonify({"status": "error", "message": "Invalid task ID"}), 404

    task = tasks[task_id]
    if task["status"] == "completed":
        return jsonify({"status": "completed", "output_file": task["output_file"]})
    elif task["status"] == "failed":
        return jsonify({"status": "failed", "error": task["error"]})
    else:
        return jsonify({"status": "pending"})

@app.route('/download/<task_id>', methods=['GET'])
def download_file(task_id):
    """Download the output file if task is completed."""
    if task_id not in tasks or tasks[task_id]["status"] != "completed":
        return jsonify({"status": "error", "message": "Task not completed or invalid task ID"}), 404

    #output_file = tasks[task_id]["output_file"]
    if os.path.exists(OUTPUT_FILE):
        return send_file(OUTPUT_FILE, as_attachment=True, mimetype="application/x-yaml")
    else:
        return jsonify({"status": "error", "message": "File not found"}), 404


@app.route('/failed_files', methods=['GET'])
def get_failed_files():
    """Show the contents of failed_files.txt."""
    failed_files_path = 'failed_files.txt'
    if os.path.exists(failed_files_path):
        with open(failed_files_path, 'r') as file:
            content = file.read()
        return jsonify({"status": "success", "content": content})
    else:
        return jsonify({"status": "error", "message": "File not found"}), 404


if __name__ == '__main__':
    app.run(debug=True)
