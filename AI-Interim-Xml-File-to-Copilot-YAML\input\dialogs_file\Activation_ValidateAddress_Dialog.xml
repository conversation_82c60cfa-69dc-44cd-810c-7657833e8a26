<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Activation_ValidateAddress_Dialog">
    <data-access-state id="AC1420_ValidateAddress_DB_DA">
      <session-mapping key="streetAddress" value="GlobalVars.streetAddress" type="String"/>
      <session-mapping key="city" value="GlobalVars.city" type="String"/>
      <session-mapping key="state" value="GlobalVars.state" type="String"/>
      <session-mapping key="zipCode" value="GlobalVars.zipCode" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="marketId" value="GlobalVars.marketId" type="String"/>
      <session-mapping key="streetNumber" value="" type="String"/>
      <session-mapping key="streetName" value="" type="String"/>
      <session-mapping key="GlobalVars.noServiceInZipCount" expr="0"/>
      <session-mapping key="GlobalVars.isValidAddress" value="false" type="Boolean"/>
      <if cond="GlobalVars.isRecording"/>
      <data-access id="ValidateAddress" classname="com.nuance.metro.dataaccess.ValidateAddress">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="streetAddress" mask="true"/>
          <input-variable name="city" mask="true"/>
          <input-variable name="state"/>
          <input-variable name="zipCode" mask="true"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="isValid"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="ValidateAddress.status == 'Success' &amp;&amp; ValidateAddress.isValid">
          <session-mapping key="GlobalVars.isValidAddress" value="true" type="Boolean"/>
          <gotodialog next="Activation_Common#AC1425_PlaySecurityTransition_PP"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
            <action next="getReturnLink()"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <dm-state id="AC1500_CreateSecurityCode_DM" type="DIGT">
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <success>
        <action label="default" next="AC1510_ConfirmSecurityCodeYN_DM">
          <session-mapping key="GlobalVars.securityPinCode" expr="AC1500_CreateSecurityCode_DM.returnvalue"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="AC1500_ini_01">
                  <prompt-segments>
                    <audiofile text="It needs to be 6-to-15-digits long, and something you can easily remember every time you call or log into the app But it can't be a date, like your birthday - those are too easy for other people to guess! So, what's the 6-to-15-digit PIN you want to use? " src="AC1500_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC1500_ini_02">
                    <prompt-segments>
                      <audiofile text=" It needs to be 8-digits long, and something you can easily remember every time you call or log into the app But it can't be a date, like your birthday - those are too easy for other people to guess! So, what's the 6-to-15-digit PIN you want to use? " src="AC1500_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AC1500_CreateSecurityCode_DM_reinvoke"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="AC1500_ree_01">
                  <prompt-segments>
                    <audiofile text="Say or enter the 6-to-15-digit number you'd like to use as your account PIN" src="AC1500_ree_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC1500_ree_02">
                    <prompt-segments>
                      <audiofile text="Say or enter the 8-digit number you'd like to use as your account PIN" src="AC1500_ree_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="AC1500_ni1_01">
                  <prompt-segments>
                    <audiofile text="What's the 6-to-15-digit account PIN you want to create? " src="AC1500_ni1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC1500_ni1_02">
                    <prompt-segments>
                      <audiofile text="What's the 8-digit account PIN you want to create? " src="AC1500_ni1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AC1500_ni2_01">
                <prompt-segments>
                  <audiofile text="Say or enter the account PIN youd like" src="AC1500_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="AC1500_ni3_01">
                  <prompt-segments>
                    <audiofile text="Say or enter the 6-to-15-digit number to use as your account PIN" src="AC1500_ni3_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC1500_ni3_02">
                    <prompt-segments>
                      <audiofile text="Say or enter the 8-digit number to use as your account PIN" src="AC1500_ni3_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="AC1500_nm1_01">
                  <prompt-segments>
                    <audiofile text="What's the 6-to-15-digit account PIN you want to create? " src="AC1500_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC1500_nm1_02">
                    <prompt-segments>
                      <audiofile text="What's the 8-digit account PIN you want to create?  " src="AC1500_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1500_nm2_01">
                <prompt-segments>
                  <audiofile text="Say or enter the account PIN youd like" src="AC1500_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="AC1500_nm3_01">
                  <prompt-segments>
                    <audiofile text="Say or enter a 6-to-15-digit number to use as your account PIN" src="AC1500_nm3_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC1500_nm3_02">
                    <prompt-segments>
                      <audiofile text="Say or enter an 8-digit number to use as your account PIN" src="AC1500_nm3_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="AC1500_ree_01">
                  <prompt-segments>
                    <audiofile text="Say or enter the 6-to-15-digit number you'd like to use as your account PIN" src="AC1500_ree_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC1500_ree_02">
                    <prompt-segments>
                      <audiofile text="Say or enter the 8-digit number you'd like to use as your account PIN" src="AC1500_ree_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AC1500_CreateSecurityCode_DM.grxml" count="1"/>
          <dtmfgrammars filename="AC1500_CreateSecurityCode_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="2000ms" completetimeout="0ms" maxspeechtimeout="15000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms">
        </vxml_properties>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AC1510_ConfirmSecurityCodeYN_DM" type="YSNO">
      <session-mapping key="securityCode" value="GlobalVars.securityPinCode" type="String"/>
      <success>
        <action label="true">
          <audio>
            <prompt id="AC1510_out_01">
              <prompt-segments>
                <audiofile text="Got it" src="AC1510_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <gotodialog next="Activation_Common#AC1515_AcctDetsSMS_SD"/>
        </action>
        <action label="false">
          <if cond="typeof(GlobalVars.getNewSC) == 'undefined'">
            <session-mapping key="GlobalVars.getNewSC" expr="0"/>
          </if>
          <if cond="GlobalVars.getNewSC &lt; 2">
            <audio>
              <prompt id="AC1510_out_02">
                <prompt-segments>
                  <audiofile text="Sorry about that Lets try that again" src="AC1510_out_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <session-mapping key="GlobalVars.getNewSC" expr="GlobalVars.getNewSC + 1"/>
            <action next="AC1500_CreateSecurityCode_DM"/>
            <else>
              <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AC1510_ini_01">
                <prompt-segments>
                  <audiofile text="That was" src="AC1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
                <param name="mask" value="true"/>
              </prompt>
              <prompt id="AC1510_ini_04">
                <prompt-segments>
                  <audiofile text="right?" src="AC1510_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AC1510_ni1_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   The security code you wanted was" src="AC1510_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
                <param name="mask" value="true"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1510_ni1_04">
                <prompt-segments>
                  <audiofile text="right?" src="AC1510_ni1_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AC1510_ni2_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   The security code you wanted was" src="AC1510_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
                <param name="mask" value="true"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1510_ni2_04">
                <prompt-segments>
                  <audiofile text="right?" src="AC1510_ni2_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AC1510_ni3_01">
                <prompt-segments>
                  <audiofile text="If the security code you wanted was" src="AC1510_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
                <param name="mask" value="true"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1510_ni3_04">
                <prompt-segments>
                  <audiofile text=" say  yes  or press one  Otherwise, say  no  or press two" src="AC1510_ni3_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1510_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no  The security code you wanted was" src="AC1510_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
                <param name="mask" value="true"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1510_nm1_03">
                <prompt-segments>
                  <audiofile text="right?" src="AC1510_nm1_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1510_nm2_02">
                <prompt-segments>
                  <audiofile text="Please say  yes  or press one or say  no  or press two  The security code you wanted was" src="AC1510_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
                <param name="mask" value="true"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1510_nm2_05">
                <prompt-segments>
                  <audiofile text="right?" src="AC1510_nm2_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1510_nm3_01">
                <prompt-segments>
                  <audiofile text="If the security code you wanted was" src="AC1510_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
                <param name="mask" value="true"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1510_nm3_04">
                <prompt-segments>
                  <audiofile text=" say  yes  or press one  Otherwise, say  no  or press two" src="AC1510_nm3_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AC1510_ini_01">
                <prompt-segments>
                  <audiofile text="That was" src="AC1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="securityCode">
                <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                <param name="intonation" value="f"/>
                <param name="mask" value="true"/>
              </prompt>
              <prompt id="AC1510_ini_04">
                <prompt-segments>
                  <audiofile text="right?" src="AC1510_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AC1510_ConfirmSecurityCodeYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="AC1510_ConfirmSecurityCodeYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" tts_suppress_logs="suppress">
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms">
        </vxml_properties>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="AC1540_NeedAreaCodeCheck_DS">
      <if cond="typeof(GlobalVars.npi_flag) != 'undefined' &amp;&amp; GlobalVars.npi_flag == true">
        <gotodialog next="Activation_Features#AC1635_PlayInsuranceInfo_PP"/>
        <else>
          <action next="AC1550_LookupNPANXX_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="AC1550_LookupNPANXX_SD">
      <gotodialog next="LookupNPANXX_Main_Dialog"/>
      <action next="AC1550_LookupNPANXX_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AC1550_LookupNPANXX_SD_return_CS">
      <if cond="typeof(GlobalVars.GetNPANXX.availableNpaList) != 'undefined' &amp;&amp; GlobalVars.GetNPANXX.availableNpaList.length &gt; 0">
        <action next="AC1560_CheckAreaCodesAvailable_JDA"/>
        <else>
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail.LookupNPANXX'"/>
          <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </custom-state>

    <decision-state id="AC1560_CheckAreaCodesAvailable_DS">
      <if cond="GlobalVars.GetNPANXX.availableNpaList.length == 1">
        <session-mapping key="GlobalVars.areaCode" expr="GlobalVars.GetNPANXX.availableNpaList[0].npa"/>
        <gotodialog next="Activation_Features#AC1635_PlayInsuranceInfo_PP"/>
        <else>
          <action next="AC1570_SelectAreaCode_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="AC1570_SelectAreaCode_DM" type="CUST">
      <session-mapping key="AllowedAreaCodes" value="GlobalVars.areaCodeList" type="String"/>
      <success>
        <action label="computer">
          <session-mapping key="GlobalVars.areaCode" expr="areaCode"/>
          <gotodialog next="Activation_Features_Dialog"/>
        </action>
        <action label="default">
          <audio>
            <prompt id="AC1570_out_01">
              <prompt-segments>
                <audiofile text="Okay I ll have the rest of your phone number after we get through the payment" src="AC1570_out_01.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.areaCode" expr="AC1570_SelectAreaCode_DM.returnvalue"/>
          <gotodialog next="Activation_Features_Dialog"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AC1570_ini_01">
                <prompt-segments>
                  <audiofile text="Now before you pay, I found a few area codes for you to choose from Tell me the one you want I have" src="AC1570_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="firstNameFileName">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAreaCodes"/>
                <param name="variableName" value="availableNpaList"/>
                <param name="variableScope" value="session"/>
              </prompt>
            </audio>
          </initialprompt>
          <reentryprompt count="1" filename="" text="" id="AC1570_SelectAreaCode_DM_reentry"/>
          <helpprompts count="1" bargein="true" filename="" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AC1570_ree_01">
                <prompt-segments>
                  <audiofile text="Please tell me which area code youd like  Please say or enter " src="AC1570_ree_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="firstNameFileName">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAreaCodes"/>
                <param name="variableName" value="availableNpaList"/>
                <param name="variableScope" value="session"/>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AC1570_ni1_01">
                <prompt-segments>
                  <audiofile text="Go ahead and tell me which area code youd like  Please say or enter " src="AC1570_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="firstNameFileName">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAreaCodes"/>
                <param name="variableName" value="availableNpaList"/>
                <param name="variableScope" value="session"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AC1570_ni2_01">
                <prompt-segments>
                  <audiofile text="Please tell me the area code youd like you can say or enter " src="AC1570_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="firstNameFileName">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAreaCodes"/>
                <param name="variableName" value="availableNpaList"/>
                <param name="variableScope" value="session"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AC1570_ni3_01">
                <prompt-segments>
                  <audiofile text="You can say the area code or enter it on your phones keypad  Please say or enter either " src="AC1570_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="firstNameFileName">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAreaCodes"/>
                <param name="variableName" value="availableNpaList"/>
                <param name="variableScope" value="session"/>
              </prompt>
              <prompt id="AC1570_ni3_06">
                <prompt-segments>
                  <audiofile text="You can also say  its up to you " src="AC1570_ni3_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1570_nm1_01">
                <prompt-segments>
                  <audiofile text="Say" src="AC1570_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="firstNameFileName">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAreaCodes"/>
                <param name="variableName" value="availableNpaList"/>
                <param name="variableScope" value="session"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1570_nm2_01">
                <prompt-segments>
                  <audiofile text="Please tell me the area code youd like you can say or enter  " src="AC1570_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="firstNameFileName">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAreaCodes"/>
                <param name="variableName" value="availableNpaList"/>
                <param name="variableScope" value="session"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC1570_nm3_01">
                <prompt-segments>
                  <audiofile text="You can say the area code or enter it on your phones keypad  Please say or enter either " src="AC1570_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="firstNameFileName">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAreaCodes"/>
                <param name="variableName" value="availableNpaList"/>
                <param name="variableScope" value="session"/>
              </prompt>
              <prompt id="AC1570_nm3_07">
                <prompt-segments>
                  <audiofile text="You can also say  its up to you " src="AC1570_nm3_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AC1570_ree_01">
                <prompt-segments>
                  <audiofile text="Please tell me which area code youd like  Please say or enter " src="AC1570_ree_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="firstNameFileName">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAreaCodes"/>
                <param name="variableName" value="availableNpaList"/>
                <param name="variableScope" value="session"/>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="AC1570_SelectAreaCode_DM.jsp" count="1">
            <param name="AllowedAreaCodes" value="AllowedAreaCodesVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="AC1570_SelectAreaCode_DM_dtmf.jsp" count="1">
            <param name="AllowedAreaCodes" value="AllowedAreaCodesVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param name="mask" value="true"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationIsComputer"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="AC1570_SelectAreaCode_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param name="mask" value="true"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationIsComputer"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param name="mask" value="true"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationIsComputer"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  