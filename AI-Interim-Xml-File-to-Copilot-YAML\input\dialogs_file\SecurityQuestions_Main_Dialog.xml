<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="SecurityQuestions_Main_Dialog">
    <dm-state id="SQ0900_SelectSecurityQuestion_DM" type="CUST">
      <success>
        <action label="school" next="SQ1000_RecordSchoolName_DM">
          <session-mapping key="GlobalVars.securityQuestionCode" expr="'Q11'"/>
        </action>
        <action label="street" next="SQ1030_RecordStreetName_DM">
          <session-mapping key="GlobalVars.securityQuestionCode" expr="'Q12'"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SQ0900_ini_01">
                <prompt-segments>
                  <audiofile text="Now, if you ever forget your security code, we can ask you a security question instead to let you in The two choices are the name of your first elementary school, or the name of the street you grew up on" src="SQ0900_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ0900_ini_02">
                <prompt-segments>
                  <audiofile text="Which do you wanna use? Say school or street" src="SQ0900_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SQ0900_ini_01">
                <prompt-segments>
                  <audiofile text="Now, if you ever forget your security code, we can ask you a security question instead to let you in The two choices are the name of your first elementary school, or the name of the street you grew up on" src="SQ0900_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ0900_ini_02">
                <prompt-segments>
                  <audiofile text="Which do you wanna use? Say school or street" src="SQ0900_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SQ0900_nm1_01">
                <prompt-segments>
                  <audiofile text="Please tell me which security question youd like to set up You can say elementary school or the street I grew up on" src="SQ0900_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SQ0900_nm2_01">
                <prompt-segments>
                  <audiofile text="Please select your security question You can say elementary school or press 1 or the street I grew up on or press 2" src="SQ0900_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SQ0900_nm2_01">
                <prompt-segments>
                  <audiofile text="Please select your security question You can say elementary school or press 1 or the street I grew up on or press 2" src="SQ0900_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ0900_nm1_01">
                <prompt-segments>
                  <audiofile text="Please tell me which security question youd like to set up You can say elementary school or the street I grew up on" src="SQ0900_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ0900_nm2_01">
                <prompt-segments>
                  <audiofile text="Please select your security question You can say elementary school or press 1 or the street I grew up on or press 2" src="SQ0900_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ0900_nm2_01">
                <prompt-segments>
                  <audiofile text="Please select your security question You can say elementary school or press 1 or the street I grew up on or press 2" src="SQ0900_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SQ0900_ini_01">
                <prompt-segments>
                  <audiofile text="Now, if you ever forget your security code, we can ask you a security question instead to let you in The two choices are the name of your first elementary school, or the name of the street you grew up on" src="SQ0900_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ0900_ini_02">
                <prompt-segments>
                  <audiofile text="Which do you wanna use? Say school or street" src="SQ0900_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SQ0900_SelectSecurityQuestion_DM.grxml" count="1">
      </grammars>
          <dtmfgrammars filename="SQ0900_SelectSecurityQuestion_DM_dtmf.grxml" count="1">
      </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01" cond="!(collect.value.dm_command == 'operator')">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02" cond="!(collect.value.dm_command == 'operator')">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03" cond="!(collect.value.dm_command == 'operator')">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07" cond="!(collect.value.dm_command == 'operator')">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08" cond="!(collect.value.dm_command == 'operator')">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="Global_Confirmation_Reentry"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07" cond="!(collect.value.dm_command == 'operator')">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08" cond="!(collect.value.dm_command == 'operator')">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07" cond="!(collect.value.dm_command == 'operator')">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08" cond="!(collect.value.dm_command == 'operator')">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="SQ1000_RecordSchoolName_DM" type="RCRD">
      <success>
        <action label="default">
          <session-mapping key="SQ1000Reentry" value="true" type="Boolean"/>
          <session-mapping key="recordDuration" value="audio$.duration" type="String"/>
          <session-mapping key="recordTermchar" value="audio$.termchar" type="String"/>
          <session-mapping key="recordMaxtime" value="audio$.maxtime" type="String"/>
          <if cond="recordTermchar == '1'">
            <audio>
              <prompt id="SQ1000_out_01">
                <prompt-segments>
                  <audiofile text="Okay, if you want to go back to this previous question, just press 1" src="SQ1000_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="SQ1030_RecordStreetName_DM"/>
          </if>
          <if cond="recordTermchar != null &amp;&amp; recordTermchar != '' &amp;&amp; recordTermchar != '#'">
            <action next="SQ1000_RecordSchoolName_DM"/>
            <else>
              <session-mapping key="GlobalVars.schoolNameFilename" expr="SQ1000_RecordSchoolName_DM.returnvalue"/>
              <action next="SQ1010_SpellSchoolName_DM"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SQ1000_ini_01">
                <prompt-segments>
                  <audiofile text="After the beep, go ahead and tell me the name of the school" src="SQ1000_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="SQ1000_RecordSchoolName_DM_reentry"/>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="SQ1000_RecordSchoolName_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="SQ1000_RecordSchoolName_DM_help"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SQ1000_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry, I didn t hear you Please say the name of your first elementary school, or press 1 to answer another question" src="SQ1000_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SQ1000_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry, I didn t hear you Please say the name of your first elementary school, or press 1 to answer another question" src="SQ1000_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SQ1000_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry, I didn t hear you Please say the name of your first elementary school, or press 1 to answer another question" src="SQ1000_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1000_nm1_01">
                <prompt-segments>
                  <audiofile text="Please SAY the name of your first elementary school, or press 1 to answer another question" src="SQ1000_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1000_nm2_01">
                <prompt-segments>
                  <audiofile text="If you don t want to answer this question, press 1 Otherwise, SAY the name of your first elementary school now" src="SQ1000_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1000_nm3_01">
                <prompt-segments>
                  <audiofile text="If you ever forget your security code, you ll be able to access it by answering this security question If you prefer to answer the other security question, press one After the beep, please say the name of your first elementary school" src="SQ1000_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SQ1000_ini_03">
                <prompt-segments>
                  <audiofile text="After the beep, please tell me the name of your first elementary school" src="SQ1000_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
          <grammars filename="SQ1000_RecordSchoolName_DM.grxml" count="1"/>
          <dtmfgrammars filename="SQ1000_RecordSchoolName_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms">
        </vxml_properties>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="SQ1010_SpellSchoolName_DM" type="RCRD">
      <success>
        <action label="default">
          <session-mapping key="GlobalVars.spellSchoolFilename" expr="SQ1010_SpellSchoolName_DM.returnvalue"/>
          <action next="SQ1020_ConfirmSchoolNameYN_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SQ1010_ini_01">
                <prompt-segments>
                  <audiofile text="Now, after the beep, please spell that  for me" src="SQ1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="" text="" id="SQ1010_SpellSchoolName_DM_help"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SQ1010_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry, I didn t hear you Please spell the name of your first elementary school" src="SQ1010_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SQ1010_ni2_01">
                <prompt-segments>
                  <audiofile text="Sorry, please spell the name of your first elementary school" src="SQ1010_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SQ1010_ni3_01">
                <prompt-segments>
                  <audiofile text="Sorry, please spell the name of your first elementary school" src="SQ1010_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1010_nm1_01">
                <prompt-segments>
                  <audiofile text="Please SPELL the name of your first elementary school out loud, like T O M" src="SQ1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1010_nm2_01">
                <prompt-segments>
                  <audiofile text="Sorry, I need you to say the letters out loud Please spell the name of your first elementary school now" src="SQ1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1010_nm3_01">
                <prompt-segments>
                  <audiofile text="Sorry, I need you to say the letters out loud Please spell the name of your first elementary school now" src="SQ1010_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SQ1010_ini_01">
                <prompt-segments>
                  <audiofile text="Now, after the beep, please spell that  for me" src="SQ1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
    </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms">
        </vxml_properties>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="SQ1020_ConfirmSchoolNameYN_DM" type="YSNO">
      <session-mapping key="schoolNameFilename" value="'' + GlobalVars.schoolNameFilename" type="String"/>
      <session-mapping key="spellSchoolFilename" value="'' + GlobalVars.spellSchoolFilename" type="String"/>
      <success>
        <action label="true" next="SQ1025_SubmitSchoolNameToTranscription_DB_DA">
          <session-mapping key="GlobalVars.securityQuestion" expr="'school'"/>
          <audio>
            <prompt id="SQ1020_out_01">
              <prompt-segments>
                <audiofile text="Thanks, someone will listen to that too and update your account" src="SQ1020_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="false">
          <if cond="securityQuestionFNameAttempts &lt; 1">
            <audio>
              <prompt id="SQ1020_out_02">
                <prompt-segments>
                  <audiofile text="My mistake  Let s try one more time" src="SQ1020_out_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <session-mapping key="securityQuestionFNameAttempts" expr="securityQuestionFNameAttempts + 1"/>
            <action next="SQ1000_RecordSchoolName_DM"/>
            <else>
              <audio>
                <prompt id="SQ1020_out_03">
                  <prompt-segments>
                    <audiofile text="My mistake again" src="SQ1020_out_03.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SQ1020_ini_01">
                <prompt-segments>
                  <audiofile text="I recorded" src="SQ1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="schoolNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellSchoolFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_ini_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1020_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SQ1020_ini_01">
                <prompt-segments>
                  <audiofile text="I recorded" src="SQ1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="schoolNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellSchoolFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_ini_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1020_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SQ1020_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   I recorded" src="SQ1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="schoolNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellSchoolFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_nm1_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1020_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SQ1020_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or press one or  no  or press two  I recorded" src="SQ1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="schoolNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellSchoolFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_nm2_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1020_nm2_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SQ1020_nm3_01">
                <prompt-segments>
                  <audiofile text="If your school name is" src="SQ1020_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="schoolNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellSchoolFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_nm3_05">
                <prompt-segments>
                  <audiofile text="Say  yes  or press one To re-record, say  no  or press two" src="SQ1020_nm3_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   I recorded" src="SQ1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="schoolNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellSchoolFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_nm1_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1020_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or press one or  no  or press two  I recorded" src="SQ1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="schoolNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellSchoolFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_nm2_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1020_nm2_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_nm3_01">
                <prompt-segments>
                  <audiofile text="If your school name is" src="SQ1020_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="schoolNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellSchoolFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_nm3_05">
                <prompt-segments>
                  <audiofile text="Say  yes  or press one To re-record, say  no  or press two" src="SQ1020_nm3_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SQ1020_ini_01">
                <prompt-segments>
                  <audiofile text="I recorded" src="SQ1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="schoolNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellSchoolFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1020_ini_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1020_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SQ1020_ConfirmSchoolNameYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="SQ1020_ConfirmSchoolNameYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms">
        </vxml_properties>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="SQ1025_SubmitSchoolNameToTranscription_DB_DA">
      <session-mapping key="utterance" value="''+GlobalVars.schoolNameFilename" type="String"/>
      <session-mapping key="spelledUtterance" value="''+GlobalVars.spellSchoolFilename" type="String"/>
      <session-mapping key="slot" value="secSchool" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="SubmitUtteranceToTranscription" classname="com.nuance.metro.dataaccess.SubmitUtteranceToTranscription">
        <inputs>
          <input-variable name="utterance"/>
          <input-variable name="spelledUtterance"/>
          <input-variable name="slot"/>
          <input-variable name="sessionId"/>
          <input-variable name="language"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.SecQuestSuccess" value="true" type="Boolean"/>
        <if cond="SubmitUtteranceToTranscription.status == 'Success'">
          <session-mapping key="GlobalVars.SubmitSchoolNameToTranscription" expr="SubmitUtteranceToTranscription"/>
          <action next="getReturnLink()"/>
          <else>
            <action next="getReturnLink()"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <dm-state id="SQ1030_RecordStreetName_DM" type="RCRD">
      <session-mapping key="otherOptionSelected" value="false" type="Boolean"/>
      <success>
        <action label="default">
          <session-mapping key="recordDuration" value="audio$.duration" type="String"/>
          <session-mapping key="recordTermchar" value="audio$.termchar" type="String"/>
          <session-mapping key="recordMaxtime" value="audio$.maxtime" type="String"/>
          <if cond="recordTermchar == '1'">
            <audio>
              <prompt id="SQ1030_out_01">
                <prompt-segments>
                  <audiofile text="Ok" src="SQ1030_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="SQ1000_RecordSchoolName_DM"/>
          </if>
          <if cond="recordTermchar != null &amp;&amp; recordTermchar != '' &amp;&amp; recordTermchar != '#'">
            <action next="SQ1030_RecordStreetName_DM"/>
            <else>
              <session-mapping key="GlobalVars.streetNameFilename" expr="SQ1030_RecordStreetName_DM.returnvalue"/>
              <action next="SQ1040_SpellStreetName_DM"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SQ1030_ini_01">
                <prompt-segments>
                  <audiofile text="After the beep, go ahead and tell me the name of the street" src="SQ1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="SQ1030_RecordStreetName_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="SQ1030_RecordStreetName_DM_help"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SQ1030_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry, I didn t hear you Please say the name of the street you grew up on If you prefer to answer the elementary school question, press 1" src="SQ1030_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SQ1030_ni2_01">
                <prompt-segments>
                  <audiofile text="Sorry, please say the name of the street you grew up on To answer the elementary school question press 1" src="SQ1030_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SQ1030_ni3_01">
                <prompt-segments>
                  <audiofile text="Sorry, please say the name of the street you grew up on To answer the elementary school question press 1" src="SQ1030_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1030_nm1_01">
                <prompt-segments>
                  <audiofile text="Please SAY the name of the street you grew up on, or press 1 to go back to the elementary school question" src="SQ1030_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you don t want to answer this question, press 1 to go back to the elementary school question Otherwise, SAY the name of the street you grew up on now" src="SQ1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1030_nm3_01">
                <prompt-segments>
                  <audiofile text="If you ever forget your security code, you ll be able to access it by answering this security question If you prefer to answer the previous security question, press 1 After the beep, please say the name the street you grew up on" src="SQ1030_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SQ1030_ini_01">
                <prompt-segments>
                  <audiofile text="After the beep, go ahead and tell me the name of the street" src="SQ1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
          <grammars filename="SQ1030_RecordStreetName_DM.grxml" count="1"/>
          <dtmfgrammars filename="SQ1030_RecordStreetName_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="SQ1040_SpellStreetName_DM" type="RCRD">
      <success>
        <action label="default" next="SQ1050_ConfirmStreetNameYN_DM">
          <session-mapping key="GlobalVars.spellStreetFilename" expr="SQ1040_SpellStreetName_DM.returnvalue"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SQ1040_ini_01">
                <prompt-segments>
                  <audiofile text="Now, after the beep, please spell that  for me" src="SQ1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="" text="" id="SQ1040_SpellStreetName_DM_help"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SQ1040_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry, I didn t hear you Please spell the name of the street you grew up on" src="SQ1040_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SQ1040_ni2_01">
                <prompt-segments>
                  <audiofile text="Sorry, please spell the name of the street you grew up on" src="SQ1040_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SQ1040_ni3_01">
                <prompt-segments>
                  <audiofile text="Sorry, please spell the name of the street you grew up on" src="SQ1040_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1040_nm1_01">
                <prompt-segments>
                  <audiofile text="Please SPELL the name of the street out loud, like T O M" src="SQ1040_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1040_nm2_01">
                <prompt-segments>
                  <audiofile text="Sorry, I need you to say the letters out loud Please spell the name of the street you grew up on now" src="SQ1040_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1040_nm3_01">
                <prompt-segments>
                  <audiofile text="Sorry, I need you to say the letters out loud Please spell the name of the street you grew up on now" src="SQ1040_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SQ1040_ini_01">
                <prompt-segments>
                  <audiofile text="Now, after the beep, please spell that  for me" src="SQ1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
    </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="SQ1050_ConfirmStreetNameYN_DM" type="YSNO">
      <session-mapping key="streetNameFilename" value="'' + GlobalVars.streetNameFilename" type="String"/>
      <session-mapping key="spellStreetFilename" value="'' + GlobalVars.spellStreetFilename" type="String"/>
      <success>
        <action label="true" next="SQ1055_SubmitStreetNameToTranscription_DB_DA">
          <session-mapping key="GlobalVars.securityQuestion" expr="'street'"/>
          <audio>
            <prompt id="SQ1050_out_01">
              <prompt-segments>
                <audiofile text="Thanks, someone will listen to that too and update your account" src="SQ1050_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="false">
          <if cond="securityQuestionStNameAttempts &lt; 1">
            <audio>
              <prompt id="SQ1050_out_02">
                <prompt-segments>
                  <audiofile text="My mistake  Let s try one more time" src="SQ1050_out_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <session-mapping key="securityQuestionStNameAttempts" expr="securityQuestionStNameAttempts + 1"/>
            <action next="SQ1030_RecordStreetName_DM"/>
            <else>
              <audio>
                <prompt id="SQ1050_out_03">
                  <prompt-segments>
                    <audiofile text="My mistake again" src="SQ1050_out_03.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SQ1050_ini_01">
                <prompt-segments>
                  <audiofile text="I recorded" src="SQ1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="streetNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellStreetFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_ini_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1050_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SQ1050_ini_01">
                <prompt-segments>
                  <audiofile text="I recorded" src="SQ1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="streetNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellStreetFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_ini_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1050_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SQ1050_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   I recorded" src="SQ1050_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="streetNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellStreetFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_nm1_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1050_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SQ1050_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or press one or  no  or press two  I recorded" src="SQ1050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="streetNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellStreetFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_nm2_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1050_nm2_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SQ1050_nm3_01">
                <prompt-segments>
                  <audiofile text="If the street name is" src="SQ1050_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="streetNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellStreetFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_nm3_05">
                <prompt-segments>
                  <audiofile text="Say  yes  or press one To re-record, say  no  or press two" src="SQ1050_nm3_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   I recorded" src="SQ1050_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="streetNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellStreetFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_nm1_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1050_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or press one or  no  or press two  I recorded" src="SQ1050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="streetNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellStreetFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_nm2_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1050_nm2_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_nm3_01">
                <prompt-segments>
                  <audiofile text="If the street name is" src="SQ1050_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="streetNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellStreetFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_nm3_05">
                <prompt-segments>
                  <audiofile text="Say  yes  or press one To re-record, say  no  or press two" src="SQ1050_nm3_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SQ1050_ini_01">
                <prompt-segments>
                  <audiofile text="I recorded" src="SQ1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="streetNameFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="spellStreetFilename">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptFullPathRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SQ1050_ini_05">
                <prompt-segments>
                  <audiofile text="Is that right?" src="SQ1050_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SQ1050_ConfirmStreetNameYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="SQ1050_ConfirmStreetNameYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms">
        </vxml_properties>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="SQ1055_SubmitStreetNameToTranscription_DB_DA">
      <session-mapping key="utterance" value="''+GlobalVars.streetNameFilename" type="String"/>
      <session-mapping key="spelledUtterance" value="''+GlobalVars.spellStreetFilename" type="String"/>
      <session-mapping key="slot" value="secStreet" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="SubmitUtteranceToTranscription" classname="com.nuance.metro.dataaccess.SubmitUtteranceToTranscription">
        <inputs>
          <input-variable name="utterance"/>
          <input-variable name="spelledUtterance"/>
          <input-variable name="slot"/>
          <input-variable name="sessionId"/>
          <input-variable name="language"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.SecQuestSuccess" value="true" type="Boolean"/>
        <if cond="SubmitUtteranceToTranscription.status == 'Success'">
          <session-mapping key="GlobalVars.SubmitStreetNameToTranscription" expr="SubmitUtteranceToTranscription"/>
          <action next="getReturnLink()"/>
          <else>
            <action next="getReturnLink()"/>
          </else>
        </if>
      </action>
    </data-access-state>

  </dialog>
  