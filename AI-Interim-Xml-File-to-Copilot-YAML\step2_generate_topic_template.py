import xml.etree.ElementTree as ET
import yaml
import os
import logging
from util import read_filenames_in_folders, delete_existing_files, generate_unique_value

logging.basicConfig(filename='agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')

## this method is creating topic level template
def create_topic_template(xml_path, yml_path, output_yml_path):

    # Step 1: Parse XML to extract the id value from the dialog tag
    logging.debug("step2_generate_topic_template: create_topic_template")
    tree = ET.parse(xml_path)
    root = tree.getroot()
    dialog_id = root.attrib['id']

    logging.debug(f"dialog_id: {dialog_id}")

    # Step 2: Read and modify the YAML file
    with open(yml_path, 'r') as file:
        yml_data = yaml.safe_load(file)
        updated_data = replace_keys(yml_data,dialog_id)
        #remove_last_null(updated_data)  # Remove null values

        with open(output_yml_path, 'w') as file:
         yaml.dump(updated_data, file,sort_keys=False)


def replace_keys(yml_data,dialog_id):
    if isinstance(yml_data, list):
        return [replace_keys(item,dialog_id) for item in yml_data]
    elif isinstance(yml_data, dict):
        for key, value in yml_data.items():
           if key == 'displayName':
                yml_data[key] = dialog_id
           elif key == 'schemaName':
                # Extract the last part after the last dot and replace it with 'UsageInfo_SD'
                parts = value.split('.')
                parts[-1] = dialog_id
                yml_data[key] = '.'.join(parts)
           else:
                yml_data[key] = replace_keys(value,dialog_id)
        return yml_data
    else:
        return yml_data


# Usage
dialog_xml_path = 'input/dialogs_file/'
custom_xml_path = 'input/custom_file/'
dvxml_path = 'input/dvxml_file/'
yml_path = 'YAML-template/Topic_template_v1.yml'
output_yml_path = 'YAML-template/topic-yaml/'
bot_yaml_path = 'YAML-template/botContentIVR.yml'

delete_existing_files(output_yml_path)

files = [os.path.splitext(f)[0] for f in read_filenames_in_folders(dialog_xml_path,custom_xml_path,dvxml_path)]
first_SD = files[0]
for file in files:
 if os.path.exists(os.path.join(dialog_xml_path, file + '.xml')):
    create_topic_template(dialog_xml_path+file+'.xml', yml_path, output_yml_path+file+'.yml')
 elif os.path.exists(os.path.join(custom_xml_path, file + '.xml')):
    create_topic_template(custom_xml_path+file+'.xml', yml_path, output_yml_path+file+'.yml')
 else:
    create_topic_template(dvxml_path+file+'.xml', yml_path, output_yml_path+file+'.yml')

 file_path = output_yml_path + file + '.yml'
 with open(file_path, 'r') as yml_file:
    yml_content = yml_file.read()

 # Replace 'actions' key with an empty string
 updated_content = yml_content.replace('actions: _REPLACE_THIS', 'actions: ')

 with open(file_path, 'w') as yml_file:
    yml_file.write(updated_content)


# # Replace starting point of Conversation
# with open(bot_yaml_template, 'r') as yml_file:
#     bot_yml_content = yml_file.read() 

# bot_updated_content = bot_yml_content.replace('dialog: topic.abc', 'dialog: topic.All_Variable')

# with open(bot_yaml_path, 'w') as yml_file:
#  yml_file.write(bot_updated_content)