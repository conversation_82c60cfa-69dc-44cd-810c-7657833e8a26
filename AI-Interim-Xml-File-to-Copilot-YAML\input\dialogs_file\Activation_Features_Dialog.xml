<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Activation_Features_Dialog">
    <subdialog-state id="AC1640_GetPayment_SD">
      <gotodialog next="PaymentIntroMethod_Main_Dialog"/>
      <action next="AC1640_GetPayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AC1640_GetPayment_SD_return_CS">
      <if cond="GlobalVars.activationResult == 'goodbye' || GlobalVars.activationResult == 'transfer'">
        <action next="getReturnLink()"/>
        <else>
          <action next="AC1700_PleaseWait_PP"/>
        </else>
      </if>
    </custom-state>

    <play-state id="AC1635_PlayInsuranceInfo_PP">
      <session-mapping key="pricePlanCode" value="GlobalVars.selectedPlan" type="String"/>
      <session-mapping key="insuranceCompatibility" value="false" type="Boolean"/>
      <session-mapping key="audioMessageKey" value="metro_insurance_product_message" type="String"/>
      <audio>
        <prompt id="AC1635_out_02">
          <prompt-segments>
            <audiofile text="Before we wrap up" src="AC1635_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="audioMessageKey">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayAudioMessage"/>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.isOrderApproved" value="true" type="Boolean"/>
      <action next="AC1640_GetPayment_SD"/>
    </play-state>

    <play-state id="AC1700_PleaseWait_PP">
      <session-mapping key="totalAmount" value="GlobalVars.paymentAmount" type="String"/>
      <session-mapping key="activationEntryPoint" value="GlobalVars.activationEntryPoint" type="String"/>
      <session-mapping key="activationType" value="GlobalVars.activationType" type="String"/>
      <audio>
        <prompt id="AC1700_out_03">
          <prompt-segments>
            <audiofile text="Hang tight while I take care of your payment Here s a few tips while you wait" src="AC1700_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="activationEntryPoint == '228'">
          <prompt id="AC1700_out_01">
            <prompt-segments>
              <audiofile text="When your phone s activated, this call could drop and you ll need to turn your phone off and back on After that, it should be less than 5 minutes before it s working normally, but it could be up to 2 hoursWhen it s working again, you can get your new phone number by dialing #-6-8-6-# Hit send as if you were making a call, and your phone number will appear on your screen Again, that s #-6-8-6-#" src="AC1700_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="silence_1000ms">
            <prompt-segments>
              <audiofile text="test" src="silence_1000ms.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="AC1700_out_05">
            <prompt-segments>
              <audiofile text="If after 2 hours your device isn t working normally, please call 888 8METRO8 from a different phone and ask for troubleshooting Hang tight" src="AC1700_out_05.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="AC1700_out_07">
              <prompt-segments>
                <audiofile text="When your phone s activated, we recommend you turn it off and back on After that, it should be less than 5 minutes before it s working normally, but it COULD be up to 2 hours  If it s STILL not working after 2 hours, call 888-8METRO8 from a DIFFERENT phone and ask for troubleshooting" src="AC1700_out_07.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="AC1700_out_04" cond="activationType == 'eSIM'">
              <prompt-segments>
                <audiofile text="Just a reminder, you still need go to metro website to complete the remaining activation steps at metro by t mobile dot com Make sure to have WiFi" src="AC1700_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AC1705_ActivateSubscriberOnNewAccount_DB_DA"/>
    </play-state>

    <data-access-state id="AC1705_ActivateSubscriberOnNewAccount_DB_DA">
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="streetAddress" value="GlobalVars.streetAddress" type="String"/>
      <session-mapping key="city" value="GlobalVars.city" type="String"/>
      <session-mapping key="stateCode" value="GlobalVars.state" type="String"/>
      <session-mapping key="zipCode" value="GlobalVars.zipCode" type="String"/>
      <session-mapping key="securityPinCode" value="GlobalVars.securityPinCode" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="areaCode" value="GlobalVars.areaCode" type="String"/>
      <session-mapping key="offerCode" value="" type="String"/>
      <session-mapping key="pricePlanCode" value="GlobalVars.selectedPlan" type="String"/>
      <session-mapping key="priceSheetCode" value="" type="String"/>
      <session-mapping key="sim" value="GlobalVars.activationType == 'eSIM' ? GlobalVars.GetDeviceStatus.eid : GlobalVars.iccidSerialNumber" type="String"/>
      <session-mapping key="isAddressApproved" value="GlobalVars.isValidAddress?true:false" type="String"/>
      <if cond="GlobalVars.npi_flag == true">
        <session-mapping key="areaCode" expr="''"/>
      </if>
      <data-access id="ActivateSubscriberOnNewAccount_AC1705" classname="com.nuance.metro.dataaccess.ActivateSubscriberOnNewAccount">
        <fetchAudio>AC1705_out_01</fetchAudio>
        <inputs>
          <input-variable name="streetAddress" mask="true"/>
          <input-variable name="addressType" mask="true"/>
          <input-variable name="city" mask="true"/>
          <input-variable name="stateCode" mask="true"/>
          <input-variable name="zipCode" mask="true"/>
          <input-variable name="securityPinCode" mask="true"/>
          <input-variable name="isAddressApproved"/>
          <input-variable name="isOrderApproved"/>
          <input-variable name="imei"/>
          <input-variable name="portInMdn"/>
          <input-variable name="portInproviderId"/>
          <input-variable name="offerCode"/>
          <input-variable name="pricePlanCode"/>
          <input-variable name="priceSheetCode"/>
          <input-variable name="sim"/>
          <input-variable name="iccid"/>
          <input-variable name="submarket"/>
          <input-variable name="areaCode"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="createdAccountNumber" mask="true"/>
          <output-variable name="totalAmount"/>
          <output-variable name="mdn"/>
          <output-variable name="isInvalidAddress"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="ActivateSubscriberOnNewAccount_AC1705.status == 'Success'">
          <session-mapping key="GlobalVars.ActivateSubscriberOnNewAccount" expr="ActivateSubscriberOnNewAccount_AC1705"/>
          <session-mapping key="GlobalVars.activationStatus" expr="ActivateSubscriberOnNewAccount_AC1705.status"/>
          <session-mapping key="GlobalVars.mdn" expr="ActivateSubscriberOnNewAccount_AC1705.mdn"/>
          <session-mapping key="GlobalVars.banId" expr="ActivateSubscriberOnNewAccount_AC1705.createdAccountNumber"/>
          <session-mapping key="GlobalVars.dueImmediately" expr="ActivateSubscriberOnNewAccount_AC1705.totalAmount"/>
          <session-mapping key="ActivationTable.ACCOUNT_NUM" expr="ActivateSubscriberOnNewAccount_AC1705.createdAccountNumber"/>
          <session-mapping key="ActivationTable.MDN" expr="ActivateSubscriberOnNewAccount_AC1705.mdn"/>
          <session-mapping key="ActivationTable.BILLABLE_DATE" expr="GlobalVars.dueDate"/>
          <session-mapping key="GlobalVars.amountDue" expr="ActivateSubscriberOnNewAccount_AC1705.totalAmount"/>
          <action next="AC1706_SendPMTEvent_DB_DA"/>
          <else>
            <action next="AC1710_CheckProcessingResults_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <data-access-state id="AC1706_SendPMTEvent_DB_DA">
      <session-mapping key="monitoringFullString" value="" type="String"/>
      <session-mapping key="paymentVars.eventTypeGMT" expr="getEventTime()"/>
      <session-mapping key="paymentVars.eventType" expr="'Payment'"/>
      <session-mapping key="paymentVars.status" expr="'start'"/>
      <data-access id="SendFrontendEvent" classname="com.nuance.metro.dataaccess.SendFrontendEvents">
        <inputs>
          <input-variable name="monitoringFullString"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="message"/>
        </outputs>
      </data-access>
      <action label="default">
        <action next="AC1709_CheckPrepaidPINs_JDA"/>
      </action>
    </data-access-state>

    <data-access-state id="AC1707_AuthorizeByAnonymous_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="waiveConvFee" value="false" type="Boolean"/>
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount" type="String"/>
      <session-mapping key="targetMdn" value="NumberPortInVars.portingNumber" type="String"/>
      <session-mapping key="preferredPaymentMethod" value="GlobalVars.preferredPaymentMethod" type="String"/>
      <session-mapping key="cardNumber" value="" type="String"/>
      <session-mapping key="expirationDate" value="" type="String"/>
      <session-mapping key="billingZip" value="" type="String"/>
      <session-mapping key="creditCardSecurityCode" value="" type="String"/>
      <session-mapping key="selectedPlanPrice" value="GlobalVars.selectedPlanPrice" type="String"/>
      <session-mapping key="amountDue" value="GlobalVars.amountDue" type="String"/>
      <session-mapping key="cardNumber" expr="GlobalVars.bankCardNumber"/>
      <session-mapping key="expirationDate" expr="GlobalVars.bankCardDate"/>
      <session-mapping key="billingZip" expr="GlobalVars.bankCardZip"/>
      <session-mapping key="creditCardSecurityCode" expr="GlobalVars.bankCardCVV"/>
      <data-access id="AuthorizeByAnonymous_AC1707" classname="com.nuance.metro.dataaccess.AuthorizeByAnonymous_AC1707">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
          <input-variable name="waiveConvFee"/>
          <input-variable name="paymentAmount"/>
          <input-variable name="targetMdn"/>
          <input-variable name="preferredPaymentMethod"/>
          <input-variable name="cardNumber" mask="true"/>
          <input-variable name="expirationDate" mask="true"/>
          <input-variable name="billingZip" mask="true"/>
          <input-variable name="creditCardSecurityCode" mask="true"/>
          <input-variable name="amountDue"/>
        </inputs>
        <outputs>
          <output-variable name="isAuthorized"/>
          <output-variable name="confirmationNumber"/>
          <output-variable name="transactionId"/>
          <output-variable name="authorizationCode"/>
          <output-variable name="timestamp"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="AuthorizeByAnonymous_AC1707.paymentStatus == 'SUCCESS'">
          <session-mapping key="GlobalVars.isAuthorized" expr="AuthorizeByAnonymous_AC1707.isAuthorized"/>
          <session-mapping key="GlobalVars.isPosted" expr="AuthorizeByAnonymous_AC1707.isPosted"/>
          <session-mapping key="GlobalVars.resultCode" expr="AuthorizeByAnonymous_AC1707.resultCode"/>
          <session-mapping key="GlobalVars.confirmationNumber" expr="AuthorizeByAnonymous_AC1707.confirmationNumber"/>
          <session-mapping key="GlobalVars.timestamp" expr="AuthorizeByAnonymous_AC1707.timestamp"/>
          <session-mapping key="GlobalVars.transactionId" expr="AuthorizeByAnonymous_AC1707.transactionId"/>
          <session-mapping key="GlobalVars.authorizationCode" expr="AuthorizeByAnonymous_AC1707.authorizationCode"/>
          <session-mapping key="GlobalVars.amountPaidOnCall" expr="parseFloat(GlobalVars.amountPaidOnCall)+ parseFloat(paymentAmount)"/>
        </if>
        <session-mapping key="GlobalVars.paymentStatus" expr="AuthorizeByAnonymous_AC1707.paymentStatus"/>
        <action next="AC1710_CheckProcessingResults_JDA"/>
      </action>
    </data-access-state>

    <data-access-state id="AC1708_RechargeByVoucher_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="numberVouchersRedeemed" value="GlobalVars.numberVouchersRedeemed" type="String"/>
      <session-mapping key="voucherNumber" value="(GlobalVars.numberVouchersRedeemed &lt; GlobalVars.voucherNumberList.length) ? GlobalVars.voucherNumberList[numberVouchersRedeemed]:GlobalVars.voucherNumberList[0]" type="String"/>
      <data-access id="AnonymousRechargeByVoucher" classname="com.nuance.metro.dataaccess.AnonymousRechargeByVoucher">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="voucherNumber" mask="true"/>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="voucherNumber" mask="true"/>
          <output-variable name="reChargeStatus"/>
          <output-variable name="confirmationId"/>
          <output-variable name="reChargeErrorDesc"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.AnonymousRechargeByVoucher" expr="AnonymousRechargeByVoucher"/>
        <if cond="(AnonymousRechargeByVoucher &amp;&amp; AnonymousRechargeByVoucher.status &amp;&amp; AnonymousRechargeByVoucher.status.toUpperCase() == 'SUCCESS')">
          <session-mapping key="GlobalVars.voucherNumber" expr="AnonymousRechargeByVoucher.voucherNumber"/>
          <session-mapping key="GlobalVars.reChargeStatus" expr="AnonymousRechargeByVoucher.reChargeStatus"/>
          <session-mapping key="GlobalVars.confirmationId" expr="AnonymousRechargeByVoucher.confirmationId"/>
          <session-mapping key="GlobalVars.confirmationNumber" expr="AnonymousRechargeByVoucher.confirmationId"/>
          <session-mapping key="GlobalVars.reChargeErrorDesc" expr="AnonymousRechargeByVoucher.reChargeErrorDesc"/>
          <session-mapping key="GlobalVars.errorCode" expr="AnonymousRechargeByVoucher.errorCode"/>
          <if cond="GlobalVars.reChargeStatus == 'Y'">
            <session-mapping key="GlobalVars.numberVouchersRedeemed" expr="GlobalVars.numberVouchersRedeemed +1"/>
            <action next="AC1711_CheckMorePINs_JDA"/>
            <else>
              <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
              <action next="AC1715_ProcessingFailure_PP"/>
            </else>
          </if>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <action next="AC1715_ProcessingFailure_PP"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="AC1709_CheckPrepaidPINs_DS">
      <session-mapping key="numberPINPayments" value="GlobalVars.numberPINPayments != undefined ? GlobalVars.numberPINPayments : 0" type="String"/>
      <if cond="numberPINPayments &gt; 0">
        <action next="AC1713_PlayWaitMusic_PP"/>
        <else>
          <action next="AC1707_AuthorizeByAnonymous_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="AC1713_PlayWaitMusic_PP">
      <audio>
        <prompt id="AC1713_out_01">
          <prompt-segments>
            <audiofile src="AC1713_out_01.wav" text="3s of wait music"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AC1708_RechargeByVoucher_DB_DA"/>
    </play-state>

    <decision-state id="AC1710_CheckProcessingResults_DS">
      <session-mapping key="PaymentTable.ACTIVATION_ENDED_GMT" expr="getGMTTime()"/>
      <if cond="GlobalVars.activationStatus == 'Success'">
        <session-mapping key="ActivationTable.ACCOUNT_NUM" expr="GlobalVars.banId"/>
        <session-mapping key="ActivationTable.MDN" expr="GlobalVars.mdn"/>
        <session-mapping key="ActivationTable.BILLABLE_DATE" expr="GlobalVars.dueDate"/>
        <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'32'"/>
        <session-mapping key="activationVars.eventTypeGMT" expr="getEventTime()"/>
        <session-mapping key="activationVars.status" expr="'success'"/>
        <session-mapping key="activationVars.eventType" expr="'Activation'"/>
        <if cond="(GlobalVars.paymentStatus == 'SUCCESS' || GlobalVars.reChargeStatus == 'Y')&amp;&amp; (((GlobalVars.preferredPaymentMethod == 'credit' || GlobalVars.preferredPaymentMethod == 'debit') &amp;&amp;(GlobalVars.isAuthorized == true))|| ((GlobalVars.preferredPaymentMethod == 'metro') &amp;&amp; GlobalVars.isAuthorized == true)|| ((GlobalVars.payingWithPrepaid == true) &amp;&amp; GlobalVars.reChargeStatus == 'Y'))">
          <if cond="GlobalVars.preferredPaymentMethod == 'credit' || GlobalVars.preferredPaymentMethod == 'debit'">
            <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="81"/>
            <session-mapping key="PaymentTable.TRANSACTION_ID" expr="GlobalVars.transactionId"/>
            <session-mapping key="PaymentTable.TRANSACTION_AMOUNT" expr="GlobalVars.amountDue"/>
            <else>
              <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="82"/>
              <if cond="(GlobalVars.payingWithPrepaid == true) &amp;&amp; (GlobalVars.reChargeStatus == 'Y')">
                <session-mapping key="PaymentTable.TRANSACTION_ID" expr="GlobalVars.AnonymousRechargeByVoucher.confirmationId"/>
                <session-mapping key="PaymentTable.TRANSACTION_AMOUNT" expr="GlobalVars.SearchVoucherInfo.voucherAmount"/>
                <else>
                  <session-mapping key="PaymentTable.TRANSACTION_ID" expr="GlobalVars.incommConfNumber"/>
                  <session-mapping key="PaymentTable.TRANSACTION_AMOUNT" expr="GlobalVars.metroPaymentCardFaceValue"/>
                </else>
              </if>
            </else>
          </if>
          <session-mapping key="paymentVars.eventTypeGMT" expr="getEventTime()"/>
          <session-mapping key="paymentVars.status" expr="'success'"/>
          <session-mapping key="paymentVars.eventType" expr="'Payment'"/>
          <action next="AC2125_CheckWantPNSMS_JDA"/>
          <else>
            <if cond="GlobalVars.paymentStatus == 'PENDING'">
              <if cond="GlobalVars.preferredPaymentMethod == 'credit' || GlobalVars.preferredPaymentMethod == 'debit'">
                <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="86"/>
                <else>
                  <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="88"/>
                </else>
              </if>
              <elseif cond="GlobalVars.paymentStatus == 'NOT_ATTEMPTED'"/>
              <else>
                <if cond="GlobalVars.preferredPaymentMethod == 'credit' || GlobalVars.preferredPaymentMethod == 'debit'">
                  <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="85"/>
                  <else>
                    <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="87"/>
                  </else>
                </if>
              </else>
            </if>
            <session-mapping key="paymentVars.eventTypeGMT" expr="getEventTime()"/>
            <session-mapping key="paymentVars.status" expr="'failure'"/>
            <session-mapping key="paymentVars.eventType" expr="'Payment'"/>
            <action next="AC1715_ProcessingFailure_PP"/>
          </else>
        </if>
        <else>
          <if cond="GlobalVars.activationStatus == 'PENDING'">
            <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="78"/>
            <else>
              <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="77"/>
            </else>
          </if>
          <session-mapping key="activationVars.eventTypeGMT" expr="getEventTime()"/>
          <session-mapping key="activationVars.status" expr="'failure'"/>
          <session-mapping key="activationVars.eventType" expr="'Activation'"/>
          <action next="AC1715_ProcessingFailure_PP"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="AC1711_CheckMorePINs_DS">
      <if cond="parseFloat(GlobalVars.numberVouchersRedeemed) &lt; parseFloat(GlobalVars.voucherNumberList.length)">
        <action next="AC1708_RechargeByVoucher_DB_DA"/>
        <else>
          <action next="AC1712_CheckCardPayment_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="AC1712_CheckCardPayment_DS">
      <if cond="(parseFloat(GlobalVars.paymentAmount) &gt; 0) &amp;&amp;  (GlobalVars.bankCardNumber != undefined)">
        <action next="AC1707_AuthorizeByAnonymous_DB_DA"/>
        <else>
          <action next="AC1710_CheckProcessingResults_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="AC1715_ProcessingFailure_PP">
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <audio>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC1715_out_01" cond="accountPinToggleOn == true">
          <prompt-segments>
            <audiofile text="I'm sorry for the wait It looks like there was a problem creating your account I'll take you to one of our specialists for help They might need your device serial number to look up what happened, and they could ask you for the 8-digit security code we set up earlier Then they'll take your payment information again to complete your activation" src="AC1715_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC1715_out_02" cond="accountPinToggleOn != true">
          <prompt-segments>
            <audiofile src="AC1715_out_02.wav" text="I'm sorry for the wait It looks like there was a problem creating your account I'll take you to one of our specialists for help pause They might need your device serial number to look up what happened, and they could ask you for the 8-digit account PIN we set up earlier Then they'll take your payment information again to complete your activation"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
      <action next="getReturnLink()"/>
    </play-state>

    <decision-state id="AC2125_CheckWantPNSMS_DS">
      <if cond="GlobalVars.acceptedAPNSMS == true">
        <action next="AC2126_SendAPNSMS_SD"/>
        <else>
          <action next="AC2127_CheckWantAcctDetsSMS_JDA"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="AC2126_SendAPNSMS_SD">
      <gotodialog next="BYODInfo_Main_Dialog"/>
      <action next="AC2126_SendAPNSMS_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AC2126_SendAPNSMS_SD_return_CS">
      <action next="AC2127_CheckWantAcctDetsSMS_JDA"/>
    </custom-state>

    <decision-state id="AC2127_CheckWantAcctDetsSMS_DS">
      <if cond="GlobalVars.acceptedAcctDetsSMS == true || GlobalVars.acceptedAcctDetsSMS == 'true'">
        <action next="AC2128_SendAcctDetsSMS_SD"/>
        <else>
          <gotodialog next="Activation_Process#AC2130_AccountInfoReadback_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="AC2128_SendAcctDetsSMS_SD">
      <gotodialog next="ActivationSMS_Dialog"/>
      <action next="AC2128_SendAcctDetsSMS_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AC2128_SendAcctDetsSMS_SD_return_CS">
      <gotodialog next="Activation_Process#AC2130_AccountInfoReadback_SD"/>
    </custom-state>

  </dialog>
  