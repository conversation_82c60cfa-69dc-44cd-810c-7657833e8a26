<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="StoreLocator_Main_Dialog">
    <play-state id="SL1005_PlayPreambleInstructions_PP">
      <session-mapping key="storeLocationReason" value="unknown" type="String"/>
      <session-mapping key="storeLocReason" value="((storeLocatorReason == undefined)||(storeLocatorReason == 'undefined')||(storeLocatorReason == null)||(storeLocatorReason == ''))?storeLocationReason:storeLocatorReason" type="String"/>
      <session-mapping key="location" value="" type="String"/>
      <session-mapping key="firstSearch" value="true" type="Boolean"/>
      <session-mapping key="playResultsPreamble" value="true" type="Boolean"/>
      <audio>
        <prompt id="SL1005_ini_01" cond="storeLocReason == 'unknown'">
          <prompt-segments>
            <audiofile text="We have lots of places where you can  shop and sign up  or  make payments , and I can help you find some of the closest ones  And you can also make a payment at any Western Union Agent location" src="SL1005_ini_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="SL1005_ini_02" cond="storeLocReason == 'payment'">
          <prompt-segments>
            <audiofile text="We have lots of places where you can make a payment, and I can help you find some of the closest ones  And just so you know, you can also make payments at any Western Union Agent location" src="SL1005_ini_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="SL1005_ini_03" cond="storeLocReason == 'sign_up'">
          <prompt-segments>
            <audiofile text="We have lots of places where you can  shop and sign up , and I can help you find some of the closest ones" src="SL1005_ini_03.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="storeLocatorReason == 'invalidEquipment'">
        <session-mapping key="location" expr="GlobalVars.zipCode"/>
        <action next="SL1020_LookupLocations_DB_DA"/>
        <else>
          <action next="SL1010_AskLocation_DM"/>
        </else>
      </if>
    </play-state>

    <dm-state id="SL1010_AskLocation_DM" type="CUST">
      <success>
        <action label="default">
          <if cond="SL1010_AskLocation_DM.returnkeys &amp;&amp; SL1010_AskLocation_DM.returnkeys.city_name">
            <session-mapping key="location" expr="SL1010_AskLocation_DM.returnkeys.city_name.replace(/_/g,' ') + '-' + SL1010_AskLocation_DM.returnkeys.state_abbr"/>
            <else>
              <session-mapping key="location" expr="SL1010_AskLocation_DM.returnvalue"/>
              <session-mapping key="newlocation" expr="location"/>
            </else>
          </if>
          <audio>
            <if type="vxml" cond="firstSearch == true">
              <prompt id="SL1010_out_03">
                <prompt-segments>
                  <audiofile text="Hmmm let s see what we have near you" src="SL1010_out_03.wav"/>
                </prompt-segments>
              </prompt>
              <else>
                <prompt id="SL1010_out_04" cond="searchCount == 1">
                  <prompt-segments>
                    <audiofile text="Okay, let s see what we have there" src="SL1010_out_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1010_out_05" cond="searchCount != 1">
                  <prompt-segments>
                    <audiofile text="Let me see" src="SL1010_out_05.wav"/>
                  </prompt-segments>
                </prompt>
              </else>
            </if>
          </audio>
          <action next="SL1020_LookupLocations_DB_DA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SL1010_ini_02" cond="firstSearch == true &amp;&amp; storeLocatorReason == 'gettingStartedConfig'">
                <prompt-segments>
                  <audiofile text="To find out where you can sign up near you, tell me your city and state, or your zip code" src="SL1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SL1010_ini_01" cond="firstSearch == true &amp;&amp; storeLocatorReason != 'gettingStartedConfig'">
                <prompt-segments>
                  <audiofile text="Tell me the zip code or the city and state where you would like to look" src="SL1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SL1010_rin_01" cond="firstSearch == false">
                <prompt-segments>
                  <audiofile text="Please go ahead and say the new zip code *or* city and state" src="SL1010_rin_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="SL1010_AskLocation_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SL1010_ini_02" cond="firstSearch == true &amp;&amp; storeLocatorReason == 'gettingStartedConfig'">
                <prompt-segments>
                  <audiofile text="To find out where you can sign up near you, tell me your city and state, or your zip code" src="SL1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SL1010_ini_01" cond="firstSearch == true &amp;&amp; storeLocatorReason != 'gettingStartedConfig'">
                <prompt-segments>
                  <audiofile text="Tell me the zip code or the city and state where you would like to look" src="SL1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SL1010_rin_01" cond="firstSearch == false">
                <prompt-segments>
                  <audiofile text="Please go ahead and say the new zip code *or* city and state" src="SL1010_rin_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SL1010_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say the zip code or city and state where you would like to look" src="SL1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SL1010_nm2_01">
                <prompt-segments>
                  <audiofile text="To help you find the nearest location, I can search by city and state, or by zip code  So, please either name a city and state, like San Francisco, California, or say or enter a five-digit zip code" src="SL1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SL1010_nm2_01">
                <prompt-segments>
                  <audiofile text="To help you find the nearest location, I can search by city and state, or by zip code  So, please either name a city and state, like San Francisco, California, or say or enter a five-digit zip code" src="SL1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SL1010_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say the zip code or city and state where you would like to look" src="SL1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SL1010_nm2_01">
                <prompt-segments>
                  <audiofile text="To help you find the nearest location, I can search by city and state, or by zip code  So, please either name a city and state, like San Francisco, California, or say or enter a five-digit zip code" src="SL1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SL1010_nm2_01">
                <prompt-segments>
                  <audiofile text="To help you find the nearest location, I can search by city and state, or by zip code  So, please either name a city and state, like San Francisco, California, or say or enter a five-digit zip code" src="SL1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SL1010_ree_01">
                <prompt-segments>
                  <audiofile text="Please tell me either the zip code, or the name of the city and state where you would like to look" src="SL1010_ree_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
          <notoconfirmprompts count="2">
            <audio>
              <prompt id="SL1010_ree_01">
                <prompt-segments>
                  <audiofile text="Please tell me either the zip code, or the name of the city and state where you would like to look" src="SL1010_ree_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
          <notoconfirmprompts count="3">
            <audio>
              <prompt id="SL1010_ree_01">
                <prompt-segments>
                  <audiofile text="Please tell me either the zip code, or the name of the city and state where you would like to look" src="SL1010_ree_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SL1010_AskLocation_DM.grxml" count="1"/>
          <grammars filename="city_states_in_us_grammar.gram" count="2"/>
          <dtmfgrammars filename="SL1010_AskLocation_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="SL1020_LookupLocations_DB_DA">
      <session-mapping key="filterBy" value="GlobalVars.storeLocatorReason" type="String"/>
      <session-mapping key="location" value="((newlocation == undefined)||(newlocation == 'undefined')||(newlocation == null)||(newlocation == ''))?GlobalVars.zipCode:newlocation" type="String"/>
      <if cond="GlobalVars.storeLocatorReason == 'invalidequipment'">
        <session-mapping key="filterBy" expr="'sign up'"/>
        <elseif cond="!GlobalVars.storeLocatorReason">
          <session-mapping key="filterBy" expr="'sign up'"/>
        </elseif>
        <else>
          <session-mapping key="filterBy" expr="GlobalVars.storeLocatorReason"/>
        </else>
      </if>
      <data-access id="GetLocations" classname="com.nuance.metro.dataaccess.GetLocations">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="location"/>
          <input-variable name="filterBy"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="locations"/>
          <output-variable name="searchedBy"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.GetLocations" expr="GetLocations"/>
        <action next="SL1030_NumberOfResults_JDA"/>
      </action>
    </data-access-state>

    <play-state id="SL1023_NoResults_PP">
      <session-mapping key="storeLocatorReason" value="GlobalVars.storeLocatorReason" type="String"/>
      <audio>
        <prompt id="SL1023_out_03" cond="storeLocatorReason == 'invalidEquipment'">
          <prompt-segments>
            <audiofile text="I couldn t find any locations within 50 miles of your zip code" src="SL1023_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="SL1023_out_01" cond="storeLocatorReason != 'invalidEquipment'">
          <prompt-segments>
            <audiofile text="I couldn t find any locations within 50 miles of there" src="SL1023_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="SL1023_out_02">
          <prompt-segments>
            <audiofile text="You can find a map of our locations at metrobyt-mobilecom If you are done, just hang up Otherwise, let s try looking elsewhere" src="SL1023_out_02.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="firstSearch" value="false" type="Boolean"/>
      <session-mapping key="searchCount" expr="searchCount+1"/>
      <action next="SL1010_AskLocation_DM"/>
    </play-state>

    <decision-state id="SL1030_NumberOfResults_DS">
      <if cond="GlobalVars.GetLocations.locations.length == 0">
        <action next="SL1023_NoResults_PP"/>
        <else>
          <action next="SL1100_ResultsPreamble_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="SL1100_ResultsPreamble_PP">
      <session-mapping key="length" value="GlobalVars.GetLocations.locations.length" type="String"/>
      <session-mapping key="storeLocatorReason" value="GlobalVars.storeLocatorReason" type="String"/>
      <audio>
        <if cond="length == 1">
          <prompt id="SL1100_out_01">
            <prompt-segments>
              <audiofile text="I only found one location within " src="SL1100_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="there_are_2" cond="length == 2">
              <prompt-segments>
                <audiofile text="I found 2 locations within" src="there_are_2.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_3" cond="length == 3">
              <prompt-segments>
                <audiofile text="I found 3 locations within" src="there_are_3.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_4" cond="length == 4">
              <prompt-segments>
                <audiofile text="I found 4 locations within" src="there_are_4.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_5" cond="length == 5">
              <prompt-segments>
                <audiofile text="I found 5 locations within" src="there_are_5.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_6" cond="length == 6">
              <prompt-segments>
                <audiofile text="I found 6 locations within" src="there_are_6.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_7" cond="length == 7">
              <prompt-segments>
                <audiofile text="I found 7 locations within" src="there_are_7.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_8" cond="length == 8">
              <prompt-segments>
                <audiofile text="I found 8 locations within" src="there_are_8.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_9" cond="length == 9">
              <prompt-segments>
                <audiofile text="I found 9 locations within" src="there_are_9.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_10" cond="length == 10">
              <prompt-segments>
                <audiofile text="I found 10 locations within" src="there_are_10.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_11" cond="length == 11">
              <prompt-segments>
                <audiofile text="I found 11 locations within" src="there_are_11.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_12" cond="length == 12">
              <prompt-segments>
                <audiofile text="I found 12 locations within" src="there_are_12.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_13" cond="length == 13">
              <prompt-segments>
                <audiofile text="I found 13 locations within" src="there_are_13.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_14" cond="length == 14">
              <prompt-segments>
                <audiofile text="I found 14 locations within" src="there_are_14.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_15" cond="length == 15">
              <prompt-segments>
                <audiofile text="I found 15 locations within" src="there_are_15.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_16" cond="length == 16">
              <prompt-segments>
                <audiofile text="I found 16 locations within" src="there_are_16.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_17" cond="length == 17">
              <prompt-segments>
                <audiofile text="I found 17 locations within" src="there_are_17.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_18" cond="length == 18">
              <prompt-segments>
                <audiofile text="I found 18 locations within" src="there_are_18.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_19" cond="length == 19">
              <prompt-segments>
                <audiofile text="I found 19 locations within" src="there_are_19.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_20" cond="length == 20">
              <prompt-segments>
                <audiofile text="I found 20 locations within" src="there_are_20.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_21" cond="length == 21">
              <prompt-segments>
                <audiofile text="I found 21 locations within" src="there_are_21.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_22" cond="length == 22">
              <prompt-segments>
                <audiofile text="I found 22 locations within" src="there_are_22.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_23" cond="length == 23">
              <prompt-segments>
                <audiofile text="I found 23 locations within" src="there_are_23.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_24" cond="length == 24">
              <prompt-segments>
                <audiofile text="I found 24 locations within" src="there_are_24.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="there_are_25" cond="length == 25">
              <prompt-segments>
                <audiofile text="I found 25 locations within" src="there_are_25.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="SL1100_out_03" cond="storeLocatorReason == 'invalidEquipment'">
          <prompt-segments>
            <audiofile text="50 miles of your zip code" src="SL1100_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="SL1100_out_07" cond="storeLocatorReason != 'invalidEquipment'">
          <prompt-segments>
            <audiofile text="50 miles " src="SL1100_out_07.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="SL1100_out_08" cond="(length &gt; 5) &amp;&amp; (playResultsPreamble == true)">
          <prompt-segments>
            <audiofile text="I will read them out one by one, starting with the closest locations " src="SL1100_out_08.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="SL1100_out_02" cond="(length &gt;= 2) &amp;&amp; (length &lt;= 5) &amp;&amp; (playResultsPreamble == true)">
          <prompt-segments>
            <audiofile text="I will read them out one by one " src="SL1100_out_02.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="searchStatus" expr="'start'"/>
      <session-mapping key="playResultsPreamble" expr="(length &gt; 1)?false:playResultsPreamble"/>
      <action next="SL1101_ResultsNavigation_DM"/>
    </play-state>

    <dm-state id="SL1101_ResultsNavigation_DM" type="CUST">
      <session-mapping key="length" value="GlobalVars.GetLocations.locations.length" type="String"/>
      <session-mapping key="allowedOptions" value="" type="String"/>
      <session-mapping key="isPreviousAllowed" value="false" type="Boolean"/>
      <session-mapping key="isNextAllowed" value="false" type="Boolean"/>
      <session-mapping key="isMoreAllowed" value="false" type="Boolean"/>
      <session-mapping key="maxStoreIndex" expr="GlobalVars.GetLocations.locations.length - 1"/>
      <if cond="curStoreIndex &gt; 0">
        <session-mapping key="isPreviousAllowed" value="true" type="Boolean"/>
      </if>
      <if cond="curStoreIndex &lt; maxStoreIndex">
        <session-mapping key="isNextAllowed" value="true" type="Boolean"/>
      </if>
      <if cond="(curStoreIndex % 5 == 4) &amp;&amp; (curStoreIndex != maxStoreIndex) &amp;&amp; ((length - curStoreIndex) &gt; 5)">
        <session-mapping key="isMoreAllowed" value="true" type="Boolean"/>
      </if>
      <if cond="isPreviousAllowed == true &amp;&amp; isNextAllowed == true &amp;&amp; isMoreAllowed == true">
        <session-mapping key="allowedOptions" expr="'previous^next^more'"/>
        <elseif cond="isPreviousAllowed == true &amp;&amp; isNextAllowed == true &amp;&amp; isMoreAllowed == false">
          <session-mapping key="allowedOptions" expr="'previous^next'"/>
        </elseif>
        <elseif cond="isPreviousAllowed == false &amp;&amp; isNextAllowed == true &amp;&amp; isMoreAllowed == true">
          <session-mapping key="allowedOptions" expr="'next^more'"/>
        </elseif>
        <elseif cond="isPreviousAllowed == false &amp;&amp; isNextAllowed == true &amp;&amp; isMoreAllowed == false">
          <session-mapping key="allowedOptions" expr="'next'"/>
        </elseif>
        <elseif cond="isPreviousAllowed == true &amp;&amp; isNextAllowed == false">
          <session-mapping key="allowedOptions" expr="'previous'"/>
        </elseif>
        <else>
          <session-mapping key="allowedOptions" expr="''"/>
        </else>
      </if>
      <success>
        <action label="new" next="SL1010_AskLocation_DM">
          <session-mapping key="firstSearch" value="false" type="Boolean"/>
          <session-mapping key="searchCount" expr="searchCount+1"/>
          <if cond="length == 1">
            <session-mapping key="heardNavigationInstr" value="false" type="Boolean"/>
            <else>
              <session-mapping key="heardNavigationInstr" value="true" type="Boolean"/>
            </else>
          </if>
          <session-mapping key="isRepeat" value="false" type="Boolean"/>
          <session-mapping key="isFirstRepeatCnt" value="false" type="Boolean"/>
          <session-mapping key="curStoreIndex" expr="-1"/>
        </action>
        <action label="next">
          <session-mapping key="curStoreIndex" expr="curStoreIndex + 1"/>
        </action>
        <action label="previous">
          <session-mapping key="curStoreIndex" expr="curStoreIndex - 1"/>
          <session-mapping key="isPrevious" value="true" type="Boolean"/>
        </action>
        <action label="more">
          <session-mapping key="curStoreIndex" expr="curStoreIndex + 1"/>
        </action>
        <action label="done">
          <action next="getReturnLink()"/>
        </action>
        <action label="default" next="SL1102_ReadResults_PP">
          <session-mapping key="isRepeat" value="false" type="Boolean"/>
          <session-mapping key="isFirstRepeatCnt" value="false" type="Boolean"/>
        </action>
        <action label="operator"/>
        <action label="repeat">
          <if cond="curStoreIndex == -1">
            <session-mapping key="curStoreIndex" expr="curStoreIndex + 1"/>
            <else>
              <session-mapping key="isRepeat" value="true" type="Boolean"/>
              <if cond="isFirstRepeatCnt == false">
                <session-mapping key="isFirstRepeatCnt" value="true" type="Boolean"/>
                <audio>
                  <prompt id="SL1101_repeat_01">
                    <prompt-segments>
                      <audiofile text="Again, that was" src="SL1101_repeat_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </audio>
              </if>
            </else>
          </if>
          <action next="SL1102_ReadResults_PP"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="searchStatus == 'start'">
                <prompt id="SL1101_ini_01" cond="firstSearch == true &amp;&amp; length == 1">
                  <prompt-segments>
                    <audiofile text="" src="SL1101_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_02" cond="firstSearch == true &amp;&amp; length != 1">
                  <prompt-segments>
                    <audiofile text="To hear a store s information again, say repeat You can also say next or previous or look elsewhere" src="SL1101_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_03" cond="firstSearch == false &amp;&amp; length == 1">
                  <prompt-segments>
                    <audiofile text="" src="SL1101_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_04" cond="firstSearch == false &amp;&amp; length != 1 &amp;&amp; heardNavigationInstr == true">
                  <prompt-segments>
                    <audiofile text="You can say  repeat ,  next ,  previous , or  look elsewhere " src="SL1101_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_05" cond="firstSearch == false &amp;&amp; length != 1 &amp;&amp; heardNavigationInstr != true">
                  <prompt-segments>
                    <audiofile text="To hear a store s information again, say  repeat  You can also say  next  or  previous  or  look elsewhere" src="SL1101_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(curStoreIndex % 5 == 4) &amp;&amp; (curStoreIndex != maxStoreIndex)">
                <prompt id="silence_1000ms" cond="length &gt;5">
                  <prompt-segments>
                    <audiofile text="test" src="silence_1000ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_10" cond="length &gt;5">
                  <prompt-segments>
                    <audiofile text="To hear the rest of the locations, say  more stores  You can also say  look elsewhere  or  I m done here " src="SL1101_ini_10.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="curStoreIndex == maxStoreIndex">
                <prompt id="silence_1000ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_1000ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_07" cond="length == 1">
                  <prompt-segments>
                    <audiofile text="To hear that again, say  repeat  You can also say  look elsewhere , or  I m done here " src="SL1101_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_08" cond="length != 1">
                  <prompt-segments>
                    <audiofile text="You can say  look elsewhere , or  I m done here " src="SL1101_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="searchStatus == 'start'">
                <prompt id="SL1101_ini_01" cond="firstSearch == true &amp;&amp; length == 1">
                  <prompt-segments>
                    <audiofile text="" src="SL1101_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_02" cond="firstSearch == true &amp;&amp; length != 1">
                  <prompt-segments>
                    <audiofile text="To hear a store s information again, say repeat You can also say next or previous or look elsewhere" src="SL1101_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_03" cond="firstSearch == false &amp;&amp; length == 1">
                  <prompt-segments>
                    <audiofile text="" src="SL1101_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_04" cond="firstSearch == false &amp;&amp; length != 1 &amp;&amp; heardNavigationInstr == true">
                  <prompt-segments>
                    <audiofile text="You can say  repeat ,  next ,  previous , or  look elsewhere " src="SL1101_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_05" cond="firstSearch == false &amp;&amp; length != 1 &amp;&amp; heardNavigationInstr != true">
                  <prompt-segments>
                    <audiofile text="To hear a store s information again, say  repeat  You can also say  next  or  previous  or  look elsewhere" src="SL1101_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(curStoreIndex % 5 == 4) &amp;&amp; (curStoreIndex != maxStoreIndex)">
                <prompt id="silence_1000ms" cond="length &gt;5">
                  <prompt-segments>
                    <audiofile text="test" src="silence_1000ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_10" cond="length &gt;5">
                  <prompt-segments>
                    <audiofile text="To hear the rest of the locations, say  more stores  You can also say  look elsewhere  or  I m done here " src="SL1101_ini_10.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="curStoreIndex == maxStoreIndex">
                <prompt id="silence_1000ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_1000ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_07" cond="length == 1">
                  <prompt-segments>
                    <audiofile text="To hear that again, say  repeat  You can also say  look elsewhere , or  I m done here " src="SL1101_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SL1101_ini_08" cond="length != 1">
                  <prompt-segments>
                    <audiofile text="You can say  look elsewhere , or  I m done here " src="SL1101_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="SL1101_ResultsNavigation_DM.jsp" count="1">
            <param name="allowedOptions" value="allowedOptionsVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="SL1101_ResultsNavigation_DM_dtmf.jsp" count="1">
            <param name="allowedOptions" value="allowedOptionsVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="2000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="1" maxnomatches="0" maxnoinputs="1"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="gl_cnf_ini_07">
                <prompt-segments>
                  <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="2000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="SL1102_ReadResults_PP">
      <session-mapping key="length" value="GlobalVars.GetLocations.locations.length" type="String"/>
      <session-mapping key="cust_store_type" value="GlobalVars.GetLocations.locations[curStoreIndex].storeType" type="String"/>
      <session-mapping key="cust_store_id" value="GlobalVars.GetLocations.locations[curStoreIndex].storeId" type="String"/>
      <session-mapping key="name" value="GlobalVars.GetLocations.locations[curStoreIndex].name" type="String"/>
      <session-mapping key="address" value="GlobalVars.GetLocations.locations[curStoreIndex].address" type="String"/>
      <session-mapping key="city" value="GlobalVars.GetLocations.locations[curStoreIndex].city" type="String"/>
      <session-mapping key="state" value="GlobalVars.GetLocations.locations[curStoreIndex].state" type="String"/>
      <session-mapping key="zip" value="GlobalVars.GetLocations.locations[curStoreIndex].zip" type="String"/>
      <session-mapping key="fullAddress" value="address + ',' + city + ',' + state + ',' + zip" type="String"/>
      <audio>
        <if cond="length &gt; 1 &amp;&amp; isRepeat == false">
          <prompt id="SL1102_out_01" cond="curStoreIndex == 0 &amp;&amp; isPrevious == false">
            <prompt-segments>
              <audiofile text="The first  one is " src="SL1102_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="SL1102_out_02" cond="curStoreIndex == maxStoreIndex &amp;&amp; isPrevious == false">
            <prompt-segments>
              <audiofile text="The last one is " src="SL1102_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="SL1102_out_03" cond="isPrevious == true">
            <prompt-segments>
              <audiofile text="The previous one is" src="SL1102_out_03.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="SL1102_out_05" cond="curStoreIndex == 1 &amp;&amp; isPrevious == false">
            <prompt-segments>
              <audiofile text="The next one is" src="SL1102_out_05.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="SL1102_out_06" cond="!((curStoreIndex == 0 &amp;&amp; isPrevious == false) || (curStoreIndex == maxStoreIndex &amp;&amp; isPrevious == false) || isPrevious == true || (curStoreIndex == 1 &amp;&amp; isPrevious == false))">
            <prompt-segments>
              <audiofile text="Next is" src="SL1102_out_06.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="SL1102_out_07" cond="cust_store_type == 'MetroSt' &amp;&amp; isRepeat == false">
          <prompt-segments>
            <audiofile text="A Metro Corporate Store" src="SL1102_out_07.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="SL1102_out_08" cond="cust_store_type == 'Dealer' &amp;&amp; isRepeat == false">
          <prompt-segments>
            <audiofile text="an Authorized Dealer" src="SL1102_out_08.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms" cond="cust_store_type == 'MetroSt' || cust_store_type == 'Dealer' &amp;&amp; isRepeat == false">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="tts" expr="name"/>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="fullAddress">
          <param name="prosodyRate" value="-10%"/>
          <param name="sayAs" value="address"/>
          <param name="className" value="com.nuance.metro.audio.custom.SayasPromptRendererImpl"/>
        </prompt>
      </audio>
      <session-mapping key="isPrevious" value="false" type="Boolean"/>
      <session-mapping key="searchStatus" expr="'middle'"/>
      <action next="SL1101_ResultsNavigation_DM"/>
    </play-state>

  </dialog>
  