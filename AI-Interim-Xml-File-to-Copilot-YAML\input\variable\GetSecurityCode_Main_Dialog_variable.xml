<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="pin" value="empty" type="String"/>
  <session-mapping key="accessToken" value="empty" type="String"/>
  <session-mapping key="verificationType" value="empty" type="String"/>
  <session-mapping key="verificationValue" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="providerId" value="empty" type="String"/>
  <session-mapping key="GlobalVars.lastPinTry" value="true" type="boolean"/>
  <session-mapping key="accountPinToggleOn" value="true" type="boolean"/>
  <session-mapping key="heardCodeInfo" value="true" type="boolean"/>
  <session-mapping key="lastPinTry" value="true" type="boolean"/>
  <session-mapping key="FromGC1010" value="true" type="boolean"/>
  <session-mapping key="saidOperator" value="true" type="string"/>
  <session-mapping key="Authenticate.status" value="FAILURE" type="string"/>
  <session-mapping key="Authenticate.acctLocked" value="true" type="boolean"/>
  <session-mapping key="Authenticate.onePinTryRemaining" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.onePinTryRemaining" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.acctLocked" value="true" type="boolean"/>
  <session-mapping key="GetSecurityCodeVars.invalidSecCodeAttempts" value="2" type="integer"/>
</session-mappings>
