<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="LostPhone_Main_Dialog">
    <dm-state id="LP1005_LostPhoneHandling_DM" type="CUST">
      <success>
        <action label="suspend">
          <audio>
            <prompt id="LP1005_out_01">
              <prompt-segments>
                <audiofile text="Just so you know, to suspend your account, we'll need the phone number for the device and the account PIN for the account" src="LP1005_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_750ms">
              <prompt-segments>
                <audiofile text="test" src="silence_750ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="LP1010_GoToCallTransfer_SD"/>
        </action>
        <action label="insurance">
          <action next="LP1020_InsuranceInfoWait_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <action next="LP1015_GoToGoodbye_SD"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="LP1005_ini_01">
                <prompt-segments>
                  <audiofile text="If you need to find out about temporarily suspending service while you look for your phone, say suspend line If you signed up for phone insurance, and would like to find out about a replacement phone, filing a claim, deductible, and more, say phone insurance" src="LP1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="LP1005_ini_01">
                <prompt-segments>
                  <audiofile text="If you need to find out about temporarily suspending service while you look for your phone, say suspend line If you signed up for phone insurance, and would like to find out about a replacement phone, filing a claim, deductible, and more, say phone insurance" src="LP1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="LP1005_nm1_01">
                <prompt-segments>
                  <audiofile text="If you would like to temporarily suspend your service, say suspend line If you have phone insurance and would like to find out how you can get a replacement phone, file a claim and more, say phone insurance " src="LP1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you need to temporarily suspend your service, please say suspend line or press 1 If you signed up for insurance and need to know where to get started with it, please say phone insurance or press 2 " src="LP1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you need to temporarily suspend your service, please say suspend line or press 1 If you signed up for insurance and need to know where to get started with it, please say phone insurance or press 2 " src="LP1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1005_nm1_01">
                <prompt-segments>
                  <audiofile text="If you would like to temporarily suspend your service, say suspend line If you have phone insurance and would like to find out how you can get a replacement phone, file a claim and more, say phone insurance " src="LP1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you need to temporarily suspend your service, please say suspend line or press 1 If you signed up for insurance and need to know where to get started with it, please say phone insurance or press 2 " src="LP1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you need to temporarily suspend your service, please say suspend line or press 1 If you signed up for insurance and need to know where to get started with it, please say phone insurance or press 2 " src="LP1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="LP1005_ini_01">
                <prompt-segments>
                  <audiofile text="If you need to find out about temporarily suspending service while you look for your phone, say suspend line If you signed up for phone insurance, and would like to find out about a replacement phone, filing a claim, deductible, and more, say phone insurance" src="LP1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="LP1005_LostPhoneHandling_DM.grxml" count="1"/>
          <dtmfgrammars filename="LP1005_LostPhoneHandling_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="LP1010_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="LP1010_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="LP1010_GoToCallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="LP1015_GoToGoodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </subdialog-state>
    <custom-state id="LP1015_GoToGoodbye_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <dm-state id="LP1020_InsuranceInfoWait_DM" type="CUST">
      <success>
        <action label="continue">
          <action next="LP1025_PhoneInsuranceInfo_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <action next="LP1015_GoToGoodbye_SD"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="LP1020_ini_01">
                <prompt-segments>
                  <audiofile text="Okay I will give you a phone number and website where you can find help When youre ready to write this down, say ContinueWhen youre ready, say Continue, or press 1 You can say Continue, or press 1, at any time If youre ready to hear the the MetroGuard phone number and website, say Continue or press 1 If youre ready,  say Continue or press 1  When youre ready say Continue or press 1 Im having some trouble Lets move on" src="LP1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="LP1020_ini_01">
                <prompt-segments>
                  <audiofile text="Okay I will give you a phone number and website where you can find help When youre ready to write this down, say ContinueWhen youre ready, say Continue, or press 1 You can say Continue, or press 1, at any time If youre ready to hear the the MetroGuard phone number and website, say Continue or press 1 If youre ready,  say Continue or press 1  When youre ready say Continue or press 1 Im having some trouble Lets move on" src="LP1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="LP1020_ini_01">
                <prompt-segments>
                  <audiofile text="Okay I will give you a phone number and website where you can find help When youre ready to write this down, say ContinueWhen youre ready, say Continue, or press 1 You can say Continue, or press 1, at any time If youre ready to hear the the MetroGuard phone number and website, say Continue or press 1 If youre ready,  say Continue or press 1  When youre ready say Continue or press 1 Im having some trouble Lets move on" src="LP1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="LP1020_InsuranceInfoWait_DM.grxml" count="1"/>
          <dtmfgrammars filename="LP1020_InsuranceInfoWait_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="1000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="LP1025_PhoneInsuranceInfo_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="repromptFlag" value="true" type="Boolean"/>
          <action next="LP1025_PhoneInsuranceInfo_DM"/>
        </action>
        <action label="false">
          <action next="LP1030_SuspendLineYN_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <action next="LP1015_GoToGoodbye_SD"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="repromptFlag == true">
                <prompt id="LP1025_ree_01">
                  <prompt-segments>
                    <audiofile text="You can call ************ or go online to phoneclaimcom slash Metro to file a claim or get other information" src="LP1025_ree_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="LP1025_ree_02">
                  <prompt-segments>
                    <audiofile text="Would you like me to repeat that" src="LP1025_ree_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LP1025_ini_01">
                    <prompt-segments>
                      <audiofile text="To get started with an insurance claim for your phone, please call ************ Or go online to  phoneclaimcom slash Metro You'll  also be able to get answers about things like getting a replacement phone, and the deductible" src="LP1025_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="LP1025_ini_02">
                    <prompt-segments>
                      <audiofile text="Would you like to hear that again" src="LP1025_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="repromptFlag == true">
                <prompt id="LP1025_ree_01">
                  <prompt-segments>
                    <audiofile text="You can call ************ or go online to phoneclaimcom slash Metro to file a claim or get other information" src="LP1025_ree_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="LP1025_ree_02">
                  <prompt-segments>
                    <audiofile text="Would you like me to repeat that" src="LP1025_ree_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LP1025_ini_01">
                    <prompt-segments>
                      <audiofile text="To get started with an insurance claim for your phone, please call ************ Or go online to  phoneclaimcom slash Metro You'll  also be able to get answers about things like getting a replacement phone, and the deductible" src="LP1025_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="LP1025_ini_02">
                    <prompt-segments>
                      <audiofile text="Would you like to hear that again" src="LP1025_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="LP1025_ree_01">
                <prompt-segments>
                  <audiofile text="You can call ************ or go online to phoneclaimcom slash Metro to file a claim or get other information" src="LP1025_ree_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1025_ree_02">
                <prompt-segments>
                  <audiofile text="Would you like me to repeat that" src="LP1025_ree_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1025_ree_01">
                <prompt-segments>
                  <audiofile text="You can call ************ or go online to phoneclaimcom slash Metro to file a claim or get other information" src="LP1025_ree_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1025_ree_02">
                <prompt-segments>
                  <audiofile text="Would you like me to repeat that" src="LP1025_ree_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1025_ree_01">
                <prompt-segments>
                  <audiofile text="You can call ************ or go online to phoneclaimcom slash Metro to file a claim or get other information" src="LP1025_ree_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1025_ree_02">
                <prompt-segments>
                  <audiofile text="Would you like me to repeat that" src="LP1025_ree_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1025_ree_01">
                <prompt-segments>
                  <audiofile text="You can call ************ or go online to phoneclaimcom slash Metro to file a claim or get other information" src="LP1025_ree_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1025_ree_02">
                <prompt-segments>
                  <audiofile text="Would you like me to repeat that" src="LP1025_ree_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1025_ree_01">
                <prompt-segments>
                  <audiofile text="You can call ************ or go online to phoneclaimcom slash Metro to file a claim or get other information" src="LP1025_ree_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1025_ree_02">
                <prompt-segments>
                  <audiofile text="Would you like me to repeat that" src="LP1025_ree_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1025_ree_01">
                <prompt-segments>
                  <audiofile text="You can call ************ or go online to phoneclaimcom slash Metro to file a claim or get other information" src="LP1025_ree_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1025_ree_02">
                <prompt-segments>
                  <audiofile text="Would you like me to repeat that" src="LP1025_ree_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="repromptFlag == true">
                <prompt id="LP1025_ree_01">
                  <prompt-segments>
                    <audiofile text="You can call ************ or go online to phoneclaimcom slash Metro to file a claim or get other information" src="LP1025_ree_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="LP1025_ree_02">
                  <prompt-segments>
                    <audiofile text="Would you like me to repeat that" src="LP1025_ree_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LP1025_ini_01">
                    <prompt-segments>
                      <audiofile text="To get started with an insurance claim for your phone, please call ************ Or go online to  phoneclaimcom slash Metro You'll  also be able to get answers about things like getting a replacement phone, and the deductible" src="LP1025_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="LP1025_ini_02">
                    <prompt-segments>
                      <audiofile text="Would you like to hear that again" src="LP1025_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="LP1025_PhoneInsuranceInfo_DM.grxml" count="1"/>
          <dtmfgrammars filename="LP1025_PhoneInsuranceInfo_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="LP1030_SuspendLineYN_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
          <audio>
            <prompt id="LP1030_out_01">
              <prompt-segments>
                <audiofile text="Okay" src="LP1030_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="LP1010_GoToCallTransfer_SD"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="LP1030_out_02">
              <prompt-segments>
                <audiofile text="No problem" src="LP1030_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="LP1015_GoToGoodbye_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <action next="LP1015_GoToGoodbye_SD"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="LP1030_ini_01">
                <prompt-segments>
                  <audiofile text="To make sure no one can use your phone  in the meantime, and to avoid paying for the days you cant use it, you can temporarily suspend that line " src="LP1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1030_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to do that now" src="LP1030_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="LP1030_ini_01">
                <prompt-segments>
                  <audiofile text="To make sure no one can use your phone  in the meantime, and to avoid paying for the days you cant use it, you can temporarily suspend that line " src="LP1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1030_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to do that now" src="LP1030_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="LP1030_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say yes or no" src="LP1030_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you would like to temporarily suspend service to your phone line, say yes Otherwise, say no" src="LP1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you would like to temporarily suspend service to your phone line, say yes Otherwise, say no" src="LP1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1030_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say yes or no" src="LP1030_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you would like to temporarily suspend service to your phone line, say yes Otherwise, say no" src="LP1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you would like to temporarily suspend service to your phone line, say yes Otherwise, say no" src="LP1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="LP1030_ini_01">
                <prompt-segments>
                  <audiofile text="To make sure no one can use your phone  in the meantime, and to avoid paying for the days you cant use it, you can temporarily suspend that line " src="LP1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LP1030_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to do that now" src="LP1030_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="LP1030_SuspendLineYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="LP1030_SuspendLineYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  