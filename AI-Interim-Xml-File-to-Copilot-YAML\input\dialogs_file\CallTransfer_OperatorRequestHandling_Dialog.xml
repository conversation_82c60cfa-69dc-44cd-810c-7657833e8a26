<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="CallTransfer_OperatorRequestHandling_Dialog">
    <decision-state id="XR1005_CheckConfirmationConfidence_DS">
      <session-mapping key="GlobalVars.isSuspended" expr="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false"/>
      <if cond="GlobalVars.isSuspended">
        <session-mapping key="maxRequests" expr="GlobalVars.unCoopMaxRequest"/>
        <else>
          <session-mapping key="maxRequests" expr="GlobalVars.coopMaxRequest"/>
        </else>
      </if>
      <if cond="operatorNeedsConfirmation == false">
        <action next="XR1015_CheckAllowedConditions_JDA"/>
        <else>
          <action next="XR1010_ConfirmOperatorRequest_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="XR1010_ConfirmOperatorRequest_DM" type="YSNO">
      <success>
        <action label="true" next="XR1015_CheckAllowedConditions_JDA">
          <session-mapping key="operatorNeedsConfirmation" value="true" type="Boolean"/>
        </action>
        <action label="false">
          <session-mapping key="callerSaidOperator" value="false" type="Boolean"/>
          <audio>
            <prompt id="XR1010_out_01">
              <prompt-segments>
                <audiofile text="Sorry about that" src="XR1010_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="(GlobalVars.callType == 'make_pmt') || (GlobalVars.suspendedOperatorRequest == true) || (GlobalVars.GetAccountDetails == null || GlobalVars.GetAccountDetails == '' || GlobalVars.GetAccountDetails == undefined) ">
            <session-mapping key="GlobalVars.suspendedOperatorRequest" value="false" type="Boolean"/>
            <action next="getReturnLink()"/>
            <elseif cond="(GlobalVars.GetBCSParameters.care_nlu_enabled == 'true' &amp;&amp; language == 'en-US')">
              <session-mapping key="GlobalVars.operatorRequestMidFlowNLU" value="true" type="Boolean"/>
              <session-mapping key="GlobalVars.callType" expr="undefined"/>
              <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
              <session-mapping key="GlobalVars.tag" expr="undefined"/>
              <action next="XR1021_InitialHandling_SD"/>
            </elseif>
            <else>
              <action next="XR1065_GoToMainMenu_SD"/>
            </else>
          </if>
        </action>
        <action label="operator" next="XR1015_CheckAllowedConditions_JDA"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="XR1010_ini_01">
                <prompt-segments>
                  <audiofile text="Hmm, I think you said you wanted to talk to someone, right?" src="XR1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="XR1010_ini_01">
                <prompt-segments>
                  <audiofile text="Hmm, I think you said you wanted to talk to someone, right?" src="XR1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="XR1010_ini_01">
                <prompt-segments>
                  <audiofile text="Hmm, I think you said you wanted to talk to someone, right?" src="XR1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="XR1010_ConfirmOperatorRequest_DM.grxml" count="1"/>
          <dtmfgrammars filename="XR1010_ConfirmOperatorRequest_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="XR1015_CheckAllowedConditions_DS">
      <session-mapping key="randPromptTurn" value="random(2)" type="String"/>
      <session-mapping key="troubleshootEnabled" value="GlobalVars.GetBCSParameters.care_troubleshootPromptEnabled == 'true'" type="String"/>
      <session-mapping key="care_allow_payment_transfers" value="GlobalVars.GetBCSParameters.care_allow_payment_transfers == 'true'" type="String"/>
      <session-mapping key="care_allow_suspended_transfers" value="GlobalVars.GetBCSParameters.care_allow_suspended_transfers == 'true'" type="String"/>
      <session-mapping key="accountStatus" value="(GlobalVars.GetAccountDetails)?GlobalVars.GetAccountDetails.accountStatus :''" type="String"/>
      <session-mapping key="securityQuestionAnswerIsValid" value="(GlobalVars.GetAccountDetails)?GlobalVars.GetAccountDetails.securityQuestionAnswerIsValid : false" type="String"/>
      <if cond="GlobalVars.callType == 'about_metro' || GlobalVars.callType == 'new_customer' || GlobalVars.callType == 'ThirdPartyFeaturesXfer' || GlobalVars.callType == 'auto_pay'">
        <gotodialog next="CallTransfer_Main#XR0023_GoToBroadcastMessages_SD"/>
        <elseif cond="care_allow_payment_transfers == false &amp;&amp; GlobalVars.operatorFromPayment == true">
          <session-mapping key="callerSaidOperator" value="false" type="Boolean"/>
          <gotodialog next="CallTransfer_OperatorRequestHandling#XR1040_PaymentTransfersNotAllowed_PP"/>
        </elseif>
        <elseif cond="(operatorReqCount &gt; maxRequests) &amp;&amp; (accountStatus == 'suspended') &amp;&amp; (care_allow_suspended_transfers == false)">
          <session-mapping key="GlobalVars.maxRequests" expr="maxRequests"/>
          <gotodialog next="CallTransfer_Main#XR0040_GoToGoodbye_SD"/>
        </elseif>
        <elseif cond="(operatorReqCount &gt; maxRequests)  || (GlobalVars.tag == 'transfer-disambig_troubleshooting' || GlobalVars.tag == 'transfer-disambig_billing' || GlobalVars.tag == 'transfer-disambig_payment' || GlobalVars.tag == 'transfer-disambig_plan' || GlobalVars.tag == 'transfer-disambig_other')">
          <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
          <session-mapping key="callerSaidOperator" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_TransferReason" expr="getctiTransferReason(GlobalVars.GetCTIParameters.ctiTransferReason, 'xferred_max_agent_requests')"/>
          <if cond="GlobalVars.loggedIn == true">
            <gotodialog next="CallTransfer_Main#XR0023_GoToBroadcastMessages_SD"/>
            <elseif cond="!(GlobalVars.GetAccountDetails == null || GlobalVars.GetAccountDetails == '' || GlobalVars.GetAccountDetails == undefined)">
              <action next="XR1505_PreTransferCode_SD"/>
            </elseif>
            <else>
              <gotodialog next="CallTransfer_Main#XR0023_GoToBroadcastMessages_SD"/>
            </else>
          </if>
        </elseif>
        <elseif cond="operatorReqCount &lt;= maxRequests">
          <session-mapping key="callerSaidOperator" value="false" type="Boolean"/>
          <action next="XR1020_AcknowledgeTransferRequest_PP"/>
        </elseif>
        <else>
          <session-mapping key="callerSaidOperator" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_TransferReason" expr="getctiTransferReason(GlobalVars.GetCTIParameters.ctiTransferReason, 'xferred_max_agent_requests')"/>
          <gotodialog next="CallTransfer_Main#XR0025_PlayTransferPrompt_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="XR1020_AcknowledgeTransferRequest_PP">
      <session-mapping key="care_enable_high_volume_prompts" value="GlobalVars.GetBCSParameters.care_enable_high_volume_prompts == true || GlobalVars.GetBCSParameters.care_enable_high_volume_prompts == 'true'" type="String"/>
      <session-mapping key="XR1020_visit_count" value="GlobalVars.XR1020_visit_count" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <audio>
        <prompt id="XR1020_out_01" cond="operatorReqCount == 1">
          <prompt-segments>
            <audiofile text="Okay agent It s a busy time, so you might need to wait a while to talk to someone " src="XR1020_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="care_enable_high_volume_prompts == true">
          <prompt id="XR1020_out_03" cond="XR1020_visit_count % 5 == 0 ">
            <prompt-segments>
              <audiofile text="Our call center is currently getting more calls than normal, and you might have to wait much longer than usual Lets try this again " src="XR1020_out_03.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="XR1020_out_04" cond="XR1020_visit_count % 5 == 1 ">
            <prompt-segments>
              <audiofile text="Our agents are currently busy, so lets try again " src="XR1020_out_04.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="XR1020_out_05" cond="XR1020_visit_count % 5 == 2 ">
            <prompt-segments>
              <audiofile text="The wait time to speak to an agent is unusually high right now Let me try to help you here first " src="XR1020_out_05.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="XR1020_out_06" cond="XR1020_visit_count % 5 == 3 ">
            <prompt-segments>
              <audiofile text="We dont have any available agents right now, and the wait time may be very long But I can help you right now " src="XR1020_out_06.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="XR1020_out_07" cond="XR1020_visit_count % 5 == 4 ">
            <prompt-segments>
              <audiofile text="Our call center is currently very busy But you dont have to wait Lets see what we can do right here " src="XR1020_out_07.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="(GlobalVars.isSuspended == 1) || ((GlobalVars.transferFrom == 'LoginSD_XR') &amp;&amp; (GlobalVars.noAccountFound != true)) || (callType == 'make_pmt')">
            <prompt id="XR1020_out_13" cond="XR1020_visit_count % 5 == 0">
              <prompt-segments>
                <audiofile text="I can help you get this done right here Lets try again" src="XR1020_out_13.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="XR1020_out_14" cond="XR1020_visit_count % 5 == 1">
              <prompt-segments>
                <audiofile text="I can help you right here Lets give it another try" src="XR1020_out_14.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="XR1020_out_15" cond="XR1020_visit_count % 5 == 2">
              <prompt-segments>
                <audiofile text="Lets see what more we can do here" src="XR1020_out_15.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="XR1020_out_16" cond="XR1020_visit_count % 5 == 3">
              <prompt-segments>
                <audiofile text="Lets try to get a little bit more done right here " src="XR1020_out_16.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="XR1020_out_17" cond="XR1020_visit_count % 5 == 4">
              <prompt-segments>
                <audiofile text="Let me try to help you here " src="XR1020_out_17.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="XR1020_out_08" cond="XR1020_visit_count % 5 == 0">
              <prompt-segments>
                <audiofile text="To get you to right place, I need to know what you need help with" src="XR1020_out_08.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="XR1020_out_09" cond="XR1020_visit_count % 5 == 1">
              <prompt-segments>
                <audiofile text="Let me first try to help you out right here" src="XR1020_out_09.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="XR1020_out_10" cond="XR1020_visit_count % 5 == 2">
              <prompt-segments>
                <audiofile text="Please, let me understand how I can help you today" src="XR1020_out_10.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="XR1020_out_11" cond="XR1020_visit_count % 5 == 3">
              <prompt-segments>
                <audiofile text="In order to help you better, I need to know what youre calling about" src="XR1020_out_11.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="XR1020_out_12" cond="XR1020_visit_count % 5 == 4">
              <prompt-segments>
                <audiofile text="Lets see if I can help you here Please tell me what youre calling about" src="XR1020_out_12.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_500ms" cond="callType == 'make_pmt' || GlobalVars.suspendedOperatorRequest == true">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms" cond="callType != 'make_pmt' &amp;&amp; GlobalVars.suspendedOperatorRequest != true">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.XR1020_visit_count" expr="GlobalVars.XR1020_visit_count + 1"/>
      <if cond="(GlobalVars.callType == 'make_pmt') || (GlobalVars.suspendedOperatorRequest == true) || (GlobalVars.GetAccountDetails == null || GlobalVars.GetAccountDetails == '' || GlobalVars.GetAccountDetails == undefined) ">
        <session-mapping key="GlobalVars.suspendedOperatorRequest" value="false" type="Boolean"/>
        <action next="getReturnLink()"/>
        <elseif cond="(GlobalVars.GetBCSParameters.care_nlu_enabled == 'true' &amp;&amp; language == 'en-US')">
          <session-mapping key="GlobalVars.operatorRequestMidFlowNLU" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.tag" expr="undefined"/>
          <action next="XR1021_InitialHandling_SD"/>
        </elseif>
      </if>
      <session-mapping key="GlobalVars.callType" expr="undefined"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <action next="XR1065_GoToMainMenu_SD"/>
    </play-state>

    <subdialog-state id="XR1021_InitialHandling_SD">
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <gotodialog next="InitialHandling_Main_Dialog"/>
      <action next="XR1021_InitialHandling_SD_return"/>
    </subdialog-state>
    <play-state id="XR1040_PaymentTransfersNotAllowed_PP">
      <session-mapping key="operatorPaymentsReqCount" value="GlobalVars.operatorPaymentsReqCount" type="String"/>
      <audio>
        <prompt id="XR1040_out_01" cond="operatorPaymentsReqCount == 1">
          <prompt-segments>
            <audiofile text="I understand youd like to speak to a representative But to give you the features you like at the prices you love, weve designed this system to support you" src="XR1040_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="XR1040_out_02" cond="operatorPaymentsReqCount == 1">
          <prompt-segments>
            <audiofile text="Lets try again" src="XR1040_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="XR1040_out_03" cond="operatorPaymentsReqCount == 2">
          <prompt-segments>
            <audiofile text="Sorry, I cant transfer you at this time Lets try one last time" src="XR1040_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="XR1040_out_04" cond="operatorPaymentsReqCount == 3">
          <prompt-segments>
            <audiofile text="Sorry, but I cant transfer you to an agent right now Please use the myMetro app on your phone, or visit us at metrobyt-mobilecom when youre ready to make your payment" src="XR1040_out_04.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.operatorFromPayment " expr="undefined"/>
      <if cond="operatorPaymentsReqCount == 1">
        <action next="getReturnLink()"/>
        <elseif cond="operatorPaymentsReqCount == 2">
          <action next="getReturnLink()"/>
        </elseif>
        <else>
          <gotodialog next="CallTransfer_Main#XR0040_GoToGoodbye_SD"/>
        </else>
      </if>
    </play-state>

    <subdialog-state id="XR1065_GoToMainMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
      <action next="CallTransfer_Terminal_return_CS"/>
    </subdialog-state>
    <custom-state id="CallTransfer_Terminal_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="XR1505_PreTransferCode_SD">
      <gotodialog next="PreTransferSecurityCode_Dialog"/>
      <action next="XR1505_PreTransferCode_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="XR1505_PreTransferCode_SD_return_CS">
      <gotodialog next="CallTransfer_Main#XR0023_GoToBroadcastMessages_SD"/>
    </custom-state>

  </dialog>
  