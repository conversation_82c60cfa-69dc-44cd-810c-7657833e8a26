<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="AskSecurityQuestion_Main_Dialog">
    <decision-state id="AQ1001_CheckRouting_DS">
      <session-mapping key="GlobalVars.saidOperatorAQ1105" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.saidOperatorAQ1110" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.saidOperatorAQ1115" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.saidOperatorAQ1120" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.saidOperatorAQ1125" value="false" type="Boolean"/>
      <action next="AQ1002_CheckSecurityQuestion_JDA"/>
    </decision-state>

    <decision-state id="AQ1002_CheckSecurityQuestion_DS">
      <session-mapping key="GlobalVars.securityQuestionAttempts" expr="0"/>
      <if cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ2')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ3')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ5')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q10')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q13')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q14'))">
        <action next="AQ1105_NamesQuestion_DM"/>
        <elseif cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ6')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q12'))">
          <action next="AQ1110_StreetQuestion_DM"/>
        </elseif>
        <elseif cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ4')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q11'))">
          <action next="AQ1115_SchoolQuestion_DM"/>
        </elseif>
        <elseif cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ7'))">
          <action next="AQ1120_ColorQuestion_DM"/>
        </elseif>
        <else>
          <action next="AQ1125_CityAndMiscQuestion_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="AQ1105_NamesQuestion_DM" type="CUST">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorAQ1105?GlobalVars.saidOperatorAQ1105:false" type="String"/>
      <session-mapping key="securityInfo" value="" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="securityQuestionCode" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityQuestionCode:''" type="String"/>
      <session-mapping key="securityAnswer" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityAnswer:''" type="String"/>
      <session-mapping key="collection_maxnoinputs" value="GlobalVars.smsReceived?1:2" type="String"/>
      <session-mapping key="collection_maxnomatches" value="GlobalVars.smsReceived?1:2" type="String"/>
      <session-mapping key="securityInfo" expr="securityAnswer + ';' + securityQuestionCode + ';' + language"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorAQ1105" value="false" type="Boolean"/>
        <action label="wait">
          <action next="AQ1130_GetAnswerWaitSBI_DM"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.verificationType" expr="'question'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="AQ1105_NamesQuestion_DM.returnvalue"/>
          <action next="AQ1134_Authenticate_DB_DA"/>
        </action>
        <action label="operator">
          <if cond="GlobalVars.callType == 'dsgExtension'">
            <action next="getReturnLink()"/>
            <elseif cond="GlobalVars.saidOperatorAQ1105 == false">
              <session-mapping key="GlobalVars.saidOperatorAQ1105" value="true" type="Boolean"/>
              <action next="AQ1105_NamesQuestion_DM"/>
            </elseif>
            <else>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AQ1105_ini_08" cond="securityQuestionCode == 'Q13' &amp;&amp; (callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt-segments>
                  <audiofile text="The security question is your childhood nickname " src="AQ1105_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_01" cond="securityQuestionCode == 'Q13' &amp;&amp; (callType != 'dsgExtension' &amp;&amp; callType != 'switch_lines')">
                <prompt-segments>
                  <audiofile text="I can also use the answer to your security question - your childhood nickname" src="AQ1105_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_09" cond="securityQuestionCode == 'Q10' &amp;&amp; (callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt-segments>
                  <audiofile text="The security question is the name of your first pet " src="AQ1105_ini_09.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_02" cond="securityQuestionCode == 'Q10' &amp;&amp; (callType != 'dsgExtension' &amp;&amp; callType != 'switch_lines')">
                <prompt-segments>
                  <audiofile text="I can also use the answer to your security question - the name of your first pet" src="AQ1105_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_10" cond="securityQuestionCode == 'SQ3' &amp;&amp; (callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt-segments>
                  <audiofile text="The security question is your pet's name " src="AQ1105_ini_10.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_03" cond="securityQuestionCode == 'SQ3' &amp;&amp; (callType != 'dsgExtension' &amp;&amp; callType != 'switch_lines')">
                <prompt-segments>
                  <audiofile text="I can also use the answer to your security question - your pet's name" src="AQ1105_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_11" cond="securityQuestionCode == 'SQ5' &amp;&amp; (callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt-segments>
                  <audiofile text="The security question is the name of your school's mascot " src="AQ1105_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_04" cond="securityQuestionCode == 'SQ5' &amp;&amp; (callType != 'dsgExtension' &amp;&amp; callType != 'switch_lines')">
                <prompt-segments>
                  <audiofile text="I can also use the answer to your security question - the name of your school's mascot" src="AQ1105_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_12" cond="securityQuestionCode == 'Q14' &amp;&amp; (callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt-segments>
                  <audiofile text="The security question is your sibling's middle name " src="AQ1105_ini_12.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_05" cond="securityQuestionCode == 'Q14' &amp;&amp; (callType != 'dsgExtension' &amp;&amp; callType != 'switch_lines')">
                <prompt-segments>
                  <audiofile text="I can also use the answer to your security question - your sibling's middle name" src="AQ1105_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_13" cond="securityQuestionCode == 'SQ2' &amp;&amp; (callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt-segments>
                  <audiofile text="The security question is your mother's maiden name  " src="AQ1105_ini_13.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_06" cond="securityQuestionCode == 'SQ2' &amp;&amp; (callType != 'dsgExtension' &amp;&amp; callType != 'switch_lines')">
                <prompt-segments>
                  <audiofile text="I can also use the answer to your security question - your mother's maiden name" src="AQ1105_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_ini_07">
                <prompt-segments>
                  <audiofile text="If you know the answer, say it now Or say 'wait a minute'" src="AQ1105_ini_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="AQ1105_NamesQuestion_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1105_rin_01">
                  <prompt-segments>
                    <audiofile text="Sorry, let's try one more time You can say or spell your answer" src="AQ1105_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="AQ1105_rin_02" cond="securityQuestionCode == 'Q13'">
                <prompt-segments>
                  <audiofile text="So, what's your childhood nickname?" src="AQ1105_rin_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_rin_03" cond="securityQuestionCode == 'Q10'">
                <prompt-segments>
                  <audiofile text="So, what's the name of your first pet?" src="AQ1105_rin_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_rin_04" cond="securityQuestionCode == 'SQ3'">
                <prompt-segments>
                  <audiofile text="So, what's your pet name?" src="AQ1105_rin_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_rin_05" cond="securityQuestionCode == 'SQ5'">
                <prompt-segments>
                  <audiofile text="So, what's the name of your school's mascot?" src="AQ1105_rin_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_rin_06" cond="securityQuestionCode == 'Q14'">
                <prompt-segments>
                  <audiofile text="So, what's your sibling's middle name?" src="AQ1105_rin_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_rin_07" cond="securityQuestionCode == 'SQ2'">
                <prompt-segments>
                  <audiofile text="So, what's your mother's maiden name?" src="AQ1105_rin_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AQ1105_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry please say the answer to your security question now You can also spell it or say 'wait a minute'" src="AQ1105_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AQ1105_ni2_01">
                <prompt-segments>
                  <audiofile text="Please say the answer to your security question now You can also spell it or say 'wait a minute'" src="AQ1105_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="AQ1105_nm1_01">
                <prompt-segments>
                  <audiofile text="I'm sorry, that's not the answer I have Please try again If you want, you can also spell it" src="AQ1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="AQ1105_nm2_01">
                <prompt-segments>
                  <audiofile text="That doesn't match my records either Please try one more time" src="AQ1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1105_rin_01">
                  <prompt-segments>
                    <audiofile text="Sorry, let's try one more time You can say or spell your answer" src="AQ1105_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="AQ1105_rin_02" cond="securityQuestionCode == 'Q13'">
                <prompt-segments>
                  <audiofile text="So, what's your childhood nickname?" src="AQ1105_rin_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_rin_03" cond="securityQuestionCode == 'Q10'">
                <prompt-segments>
                  <audiofile text="So, what's the name of your first pet?" src="AQ1105_rin_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_rin_04" cond="securityQuestionCode == 'SQ3'">
                <prompt-segments>
                  <audiofile text="So, what's your pet name?" src="AQ1105_rin_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_rin_05" cond="securityQuestionCode == 'SQ5'">
                <prompt-segments>
                  <audiofile text="So, what's the name of your school's mascot?" src="AQ1105_rin_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_rin_06" cond="securityQuestionCode == 'Q14'">
                <prompt-segments>
                  <audiofile text="So, what's your sibling's middle name?" src="AQ1105_rin_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1105_rin_07" cond="securityQuestionCode == 'SQ2'">
                <prompt-segments>
                  <audiofile text="So, what's your mother's maiden name?" src="AQ1105_rin_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="SQ_collection.jsp" count="1">
            <param name="securityInfo" value="securityInfoVXMLVar"/>
          </grammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AQ1110_StreetQuestion_DM" type="CUST">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorAQ1110?GlobalVars.saidOperatorAQ1110:false" type="String"/>
      <session-mapping key="securityInfo" value="" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="securityQuestionCode" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityQuestionCode:''" type="String"/>
      <session-mapping key="securityAnswer" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityAnswer:''" type="String"/>
      <session-mapping key="collection_maxnoinputs" value="GlobalVars.smsReceived?1:2" type="String"/>
      <session-mapping key="collection_maxnomatches" value="GlobalVars.smsReceived?1:2" type="String"/>
      <session-mapping key="smsReceived" value="GlobalVars.smsReceived" type="String"/>
      <session-mapping key="securityInfo" expr="securityAnswer + ';' + securityQuestionCode + ';' + language"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorAQ1110" value="false" type="Boolean"/>
        <action label="wait">
          <action next="AQ1130_GetAnswerWaitSBI_DM"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.verificationType" expr="'question'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="AQ1110_StreetQuestion_DM.returnvalue"/>
          <action next="AQ1134_Authenticate_DB_DA"/>
        </action>
        <action label="operator">
          <if cond="GlobalVars.callType == 'dsgExtension'">
            <action next="getReturnLink()"/>
            <elseif cond="GlobalVars.saidOperatorAQ1110 == false">
              <session-mapping key="GlobalVars.saidOperatorAQ1110" value="true" type="Boolean"/>
              <action next="AQ1110_StreetQuestion_DM"/>
            </elseif>
            <else>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="(callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt id="AQ1110_ini_03">
                  <prompt-segments>
                    <audiofile text="The security question is the street you grew up on" src="AQ1110_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AQ1110_ini_01">
                    <prompt-segments>
                      <audiofile text="I can also use the answer to your security question - the street you grew up on" src="AQ1110_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1110_ini_02">
                <prompt-segments>
                  <audiofile text="If you know the answer, say it now Or say 'wait a minute'" src="AQ1110_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="AQ1110_StreetQuestion_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1110_rin_01">
                  <prompt-segments>
                    <audiofile text="Sorry, let's try one more time You can say or spell your answer" src="AQ1110_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="AQ1110_rin_02">
                <prompt-segments>
                  <audiofile text="So, what's the street you grew up on?" src="AQ1110_rin_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AQ1110_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry please say the answer to your security question now You can also spell it or say 'wait a minute'" src="AQ1110_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AQ1110_ni2_01">
                <prompt-segments>
                  <audiofile text="Please say the answer to your security question now You can also spell it or say 'wait a minute'" src="AQ1110_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="AQ1110_nm1_01">
                <prompt-segments>
                  <audiofile text="I'm sorry, that's not the answer I have Please try again If you want, you can also spell it" src="AQ1110_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="AQ1110_nm2_01">
                <prompt-segments>
                  <audiofile text="That doesn't match my records either Please try one more time" src="AQ1110_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1110_rin_01">
                  <prompt-segments>
                    <audiofile text="Sorry, let's try one more time You can say or spell your answer" src="AQ1110_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="AQ1110_rin_02">
                <prompt-segments>
                  <audiofile text="So, what's the street you grew up on?" src="AQ1110_rin_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="SQ_collection.jsp" count="1">
            <param name="securityInfo" value="securityInfoVXMLVar"/>
          </grammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AQ1115_SchoolQuestion_DM" type="CUST">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorAQ1115?GlobalVars.saidOperatorAQ1115:false" type="String"/>
      <session-mapping key="securityInfo" value="" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="securityQuestionCode" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityQuestionCode:''" type="String"/>
      <session-mapping key="securityAnswer" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityAnswer:''" type="String"/>
      <session-mapping key="collection_maxnoinputs" value="GlobalVars.smsReceived?1:2" type="String"/>
      <session-mapping key="collection_maxnomatches" value="GlobalVars.smsReceived?1:2" type="String"/>
      <session-mapping key="smsReceived" value="GlobalVars.smsReceived" type="String"/>
      <session-mapping key="securityInfo" expr="securityAnswer + ';' + securityQuestionCode + ';' + language"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorAQ1115" value="false" type="Boolean"/>
        <action label="wait">
          <action next="AQ1130_GetAnswerWaitSBI_DM"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.verificationType" expr="'question'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="AQ1115_SchoolQuestion_DM.returnvalue"/>
          <action next="AQ1134_Authenticate_DB_DA"/>
        </action>
        <action label="operator">
          <if cond="GlobalVars.callType == 'dsgExtension'">
            <action next="getReturnLink()"/>
            <elseif cond="GlobalVars.saidOperatorAQ1115 == false">
              <session-mapping key="GlobalVars.saidOperatorAQ1115" value="true" type="Boolean"/>
              <action next="AQ1115_SchoolQuestion_DM"/>
            </elseif>
            <else>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AQ1115_ini_04" cond="securityQuestionCode == 'Q11' &amp;&amp; (callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt-segments>
                  <audiofile text="The security question is your first elementary school" src="AQ1115_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1115_ini_01" cond="securityQuestionCode == 'Q11' &amp;&amp; (callType != 'dsgExtension' &amp;&amp; callType != 'switch_lines')">
                <prompt-segments>
                  <audiofile text="I can also use the answer to your security question - your first elementary school" src="AQ1115_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1115_ini_05" cond="securityQuestionCode == 'SQ4' &amp;&amp; (callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt-segments>
                  <audiofile text="The security question is your first school" src="AQ1115_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1115_ini_02" cond="securityQuestionCode == 'SQ4' &amp;&amp; (callType != 'dsgExtension' &amp;&amp; callType != 'switch_lines')">
                <prompt-segments>
                  <audiofile text="I can also use the answer to your security question - your first school" src="AQ1115_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1115_ini_03">
                <prompt-segments>
                  <audiofile text="If you know the answer, say it now Or say 'wait a minute'" src="AQ1115_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="AQ1115_SchoolQuestion_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1115_rin_01">
                  <prompt-segments>
                    <audiofile text="Sorry, let's try one more time You can say or spell your answer" src="AQ1115_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="AQ1115_rin_02">
                <prompt-segments>
                  <audiofile text="So, what's the name of your first school?" src="AQ1115_rin_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AQ1115_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry please say the answer to your security question now You can also spell it or say 'wait a minute'" src="AQ1115_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AQ1115_ni2_01">
                <prompt-segments>
                  <audiofile text="Please say the answer to your security question now You can also spell it or say 'wait a minute'" src="AQ1115_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="AQ1115_nm1_01">
                <prompt-segments>
                  <audiofile text="I'm sorry, that's not the answer I have Please try again If you want, you can also spell it" src="AQ1115_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="AQ1115_nm2_01">
                <prompt-segments>
                  <audiofile text="That doesn't match my records either Please try one more time" src="AQ1115_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1115_rin_01">
                  <prompt-segments>
                    <audiofile text="Sorry, let's try one more time You can say or spell your answer" src="AQ1115_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="AQ1115_rin_02">
                <prompt-segments>
                  <audiofile text="So, what's the name of your first school?" src="AQ1115_rin_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="SQ_collection.jsp" count="1">
            <param name="securityInfo" value="securityInfoVXMLVar"/>
          </grammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AQ1120_ColorQuestion_DM" type="CUST">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorAQ1120?GlobalVars.saidOperatorAQ1120:false" type="String"/>
      <session-mapping key="securityInfo" value="" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="securityQuestionCode" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityQuestionCode:''" type="String"/>
      <session-mapping key="securityAnswer" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityAnswer:''" type="String"/>
      <session-mapping key="collection_maxnoinputs" value="GlobalVars.smsReceived?1:2" type="String"/>
      <session-mapping key="collection_maxnomatches" value="GlobalVars.smsReceived?1:2" type="String"/>
      <session-mapping key="smsReceived" value="GlobalVars.smsReceived" type="String"/>
      <session-mapping key="securityInfo" expr="securityAnswer + ';' + securityQuestionCode + ';' + language"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorAQ1120" value="false" type="Boolean"/>
        <action label="wait">
          <action next="AQ1130_GetAnswerWaitSBI_DM"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.verificationType" expr="'question'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="AQ1120_ColorQuestion_DM.returnvalue"/>
          <action next="AQ1134_Authenticate_DB_DA"/>
        </action>
        <action label="operator">
          <if cond="GlobalVars.callType == 'dsgExtension'">
            <action next="getReturnLink()"/>
            <elseif cond="GlobalVars.saidOperatorAQ1120 == false">
              <session-mapping key="GlobalVars.saidOperatorAQ1120" value="true" type="Boolean"/>
              <action next="AQ1120_ColorQuestion_DM"/>
            </elseif>
            <else>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AQ1120_ini_03" cond="(callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt-segments>
                  <audiofile text="The security question is your favorite color " src="AQ1120_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1120_ini_01" cond="(callType != 'dsgExtension' &amp;&amp; callType != 'switch_lines')">
                <prompt-segments>
                  <audiofile text="I can also use the answer to your security question - your favorite color" src="AQ1120_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1120_ini_02">
                <prompt-segments>
                  <audiofile text="If you know the answer, say it now Or say 'wait a minute'" src="AQ1120_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="AQ1120_ColorQuestion_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1120_rin_01">
                  <prompt-segments>
                    <audiofile text="Sorry, let's try one more time You can say or spell your answer" src="AQ1120_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="AQ1120_rin_02">
                <prompt-segments>
                  <audiofile text="So, what's your favorite color?" src="AQ1120_rin_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AQ1120_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry please say the answer to your security question now You can also spell it or say 'wait a minute'" src="AQ1120_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AQ1120_ni2_01">
                <prompt-segments>
                  <audiofile text="Please say the answer to your security question now You can also spell it or say 'wait a minute'" src="AQ1120_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="AQ1120_nm1_01">
                <prompt-segments>
                  <audiofile text="I'm sorry, that's not the answer I have Please try again If you want, you can also spell it" src="AQ1120_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="AQ1120_nm2_01">
                <prompt-segments>
                  <audiofile text="That doesn't match my records either Please try one more time" src="AQ1120_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1120_rin_01">
                  <prompt-segments>
                    <audiofile text="Sorry, let's try one more time You can say or spell your answer" src="AQ1120_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="AQ1120_rin_02">
                <prompt-segments>
                  <audiofile text="So, what's your favorite color?" src="AQ1120_rin_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="SQ_collection.jsp" count="1">
            <param name="securityInfo" value="securityInfoVXMLVar"/>
          </grammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AQ1125_CityAndMiscQuestion_DM" type="CUST">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorAQ1125?GlobalVars.saidOperatorAQ1125:false" type="String"/>
      <session-mapping key="securityInfo" value="" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="securityQuestionCode" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityQuestionCode:''" type="String"/>
      <session-mapping key="securityAnswer" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityAnswer:''" type="String"/>
      <session-mapping key="collection_maxnoinputs" value="GlobalVars.smsReceived?1:2" type="String"/>
      <session-mapping key="collection_maxnomatches" value="GlobalVars.smsReceived?1:2" type="String"/>
      <session-mapping key="smsReceived" value="GlobalVars.smsReceived" type="String"/>
      <session-mapping key="securityInfo" expr="securityAnswer + ';' + securityQuestionCode + ';' + language"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorAQ1125" value="false" type="Boolean"/>
        <action label="wait">
          <action next="AQ1130_GetAnswerWaitSBI_DM"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.verificationType" expr="'question'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="AQ1125_CityAndMiscQuestion_DM.returnvalue"/>
          <action next="AQ1134_Authenticate_DB_DA"/>
        </action>
        <action label="operator">
          <if cond="GlobalVars.callType == 'dsgExtension'">
            <action next="getReturnLink()"/>
            <elseif cond="GlobalVars.saidOperatorAQ1125 == false">
              <session-mapping key="GlobalVars.saidOperatorAQ1125" value="true" type="Boolean"/>
              <action next="AQ1125_CityAndMiscQuestion_DM"/>
            </elseif>
            <else>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AQ1125_ini_03" cond="securityQuestionCode == 'SQ1' &amp;&amp; (callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt-segments>
                  <audiofile text="The security question is the city you were born in" src="AQ1125_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1125_ini_01" cond="securityQuestionCode == 'SQ1' &amp;&amp; (callType != 'dsgExtension' &amp;&amp; callType != 'switch_lines')">
                <prompt-segments>
                  <audiofile text=" can also use the answer to your security question - the city you were born in" src="AQ1125_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1125_ini_06" cond="securityQuestionCode != 'SQ1' &amp;&amp; (callType == 'dsgExtension' || callType == 'switch_lines')">
                <prompt-segments>
                  <audiofile text="The security question is the one that was set up when this account was opened " src="AQ1125_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1125_ini_02" cond="securityQuestionCode != 'SQ1' &amp;&amp; (callType != 'dsgExtension' &amp;&amp; callType != 'switch_lines')">
                <prompt-segments>
                  <audiofile text="I can also use the answer to your security question, the one you set up when you opened your account" src="AQ1125_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1125_ini_04">
                <prompt-segments>
                  <audiofile text="If you know the answer, say it now Or say 'wait a minute'" src="AQ1125_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="AQ1125_CityAndMiscQuestion_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1125_rin_01">
                  <prompt-segments>
                    <audiofile text="Sorry, let's try one more time You can say or spell your answer" src="AQ1125_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="AQ1125_rin_03" cond="securityQuestionCode == 'SQ1'">
                <prompt-segments>
                  <audiofile text="So, what city were you born in?" src="AQ1125_rin_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1125_ini_05" cond="!(securityQuestionCode == 'SQ1')">
                <prompt-segments>
                  <audiofile text="So, what's your security answer?" src="AQ1125_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AQ1125_ni1_01">
                <prompt-segments>
                  <audiofile text="Sorry please say the answer to your security question now You can also spell it or say 'wait a minute'" src="AQ1125_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AQ1125_ni2_01">
                <prompt-segments>
                  <audiofile text="Please say the answer to your security question now You can also spell it or say 'wait a minute'" src="AQ1125_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="AQ1125_nm1_01">
                <prompt-segments>
                  <audiofile text="I'm sorry, that's not the answer I have Please try again If you want, you can also spell it" src="AQ1125_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="AQ1125_nm2_01">
                <prompt-segments>
                  <audiofile text="That doesn't match my records either Please try one more time" src="AQ1125_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1125_rin_01">
                  <prompt-segments>
                    <audiofile text="Sorry, let's try one more time You can say or spell your answer" src="AQ1125_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="AQ1125_rin_03" cond="securityQuestionCode == 'SQ1'">
                <prompt-segments>
                  <audiofile text="So, what city were you born in?" src="AQ1125_rin_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1125_ini_05" cond="!(securityQuestionCode == 'SQ1')">
                <prompt-segments>
                  <audiofile text="So, what's your security answer?" src="AQ1125_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="SQ_collection.jsp" count="1">
            <param name="securityInfo" value="securityInfoVXMLVar"/>
          </grammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AQ1130_GetAnswerWaitSBI_DM" type="CUST">
      <success>
        <action label="continue">
          <if cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ2')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ3')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ5')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q10')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q13')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q14'))">
            <action next="AQ1105_NamesQuestion_DM"/>
            <elseif cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ6')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q12'))">
              <action next="AQ1110_StreetQuestion_DM"/>
            </elseif>
            <elseif cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ4')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q11'))">
              <action next="AQ1115_SchoolQuestion_DM"/>
            </elseif>
            <elseif cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ7'))">
              <action next="AQ1120_ColorQuestion_DM"/>
            </elseif>
            <else>
              <action next="AQ1125_CityAndMiscQuestion_DM"/>
            </else>
          </if>
        </action>
        <action label="cant_find">
          <action next="AQ1530_MaxAttemptsRouting_JDA"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperator" value="true" type="Boolean"/>
          <action next="AQ1530_MaxAttemptsRouting_JDA"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AQ1130_ini_01">
                <prompt-segments>
                  <audiofile text="When you're ready with your answer, just say 'Continue'  When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2  At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2  If you're ready with your answer, say 'Continue' or press 1, or say 'I can't find it' or press 2  If you're ready,  say 'Continue' or press 1, or say 'I can't find it' or press 2  You can say 'Continue' or press 1, or 'I can't find it' or press 2  It looks like we're having some trouble" src="AQ1130_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AQ1130_GetAnswerWaitSBI_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AQ1130_ini_01">
                <prompt-segments>
                  <audiofile text="When you're ready with your answer, just say 'Continue'  When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2  At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2  If you're ready with your answer, say 'Continue' or press 1, or say 'I can't find it' or press 2  If you're ready,  say 'Continue' or press 1, or say 'I can't find it' or press 2  You can say 'Continue' or press 1, or 'I can't find it' or press 2  It looks like we're having some trouble" src="AQ1130_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AQ1130_ini_01">
                <prompt-segments>
                  <audiofile text="When you're ready with your answer, just say 'Continue'  When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2  At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2  If you're ready with your answer, say 'Continue' or press 1, or say 'I can't find it' or press 2  If you're ready,  say 'Continue' or press 1, or say 'I can't find it' or press 2  You can say 'Continue' or press 1, or 'I can't find it' or press 2  It looks like we're having some trouble" src="AQ1130_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="AQ1130_GetAnswerWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="AQ1130_GetAnswerWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="0ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="AQ1134_Authenticate_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="verificationType" value="GlobalVars.verificationType" type="String"/>
      <session-mapping key="verificationValue" value="GlobalVars.verificationValue" type="String"/>
      <data-access id="Authenticate" classname="com.nuance.metro.dataaccess.ValidatePinForAuthenticate">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="pin" mask="true"/>
          <input-variable name="accessToken" mask="false"/>
          <input-variable name="verificationType"/>
          <input-variable name="verificationValue" mask="true"/>
          <input-variable name="sessionId"/>
          <input-variable name="providerId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="JWTToken"/>
          <output-variable name="expiresIn"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="Authenticate.status == 'FAILURE'">
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          <gotodialog next="CallTransfer_Main_Dialog"/>
          <else>
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <action next="AQ1505_CheckAnswerValid_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="AQ1505_CheckAnswerValid_DS">
      <session-mapping key="securityQuestionAttempts" value="GlobalVars.securityQuestionAttempts" type="String"/>
      <if cond="(GlobalVars.Authenticate &amp;&amp; GlobalVars.Authenticate.status &amp;&amp; GlobalVars.Authenticate.status.toUpperCase() == 'SUCCESS')">
        <session-mapping key="GlobalVars.loggedIn" value="true" type="Boolean"/>
        <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'authenticated_by_security_question')"/>
        <action next="AQ1515_KnewAnswerBeforeSMS_JDA"/>
        <else>
          <session-mapping key="GlobalVars.securityQuestionAttempts" expr="(securityQuestionAttempts+1)"/>
          <action next="AQ1525_InvalidCheckAttempts_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="AQ1510_ValidCheckSMS_DS">
      <session-mapping key="GlobalVars.verificationType" expr="'TBD'"/>
      <if cond="(GlobalVars.smsReceived == true)">
        <action next="AQ1520_KnewAnswerAfterSMS_JDA"/>
        <else>
          <action next="AQ1515_KnewAnswerBeforeSMS_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="AQ1515_KnewAnswerBeforeSMS_DS">
      <action next="getReturnLink()"/>
    </decision-state>

    <decision-state id="AQ1520_KnewAnswerAfterSMS_DS">
      <action next="getReturnLink()"/>
    </decision-state>

    <decision-state id="AQ1525_InvalidCheckAttempts_DS">
      <session-mapping key="securityQuestionAttempts" value="GlobalVars.securityQuestionAttempts" type="String"/>
      <if cond="(securityQuestionAttempts == 3)">
        <action next="AQ1530_MaxAttemptsRouting_JDA"/>
        <else>
          <action next="AQ1605_PlayAnswerInvalid_PP"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="AQ1530_MaxAttemptsRouting_DS">
      <if cond="(GlobalVars.askSQEntry == 'EWallet')">
        <session-mapping key="GlobalVars.abandonEWalletAuth" value="true" type="Boolean"/>
        <else>
          <session-mapping key="GlobalVars.authFailure" value="true" type="Boolean"/>
        </else>
      </if>
      <session-mapping key="GlobalVars.playNoPINTransferPrompt" value="true" type="Boolean"/>
      <action next="getReturnLink()"/>
    </decision-state>

    <play-state id="AQ1605_PlayAnswerInvalid_PP">
      <session-mapping key="securityQuestionAttempts" value="GlobalVars.securityQuestionAttempts" type="String"/>
      <audio>
        <if cond="(securityQuestionAttempts == 1)">
          <prompt id="AQ1605_out_01">
            <prompt-segments>
              <audiofile text="I'm sorry, that's not the answer I have Please try again If you want, you can also spell it " src="AQ1605_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="AQ1605_out_02">
              <prompt-segments>
                <audiofile text="That doesn't match my records either Please try one more time " src="AQ1605_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AQ1610_ReturnToQuestion_JDA"/>
    </play-state>

    <decision-state id="AQ1610_ReturnToQuestion_DS">
      <if cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ2')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ3')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ5')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q10')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q13')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q14'))">
        <action next="AQ1105_NamesQuestion_DM"/>
        <elseif cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ6')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q12'))">
          <action next="AQ1110_StreetQuestion_DM"/>
        </elseif>
        <elseif cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ4')|| (GlobalVars.GetAccountDetails.securityQuestionCode == 'Q11'))">
          <action next="AQ1115_SchoolQuestion_DM"/>
        </elseif>
        <elseif cond="GlobalVars.GetAccountDetails&amp;&amp; ((GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ7'))">
          <action next="AQ1120_ColorQuestion_DM"/>
        </elseif>
        <else>
          <action next="AQ1125_CityAndMiscQuestion_DM"/>
        </else>
      </if>
    </decision-state>

  </dialog>
  