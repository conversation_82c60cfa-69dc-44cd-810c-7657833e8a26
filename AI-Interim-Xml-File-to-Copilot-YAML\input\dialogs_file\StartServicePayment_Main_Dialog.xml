<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="StartServicePayment_Main_Dialog">
    <decision-state id="SP1001_CheckContext_DS">
      <session-mapping key="payments_enable_prepaid_methods" value="GlobalVars.GetBCSParameters.payments_enable_prepaid_methods != undefined ? GlobalVars.GetBCSParameters.payments_enable_prepaid_methods : false" type="String"/>
      <if cond="(GlobalVars.suspendedRatePlanChange != true &amp;&amp; GlobalVars.dataTopUpPayment != true &amp;&amp; GlobalVars.acceptedBCR != true &amp;&amp; GlobalVars.paymentsEntryPoint != 'careESN')">
        <session-mapping key="GlobalVars.paymentAmount" expr="0.00"/>
      </if>
      <if cond="(payments_enable_prepaid_methods == true || payments_enable_prepaid_methods == 'true')">
        <session-mapping key="GlobalVars.offerPrepaid" value="true" type="Boolean"/>
        <else>
          <session-mapping key="GlobalVars.offerPrepaid" value="false" type="Boolean"/>
        </else>
      </if>
      <if cond="(GlobalVars.authorizationFailureHandling == 'systemError')">
        <session-mapping key="GlobalVars.paymentErrorCount" expr="1"/>
        <else>
          <session-mapping key="GlobalVars.paymentErrorCount" expr="0"/>
        </else>
      </if>
      <session-mapping key="GlobalVars.paymentErrorCount" expr="0"/>
      <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.bankCardNumber" expr="undefined"/>
      <session-mapping key="GlobalVars.bankCardDate" expr="undefined"/>
      <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
      <session-mapping key="GlobalVars.bankCardZip" expr="undefined"/>
      <session-mapping key="GlobalVars.cardStatus" expr="undefined"/>
      <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
      <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
      <session-mapping key="GlobalVars.failedChecksum" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.numberPINPayments" expr="0"/>
      <if cond="GlobalVars.payingWithPrepaid == true">
        <session-mapping key="GlobalVars.payingWithPrepaid" value="true" type="Boolean"/>
        <else>
          <session-mapping key="GlobalVars.payingWithPrepaid" value="false" type="Boolean"/>
        </else>
      </if>
      <if cond="GlobalVars.GetPaymentOptions == undefined">
        <action next="SP1005_GetWalletLastPayment_DB_DA"/>
        <else>
          <action next="SP1007_CheckNeedConvenieneFee_JDA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="SP1003_GetConvenienceFee_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="paymentType" value="service" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="GetConvenienceFee" classname="com.nuance.metro.dataaccess.GetConvenienceFee">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="paymentType"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="walletConvenienceFee"/>
          <output-variable name="cardConvenienceFee"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.GetConvenienceFee" expr="GetConvenienceFee"/>
        <action next="SP1010_CheckPaymentSettings_JDA"/>
      </action>
    </data-access-state>

    <data-access-state id="SP1005_GetWalletLastPayment_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="paymentType" value="service" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="GetWalletLastPayment" classname="com.nuance.metro.dataaccess.GetWalletLastPayment">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="JWTToken"/>
          <input-variable name="sessionID"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="madePaymentInLast24Hours"/>
          <output-variable name="isWalletPopulated"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.GetPaymentOptions" expr="GetWalletLastPayment"/>
        <session-mapping key="GlobalVars.GetPaymentOptions.hasEWallet" expr="GlobalVars.GetAccountDetails ? GlobalVars.GetAccountDetails.hasEWallet : false"/>
        <session-mapping key="GlobalVars.GetPaymentOptions.accountStatus" expr="GlobalVars.GetAccountDetails ? GlobalVars.GetAccountDetails.accountStatus : ''"/>
        <if cond="GlobalVars.GetPaymentOptions.status=='BLACKLISTED'">
          <if cond="GlobalVars.paymentsEntryPoint !='228Suspended'">
            <action next="SP1006_PaymentDataFailure_PP"/>
            <else>
              <action next="SP1007_CheckNeedConvenieneFee_JDA"/>
            </else>
          </if>
          <else>
            <if cond="(GlobalVars.GetPaymentOptions &amp;&amp; GlobalVars.GetPaymentOptions.isWalletPopulated == true)">
              <session-mapping key="GlobalVars.abandonEWalletAuth" value="false" type="Boolean"/>
            </if>
            <if cond="(GlobalVars.GetPaymentOptions &amp;&amp; GlobalVars.GetPaymentOptions.madePaymentInLast24Hours)">
              <session-mapping key="GlobalVars.lastPaymentAmount" expr="GlobalVars.GetPaymentOptions.lastPaymentAmount"/>
            </if>
            <action next="SP1007_CheckNeedConvenieneFee_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <play-state id="SP1006_PaymentDataFailure_PP">
      <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.skipGoodbye" value="true" type="Boolean"/>
      <audio>
        <prompt id="SP1006_out_01">
          <prompt-segments>
            <audiofile text="I m sorry, it looks like there s a problem with your account So you won t be able to pay over the phone today You can pay in cash at any Metro store, or authorized dealer For a map of our locations near you, please visit us at MetroPCScom We apologize for this inconvenience Goodbye " src="SP1006_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <decision-state id="SP1007_CheckNeedConvenieneFee_DS">
      <if cond="GlobalVars.GetConvenienceFee == undefined">
        <action next="SP1003_GetConvenienceFee_DB_DA"/>
        <else>
          <action next="SP1010_CheckPaymentSettings_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="SP1010_CheckPaymentSettings_DS">
      <session-mapping key="isAutopayEnabled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <if cond="GlobalVars.paymentsEntryPoint == 'careVoiceStore' || GlobalVars.callType == 'mdn_change' || GlobalVars.completedOnePaymentOnCall == true|| GlobalVars.suspendedRatePlanChange == true || GlobalVars.dataTopUpPayment == true || GlobalVars.guestPayment == true || GlobalVars.paymentsEntryPoint == 'careESN'">
        <action next="SP1201_ServicePaymentCheckContext_JDA"/>
        <elseif cond="(((isAutopayEnabled == true) &amp;&amp; (GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus != 'suspended')) || ((GlobalVars.GetPaymentOptions.madePaymentInLast24Hours == true) &amp;&amp; (GlobalVars.heardRecentPaymentInfoCare != true)))">
          <action next="SP1103_PlayExistingPaymentInfo_PP"/>
        </elseif>
        <else>
          <action next="SP1201_ServicePaymentCheckContext_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="SP1103_PlayExistingPaymentInfo_PP">
      <session-mapping key="acceptedBCR" value="GlobalVars.acceptedBCR" type="String"/>
      <session-mapping key="madePaymentInLast24Hours" value="GlobalVars.GetPaymentOptions.madePaymentInLast24Hours" type="String"/>
      <session-mapping key="lastPaymentAmount" value="GlobalVars.GetPaymentOptions.lastPaymentAmount" type="String"/>
      <session-mapping key="isAutopayEnabled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="balance" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails.payDateDay" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="parseFloat(GlobalVars.GetAccountDetails.dueImmediatelyAmount)" type="String"/>
      <session-mapping key="totalDueAmount" value="(parseFloat(balance) + dueImmediatelyAmount).toString()" type="String"/>
      <session-mapping key="skipBalance" value="GlobalVars.skipBalance" type="String"/>
      <audio>
        <if cond="acceptedBCR == true">
          <prompt id="SP1103_out_01">
            <prompt-segments>
              <audiofile text="Actually " src="SP1103_out_01.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <if cond="isAutopayEnabled  == true">
          <prompt id="SP1103_out_02" cond="(totalDueAmount == 0)">
            <prompt-segments>
              <audiofile text="I see your account's all paid up, and your next automatic payment " src="SP1103_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="SP1103_out_03" cond="(totalDueAmount &gt; 0)">
            <prompt-segments>
              <audiofile text="I see your automatic payment, for your balance of " src="SP1103_out_03.wav"/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="totalDueAmount" cond="(totalDueAmount &gt; 0)">
            <param name="runtime" value="ecmascript"/>
            <param value="m" name="intonation"/>
            <param value="false" name="playZeroCents"/>
          </prompt>
          <prompt id="due_today" cond="payDate == 'today'">
            <prompt-segments>
              <audiofile text=" is due today," src="due_today.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_1st" cond="payDate == '1'">
            <prompt-segments>
              <audiofile text=" is due on the first" src="due_1st.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_2nd" cond="payDate == '2'">
            <prompt-segments>
              <audiofile text=" is due on the 2nd" src="due_2nd.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_3rd" cond="payDate == '3'">
            <prompt-segments>
              <audiofile text=" is due on the 3rd" src="due_3rd.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_4th" cond="payDate == '4'">
            <prompt-segments>
              <audiofile text=" is due on the 4th" src="due_4th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_5th" cond="payDate == '5'">
            <prompt-segments>
              <audiofile text=" is due on the 5th" src="due_5th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_6th" cond="payDate == '6'">
            <prompt-segments>
              <audiofile text=" is due on the 6th" src="due_6th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_7th" cond="payDate == '7'">
            <prompt-segments>
              <audiofile text=" is due on the 7th" src="due_7th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_8th" cond="payDate == '8'">
            <prompt-segments>
              <audiofile text=" is due on the 8th" src="due_8th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_9th" cond="payDate == '9'">
            <prompt-segments>
              <audiofile text=" is due on the 9th" src="due_9th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_10th" cond="payDate == '10'">
            <prompt-segments>
              <audiofile text=" is due on the 10th" src="due_10th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_11th" cond="payDate == '11'">
            <prompt-segments>
              <audiofile text=" is due on the 11th" src="due_11th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_12th" cond="payDate == '12'">
            <prompt-segments>
              <audiofile text=" is due on the 12th" src="due_12th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_13th" cond="payDate == '13'">
            <prompt-segments>
              <audiofile text=" is due on the 13th" src="due_13th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_14th" cond="payDate == '14'">
            <prompt-segments>
              <audiofile text=" is due on the 14th" src="due_14th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_15th" cond="payDate == '15'">
            <prompt-segments>
              <audiofile text=" is due on the 15th" src="due_15th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_16th" cond="payDate == '16'">
            <prompt-segments>
              <audiofile text=" is due on the 16th" src="due_16th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_17th" cond="payDate == '17'">
            <prompt-segments>
              <audiofile text=" is due on the 17th" src="due_17th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_18th" cond="payDate == '18'">
            <prompt-segments>
              <audiofile text=" is due on the 18th" src="due_18th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_19th" cond="payDate == '19'">
            <prompt-segments>
              <audiofile text=" is due on the 19th" src="due_19th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_20th" cond="payDate == '20'">
            <prompt-segments>
              <audiofile text=" is due on the 20th" src="due_20th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_21st" cond="payDate == '21'">
            <prompt-segments>
              <audiofile text=" is due on the 21st" src="due_21st.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_22nd" cond="payDate == '22'">
            <prompt-segments>
              <audiofile text=" is due on the 22nd" src="due_22nd.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_23rd" cond="payDate == '23'">
            <prompt-segments>
              <audiofile text=" is due on the 23rd" src="due_23rd.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_24th" cond="payDate == '24'">
            <prompt-segments>
              <audiofile text=" is due on the 24th" src="due_24th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_25th" cond="payDate == '25'">
            <prompt-segments>
              <audiofile text=" is due on the 25th" src="due_25th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_26th" cond="payDate == '26'">
            <prompt-segments>
              <audiofile text=" is due on the 26th" src="due_26th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_27th" cond="payDate == '27'">
            <prompt-segments>
              <audiofile text=" is due on the 27th" src="due_27th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_28th" cond="payDate == '28'">
            <prompt-segments>
              <audiofile text=" is due on the 28th" src="due_28th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_29th" cond="payDate == '29'">
            <prompt-segments>
              <audiofile text=" is due on the 29th" src="due_29th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_30th" cond="payDate == '30'">
            <prompt-segments>
              <audiofile text=" is due on the 30th" src="due_30th.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="due_31st" cond="payDate == '31'">
            <prompt-segments>
              <audiofile text=" is due on the 31st" src="due_31st.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="madePaymentInLast24Hours == true">
            <prompt id="SP1103_out_04">
              <prompt-segments>
                <audiofile text="I see you recently made a payment, for " src="SP1103_out_04.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="lastPaymentAmount">
              <param name="runtime" value="ecmascript"/>
              <param value="f" name="intonation"/>
              <param value="false" name="playZeroCents"/>
            </prompt>
          </elseif>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SP1104_CheckOrigin_JDA"/>
    </play-state>

    <decision-state id="SP1104_CheckOrigin_DS">
      <if cond="GlobalVars.acceptedBCR == true">
        <session-mapping key="GlobalVars.bcrPendingPmtTransfer" value="true" type="Boolean"/>
        <action next="getReturnLink()"/>
        <else>
          <action next="SP1105_MakeExtraPaymentYN_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="SP1105_MakeExtraPaymentYN_DM" type="YSNO">
      <session-mapping key="isAutopayEnabled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="balance" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="parseFloat(GlobalVars.GetAccountDetails.dueImmediatelyAmount)" type="String"/>
      <session-mapping key="balanceAnddueImmediatelyAmount" value="(parseFloat(balance) + dueImmediatelyAmount).toString()" type="String"/>
      <session-mapping key="skipBalance" value="GlobalVars.skipBalance" type="String"/>
      <success>
        <action label="true">
          <if cond="GlobalVars.GetAccountDetails.isAutopayEnabled == true &amp;&amp; balanceAnddueImmediatelyAmount == 0">
            <session-mapping key="GlobalVars.skipBalance" value="true" type="Boolean"/>
          </if>
          <audio>
            <prompt id="SP1105_out_03">
              <prompt-segments>
                <audiofile text="alright" src="SP1105_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="SP1201_ServicePaymentCheckContext_JDA"/>
        </action>
        <action label="false">
          <if cond="GlobalVars.callType == 'make_pmt_auto_pay'">
            <audio>
              <prompt id="SP1105_out_04">
                <prompt-segments>
                  <audiofile text="Ok, please call back later to set up autopay" src="SP1105_out_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
            <action next="getReturnLink()"/>
            <elseif cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended'">
              <audio>
                <prompt id="SP1105_out_02">
                  <prompt-segments>
                    <audiofile text="No problem" src="SP1105_out_02.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
              <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
            </elseif>
            <else>
              <audio>
                <prompt id="SP1105_out_01">
                  <prompt-segments>
                    <audiofile text="Alright If you're done, you can just hang up Otherwise, I'll take you to the main menu" src="SP1105_out_01.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_200ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_200ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.callType" expr="undefined"/>
              <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
            </else>
          </if>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SP1105_ini_03">
                <prompt-segments>
                  <audiofile text="Do you want to make a payment on top of that?" src="SP1105_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="SP1105_MakeExtraPaymentYN_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SP1105_ini_03">
                <prompt-segments>
                  <audiofile text="Do you want to make a payment on top of that?" src="SP1105_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SP1105_nm1_02" cond="isAutopayEnabled == true">
                <prompt-segments>
                  <audiofile text="I see you have automatic payments set up" src="SP1105_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1105_nm1_01" cond="isAutopayEnabled != true">
                <prompt-segments>
                  <audiofile text="I see you already made a payment yesterday" src="SP1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1105_nm1_03">
                <prompt-segments>
                  <audiofile text="Did you want to make a payment on top of that" src="SP1105_nm1_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SP1105_nm2_02" cond="isAutopayEnabled == true">
                <prompt-segments>
                  <audiofile text="If you want to make a payment on top of the automatic payment, say 'yes' or press 1" src="SP1105_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1105_nm2_01" cond="isAutopayEnabled != true">
                <prompt-segments>
                  <audiofile text="If you want to make a payment on top of the payment you made yesterday, say 'yes' or press 1" src="SP1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1105_nm2_03">
                <prompt-segments>
                  <audiofile text="Otherwise, say 'no' or press 2" src="SP1105_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SP1105_nm3_01">
                <prompt-segments>
                  <audiofile text="To make an additional payment, press 1 Otherwise, press 2" src="SP1105_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1105_nm1_02" cond="isAutopayEnabled == true">
                <prompt-segments>
                  <audiofile text="I see you have automatic payments set up" src="SP1105_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1105_nm1_01" cond="isAutopayEnabled != true">
                <prompt-segments>
                  <audiofile text="I see you already made a payment yesterday" src="SP1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1105_nm1_03">
                <prompt-segments>
                  <audiofile text="Did you want to make a payment on top of that" src="SP1105_nm1_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1105_nm2_02" cond="isAutopayEnabled == true">
                <prompt-segments>
                  <audiofile text="If you want to make a payment on top of the automatic payment, say 'yes' or press 1" src="SP1105_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1105_nm2_01" cond="isAutopayEnabled != true">
                <prompt-segments>
                  <audiofile text="If you want to make a payment on top of the payment you made yesterday, say 'yes' or press 1" src="SP1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1105_nm2_03">
                <prompt-segments>
                  <audiofile text="Otherwise, say 'no' or press 2" src="SP1105_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1105_nm3_01">
                <prompt-segments>
                  <audiofile text="To make an additional payment, press 1 Otherwise, press 2" src="SP1105_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SP1105_ini_03">
                <prompt-segments>
                  <audiofile text="Do you want to make a payment on top of that?" src="SP1105_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="SP1105_MakeExtraPaymentYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="SP1105_MakeExtraPaymentYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="SP1201_ServicePaymentCheckContext_DS">
      <if cond="GlobalVars.dataTopUpPayment == 'true' || GlobalVars.dataTopUpPayment == true || GlobalVars.acceptedBCR == true">
        <action next="SP1210_PaymentIntroMethod_SD"/>
        <else>
          <action next="SP1202_IsEligibleForBCR_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="SP1202_IsEligibleForBCR_DS">
      <if cond="(GlobalVars.GetAccountDetails.eligibleForBillCycleReset == true &amp;&amp; GlobalVars.heardBCRTerms != true &amp;&amp; GlobalVars.guestPayment !=true &amp;&amp; GlobalVars.paymentsEntryPoint !='228Suspended' &amp;&amp; GlobalVars.suspendedRatePlanChange == false &amp;&amp; GlobalVars.paymentsEntryPoint != 'careESN')">
        <action next="SP1203_OfferBillCycleReset_SD"/>
        <else>
          <action next="SP1205_PaymentAmount_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="SP1203_OfferBillCycleReset_SD">
      <if cond="(GlobalVars.paymentsEntryPoint != undefined &amp;&amp; GlobalVars.paymentsEntryPoint.indexOf('228') != -1)">
        <gotodialog next="BillCycleReset_Main_Dialog"/>
        <else>
          <gotodialog next="BillCycleReset_Care_Dialog"/>
        </else>
      </if>
      <action next="SP1203_OfferBillCycleReset_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SP1203_OfferBillCycleReset_SD_return_CS">
      <action next="SP1205_PaymentAmount_SD"/>
    </custom-state>

    <subdialog-state id="SP1205_PaymentAmount_SD">
      <gotodialog next="PaymentAmount_Main_Dialog"/>
      <action next="SP1205_PaymentAmount_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SP1205_PaymentAmount_SD_return_CS">
      <if cond="GlobalVars.callType == 'goodbye' || GlobalVars.callType == 'extension'">
        <action next="getReturnLink()"/>
        <elseif cond="GlobalVars.payingWithPrepaid == true">
          <action next="SP1206_PrepaidCard_SD"/>
        </elseif>
        <else>
          <action next="SP1210_PaymentIntroMethod_SD"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="SP1206_PrepaidCard_SD">
      <gotodialog next="PrepaidCardPIN_Dialog"/>
      <action next="SP1206_PrepaidCard_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SP1206_PrepaidCard_SD_return_CS">
      <if cond="GlobalVars.callType == undefined">
        <action next="getReturnLink()"/>
        <elseif cond="GlobalVars.payingWithPrepaid == false">
          <action next="SP1205_PaymentAmount_SD"/>
        </elseif>
        <else>
          <action next="SP1304_WaitMessage_PP"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="SP1210_PaymentIntroMethod_SD">
      <gotodialog next="PaymentIntroMethod_Main_Dialog"/>
      <action next="SP1210_PaymentIntroMethod_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SP1210_PaymentIntroMethod_SD_return_CS">
      <if cond="(GlobalVars.authFailure == true) || (GlobalVars.callType == 'goodbye') || (GlobalVars.callType == undefined)">
        <action next="getReturnLink()"/>
        <elseif cond="GlobalVars.cardStatus == 'amount_under_min' || GlobalVars.cardStatus == 'amount_over_max' ">
          <action next="SP1205_PaymentAmount_SD"/>
        </elseif>
        <else>
          <action next="SP1304_WaitMessage_PP"/>
        </else>
      </if>
    </custom-state>

    <play-state id="SP1304_WaitMessage_PP">
      <session-mapping key="paymentErrorCount" value="GlobalVars.paymentErrorCount" type="String"/>
      <session-mapping key="payingWithPrepaid" value="GlobalVars.payingWithPrepaid != undefined ? GlobalVars.payingWithPrepaid : false" type="String"/>
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet ? GlobalVars.payingWithEWallet : false" type="String"/>
      <audio>
        <if cond="paymentErrorCount == 1">
          <prompt id="SP1304_out_02">
            <prompt-segments>
              <audiofile text="One second while I try again" src="SP1304_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="payingWithPrepaid == true">
            <prompt id="SP1304_out_03">
              <prompt-segments>
                <audiofile text="One second " src="SP1304_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="SP1304_out_01">
              <prompt-segments>
                <audiofile text="Great! One second" src="SP1304_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <session-mapping key="GlobalVars.authorizationFailureHandling" expr="undefined"/>
      <session-mapping key="GlobalVars.fromAnotherPaymentMenu" value="false" type="Boolean"/>
      <audio>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="(payingWithEWallet)">
        <action next="SP1307_AuthorizeByAuthenticated_DB_DA"/>
        <elseif cond="payingWithPrepaid == true">
          <action next="SP1308_RechargeByVoucher_DB_DA"/>
        </elseif>
        <else>
          <action next="SP1305_AuthorizeByAnonymous_DB_DA"/>
        </else>
      </if>
    </play-state>

    <data-access-state id="SP1305_AuthorizeByAnonymous_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="min" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="targetMdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount" type="String"/>
      <session-mapping key="accountNumber" value="GlobalVars.GetAccountDetails.accountNumber" type="String"/>
      <session-mapping key="preferredPaymentMethod" value="GlobalVars.preferredPaymentMethod" type="String"/>
      <session-mapping key="paymentMethod" value="" type="String"/>
      <session-mapping key="cardNumber" value="" type="String"/>
      <session-mapping key="expirationDate" value="" type="String"/>
      <session-mapping key="billingZip" value="" type="String"/>
      <session-mapping key="creditCardSecurityCode" value="" type="String"/>
      <session-mapping key="ACTIVATION_TYPE" value="PaymentTable.ACTIVATION_TYPE" type="String"/>
      <session-mapping key="ACTIVATION_MODE" value="PaymentTable.ACTIVATION_MODE" type="String"/>
      <session-mapping key="PAYMENT_STARTED_GMT" value="PaymentTable.ACTIVATION_STARTED" type="String"/>
      <session-mapping key="ESN" value="" type="String"/>
      <session-mapping key="DID" value="dnis" type="String"/>
      <session-mapping key="ANI" value="GlobalVars.trn" type="String"/>
      <session-mapping key="CITY" value="" type="String"/>
      <session-mapping key="STATE" value="" type="String"/>
      <session-mapping key="ZIP" value="GlobalVars.GetAccountDetails.zipCode" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="serialNumber" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.serialNumber:''" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <session-mapping key="cardNumber" expr="GlobalVars.bankCardNumber"/>
      <session-mapping key="expirationDate" expr="GlobalVars.bankCardDate"/>
      <session-mapping key="billingZip" expr="GlobalVars.bankCardZip"/>
      <session-mapping key="creditCardSecurityCode" expr="GlobalVars.bankCardCVV"/>
      <data-access id="AuthorizeByAnonymous" classname="com.nuance.metro.dataaccess.AuthorizeByAnonymous">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
          <input-variable name="paymentAmount"/>
          <input-variable name="paymentMethod"/>
          <input-variable name="paymentType"/>
          <input-variable name="targetMdn"/>
          <input-variable name="accountNumber" mask="true"/>
          <input-variable name="waiveConvFee"/>
          <input-variable name="cardNumber" mask="true"/>
          <input-variable name="expirationDate" mask="true"/>
          <input-variable name="preferredCardMethod"/>
          <input-variable name="billingZip" mask="true"/>
          <input-variable name="securityCode" mask="true"/>
          <input-variable name="creditCardSecurityCode" mask="true"/>
          <input-variable name="nameOnCard" mask="true"/>
          <input-variable name="languageCode"/>
          <input-variable name="serialNumber"/>
          <input-variable name="clearCache"/>
        </inputs>
        <outputs>
          <output-variable name="isAuthorized"/>
          <output-variable name="convenienceFee"/>
          <output-variable name="paymentId"/>
          <output-variable name="transactionId"/>
          <output-variable name="authorizationCode"/>
          <output-variable name="paymentAuthSource"/>
          <output-variable name="GetAccountDetails.pin" mask="true"/>
          <output-variable name="GetAccountDetails.securityAnswer" mask="true"/>
          <output-variable name="GetAccountDetails.accountNumber" mask="true"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.ProcessPayment" expr="AuthorizeByAnonymous"/>
        <session-mapping key="ProcessPayment" value="AuthorizeByAnonymous" type="String"/>
        <if cond="(ProcessPayment &amp;&amp; ProcessPayment.status &amp;&amp; ProcessPayment.status.toUpperCase() == 'SUCCESS')">
          <session-mapping key="paymentVars.eventTypeGMT" expr="getEventTime()"/>
          <session-mapping key="paymentVars.status" expr="'success'"/>
          <session-mapping key="paymentVars.eventType" expr="'Payment'"/>
          <session-mapping key="GlobalVars.recentPayment" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.isAuthorized" expr="ProcessPayment.isAuthorized"/>
          <session-mapping key="GlobalVars.isPosted" expr="ProcessPayment.isPosted"/>
          <session-mapping key="GlobalVars.resultCode" expr="ProcessPayment.resultCode"/>
          <session-mapping key="GlobalVars.confirmationNumber" expr="ProcessPayment.confirmationNumber"/>
          <session-mapping key="GlobalVars.transactionId" expr="ProcessPayment.transactionId"/>
          <session-mapping key="GlobalVars.authorizationCode" expr="ProcessPayment.authorizationCode"/>
          <if cond="ProcessPayment &amp;&amp; ProcessPayment.GetAccountDetails">
            <session-mapping key="GlobalVars.GetAccountDetails" expr="ProcessPayment.GetAccountDetails"/>
          </if>
          <elseif cond="(ProcessPayment &amp;&amp; ProcessPayment.status &amp;&amp; ProcessPayment.status == 'PENDING')">
            <session-mapping key="GlobalVars.authorizationFailureHandling" expr="'waitPrompting'"/>
            <session-mapping key="paymentVars.eventTypeGMT" expr="getEventTime()"/>
            <session-mapping key="paymentVars.status" expr="'failure'"/>
            <session-mapping key="paymentVars.eventType" expr="'Payment'"/>
          </elseif>
          <else>
            <session-mapping key="paymentVars.eventTypeGMT" expr="getEventTime()"/>
            <session-mapping key="paymentVars.status" expr="'failure'"/>
            <session-mapping key="paymentVars.eventType" expr="'Payment'"/>
          </else>
        </if>
        <action next="SP1306_CheckAuthorizationResult_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="SP1306_CheckAuthorizationResult_DS">
      <session-mapping key="PaymentTable.CARD_TYPE" expr="'not_set'"/>
      <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="''"/>
      <if cond="(GlobalVars.ProcessPayment &amp;&amp; GlobalVars.ProcessPayment.status &amp;&amp; GlobalVars.ProcessPayment.status.toUpperCase() == 'SUCCESS')&amp;&amp; ((GlobalVars.preferredPaymentMethod == 'credit' || GlobalVars.preferredPaymentMethod == 'debit') &amp;&amp;(GlobalVars.isAuthorized == true))">
        <session-mapping key="GlobalVars.completedOnePaymentOnCall" value="true" type="Boolean"/>
        <action next="SP1310_PlaySuccessConfirmation_PP"/>
        <elseif cond="GlobalVars.payingWithPrepaid == true &amp;&amp; GlobalVars.reChargeStatus == 'Y'">
          <action next="SP1310_PlaySuccessConfirmation_PP"/>
        </elseif>
        <else>
          <if cond="(GlobalVars.ProcessPayment &amp;&amp; GlobalVars.ProcessPayment.authorizationFailureHandling) &amp;&amp; (GlobalVars.preferredPaymentMethod != 'metro')">
            <session-mapping key="GlobalVars.authorizationFailureHandling" expr="GlobalVars.ProcessPayment.authorizationFailureHandling"/>
            <else>
              <session-mapping key="GlobalVars.authorizationFailureHandling" expr="'genericXfer'"/>
            </else>
          </if>
          <action next="SP1325_AuthorizationErrors_SD"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="SP1307_AuthorizeByAuthenticated_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="min" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="targetMdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount" type="String"/>
      <session-mapping key="accountNumber" value="GlobalVars.GetAccountDetails.accountNumber" type="String"/>
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet ? GlobalVars.payingWithEWallet : false" type="String"/>
      <session-mapping key="preferredPaymentMethod" value="GlobalVars.preferredPaymentMethod" type="String"/>
      <session-mapping key="paymentMethod" value="" type="String"/>
      <session-mapping key="paymentOptionId" value="" type="String"/>
      <session-mapping key="cardNumber" value="" type="String"/>
      <session-mapping key="expirationDate" value="" type="String"/>
      <session-mapping key="billingZip" value="" type="String"/>
      <session-mapping key="creditCardSecurityCode" value="" type="String"/>
      <session-mapping key="ACTIVATION_TYPE" value="PaymentTable.ACTIVATION_TYPE" type="String"/>
      <session-mapping key="ACTIVATION_MODE" value="PaymentTable.ACTIVATION_MODE" type="String"/>
      <session-mapping key="PAYMENT_STARTED_GMT" value="PaymentTable.ACTIVATION_STARTED" type="String"/>
      <session-mapping key="ESN" value="" type="String"/>
      <session-mapping key="DID" value="dnis" type="String"/>
      <session-mapping key="ANI" value="GlobalVars.trn" type="String"/>
      <session-mapping key="CITY" value="" type="String"/>
      <session-mapping key="STATE" value="" type="String"/>
      <session-mapping key="ZIP" value="GlobalVars.GetAccountDetails.zipCode" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="serialNumber" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.serialNumber:''" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <if cond="payingWithEWallet">
        <session-mapping key="paymentOptionId" expr="GlobalVars.paymentOptionId"/>
        <elseif cond="preferredPaymentMethod == 'credit' || preferredPaymentMethod == 'debit'">
          <session-mapping key="cardNumber" expr="GlobalVars.bankCardNumber"/>
          <session-mapping key="expirationDate" expr="GlobalVars.bankCardDate"/>
          <session-mapping key="billingZip" expr="GlobalVars.bankCardZip"/>
          <session-mapping key="creditCardSecurityCode" expr="GlobalVars.bankCardCVV"/>
        </elseif>
      </if>
      <data-access id="AuthorizeByAuthenticated" classname="com.nuance.metro.dataaccess.AuthorizeByAuthenticated">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
          <input-variable name="JWTToken"/>
          <input-variable name="paymentMethod"/>
          <input-variable name="paymentAmount"/>
          <input-variable name="paymentType"/>
          <input-variable name="targetMdn"/>
          <input-variable name="accountNumber" mask="true"/>
          <input-variable name="languageCode"/>
          <input-variable name="serialNumber"/>
          <input-variable name="clearCache"/>
        </inputs>
        <outputs>
          <output-variable name="isAuthorized"/>
          <output-variable name="convenienceFee"/>
          <output-variable name="paymentId"/>
          <output-variable name="transactionId"/>
          <output-variable name="authorizationCode"/>
          <output-variable name="paymentAuthSource"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.ProcessPayment" expr="AuthorizeByAuthenticated"/>
        <session-mapping key="ProcessPayment" value="AuthorizeByAuthenticated" type="String"/>
        <if cond="(ProcessPayment &amp;&amp; ProcessPayment.status &amp;&amp; ProcessPayment.status == 'SUCCESS')">
          <session-mapping key="paymentVars.eventTypeGMT" expr="getEventTime()"/>
          <session-mapping key="paymentVars.status" expr="'success'"/>
          <session-mapping key="paymentVars.eventType" expr="'Payment'"/>
          <session-mapping key="GlobalVars.isAuthorized" expr="ProcessPayment.isAuthorized"/>
          <session-mapping key="GlobalVars.isPosted" expr="ProcessPayment.isPosted"/>
          <session-mapping key="GlobalVars.resultCode" expr="ProcessPayment.resultCode"/>
          <session-mapping key="GlobalVars.confirmationNumber" expr="ProcessPayment.confirmationNumber"/>
          <session-mapping key="GlobalVars.transactionId" expr="ProcessPayment.transactionId"/>
          <session-mapping key="GlobalVars.authorizationCode" expr="ProcessPayment.authorizationCode"/>
          <elseif cond="(ProcessPayment &amp;&amp; ProcessPayment.status &amp;&amp; ProcessPayment.status == 'PENDING')">
            <session-mapping key="GlobalVars.authorizationFailureHandling" expr="'waitPrompting'"/>
            <session-mapping key="paymentVars.eventTypeGMT" expr="getEventTime()"/>
            <session-mapping key="paymentVars.status" expr="'failure'"/>
            <session-mapping key="paymentVars.eventType" expr="'Payment'"/>
          </elseif>
          <else>
            <session-mapping key="paymentVars.eventTypeGMT" expr="getEventTime()"/>
            <session-mapping key="paymentVars.status" expr="'failure'"/>
            <session-mapping key="paymentVars.eventType" expr="'Payment'"/>
          </else>
        </if>
        <action next="SP1306_CheckAuthorizationResult_JDA"/>
      </action>
    </data-access-state>

    <play-state id="SP1310_PlaySuccessConfirmation_PP">
      <session-mapping key="guestPayment" value="GlobalVars.guestPayment" type="String"/>
      <session-mapping key="isSuspended" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" type="String"/>
      <audio>
        <if type="java">
          <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
            <param name="numToMatch" value="1"/>
            <param name="startNum" value="1"/>
            <param name="endNum" value="3"/>
          </condition>
          <prompt id="SP1310_out_01">
            <prompt-segments>
              <audiofile text="Your payment went through!" src="SP1310_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <elseif>
            <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
              <param name="numToMatch" value="2"/>
              <param name="startNum" value="1"/>
              <param name="endNum" value="3"/>
            </condition>
            <prompt id="SP1310_out_01_02">
              <prompt-segments>
                <audiofile text="Your payment is in!" src="SP1310_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="SP1310_out_01_03">
              <prompt-segments>
                <audiofile text="We got your payment!" src="SP1310_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="SP1310_out_05" cond="isSuspended == true">
          <prompt-segments>
            <audiofile text="Please be aware that making a payment after the due date  does not reset  the payment due date for future payments" src="SP1310_out_05.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SP1309_GetAccountDetails_DB_DA"/>
    </play-state>

    <play-state id="SP1313_PlayBalanceUpdate_PP">
      <session-mapping key="guestPayment" value="GlobalVars.guestPayment" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="transactionId" value="" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="arBalance" value="GlobalVars.GetAccountDetails.arBalance" type="String"/>
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount" type="String"/>
      <session-mapping key="dueImmediatelyAmountPlay" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="amountDue" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails.payDate" type="String"/>
      <session-mapping key="arBalanceCredit" value="(Math.abs(arBalance)).toString()" type="String"/>
      <session-mapping key="isAutopayEligPlanExists" value="GlobalVars.GetAccountDetails.isAutopayEligPlanExists" type="String"/>
      <session-mapping key="isAutopayEnabled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="payingWithPrepaid" value="GlobalVars.payingWithPrepaid != undefined ? GlobalVars.payingWithPrepaid : false" type="String"/>
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet != undefined ? GlobalVars.payingWithEWallet : false" type="String"/>
      <session-mapping key="numDaysBtwPayAndCurrentDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String"/>
      <if cond="GlobalVars.preferredPaymentMethod == 'credit' || GlobalVars.preferredPaymentMethod == 'debit' || GlobalVars.reChargeStatus == 'Y'">
        <session-mapping key="transactionId" expr="GlobalVars.confirmationNumber"/>
        <else>
          <session-mapping key="transactionId" expr="GlobalVars.incommConfNumber"/>
        </else>
      </if>
      <audio>
        <prompt id="SP1313_out_01">
          <prompt-segments>
            <audiofile text="Your confirmation number is " src="SP1313_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="alphanum" expr="transactionId" cond="(transactionId != undefined) &amp;&amp; (transactionId != null) &amp;&amp; (transactionId != '') ">
          <param value="f" name="intonation"/>
        </prompt>
        <if cond="parseFloat(dueImmediatelyAmount) &gt; 3">
          <prompt id="SP1313_out_11" cond="callType == 'make_pmt_auto_pay'">
            <prompt-segments>
              <audiofile text="Since you made a partial payment, autopay cannot be set up yet  A payment of " src="SP1313_out_11.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="SP1313_out_05" cond="callType != 'make_pmt_auto_pay'">
            <prompt-segments>
              <audiofile text="Since you made a partial payment, your account is still suspended  So a payment of " src="SP1313_out_05.wav"/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="dueImmediatelyAmount" cond=" parseFloat(dueImmediatelyAmount) &gt; 0">
            <param value="f" name="intonation"/>
            <param value="false" name="playZeroCents"/>
          </prompt>
          <prompt id="SP1313_out_10">
            <prompt-segments>
              <audiofile text="is still needed " src="SP1313_out_10.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="guestPayment == true || guestPayment == 'true'">
            <prompt id="SP1313_out_02">
              <prompt-segments>
                <audiofile text="Again, if this account is suspended, you'll need to pay the full amount due to reactivate it So please check if this payment covered it! " src="SP1313_out_02.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="SP1313_out_03" cond="dueImmediatelyAmount &gt; 0">
              <prompt-segments>
                <audiofile text="Your new amount due now is  " src="SP1313_out_03.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="dueImmediatelyAmountPlay" cond="dueImmediatelyAmount &gt; 0">
              <param value="false" name="playZeroCents"/>
              <param name="intonation" value="f"/>
            </prompt>
            <prompt id="SP1313_out_04" cond="parseFloat(arBalance) &lt; 0">
              <prompt-segments>
                <audiofile text="For your next payment cycle, you'll have a credit of " src="SP1313_out_04.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="arBalanceCredit" cond="parseFloat(arBalance) &lt; 0">
              <param value="f" name="intonation"/>
              <param value="false" name="playZeroCents"/>
            </prompt>
            <prompt id="SP1313_out_06" cond="(parseFloat(arBalance) &lt; 0) &amp;&amp; (parseFloat(amountDue) &gt; 0)">
              <prompt-segments>
                <audiofile text="SO " src="SP1313_out_06.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="SP1313_out_11" cond="((parseFloat(amountDue) &gt; 0)  &amp;&amp; (callType == 'make_pmt_auto_pay' &amp;&amp; numDaysBtwPayAndCurrentDate &lt; 7))">
              <prompt-segments>
                <audiofile text="Since you made a partial payment, autopay cannot be set up yet  A payment of " src="SP1313_out_11.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="amountDue" cond="((parseFloat(amountDue) &gt; 0)  &amp;&amp; (callType == 'make_pmt_auto_pay' &amp;&amp; numDaysBtwPayAndCurrentDate &lt; 7))">
              <param value="f" name="intonation"/>
              <param value="false" name="playZeroCents"/>
            </prompt>
            <prompt id="SP1313_out_10" cond="((parseFloat(amountDue) &gt; 0)  &amp;&amp; (callType == 'make_pmt_auto_pay' &amp;&amp; numDaysBtwPayAndCurrentDate &lt; 7))">
              <prompt-segments>
                <audiofile text="is still needed " src="SP1313_out_10.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="SP1313_out_07" cond=" parseFloat(amountDue) &gt; 0">
              <prompt-segments>
                <audiofile text="Your new balance is " src="SP1313_out_07.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="amountDue" cond=" parseFloat(amountDue) &gt; 0">
              <param value="f" name="intonation"/>
              <param value="false" name="playZeroCents"/>
            </prompt>
            <prompt id="SP1313_out_09" cond=" parseFloat(amountDue) &gt; 0">
              <prompt-segments>
                <audiofile text="It's due  " src="SP1313_out_09.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="date" expr="payDate" cond=" parseFloat(amountDue) &gt; 0">
              <param name="dateFormat" value="MMM dd, yyyy"/>
              <param name="playDayOfMonth" value="true"/>
              <param name="playYear" value="false"/>
              <param name="playDayOfTheWeek" value="false"/>
              <param name="intonation" value="f"/>
            </prompt>
          </else>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="isAutopayEnabled == false &amp;&amp; isAutopayEligPlanExists == true">
        <action next="SP1314_RepeatOtherPayment_DM"/>
        <elseif cond="payingWithEWallet == false &amp;&amp; payingWithPrepaid == false">
          <action next="SP1316_AskToSaveCard_DM"/>
        </elseif>
        <else>
          <action next="SP1314_RepeatOtherPayment_DM"/>
        </else>
      </if>
    </play-state>

    <dm-state id="SP1314_RepeatOtherPayment_DM" type="CUST">
      <session-mapping key="guestPayment" value="GlobalVars.guestPayment" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="payments_enable_prepaid_methods" value="GlobalVars.GetBCSParameters.payments_enable_prepaid_methods" type="String"/>
      <session-mapping key="amountDue" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="isAutopayEnabled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="isAutopayEnrolled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="isAutopayEligPlanExists" value="GlobalVars.GetAccountDetails.isAutopayEligPlanExists" type="String"/>
      <session-mapping key="payingWithPrepaid" value="GlobalVars.payingWithPrepaid != undefined ? GlobalVars.payingWithPrepaid : false" type="String"/>
      <success>
        <action label="repeat">
          <audio>
            <prompt id="SP1314_out_01">
              <prompt-segments>
                <audiofile text="Sure" src="SP1314_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="SP1313_PlayBalanceUpdate_PP"/>
        </action>
        <action label="make-payment">
          <if cond="(guestPayment == false &amp;&amp; (parseFloat(GlobalVars.GetAccountDetails.dueImmediatelyAmount) &gt;3))">
            <session-mapping key="GlobalVars.paymentAmount" expr="GlobalVars.GetAccountDetails.dueImmediatelyAmount"/>
            <else>
              <session-mapping key="GlobalVars.paymentAmount" expr="0.00"/>
            </else>
          </if>
          <if cond="payments_enable_prepaid_methods == true || payments_enable_prepaid_methods == 'true'">
            <session-mapping key="GlobalVars.offerPrepaid" value="true" type="Boolean"/>
          </if>
          <session-mapping key="GlobalVars.paymentErrorCount" expr="0"/>
          <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardNumber" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardDate" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardZip" expr="undefined"/>
          <session-mapping key="GlobalVars.cardStatus" expr="undefined"/>
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
          <session-mapping key="GlobalVars.failedChecksum" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.payingWithPrepaid" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.fromAnotherPaymentMenu" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.skipBalance" value="false" type="Boolean"/>
          <action next="SP1205_PaymentAmount_SD"/>
        </action>
        <action label="done">
          <if cond="GlobalVars.callType == 'make_pmt_auto_pay'">
            <if cond="dueImmediatelyAmount &gt; 3 || amountDue &gt; 0">
              <session-mapping key="GlobalVars.callType" expr="undefined"/>
              <action next="SP1312_PostPaymentRouting_JDA"/>
              <else>
                <action next="SP1328_AutoPay_SD"/>
              </else>
            </if>
            <else>
              <session-mapping key="GlobalVars.callType" expr="undefined"/>
              <action next="SP1312_PostPaymentRouting_JDA"/>
            </else>
          </if>
        </action>
        <action label="setup-autopay">
          <session-mapping key="GlobalVars.tag" expr="'setup-autopay'"/>
          <action next="SP1328_AutoPay_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SP1314_ini_02" cond="(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that', otherwise to continue  to autopay set up say 'I'm done here'" src="SP1314_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_ini_03" cond="((!(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))&amp;&amp; (isAutopayEnabled == false)))">
                <prompt-segments>
                  <audiofile text="Say 'repeat that', 'make another payment', 'set up autopay', or  'I'm done'" src="SP1314_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_ini_01" cond="((!(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))&amp;&amp; (isAutopayEnabled == true)))">
                <prompt-segments>
                  <audiofile text="Say 'repeat that', 'make another payment', 'I'm done'" src="SP1314_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="SP1314_RepeatOtherPayment_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SP1314_ini_02" cond="(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that', otherwise to continue  to autopay set up say 'I'm done here'" src="SP1314_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_ini_03" cond="((!(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))&amp;&amp; (isAutopayEnabled == false)))">
                <prompt-segments>
                  <audiofile text="Say 'repeat that', 'make another payment', 'set up autopay', or  'I'm done'" src="SP1314_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_ini_01" cond="((!(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))&amp;&amp; (isAutopayEnabled == true)))">
                <prompt-segments>
                  <audiofile text="Say 'repeat that', 'make another payment', 'I'm done'" src="SP1314_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SP1314_ini_02" cond="(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that', otherwise to continue  to autopay set up say 'I'm done here'" src="SP1314_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_ini_03" cond="((!(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))&amp;&amp; (isAutopayEnabled == false)))">
                <prompt-segments>
                  <audiofile text="Say 'repeat that', 'make another payment', 'set up autopay', or  'I'm done'" src="SP1314_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_ini_01" cond="((!(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))&amp;&amp; (isAutopayEnabled == true)))">
                <prompt-segments>
                  <audiofile text="Say 'repeat that', 'make another payment', 'I'm done'" src="SP1314_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_01" cond="callType == 'make_pmt_auto_pay' &amp;&amp; (dueImmediatelyAmount &gt; 3 || (amountDue &gt; 0))">
                <prompt-segments>
                  <audiofile text="To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3" src="SP1314_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_02" cond="callType == 'make_pmt_auto_pay' &amp;&amp; !(dueImmediatelyAmount &gt; 3 || amountDue &gt; 0)">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that', or press 1  otherwise to continue  to autopay set up say 'I'm done here' or press 2" src="SP1314_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_03" cond="((callType != 'make_pmt_auto_pay') &amp;&amp; (isAutopayEnabled == true))">
                <prompt-segments>
                  <audiofile text="To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3" src="SP1314_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_04" cond="((callType != 'make_pmt_auto_pay') &amp;&amp; (isAutopayEnabled == false))">
                <prompt-segments>
                  <audiofile text="Please say  'repeat that' or press 1 'make another payment', or press 2  'set up autopay', or press 3  or  say 'I'm done'  or press 4" src="SP1314_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_01" cond="callType == 'make_pmt_auto_pay' &amp;&amp; (dueImmediatelyAmount &gt; 3 || (amountDue &gt; 0))">
                <prompt-segments>
                  <audiofile text="To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3" src="SP1314_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_02" cond="callType == 'make_pmt_auto_pay' &amp;&amp; !(dueImmediatelyAmount &gt; 3 || amountDue &gt; 0)">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that', or press 1  otherwise to continue  to autopay set up say 'I'm done here' or press 2" src="SP1314_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_03" cond="((callType != 'make_pmt_auto_pay') &amp;&amp; (isAutopayEnabled == true))">
                <prompt-segments>
                  <audiofile text="To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3" src="SP1314_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_04" cond="((callType != 'make_pmt_auto_pay') &amp;&amp; (isAutopayEnabled == false))">
                <prompt-segments>
                  <audiofile text="Please say  'repeat that' or press 1 'make another payment', or press 2  'set up autopay', or press 3  or  say 'I'm done'  or press 4" src="SP1314_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_ini_02" cond="(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that', otherwise to continue  to autopay set up say 'I'm done here'" src="SP1314_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_ini_03" cond="((!(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))&amp;&amp; (isAutopayEnabled == false)))">
                <prompt-segments>
                  <audiofile text="Say 'repeat that', 'make another payment', 'set up autopay', or  'I'm done'" src="SP1314_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_ini_01" cond="((!(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))&amp;&amp; (isAutopayEnabled == true)))">
                <prompt-segments>
                  <audiofile text="Say 'repeat that', 'make another payment', 'I'm done'" src="SP1314_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_01" cond="callType == 'make_pmt_auto_pay' &amp;&amp; (dueImmediatelyAmount &gt; 3 || (amountDue &gt; 0))">
                <prompt-segments>
                  <audiofile text="To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3" src="SP1314_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_02" cond="callType == 'make_pmt_auto_pay' &amp;&amp; !(dueImmediatelyAmount &gt; 3 || amountDue &gt; 0)">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that', or press 1  otherwise to continue  to autopay set up say 'I'm done here' or press 2" src="SP1314_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_03" cond="((callType != 'make_pmt_auto_pay') &amp;&amp; (isAutopayEnabled == true))">
                <prompt-segments>
                  <audiofile text="To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3" src="SP1314_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_04" cond="((callType != 'make_pmt_auto_pay') &amp;&amp; (isAutopayEnabled == false))">
                <prompt-segments>
                  <audiofile text="Please say  'repeat that' or press 1 'make another payment', or press 2  'set up autopay', or press 3  or  say 'I'm done'  or press 4" src="SP1314_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_01" cond="callType == 'make_pmt_auto_pay' &amp;&amp; (dueImmediatelyAmount &gt; 3 || (amountDue &gt; 0))">
                <prompt-segments>
                  <audiofile text="To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3" src="SP1314_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_02" cond="callType == 'make_pmt_auto_pay' &amp;&amp; !(dueImmediatelyAmount &gt; 3 || amountDue &gt; 0)">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that', or press 1  otherwise to continue  to autopay set up say 'I'm done here' or press 2" src="SP1314_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_03" cond="((callType != 'make_pmt_auto_pay') &amp;&amp; (isAutopayEnabled == true))">
                <prompt-segments>
                  <audiofile text="To hear your confirmation again, say 'repeat' or press 1 To make another payment, say 'another payment' or press 2 To move on, say 'I'm done here' or press 3" src="SP1314_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_nm2_04" cond="((callType != 'make_pmt_auto_pay') &amp;&amp; (isAutopayEnabled == false))">
                <prompt-segments>
                  <audiofile text="Please say  'repeat that' or press 1 'make another payment', or press 2  'set up autopay', or press 3  or  say 'I'm done'  or press 4" src="SP1314_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SP1314_ini_02" cond="(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that', otherwise to continue  to autopay set up say 'I'm done here'" src="SP1314_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_ini_03" cond="((!(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))&amp;&amp; (isAutopayEnabled == false)))">
                <prompt-segments>
                  <audiofile text="Say 'repeat that', 'make another payment', 'set up autopay', or  'I'm done'" src="SP1314_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1314_ini_01" cond="((!(callType == 'make_pmt_auto_pay' &amp;&amp; dueImmediatelyAmount &lt; 3 &amp;&amp; !(amountDue &gt; 0))&amp;&amp; (isAutopayEnabled == true)))">
                <prompt-segments>
                  <audiofile text="Say 'repeat that', 'make another payment', 'I'm done'" src="SP1314_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="SP1314_RepeatOtherPayment_DM.jsp" count="1">
            <param name="isAutopayEnrolled" value="isAutopayEnrolledVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="SP1314_RepeatOtherPayment_DM_dtmf.jsp" count="1">
            <param name="isAutopayEnrolled" value="isAutopayEnrolledVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="SP1314_RepeatOtherPayment_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SP1314_RepeatOtherPayment_DM_cnf_nomatch_1"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="SP1314_RepeatOtherPayment_DM_cnf_nomatch_1"/>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="SP1328_AutoPay_SD">
      <gotodialog next="AutoPay_Main_Dialog"/>
      <action next="SP1328_AutoPay_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SP1328_AutoPay_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="SP1312_PostPaymentRouting_DS">
      <if cond="GlobalVars.paymentsEntryPoint =='228Suspended'">
        <session-mapping key="GlobalVars.GetPaymentOptions" expr="undefined"/>
        <if cond="GlobalVars.callType == 'esn_swap' || (GlobalVars.imeiEquipmentActive == true &amp;&amp; GlobalVars.iccidEquipmentActive == false)">
          <action next="SP1320_ReturnToTaskYN_DM"/>
          <else>
            <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
            <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
            <session-mapping key="GlobalVars.paymentsEntryPoint" expr="undefined"/>
            <action next="getReturnLink()"/>
          </else>
        </if>
        <elseif cond="(GlobalVars.GetAccountDetails != null &amp;&amp; GlobalVars.GetAccountDetails != undefined&amp;&amp; GlobalVars.GetAccountDetails.ratePlan != null &amp;&amp; GlobalVars.GetAccountDetails.ratePlan != undefined&amp;&amp; GlobalVars.GetAccountDetails.ratePlan.toLowerCase() == 'emp')">
          <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="undefined"/>
          <session-mapping key="GlobalVars.GetPaymentOptions" expr="undefined"/>
          <action next="getReturnLink()"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'make_pmt_auto_pay'">
          <action next="getReturnLink()"/>
        </elseif>
        <elseif cond="GlobalVars.paymentsEntryPoint == 'careESN'">
          <action next="getReturnLink()"/>
        </elseif>
        <elseif cond="GlobalVars.guestPayment == true">
          <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
        </elseif>
        <else>
          <action next="SP1315_PaymentTips_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="SP1315_PaymentTips_SD">
      <gotodialog next="PaymentTips_Main_Dialog"/>
      <action next="SP1315_PaymentTips_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SP1315_PaymentTips_SD_return_CS">
      <session-mapping key="GlobalVars.paymentsEntryPoint" expr="undefined"/>
      <session-mapping key="GlobalVars.GetPaymentOptions" expr="undefined"/>
      <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
      <action next="getReturnLink()"/>
    </custom-state>

    <dm-state id="SP1320_ReturnToTaskYN_DM" type="YSNO">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <action label="true">
          <audio>
            <prompt id="SP1320_out_01">
              <prompt-segments>
                <audiofile text="Alright" src="SP1320_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.continue228TaskAfterPayment" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="undefined"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="SP1320_out_02">
              <prompt-segments>
                <audiofile text="Alright" src="SP1320_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="undefined"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SP1320_ini_01" cond="callType == 'esn_swap'">
                <prompt-segments>
                  <audiofile text="Do you want to continue switching your phone" src="SP1320_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_ini_02" cond="callType != 'esn_swap'">
                <prompt-segments>
                  <audiofile text="We still need to activate your SIM card so you can begin using your phone Would you like to do that now? " src="SP1320_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="SP1320_ReturnToTaskYN_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SP1320_ini_01" cond="callType == 'esn_swap'">
                <prompt-segments>
                  <audiofile text="Do you want to continue switching your phone" src="SP1320_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_ini_02" cond="callType != 'esn_swap'">
                <prompt-segments>
                  <audiofile text="We still need to activate your SIM card so you can begin using your phone Would you like to do that now? " src="SP1320_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SP1320_nm1_01" cond="callType == 'esn_swap'">
                <prompt-segments>
                  <audiofile text="Do you want to continue switching your phone" src="SP1320_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_nm1_02" cond="callType != 'esn_swap'">
                <prompt-segments>
                  <audiofile text="Would you like to activate your SIM card now" src="SP1320_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SP1320_nm2_01" cond="callType == 'esn_swap'">
                <prompt-segments>
                  <audiofile text="If you want to continue switching your phone,  say 'yes' or press 1 Otherwise, say 'no' or press 2" src="SP1320_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_nm2_02" cond="callType != 'esn_swap'">
                <prompt-segments>
                  <audiofile text="If you want to activate your SIM card right now, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="SP1320_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SP1320_nm3_01" cond="callType == 'esn_swap'">
                <prompt-segments>
                  <audiofile text="To continue switching your phone, press 1 If you're done, press 2" src="SP1320_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_nm3_02" cond="callType != 'esn_swap'">
                <prompt-segments>
                  <audiofile text="To activate your SIM card now, press 1 If you're done, press 2" src="SP1320_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_nm1_01" cond="callType == 'esn_swap'">
                <prompt-segments>
                  <audiofile text="Do you want to continue switching your phone" src="SP1320_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_nm1_02" cond="callType != 'esn_swap'">
                <prompt-segments>
                  <audiofile text="Would you like to activate your SIM card now" src="SP1320_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_nm2_01" cond="callType == 'esn_swap'">
                <prompt-segments>
                  <audiofile text="If you want to continue switching your phone,  say 'yes' or press 1 Otherwise, say 'no' or press 2" src="SP1320_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_nm2_02" cond="callType != 'esn_swap'">
                <prompt-segments>
                  <audiofile text="If you want to activate your SIM card right now, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="SP1320_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_nm3_01" cond="callType == 'esn_swap'">
                <prompt-segments>
                  <audiofile text="To continue switching your phone, press 1 If you're done, press 2" src="SP1320_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_nm3_02" cond="callType != 'esn_swap'">
                <prompt-segments>
                  <audiofile text="To activate your SIM card now, press 1 If you're done, press 2" src="SP1320_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SP1320_ini_01" cond="callType == 'esn_swap'">
                <prompt-segments>
                  <audiofile text="Do you want to continue switching your phone" src="SP1320_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1320_ini_02" cond="callType != 'esn_swap'">
                <prompt-segments>
                  <audiofile text="We still need to activate your SIM card so you can begin using your phone Would you like to do that now? " src="SP1320_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="SP1320_ReturnToTaskYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="SP1320_ReturnToTaskYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="SP1325_AuthorizationErrors_SD">
      <gotodialog next="PaymentErrors_Main_Dialog"/>
      <action next="SP1325_AuthorizationErrors_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SP1325_AuthorizationErrors_SD_return_CS">
      <if cond="GlobalVars.callType == 'goodbye' || GlobalVars.callType == 'extension' || GlobalVars.paymentFailure == true || GlobalVars.authorizationFailureHandling == 'waitPrompting' ||  GlobalVars.authorizationFailureHandling == 'systemError'">
        <action next="getReturnLink()"/>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'sameAmountDeclined'">
          <action next="SP1205_PaymentAmount_SD"/>
        </elseif>
        <else>
          <action next="SP1210_PaymentIntroMethod_SD"/>
        </else>
      </if>
    </custom-state>

    <data-access-state id="SP1308_RechargeByVoucher_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="voucherNumber" value="GlobalVars.voucherNumber" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="AnonymousRechargeByVoucher" classname="com.nuance.metro.dataaccess.AnonymousRechargeByVoucher">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="voucherNumber" mask="true"/>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="voucherNumber" mask="true"/>
          <output-variable name="reChargeStatus"/>
          <output-variable name="confirmationId"/>
          <output-variable name="reChargeErrorDesc"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="PaymentTable.CARD_TYPE" expr="'not_set'"/>
        <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="''"/>
        <session-mapping key="GlobalVars.AnonymousRechargeByVoucher" expr="AnonymousRechargeByVoucher"/>
        <if cond="(AnonymousRechargeByVoucher &amp;&amp; AnonymousRechargeByVoucher.status &amp;&amp; AnonymousRechargeByVoucher.status.toUpperCase() == 'SUCCESS')">
          <session-mapping key="GlobalVars.voucherNumber" expr="AnonymousRechargeByVoucher.voucherNumber"/>
          <session-mapping key="GlobalVars.reChargeStatus" expr="AnonymousRechargeByVoucher.reChargeStatus"/>
          <session-mapping key="GlobalVars.confirmationId" expr="AnonymousRechargeByVoucher.confirmationId"/>
          <session-mapping key="GlobalVars.confirmationNumber" expr="AnonymousRechargeByVoucher.confirmationId"/>
          <session-mapping key="GlobalVars.reChargeErrorDesc" expr="AnonymousRechargeByVoucher.reChargeErrorDesc"/>
          <session-mapping key="GlobalVars.errorCode" expr="AnonymousRechargeByVoucher.errorCode"/>
        </if>
        <if cond="GlobalVars.AnonymousRechargeByVoucher.reChargeStatus != 'Y'">
          <session-mapping key="GlobalVars.authorizationFailureHandling" expr="'prepaidVoucher'"/>
          <action next="SP1325_AuthorizationErrors_SD"/>
          <else>
            <session-mapping key="GlobalVars.completedOnePaymentOnCall" value="true" type="Boolean"/>
            <action next="SP1326_MetricsSuccessPrepaid_JDA"/>
          </else>
        </if>
        <action next="SP1306_CheckAuthorizationResult_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="SP1326_MetricsSuccessPrepaid_DS">
      <action next="SP1309_GetAccountDetails_DB_DA"/>
    </decision-state>

    <data-access-state id="SP1309_GetAccountDetails_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <data-access id="GetSubscriberDetails" classname="com.nuance.metro.dataaccess.GetSubscriberDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="languageCode"/>
          <input-variable name="sessionId"/>
          <input-variable name="imei"/>
          <input-variable name="clearCache"/>
          <input-variable name="multiLineDetails"/>
        </inputs>
        <outputs>
          <output-variable name="mdn"/>
          <output-variable name="accountNumber" mask="true"/>
          <output-variable name="active"/>
          <output-variable name="serialNumber"/>
          <output-variable name="features"/>
          <output-variable name="thirdPartyFeatures"/>
          <output-variable name="payDate"/>
          <output-variable name="taxInclusive"/>
          <output-variable name="accountStatus"/>
          <output-variable name="dueImmediatelyAmount"/>
          <output-variable name="highSecurity"/>
          <output-variable name="ratePlan"/>
          <output-variable name="marketID"/>
          <output-variable name="language"/>
          <output-variable name="isAutopayEnabled"/>
          <output-variable name="hasPhoneInsurance"/>
          <output-variable name="balance"/>
          <output-variable name="isDeviceAllowed"/>
          <output-variable name="isSuspended"/>
          <output-variable name="accountType"/>
          <output-variable name="validAccount"/>
          <output-variable name="roamingBucket"/>
          <output-variable name="paymentStatus"/>
          <output-variable name="ratePlanAttributesMap"/>
          <output-variable name="networkType"/>
          <output-variable name="securityQuestionCode"/>
          <output-variable name="hasEWallet"/>
          <output-variable name="eligibleForBillCycleReset"/>
          <output-variable name="billCycleLength"/>
          <output-variable name="firstName"/>
          <output-variable name="lastName"/>
          <output-variable name="eligibleForUnhotlineCheck"/>
          <output-variable name="securityQuestionString"/>
          <output-variable name="iccid"/>
          <output-variable name="imei"/>
          <output-variable name="hasAutopayPending"/>
          <output-variable name="insuranceCode"/>
          <output-variable name="subscriberId"/>
          <output-variable name="usedESNAllowed"/>
          <output-variable name="emailVerificationStatus"/>
          <output-variable name="emailAddress"/>
          <output-variable name="currentPromo"/>
          <output-variable name="futurePromo"/>
          <output-variable name="featureCodes"/>
          <output-variable name="superRegion"/>
          <output-variable name="accountFutureRequestInd"/>
          <output-variable name="subscriberFuturePricePlanInd"/>
          <output-variable name="ratePlanPrice"/>
          <output-variable name="currentPlanPromptURL"/>
          <output-variable name="currentPlanPromptTTS"/>
          <output-variable name="familyPlanIndicator"/>
          <output-variable name="topupEligibility"/>
          <output-variable name="customerTreatmentLevel"/>
          <output-variable name="HasHighSpeedData"/>
          <output-variable name="includedFeatures"/>
          <output-variable name="hasDataMaximizer"/>
          <output-variable name="eligibleForUpgrade"/>
          <output-variable name="upgradeEligibilityDate"/>
          <output-variable name="arBalance"/>
          <output-variable name="multilineList"/>
          <output-variable name="planMedialPromptURL"/>
          <output-variable name="planMedialPromptTTS"/>
          <output-variable name="hasDefaultAccountValues"/>
          <output-variable name="zipCode" mask="true"/>
          <output-variable name="accountRestrictIndicator"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetSubscriberDetails.status &amp;&amp; GetSubscriberDetails.status.toUpperCase() == 'SUCCESS' &amp;&amp; GetSubscriberDetails.validAccount">
          <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
          <action next="SP1313_PlayBalanceUpdate_PP"/>
          <else>
            <action next="SP1313_PlayBalanceUpdate_PP"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <dm-state id="SP1316_AskToSaveCard_DM" type="YSNO">
      <session-mapping key="isWalletPopulated" value="GlobalVars.GetPaymentOptions.isWalletPopulated" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.manageCardTask" expr="'saveMyCard'"/>
          <if cond="isWalletPopulated ==true">
            <action next="SP1317_AskPrimary_DM"/>
            <else>
              <session-mapping key="GlobalVars.manageCardTask" expr="'saveMyCard'"/>
              <session-mapping key="GlobalVars.defaultPaymentMethod" value="true" type="Boolean"/>
              <action next="SP1330_ManageCards_SD"/>
            </else>
          </if>
        </action>
        <action label="false">
          <audio>
            <prompt id="SP1316_out_01">
              <prompt-segments>
                <audiofile text="Ok" src="SP1316_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SP1316_ini_01" cond="(isWalletPopulated == false)">
                <prompt-segments>
                  <audiofile text="Would you like to save this card in your account so it will be easier to use next time?" src="SP1316_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1316_ini_02" cond="!(isWalletPopulated == false)">
                <prompt-segments>
                  <audiofile text="Would you save this card to your account?" src="SP1316_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="SP1316_AskToSaveCard_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SP1316_ini_01" cond="(isWalletPopulated == false)">
                <prompt-segments>
                  <audiofile text="Would you like to save this card in your account so it will be easier to use next time?" src="SP1316_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1316_ini_02" cond="!(isWalletPopulated == false)">
                <prompt-segments>
                  <audiofile text="Would you save this card to your account?" src="SP1316_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SP1316_ini_01" cond="(isWalletPopulated == false)">
                <prompt-segments>
                  <audiofile text="Would you like to save this card in your account so it will be easier to use next time?" src="SP1316_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1316_ini_02" cond="!(isWalletPopulated == false)">
                <prompt-segments>
                  <audiofile text="Would you save this card to your account?" src="SP1316_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SP1316_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to save this card so you can use it next time you make a payment press 1  If not press 2" src="SP1316_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SP1316_AskToSaveCard_DM_noinput_3"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="SP1316_nm1_01">
                <prompt-segments>
                  <audiofile text="Sorry, I didnt get that  If you'd like to save this card to your account say 'yes' or press 1  if not say 'no' or press 2" src="SP1316_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="SP1316_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to save this card so you can use it next time you make a payment press 1  If not press 2" src="SP1316_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="SP1316_AskToSaveCard_DM_nomatch_3"/>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SP1316_ini_01" cond="(isWalletPopulated == false)">
                <prompt-segments>
                  <audiofile text="Would you like to save this card in your account so it will be easier to use next time?" src="SP1316_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SP1316_ini_02" cond="!(isWalletPopulated == false)">
                <prompt-segments>
                  <audiofile text="Would you save this card to your account?" src="SP1316_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="SP1316_AskToSaveCard_DM.grxml" count="1"/>
          <dtmfgrammars filename="SP1316_AskToSaveCard_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="SP1317_AskPrimary_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.defaultPaymentMethod" value="true" type="Boolean"/>
          <action next="SP1330_ManageCards_SD"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <action next="SP1330_ManageCards_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SP1317_ini_01">
                <prompt-segments>
                  <audiofile text="And would you like to make this your primary payment card?" src="SP1317_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="SP1317_AskPrimary_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SP1317_ini_01">
                <prompt-segments>
                  <audiofile text="And would you like to make this your primary payment card?" src="SP1317_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SP1317_ini_01">
                <prompt-segments>
                  <audiofile text="And would you like to make this your primary payment card?" src="SP1317_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="SP1317_nm2_01">
                <prompt-segments>
                  <audiofile text="I still didnt get that  To set this card as your primary payment card press 1  if not press 2" src="SP1317_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="SP1317_AskPrimary_DM_noinput_3"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="SP1317_nm1_01">
                <prompt-segments>
                  <audiofile text="Sorry, I didn't get that If you'd like to make this your primary payment card say 'yes' or press 1  If not say 'no' or press 2" src="SP1317_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="SP1317_nm2_01">
                <prompt-segments>
                  <audiofile text="I still didnt get that  To set this card as your primary payment card press 1  if not press 2" src="SP1317_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="SP1317_AskPrimary_DM_nomatch_3"/>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SP1317_ini_01">
                <prompt-segments>
                  <audiofile text="And would you like to make this your primary payment card?" src="SP1317_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="SP1317_AskPrimary_DM.grxml" count="1"/>
          <dtmfgrammars filename="SP1317_AskPrimary_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="SP1330_ManageCards_SD">
      <gotodialog next="ManageCards_Main_Dialog"/>
      <action next="SP1330_ManageCards_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SP1330_ManageCards_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  