<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="cheaperOnly" value="empty" type="String"/>
  <session-mapping key="JWTToken" value="empty" type="String"/>
  <session-mapping key="futureChangeOffer" value="empty" type="String"/>
  <session-mapping key="careEsnPlan" value="true" type="boolean"/>
  <session-mapping key="GetAvailableRatePlanOffers.status" value="Success" type="string"/>
  <session-mapping key="GlobalVars.ratePlans" value="null" type="string"/>
  <session-mapping key="ratePlans.length" value="0" type="string"/>
  <session-mapping key="restrictionType" value="NoChangeAllowed" type="string"/>
  <session-mapping key="numPlansWithSamePrice" value="1" type="integer"/>
  <session-mapping key="EP1110_ChooseNewPlanShort_DM.nbestresults" value="undefined" type="string"/>
  <session-mapping key="newPlanShortEntryFrom" value="EP1201_CheckAmbiguity_DS" type="string"/>
  <session-mapping key="choseInvalidPlan" value="true" type="boolean"/>
  <session-mapping key="interpretation.dm_root" value="different_plan" type="string"/>
  <session-mapping key="EP1115_ChooseNewPlanLong_DM.nbestresults" value="undefined" type="string"/>
  <session-mapping key="isOnFamilyPlan" value="true" type="boolean"/>
  <session-mapping key="numberOfPlans" value="1" type="integer"/>
  <session-mapping key="lowestPrice" value="highestPrice" type="string"/>
  <session-mapping key="GlobalVars.ratePlanSelectionType" value="price" type="string"/>
  <session-mapping key="availablePlansArray.length" value="3" type="integer"/>
  <session-mapping key="cancelESNSwap" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.offerRepeatESNPlan" value="true" type="boolean"/>
  <session-mapping key="noToConfirmPlan" value="2" type="integer"/>
  <session-mapping key="GlobalVars.disambigNoMatches" value="true" type="boolean"/>
  <session-mapping key="ratePlansLength" value="1" type="string"/>
</session-mappings>
