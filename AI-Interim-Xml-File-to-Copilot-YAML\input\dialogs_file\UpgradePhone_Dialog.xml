<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="UpgradePhone_Dialog">
    <decision-state id="UP1001_CheckEligible_DS">
      <session-mapping key="eligibleForUpgrade" value="GlobalVars.eligibleForUpgrade" type="String"/>
      <if cond="eligibleForUpgrade == true">
        <action next="UP1005_PlayEligibleNow_PP"/>
        <else>
          <action next="UP1005_PlayEligibleNow_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="UP1005_PlayEligibleNow_PP">
      <audio>
        <prompt id="UP1005_out_01">
          <prompt-segments>
            <audiofile text="We have lots of new phones for you to choose from!  Our call center agents don't have access to those phone pricing, so come see us at a Metro store and we'll set you up" src="UP1005_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="UP1005_out_02">
          <prompt-segments>
            <audiofile text="If you stay on the line I ll help you look for the nearest store We also have a map on metro by t dash mobile dot com So if you re all set you can just hang up" src="UP1005_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_3000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_3000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.storeLocatorReason" expr="'buy-phone'"/>
      <action next="UP1010_StoreLocator_SD"/>
    </play-state>

    <subdialog-state id="UP1010_StoreLocator_SD">
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="UP1010_StoreLocator_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="UP1010_StoreLocator_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <play-state id="UP1015_PlayEligibilityDate_PP">
      <session-mapping key="upgradeEligibilityDate" value="GlobalVars.upgradeEligibilityDate" type="String"/>
      <audio>
        <prompt id="UP1015_out_01">
          <prompt-segments>
            <audiofile text="I see you ll be eligible for special prices on phone upgrades starting" src="UP1015_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="date" expr="upgradeEligibilityDate">
          <param name="dateFormat" value="MMddyyyy" scope="session"/>
          <param name="playDayOfMonth" value="true"/>
          <param name="playYear" value="true"/>
          <param name="playDayOfTheWeek" value="false"/>
          <param name="intonation" value="f"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="UP1020_OfferSalesAgentYN_DM"/>
    </play-state>

    <dm-state id="UP1020_OfferSalesAgentYN_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.callType" expr="'transfer'"/>
          <session-mapping key="GlobalVars.tag" expr="'purchase_phone'"/>
          <action next="UP1025_Transfer_SD"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="UP1020_out_01">
              <prompt-segments>
                <audiofile text="No problem" src="UP1020_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="repeat" next="UP1015_PlayEligibilityDate_PP"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="UP1020_ini_01">
                <prompt-segments>
                  <audiofile text="If you re looking to buy a phone at the regular price right now I ll take you to someone who can help you find the best match Would you like to do that? " src="UP1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="UP1020_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to talk to someone about buying a phone today? Say yes no or repeat" src="UP1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UP1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to talk to someone about buying a phone at the regular price today, say yes or press 1 If you re all set, say no or press 2 To hear your eligibility date again, say repeat or press 3" src="UP1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UP1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to talk to someone about buying a phone at the regular price today, say yes or press 1 If you re all set, say no or press 2 To hear your eligibility date again, say repeat or press 3" src="UP1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UP1020_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to talk to someone about buying a phone today? Say yes no or repeat" src="UP1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UP1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to talk to someone about buying a phone at the regular price today, say yes or press 1 If you re all set, say no or press 2 To hear your eligibility date again, say repeat or press 3" src="UP1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UP1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to talk to someone about buying a phone at the regular price today, say yes or press 1 If you re all set, say no or press 2 To hear your eligibility date again, say repeat or press 3" src="UP1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="UP1020_ini_01">
                <prompt-segments>
                  <audiofile text="If you re looking to buy a phone at the regular price right now I ll take you to someone who can help you find the best match Would you like to do that? " src="UP1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_UP1020.grxml" dtmfcommandgrammar="GlobalCommands_UP1020_dtmf.grxml">
          <grammars filename="UP1020_OfferSalesAgentYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="UP1020_OfferSalesAgentYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="UP1025_Transfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="UP1025_Transfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="UP1025_Transfer_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

  </dialog>
  