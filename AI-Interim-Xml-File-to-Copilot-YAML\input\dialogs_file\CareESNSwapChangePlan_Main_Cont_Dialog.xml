<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="CareESNSwapChangePlan_Main_Cont_Dialog">
    <decision-state id="EP1001_CheckContext_DS">
      <if cond="careEsnPlan == 'true'">
        <session-mapping key="GlobalVars.offerRepeatESNPlan" value="true" type="Boolean"/>
        <action next="EP1002_BroadcastMessages_SD"/>
        <else>
          <action next="EP1020_CallTransfer_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="EP1002_BroadcastMessages_SD">
      <session-mapping key="GlobalVars.broadcastMessageKey" expr="'RP'"/>
      <gotodialog next="BroadcastMessages_Dialog"/>
      <action next="EP1002_BroadcastMessages_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="EP1002_BroadcastMessages_SD_return_CS">
      <action next="EP1005_GetAvailableRatePlanOffers_DB_DA"/>
    </custom-state>

    <data-access-state id="EP1005_GetAvailableRatePlanOffers_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="GlobalVars.sessionId" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="cheaperOnly" value="GlobalVars.cheaperOnly" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="currentRatePlan" value="GlobalVars.GetAccountDetails.ratePlan.toLowerCase()" type="String"/>
      <session-mapping key="isSwitchRatePlan" value="true" type="Boolean"/>
      <session-mapping key="futureChangeOffer" value="false" type="Boolean"/>
      <data-access id="GetAvailableRatePlanOffers" classname="com.nuance.metro.dataaccess.GetAvailableRatePlanOffers">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="imei"/>
          <input-variable name="sessionId"/>
          <input-variable name="languageCode"/>
          <input-variable name="cheaperOnly"/>
          <input-variable name="JWTToken"/>
          <input-variable name="futureChangeOffer"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="ratePlans"/>
          <output-variable name="futureDate"/>
          <output-variable name="restrictionType"/>
          <output-variable name="existingPlanPricePrimaryRate"/>
          <output-variable name="RatePlanGrammarURL"/>
          <output-variable name="RatePlanDTMFGrammarURL"/>
          <output-variable name="RatePlanUnambiguousGrammarURL"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetAvailableRatePlanOffers.status == 'Success'">
          <session-mapping key="GlobalVars.ratePlans" expr="GetAvailableRatePlanOffers.ratePlans"/>
          <session-mapping key="GlobalVars.unfilteredRatePlanList" expr="GetAvailableRatePlanOffers.unfilteredRatePlanList"/>
          <session-mapping key="GlobalVars.GetAvailableRatePlans" expr="GetAvailableRatePlanOffers"/>
          <session-mapping key="GlobalVars.futureDate" expr="GetAvailableRatePlanOffers.futureDate"/>
          <session-mapping key="GlobalVars.restrictionType" expr="GetAvailableRatePlanOffers.restrictionType"/>
          <session-mapping key="GlobalVars.RatePlanGrammarURL" expr="GetAvailableRatePlanOffers.RatePlanGrammarURL"/>
          <session-mapping key="GlobalVars.RatePlanDTMFGrammarURL" expr="GetAvailableRatePlanOffers.RatePlanDTMFGrammarURL"/>
          <session-mapping key="GlobalVars.RatePlanUnambiguousGrammarURL" expr="GetAvailableRatePlanOffers.RatePlanUnambiguousGrammarURL"/>
          <action next="EP1015_NoIVRPlans_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail.getOffers'"/>
            <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'133'"/>
            <action next="EP1020_CallTransfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="EP1015_NoIVRPlans_DS">
      <session-mapping key="restrictionType" value="GlobalVars.restrictionType" type="String"/>
      <if cond="(GlobalVars.ratePlans == null || GlobalVars.ratePlans.length == 0) || (restrictionType == 'NoChangeAllowed')">
        <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
        <action next="EP1020_CallTransfer_SD"/>
        <else>
          <action next="EP1101_OnlyOnePlan_JDA"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="EP1020_CallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="EP1020_CallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="EP1020_CallTransfer_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="EP1101_OnlyOnePlan_DS">
      <if cond="GlobalVars.ratePlans.length == 1">
        <session-mapping key="playDetailsEnteringFrom" expr="'EP1101'"/>
        <session-mapping key="GlobalVars.newPlanSOC" expr="GlobalVars.ratePlans[0].soc"/>
        <action next="EP1106_OfferOnePlanYN_DM"/>
        <else>
          <action next="EP1105_CheckPricePoint_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="EP1105_CheckPricePoint_DS">
      <if cond="numPlansWithSamePrice == 1">
        <session-mapping key="GlobalVars.newPlanSOC" expr="matchingPlanSOC"/>
        <session-mapping key="playDetailsEnteringFrom" expr="'EP1105'"/>
        <action next="EP1106_OfferOnePlanYN_DM"/>
        <else>
          <action next="EP1110_ChooseNewPlanShort_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="EP1110_ChooseNewPlanShort_DM" type="CUST">
      <session-mapping key="ratePlanSOCs" value="" type="String"/>
      <session-mapping key="cheapPlan" value="" type="String"/>
      <session-mapping key="expensivePlan" value="" type="String"/>
      <session-mapping key="choseInvalidPlan" value="GlobalVars.choseInvalidPlan" type="String"/>
      <session-mapping key="planGrammarURL" value="GlobalVars.RatePlanGrammarURL" type="String"/>
      <session-mapping key="planGrammarDtmfURL" value="GlobalVars.RatePlanDTMFGrammarURL" type="String"/>
      <session-mapping key="RatePlanUnambiguousGrammarURL" value="GlobalVars.RatePlanUnambiguousGrammarURL" type="String"/>
      <session-mapping key="ratePlanSOCs" expr="getAllRatePlans(GlobalVars.ratePlans)"/>
      <session-mapping key="cheapPlan" expr="Math.round(getLowestRatePlanPrice(GlobalVars.ratePlans))"/>
      <session-mapping key="expensivePlan" expr="Math.round(getHighestRatePlanPrice(GlobalVars.ratePlans))"/>
      <success>
        <action label="more_info" next="EP1115_ChooseNewPlanLong_DM"/>
        <action label="different_plan">
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
          <action next="EP1020_CallTransfer_SD"/>
        </action>
        <action label="default" next="EP1201_CheckAmbiguity_JDA">
          <if cond="EP1110_ChooseNewPlanShort_DM.nbestresults != undefined">
            <session-mapping key="GlobalVars.nbestresults" expr="EP1110_ChooseNewPlanShort_DM.nbestresults"/>
            <session-mapping key="GlobalVars.ratePlanSelectionType" expr="EP1110_ChooseNewPlanShort_DM.nbestresults[0].interpretation.selectionType"/>
          </if>
          <session-mapping key="GlobalVars.selectedPlan" expr="EP1110_ChooseNewPlanShort_DM.returnvalue"/>
          <session-mapping key="GlobalVars.choseInvalidPlan" expr="undefined"/>
        </action>
        <action label="help">
          <action next="EP1115_ChooseNewPlanLong_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="newPlanShortEntryFrom == 'EP1201_CheckAmbiguity_DS'">
                <prompt id="EP1110_rin_01">
                  <prompt-segments>
                    <audiofile text=" There are a few plans that match that option Please say the full name of the plan you d like I ll read out your choices again  you can say " src="EP1110_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="newPlanShortEntryFrom == 'EP1215_DisambiguatePlan_SD_return'">
                  <prompt id="EP1110_rin_02">
                    <prompt-segments>
                      <audiofile text=" Let me read you the available plans again, in case one of them works for you If you do hear one that you want, repeat the full name after me Here they are " src="EP1110_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="choseInvalidPlan == true">
                  <prompt id="EP1110_rin_03">
                    <prompt-segments>
                      <audiofile text=" Let me tell you about the plans we DO have, and when you hear the one you want, just say it back to me So, tell me which plan you want " src="EP1110_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="EP1110_ini_01">
                    <prompt-segments>
                      <audiofile text=" I ll tell you what plans are available  when you hear the one you want, just say it back to me " src="EP1110_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1110_ini_02">
                <prompt-segments>
                  <audiofile text=" So, which plan would you like? " src="EP1110_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1110_rin_04" cond="choseInvalidPlan == true">
                <prompt-segments>
                  <audiofile text="If none of those are what you wanted, you can say different plan  " src="EP1110_rin_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="newPlanShortEntryFrom == 'EP1201_CheckAmbiguity_DS'">
                <prompt id="EP1110_rin_01">
                  <prompt-segments>
                    <audiofile text=" There are a few plans that match that option Please say the full name of the plan you d like I ll read out your choices again  you can say " src="EP1110_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="newPlanShortEntryFrom == 'EP1215_DisambiguatePlan_SD_return'">
                  <prompt id="EP1110_rin_02">
                    <prompt-segments>
                      <audiofile text=" Let me read you the available plans again, in case one of them works for you If you do hear one that you want, repeat the full name after me Here they are " src="EP1110_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="choseInvalidPlan == true">
                  <prompt id="EP1110_rin_03">
                    <prompt-segments>
                      <audiofile text=" Let me tell you about the plans we DO have, and when you hear the one you want, just say it back to me So, tell me which plan you want " src="EP1110_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="EP1110_ini_01">
                    <prompt-segments>
                      <audiofile text=" I ll tell you what plans are available  when you hear the one you want, just say it back to me " src="EP1110_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1110_ini_02">
                <prompt-segments>
                  <audiofile text=" So, which plan would you like? " src="EP1110_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1110_rin_04" cond="choseInvalidPlan == true">
                <prompt-segments>
                  <audiofile text="If none of those are what you wanted, you can say different plan  " src="EP1110_rin_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="EP1110_nm1_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1110_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1110_nm2_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1110_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansOnly_EP1110_NM2"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1110_nm2_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1110_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansOnly_EP1110_NM2"/>
              </prompt>
              <prompt id="EP1110_nm3_01">
                <prompt-segments>
                  <audiofile text=" Or say more information " src="EP1110_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1110_nm1_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1110_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1110_nm2_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1110_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansOnly_EP1110_NM2"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1110_nm2_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1110_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansOnly_EP1110_NM2"/>
              </prompt>
              <prompt id="EP1110_nm3_01">
                <prompt-segments>
                  <audiofile text=" Or say more information " src="EP1110_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="newPlanShortEntryFrom == 'EP1201_CheckAmbiguity_DS'">
                <prompt id="EP1110_rin_01">
                  <prompt-segments>
                    <audiofile text=" There are a few plans that match that option Please say the full name of the plan you d like I ll read out your choices again  you can say " src="EP1110_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="newPlanShortEntryFrom == 'EP1215_DisambiguatePlan_SD_return'">
                  <prompt id="EP1110_rin_02">
                    <prompt-segments>
                      <audiofile text=" Let me read you the available plans again, in case one of them works for you If you do hear one that you want, repeat the full name after me Here they are " src="EP1110_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="choseInvalidPlan == true">
                  <prompt id="EP1110_rin_03">
                    <prompt-segments>
                      <audiofile text=" Let me tell you about the plans we DO have, and when you hear the one you want, just say it back to me So, tell me which plan you want " src="EP1110_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="EP1110_ini_01">
                    <prompt-segments>
                      <audiofile text=" I ll tell you what plans are available  when you hear the one you want, just say it back to me " src="EP1110_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1110_ini_02">
                <prompt-segments>
                  <audiofile text=" So, which plan would you like? " src="EP1110_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1110_rin_04" cond="choseInvalidPlan == true">
                <prompt-segments>
                  <audiofile text="If none of those are what you wanted, you can say different plan  " src="EP1110_rin_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="EP1110_ChooseNewPlanShort_DM.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="EP1110_ChooseNewPlanShort_DM_dtmf.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'different_plan'">
                <prompt id="EP1110_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You wanted a different plan " src="EP1110_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="EP1115_ChooseNewPlanLong_DM" type="CUST">
      <session-mapping key="ratePlanSOCs" value="" type="String"/>
      <session-mapping key="numberOfPlans" value="numberOfRatePlans(GlobalVars.ratePlans)" type="String"/>
      <session-mapping key="lowestPrice" value="getLowestRatePlanPrice(GlobalVars.ratePlans)" type="String"/>
      <session-mapping key="highestPrice" value="getHighestRatePlanPrice(GlobalVars.ratePlans)" type="String"/>
      <session-mapping key="choseInvalidPlan" value="GlobalVars.choseInvalidPlan" type="String"/>
      <session-mapping key="isOnFamilyPlan" value="GlobalVars.isOnFamilyPlan" type="String"/>
      <session-mapping key="planGrammarURL" value="GlobalVars.RatePlanGrammarURL" type="String"/>
      <session-mapping key="planGrammarDtmfURL" value="GlobalVars.RatePlanDTMFGrammarURL" type="String"/>
      <session-mapping key="RatePlanUnambiguousGrammarURL" value="GlobalVars.RatePlanUnambiguousGrammarURL" type="String"/>
      <session-mapping key="ratePlanSOCs" expr="getAllRatePlans(GlobalVars.ratePlans)"/>
      <success>
        <action label="more_info" next="EP1115_ChooseNewPlanLong_DM">
          <audio>
            <prompt id="gl_cnf_ini_01">
              <prompt-segments>
                <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="reentry_EP1115_ChooseNewPlanLong_DM" value="true" type="Boolean"/>
        </action>
        <action label="repeat" next="EP1115_ChooseNewPlanLong_DM">
          <audio>
            <prompt id="EP1115_out_01">
              <prompt-segments>
                <audiofile text=" Sure, I ll go through those details again " src="EP1115_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="reentry_EP1115_ChooseNewPlanLong_DM" value="true" type="Boolean"/>
        </action>
        <action label="different_plan">
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
          <action next="EP1020_CallTransfer_SD"/>
        </action>
        <action label="default" next="EP1201_CheckAmbiguity_JDA">
          <session-mapping key="bHeardInNewPlanLong" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.ratePlanSelectionType" expr="EP1115_ChooseNewPlanLong_DM.nbestresults[0].interpretation.selectionType"/>
          <if cond="EP1115_ChooseNewPlanLong_DM.nbestresults != undefined">
            <session-mapping key="GlobalVars.nbestresults" expr="EP1115_ChooseNewPlanLong_DM.nbestresults"/>
          </if>
          <session-mapping key="GlobalVars.selectedPlan" expr="EP1115_ChooseNewPlanLong_DM.returnvalue"/>
          <session-mapping key="GlobalVars.choseInvalidPlan" expr="undefined"/>
          <session-mapping key="GlobalVars.offerRepeatESNPlan" value="false" type="Boolean"/>
        </action>
        <action label="help">
          <action next="EP1115_ChooseNewPlanLong_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="isOnFamilyPlan == true">
                <prompt id="EP1115_ini_01">
                  <prompt-segments>
                    <audiofile text=" There are several plans you can change to " src="EP1115_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="EP1115_ini_02" cond="numberOfPlans &gt; 1 &amp;&amp; lowestPrice != highestPrice">
                    <prompt-segments>
                      <audiofile text="There are several plans you can change to, ranging in price from  " src="EP1115_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="natural" expr="lowestPrice" cond="numberOfPlans &gt; 1 &amp;&amp; lowestPrice != highestPrice"/>
                  <prompt id="EP1115_ini_03" cond="numberOfPlans &gt; 1 &amp;&amp; lowestPrice != highestPrice">
                    <prompt-segments>
                      <audiofile text="to " src="EP1115_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="natural" expr="highestPrice" cond="numberOfPlans &gt; 1 &amp;&amp; lowestPrice != highestPrice"/>
                  <prompt id="EP1115_ini_04" cond="numberOfPlans &gt; 1 &amp;&amp; lowestPrice != highestPrice">
                    <prompt-segments>
                      <audiofile text=" dollars a month, and the price for each plan includes all taxes and regulatory fees " src="EP1115_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="EP1115_ini_05">
                <prompt-segments>
                  <audiofile text=" When you hear the one you want, just say the name of the plan back to me " src="EP1115_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_EP1115_ChooseNewPlanLong"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1115_ini_10">
                <prompt-segments>
                  <audiofile text=" Now, which one would you like You can say" src="EP1115_ini_10.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
              <prompt id="EP1115_ini_11">
                <prompt-segments>
                  <audiofile text=" Or you can say repeat that to hear those options again " src="EP1115_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1115_ini_12" cond="choseInvalidPlan == true">
                <prompt-segments>
                  <audiofile text="If none of those are what you wanted, you can say different plan  " src="EP1115_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="EP1115_ini_05">
                <prompt-segments>
                  <audiofile text=" When you hear the one you want, just say the name of the plan back to me " src="EP1115_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_EP1115_ChooseNewPlanLong"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1115_ini_10">
                <prompt-segments>
                  <audiofile text=" Now, which one would you like You can say" src="EP1115_ini_10.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
              <prompt id="EP1115_ini_11">
                <prompt-segments>
                  <audiofile text=" Or you can say repeat that to hear those options again " src="EP1115_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1115_ini_12" cond="choseInvalidPlan == true">
                <prompt-segments>
                  <audiofile text="If none of those are what you wanted, you can say different plan  " src="EP1115_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="EP1115_nm1_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1115_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1115_nm2_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1115_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansOnly_EP1115_NM2"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1115_nm2_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1115_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansOnly_EP1115_NM2"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1115_nm1_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1115_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1115_nm2_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1115_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansOnly_EP1115_NM2"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1115_nm2_01">
                <prompt-segments>
                  <audiofile text="Say " src="EP1115_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansOnly_EP1115_NM2"/>
              </prompt>
              <prompt id="EP1115_nm3_01">
                <prompt-segments>
                  <audiofile text="Or say more information " src="EP1115_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="isOnFamilyPlan == true">
                <prompt id="EP1115_ini_01">
                  <prompt-segments>
                    <audiofile text=" There are several plans you can change to " src="EP1115_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="EP1115_ini_02" cond="numberOfPlans &gt; 1 &amp;&amp; lowestPrice != highestPrice">
                    <prompt-segments>
                      <audiofile text="There are several plans you can change to, ranging in price from  " src="EP1115_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="natural" expr="lowestPrice" cond="numberOfPlans &gt; 1 &amp;&amp; lowestPrice != highestPrice"/>
                  <prompt id="EP1115_ini_03" cond="numberOfPlans &gt; 1 &amp;&amp; lowestPrice != highestPrice">
                    <prompt-segments>
                      <audiofile text="to " src="EP1115_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="natural" expr="highestPrice" cond="numberOfPlans &gt; 1 &amp;&amp; lowestPrice != highestPrice"/>
                  <prompt id="EP1115_ini_04" cond="numberOfPlans &gt; 1 &amp;&amp; lowestPrice != highestPrice">
                    <prompt-segments>
                      <audiofile text=" dollars a month, and the price for each plan includes all taxes and regulatory fees " src="EP1115_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="EP1115_ini_05">
                <prompt-segments>
                  <audiofile text=" When you hear the one you want, just say the name of the plan back to me " src="EP1115_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_EP1115_ChooseNewPlanLong"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1115_ini_10">
                <prompt-segments>
                  <audiofile text=" Now, which one would you like You can say" src="EP1115_ini_10.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="ratePlanSOCs">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
              <prompt id="EP1115_ini_11">
                <prompt-segments>
                  <audiofile text=" Or you can say repeat that to hear those options again " src="EP1115_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1115_ini_12" cond="choseInvalidPlan == true">
                <prompt-segments>
                  <audiofile text="If none of those are what you wanted, you can say different plan  " src="EP1115_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
          <grammars filename="EP1115_ChooseNewPlanLong_DM.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="EP1115_ChooseNewPlanLong_DM_dtmf.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'different_plan'">
                <prompt id="EP1115_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text=" You wanted a different plan" src="EP1115_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="EP1201_CheckAmbiguity_DS">
      <if cond="GlobalVars.ratePlanSelectionType == 'price'">
        <session-mapping key="requestedPrice" value="GlobalVars.nbestresults[0].interpretation.dm_root" type="String"/>
        <session-mapping key="GlobalVars.availablePlansArray" expr="getRatePlansForPrice(GlobalVars.ratePlans, requestedPrice, GlobalVars.isOnFamilyPlan)"/>
        <if cond="GlobalVars.availablePlansArray.length &gt; 3">
          <session-mapping key="GlobalVars.choseInvalidPlan" value="false" type="Boolean"/>
          <action next="EP1110_ChooseNewPlanShort_DM"/>
          <elseif cond="GlobalVars.availablePlansArray.length == 2 || GlobalVars.availablePlansArray.length == 3">
            <session-mapping key="GlobalVars.choseInvalidPlan" value="false" type="Boolean"/>
            <action next="EP1215_DisambiguatePlan_SD"/>
          </elseif>
          <elseif cond="GlobalVars.availablePlansArray.length == 1">
            <session-mapping key="GlobalVars.selectedPlan" expr="GlobalVars.availablePlansArray[0].soc"/>
            <session-mapping key="GlobalVars.choseInvalidPlan" value="false" type="Boolean"/>
            <action next="EP1205_ConfirmNewPlan_DM"/>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.choseInvalidPlan" value="true" type="Boolean"/>
            <action next="EP1220_PlanNotAvailable_PP"/>
          </else>
        </if>
        <else>
          <session-mapping key="GlobalVars.availablePlansArray" expr="getRatePlansFromNBest(GlobalVars.nbestresults)"/>
          <if cond="GlobalVars.availablePlansArray.length &gt; 3">
            <session-mapping key="GlobalVars.choseInvalidPlan" value="false" type="Boolean"/>
            <action next="EP1110_ChooseNewPlanShort_DM"/>
            <elseif cond="GlobalVars.availablePlansArray.length == 2 || GlobalVars.availablePlansArray.length == 3">
              <session-mapping key="GlobalVars.choseInvalidPlan" value="false" type="Boolean"/>
              <action next="EP1215_DisambiguatePlan_SD"/>
            </elseif>
            <else>
              <session-mapping key="GlobalVars.choseInvalidPlan" value="true" type="Boolean"/>
              <session-mapping key="GlobalVars.selectedPlan" expr="GlobalVars.availablePlansArray[0].soc"/>
              <action next="EP1205_ConfirmNewPlan_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="EP1220_PlanNotAvailable_PP">
      <audio>
        <prompt id="EP1220_out_01">
          <prompt-segments>
            <audiofile text="I m sorry, I can t offer you this plan right now " src="EP1220_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="EP1110_ChooseNewPlanShort_DM"/>
    </play-state>

    <subdialog-state id="EP1225_DetectConflictingOffers_SD">
      <gotodialog next="DetectConflictingOffer_Dialog"/>
      <action next="EP1225_DetectConflictingOffers_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="EP1225_DetectConflictingOffers_SD_return_CS">
      <session-mapping key="plan" value="GlobalVars.selectedPlan" type="String"/>
      <session-mapping key="cancelESNSwap" value="GlobalVars.cancelESNSwap" type="String"/>
      <if cond="cancelESNSwap == true">
        <action next="getReturnLink()"/>
        <else>
          <session-mapping key="GlobalVars.newRatePlan" expr="plan"/>
          <action next="EP1304_CheckNeedPlanDets_JDA"/>
        </else>
      </if>
    </custom-state>

    <decision-state id="EP1304_CheckNeedPlanDets_DS">
      <session-mapping key="offerRepeatESNPlan" value="GlobalVars.offerRepeatESNPlan" type="String"/>
      <if cond="GlobalVars.offerRepeatESNPlan == true">
        <action next="EP1305_HearPlanDetailsYN_DM"/>
        <else>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="EP1305_HearPlanDetailsYN_DM" type="YSNO">
      <success>
        <action label="true" next="EP1310_PlayDetailsAgainYN_DM">
          <session-mapping key="playDetailsEnteringFrom" expr="'EP1305'"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="EP1305_out_01">
              <prompt-segments>
                <audiofile text="Alright" src="EP1305_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="EP1305_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear those plan details again?" src="EP1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="" text="" id="EP1305_HearPlanDetailsYN_DM_help"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="EP1305_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear those plan details again?" src="EP1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="EP1305_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to hear the details for your new plan? " src="EP1305_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1305_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to hear the details of your new plan, say 'yes' or press 1 If you'd just like to move on, say 'no' or press 2 " src="EP1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1305_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to hear the details of your new plan, say 'yes' or press 1 If you'd just like to move on, say 'no' or press 2 " src="EP1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1305_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to hear the details for your new plan? " src="EP1305_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1305_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to hear the details of your new plan, say 'yes' or press 1 If you'd just like to move on, say 'no' or press 2 " src="EP1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1305_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to hear the details of your new plan, say 'yes' or press 1 If you'd just like to move on, say 'no' or press 2 " src="EP1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="EP1305_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear those plan details again?" src="EP1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="EP1305_HearPlanDetailsYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="EP1305_HearPlanDetailsYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="EP1310_PlayDetailsAgainYN_DM" type="YSNO">
      <session-mapping key="plan" value="GlobalVars.newRatePlan" type="String"/>
      <success>
        <action label="true" next="EP1310_PlayDetailsAgainYN_DM">
          <session-mapping key="playDetailsEnteringFrom" expr="'EP1310'"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="EP1310_out_01">
              <prompt-segments>
                <audiofile text="Okay " src="EP1310_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="EP1310_PlayDetailsAgainYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="EP1310_PlayDetailsAgainYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="EP1310_ini_03">
                <prompt-segments>
                  <audiofile text="All right, here are the details for your plan " src="EP1310_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_EP1310_PlayDetailsAgain"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1310_ini_08">
                <prompt-segments>
                  <audiofile text="Would you like to hear those details again? " src="EP1310_ini_08.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="EP1310_PlayDetailsAgainYN_DM_reinvoke"/>
          <repeatprompts count="1">
            <audio>
              <prompt id="EP1310_ini_03">
                <prompt-segments>
                  <audiofile text="All right, here are the details for your plan " src="EP1310_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_EP1310_PlayDetailsAgain"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1310_ini_08">
                <prompt-segments>
                  <audiofile text="Would you like to hear those details again? " src="EP1310_ini_08.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="EP1310_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to hear the details for your new plan again? " src="EP1310_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1310_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to hear the details of your new plan once more, say yes or press 1 If you d just like to move on, say no or press 2 " src="EP1310_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1310_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to hear the details of your new plan once more, say yes or press 1 If you d just like to move on, say no or press 2 " src="EP1310_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1310_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to hear the details for your new plan again? " src="EP1310_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1310_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to hear the details of your new plan once more, say yes or press 1 If you d just like to move on, say no or press 2 " src="EP1310_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1310_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to hear the details of your new plan once more, say yes or press 1 If you d just like to move on, say no or press 2 " src="EP1310_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="EP1310_ree_01">
                <prompt-segments>
                  <audiofile text="I ll read the details again " src="EP1310_ree_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_EP1310_PlayDetailsAgain"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1310_ree_02">
                <prompt-segments>
                  <audiofile text="Do you want to hear your plan information once more?  If not, say no and we ll continue " src="EP1310_ree_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="EP1205_ConfirmNewPlan_DM" type="YSNO">
      <session-mapping key="plan" value="GlobalVars.selectedPlan" type="String"/>
      <session-mapping key="audioMessageKey" value="care_configurable_rate_plan_downgrade_audio" type="String"/>
      <success>
        <action label="true">
          <audio>
            <prompt id="EP1205_out_01">
              <prompt-segments>
                <audiofile text="Great! " src="EP1205_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.newRatePlan" expr="plan"/>
          <session-mapping key="GlobalVars.newPlanSOC" expr="plan"/>
          <action next="EP1225_DetectConflictingOffers_SD"/>
        </action>
        <action label="false">
          <if cond="noToConfirmPlan &lt; 2">
            <audio>
              <prompt id="EP1205_out_02">
                <prompt-segments>
                  <audiofile text="Sorry about that " src="EP1205_out_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <session-mapping key="noToConfirmPlan" expr="noToConfirmPlan + 1"/>
            <action next="EP1110_ChooseNewPlanShort_DM"/>
            <else>
              <audio>
                <prompt id="EP1205_out_03">
                  <prompt-segments>
                    <audiofile text="I'm sorry for this trouble " src="EP1205_out_03.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="EP1020_CallTransfer_SD"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_300ms">
                <prompt-segments>
                  <audiofile text="" src="silence_300ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1205_ini_01">
                <prompt-segments>
                  <audiofile text="Just to be sure, that's " src="EP1205_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="EP1205_ini_02">
                <prompt-segments>
                  <audiofile text="Is that right? " src="EP1205_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_300ms">
                <prompt-segments>
                  <audiofile text="" src="silence_300ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1205_ini_01">
                <prompt-segments>
                  <audiofile text="Just to be sure, that's " src="EP1205_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="EP1205_ini_02">
                <prompt-segments>
                  <audiofile text="Is that right? " src="EP1205_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="EP1205_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say 'yes' or 'no'  You wanted " src="EP1205_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="EP1205_nm1_03">
                <prompt-segments>
                  <audiofile text="Is that right? " src="EP1205_nm1_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1205_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'yes' or press 1, or say 'no' or press 2  You wanted " src="EP1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="EP1205_nm2_03">
                <prompt-segments>
                  <audiofile text="Did I get that right? " src="EP1205_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1205_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'yes' or press 1, or say 'no' or press 2  You wanted " src="EP1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="EP1205_nm2_03">
                <prompt-segments>
                  <audiofile text="Did I get that right? " src="EP1205_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1205_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say 'yes' or 'no'  You wanted " src="EP1205_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="EP1205_nm1_03">
                <prompt-segments>
                  <audiofile text="Is that right? " src="EP1205_nm1_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1205_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'yes' or press 1, or say 'no' or press 2  You wanted " src="EP1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="EP1205_nm2_03">
                <prompt-segments>
                  <audiofile text="Did I get that right? " src="EP1205_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1205_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'yes' or press 1, or say 'no' or press 2  You wanted " src="EP1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1205_nm3_01">
                <prompt-segments>
                  <audiofile text="If you wanted " src="EP1205_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="EP1205_nm3_03">
                <prompt-segments>
                  <audiofile text="say 'yes' or press 1  If not, say 'no' or press 2 " src="EP1205_nm3_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_300ms">
                <prompt-segments>
                  <audiofile text="" src="silence_300ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1205_ini_01">
                <prompt-segments>
                  <audiofile text="Just to be sure, that's " src="EP1205_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="EP1205_ini_02">
                <prompt-segments>
                  <audiofile text="Is that right? " src="EP1205_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="EP1205_ConfirmNewPlan_DM.grxml" count="1"/>
          <dtmfgrammars filename="EP1205_ConfirmNewPlan_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="EP1215_DisambiguatePlan_SD">
      <session-mapping key="GlobalVars.disambigNoMatches" value="false" type="Boolean"/>
      <gotodialog next="DisambiguatePlan_Main_Dialog"/>
      <action next="EP1215_DisambiguatePlan_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="EP1215_DisambiguatePlan_SD_return_CS">
      <if cond="GlobalVars.disambigNoMatches == true">
        <session-mapping key="newPlanShortEntryFrom" expr="'EP1215_DisambiguatePlan_SD_return'"/>
        <action next="EP1110_ChooseNewPlanShort_DM"/>
        <else>
          <session-mapping key="GlobalVars.selectedPlan" expr="GlobalVars.newPlanSOC"/>
          <action next="EP1225_DetectConflictingOffers_SD"/>
        </else>
      </if>
    </custom-state>

    <dm-state id="EP1106_OfferOnePlanYN_DM" type="CUST">
      <session-mapping key="ratePlansLength" value="GlobalVars.ratePlans.length" type="String"/>
      <session-mapping key="audioMessageKey" value="care_configurable_rate_plan_downgrade_audio" type="String"/>
      <session-mapping key="soc" value="ratePlansLength == 1 ? GlobalVars.ratePlans[0].soc : GlobalVars.newPlanSOC " type="String"/>
      <success>
        <action label="yes">
          <audio>
            <prompt id="EP1106_out_01">
              <prompt-segments>
                <audiofile text="All right" src="EP1106_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.offerRepeatESNPlan" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.selectedPlan" expr="soc"/>
          <session-mapping key="GlobalVars.newRatePlan" expr="soc"/>
          <action next="EP1225_DetectConflictingOffers_SD"/>
        </action>
        <action label="no">
          <if cond="(ratePlansLength == 1)">
            <audio>
              <prompt id="EP1106_out_02">
                <prompt-segments>
                  <audiofile text="Okay" src="EP1106_out_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="EP1020_CallTransfer_SD"/>
            <else>
              <audio>
                <prompt id="EP1106_out_03">
                  <prompt-segments>
                    <audiofile text="No problem" src="EP1106_out_03.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="EP1110_ChooseNewPlanShort_DM"/>
            </else>
          </if>
        </action>
        <action label="repeat">
          <session-mapping key="GlobalVars.offerRepeatESNPlan" value="false" type="Boolean"/>
          <action next="EP1106_OfferOnePlanYN_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="ratePlansLength == 1">
                <prompt id="EP1106_ini_01">
                  <prompt-segments>
                    <audiofile text="There's one plan we have available now" src="EP1106_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="EP1106_ini_02">
                    <prompt-segments>
                      <audiofile text="There's a plan at the same price you pay now" src="EP1106_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_EP1106"/>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1106_ini_05">
                <prompt-segments>
                  <audiofile text="Should I switch you to this plan? You can also say 'repeat'" src="EP1106_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="EP1106_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to switch to this plan? To hear about it again, say 'repeat'" src="EP1106_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1106_nm2_01">
                <prompt-segments>
                  <audiofile text="To continue with the plan I just told you about, say 'yes' or press 1 If you don't like it, say 'no' or press 2 To hear its details again, say 'repeat' or press 3" src="EP1106_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1106_nm2_01">
                <prompt-segments>
                  <audiofile text="To continue with the plan I just told you about, say 'yes' or press 1 If you don't like it, say 'no' or press 2 To hear its details again, say 'repeat' or press 3" src="EP1106_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1106_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to switch to this plan? To hear about it again, say 'repeat'" src="EP1106_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1106_nm2_01">
                <prompt-segments>
                  <audiofile text="To continue with the plan I just told you about, say 'yes' or press 1 If you don't like it, say 'no' or press 2 To hear its details again, say 'repeat' or press 3" src="EP1106_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1106_nm2_01">
                <prompt-segments>
                  <audiofile text="To continue with the plan I just told you about, say 'yes' or press 1 If you don't like it, say 'no' or press 2 To hear its details again, say 'repeat' or press 3" src="EP1106_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="ratePlansLength == 1">
                <prompt id="EP1106_ini_01">
                  <prompt-segments>
                    <audiofile text="There's one plan we have available now" src="EP1106_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="EP1106_ini_02">
                    <prompt-segments>
                      <audiofile text="There's a plan at the same price you pay now" src="EP1106_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_EP1106"/>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EP1106_ini_05">
                <prompt-segments>
                  <audiofile text="Should I switch you to this plan? You can also say 'repeat'" src="EP1106_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
          <grammars filename="EP1106_OfferOnePlanYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="EP1106_OfferOnePlanYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.600" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.600" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.600">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.600" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  