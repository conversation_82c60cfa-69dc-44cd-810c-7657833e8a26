<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="getTopUpHistory" value="empty" type="String"/>
  <session-mapping key="payDate" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="isSuspended" value="empty" type="String"/>
  <session-mapping key="isBARTET" value="empty" type="String"/>
  <session-mapping key="dataUsage_dataCap_KB" value="empty" type="String"/>
  <session-mapping key="topUpHotSpot_dataCap_KB" value="empty" type="String"/>
  <session-mapping key="dataUsage_dataUsed_KB" value="empty" type="String"/>
  <session-mapping key="topUpHotSpot_dataUsed_KB" value="empty" type="String"/>
  <session-mapping key="GlobalVars.enteredFrom" value="WNP" type="string"/>
  <session-mapping key="GlobalVars.aniMatch" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="suspended" type="string"/>
  <session-mapping key="GlobalVars.serviceDialed" value="888-111" type="string"/>
  <session-mapping key="isDataCollectionEnabled" value="true" type="boolean"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="GlobalVars.returningFromPayments" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.GetAccountDetails" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.callType" value="undefined" type="string"/>
  <session-mapping key="GetAccountDetails.accountType" value="PIA" type="string"/>
  <session-mapping key="GlobalVars.switchLinesSuccess" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.parentDeviceType" value="INT" type="string"/>
  <session-mapping key="GlobalVars.lastCustomerIntent" value="undefined" type="string"/>
  <session-mapping key="GetBCSParameters.care_enable_proactive_balance" value="true" type="boolean"/>
  <session-mapping key="hasAutopay" value="false" type="string"/>
  <session-mapping key="balance" value="0" type="integer"/>
  <session-mapping key="dueImmediatelyAmount" value="0" type="string"/>
  <session-mapping key="remainingDays" value="20" type="integer"/>
  <session-mapping key="autoEligPlan" value="true" type="boolean"/>
  <session-mapping key="totalAmountDue" value="0" type="integer"/>
  <session-mapping key="paymentStatus" value="expire_within_week" type="string"/>
  <session-mapping key="accountBalance" value="0" type="integer"/>
  <session-mapping key="payDateDay" value="1" type="string"/>
  <session-mapping key="GlobalVars.tag" value="request-extension" type="string"/>
  <session-mapping key="GetBCSParameters.care_nlu_enabled" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.securityQuestionCode" value="SQ9" type="string"/>
  <session-mapping key="GetBCSParameters.care_enable_proactive_data" value="true" type="boolean"/>
  <session-mapping key="hasHighSpeedData" value="FromRatePlan" type="string"/>
  <session-mapping key="GetDataUsage.status" value="Success" type="string"/>
  <session-mapping key="dataUsage_isUnlimited" value="true" type="boolean"/>
  <session-mapping key="topUpHotSpot_dataFound" value="true" type="string"/>
  <session-mapping key="topUpHotSpot_dataUsed_KB" value="0" type="string"/>
</session-mappings>
