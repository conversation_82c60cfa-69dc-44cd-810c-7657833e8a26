import xml.etree.ElementTree as ET
import re, os
from xml.dom import minidom
from share_methods import read_filenames_in_folder, delete_existing_files


def determine_type(value):
    """Determine the type of the value."""
    if value.lower() in ["true", "false"]:
        return "Boolean"
    elif value.isdigit():
        return "Integer"
    else:
        return "String"

def extract_conditions(input_file, output_file):
    # Parse the XML file
    tree = ET.parse(input_file)
    root = tree.getroot()

    # Create a new root for the output XML
    output_root = ET.Element("conditions")

    # Dictionary to store the first occurrence of each variable
    variables_dict = {}

    # Function to process each condition
    def process_condition(condition, parent_element):
        cond_text = condition.get("cond")
        if cond_text:
            # Split the condition into individual sub-conditions based on AND/OR operators
            sub_conditions = re.split(r'\s*(&&|\|\|)\s*', cond_text)
            for sub_cond in sub_conditions:
                # Extract variable-value pairs for the current sub-condition
                matches = re.findall(r"(\w+\(.*?\)|\w+)\s*([!=><]+)\s*'?(.*?)'?(?=\s*[\)&\|]|\s*$)", sub_cond)
                for match in matches:
                    variable, operator, value = match
                    # Remove parentheses from the variable name if present
                    cleaned_variable = re.sub(r'\(\)', '', variable)

                    # Extract the first part before the operator
                    first_part = sub_cond.split(operator)[0].strip()

                    # Clean the first part to remove parentheses from the variable
                    cleaned_first_part = re.sub(r'\(\)', '', first_part)

                    # Check if the cleaned variable is already in the dictionary
                    if cleaned_variable not in variables_dict:
                        variables_dict[cleaned_variable] = value
                        # Determine the type of the value
                        value_type = determine_type(value)
                        # Create a new XML element with the specified format
                        session_mapping = ET.SubElement(parent_element, "session-mapping")
                        session_mapping.set("key", cleaned_first_part)
                        session_mapping.set("value", value)
                        session_mapping.set("type", value_type)

    # Traverse the XML to find 'if', 'elseif', and 'else' tags
    for element in root.iter():
        if element.tag in ["if", "elseif", "else"]:
            process_condition(element, output_root)

    # Convert the ElementTree to a string
    rough_string = ET.tostring(output_root, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    pretty_xml_as_string = reparsed.toprettyxml(indent="  ")

    # Write the prettified XML to the output file
    with open(output_file, 'w') as f:
        f.write(pretty_xml_as_string)

    print(f"Extracted conditions saved to: {output_file}")


# # TO RUN INDIVIDUAL FILE
# input_file = 'input/dialogs_file/vptransfer.xml'  # Input XML file
# variable_file = 'input/variable/vptransfer_variable.xml'  # Output XML file

# extract_conditions(input_file, variable_file)

# ONE GO RUN
input_file = 'input/dialogs_file/'  
variable_file = 'input/variable/' 

delete_existing_files(variable_file)
files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(input_file)]
for file in files:
 extract_conditions(input_file+file+'.xml', variable_file+file+'_variable.xml')
 




