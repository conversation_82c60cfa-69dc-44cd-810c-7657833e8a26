<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="PromotionCenterLine_Dialog">
    <decision-state id="PL1001_CheckContext_DS">
      <if cond="GlobalVars.tag == 'inquire-rebate'">
        <action next="PL1025_PlayMoreRebateInfo_PP"/>
        <else>
          <action next="PL1005_WelcomeMessage_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="PL1015_PlayRebateInfo_PP">
      <audio>
        <prompt id="PL1015_out_01">
          <prompt-segments>
            <audiofile text="You can get answers to frequently asked questions about promotional rebate and check the status of your rebate on our website Just visit promo dot metro by t dash mobile dot com That was promo dot metro by t dash mobile dot com" src="PL1015_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="language == 'en-US'">
        <action next="PL1020_GoToInitialHandling_SD"/>
        <else>
          <action next="PL1021_SpanishPromoMenu_DM"/>
        </else>
      </if>
    </play-state>

    <play-state id="PL1005_WelcomeMessage_PP">
      <audio>
        <prompt id="PL1005_out_01">
          <prompt-segments>
            <audiofile text="Thank you for calling the Metro by T-Mobile Promotion Center!" src="PL1005_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PL1010_AskLanguage_DM"/>
    </play-state>

    <dm-state id="PL1010_AskLanguage_DM" type="CUST">
      <success>
        <action label="Spanish">
          <session-mapping key="language" expr="'es-US'"/>
          <gotodialog next="PromotionCenterLine_Dialog#PL1015_PlayRebateInfo_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PL1010_ini_01">
                <prompt-segments>
                  <audiofile text="Para continuar en español, marque '9'" src="PL1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PL1010_ini_01">
                <prompt-segments>
                  <audiofile text="Para continuar en español, marque '9'" src="PL1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <dtmfgrammars filename="PL1010_AskLanguage_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="PL1025_PlayMoreRebateInfo_PP">
      <audio>
        <prompt id="PL1025_out_01">
          <prompt-segments>
            <audiofile text="If you have not received your rebate for your Metro by T-Mobile promotion, please make sure you have completed all steps in the rebate process To do this, click the promotion link that we sent you in the initial SMS text message And make sure to provide all required information" src="PL1025_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PL1030_AskHearAgain_DM"/>
    </play-state>

    <dm-state id="PL1030_AskHearAgain_DM" type="YSNO">
      <success>
        <action label="true" next="PL1025_PlayMoreRebateInfo_PP"/>
        <action label="false" next="PL1035_GoToGoobye_SD"/>
        <action label="operator">
          <session-mapping key="GlobalVars.callerSaidOperator" value="true" type="Boolean"/>
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'Promo_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'Promo_Spanish'"/>
            </else>
          </if>
          <gotodialog next="Init_Main#MN1105_GoToCallTransfer_SD"/>
        </action>
        <action label="MainMenu">
          <if cond="language == 'en-US'">
            <action next="PL1040_GoToNLU_SD"/>
            <else>
              <action next="PL1020_GoToInitialHandling_SD"/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PL1030_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? If you're done, you can simply hang up" src="PL1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PL1030_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? If you're done, you can simply hang up" src="PL1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PL1030_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? If you're done, you can simply hang up" src="PL1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PL1030_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again? If you're done, you can simply hang up" src="PL1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat_PL1030.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="PL1030_AskHearAgain_DM.grxml" count="1"/>
          <dtmfgrammars filename="PL1030_AskHearAgain_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="PL1020_GoToInitialHandling_SD">
      <gotodialog next="InitialHandling_Main_Dialog"/>
      <action next="Pl1020_GoToInitialHandling_SDReturn"/>
    </subdialog-state>
    <custom-state id="PL1020_GoToInitialHandling_SDReturn_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="PL1040_GoToNLU_SD">
      <gotodialog next="NLUMainMenu_Start_Dialog"/>
      <action next="PL1040_GoToNLU_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="PL1040_GoToNLU_SDReturn_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="PL1035_GoToGoobye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="PL1035_GoToGoobye_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="PL1035_GoToGoobye_SDReturn_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <dm-state id="PL1021_SpanishPromoMenu_DM" type="CUST">
      <success>
        <action label="repeat" next="PL1015_PlayRebateInfo_PP"/>
        <action label="rebate_status" next="PL1025_PlayMoreRebateInfo_PP">
          <audio>
            <prompt id="PL1021_out_01">
              <prompt-segments>
                <audiofile text="" src="PL1021_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="main_menu" next="PL1020_GoToInitialHandling_SD"/>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PL1021_ini_01">
                <prompt-segments>
                  <audiofile text="" src="PL1021_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="PL1021_SpanishPromoMenu_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PL1021_ini_01">
                <prompt-segments>
                  <audiofile text="" src="PL1021_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PL1021_ini_01">
                <prompt-segments>
                  <audiofile text="" src="PL1021_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PL1021_nm2_01">
                <prompt-segments>
                  <audiofile text="" src="PL1021_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PL1021_nm2_01">
                <prompt-segments>
                  <audiofile text="" src="PL1021_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PL1021_ini_01">
                <prompt-segments>
                  <audiofile text="" src="PL1021_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PL1021_nm2_01">
                <prompt-segments>
                  <audiofile text="" src="PL1021_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PL1021_nm2_01">
                <prompt-segments>
                  <audiofile text="" src="PL1021_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PL1021_ini_01">
                <prompt-segments>
                  <audiofile text="" src="PL1021_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="PL1021_SpanishPromoMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="PL1021_SpanishPromoMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="PL1021_SpanishPromoMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  