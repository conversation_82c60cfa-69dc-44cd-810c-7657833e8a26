#!/usr/bin/env python3
import xml.etree.ElementTree as ET
import os
import logging

# Configure logging
logging.basicConfig(filename='debug_dm.log', level=logging.INFO, format='%(asctime)s - %(message)s')

def analyze_single_file(file_path):
    """Analyze a single XML file for dm-state elements"""
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        file_name = os.path.basename(file_path)
        
        # Debug: Log root element and all child element tags
        logging.info(f"Root element: {root.tag}, Total child elements: {len(root)}")
        all_tags = [element.tag for element in root]
        logging.info(f"All element tags in {file_name}: {set(all_tags)}")
        
        # Check if any elements contain 'dm' in their name (case insensitive)
        dm_related_tags = [tag for tag in all_tags if 'dm' in tag.lower()]
        if dm_related_tags:
            logging.info(f"DM-related tags found in {file_name}: {dm_related_tags}")
        
        dm_count = 0
        for element in root:
            if element.tag == "dm-state":
                dm_count += 1
                logging.info(f"Found dm-state: {element.get('id', 'no-id')} in {file_name}")
        
        print(f"{file_name}: {dm_count} dm-state elements found")
        logging.info(f"Total dm-states found in {file_name}: {dm_count}")
        return dm_count
        
    except Exception as e:
        logging.error(f"Error analyzing {file_path}: {e}")
        print(f"Error analyzing {file_path}: {e}")
        return 0

if __name__ == "__main__":
    # Test with just a few files that we know should have dm-states
    test_files = [
        "UseWallet_Main_Dialog.xml",
        "Voicestore_Main_Dialog.xml", 
        "XferTwoFactorAuth_Dialog.xml"
    ]
    
    dialog_folder = "input/dialogs_file/"
    total_dm_states = 0
    
    for filename in test_files:
        file_path = os.path.join(dialog_folder, filename)
        if os.path.exists(file_path):
            count = analyze_single_file(file_path)
            total_dm_states += count
        else:
            print(f"File not found: {file_path}")
    
    print(f"\nTotal dm-states found across test files: {total_dm_states}")
    logging.info(f"Debug analysis complete. Total dm-states: {total_dm_states}")
