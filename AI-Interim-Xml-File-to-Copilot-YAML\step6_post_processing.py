import re
import logging
from app_run import  data_access_url
from run_all_in_one import local_run
logging.basicConfig(filename='agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')

# Function to handle dynamic variable under prompts
def process_yaml_file(input_file, output_file=None):
    """
    Process YAML file to replace {variable} with {Global.variable}
    only within speak: sections, ignoring System.Bot.Name and Switch statements
    """
    print("Starting post-processing of YAML file...")
    
    # If no output file specified, use the input file
    output_file = output_file or input_file
    
    try:
        # Read the YAML file
        with open(input_file, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        modified_lines = []
        in_speak_section = False
        
        for line in lines:
            # Check if we're entering a speak section
            if 'speak:' in line:
                in_speak_section = True
                modified_lines.append(line)
                continue
            
            # If we're in a speak section and line starts with '-'
            if in_speak_section and line.strip().startswith('-'):
                # Skip processing if line contains a Switch statement
                if '{Switch(' in line:
                    modified_lines.append(line)
                    continue
                
                # Pattern to find {text} where text:
                # - doesn't start with Global.
                # - isn't System.Bot.Name
                pattern = r'\{(?!Global\.)(?!System\.Bot\.Name)([^}]+)\}'
                
                def replacement(match):
                    variable = match.group(1)
                    # Don't modify if it's System.Bot.Name
                    if variable == 'System.Bot.Name':
                        return f"{{{variable}}}"
                    new_format = f"{{Global.{variable}}}"
                    print(f"Replacing {{{variable}}} with {new_format}")
                    return new_format
                
                # Apply replacements only to speak section content
                modified_line = re.sub(pattern, replacement, line)
                modified_lines.append(modified_line)
            else:
                # If line doesn't start with '-', we're no longer in speak section content
                if in_speak_section and not line.strip().startswith('-'):
                    in_speak_section = False
                modified_lines.append(line)
        
        # Write the modified content back to file
        with open(output_file, 'w', encoding='utf-8') as file:
            file.writelines(modified_lines)
            
        print("Post-processing completed successfully!")
        logging.info("YAML post-processing completed successfully")
        
    except Exception as e:
        print(f"Error during post-processing: {str(e)}")
        logging.error(f"Error during YAML post-processing: {str(e)}")
        raise

# Function to read Audio_location from app.properties and replace AUDIO_LOCATION in yaml_content
# def replace_audio_location_in_yaml(yaml_content, properties_file_path='app.properties'):
#     audio_location = None
#     properties = {}

#     with open(properties_file_path, 'r') as file:
#         for line in file:
#             if '=' in line:
#                 key, value = line.split('=', 1)
#                 properties[key.strip()] = value.strip()

#     required_keys = ['BASE_PATH', 'PROJECT_NAME', 'LANGUAGE', 'DEFAULT_PATH']
#     if all(key in properties for key in required_keys):
#         audio_location = f"{properties['BASE_PATH']}/{properties['PROJECT_NAME']}/{properties['LANGUAGE']}/{properties['DEFAULT_PATH']}"
#         yaml_content = yaml_content.replace('AUDIO_LOCATION', audio_location)
    
#     return yaml_content

# Function to read CREATE_SESSION_BASE_URL from app.properties and replace CREATE_SESSION_URL in yaml_content
def replace_url_in_yaml(yaml_content, properties_file_path='app.properties'):
    required_keys = ['BASE_PATH', 'DEFAULT_PATH']
    properties = {}

    with open(properties_file_path, 'r') as file:
        for line in file:
            if '=' in line:
                key, value = line.split('=', 1)
                properties[key.strip()] = value.strip()

    if all(key in properties for key in required_keys):
        if local_run:
            audio_location = f"{properties['BASE_PATH']}/{{Global.project_name}}/{{Global.language}}/{properties['DEFAULT_PATH']}"
            data_access_base_url = properties.get('DATA_ACCESS_URL')
            logging.info(f"Under local_run = True audio_location, data_access_url : {audio_location, data_access_base_url}")
        else: 
            #audio_location = f"{properties['BASE_PATH']}/{project_name}/{language}/{properties['DEFAULT_PATH']}"
            data_access_base_url = data_access_url
            logging.info(f"Under local_run = false audio_location, create_session_url : {audio_location, data_access_base_url}")
        
        yaml_content = yaml_content.replace('AUDIO_LOCATION', audio_location)
        yaml_content = yaml_content.replace('DATA_ACCESS_URL', data_access_base_url)
    
    return yaml_content


# Function to add double quotes and escape characters into YAML content
def add_quotes_and_escape_characters(yaml_content):
    def escape_audio_tag(match):
        audio_tag = match.group(0)
        # Check if the audio tag already contains escaped quotes
        if '\\"' in audio_tag:
            return audio_tag
        escaped_audio_tag = audio_tag.replace('"', '\\"')
        return f'"{escaped_audio_tag}"'

    # Regex to find <audio> tags
    audio_tag_pattern = re.compile(r'<audio.*?>.*?</audio>', re.DOTALL)
    updated_content = re.sub(audio_tag_pattern, escape_audio_tag, yaml_content)
    return updated_content


def update_yaml_file(file_path):
    with open(file_path, 'r') as file:
        yaml_content = file.read()
    updated_content1 = replace_url_in_yaml(yaml_content)
    updated_content2 = add_quotes_and_escape_characters(updated_content1)
    return updated_content2



if __name__ == "__main__":
    # Process the final YAML file
    yaml_file = 'output/bot_yaml/final.yml'
    updated_yaml_content = update_yaml_file(yaml_file)
    with open(yaml_file, 'w') as file:
        file.write(updated_yaml_content)
    process_yaml_file(yaml_file)