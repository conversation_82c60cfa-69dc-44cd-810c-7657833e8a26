<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="MetroPaymentCard_Main_Dialog">
    <decision-state id="ME1001_CheckContext_DS">
      <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
      <action next="ME1005_GetMetroCardPIN_DM"/>
    </decision-state>

    <dm-state id="ME1005_GetMetroCardPIN_DM" type="DIGT">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorME1005?GlobalVars.saidOperatorME1005:false" type="String"/>
      <session-mapping key="firstInvalidPIN" value="GlobalVars.firstInvalidPIN" type="String"/>
      <session-mapping key="metroCardFail" value="GlobalVars.metroCardFail" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <action label="default">
          <session-mapping key="GlobalVars.saidOperatorME1005" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.metroCardPIN" expr="ME1005_GetMetroCardPIN_DM.returnvalue"/>
          <if cond="GlobalVars.metroCardPIN == GlobalVars.firstInvalidPIN">
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
            <action next="ME1035_CallTransfer_SD"/>
          </if>
          <action next="ME1010_ValidateMetroPaymentCard_DB_DA"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperatorME1005" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ME1005_ini_01">
                <prompt-segments>
                  <audiofile text="Sure" src="ME1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1005_ini_04" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="Just so you know, we will add the full value of this card to your account" src="ME1005_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1005_ini_02">
                <prompt-segments>
                  <audiofile text="What's the 10-digit PIN on the card? You can say OR enter it" src="ME1005_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_2000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1005_ini_03">
                <prompt-segments>
                  <audiofile text="You can also say 'more info'" src="ME1005_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="ME1005_GetMetroCardPIN_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="ME1005_GetMetroCardPIN_DM_help"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="ME1005_rin_01">
                  <prompt-segments>
                    <audiofile text="Please enter the 10-digit PIN for your card" src="ME1005_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="metroCardFail == true">
                  <prompt id="ME1005_rin_02">
                    <prompt-segments>
                      <audiofile text="Actually I got" src="ME1005_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="custom" expr="firstInvalidPIN">
                    <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="ME1005_rin_03">
                    <prompt-segments>
                      <audiofile text="and I can't validate that PIN Please enter it again using your keypad" src="ME1005_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="ME1005_rin_04">
                    <prompt-segments>
                      <audiofile text="What's the 10-digit PIN on the card?  You can say OR enter it" src="ME1005_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_2000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1005_rin_05">
                <prompt-segments>
                  <audiofile text="You can also say 'more info'" src="ME1005_rin_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ME1005_nm1_01">
                <prompt-segments>
                  <audiofile text="What's the 10-digit PIN for the card? You can also say 'more info'" src="ME1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="ME1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say or enter the 10-digit PIN for the card Or say 'more info' or press star" src="ME1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="ME1005_nm3_01">
                <prompt-segments>
                  <audiofile text="Using your keypad, enter teh 10-digit PIN for this card If you're not sure where to find it, press star" src="ME1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1005_nm1_01">
                <prompt-segments>
                  <audiofile text="What's the 10-digit PIN for the card? You can also say 'more info'" src="ME1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say or enter the 10-digit PIN for the card Or say 'more info' or press star" src="ME1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1005_nm3_01">
                <prompt-segments>
                  <audiofile text="Using your keypad, enter teh 10-digit PIN for this card If you're not sure where to find it, press star" src="ME1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="ME1005_rin_01">
                  <prompt-segments>
                    <audiofile text="Please enter the 10-digit PIN for your card" src="ME1005_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="metroCardFail == true">
                  <prompt id="ME1005_rin_02">
                    <prompt-segments>
                      <audiofile text="Actually I got" src="ME1005_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="custom" expr="firstInvalidPIN">
                    <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="ME1005_rin_03">
                    <prompt-segments>
                      <audiofile text="and I can't validate that PIN Please enter it again using your keypad" src="ME1005_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="ME1005_rin_04">
                    <prompt-segments>
                      <audiofile text="What's the 10-digit PIN on the card?  You can say OR enter it" src="ME1005_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_2000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1005_rin_05">
                <prompt-segments>
                  <audiofile text="You can also say 'more info'" src="ME1005_rin_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="ME1005_GetMetroCardPIN_DM.grxml" count="1"/>
          <dtmfgrammars filename="ME1005_GetMetroCardPIN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="16000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="2000ms" timeout="16000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="ME1010_ValidateMetroPaymentCard_DB_DA">
      <session-mapping key="pin" value="GlobalVars.metroCardPIN" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="InquireIncommCard" classname="com.nuance.metro.dataaccess.InquireIncommCard">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="pin" mask="true"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="message"/>
          <output-variable name="balance"/>
          <output-variable name="faceValue"/>
          <output-variable name="upc"/>
          <output-variable name="cardType"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.InquireIncommCard" expr="InquireIncommCard"/>
        <action next="ME1015_CheckMetroCardResults_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="ME1015_CheckMetroCardResults_DS">
      <session-mapping key="cardBalance" value="GlobalVars.InquireIncommCard.faceValue" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="(GlobalVars.GetAccountDetails == null)?0:GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <if cond="(GlobalVars.InquireIncommCard) &amp;&amp; (GlobalVars.InquireIncommCard.status == 'Success') &amp;&amp; (GlobalVars.InquireIncommCard.cardStatus == 'ACTIVE')">
        <if cond="parseFloat(GlobalVars.InquireIncommCard.faceValue) &lt; parseFloat(GlobalVars.paymentAmount)">
          <session-mapping key="GlobalVars.tryOtherCardReason " expr="'insufficient_funds'"/>
          <if cond="callType != 'activate' &amp;&amp; (parseFloat(GlobalVars.InquireIncommCard.faceValue) &gt;= parseFloat(dueImmediatelyAmount))">
            <action next="ME1040_PayCardAmount_DM"/>
            <else>
              <action next="ME1025_UseOtherCard_DM"/>
            </else>
          </if>
          <else>
            <action next="ME1020_MetroCardSuccess_PP"/>
          </else>
        </if>
        <else>
          <if cond="(GlobalVars.InquireIncommCard) &amp;&amp; (GlobalVars.InquireIncommCard.status == 'Success') &amp;&amp; (GlobalVars.InquireIncommCard.cardStatus == 'REDEEMED')">
            <session-mapping key="GlobalVars.tryOtherCardReason" expr="'pin_invalid'"/>
            <action next="ME1025_UseOtherCard_DM"/>
          </if>
          <if cond="(GlobalVars.metroCardFail == false) || (GlobalVars.metroCardFail == undefined)">
            <session-mapping key="GlobalVars.metroCardFail" value="true" type="Boolean"/>
            <session-mapping key="GlobalVars.firstInvalidPIN" expr="GlobalVars.metroCardPIN"/>
            <action next="ME1005_GetMetroCardPIN_DM"/>
            <else>
              <session-mapping key="GlobalVars.tryOtherCardReason" expr="'pin_invalid'"/>
              <session-mapping key="GlobalVars.metroCardFail" value="false" type="Boolean"/>
              <action next="ME1025_UseOtherCard_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="ME1020_MetroCardSuccess_PP">
      <session-mapping key="cardBalance" value="GlobalVars.InquireIncommCard.faceValue" type="String"/>
      <session-mapping key="remainingBalance" value="(parseFloat(GlobalVars.InquireIncommCard.faceValue) - parseFloat(GlobalVars.paymentAmount)).toString()" type="String"/>
      <audio>
        <prompt id="ME1020_out_01" cond="(remainingBalance &gt; 0)">
          <prompt-segments>
            <audiofile text="Thanks" src="ME1020_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_02" cond="(remainingBalance &gt; 0) &amp;&amp; (cardBalance == 45)">
          <prompt-segments>
            <audiofile text="By the way, this Metro PCS Metro payment card was for $45" src="ME1020_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_03" cond="(remainingBalance &gt; 0) &amp;&amp; (cardBalance == 50)">
          <prompt-segments>
            <audiofile text="By the way, this Metro PCS Metro payment card was for $50" src="ME1020_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_04" cond="(remainingBalance &gt; 0) &amp;&amp; (cardBalance == 60)">
          <prompt-segments>
            <audiofile text="By the way, this Metro PCS Metro payment card was for $60" src="ME1020_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_05" cond="(remainingBalance &gt; 0) &amp;&amp; (cardBalance == 70)">
          <prompt-segments>
            <audiofile text="By the way, this Metro PCS Metro payment card was for $70" src="ME1020_out_05.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_06" cond="(remainingBalance &gt; 0) &amp;&amp; (cardBalance == 90)">
          <prompt-segments>
            <audiofile text="By the way, this Metro PCS Metro payment card was for $90" src="ME1020_out_06.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_07" cond="(remainingBalance &gt; 0) &amp;&amp; (!((cardBalance == 45) || (cardBalance == 50)|| (cardBalance == 60)|| (cardBalance == 70)|| (cardBalance == 90)))">
          <prompt-segments>
            <audiofile text="By the way, this Metro PCS Metro payment card was for" src="ME1020_out_07.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="currency" expr="cardBalance" cond="(remainingBalance &gt; 0) &amp;&amp; (!((cardBalance == 45) || (cardBalance == 50)|| (cardBalance == 60)|| (cardBalance == 70)|| (cardBalance == 90)))">
          <param value="f" name="intonation"/>
          <param value="false" name="playZeroCents"/>
        </prompt>
        <prompt id="ME1020_out_08" cond="(remainingBalance &gt; 0) &amp;&amp; (remainingBalance == 5)">
          <prompt-segments>
            <audiofile text="So you'll have 5 extra dollars on your account" src="ME1020_out_08.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_09" cond="(remainingBalance &gt; 0) &amp;&amp; (remainingBalance == 10)">
          <prompt-segments>
            <audiofile text="So you'll have 10 extra dollars on your account" src="ME1020_out_09.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_10" cond="(remainingBalance &gt; 0) &amp;&amp; (remainingBalance == 15)">
          <prompt-segments>
            <audiofile text="So you'll have 15 extra dollars on your account" src="ME1020_out_10.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_11" cond="(remainingBalance &gt; 0) &amp;&amp; (remainingBalance == 20)">
          <prompt-segments>
            <audiofile text="So you'll have 20 extra dollars on your account" src="ME1020_out_11.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_12" cond="(remainingBalance &gt; 0) &amp;&amp; (remainingBalance == 25)">
          <prompt-segments>
            <audiofile text="So you'll have 25 extra dollars on your account" src="ME1020_out_12.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_13" cond="(remainingBalance &gt; 0) &amp;&amp; (remainingBalance == 30)">
          <prompt-segments>
            <audiofile text="So you'll have 30 extra dollars on your account" src="ME1020_out_13.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_14" cond="(remainingBalance &gt; 0) &amp;&amp; (!((remainingBalance == 5) || (remainingBalance == 10) ||(remainingBalance == 15) ||(remainingBalance == 20) ||(remainingBalance == 25) ||(remainingBalance == 30)))">
          <prompt-segments>
            <audiofile text="So your account will have an extra" src="ME1020_out_14.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="currency" expr="remainingBalance" cond="(remainingBalance &gt; 0) &amp;&amp; (!((remainingBalance == 5) || (remainingBalance == 10) ||(remainingBalance == 15) ||(remainingBalance == 20) ||(remainingBalance == 25) ||(remainingBalance == 30)))">
          <param value="f" name="intonation"/>
          <param value="false" name="playZeroCents"/>
        </prompt>
        <prompt id="silence_500ms" cond="(remainingBalance &gt; 0)">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ME1020_out_16" cond="!(remainingBalance &gt; 0)">
          <prompt-segments>
            <audiofile text="Thanks" src="ME1020_out_16.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms" cond="!(remainingBalance &gt; 0)">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <dm-state id="ME1025_UseOtherCard_DM" type="CUST">
      <session-mapping key="tryOtherCardReason" value="GlobalVars.tryOtherCardReason" type="String"/>
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorME1025?GlobalVars.saidOperatorME1025:false" type="String"/>
      <session-mapping key="cardStatus" value="(GlobalVars.InquireIncommCard)?(GlobalVars.InquireIncommCard.cardStatus):''" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="(GlobalVars.GetAccountDetails == null)?0:GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorME1025" value="false" type="Boolean"/>
        <action label="yes">
          <session-mapping key="GlobalVars.metroCardFail" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.firstInvalidPIN" expr="undefined"/>
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'not_set'"/>
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="'fail_metrocard'"/>
          <audio>
            <prompt id="ME1025_out_01">
              <prompt-segments>
                <audiofile text="Alright" src="ME1025_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="credit">
          <session-mapping key="GlobalVars.metroCardFail" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.firstInvalidPIN" expr="undefined"/>
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'credit'"/>
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="'fail_metrocard'"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'credit'"/>
          <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'83'"/>
          <audio>
            <prompt id="ME1025_out_02">
              <prompt-segments>
                <audiofile text="Alright" src="ME1025_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="debit">
          <session-mapping key="GlobalVars.metroCardFail" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.firstInvalidPIN" expr="undefined"/>
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'debit'"/>
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="'fail_metrocard'"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'debit'"/>
          <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'83'"/>
          <audio>
            <prompt id="ME1025_out_03">
              <prompt-segments>
                <audiofile text="Alright" src="ME1025_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="no" next="ME1030_GoTo_ErrorHandling_SD"/>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperatorME1025" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ME1025_ini_05" cond="tryOtherCardReason == 'pin_invalid' &amp;&amp; (cardStatus == 'REDEEMED')">
                <prompt-segments>
                  <audiofile text="Actually, it looks like this MetroPCS Payment card has already been redeemed There is no money left on it" src="ME1025_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1025_ini_01" cond="tryOtherCardReason == 'pin_invalid' &amp;&amp; !(cardStatus == 'REDEEMED')">
                <prompt-segments>
                  <audiofile text="I still can't validate that PIN" src="ME1025_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1025_ini_02" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType == 'activate'">
                <prompt-segments>
                  <audiofile text="Actually, that card doesn't have enough money to cover your first month's charges" src="ME1025_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1025_ini_06" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType != 'activate'">
                <prompt-segments>
                  <audiofile text="To keep your account active, you need to make a payment for at least" src="ME1025_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType != 'activate'">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="ME1025_ini_08" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType != 'activate'">
                <prompt-segments>
                  <audiofile text="And this card doesn't have enough money" src="ME1025_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1025_ini_03" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="And I wont be able to complete your activation without a payment" src="ME1025_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1025_ini_04">
                <prompt-segments>
                  <audiofile text="Would you like to pay another way?" src="ME1025_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="ME1025_UseOtherCard_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="ME1025_rin_01">
                  <prompt-segments>
                    <audiofile text="Would you like to pay with a debit or credit card?" src="ME1025_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="ME1025_ini_05" cond="tryOtherCardReason == 'pin_invalid' &amp;&amp; (cardStatus == 'REDEEMED')">
                    <prompt-segments>
                      <audiofile text="Actually, it looks like this MetroPCS Payment card has already been redeemed There is no money left on it" src="ME1025_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="ME1025_ini_01" cond="tryOtherCardReason == 'pin_invalid' &amp;&amp; !(cardStatus == 'REDEEMED')">
                    <prompt-segments>
                      <audiofile text="I still can't validate that PIN" src="ME1025_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="ME1025_ini_02" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType == 'activate'">
                    <prompt-segments>
                      <audiofile text="Actually, that card doesn't have enough money to cover your first month's charges" src="ME1025_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="ME1025_ini_06" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType != 'activate'">
                    <prompt-segments>
                      <audiofile text="To keep your account active, you need to make a payment for at least" src="ME1025_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="currency" expr="dueImmediatelyAmount" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType != 'activate'">
                    <param value="f" name="intonation"/>
                    <param value="false" name="playZeroCents"/>
                  </prompt>
                  <prompt id="ME1025_ini_08" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType != 'activate'">
                    <prompt-segments>
                      <audiofile text="And this card doesn't have enough money" src="ME1025_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="ME1025_ini_03" cond="callType == 'activate'">
                    <prompt-segments>
                      <audiofile text="And I wont be able to complete your activation without a payment" src="ME1025_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="ME1025_ini_04">
                    <prompt-segments>
                      <audiofile text="Would you like to pay another way?" src="ME1025_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ME1025_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay a different way?" src="ME1025_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="ME1025_nm2_01">
                <prompt-segments>
                  <audiofile text="If you can pay a different way, say yes or press 1 Otherwise, say no or press 2" src="ME1025_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="ME1025_nm3_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment If you have another MetroPCS payment card, or a credit or debit card you can use now, press 1 Otherwise, press 2" src="ME1025_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1025_nm3_02" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="I can't process your payment If you have another MetroPCS payment card, or a credit or debit card you can use now, press 1 Otherwise, press 2" src="ME1025_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1025_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay a different way?" src="ME1025_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1025_nm2_01">
                <prompt-segments>
                  <audiofile text="If you can pay a different way, say yes or press 1 Otherwise, say no or press 2" src="ME1025_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1025_nm3_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment If you have another MetroPCS payment card, or a credit or debit card you can use now, press 1 Otherwise, press 2" src="ME1025_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1025_nm3_02" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="I can't process your payment If you have another MetroPCS payment card, or a credit or debit card you can use now, press 1 Otherwise, press 2" src="ME1025_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="ME1025_rin_01">
                  <prompt-segments>
                    <audiofile text="Would you like to pay with a debit or credit card?" src="ME1025_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="ME1025_ini_05" cond="tryOtherCardReason == 'pin_invalid' &amp;&amp; (cardStatus == 'REDEEMED')">
                    <prompt-segments>
                      <audiofile text="Actually, it looks like this MetroPCS Payment card has already been redeemed There is no money left on it" src="ME1025_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="ME1025_ini_01" cond="tryOtherCardReason == 'pin_invalid' &amp;&amp; !(cardStatus == 'REDEEMED')">
                    <prompt-segments>
                      <audiofile text="I still can't validate that PIN" src="ME1025_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="ME1025_ini_02" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType == 'activate'">
                    <prompt-segments>
                      <audiofile text="Actually, that card doesn't have enough money to cover your first month's charges" src="ME1025_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="ME1025_ini_06" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType != 'activate'">
                    <prompt-segments>
                      <audiofile text="To keep your account active, you need to make a payment for at least" src="ME1025_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="currency" expr="dueImmediatelyAmount" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType != 'activate'">
                    <param value="f" name="intonation"/>
                    <param value="false" name="playZeroCents"/>
                  </prompt>
                  <prompt id="ME1025_ini_08" cond="tryOtherCardReason != 'pin_invalid' &amp;&amp; callType != 'activate'">
                    <prompt-segments>
                      <audiofile text="And this card doesn't have enough money" src="ME1025_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="ME1025_ini_03" cond="callType == 'activate'">
                    <prompt-segments>
                      <audiofile text="And I wont be able to complete your activation without a payment" src="ME1025_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="ME1025_ini_04">
                    <prompt-segments>
                      <audiofile text="Would you like to pay another way?" src="ME1025_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="ME1025_UseOtherCard_DM.grxml" count="1"/>
          <dtmfgrammars filename="ME1025_UseOtherCard_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="ME1030_GoTo_ErrorHandling_SD">
      <gotodialog next="ErrorHandlingPayment_Dialog"/>
      <action next="ME1030_GoTo_ErrorHandling_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ME1030_GoTo_ErrorHandling_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="ME1035_CallTransfer_SD">
      <if cond="GlobalVars.isCareTransfer == 'true'">
        <gotodialog next="CallTransfer_Main_Dialog"/>
        <else>
          <gotodialog next="Transfer_Main"/>
        </else>
      </if>
      <action next="ME1035_CallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ME1035_CallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <dm-state id="ME1040_PayCardAmount_DM" type="CUST">
      <session-mapping key="cardBalance" value="GlobalVars.InquireIncommCard.faceValue" type="String"/>
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount" type="String"/>
      <success>
        <action label="continue">
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
          <session-mapping key="GlobalVars.paymentAmount" expr="cardBalance"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="go_back">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
          <session-mapping key="GlobalVars.metroCardFail" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.firstInvalidPIN" expr="undefined"/>
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="'fail_metrocard'"/>
          <audio>
            <prompt id="ME1040_out_01">
              <prompt-segments>
                <audiofile text="Alright" src="ME1040_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ME1040_ini_01">
                <prompt-segments>
                  <audiofile text="You said you wanted to pay" src="ME1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="paymentAmount">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_02" cond="cardBalance == 10">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $10" src="ME1040_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_03" cond="cardBalance == 20">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $20" src="ME1040_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_04" cond="cardBalance == 35">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $35 " src="ME1040_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_05" cond="cardBalance == 40">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $40" src="ME1040_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_06" cond="cardBalance == 45">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $45" src="ME1040_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_07" cond="cardBalance == 50">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $50" src="ME1040_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_08" cond="cardBalance == 60">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $60" src="ME1040_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_09" cond="cardBalance == 70">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $70" src="ME1040_ini_09.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_10" cond="cardBalance == 90">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $90" src="ME1040_ini_10.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_11" cond="(cardBalance != 10 &amp;&amp; cardBalance != 20 &amp;&amp; cardBalance != 30 &amp;&amp; cardBalance != 40 &amp;&amp; cardBalance != 45 &amp;&amp; cardBalance != 50 &amp;&amp; cardBalance != 60 &amp;&amp; cardBalance != 70 &amp;&amp; cardBalance != 80 &amp;&amp; cardBalance != 90 )">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for" src="ME1040_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="cardBalance" cond="(cardBalance != 10 &amp;&amp; cardBalance != 20 &amp;&amp; cardBalance != 30 &amp;&amp; cardBalance != 40  &amp;&amp; cardBalance != 45 &amp;&amp; cardBalance != 50 &amp;&amp; cardBalance != 60 &amp;&amp; cardBalance != 70 &amp;&amp; cardBalance != 80 &amp;&amp; cardBalance != 90 )">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_12">
                <prompt-segments>
                  <audiofile text="To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up" src="ME1040_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ME1040_ini_01">
                <prompt-segments>
                  <audiofile text="You said you wanted to pay" src="ME1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="paymentAmount">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_02" cond="cardBalance == 10">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $10" src="ME1040_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_03" cond="cardBalance == 20">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $20" src="ME1040_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_04" cond="cardBalance == 35">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $35 " src="ME1040_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_05" cond="cardBalance == 40">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $40" src="ME1040_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_06" cond="cardBalance == 45">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $45" src="ME1040_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_07" cond="cardBalance == 50">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $50" src="ME1040_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_08" cond="cardBalance == 60">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $60" src="ME1040_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_09" cond="cardBalance == 70">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $70" src="ME1040_ini_09.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_10" cond="cardBalance == 90">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $90" src="ME1040_ini_10.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_11" cond="(cardBalance != 10 &amp;&amp; cardBalance != 20 &amp;&amp; cardBalance != 30 &amp;&amp; cardBalance != 40 &amp;&amp; cardBalance != 45 &amp;&amp; cardBalance != 50 &amp;&amp; cardBalance != 60 &amp;&amp; cardBalance != 70 &amp;&amp; cardBalance != 80 &amp;&amp; cardBalance != 90 )">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for" src="ME1040_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="cardBalance" cond="(cardBalance != 10 &amp;&amp; cardBalance != 20 &amp;&amp; cardBalance != 30 &amp;&amp; cardBalance != 40  &amp;&amp; cardBalance != 45 &amp;&amp; cardBalance != 50 &amp;&amp; cardBalance != 60 &amp;&amp; cardBalance != 70 &amp;&amp; cardBalance != 80 &amp;&amp; cardBalance != 90 )">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_12">
                <prompt-segments>
                  <audiofile text="To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up" src="ME1040_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ME1040_nm1_01">
                <prompt-segments>
                  <audiofile text="To pay the amount indicated on your card, say continue To pay a different way, say go back If you're done here, you can simply hang up" src="ME1040_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="ME1040_nm2_01">
                <prompt-segments>
                  <audiofile text="Your card has less money than you wanted to pay To pay the amount that's written on your card, say 'continue', or press 1 Otherwise, to pay the full amount a different way, say 'go back' or press 2 If you don't want to pay now, you can simply hang up" src="ME1040_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="ME1040_nm3_01">
                <prompt-segments>
                  <audiofile text="To pay the amount that's on your card, press 1 To go back and pay with a debit or credit card, press 2 If you don't want to pay now, you can simply hang up" src="ME1040_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_nm1_01">
                <prompt-segments>
                  <audiofile text="To pay the amount indicated on your card, say continue To pay a different way, say go back If you're done here, you can simply hang up" src="ME1040_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_nm2_01">
                <prompt-segments>
                  <audiofile text="Your card has less money than you wanted to pay To pay the amount that's written on your card, say 'continue', or press 1 Otherwise, to pay the full amount a different way, say 'go back' or press 2 If you don't want to pay now, you can simply hang up" src="ME1040_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_nm3_01">
                <prompt-segments>
                  <audiofile text="To pay the amount that's on your card, press 1 To go back and pay with a debit or credit card, press 2 If you don't want to pay now, you can simply hang up" src="ME1040_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="ME1040_ini_01">
                <prompt-segments>
                  <audiofile text="You said you wanted to pay" src="ME1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="paymentAmount">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_02" cond="cardBalance == 10">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $10" src="ME1040_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_03" cond="cardBalance == 20">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $20" src="ME1040_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_04" cond="cardBalance == 35">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $35 " src="ME1040_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_05" cond="cardBalance == 40">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $40" src="ME1040_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_06" cond="cardBalance == 45">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $45" src="ME1040_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_07" cond="cardBalance == 50">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $50" src="ME1040_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_08" cond="cardBalance == 60">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $60" src="ME1040_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_09" cond="cardBalance == 70">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $70" src="ME1040_ini_09.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_10" cond="cardBalance == 90">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for $90" src="ME1040_ini_10.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_11" cond="(cardBalance != 10 &amp;&amp; cardBalance != 20 &amp;&amp; cardBalance != 30 &amp;&amp; cardBalance != 40 &amp;&amp; cardBalance != 45 &amp;&amp; cardBalance != 50 &amp;&amp; cardBalance != 60 &amp;&amp; cardBalance != 70 &amp;&amp; cardBalance != 80 &amp;&amp; cardBalance != 90 )">
                <prompt-segments>
                  <audiofile text="However, this Metro PCS payment card is only for" src="ME1040_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="cardBalance" cond="(cardBalance != 10 &amp;&amp; cardBalance != 20 &amp;&amp; cardBalance != 30 &amp;&amp; cardBalance != 40  &amp;&amp; cardBalance != 45 &amp;&amp; cardBalance != 50 &amp;&amp; cardBalance != 60 &amp;&amp; cardBalance != 70 &amp;&amp; cardBalance != 80 &amp;&amp; cardBalance != 90 )">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ME1040_ini_12">
                <prompt-segments>
                  <audiofile text="To pay the amount on your card, say continue If you'd like to pay a different way, say go back And if you'd like to call us later, you can simply hang up" src="ME1040_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="ME1040_PayCardAmount_DM.grxml" count="1"/>
          <dtmfgrammars filename="ME1040_PayCardAmount_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  