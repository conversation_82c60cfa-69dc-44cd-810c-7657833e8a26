import xml.etree.ElementTree as ET
import os
from llm_response import convertXmltoYaml
import logging
from util import read_filenames_in_folder, delete_existing_files,log_failed_file,remove_null_from_action
from indentation import topic_yaml_indentation
import llm_response
from ruamel.yaml import YAML

# Configure logging
logging.basicConfig(filename='agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')

def data_access_agent(content):
    logging.info('step4_callAgentDecision:data_access_agent')
    logging.info(f'Input: {content}')
    prompt_path = 'prompts/few_shot_CPS_DA.md'
    convertXmltoYaml(content, prompt_path, topic_template)

def decision_state_agent(content):
    logging.info('step4_callAgentDecision:decision_state_agent')
    logging.info(f'Input: {content}')
    prompt_path = 'prompts/few_shot_CPS_DS.md'
    try:
        convertXmltoYaml(content, prompt_path, topic_template)
    except Exception as e:
        logging.error(f'Error in convertXmltoYaml: {e}. Retrying...')
        try:
            convertXmltoYaml(content, prompt_path, topic_template)
        except Exception as e:
            logging.error(f'Second attempt failed: {e}. Aborting...')
            raise

def play_state_agent(content):
    logging.info('step4_callAgentDecision:play_state_agent')
    logging.info(f'Input: {content}')
    prompt_path = 'prompts/few_shot_CPS_PP.md'
    try:
        convertXmltoYaml(content, prompt_path, topic_template)
    except Exception as e:
        logging.error(f'Error in convertXmltoYaml: {e}. Retrying...')
        try:
            convertXmltoYaml(content, prompt_path, topic_template)
        except Exception as e:
            logging.error(f'Second attempt failed: {e}. Aborting...')
            raise

def custom_state_agent(content):
    logging.info('step4_callAgentDecision:custom_state_agent')
    logging.info(f'Input: {content}')
    print(f'custom topic_template : {topic_template}')
    prompt_path = 'prompts/few_shot_CPS_CS.md'
    try:
        convertXmltoYaml(content, prompt_path, topic_template)
    except Exception as e:
        logging.error(f'Error in convertXmltoYaml: {e}. Retrying...')
        try:
            convertXmltoYaml(content, prompt_path, topic_template)
        except Exception as e:
            logging.error(f'Second attempt failed: {e}. Aborting...')
            raise

def dm_state_agent(content):
    logging.info('step4_callAgentDecision:dm_state_agent')
    logging.info(f'Input: {content}')
    prompt_path = 'prompts/few_shot_CPS_DM.md'
    try:
        convertXmltoYaml(content, prompt_path, topic_template)
    except Exception as e:
        logging.error(f'Error in convertXmltoYaml: {e}. Retrying...')
        try:
            convertXmltoYaml(content, prompt_path, topic_template)
        except Exception as e:
            logging.error(f'Second attempt failed: {e}. Aborting...')
            raise

def subdialog_state_agent(content):
    logging.info('step4_callAgentDecision:subdialog_state_agent')
    logging.info(f'Input: {content}')
    prompt_path = 'prompts/few_shot_CPS_SD.md'
    try:
        convertXmltoYaml(content, prompt_path, topic_template)
    except Exception as e:
        logging.error(f'Error in convertXmltoYaml: {e}. Retrying...')
        try:
            convertXmltoYaml(content, prompt_path, topic_template)
        except Exception as e:
            logging.error(f'Second attempt failed: {e}. Aborting...')
            raise

def dvxml_agent(dvxml_file_path,dvxml_topic_template,dvxml_prompt_path):
    logging.info('step4_callAgentDecision:default_agent')

    # Check if the file is not empty or missing essential data
    try:
        tree = ET.parse(dvxml_file_path)
        root = tree.getroot()
        llm_response.is_first_time = True
        logging.info(f'llm_response.is_first_time: {llm_response.is_first_time}')
        # Check if the root has any children (i.e., the file contains data)
        if len(root) == 0:
            logging.warning('The XML file has no data.')

        content = ET.tostring(root, encoding='unicode')
        logging.info(f'Input: {content}')

        convertXmltoYaml(content, dvxml_prompt_path, dvxml_topic_template)

    except ET.ParseError as e:
        logging.error(f'Error parsing XML file: {e}')
        raise
    except FileNotFoundError:
        logging.error(f'File not found: {file_path}')
        raise
    

def process_xml_file(file_path):
    # Load and parse the XML data from the file
    logging.info('step4_callAgentDecision:process_xml_file')
    tree = ET.parse(file_path)
    root = tree.getroot()
    llm_response.is_first_time = True
    # Dictionary to map tag names to functions
    tag_function_map = {
        #"data-access-state": data_access_agent,
        #"decision-state": decision_state_agent,
        #"play-state": play_state_agent,
        "dm-state": dm_state_agent,
        #"subdialog-state": subdialog_state_agent,
        #"custom-state": custom_state_agent
        #"dvxml": dvxml_agent
        # "script": script_agent,
        # Add more mappings here
        # "another-tag": another_function,
    }

    for element in root:
        tag_name = element.tag
        content = ET.tostring(element, encoding='unicode')

        # Call the corresponding function if the tag is in the map
        if tag_name in tag_function_map:
            tag_function_map[tag_name](content)
        else:
            print(f"No function mapped for tag: {tag_name}")

##This method is used to create other state yaml except PP,DS,DM,DA.
def default_agent(file_path,topic_template,prompt_path):
    logging.info('step4_callAgentDecision:default_agent')

    # Check if the file is not empty or missing essential data
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        llm_response.is_first_time = True
        logging.info(f'llm_response.is_first_time: {llm_response.is_first_time}')
        # Check if the root has any children (i.e., the file contains data)
        if len(root) == 0:
            logging.warning('The XML file has no data.')
            return
        content = ET.tostring(root, encoding='unicode')
        logging.info(f'Input: {content}')

        convertXmltoYaml(content, prompt_path, topic_template)

    except ET.ParseError as e:
        logging.error(f'Error parsing XML file: {e}')
        raise
    except FileNotFoundError:
        logging.error(f'File not found: {file_path}')
        raise

input_dialog_folder = 'input/dialogs_file/p4/'
# Add next transition in AllVariable topic
def next_transition(unformatted_variable_path):
    yaml = YAML()
    new_yaml_content = [
    {
       "kind": "BeginDialog",
        "id": "BeginDialog_REPLACE_THIS",
        "dialog": "topic.hl00_CareSharedHL_SD_Dialog"
    }
]
    
    dialog_files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(input_dialog_folder)]
    first_file = dialog_files[0]
    print(f'first_file : {first_file}')

    for item in new_yaml_content:
        if 'dialog' in item:
            item['dialog'] = "topic."+first_file
    
    # Read the existing YAML file
    with open(unformatted_variable_path, 'r') as file:
        yaml_content = yaml.load(file)

    # Append the new content to the YAML
    yaml_content.extend(new_yaml_content)

    # Overwrite the existing file with the updated content
    with open(unformatted_variable_path, 'w') as file:
        yaml.dump(yaml_content, file)

# ONE GO RUN
output_yaml_path = 'output/unformatted_topic_yaml/'
final_topic_yaml = 'output/topic_yaml/'
topic_template_path = 'YAML-template/topic-yaml/'

#delete existing file
delete_existing_files(output_yaml_path)
delete_existing_files(final_topic_yaml)

#TO CREATE SEPARATE TOPIC FOR ALL VARIABLE
variable_file_path = 'input/variable/merged_variables.xml'
variable_template = 'YAML-template/All_Variable.yml'
variable_prompt_path = 'prompts/few_shot_CPS_Variable.md'
unformatted_variable_path = output_yaml_path+'All_Variable.yml'

default_agent(variable_file_path, variable_template,variable_prompt_path)
next_transition(unformatted_variable_path)
topic_yaml_indentation(unformatted_variable_path, final_topic_yaml+'All_Variable.yml')
fileName = final_topic_yaml+'All_Variable.yml'
remove_null_from_action(fileName)


#TO RUN DIALOG STATE
input_dialog_files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(input_dialog_folder)]
for file in input_dialog_files:
    try:
        file_path = os.path.join(input_dialog_folder, file+'.xml')
        topic_template = topic_template_path+file+'.yml'
        process_xml_file(file_path)
        topic_yaml_indentation(output_yaml_path+file+'.yml', final_topic_yaml+file+'_topic.yml')

        #Repalcing null with empty for key:ACTION
        fileName = final_topic_yaml+file+'_topic.yml'
        remove_null_from_action(fileName)
    except Exception as e:
        logging.error(f'Failed to process dialog-file {file}: {e}')
        log_failed_file(file)



#TO RUN CUSTOM STATE
input_custom_folder = 'input/custom_file/'
custom_state_prompt_path = 'prompts/few_shot_CPS_CS_topic.md'
files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(input_custom_folder)]
for file in files:
    try:
        file_path2 = os.path.join(input_custom_folder, file+'.xml')
        topic_template = topic_template_path+file+'.yml'
        default_agent(input_custom_folder+file+'.xml', topic_template,custom_state_prompt_path)
        topic_yaml_indentation(output_yaml_path+file+'.yml', final_topic_yaml+file+'_topic.yml')

        fileName = final_topic_yaml+file+'_topic.yml'
        remove_null_from_action(fileName)
    except Exception as e:
        logging.error(f'Failed to process custom-file {file}: {e}')
        log_failed_file(file)



#TO RUN DVXML STATE
dvxml_prompt_path = 'prompts/few_shot_CPS_DVXML.md'
input_dvxml_folder = 'input/dvxml_file/'
files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(input_dvxml_folder)]
for file in files:
    try:
        dvxml_topic_template = topic_template_path+file+'.yml'
        dvxml_agent(input_dvxml_folder+file+'.xml', dvxml_topic_template,dvxml_prompt_path)
        topic_yaml_indentation(output_yaml_path+file+'.yml', final_topic_yaml+file+'_topic.yml')

        fileName = final_topic_yaml+file+'_topic.yml'
        remove_null_from_action(fileName)

    except Exception as e:
        logging.error(f'Failed to process dvxml-file {file}: {e}')
        log_failed_file(file)
