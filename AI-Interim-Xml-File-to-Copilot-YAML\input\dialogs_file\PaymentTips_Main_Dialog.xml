<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="PaymentTips_Main_Dialog">
    <decision-state id="PT1001_CheckContext_DS">
      <if cond="GlobalVars.paymentsEntryPoint == 'paymentHelp'">
        <session-mapping key="GlobalVars.manageCardTask" expr="'MainMC'"/>
        <action next="PT1025_ManageCards_SD"/>
        <elseif cond="GlobalVars.paymentsEntryPoint == 'carePlanRestriction'">
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
        </elseif>
      </if>
      <if cond="GlobalVars.paymentsEntryPoint == 'careSuspended'">
        <if cond="GlobalVars.GetAccountDetails.isAutopayEnabled == true">
          <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
          <else>
            <if cond="GlobalVars.GetAccountDetails.dueImmediatelyAmount &gt; 3">
              <action next="PT1005_PaymentTipsMenu_DM"/>
              <else>
                <action next="PT1010_OfferAutoPay_DM"/>
              </else>
            </if>
          </else>
        </if>
        <else>
          <action next="PT1005_PaymentTipsMenu_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="PT1005_PaymentTipsMenu_DM" type="CUST">
      <session-mapping key="payingWithPrepaid" value="GlobalVars.payingWithPrepaid != undefined ? GlobalVars.payingWithPrepaid : false" type="String"/>
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet != undefined ? GlobalVars.payingWithEWallet : false" type="String"/>
      <session-mapping key="isAutopayEnabled" value="GlobalVars.GetAccountDetails.isAutopayEnabled" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="hasEWallet" value="GlobalVars.GetPaymentOptions.hasEWallet" type="String"/>
      <session-mapping key="collection_grammar1" value="PT1005_PaymentTipsMenu_DM.grxml" type="String"/>
      <if cond="isAutopayEnabled == true || (parseFloat(dueImmediatelyAmount) &gt; 3)">
        <session-mapping key="collection_grammar1" expr="collection_grammar1 + '?SWI_vars.disallow=autopay^save_cards'"/>
        <elseif cond="payingWithPrepaid != true &amp;&amp; payingWithEWallet != true">
          <session-mapping key="collection_grammar1" expr="collection_grammar1"/>
        </elseif>
        <else>
          <session-mapping key="collection_grammar1" expr="collection_grammar1 + '?SWI_vars.disallow=save_cards'"/>
        </else>
      </if>
      <success>
        <action label="save_cards">
          <session-mapping key="GlobalVars.manageCardTask" expr="'saveMyCard'"/>
          <action next="PT1025_ManageCards_SD"/>
        </action>
        <action label="autopay">
          <session-mapping key="GlobalVars.tag" expr="'setup-autopay'"/>
          <action next="PT1030_AutoPay_SD"/>
        </action>
        <action label="main_menu">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.tag" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.returningFromPayments" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="app_hangup">
          <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="(isAutopayEnabled == true || dueImmediatelyAmount &gt;3)">
                <prompt id="PT1005_ini_01">
                  <prompt-segments>
                    <audiofile text="You can say  main menu  or if you re done here you can simply hang up" src="PT1005_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="(payingWithEWallet == false &amp;&amp; payingWithPrepaid == false)">
                  <prompt id="PT1005_ini_03">
                    <prompt-segments>
                      <audiofile text="To save time on your next payment, you can save this card to your account, or even set up auto-pay!     You can say,  'save my card', 'set up autopay' or 'main menu' If you're done here, you can simply hang up" src="PT1005_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PT1005_ini_04">
                    <prompt-segments>
                      <audiofile text="You can say, 'set up autopay' or 'main menu' If you're done here, you can simply hang up" src="PT1005_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="(isAutopayEnabled == true || dueImmediatelyAmount &gt;3)">
                <prompt id="PT1005_ini_01">
                  <prompt-segments>
                    <audiofile text="You can say  main menu  or if you re done here you can simply hang up" src="PT1005_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="(payingWithEWallet == false &amp;&amp; payingWithPrepaid == false)">
                  <prompt id="PT1005_ini_03">
                    <prompt-segments>
                      <audiofile text="To save time on your next payment, you can save this card to your account, or even set up auto-pay!     You can say,  'save my card', 'set up autopay' or 'main menu' If you're done here, you can simply hang up" src="PT1005_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PT1005_ini_04">
                    <prompt-segments>
                      <audiofile text="You can say, 'set up autopay' or 'main menu' If you're done here, you can simply hang up" src="PT1005_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="(isAutopayEnabled == true || dueImmediatelyAmount &gt; 3)">
                <prompt id="PT1005_nm1_01">
                  <prompt-segments>
                    <audiofile text="You can say  main menu  or if you re finished you can just hang up" src="PT1005_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="(payingWithEWallet == false &amp;&amp; payingWithPrepaid == false)">
                  <prompt id="PT1005_nm1_03">
                    <prompt-segments>
                      <audiofile text="Please say 'save my card', 'set up autopay' or 'main menu'  if you're finished here, you can simply hang up" src="PT1005_nm1_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PT1005_nm1_04">
                    <prompt-segments>
                      <audiofile text="Please say, 'set up autopay' or 'main menu' If you're finished here, you can simply hang up" src="PT1005_nm1_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <if cond="(isAutopayEnabled == true || dueImmediatelyAmount &gt; 3)">
                <prompt id="PT1005_nm1_02">
                  <prompt-segments>
                    <audiofile text="To hear more about how you can save a bank card to your account say   saving cards  You can also say  main menu  or if you re finished here you can just hang up" src="PT1005_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="(payingWithEWallet == false &amp;&amp; payingWithPrepaid == false)">
                  <prompt id="PT1005_nm2_03">
                    <prompt-segments>
                      <audiofile text="Say 'save my card'  or press 1 set up auto pay' or press 2 'main menu' or press *  if you're done here, you can simply hang up" src="PT1005_nm2_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PT1005_nm2_04">
                    <prompt-segments>
                      <audiofile text="say set up autopay or press 1  main menu or press *  if your done here you can simply hang up" src="PT1005_nm2_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <if cond="(isAutopayEnabled == true || dueImmediatelyAmount &gt; 3)">
                <prompt id="PT1005_nm3_01">
                  <prompt-segments>
                    <audiofile text="To go back to the Main Menu press star If you re done you can simply hang up " src="PT1005_nm3_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="(payingWithEWallet == false &amp;&amp; payingWithPrepaid == false)">
                  <prompt id="PT1005_nm3_03">
                    <prompt-segments>
                      <audiofile text="To save this card to your account, press 1 To set up Autopay, press 2 To go back to the Main Menu, press star If you're done, you can simply hang up" src="PT1005_nm3_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PT1005_nm3_04">
                    <prompt-segments>
                      <audiofile text="To set up autopay press 1 To go back to the mainmenu press *  If your done you can simply hang up " src="PT1005_nm3_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(isAutopayEnabled == true || dueImmediatelyAmount &gt; 3)">
                <prompt id="PT1005_nm1_01">
                  <prompt-segments>
                    <audiofile text="You can say  main menu  or if you re finished you can just hang up" src="PT1005_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="(payingWithEWallet == false &amp;&amp; payingWithPrepaid == false)">
                  <prompt id="PT1005_nm1_03">
                    <prompt-segments>
                      <audiofile text="Please say 'save my card', 'set up autopay' or 'main menu'  if you're finished here, you can simply hang up" src="PT1005_nm1_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PT1005_nm1_04">
                    <prompt-segments>
                      <audiofile text="Please say, 'set up autopay' or 'main menu' If you're finished here, you can simply hang up" src="PT1005_nm1_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(isAutopayEnabled == true || dueImmediatelyAmount &gt; 3)">
                <prompt id="PT1005_nm1_02">
                  <prompt-segments>
                    <audiofile text="To hear more about how you can save a bank card to your account say   saving cards  You can also say  main menu  or if you re finished here you can just hang up" src="PT1005_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="(payingWithEWallet == false &amp;&amp; payingWithPrepaid == false)">
                  <prompt id="PT1005_nm2_03">
                    <prompt-segments>
                      <audiofile text="Say 'save my card'  or press 1 set up auto pay' or press 2 'main menu' or press *  if you're done here, you can simply hang up" src="PT1005_nm2_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PT1005_nm2_04">
                    <prompt-segments>
                      <audiofile text="say set up autopay or press 1  main menu or press *  if your done here you can simply hang up" src="PT1005_nm2_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(isAutopayEnabled == true || dueImmediatelyAmount &gt; 3)">
                <prompt id="PT1005_nm3_01">
                  <prompt-segments>
                    <audiofile text="To go back to the Main Menu press star If you re done you can simply hang up " src="PT1005_nm3_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="(payingWithEWallet == false &amp;&amp; payingWithPrepaid == false)">
                  <prompt id="PT1005_nm3_03">
                    <prompt-segments>
                      <audiofile text="To save this card to your account, press 1 To set up Autopay, press 2 To go back to the Main Menu, press star If you're done, you can simply hang up" src="PT1005_nm3_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PT1005_nm3_04">
                    <prompt-segments>
                      <audiofile text="To set up autopay press 1 To go back to the mainmenu press *  If your done you can simply hang up " src="PT1005_nm3_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="(isAutopayEnabled == true || dueImmediatelyAmount &gt;3)">
                <prompt id="PT1005_ini_01">
                  <prompt-segments>
                    <audiofile text="You can say  main menu  or if you re done here you can simply hang up" src="PT1005_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="(payingWithEWallet == false &amp;&amp; payingWithPrepaid == false)">
                  <prompt id="PT1005_ini_03">
                    <prompt-segments>
                      <audiofile text="To save time on your next payment, you can save this card to your account, or even set up auto-pay!     You can say,  'save my card', 'set up autopay' or 'main menu' If you're done here, you can simply hang up" src="PT1005_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PT1005_ini_04">
                    <prompt-segments>
                      <audiofile text="You can say, 'set up autopay' or 'main menu' If you're done here, you can simply hang up" src="PT1005_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="PT1005_PaymentTipsMenu_DM.grxml" count="1">
      </grammars>
          <dtmfgrammars filename="PT1005_PaymentTipsMenu_DM_dtmf.jsp" count="1">
            <param name="payingWithEWallet" value="payingWithEWalletVXMLVar"/>
            <param name="payingWithPrepaid" value="payingWithPrepaidVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="PT1010_OfferAutoPay_DM" type="CUST">
      <session-mapping key="paymentsEntryPoint" value="GlobalVars.paymentsEntryPoint" type="String"/>
      <session-mapping key="allowMainMenu" value="(GlobalVars.paymentsEntryPoint == 'careSuspended') ? 'false' : 'true'" type="String"/>
      <success>
        <action label="sign_up">
          <session-mapping key="GlobalVars.callType" expr="'autopay'"/>
          <session-mapping key="GlobalVars.tag" expr="'setup-autopay'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="PT1011_MetricsAutopayAfterPaymentXfer_JDA"/>
        </action>
        <action label="main_menu">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.returningFromPayments" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="app_hangup">
          <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.callType" expr="'autopay'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="PT1020_Transfer_SD"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PT1010_ini_01" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="A great way to avoid service interruptions is our Autopay service" src="PT1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_ini_02" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="Auto-pay is a great way of making sure your account is always paid up" src="PT1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_ini_03">
                <prompt-segments>
                  <audiofile text="Your balance will automatically be paid each month from the card or account you set up with us without payment fees To talk to someone about setting up auto-pay right now say  sign up " src="PT1010_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_ini_04" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="If you re done you can simply hang up" src="PT1010_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_ini_05" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="You can also say  main menu  or if you re done here you can simply hang up" src="PT1010_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PT1010_ini_01" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="A great way to avoid service interruptions is our Autopay service" src="PT1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_ini_02" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="Auto-pay is a great way of making sure your account is always paid up" src="PT1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_ini_03">
                <prompt-segments>
                  <audiofile text="Your balance will automatically be paid each month from the card or account you set up with us without payment fees To talk to someone about setting up auto-pay right now say  sign up " src="PT1010_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_ini_04" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="If you re done you can simply hang up" src="PT1010_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_ini_05" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="You can also say  main menu  or if you re done here you can simply hang up" src="PT1010_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PT1010_nm1_01" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To talk to an agent about setting up auto-pay say sign up Or if youre done you can simply hang up" src="PT1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_nm1_02" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To set up auto-pay, say 'sign up' You can also say 'main menu' or if you're done, you can simply hang up" src="PT1010_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="PT1010_nm2_01" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To talk to someone about setting up auto-pay say  sign up  or press 1 And if you re done you can simply hang up" src="PT1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_nm2_02" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To set up auto-pay, say 'sign up' or press 1 Or say 'main menu' or press star And if you're done, you can simply hang up" src="PT1010_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="PT1010_nm3_01" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To talk to an agent about Autopay press 1 If you re done you can simply hang up" src="PT1010_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_nm3_02" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To talk to an agent about Autopay press 1 To go back to the Main Menu press star If you re done you can simply hang up" src="PT1010_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_nm1_01" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To talk to an agent about setting up auto-pay say sign up Or if youre done you can simply hang up" src="PT1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_nm1_02" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To set up auto-pay, say 'sign up' You can also say 'main menu' or if you're done, you can simply hang up" src="PT1010_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_nm2_01" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To talk to someone about setting up auto-pay say  sign up  or press 1 And if you re done you can simply hang up" src="PT1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_nm2_02" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To set up auto-pay, say 'sign up' or press 1 Or say 'main menu' or press star And if you're done, you can simply hang up" src="PT1010_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_nm3_01" cond="paymentsEntryPoint == 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To talk to an agent about Autopay press 1 If you re done you can simply hang up" src="PT1010_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PT1010_nm3_02" cond="paymentsEntryPoint != 'careSuspended'">
                <prompt-segments>
                  <audiofile text="To talk to an agent about Autopay press 1 To go back to the Main Menu press star If you re done you can simply hang up" src="PT1010_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="(isAutopayEnabled == true || dueImmediatelyAmount &gt;3)">
                <prompt id="PT1005_ini_01">
                  <prompt-segments>
                    <audiofile text="You can say  main menu  or if you re done here you can simply hang up" src="PT1005_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="(payingWithEWallet == false &amp;&amp; payingWithPrepaid == false)">
                  <prompt id="PT1005_ini_03">
                    <prompt-segments>
                      <audiofile text="To save time on your next payment, you can save this card to your account, or even set up auto-pay!     You can say,  'save my card', 'set up autopay' or 'main menu' If you're done here, you can simply hang up" src="PT1005_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PT1005_ini_04">
                    <prompt-segments>
                      <audiofile text="You can say, 'set up autopay' or 'main menu' If you're done here, you can simply hang up" src="PT1005_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="PT1010_OfferAutoPay_DM.jsp" count="1">
            <param name="allowMainMenu" value="allowMainMenuVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="PT1010_OfferAutoPay_DM_dtmf.jsp" count="1">
            <param name="allowMainMenu" value="allowMainMenuVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="7000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="PT1011_MetricsAutopayAfterPaymentXfer_DS">
      <action next="PT1030_AutoPay_SD"/>
    </decision-state>

    <subdialog-state id="PT1020_Transfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="PT1020_Transfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PT1020_Transfer_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <subdialog-state id="PT1025_ManageCards_SD">
      <gotodialog next="ManageCards_Main_Dialog"/>
      <action next="PT1025_ManageCards_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PT1025_ManageCards_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="PT1030_AutoPay_SD">
      <gotodialog next="AutoPay_Main_Dialog"/>
      <action next="PT1030_AutoPay_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PT1030_AutoPay_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  