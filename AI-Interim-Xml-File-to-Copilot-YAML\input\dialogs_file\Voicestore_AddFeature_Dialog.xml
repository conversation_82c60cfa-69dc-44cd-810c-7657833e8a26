<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Voicestore_AddFeature_Dialog">
    <subdialog-state id="VS1118_DetectConflictingOffers_SD">
      <gotodialog next="DetectConflictingOffer_Dialog"/>
      <action next="VS1118_DetectConflictingOffers_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="VS1118_DetectConflictingOffers_SD_return_CS">
      <action next="VS1405_OfferFutureDatedFeature_DM"/>
    </custom-state>

    <subdialog-state id="VS1125_SubmitChangeOfferFeature_SD">
      <gotodialog next="SubmitChangeOffer_Dialog"/>
      <action next="VS1125_SubmitChangeOfferFeature_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="VS1125_SubmitChangeOfferFeature_SD_return_CS">
      <if cond="GlobalVars.cancelAddFeature == true">
        <gotodialog next="Voicestore_Routing#VS1320_GoToAnythingElse_SD"/>
        <else>
          <action next="VS1135_PlaySuccessSignup_PP"/>
        </else>
      </if>
    </custom-state>

    <play-state id="VS1135_PlaySuccessSignup_PP">
      <session-mapping key="soc" value="GlobalVars.selectedFeature" type="String"/>
      <session-mapping key="VS1135initialEntry" value="GlobalVars.VS1135initialEntry" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <audio>
        <prompt id="VS1135_ini_01" cond="VS1135initialEntry == true">
          <prompt-segments>
            <audiofile text="Youre all set" src="VS1135_ini_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms" cond="VS1135initialEntry == true">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="VS1135_out_03">
          <prompt-segments>
            <audiofile text="Ive signed you up for " src="VS1135_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="soc">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayFeatureMedialPrompt"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="soc">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayFeaturePostamblePrompt"/>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.VS1135initialEntry" value="false" type="Boolean"/>
      <if cond="GlobalVars.accountUpdateFailed == true">
        <gotodialog next="Voicestore_Routing#VS1320_GoToAnythingElse_SD"/>
      </if>
      <if cond="dueImmediatelyAmount &gt; 3">
        <gotodialog next="Voicestore_Routing#VS1330_GoToMakePayments_SD"/>
        <else>
          <action next="VS1145_AddFeaturePostamble_DM"/>
        </else>
      </if>
    </play-state>

    <dm-state id="VS1145_AddFeaturePostamble_DM" type="CUST">
      <success>
        <action label="no">
          <audio>
            <prompt id="VS1145_out_01">
              <prompt-segments>
                <audiofile text="Alright" src="VS1145_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <gotodialog next="Voicestore_Main#VS1045_AnotherFeature_DM"/>
        </action>
        <action label="yes">
          <action next="VS1135_PlaySuccessSignup_PP"/>
        </action>
        <action label="main_menu">
          <gotodialog next="Voicestore_Main#VS1045_AnotherFeature_DM"/>
        </action>
        <action label="make_payment">
          <gotodialog next="Voicestore_Routing#VS1330_GoToMakePayments_SD"/>
        </action>
        <action label="operator"/>
        <action label="repeat">
          <action next="VS1135_PlaySuccessSignup_PP"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="VS1145_ini_01">
                <prompt-segments>
                  <audiofile text="To hear that again, say repeat that To pay for it now, say pay now" src="VS1145_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="VS1145_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say yes or no Would you like to hear that information again?" src="VS1145_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1145_nm1_02">
                <prompt-segments>
                  <audiofile text="You can also say make a payment" src="VS1145_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1145_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear about your new feature, say yes or press 1 If youre finished, say no, or press 2 To pay for your new feature now, say make a payment or press 3" src="VS1145_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1145_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear about your new feature, say yes or press 1 If youre finished, say no, or press 2 To pay for your new feature now, say make a payment or press 3" src="VS1145_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1145_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say yes or no Would you like to hear that information again?" src="VS1145_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1145_nm1_02">
                <prompt-segments>
                  <audiofile text="You can also say make a payment" src="VS1145_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1145_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear about your new feature, say yes or press 1 If youre finished, say no, or press 2 To pay for your new feature now, say make a payment or press 3" src="VS1145_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1145_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear about your new feature, say yes or press 1 If youre finished, say no, or press 2 To pay for your new feature now, say make a payment or press 3" src="VS1145_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="VS1145_out_01">
                <prompt-segments>
                  <audiofile text="Alright" src="VS1145_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="VS1145_ini_01">
                <prompt-segments>
                  <audiofile text="To hear that again, say repeat that To pay for it now, say pay now" src="VS1145_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="VS1145_AddFeaturePostamble_DM.grxml" count="1"/>
          <dtmfgrammars filename="VS1145_AddFeaturePostamble_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="VS1405_OfferFutureDatedFeature_DM" type="CUST">
      <session-mapping key="heardFutureDatedFeatureInstructions" value="GlobalVars.heardFutureDatedFeatureInstructions" type="String"/>
      <session-mapping key="futureDate" value="GlobalVars.futureDate" type="String"/>
      <success>
        <action label="immediate" next="VS1410_MetricsImmediateFeatureAdd_JDA">
          <audio>
            <prompt id="VS1405_out_01">
              <prompt-segments>
                <audiofile text="Got it One second while I add that" src="VS1405_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.futureDate" expr="undefined"/>
          <session-mapping key="GlobalVars.heardFutureDatedFeatureInstructions" value="true" type="Boolean"/>
        </action>
        <action label="futuredate">
          <if cond="GlobalVars.heardFutureDatedFeatureInstructions == false">
            <audio>
              <prompt id="VS1405_out_02">
                <prompt-segments>
                  <audiofile text="Okay! Again, you won't have to call back or do anything else You'll just have this feature ready to use on your next due date" src="VS1405_out_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <else>
              <audio>
                <prompt id="VS1405_out_03">
                  <prompt-segments>
                    <audiofile text="Okay! It'll be ready on your next due date" src="VS1405_out_03.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
            </else>
          </if>
          <audio>
            <prompt id="VS1405_out_04">
              <prompt-segments>
                <audiofile text="One second while I add that" src="VS1405_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.heardFutureDatedFeatureInstructions " value="true" type="Boolean"/>
          <action next="VS1415_MetricsFuturefeatureAdd_JDA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="heardFutureDatedFeatureInstructions == true">
                <prompt id="VS1405_ini_01">
                  <prompt-segments>
                    <audiofile text="When would you like this feature to start? Say 'right now' or 'next due date'" src="VS1405_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="VS1405_ini_02">
                    <prompt-segments>
                      <audiofile text="Before I add this to your account - I can activate it for you right away, or I can make it activate on your next due date That's" src="VS1405_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="VS1405_ini_03">
                    <prompt-segments>
                      <audiofile text="If you choose that, you *won't* have to call back or do anything else after today So, when would you like it to start? Say 'right now' or 'next due date'" src="VS1405_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="heardFutureDatedFeatureInstructions == true">
                <prompt id="VS1405_ini_01">
                  <prompt-segments>
                    <audiofile text="When would you like this feature to start? Say 'right now' or 'next due date'" src="VS1405_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="VS1405_ini_02">
                    <prompt-segments>
                      <audiofile text="Before I add this to your account - I can activate it for you right away, or I can make it activate on your next due date That's" src="VS1405_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="VS1405_ini_03">
                    <prompt-segments>
                      <audiofile text="If you choose that, you *won't* have to call back or do anything else after today So, when would you like it to start? Say 'right now' or 'next due date'" src="VS1405_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="heardFutureDatedFeatureInstructions == true">
                <prompt id="VS1405_ini_01">
                  <prompt-segments>
                    <audiofile text="When would you like this feature to start? Say 'right now' or 'next due date'" src="VS1405_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="VS1405_ini_02">
                    <prompt-segments>
                      <audiofile text="Before I add this to your account - I can activate it for you right away, or I can make it activate on your next due date That's" src="VS1405_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="VS1405_ini_03">
                    <prompt-segments>
                      <audiofile text="If you choose that, you *won't* have to call back or do anything else after today So, when would you like it to start? Say 'right now' or 'next due date'" src="VS1405_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1405_nm2_01">
                <prompt-segments>
                  <audiofile text="I can activate this feature for you right now, or I can make it activate with your next due date That'll be" src="VS1405_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1405_nm2_02">
                <prompt-segments>
                  <audiofile text="So when would you like it to activate? Say 'right now' or press 1, or 'next due date' or press 2" src="VS1405_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1405_nm2_01">
                <prompt-segments>
                  <audiofile text="I can activate this feature for you right now, or I can make it activate with your next due date That'll be" src="VS1405_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1405_nm2_02">
                <prompt-segments>
                  <audiofile text="So when would you like it to activate? Say 'right now' or press 1, or 'next due date' or press 2" src="VS1405_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="heardFutureDatedFeatureInstructions == true">
                <prompt id="VS1405_ini_01">
                  <prompt-segments>
                    <audiofile text="When would you like this feature to start? Say 'right now' or 'next due date'" src="VS1405_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="VS1405_ini_02">
                    <prompt-segments>
                      <audiofile text="Before I add this to your account - I can activate it for you right away, or I can make it activate on your next due date That's" src="VS1405_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="VS1405_ini_03">
                    <prompt-segments>
                      <audiofile text="If you choose that, you *won't* have to call back or do anything else after today So, when would you like it to start? Say 'right now' or 'next due date'" src="VS1405_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1405_nm2_01">
                <prompt-segments>
                  <audiofile text="I can activate this feature for you right now, or I can make it activate with your next due date That'll be" src="VS1405_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1405_nm2_02">
                <prompt-segments>
                  <audiofile text="So when would you like it to activate? Say 'right now' or press 1, or 'next due date' or press 2" src="VS1405_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1405_nm2_01">
                <prompt-segments>
                  <audiofile text="I can activate this feature for you right now, or I can make it activate with your next due date That'll be" src="VS1405_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="date" expr="futureDate">
                <param name="dateFormat" value="yyyyMMdd"/>
                <param name="playDayOfMonth" value="true"/>
                <param name="playYear" value="false"/>
                <param name="playDayOfTheWeek" value="true"/>
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1405_nm2_02">
                <prompt-segments>
                  <audiofile text="So when would you like it to activate? Say 'right now' or press 1, or 'next due date' or press 2" src="VS1405_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="heardFutureDatedFeatureInstructions == true">
                <prompt id="VS1405_ini_01">
                  <prompt-segments>
                    <audiofile text="When would you like this feature to start? Say 'right now' or 'next due date'" src="VS1405_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="VS1405_ini_02">
                    <prompt-segments>
                      <audiofile text="Before I add this to your account - I can activate it for you right away, or I can make it activate on your next due date That's" src="VS1405_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="date" expr="futureDate">
                    <param name="dateFormat" value="yyyyMMdd"/>
                    <param name="playDayOfMonth" value="true"/>
                    <param name="playYear" value="false"/>
                    <param name="playDayOfTheWeek" value="true"/>
                    <param name="intonation" value="f"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="VS1405_ini_03">
                    <prompt-segments>
                      <audiofile text="If you choose that, you *won't* have to call back or do anything else after today So, when would you like it to start? Say 'right now' or 'next due date'" src="VS1405_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="VS1405_OfferFutureDatedFeature_DM.grxml" count="1"/>
          <dtmfgrammars filename="VS1405_OfferFutureDatedFeature_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="VS1410_MetricsImmediateFeatureAdd_DS">
      <action next="VS1125_SubmitChangeOfferFeature_SD"/>
    </decision-state>

    <decision-state id="VS1415_MetricsFuturefeatureAdd_DS">
      <action next="VS1125_SubmitChangeOfferFeature_SD"/>
    </decision-state>

  </dialog>
  