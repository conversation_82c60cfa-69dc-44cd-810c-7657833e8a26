<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="accountNumber" value="empty" type="String"/>
  <session-mapping key="voucherNumber" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="GlobalVars.numberPINPayments" value="0" type="integer"/>
  <session-mapping key="callType" value="activate" type="string"/>
  <session-mapping key="prepaidPinToggleOn" value="true" type="boolean"/>
  <session-mapping key="fromFindPINWait" value="true" type="string"/>
  <session-mapping key="prepaidPINErrorCounter" value="1" type="integer"/>
  <session-mapping key="errorCode" value="CLOSEMAXRETRIES" type="string"/>
  <session-mapping key="numberPINPayments" value="1" type="string"/>
  <session-mapping key="GlobalVars.isCareTransfer" value="true" type="boolean"/>
  <session-mapping key="needResidualPayment" value="false" type="string"/>
  <session-mapping key="activationEntryPoint" value="care" type="string"/>
  <session-mapping key="GlobalVars.callType" value="activate" type="string"/>
  <session-mapping key="voucherStatus" value="COLD" type="string"/>
  <session-mapping key="GlobalVars.prepaidPINErrorCounter" value="3" type="string"/>
  <session-mapping key="GlobalVars.errorCode" value="MAXRETRIESEXCEEDED" type="string"/>
</session-mappings>
