<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="accountNumber" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="GlobalVars.enteredFrom" value="DSG" type="string"/>
  <session-mapping key="GlobalVars.DS3005Pressed" value="retry" type="string"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="TransferTag" value="DSGapp_EDGEPW_English" type="string"/>
  <session-mapping key="accountPinToggleOn" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="active" type="string"/>
  <session-mapping key="GetAccountDetails.securityQuestionCode" value="SQ8" type="string"/>
  <session-mapping key="GetAccountDetails.eligibleForUnhotlineCheck" value="false" type="string"/>
  <session-mapping key="CheckUnhotlineEligibility.isEligible" value="true" type="boolean"/>
  <session-mapping key="highSecurity" value="true" type="string"/>
  <session-mapping key="accountStatus" value="active" type="string"/>
  <session-mapping key="GlobalVars.extensionSuccess" value="true" type="string"/>
</session-mappings>
