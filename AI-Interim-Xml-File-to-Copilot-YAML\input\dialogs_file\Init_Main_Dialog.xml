<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Init_Main_Dialog">
    <custom-state id="HL1005_Start_CS">
      <session-mapping key="GlobalVars.outageByColo" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.fromMyMetroCustomerSupport" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.playGoodbyeMessage" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.metrics_hasVisitedMainMenu" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.cti_AuthStatus" expr="'00'"/>
      <session-mapping key="GlobalVars.cti_TransferReason" expr="'01'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="'000'"/>
      <session-mapping key="GlobalVars.playedCCPA" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.langAskedAlready" value="false" type="Boolean"/>
      <if cond="language == 'en-US'">
        <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
        <else>
          <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
        </else>
      </if>
      <action next="HL1007_GetCTIParameters_DB_DA"/>
    </custom-state>

    <data-access-state id="HL1007_GetCTIParameters_DB_DA">
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="GetCTIParameters" classname="com.nuance.metro.dataaccess.GetCTIParameters">
        <inputs/>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="ctiAuthStatus"/>
          <output-variable name="ctiTransferReason"/>
          <output-variable name="ctiIntent"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetCTIParameters.status == 'Success'">
          <session-mapping key="GlobalVars.GetCTIParameters" expr="GetCTIParameters"/>
          <else>
            <session-mapping key="GlobalVars.isCTISuccess" value="false" type="Boolean"/>
          </else>
        </if>
        <action next="HL1006_LookupConfigs_DB_DA"/>
      </action>
    </data-access-state>

    <data-access-state id="HL1006_LookupConfigs_DB_DA">
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="min" value="GlobalVars.trn" type="String"/>
      <session-mapping key="did" value="dnis" type="String"/>
      <data-access id="GetBCSParameters" classname="com.nuance.metro.dataaccess.GetBCSParameters">
        <inputs>
          <input-variable name="min"/>
          <input-variable name="sessionId"/>
          <input-variable name="did"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetBCSParameters.status == 'Success'">
          <session-mapping key="GlobalVars.GetBCSParameters" expr="GetBCSParameters"/>
          <session-mapping key="GlobalVars.isBCSSuccess" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.outageByColo" expr="GlobalVars.GetBCSParameters.isOutage == 'true' || GlobalVars.GetBCSParameters.isOutage==true"/>
          <session-mapping key="GlobalVars.enteredFrom" expr="getEnteredFrom(dnis,GlobalVars.GetBCSParameters)"/>
          <session-mapping key="GlobalVars.unCoopMaxRequest" expr="GlobalVars.GetBCSParameters.unCoopMaxRequest"/>
          <session-mapping key="GlobalVars.coopMaxRequest" expr="GlobalVars.GetBCSParameters.coopMaxRequest"/>
          <session-mapping key="GlobalVars.care_enable_autocorrect_offer" expr="GlobalVars.GetBCSParameters.care_enable_autocorrect_offer"/>
          <session-mapping key="GlobalVars.care_enable_twofactorauth" expr="GlobalVars.GetBCSParameters.care_enable_twofactorauth"/>
          <session-mapping key="GlobalVars.care_rateplan_special_error_planlist" expr="GlobalVars.GetBCSParameters.care_rateplan_special_error_planlist"/>
          <else>
            <session-mapping key="GlobalVars.isBCSSuccess" value="false" type="Boolean"/>
          </else>
        </if>
        <action next="HL1010_CheckDSG_JDA"/>
      </action>
    </data-access-state>

    <play-state id="HL1045_FraudSuspension_PP">
      <audio>
        <prompt id="HL1045_out_01">
          <prompt-segments>
            <audiofile text="Your account requires assistance " src="HL1045_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="MN1105_GoToCallTransfer_SD"/>
    </play-state>

    <decision-state id="HL1010_CheckDSG_DS">
      <session-mapping key="did" value="dnis" type="String"/>
      <if cond="GlobalVars.enteredFrom == 'DSG' || GlobalVars.enteredFrom == 'NRH'">
        <action next="HL1015_GoToDSG_SD"/>
        <elseif cond="GlobalVars.enteredFrom == 'RCC'">
          <action next="HL1025_GoToRCC_SD"/>
        </elseif>
        <elseif cond="GlobalVars.enteredFrom == 'WNP'">
          <action next="HL1030_GoToWPN_SD"/>
        </elseif>
        <elseif cond="GlobalVars.enteredFrom == 'PWR'">
          <action next="HL1035_GoToPWR_SD"/>
        </elseif>
        <elseif cond="GlobalVars.enteredFrom == 'PC'">
          <action next="HL1040_GoToPC_SD"/>
        </elseif>
        <else>
          <action next="HL1020_GoToInitialHandling_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="HL1015_GoToDSG_SD">
      <gotodialog next="DSG_Main_Dialog"/>
      <action next="HLInit_TerminalSD_return_CS"/>
    </subdialog-state>
    <subdialog-state id="HL1020_GoToInitialHandling_SD">
      <gotodialog next="InitialHandling_Main_Dialog"/>
      <action next="HLInit_TerminalSD_return_CS"/>
    </subdialog-state>
    <custom-state id="HLInit_TerminalSD_return_CS">
      <if cond="GlobalVars.enteredFrom == 'WNP'">
        <action next="HL1030_GoToWPN_SD"/>
      </if>
    </custom-state>

    <subdialog-state id="HL1030_GoToWPN_SD">
      <gotodialog next="NumberPortStatusLine_Dialog"/>
      <action next="HL1030_GoToWPN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="HL1030_GoToWPN_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <subdialog-state id="HL1035_GoToPWR_SD">
      <gotodialog next="PasswordResetLine_Dialog"/>
      <action next="HL1035_GoToPWR_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="HL1035_GoToPWR_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <subdialog-state id="HL1040_GoToPC_SD">
      <gotodialog next="PromotionCenterLine_Dialog"/>
      <action next="HL1040_GoToPC_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="HL1040_GoToPC_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <subdialog-state id="MN1105_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="MN1105_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MN1105_GoToCallTransfer_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <subdialog-state id="HL1025_GoToRCC_SD">
      <gotodialog next="RuralCallCompletionLine_Dialog"/>
      <action next="HL1025_GoToRCC_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="HL1025_GoToRCC_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

  </dialog>
  