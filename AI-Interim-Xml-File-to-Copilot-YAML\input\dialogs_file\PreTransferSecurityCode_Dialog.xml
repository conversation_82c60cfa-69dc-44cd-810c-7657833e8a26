<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="PreTransferSecurityCode_Dialog">
    <decision-state id="XC1001_CheckContext_DS">
      <session-mapping key="GlobalVars.preTransferCodeCollectionOutcome" expr="undefined"/>
      <session-mapping key="preTransferCodeCollectionErrors" expr="0"/>
      <action next="XC1005_CheckPreTransferCodeEnabled_JDA"/>
    </decision-state>

    <decision-state id="XC1005_CheckPreTransferCodeEnabled_DS">
      <if cond="(GlobalVars.visitedXferTwoFactorAuth != true) &amp;&amp; (GlobalVars.GetBCSParameters.care_enable_pretransfer_code_collection == true || GlobalVars.GetBCSParameters.care_enable_pretransfer_code_collection == 'true')">
        <action next="XC1010_CheckPreTransferCodeExclusions_JDA"/>
        <else>
          <session-mapping key="GlobalVars.preTransferCodeCollectionOutcome" expr="'flowDisabled'"/>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="XC1010_CheckPreTransferCodeExclusions_DS">
      <if cond="(GlobalVars.tag == 'vague-forgot_pin')">
        <session-mapping key="GlobalVars.preTransferCodeCollectionOutcome" expr="'notEligible'"/>
        <action next="getReturnLink()"/>
        <elseif cond="(GlobalVars.twoFactorAuthOutcome == 'code_timeout' || GlobalVars.GetBCSParameters.care_enable_twofactorauth == 'false' || GlobalVars.GetBCSParameters.care_transfer_twofactorauth_nophone == 'true' || GlobalVars.dontKnowPIN == true || GlobalVars.lastPinTry == true)">
          <action next="getReturnLink()"/>
        </elseif>
        <else>
          <action next="XC1103_GetOTP_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="XC1103_GetOTP_SD">
      <session-mapping key="GlobalVars.visitedXferTwoFactorAuth" value="true" type="Boolean"/>
      <gotodialog next="XferTwoFactorAuth_Dialog"/>
      <action next="XC1103_GetOTP_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="XC1103_GetOTP_SD_Return_CS">
      <action next="XC1105_CollectSecurityCode_DM"/>
    </custom-state>

    <dm-state id="XC1105_CollectSecurityCode_DM" type="DIGT">
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="lastLoginAttempt" value="GlobalVars.onePinTryRemaining" type="String"/>
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <success>
        <action label="default" next="XC1110_Authenticate_DB_DA">
          <session-mapping key="GlobalVars.verificationType" expr="'pin'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="XC1105_CollectSecurityCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.cti_PIN" expr="XC1105_CollectSecurityCode_DM.returnvalue"/>
        </action>
        <action label="operator">
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.preTransferCodeCollectionOutcome" expr="'agentRequest'"/>
          <action next="XC1125_PlaySkipCodeTransition_PP"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration reco_suppress_logs="true" highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="XC1105_ini_02" cond="lastLoginAttempt == true">
                <prompt-segments>
                  <audiofile text="Sorry, it's not a match  You can always reset your account PIN online at metrobyT-Mobilecom Simply sign into your account , go to My Account and select Profile From there select, PIN/Passcode  Now, please say or enter your pin one more time" src="XC1105_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XC1105_ini_03" cond="preTransferCodeCollectionErrors == 0 &amp;&amp; lastLoginAttempt != true &amp;&amp; accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="Now, to save time with an agent, what's your 6-to-15-digit account PIN?" src="XC1105_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XC1105_ini_04" cond="preTransferCodeCollectionErrors == 0 &amp;&amp; lastLoginAttempt != true &amp;&amp; accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="Now, to save time with an agent, what's your 8-digit account PIN?" src="XC1105_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XC1105_ini_01" cond="preTransferCodeCollectionErrors != 0 &amp;&amp; lastLoginAttempt != true">
                <prompt-segments>
                  <audiofile text="That didn't match either, Let's try again" src="XC1105_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="XC1105_CollectSecurityCode_DM_initial"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="XC1105_ini_02" cond="lastLoginAttempt == true">
                <prompt-segments>
                  <audiofile text="Sorry, it's not a match  You can always reset your account PIN online at metrobyT-Mobilecom Simply sign into your account , go to My Account and select Profile From there select, PIN/Passcode  Now, please say or enter your pin one more time" src="XC1105_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XC1105_ini_03" cond="preTransferCodeCollectionErrors == 0 &amp;&amp; lastLoginAttempt != true &amp;&amp; accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="Now, to save time with an agent, what's your 6-to-15-digit account PIN?" src="XC1105_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XC1105_ini_04" cond="preTransferCodeCollectionErrors == 0 &amp;&amp; lastLoginAttempt != true &amp;&amp; accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="Now, to save time with an agent, what's your 8-digit account PIN?" src="XC1105_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XC1105_ini_01" cond="preTransferCodeCollectionErrors != 0 &amp;&amp; lastLoginAttempt != true">
                <prompt-segments>
                  <audiofile text="That didn't match either, Let's try again" src="XC1105_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="XC1105_ini_02" cond="lastLoginAttempt == true">
                <prompt-segments>
                  <audiofile text="Sorry, it's not a match  You can always reset your account PIN online at metrobyT-Mobilecom Simply sign into your account , go to My Account and select Profile From there select, PIN/Passcode  Now, please say or enter your pin one more time" src="XC1105_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XC1105_ini_03" cond="preTransferCodeCollectionErrors == 0 &amp;&amp; lastLoginAttempt != true &amp;&amp; accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile text="Now, to save time with an agent, what's your 6-to-15-digit account PIN?" src="XC1105_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XC1105_ini_04" cond="preTransferCodeCollectionErrors == 0 &amp;&amp; lastLoginAttempt != true &amp;&amp; accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile text="Now, to save time with an agent, what's your 8-digit account PIN?" src="XC1105_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XC1105_ini_01" cond="preTransferCodeCollectionErrors != 0 &amp;&amp; lastLoginAttempt != true">
                <prompt-segments>
                  <audiofile text="That didn't match either, Let's try again" src="XC1105_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="XC1105_CollectSecurityCode_DM.grxml" count="1"/>
          <dtmfgrammars filename="XC1105_CollectSecurityCode_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="19000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" interdigittimeout="3000ms" termtimeout="200ms" timeout="19000ms"/>
      </global_configuration>
      <confirmation_configuration reco_suppress_logs="true" highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="XC1110_Authenticate_DB_DA">
      <session-mapping key="accessToken" value="GlobalVars.accessToken" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="verificationType" value="GlobalVars.verificationType" type="String"/>
      <session-mapping key="verificationValue" value="GlobalVars.verificationValue" type="String"/>
      <session-mapping key="pin" value="GlobalVars.verificationValue" type="String"/>
      <data-access id="Authenticate" classname="com.nuance.metro.dataaccess.ValidatePinForAuthenticate">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="pin" mask="true"/>
          <input-variable name="accessToken" mask="false"/>
          <input-variable name="verificationType"/>
          <input-variable name="verificationValue" mask="true"/>
          <input-variable name="sessionId"/>
          <input-variable name="providerId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="JWTToken"/>
          <output-variable name="expiresIn"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="Authenticate.status == 'FAILURE'">
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          <gotodialog next="CallTransfer_Main_Dialog"/>
          <elseif cond="Authenticate.acctLocked == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.acctLocked" value="true" type="Boolean"/>
            <action next="XC1215_PlayInvalidCode_PP"/>
          </elseif>
          <elseif cond="Authenticate.onePinTryRemaining == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.onePinTryRemaining" value="true" type="Boolean"/>
            <action next="XC1115_CheckSecurityCodeMatch_JDA"/>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <action next="XC1115_CheckSecurityCodeMatch_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="XC1115_CheckSecurityCodeMatch_DS">
      <if cond="(GlobalVars.Authenticate &amp;&amp; GlobalVars.Authenticate.status != 'SUCCESS')">
        <session-mapping key="preTransferCodeCollectionErrors" expr="preTransferCodeCollectionErrors + 1"/>
        <action next="XC1205_CheckInvalidCodeRetryConfig_JDA"/>
        <else>
          <session-mapping key="GlobalVars.loggedIn" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'authenticated_by_pin')"/>
          <action next="XC1120_PlayCodeCorrect_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="XC1120_PlayCodeCorrect_PP">
      <audio>
        <if type="java">
          <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
            <param name="numToMatch" value="1"/>
            <param name="startNum" value="1"/>
            <param name="endNum" value="3"/>
          </condition>
          <prompt id="XC1120_out_01">
            <prompt-segments>
              <audiofile text="Bingo" src="XC1120_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <elseif>
            <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
              <param name="numToMatch" value="2"/>
              <param name="startNum" value="1"/>
              <param name="endNum" value="3"/>
            </condition>
            <prompt id="XC1120_out_02">
              <prompt-segments>
                <audiofile text="That's the one" src="XC1120_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="XC1120_out_03">
              <prompt-segments>
                <audiofile text="Got it! " src="XC1120_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="XC1125_PlaySkipCodeTransition_PP">
      <session-mapping key="preTransferCodeCollectionOutcome" value="GlobalVars.preTransferCodeCollectionOutcome" type="String"/>
      <audio>
        <if cond="preTransferCodeCollectionOutcome == 'agentRequest'">
          <prompt id="XC1125_out_01">
            <prompt-segments>
              <audiofile text="Okay Just make sure you have your account PIN, or the answer to your security question ready to give the agent!" src="XC1125_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="XC1125_out_02">
              <prompt-segments>
                <audiofile text="I didn't get that But that's okay! Just make sure you have your account PIN, or the answer to your security question ready to give the agent!" src="XC1125_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <decision-state id="XC1205_CheckInvalidCodeRetryConfig_DS">
      <if cond="(GlobalVars.GetBCSParameters.care_enable_pretransfer_code_retries == true || GlobalVars.GetBCSParameters.care_enable_pretransfer_code_retries == 'true')">
        <action next="XC1210_CheckNumberAttempts_JDA"/>
        <else>
          <action next="XC1215_PlayInvalidCode_PP"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="XC1210_CheckNumberAttempts_DS">
      <if cond="(preTransferCodeCollectionErrors &lt;= 5)">
        <action next="XC1105_CollectSecurityCode_DM"/>
        <else>
          <action next="XC1215_PlayInvalidCode_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="XC1215_PlayInvalidCode_PP">
      <audio>
        <prompt id="XC1215_out_01">
          <prompt-segments>
            <audiofile text="Sorry, it's not a match That's okay, our agents will help you log in!" src="XC1215_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

  </dialog>
  