<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="operatorNeedsConfirmation" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.callType" value="make_pmt" type="string"/>
  <session-mapping key="GlobalVars.suspendedOperatorRequest" value="true" type="string"/>
  <session-mapping key="GlobalVars.GetAccountDetails" value="null" type="string"/>
  <session-mapping key="GetBCSParameters.care_nlu_enabled" value="true" type="boolean"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="care_allow_payment_transfers" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.operatorFromPayment" value="true" type="boolean"/>
  <session-mapping key="operatorReqCount" value="maxRequests" type="string"/>
  <session-mapping key="accountStatus" value="suspended" type="string"/>
  <session-mapping key="care_allow_suspended_transfers" value="false" type="string"/>
  <session-mapping key="GlobalVars.tag" value="transfer-disambig_troubleshooting" type="string"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="boolean"/>
  <session-mapping key="care_enable_high_volume_prompts" value="true" type="boolean"/>
  <session-mapping key="5" value="0" type="integer"/>
  <session-mapping key="GlobalVars.isSuspended" value="1" type="string"/>
  <session-mapping key="GlobalVars.transferFrom" value="LoginSD_XR" type="string"/>
  <session-mapping key="GlobalVars.noAccountFound" value="true" type="string"/>
  <session-mapping key="callType" value="make_pmt" type="string"/>
  <session-mapping key="operatorPaymentsReqCount" value="1" type="integer"/>
</session-mappings>
