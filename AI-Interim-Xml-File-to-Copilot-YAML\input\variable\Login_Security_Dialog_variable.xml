<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="pin" value="empty" type="String"/>
  <session-mapping key="accessToken" value="empty" type="String"/>
  <session-mapping key="verificationType" value="empty" type="String"/>
  <session-mapping key="verificationValue" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="providerId" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="clearCache" value="empty" type="String"/>
  <session-mapping key="multiLineDetails" value="empty" type="String"/>
  <session-mapping key="GetAccountDetails.securityQuestionCode" value="SQ9" type="string"/>
  <session-mapping key="GlobalVars.tag" value="request-extension" type="string"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="suspended" type="string"/>
  <session-mapping key="GlobalVars.callType" value="make_pmt" type="string"/>
  <session-mapping key="GlobalVars.securityRequired" value="true" type="boolean"/>
  <session-mapping key="shortSecurityCodePrompt" value="true" type="boolean"/>
  <session-mapping key="accountPinToggleOn" value="true" type="boolean"/>
  <session-mapping key="playTransitionalPINprompt" value="true" type="boolean"/>
  <session-mapping key="pinAttempts" value="0" type="integer"/>
  <session-mapping key="reentry" value="false" type="boolean"/>
  <session-mapping key="callType" value="switch_lines" type="string"/>
  <session-mapping key="aniMatch" value="true" type="boolean"/>
  <session-mapping key="collectedMDNUpfront" value="true" type="string"/>
  <session-mapping key="acceptedBCR" value="true" type="boolean"/>
  <session-mapping key="Authenticate.status" value="FAILURE" type="string"/>
  <session-mapping key="Authenticate.acctLocked" value="true" type="boolean"/>
  <session-mapping key="Authenticate.onePinTryRemaining" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.acctLocked" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.onePinTryRemaining" value="true" type="boolean"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="GlobalVars.guestPaymentReattemptLogin" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.dontKnowPIN" value="true" type="boolean"/>
  <session-mapping key="GetBCSParameters.care_enable_guest_payment" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.suspendedRatePlanChange" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.lastPinTry" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.GetAccountDetails" value="null" type="string"/>
  <session-mapping key="GetSubscriberDetails.accountStatus" value="active" type="string"/>
  <session-mapping key="invalidMDNCount" value="1" type="integer"/>
</session-mappings>
