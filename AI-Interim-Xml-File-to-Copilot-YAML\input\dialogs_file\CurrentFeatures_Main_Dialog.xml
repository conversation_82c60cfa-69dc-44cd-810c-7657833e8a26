<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="CurrentFeatures_Main_Dialog">
    <decision-state id="CF1005_HaveFeatures_DS">
      <if cond="(GlobalVars.GetAccountDetails.features == null &amp;&amp; GlobalVars.GetAccountDetails.thirdPartyFeatures == null) || (GlobalVars.GetAccountDetails.features.length == 0 &amp;&amp; GlobalVars.GetAccountDetails.thirdPartyFeatures.length == 0)">
        <action next="CF1004_CheckNeedFutureTransactions_JDA"/>
        <elseif cond="GlobalVars.GetAccountDetails.featuresAreMissing == true">
          <action next="CF1015_GoToCallTransfer_SD"/>
        </elseif>
        <else>
          <action next="CF1004_CheckNeedFutureTransactions_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="CF1004_CheckNeedFutureTransactions_DS">
      <if cond="(GlobalVars.subscriberFuturePricePlanInd == true)">
        <action next="CF1006_GetFutureTransactionDetails_DB_DA"/>
        <else>
          <action next="CF1010_CurrentFeatures_DM"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="CF1006_GetFutureTransactionDetails_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="sessionId" value="GlobalVars.sessionId" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="features" value="" type="String"/>
      <session-mapping key="thirdPartyfeatures" value="" type="String"/>
      <data-access id="GetFutureTransactionDetails_CF1006" classname="com.nuance.metro.dataaccess.GetFutureTransactionDetails_CF1006">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
          <input-variable name="languageCode"/>
          <input-variable name="features"/>
          <input-variable name="thirdPartyfeatures"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="mdn"/>
          <output-variable name="futurePlan"/>
          <output-variable name="planFinalPromptURL"/>
          <output-variable name="futureAddOns"/>
          <output-variable name="futureRequestDate"/>
          <output-variable name="futureMetroAddOnsAdded"/>
          <output-variable name="futureMetroAddOnsRemoved"/>
          <output-variable name="futureTPAddOnsAdded"/>
          <output-variable name="futureTPAddOnsRemoved"/>
          <output-variable name="isFutureAddOnsAdded"/>
          <output-variable name="isFutureAddOnsRemoved"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="(GetFutureTransactionDetails_CF1006 &amp;&amp; GetFutureTransactionDetails_CF1006.status == 'Success')">
          <session-mapping key="GlobalVars.GetFutureTransactionDetails_CF1006" expr="GetFutureTransactionDetails_CF1006"/>
          <session-mapping key="GlobalVars.futurePlan" expr="GetFutureTransactionDetails_CF1006.futurePlan"/>
          <session-mapping key="GlobalVars.futureAddOns" expr="GetFutureTransactionDetails_CF1006.futureAddOns"/>
          <session-mapping key="GlobalVars.futureRequestDate" expr="GetFutureTransactionDetails_CF1006.futureRequestDate"/>
          <session-mapping key="GlobalVars.futureMetroAddOnsAdded" expr="GetFutureTransactionDetails_CF1006.futureMetroAddOnsAdded"/>
          <session-mapping key="GlobalVars.futureMetroAddOnsRemoved" expr="GetFutureTransactionDetails_CF1006.futureMetroAddOnsRemoved"/>
          <session-mapping key="GlobalVars.futureTPAddOnsAdded" expr="GetFutureTransactionDetails_CF1006.futureTPAddOnsAdded"/>
          <session-mapping key="GlobalVars.futureTPAddOnsRemoved" expr="GetFutureTransactionDetails_CF1006.futureTPAddOnsRemoved"/>
          <session-mapping key="GlobalVars.isFutureAddOnsAdded" expr="GetFutureTransactionDetails_CF1006.isFutureAddOnsAdded"/>
          <session-mapping key="GlobalVars.isFutureAddOnsRemoved" expr="GetFutureTransactionDetails_CF1006.isFutureAddOnsRemoved"/>
          <action next="CF1007_CheckHaveFutureDatedChange_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="CF1007_CheckHaveFutureDatedChange_DS">
      <if cond="!(GlobalVars.isFutureAddOnsAdded == true || GlobalVars.isFutureAddOnsAdded == 'true' || GlobalVars.isFutureAddOnsRemoved == true || GlobalVars.isFutureAddOnsRemoved == 'true' )">
        <action next="CF1010_CurrentFeatures_DM"/>
        <elseif cond="GlobalVars.GetFutureTransactionDetails_CF1006.isFutureAddOnsMissing == true">
          <action next="CF1015_GoToCallTransfer_SD"/>
        </elseif>
        <else>
          <action next="CF1008_PlayFutureDatedChange_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="CF1008_PlayFutureDatedChange_PP">
      <session-mapping key="futureMetroAddOnsAdded" value="getListAsCommaSeparatedString(GlobalVars.futureMetroAddOnsAdded)" type="String"/>
      <session-mapping key="futureMetroAddOnsRemoved" value="getListAsCommaSeparatedString(GlobalVars.futureMetroAddOnsRemoved)" type="String"/>
      <session-mapping key="futureTPAddOnsAdded" value="getListAsCommaSeparatedString(GlobalVars.futureTPAddOnsAdded)" type="String"/>
      <session-mapping key="futureTPAddOnsRemoved" value="getListAsCommaSeparatedString(GlobalVars.futureTPAddOnsRemoved)" type="String"/>
      <session-mapping key="futureRequestDate" value="GlobalVars.futureRequestDate" type="String"/>
      <session-mapping key="features" value="" type="String"/>
      <session-mapping key="futureAddOns" value="GlobalVars.futureAddOns" type="String"/>
      <audio>
        <prompt type="custom" expr="features">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayListOfFutureFeatures"/>
        </prompt>
        <prompt id="CF1008_out_15">
          <prompt-segments>
            <audiofile text="That ll take effect" src="CF1008_out_15.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="date" expr="futureRequestDate">
          <param name="dateFormat" value="yyyyMMdd"/>
          <param name="playDayOfMonth" value="true"/>
          <param name="playYear" value="false"/>
          <param name="playDayOfTheWeek" value="false"/>
          <param name="intonation" value="f"/>
        </prompt>
        <prompt id="CF1008_out_16">
          <prompt-segments>
            <audiofile text="Until then" src="CF1008_out_16.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="CF1010_CurrentFeatures_DM"/>
    </play-state>

    <dm-state id="CF1010_CurrentFeatures_DM" type="CUST">
      <session-mapping key="featureSocs" value="getFeaturesFromAccountDetails(GlobalVars.GetAccountDetails)" type="String"/>
      <session-mapping key="featureSocsThirdParty" value="getThirdPartyFeaturesFromAccountDetails(GlobalVars.GetAccountDetails)" type="String"/>
      <session-mapping key="heardThirdPartyInfo" value="GlobalVars.heardThirdPartyInfo" type="String"/>
      <session-mapping key="hasThirdPartyFeatures" value="featureSocsThirdParty != null &amp;&amp; featureSocsThirdParty.length &gt; 0" type="String"/>
      <session-mapping key="hasFeatures" value="(featureSocs != null &amp;&amp; featureSocs.length &gt; 0) || (featureSocsThirdParty != null &amp;&amp; featureSocsThirdParty.length &gt; 0)" type="String"/>
      <session-mapping key="futureAddOns" value="GlobalVars.futureAddOns" type="String"/>
      <session-mapping key="fromDisamg" value="GlobalVars.fromDisamg != undefined ? GlobalVars.fromDisamg : false" type="String"/>
      <session-mapping key="collection_dtmfgrammar1" value="CF1010_CurrentFeatures_DM_dtmf.jsp" type="String"/>
      <session-mapping key="collection_grammar1" value="CF1010_CurrentFeatures_DM.jsp" type="String"/>
      <success>
        <session-mapping key="GlobalVars.fromDisamg" value="false" type="Boolean"/>
        <if cond="hasThirdPartyFeatures == true">
          <session-mapping key="GlobalVars.heardThirdPartyInfo" value="true" type="Boolean"/>
        </if>
        <action label="add-feature">
          <session-mapping key="GlobalVars.callType" expr="'add_feature'"/>
          <session-mapping key="GlobalVars.ifFromCurrentFeatures" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <if cond="GlobalVars.GetAccountDetails.accountStatus == 'active'">
            <action next="CF1020_GoToVoiceStore_SD"/>
            <else>
              <session-mapping key="GlobalVars.tag" expr="'add-feature_suspended'"/>
              <action next="CF1015_GoToCallTransfer_SD"/>
            </else>
          </if>
        </action>
        <action label="hear-plan_details">
          <session-mapping key="GlobalVars.callType" expr="'plan_details'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="CF1025_GoToRatePlan_SD"/>
        </action>
        <action label="remove_feature">
          <action next="CF1015_GoToCallTransfer_SD"/>
        </action>
        <action label="operator"/>
        <action label="repeat">
          <action next="CF1007_CheckHaveFutureDatedChange_JDA"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_CF1010"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
              <prompt id="CF1010_ini_14" cond="heardThirdPartyInfo != true &amp;&amp; hasThirdPartyFeatures == true">
                <prompt-segments>
                  <audiofile text="Third Party charges will be clearly defined on your statement To block those purchases, check your account settings on metrobyt-mobilecom, or say 'operator' now" src="CF1010_ini_14.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="heardThirdPartyInfo != true &amp;&amp; hasThirdPartyFeatures == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_ini_15" cond="fromDisamg == true &amp;&amp; hasFeatures == true">
                <prompt-segments>
                  <audiofile text="You can say repeat that', 'add a feature' or 'remove a feature" src="CF1010_ini_15.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_ini_02" cond="fromDisamg == true &amp;&amp; hasFeatures != true">
                <prompt-segments>
                  <audiofile text="You can say repeat that', or 'add a feature'" src="CF1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_ini_10" cond="!(fromDisamg == true )">
                <prompt-segments>
                  <audiofile text="If you need to, say 'repeat that'  You can also say 'add a feature' or 'what's my rate plan'  If you're finished, you can just hang up" src="CF1010_ini_10.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="CF1010_nm1_02" cond="fromDisamg == true &amp;&amp; hasFeatures == true">
                <prompt-segments>
                  <audiofile text="Please say repeat that', 'add a feature' or 'remove a feature'" src="CF1010_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_04" cond="(fromDisamg == true &amp;&amp; hasFeatures != true)">
                <prompt-segments>
                  <audiofile text="Please say repeat that',or  'add a feature' " src="CF1010_nm1_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_01" cond="!(fromDisamg == true)">
                <prompt-segments>
                  <audiofile text="To hear that information again, say 'repeat that' You can also say 'add a feature' or 'what's my rate plan' If you're finished, you can just hang up" src="CF1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm2_02" cond="fromDisamg == true &amp;&amp; hasFeatures == true">
                <prompt-segments>
                  <audiofile text="To hear your features again say, repeat that',or press star  Or say 'add a feature' or press 1, 'remove a feature' or press 2" src="CF1010_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_05" cond="(fromDisamg == true &amp;&amp; hasFeatures != true)">
                <prompt-segments>
                  <audiofile text="To hear your features again say repeat that', or press star  'add a feature' or press 1 " src="CF1010_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_03" cond="!(fromDisamg == true)">
                <prompt-segments>
                  <audiofile text="Would you like me to repeat the  services you re signed up for?" src="CF1010_nm1_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm2_02" cond="fromDisamg == true &amp;&amp; hasFeatures == true">
                <prompt-segments>
                  <audiofile text="To hear your features again say, repeat that',or press star  Or say 'add a feature' or press 1, 'remove a feature' or press 2" src="CF1010_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_05" cond="(fromDisamg == true &amp;&amp; hasFeatures != true)">
                <prompt-segments>
                  <audiofile text="To hear your features again say repeat that', or press star  'add a feature' or press 1 " src="CF1010_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_03" cond="!(fromDisamg == true)">
                <prompt-segments>
                  <audiofile text="Would you like me to repeat the  services you re signed up for?" src="CF1010_nm1_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_02" cond="fromDisamg == true &amp;&amp; hasFeatures == true">
                <prompt-segments>
                  <audiofile text="Please say repeat that', 'add a feature' or 'remove a feature'" src="CF1010_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_04" cond="(fromDisamg == true &amp;&amp; hasFeatures != true)">
                <prompt-segments>
                  <audiofile text="Please say repeat that',or  'add a feature' " src="CF1010_nm1_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_01" cond="!(fromDisamg == true)">
                <prompt-segments>
                  <audiofile text="To hear that information again, say 'repeat that' You can also say 'add a feature' or 'what's my rate plan' If you're finished, you can just hang up" src="CF1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm2_02" cond="fromDisamg == true &amp;&amp; hasFeatures == true">
                <prompt-segments>
                  <audiofile text="To hear your features again say, repeat that',or press star  Or say 'add a feature' or press 1, 'remove a feature' or press 2" src="CF1010_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_05" cond="(fromDisamg == true &amp;&amp; hasFeatures != true)">
                <prompt-segments>
                  <audiofile text="To hear your features again say repeat that', or press star  'add a feature' or press 1 " src="CF1010_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_03" cond="!(fromDisamg == true)">
                <prompt-segments>
                  <audiofile text="Would you like me to repeat the  services you re signed up for?" src="CF1010_nm1_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm2_02" cond="fromDisamg == true &amp;&amp; hasFeatures == true">
                <prompt-segments>
                  <audiofile text="To hear your features again say, repeat that',or press star  Or say 'add a feature' or press 1, 'remove a feature' or press 2" src="CF1010_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_05" cond="(fromDisamg == true &amp;&amp; hasFeatures != true)">
                <prompt-segments>
                  <audiofile text="To hear your features again say repeat that', or press star  'add a feature' or press 1 " src="CF1010_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_nm1_03" cond="!(fromDisamg == true)">
                <prompt-segments>
                  <audiofile text="Would you like me to repeat the  services you re signed up for?" src="CF1010_nm1_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_CF1010"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
              <prompt id="CF1010_ini_14" cond="heardThirdPartyInfo != true &amp;&amp; hasThirdPartyFeatures == true">
                <prompt-segments>
                  <audiofile text="Third Party charges will be clearly defined on your statement To block those purchases, check your account settings on metrobyt-mobilecom, or say 'operator' now" src="CF1010_ini_14.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="heardThirdPartyInfo != true &amp;&amp; hasThirdPartyFeatures == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_ini_15" cond="fromDisamg == true &amp;&amp; hasFeatures == true">
                <prompt-segments>
                  <audiofile text="You can say repeat that', 'add a feature' or 'remove a feature" src="CF1010_ini_15.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_ini_02" cond="fromDisamg == true &amp;&amp; hasFeatures != true">
                <prompt-segments>
                  <audiofile text="You can say repeat that', or 'add a feature'" src="CF1010_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="CF1010_ini_10" cond="!(fromDisamg == true )">
                <prompt-segments>
                  <audiofile text="If you need to, say 'repeat that'  You can also say 'add a feature' or 'what's my rate plan'  If you're finished, you can just hang up" src="CF1010_ini_10.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_CF1010_dtmf.grxml">
          <grammars filename="CF1010_CurrentFeatures_DM.jsp" count="1">
      </grammars>
          <dtmfgrammars filename="CF1010_CurrentFeatures_DM_dtmf.jsp" count="1">
      </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="CF_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="CF1015_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="CF1015_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="CF1015_GoToCallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="CF1020_GoToVoiceStore_SD">
      <gotodialog next="Voicestore_Main_Dialog"/>
      <action next="CF1020_GoToVoiceStore_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="CF1020_GoToVoiceStore_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="CF1025_GoToRatePlan_SD">
      <gotodialog next="RatePlan_Main_Dialog"/>
      <action next="CF1025_GoToRatePlan_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="CF1025_GoToRatePlan_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  