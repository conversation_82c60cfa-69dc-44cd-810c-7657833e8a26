<states-library xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../framework/stateengine/states.xsd">
	<dialog id="SimpleDM">
		<dm-state id="dlhd0012_ACSMenu_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="1" next="EnrollAnnounceResult_PP">
					<session-mapping key="menuSelection" expr="'ACS_1'"/>
				</action>
				<action label="2" next="EnrollAnnounceResult_PP">
					<session-mapping key="menuSelection" expr="'ACS_2'"/>
				</action>
				<action label="3" next="EnrollAnnounceResult_PP">
					<session-mapping key="menuSelection" expr="'ACS_3'"/>
				</action>
				<action label="4" next="EnrollAnnounceResult_PP">
					<session-mapping key="menuSelection" expr="'ACS_4'"/>
				</action>
				<action label="5" next="EnrollAnnounceResult_PP">
					<session-mapping key="menuSelection" expr="'ACS_5'"/>
				</action>
				<action label="7" next="EnrollAnnounceResult_PP">
					<session-mapping key="menuSelection" expr="'ACS_7'"/>
				</action>
				<action label="8" next="EnrollAnnounceResult_PP">
					<session-mapping key="menuSelection" expr="'ACS_8'"/>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0012_ACSMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For Common Use Airport locations, press 1. For ACS ticketing and procedural questions, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues, press 0. For Common Use Airport locations, press 1. For ACS ticketing and procedural questions, press 2. For Corporate-owned *mobile* devices, press 3. SAP and Ariba, press 4. For Financial Systems, press 5. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="noinput1_prompt1" src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="noinput1_prompt2" src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="noinput2_prompt1" src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="noinput2_prompt2" src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="nomatch1_prompt1" src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="nomatch1_prompt2" src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="nomatch2_prompt1" src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0012_ACSMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="nomatch2_prompt2" src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
		<play-state id="EnrollAnnounceResult_PP">
			<audio>
				<prompt id="rb0450_out_03">
					<prompt-segments>
						<audiofile text="Simple DM is prcoessed successfully" src="rb0450_out_03.wav" />
					</prompt-segments>
				</prompt>
			</audio>
		</play-state>
	</dialog>
<dialog id="ConditionalDM">
		<dm-state id="GenericMenu_DM" type="CUST">
			<success>
				<action label="ACS" next="Conditional_PP">
					<session-mapping key="customMenu" expr="acs"/>
				</action>
				<action label="TechOps" next="Conditional_PP">
					<session-mapping key="customMenu" expr="techOps"/>
				</action>
				<action label="Reservations" next="Conditional_PP">
					<session-mapping key="customMenu" expr="reservations"/>
				</action>
				<action label="OfficeSupport" next="Conditional_PP">
					<session-mapping key="customMenu" expr="officeSupport"/>
				</action>
				<action label="FltOps" next="Conditional_PP">
					<session-mapping key="customMenu" expr="fltOps"/>
				</action>
				<action label="IntlSupport" next="Conditional_PP">
					<session-mapping key="customMenu" expr="intlSupport"/>
				</action>
				<action label="MLTSupport" next="Conditional_PP">
					<session-mapping key="customMenu" expr="mltSupport"/>
				</action>
				<action label="EndeavorAir" next="Conditional_PP">
					<session-mapping key="customMenu" expr="endeavorAir"/>
				</action>
				<action label="RegionalElite" next="Conditional_PP">
					<session-mapping key="customMenu" expr="regionalElite"/>
				</action>
				<action label="Air4" next="Conditional_PP">
					<session-mapping key="customMenu" expr="air4"/>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="gl_ni3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="gl_nm3_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="alarmCode" expr="ALARM_CODE_ERROR"/>
				<session-mapping key="alarmSeverity" expr="ALARM_SEVERITY_ERROR"/>
				<session-mapping key="alarmMessage" expr="'[' + _event + '] : [' + _message + ']'"/>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit.dvxml"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0010_GenericMenu_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text="initial_prompt_1 elite is false" src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text="initial_prompt_2 elite is true" src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. MLT Support, 7. Endeavor Air support, press 8. Or for Air4 support, press 9." src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text="For Airport Operations or Cargo support, press 1. for Tech Ops support, press 2. Reservations support, 3. Office and campus support, 4. Inflight or Flight operations, 5. International support, 6. Or, for Regional Elite, press 7." src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text="noinput_1 elite is false." src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text="noinput_1 elite is true" src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text="noinput_2 elite is false" src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text="noinput_2 elite is true" src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text="nomatch_1 elite is false" src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text=" nomatch_1 elite is true" src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
								<prompt-segments>
									<audiofile text=" nomatch_2 elite is false" src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
								<prompt-segments>
									<audiofile text="nomatch_2 elite is true" src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
				<play-state id="Conditional_PP">
			<audio>
				<prompt id="rb0310_ini_12_part1" bargein="false">
					<prompt-segments>
						<audiofile text="Conditional DM prcessed successfully" src="rb0310_ini_12_part1.wav" />
					</prompt-segments>
				</prompt>
			</audio>
		</play-state>
	</dialog>
	<dialog id="ComplexDM">
		<script className="com.nuance.riyadhbank.vocalpassword.script.ResetEnrollmentVariables" />
		<script className="com.nuance.riyadhbank.vocalpassword.script.SetLanguage" />
		<dm-state id="Enrollment_DM" className="com.nuance.riyadhbank.vocalpassword.stateengine.DMVocalPasswordRecordTag">
			<var name="collectionCounter" path="collectionCounter" scope="session" isNamelist="true" />
			<var name="collectionNoInputCounter" path="collectionNoInputCounter" scope="session" isNamelist="true" />
			<var name="collectionNoMatchCounter" path="collectionNoMatchCounter" scope="session" isNamelist="true" />
			<var name="isComingFrom_rb0330" path="isComingFrom_rb0330" scope="session" isNamelist="true" />
			<var name="collectionNiOrNm_Temp" path="collectionNiOrNm_Temp" scope="session" isNamelist="true" />
			<var name="repromptAfterTrainError" path="repromptAfterTrainError" scope="session" isNamelist="true" />
			<var name="callComesFromAgent" path="callComesFromAgent" scope="session" isNamelist="true" />
			<success>
				<action label="1" next="complex_PP">
					<session-mapping key="menuSelection" expr="'EndeavorAir_1'"/>
				</action>
				<action label="2" next="complex_PP">
					<session-mapping key="menuSelection" expr="'EndeavorAir_2'"/>
				</action>
				<action label="3" next="complex_PP">
					<session-mapping key="menuSelection" expr="'EndeavorAir_3'"/>
				</action>
				<action label="4" next="complex_PP">
					<session-mapping key="menuSelection" expr="'EndeavorAir_4'"/>
				</action>
				<action label="7" next="complex_PP">
					<session-mapping key="menuSelection" expr="'EndeavorAir_7'"/>
				</action>
				<action label="8" next="complex_PP">
					<session-mapping key="menuSelection" expr="'EndeavorAir_8'"/>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<action next="rb0330_CheckEnrollPassphrase_DS">
					<session-mapping key="collectionNiOrNm_Temp" value="nomatch" type="String" />
					<session-mapping key="repromptAfterTrainError" value="false" type="Boolean" />
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="collectionCounter" />
						<param name="param2" value="collectionNoMatchCounter" />
						<param name="param3" value="collectionMaxErrorCounter" />
					</script>
					<var name="callerLanguage" path="callerLanguage" scope="session" />
					<if cond="callerLanguage == 'ar'">
						<session-mapping key="enrollmentLanguage" value="ar" type="String" />
						<elseif cond="callerLanguage == 'en'">
							<session-mapping key="enrollmentLanguage" value="en" type="String" />
						</elseif>
					</if>
				</action>
			</event>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<action next="rb0330_CheckEnrollPassphrase_DS">
					<session-mapping key="collectionNiOrNm_Temp" value="noinput" type="String" />
					<session-mapping key="repromptAfterTrainError" value="false" type="Boolean" />
					<script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
						<param name="param1" value="collectionCounter" />
						<param name="param2" value="collectionNoInputCounter" />
						<param name="param3" value="collectionMaxErrorCounter" />
					</script>
					<var name="callerLanguage" path="callerLanguage" scope="session" />
					<if cond="callerLanguage == 'ar'">
						<session-mapping key="enrollmentLanguage" value="ar" type="String" />
						<elseif cond="callerLanguage == 'en'">
							<session-mapping key="enrollmentLanguage" value="en" type="String" />
						</elseif>
					</if>
				</action>
			</event>
			<collection_configuration inputmodes="voice">
				<threshold_configuration maxnomatches="0" maxnoinputs="0" />
				<vxml_properties inputmodes="voice">
					<property_map>
						<key>recordutterance</key>
						<value>true</value>
					</property_map>
				</vxml_properties>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<if cond="collectionCounter == 0 &amp;&amp; callComesFromAgent == true">
								<prompt id="rb0310_ini_12_part2" bargein="false">
									<prompt-segments>
										<audiofile text="So... after the tone please say: 'At Riyad Bank my voice is my password'." src="rb0310_ini_12_part2.wav" />
									</prompt-segments>
								</prompt>
								<elseif cond="collectionCounter == 0 &amp;&amp; callComesFromAgent == false">
									<prompt id="rb0310_ini_13" bargein="false">
										<prompt-segments>
											<audiofile text="So ... after the tone please say: 'At Riyad Bank my voice is my password'. (alternative: Great! To create your voice print, I'm going to ask you to repeat a phrase a few times so the system can analyze your voice.  So, after the tone please say 'With Riyad Bank my voice is my password#)" src="rb0310_ini_13.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'noinput' &amp;&amp; collectionNoInputCounter == 1">
									<prompt id="rb0310_ini_02" bargein="false">
										<prompt-segments>
											<audiofile text="After the tone please say: 'At Riyad Bank my voice is my password'" src="rb0310_ini_02.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'noinput' &amp;&amp; collectionNoInputCounter == 2">
									<prompt id="rb0310_ini_03" bargein="false">
										<prompt-segments>
											<audiofile text="Sorry, I didn't hear that.  After the tone, please repeat the following phrase: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_03.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'nomatch' &amp;&amp; collectionNoMatchCounter == 1">
									<prompt id="rb0310_ini_04" bargein="false">
										<prompt-segments>
											<audiofile text="Let's try that again.  After the tone, please say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_04.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'nomatch' &amp;&amp; collectionNoMatchCounter == 2">
									<prompt id="rb0310_ini_05" bargein="false">
										<prompt-segments>
											<audiofile text="Sorry, it's important you repeat *exactly* the same phrase.  After the tone, say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_05.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; repromptAfterTrainError == true &amp;&amp; collectionCounter &lt; 4">
									<prompt id="rb0310_ini_06" bargein="false">
										<prompt-segments>
											<audiofile text="I'm having some trouble getting that.  Let's try again.  After the tone say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_06.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 1">
									<prompt id="rb0310_ini_07" bargein="false">
										<prompt-segments>
											<audiofile text="Again, after the tone say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_07.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 2">
									<prompt id="rb0310_ini_08" bargein="false">
										<prompt-segments>
											<audiofile text="And again, after the tone: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_08.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 3">
									<prompt id="rb0310_ini_09" bargein="false">
										<prompt-segments>
											<audiofile text="Almost there ... after the tone: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_09.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false">
									<prompt id="rb0310_ini_10" bargein="false">
										<prompt-segments>
											<audiofile text="And last time: At Riyad Bank my voice is my password." src="rb0310_ini_10.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
							</if>
							<prompt id="rb0310_ini_11" bargein="false">
								<prompt-segments>
									<audiofile text="tone" src="rb0310_ini_11.wav" />
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id="" />
					<helpprompts count="2" filename="none" text="none" id="" />
					<repeatprompts count="1" />
					<repeatprompts count="2" />
					<nomatchprefixes count="1" />
					<nomatchprefixes count="2" />
					<nomatchprefixes count="3" />
					<noinputprefixes count="1" />
					<noinputprefixes count="2" />
					<noinputprefixes count="3" />
					<noinputprompts count="1" />
					<nomatchprompts count="1" />
					<notoconfirmprefixes count="2" />
					<notoconfirmprefixes count="3" />
					<notoconfirmprefixes count="1" />
					<notoconfirmprompts count="1" />
					<notoconfirmprefixes count="2" />
					<notoconfirmprompts count="2" />
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxnomatches="1" maxnoinputs="1" />
				<vxml_properties bargein="false" inputmodes="voice" />
				<failureprompt count="1" />
				<commandconfirmationprompts count="1" bargein="false" filename="none" text="none" id="" />
				<successprompts count="1" />
				<successprompts count="2" />
				<successprompts count="3" />
				<successcorrectedprompt count="1" />
			</global_configuration>
			<confirmation_configuration>
				<threshold_configuration maxnomatches="1" maxnoinputs="1" maxnegativeconfirmations="1" />
				<prompt_configuration>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_noinput_confirm1_01" />
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nomatch_confirm1_01" />
						</audio>
					</nomatchprompts>
				</prompt_configuration>
			</confirmation_configuration>
		</dm-state>
		<play-state id="complex_PP">
			<audio>
				<prompt id="rb0450_out_01">
					<prompt-segments>
						<audiofile text="Complex DM processed successfully" src="rb0450_out_01.wav" />
					</prompt-segments>
				</prompt>
			</audio>
		</play-state>
			</dialog>
	
</states-library>