import yaml
from ruamel.yaml import <PERSON><PERSON><PERSON>
from io import StringIO

# Custom Dumper class from the first script to handle indentation
class MyDumper(yaml.Dumper):
    def increase_indent(self, flow=False, indentless=False):
        return super(MyDumper, self).increase_indent(flow, False)

# Function to format multiline strings (present in both scripts)
def format_multiline_strings(data):
    if isinstance(data, dict):
        for key, value in data.items():
            data[key] = format_multiline_strings(value)
    elif isinstance(data, list):
        return [format_multiline_strings(item) for item in data]
    elif isinstance(data, str):
        return " ".join(data.splitlines())  # Join lines into one
    return data

# Custom representer for handling dicts, lists, and strings (from first script)
def custom_representer(dumper, data):
    if isinstance(data, dict):
        return dumper.represent_dict(data.items())
    elif isinstance(data, list):
        return dumper.represent_list(data)
    else:
        return dumper.represent_str(data)

# Function to format <PERSON><PERSON><PERSON> with customizations (from first script)
def format_yaml(yaml_input):
    # Load the YAML input
    data = yaml.safe_load(yaml_input)

    # Format multiline strings in the data
    formatted_data = format_multiline_strings(data)

    # Register the custom representer with the custom dumper
    yaml.add_representer(dict, custom_representer, Dumper=MyDumper)
    yaml.add_representer(list, custom_representer, Dumper=MyDumper)
    yaml.add_representer(str, custom_representer, Dumper=MyDumper)
    
    # Dump the data with the specific indentation, structure, and increased width
    formatted_yaml = yaml.dump(formatted_data, Dumper=MyDumper, width=8192, sort_keys=False, default_flow_style=False, indent=2)

    return formatted_yaml

# This function handles YAML file formatting for both PyYAML and ruamel.yaml
def indent_yaml(input_file, output_file):
    # Read the input YAML file
    with open(input_file, 'r') as file:
        yaml_data = file.read()

    # Format the YAML data using the custom format function
    formatted_yaml = format_yaml(yaml_data)

    # Write the formatted YAML to the output file
    with open(output_file, 'w') as file:
        file.write(formatted_yaml)

    return output_file

# Function to modify indentation for lines beyond a certain line (shared functionality)
def topic_yaml_indentation(input_file, output_file):
    start_line = 10
    with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
        for i, line in enumerate(infile, start=1):
            if i >= start_line:
                # Add eight spaces to the start of each line from the specified line number
                modified_line = ' ' * 8 + line
                outfile.write(modified_line)
            else:
                # Write the line without modification before the start line
                outfile.write(line)

# New function for advanced YAML formatting using ruamel.yaml (step5 functionality)
def indent_yaml_step5(input_file, output_file):
    # Initialize YAML object from ruamel.yaml
    yaml = YAML()
    yaml.preserve_quotes = True  # Preserve quotes in YAML
    yaml.width = 8192  # Set a high width to prevent line wrapping
    yaml.indent(mapping=2, sequence=4, offset=2)  # Advanced indentation

    # Read the input YAML file
    with open(input_file, 'r') as file:
        data = yaml.load(file)

    # Write the formatted YAML to the output file
    with open(output_file, 'w') as file:
        yaml.dump(data, file)

    return output_file