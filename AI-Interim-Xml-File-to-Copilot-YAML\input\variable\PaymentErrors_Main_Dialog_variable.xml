<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="accountNumber" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="GlobalVars.paymentErrorCount" value="1" type="string"/>
  <session-mapping key="GlobalVars.authorizationFailureHandling" value="waitPrompting" type="string"/>
  <session-mapping key="paymentErrorCount" value="2" type="integer"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="active" type="string"/>
  <session-mapping key="GlobalVars.guestPayment" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.paymentsEntryPoint" value="228Suspended" type="string"/>
  <session-mapping key="GlobalVars.attemptedCheckUnhotlineEligibility" value="true" type="string"/>
  <session-mapping key="isEligibleForUnhotlineCheck" value="true" type="boolean"/>
  <session-mapping key="CheckUnhotlineEligibility.isEligible" value="true" type="boolean"/>
  <session-mapping key="authorizationFailureHandling" value="maxAttemptExceeded" type="string"/>
  <session-mapping key="guestPayment" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.extensionAllowed" value="true" type="boolean"/>
  <session-mapping key="payingWithEWallet" value="true" type="boolean"/>
  <session-mapping key="extensionAllowed" value="true" type="boolean"/>
</session-mappings>
