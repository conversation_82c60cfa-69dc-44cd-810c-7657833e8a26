<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="RatePlan_GetPlan_Dialog">
    <dm-state id="RP0520_AskRatePlan_DM" type="CUST">
      <session-mapping key="FirstTimeRP0520" value="GlobalVars.FirstTimeRP0520" type="String"/>
      <session-mapping key="choseInvalidPlan" value="GlobalVars.choseInvalidPlan" type="String"/>
      <session-mapping key="declineChangeCounter" value="GlobalVars.declineChangeCounter" type="String"/>
      <session-mapping key="revisit" value="GlobalVars.revisit == undefined ? false : GlobalVars.revisit" type="String"/>
      <session-mapping key="plansList" value="" type="String"/>
      <session-mapping key="subPlansList" value="" type="String"/>
      <session-mapping key="finalList" value="" type="String"/>
      <session-mapping key="planType" expr="'Both'"/>
      <session-mapping key="reentry" expr="GlobalVars.RP0520reentry == undefined ? false : GlobalVars.RP0520reentry"/>
      <success>
        <session-mapping key="GlobalVars.FirstTimeRP0520" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.RP0520reentry" value="true" type="Boolean"/>
        <action label="dont_change">
          <audio>
            <prompt id="RP0520_out_01">
              <prompt-segments>
                <audiofile text="No problem" src="RP0520_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <gotodialog next="RatePlan_Change#RP1335_GoToAnythingElse_SD"/>
        </action>
        <action label="none_of_those">
          <session-mapping key="noneOfThoseCount" expr="noneOfThoseCount + 1"/>
          <session-mapping key="GlobalVars.revisit" value="true" type="Boolean"/>
          <if cond="noneOfThoseCount &lt; 2">
            <if cond="(GlobalVars.ratePlans.length &lt;= 6)">
              <gotodialog next="RatePlan_Main#RP1280_GoToCallTransfer_SD"/>
              <elseif cond="(GlobalVars.ratePlans.length == 7)">
                <audio>
                  <prompt id="RP0520_out_03">
                    <prompt-segments>
                      <audiofile text="Ok I have one more plan For " src="RP0520_out_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </audio>
                <action next="RP0520_AskRatePlan_DM"/>
              </elseif>
              <elseif cond="(GlobalVars.ratePlans.length &gt; 7)">
                <audio>
                  <prompt id="RP0520_out_04">
                    <prompt-segments>
                      <audiofile text="Ok, here are the last ones For " src="RP0520_out_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </audio>
                <action next="RP0520_AskRatePlan_DM"/>
              </elseif>
            </if>
            <else>
              <gotodialog next="RatePlan_Main#RP1280_GoToCallTransfer_SD"/>
            </else>
          </if>
        </action>
        <action label="main_menu">
          <action next="getReturnLink()"/>
        </action>
        <action label="start_over">
          <session-mapping key="GlobalVars.FirstTimeRP0520" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.RP0520reentry" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.revisit" value="false" type="Boolean"/>
          <action next="RP0520_AskRatePlan_DM"/>
        </action>
        <action label="default">
          <if cond="RP0520_AskRatePlan_DM.nbestresults != undefined">
            <session-mapping key="GlobalVars.ratePlanSelectionType" expr="RP0520_AskRatePlan_DM.nbestresults[0].interpretation.selectionType"/>
            <session-mapping key="GlobalVars.nbestresults" expr="RP0520_AskRatePlan_DM.nbestresults"/>
          </if>
          <session-mapping key="GlobalVars.selectedPlan" expr="RP0520_AskRatePlan_DM.returnvalue"/>
          <if cond="GlobalVars.ratePlanSelectionType != undefined &amp;&amp; GlobalVars.ratePlanSelectionType == 'dtmfOption'">
            <action next="RP0526_DescribePlanAskChange_DM"/>
          </if>
          <action next="RP1050_CheckDisambigNeeded_JDA"/>
        </action>
        <action label="operator"/>
        <action label="repeat" next="RP0520_AskRatePlan_DM"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP0520_ini_02" cond="FirstTimeRP0520 == true">
                <prompt-segments>
                  <audiofile text="To make sure all the features on your new plan will work on your phone, you can check out metrobyt-mobilecom" src="RP0520_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="FirstTimeRP0520 == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_ini_03" cond="FirstTimeRP0520 == true">
                <prompt-segments>
                  <audiofile text="These prices include all taxes and regulatory fees You'll get a chance to hear further plan details once you've made a selection" src="RP0520_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_ini_04" cond="FirstTimeRP0520 == true">
                <prompt-segments>
                  <audiofile text="Please choose the one you're interested " src="RP0520_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_ini_12" cond="FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1">
                <prompt-segments>
                  <audiofile text="Ok, please make another selection  or say 'dont change my plan' " src="RP0520_ini_12.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_ini_13" cond="(FirstTimeRP0520 == true) || (FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1)">
                <prompt-segments>
                  <audiofile text="for " src="RP0520_ini_13.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_rin_01" cond="reentry &amp;&amp; choseInvalidPlan == true &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP0520 == true) || (FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="Here are the plans we DO have " src="RP0520_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_rin_02" cond="reentry &amp;&amp; choseInvalidPlan != true &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP0520 == true) || (FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="Ok, so choose the plan you may be interested in " src="RP0520_rin_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="reentry &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP0520 == true) || (FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP0520"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_ini_05">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu' " src="RP0520_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="RP0520_ini_13">
                <prompt-segments>
                  <audiofile text="for " src="RP0520_ini_13.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP0520"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_ini_05">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu' " src="RP0520_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP0520_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu'  " src="RP0520_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP0520"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="RP0520_nm2_02">
                <prompt-segments>
                  <audiofile text="You can also say more details, none of those, or don t change my plan" src="RP0520_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP0520"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="RP0520_nm2_02">
                <prompt-segments>
                  <audiofile text="You can also say more details, none of those, or don t change my plan" src="RP0520_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu'  " src="RP0520_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP0520"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="RP0520_nm2_02">
                <prompt-segments>
                  <audiofile text="You can also say more details, none of those, or don t change my plan" src="RP0520_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP0520"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="RP0520_nm2_02">
                <prompt-segments>
                  <audiofile text="You can also say more details, none of those, or don t change my plan" src="RP0520_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP0520_ini_02" cond="FirstTimeRP0520 == true">
                <prompt-segments>
                  <audiofile text="To make sure all the features on your new plan will work on your phone, you can check out metrobyt-mobilecom" src="RP0520_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="FirstTimeRP0520 == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_ini_03" cond="FirstTimeRP0520 == true">
                <prompt-segments>
                  <audiofile text="These prices include all taxes and regulatory fees You'll get a chance to hear further plan details once you've made a selection" src="RP0520_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_ini_04" cond="FirstTimeRP0520 == true">
                <prompt-segments>
                  <audiofile text="Please choose the one you're interested " src="RP0520_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_ini_12" cond="FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1">
                <prompt-segments>
                  <audiofile text="Ok, please make another selection  or say 'dont change my plan' " src="RP0520_ini_12.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_ini_13" cond="(FirstTimeRP0520 == true) || (FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1)">
                <prompt-segments>
                  <audiofile text="for " src="RP0520_ini_13.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_rin_01" cond="reentry &amp;&amp; choseInvalidPlan == true &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP0520 == true) || (FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="Here are the plans we DO have " src="RP0520_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_rin_02" cond="reentry &amp;&amp; choseInvalidPlan != true &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP0520 == true) || (FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="Ok, so choose the plan you may be interested in " src="RP0520_rin_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="reentry &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP0520 == true) || (FirstTimeRP0520 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP0520"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0520_ini_05">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu' " src="RP0520_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP0520_AskRatePlan_DM.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="RP0520_AskRatePlan_DM_dtmf.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="RP0526_DescribePlanAskChange_DM" type="YSNO">
      <session-mapping key="soc" value="GlobalVars.selectedPlan" type="String"/>
      <session-mapping key="audioMessageKey" value="care_configurable_rate_plan_downgrade_audio" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.newRatePlan" expr="GlobalVars.selectedPlan"/>
          <gotodialog next="RatePlan_Main#RP0301_CheckPlanOptions_DS"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.declineChangeCounter" expr="GlobalVars.declineChangeCounter+1"/>
          <session-mapping key="GlobalVars.revisit" value="false" type="Boolean"/>
          <gotodialog next="RatePlan_Main#RP0326_CheckDeclineNextSteps_DS"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_RP0526"/>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="RP0526_ini_04">
                <prompt-segments>
                  <audiofile text="Would you like to switch to this plan? " src="RP0526_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_RP0526"/>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="RP0526_ini_04">
                <prompt-segments>
                  <audiofile text="Would you like to switch to this plan? " src="RP0526_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP0526_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to switch to this plan? " src="RP0526_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0526_nm2_01">
                <prompt-segments>
                  <audiofile text="To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 " src="RP0526_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0526_nm2_01">
                <prompt-segments>
                  <audiofile text="To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 " src="RP0526_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0526_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to switch to this plan? " src="RP0526_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0526_nm2_01">
                <prompt-segments>
                  <audiofile text="To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 " src="RP0526_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0526_nm2_01">
                <prompt-segments>
                  <audiofile text="To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 " src="RP0526_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_RP0526"/>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="RP0526_ini_04">
                <prompt-segments>
                  <audiofile text="Would you like to switch to this plan? " src="RP0526_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP0526_DescribePlanAskChange_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP0526_DescribePlanAskChange_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="RP0511_AskAddOrRemoveLines_DM" type="YSNO">
      <success>
        <action label="true">
          <gotodialog next="RatePlan_Main#RP1280_GoToCallTransfer_SD"/>
        </action>
        <action label="false">
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP0511_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to add or remove any of the lines on your account? " src="RP0511_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="RP0511_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to add or remove any of the lines on your account? " src="RP0511_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP0511_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2" src="RP0511_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0511_nm2_02">
                <prompt-segments>
                  <audiofile text="If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2" src="RP0511_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0511_nm2_02">
                <prompt-segments>
                  <audiofile text="If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2" src="RP0511_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0511_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2" src="RP0511_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0511_nm2_02">
                <prompt-segments>
                  <audiofile text="If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2" src="RP0511_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP0511_nm2_02">
                <prompt-segments>
                  <audiofile text="If you'd like to add or remove lines on your account say 'yes' or press 1  If not say 'no' or press 2" src="RP0511_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP0511_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to add or remove any of the lines on your account? " src="RP0511_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP0511_AskAddOrRemoveLines_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP0511_AskAddOrRemoveLines_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="RP1050_CheckDisambigNeeded_DS">
      <session-mapping key="requestedPrice" value="" type="String"/>
      <session-mapping key="isSuspended" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" type="String"/>
      <if cond="GlobalVars.ratePlanSelectionType == 'price'">
        <session-mapping key="requestedPrice" expr="GlobalVars.nbestresults[0].interpretation.dm_root"/>
        <session-mapping key="GlobalVars.availablePlansArray" expr="getRatePlansForPrice(GlobalVars.ratePlans, requestedPrice, GlobalVars.isOnFamilyPlan)"/>
        <if cond="GlobalVars.availablePlansArray.length &gt; 1">
          <session-mapping key="GlobalVars.choseInvalidPlan" value="false" type="Boolean"/>
          <action next="RP1055_GoToDisambiguateRatePlan_SD"/>
          <elseif cond="GlobalVars.availablePlansArray.length == 1">
            <session-mapping key="GlobalVars.choseInvalidPlan" value="false" type="Boolean"/>
            <session-mapping key="GlobalVars.selectedPlan" expr="GlobalVars.availablePlansArray[0].soc"/>
            <session-mapping key="GlobalVars.newRatePlan" expr="GlobalVars.availablePlansArray[0].soc"/>
            <if cond="isSuspended == true">
              <gotodialog next="RatePlan_Change#RP1416_DescribePlanAskChange_DM"/>
              <else>
                <action next="RP0526_DescribePlanAskChange_DM"/>
              </else>
            </if>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.choseInvalidPlan" value="true" type="Boolean"/>
            <action next="RP1345_PlanNotAvailable_PP"/>
          </else>
        </if>
        <else>
          <if cond="(GlobalVars.isComingFromRp1411 == true)">
            <session-mapping key="GlobalVars.availablePlansArray" expr="GlobalVars.ratePlans"/>
            <session-mapping key="GlobalVars.selectedPlan" expr="GlobalVars.availablePlansArray[0].soc"/>
            <elseif cond="(GlobalVars.isComingFromRp1420 == true)">
              <session-mapping key="GlobalVars.availablePlansArray" expr="getRatePlansByName(GlobalVars.ratePlans,GlobalVars.selectedPlan)"/>
            </elseif>
            <else>
              <session-mapping key="GlobalVars.availablePlansArray" expr="getRatePlansFromNBest(GlobalVars.nbestresults)"/>
            </else>
          </if>
          <if cond="GlobalVars.availablePlansArray.length &gt; 1">
            <session-mapping key="GlobalVars.choseInvalidPlan" value="false" type="Boolean"/>
            <action next="RP1055_GoToDisambiguateRatePlan_SD"/>
            <else>
              <session-mapping key="GlobalVars.choseInvalidPlan" value="false" type="Boolean"/>
              <session-mapping key="GlobalVars.newRatePlan" expr="GlobalVars.availablePlansArray[0].soc"/>
              <if cond="isSuspended == true">
                <gotodialog next="RatePlan_Change#RP1416_DescribePlanAskChange_DM"/>
                <else>
                  <action next="RP0526_DescribePlanAskChange_DM"/>
                </else>
              </if>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="RP1055_GoToDisambiguateRatePlan_SD">
      <gotodialog next="DisambiguatePlan_Main_Dialog"/>
      <action next="RP1055_GoToDisambiguateRatePlan_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="RP1055_GoToDisambiguateRatePlan_SD_return_CS">
      <session-mapping key="isSuspended" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" type="String"/>
      <if cond="GlobalVars.disambigNoMatches == true">
        <gotodialog next="RatePlan_Change#RP1335_GoToAnythingElse_SD"/>
        <else>
          <session-mapping key="GlobalVars.confirmingRatePlanFromState" expr="'RP1055_GoToDisambiguateRatePlan_SD_return'"/>
          <session-mapping key="GlobalVars.selectedPlan" expr="GlobalVars.newPlanSOC"/>
          <if cond="isSuspended == true">
            <gotodialog next="RatePlan_Change#RP1416_DescribePlanAskChange_DM"/>
            <else>
              <action next="RP0526_DescribePlanAskChange_DM"/>
            </else>
          </if>
        </else>
      </if>
    </custom-state>

    <play-state id="RP1345_PlanNotAvailable_PP">
      <audio>
        <prompt id="RP1345_out_01">
          <prompt-segments>
            <audiofile text="I m sorry, I can t offer you this plan right now" src="RP1345_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="GlobalVars.suspendedRatePlanChange == true">
        <gotodialog next="RatePlan_Change#RP1415_ListRatePlans_DM"/>
        <else>
          <action next="RP0520_AskRatePlan_DM"/>
        </else>
      </if>
    </play-state>

  </dialog>
  