<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="WR_AutopayManageCardWrap_Main_Dialog">
    <dm-state id="WR1000_CancelSetupAutoPayWrap_DM" type="CUST">
      <success>
        <action label="main_menu">
          <action next="WR1005_MainMenu_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="WR1000_ini_01">
                <prompt-segments>
                  <audiofile text="If you're finished  you can simply hang up  If you need anything else say Main Menu" src="WR1000_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="WR1000_ini_01">
                <prompt-segments>
                  <audiofile text="If you're finished  you can simply hang up  If you need anything else say Main Menu" src="WR1000_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="WR1000_nm1_01">
                <prompt-segments>
                  <audiofile text="If you need anything else say 'main menu' or press 1  Otherwise you can simply hang up" src="WR1000_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="WR1000_nm2_01">
                <prompt-segments>
                  <audiofile text="If you need anything else press 1  Otherwise you can simply hang up" src="WR1000_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="WR1000_nm2_01">
                <prompt-segments>
                  <audiofile text="If you need anything else press 1  Otherwise you can simply hang up" src="WR1000_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="WR1000_nm1_01">
                <prompt-segments>
                  <audiofile text="If you need anything else say 'main menu' or press 1  Otherwise you can simply hang up" src="WR1000_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="WR1000_nm2_01">
                <prompt-segments>
                  <audiofile text="If you need anything else press 1  Otherwise you can simply hang up" src="WR1000_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="WR1000_nm2_01">
                <prompt-segments>
                  <audiofile text="If you need anything else press 1  Otherwise you can simply hang up" src="WR1000_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="WR1000_ini_01">
                <prompt-segments>
                  <audiofile text="If you're finished  you can simply hang up  If you need anything else say Main Menu" src="WR1000_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="WR1000_CancelSetupAutopayWrap_DM.grxml" count="1"/>
          <dtmfgrammars filename="WR1000_CancelSetupAutopayWrap_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="WR1005_MainMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
    </subdialog-state>
  </dialog>
  