<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="MainMenu_CheckSecurity_Dialog">
    <decision-state id="MM1050_CheckContinueToDestination_DS">
      <if cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ9')">
        <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'not_authenticated_high_security_account')"/>
        <gotodialog next="MainMenu_Routing#MM1545_GoToCallTransfer_SD"/>
        <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ8')">
          <action next="MM1070_GoToGoodbye_SD"/>
        </elseif>
        <elseif cond="GlobalVars.securityRequired == true">
          <action next="MM1055_CheckAlreadyHavePIN_JDA"/>
        </elseif>
        <else>
          <gotodialog next="MainMenu_Routing#MM1510_RouteToDestination_DS"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="MM1055_CheckAlreadyHavePIN_DS">
      <if cond="GlobalVars.collectedPIN">
        <gotodialog next="MainMenu_Routing#MM1510_RouteToDestination_DS"/>
        <else>
          <action next="MM1060_GoToLogin_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="MM1060_GoToLogin_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="MM1060_GoToLogin_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MM1060_GoToLogin_SD_return_CS">
      <if cond="GlobalVars.callType == 'my_account'">
        <gotodialog next="MainMenu_Main#MM1035_MyAccount_DM"/>
        <else>
          <gotodialog next="MainMenu_Routing#MM1510_RouteToDestination_DS"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="MM1070_GoToGoodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </subdialog-state>
    <custom-state id="MM1070_GoToGoodbye_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

  </dialog>
  