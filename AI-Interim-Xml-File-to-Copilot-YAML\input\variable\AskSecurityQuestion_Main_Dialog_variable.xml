<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="pin" value="empty" type="String"/>
  <session-mapping key="accessToken" value="empty" type="String"/>
  <session-mapping key="verificationType" value="empty" type="String"/>
  <session-mapping key="verificationValue" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="providerId" value="empty" type="String"/>
  <session-mapping key="GetAccountDetails.securityQuestionCode" value="SQ2" type="string"/>
  <session-mapping key="GlobalVars.callType" value="dsgExtension" type="string"/>
  <session-mapping key="GlobalVars.saidOperatorAQ1105" value="false" type="boolean"/>
  <session-mapping key="securityQuestionCode" value="Q13" type="string"/>
  <session-mapping key="callType" value="dsgExtension" type="string"/>
  <session-mapping key="saidOperator" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.saidOperatorAQ1110" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.saidOperatorAQ1115" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.saidOperatorAQ1120" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.saidOperatorAQ1125" value="false" type="boolean"/>
  <session-mapping key="Authenticate.status" value="FAILURE" type="string"/>
  <session-mapping key="GlobalVars.smsReceived" value="true" type="string"/>
  <session-mapping key="securityQuestionAttempts" value="3" type="string"/>
  <session-mapping key="GlobalVars.askSQEntry" value="EWallet" type="string"/>
</session-mappings>
