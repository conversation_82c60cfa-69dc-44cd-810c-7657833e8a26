"""
Complete pipeline run with fixed indentation function
This script runs the entire pipeline from start to finish
"""
import subprocess
import sys
import os
from pathlib import Path

def run_complete_pipeline():
    """Run the complete pipeline with all steps"""
    
    print("🚀 COMPLETE PIPELINE EXECUTION")
    print("=" * 50)
    
    steps = [
        ("Step 0: Input Cleanup", "python step0_input_cleanup.py"),
        ("Step 1: Separate Dialogs", "python step1_separate_dialogs.py"),
        ("Step 2: Generate Topic Template", "python step2_generate_topic_template.py"),
        ("Step 3: Variable Extraction", "python step3_variable_extraction.py"),
        ("Step 4: Agent Decision (FIXED)", "python step4_callAgentDecision_FIXED.py"),
        ("Step 5: Generate Bot YAML", "python step5_generate_bot_yaml.py"),
        ("Step 6: Post Processing", "python step6_post_processing.py")
    ]
    
    # Check if we have the main input file
    input_files = list(Path("./input/").glob("*.xml"))
    if not input_files:
        print("❌ No XML input files found in ./input/")
        print("   Please add your XML file to the input directory")
        return False
    
    print(f"📁 Found {len(input_files)} input XML file(s):")
    for file in input_files:
        print(f"   - {file.name}")
    
    # Create necessary directories
    os.makedirs("./output/topic_yaml/", exist_ok=True)
    os.makedirs("./output/unformatted_topic_yaml/", exist_ok=True)
    os.makedirs("./output/bot_yaml/", exist_ok=True)
    
    success_count = 0
    
    for i, (step_name, command) in enumerate(steps, 1):
        print(f"\n🔄 {i}/{len(steps)}: {step_name}")
        print(f"   Command: {command}")
        
        try:
            # Run the command
            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True,
                timeout=1800  # 30 minute timeout
            )
            
            if result.returncode == 0:
                print(f"✅ {step_name} completed successfully")
                success_count += 1
                
                # Show some output for key steps
                if "step4" in command:
                    yaml_files = list(Path("./output/topic_yaml/").glob("*.yml"))
                    print(f"   📊 Generated {len(yaml_files)} YAML files")
                
            else:
                print(f"❌ {step_name} failed")
                print(f"   Error: {result.stderr}")
                
                # Ask if user wants to continue
                response = input(f"   Continue with remaining steps? (y/n): ")
                if response.lower() != 'y':
                    break
                    
        except subprocess.TimeoutExpired:
            print(f"⏱️  {step_name} timed out (30 minutes)")
            break
        except FileNotFoundError:
            print(f"❌ Script not found: {command}")
            print(f"   Skipping {step_name}")
        except Exception as e:
            print(f"❌ Error in {step_name}: {e}")
    
    # Final summary
    print(f"\n🎉 PIPELINE EXECUTION COMPLETE")
    print(f"📊 Completed steps: {success_count}/{len(steps)}")
    
    # Count output files
    topic_files = list(Path("./output/topic_yaml/").glob("*.yml"))
    bot_files = list(Path("./output/bot_yaml/").glob("*.yml"))
    
    print(f"📁 Output files:")
    print(f"   - Topic YAML files: {len(topic_files)}")
    print(f"   - Bot YAML files: {len(bot_files)}")
    
    return success_count == len(steps)

def run_quick_processing():
    """Run just the core processing steps with fixed function"""
    
    print("⚡ QUICK PROCESSING WITH FIXED FUNCTION")
    print("=" * 50)
    
    # Check requirements
    required_dirs = [
        "./input/dialogs_file/",
        "./input/custom_file/", 
        "./input/dvxml_file/"
    ]
    
    missing_dirs = [d for d in required_dirs if not os.path.exists(d)]
    if missing_dirs:
        print("❌ Missing required directories:")
        for d in missing_dirs:
            print(f"   - {d}")
        print("\nRun previous steps first or create these directories")
        return False
    
    try:
        print("🔄 Running Step 4 with FIXED indentation function...")
        result = subprocess.run(
            ["python", "step4_callAgentDecision_FIXED.py"],
            capture_output=True,
            text=True,
            timeout=1800
        )
        
        if result.returncode == 0:
            print("✅ Processing completed successfully!")
            
            # Count results
            yaml_files = list(Path("./output/topic_yaml/").glob("*.yml"))
            print(f"📊 Generated {len(yaml_files)} YAML files")
            
            # Show some file examples
            if yaml_files:
                print("\n📋 Sample output files:")
                for file in yaml_files[:5]:  # Show first 5
                    size = file.stat().st_size
                    print(f"   - {file.name} ({size:,} bytes)")
                if len(yaml_files) > 5:
                    print(f"   ... and {len(yaml_files) - 5} more files")
            
            return True
        else:
            print("❌ Processing failed:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main function with user choice"""
    
    print("🎯 HOW TO RUN THE SCRIPTS AGAIN")
    print("=" * 50)
    
    options = [
        "1. Quick Processing (Step 4 only with FIXED function)",
        "2. Complete Pipeline (All steps from scratch)", 
        "3. Investigate Empty Files",
        "4. Show Current Status",
        "5. Exit"
    ]
    
    for option in options:
        print(option)
    
    while True:
        try:
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == "1":
                run_quick_processing()
                break
            elif choice == "2":
                run_complete_pipeline()
                break
            elif choice == "3":
                subprocess.run(["python", "investigate_empty_files.py"])
                break
            elif choice == "4":
                # Show current status
                yaml_files = list(Path("./output/topic_yaml/").glob("*.yml"))
                unformatted_files = list(Path("./output/unformatted_topic_yaml/").glob("*.yml"))
                
                print(f"\n📊 CURRENT STATUS:")
                print(f"   - Formatted YAML files: {len(yaml_files)}")
                print(f"   - Unformatted YAML files: {len(unformatted_files)}")
                
                if unformatted_files:
                    empty_count = sum(1 for f in unformatted_files if f.stat().st_size == 0)
                    print(f"   - Empty files: {empty_count}")
                    print(f"   - Files with content: {len(unformatted_files) - empty_count}")
                
                print(f"\n🔧 Available scripts:")
                print(f"   - step4_callAgentDecision_FIXED.py (with fixed indentation)")
                print(f"   - fix_yaml_formatting.py (batch fix existing files)")
                print(f"   - investigate_empty_files.py (analyze failures)")
                
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1-5.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
