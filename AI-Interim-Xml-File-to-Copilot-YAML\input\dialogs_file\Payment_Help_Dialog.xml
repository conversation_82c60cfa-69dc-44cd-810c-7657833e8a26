<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Payment_Help_Dialog">
    <decision-state id="PH1001_CheckContext_DS">
      <if cond="(accountStatus == 'active')">
        <action next="PH1006_MetricsActiveAccount_DS2_CS"/>
        <elseif cond="(GlobalVars.extensionAllowed == true)">
          <if cond="GlobalVars.GetAccountDetails.eligibleForBillCycleReset == true">
            <action next="PH1002_MetricsExtAndBCR_JDA"/>
            <else>
              <action next="PH1003_MetricsExtOnly_JDA"/>
            </else>
          </if>
        </elseif>
        <else>
          <if cond="GlobalVars.GetAccountDetails.eligibleForBillCycleReset == true">
            <action next="PH1004_MetricsBCROnly_JDA"/>
            <else>
              <action next="PH1005_MetricsNoExtOrBCR_JDA"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <decision-state id="PH1002_MetricsExtAndBCR_DS">
      <action next="PH1009_PaymentHelpTransition_PP"/>
    </decision-state>

    <decision-state id="PH1003_MetricsExtOnly_DS">
      <action next="PH1009_PaymentHelpTransition_PP"/>
    </decision-state>

    <decision-state id="PH1004_MetricsBCROnly_DS">
      <action next="PH1009_PaymentHelpTransition_PP"/>
    </decision-state>

    <decision-state id="PH1005_MetricsNoExtOrBCR_DS">
      <action next="PH1009_PaymentHelpTransition_PP"/>
    </decision-state>

    <custom-state id="PH1006_MetricsActiveAccount_DS2_CS">
      <action next="PH1009_PaymentHelpTransition_PP"/>
    </custom-state>

    <play-state id="PH1009_PaymentHelpTransition_PP">
      <if cond="GlobalVars.tag == 'vague-billing'">
        <audio>
          <prompt id="PH1009_out_02">
            <prompt-segments>
              <audiofile src="PH1009_out_02.wav" text="Here are some billing options "/>
            </prompt-segments>
          </prompt>
        </audio>
        <else>
          <audio>
            <prompt id="silence_200ms">
              <prompt-segments>
                <audiofile text="test" src="silence_200ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </else>
      </if>
      <action next="PH1010_PaymentHelpMenu_DM"/>
    </play-state>

    <dm-state id="PH1010_PaymentHelpMenu_DM" type="CUST">
      <session-mapping key="allowedResponses" value="" type="String"/>
      <session-mapping key="allowedResponsesDTMF" value="" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="GlobalVars.lastTag" expr="tag"/>
      <success>
        <action label="faq_payment_methods">
          <session-mapping key="GlobalVars.tag" expr="'faq-payment_methods'"/>
          <action next="PH1035_Faq_SD"/>
        </action>
        <action label="manage_cards">
          <session-mapping key="GlobalVars.manageCardTask" expr="'MainMC'"/>
          <action next="PH1045_ManageCards_SD"/>
        </action>
        <action label="make-payment">
          <audio>
            <prompt id="PH1010_out_01">
              <prompt-segments>
                <audiofile src="PH1010_out_01.wav" text="Alright"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="PH1025_MakePayment_SD"/>
        </action>
        <action label="request-extension">
          <action next="PH1101_CheckExtensionEligibility_JDA"/>
        </action>
        <action label="bcr">
          <audio>
            <prompt id="PH1005_out_02">
              <prompt-segments>
                <audiofile src="PH1005_out_02.wav" text="Okay"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'billcyclereset'"/>
          <session-mapping key="GlobalVars.tag" expr="'change-payment_date'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="PH1015_BillCycleReset_SD"/>
        </action>
        <action label="reduce">
          <action next="PH1205_CheckHaveAddOns_JDA"/>
        </action>
        <action label="report-problem_payment_pm">
          <audio>
            <prompt id="PH1005_out_03">
              <prompt-segments>
                <audiofile src="PH1005_out_03.wav" text="Sorry to hear that!"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="PH1405_PaymentIssuesTransition_PP"/>
        </action>
        <action label="inquire-billing">
          <action next="PH1030_BalanceBreakdown_SD"/>
        </action>
        <action label="something-else_payhelp">
          <action next="PH1020_TransferPaymentHelp_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.suspendedOperatorRequest" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PH1010_ini_01" cond="(tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_ini_01.wav" text="Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_ini_02" cond="(tag != 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_ini_02.wav" text="Payment help menu  You can say 'get an extension', 'change due date', 'reduce my payment', 'ways to pay', 'question about my charges', 'manage payment cards' Or say, 'its something else' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="PH1010_PaymentHelpMenu_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PH1010_ini_01" cond="(tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_ini_01.wav" text="Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_ini_02" cond="(tag != 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_ini_02.wav" text="Payment help menu  You can say 'get an extension', 'change due date', 'reduce my payment', 'ways to pay', 'question about my charges', 'manage payment cards' Or say, 'its something else' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PH1010_ini_01" cond="(tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_ini_01.wav" text="Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_ini_02" cond="(tag != 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_ini_02.wav" text="Payment help menu  You can say 'get an extension', 'change due date', 'reduce my payment', 'ways to pay', 'question about my charges', 'manage payment cards' Or say, 'its something else' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_nm2_01" cond="(tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_nm2_01.wav" text="Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_nm2_02" cond="(tag != 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_nm2_02.wav" text="To request an extension press 1, To change your due date' press 2, 'reduce  payment'  3, 'To hear about ways to pay' - 4, 'questions about a charge' - 5, 'manage payment cards' -6 , if it's something else' press 7 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_nm2_01" cond="(tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_nm2_01.wav" text="Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_nm2_02" cond="(tag != 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_nm2_02.wav" text="To request an extension press 1, To change your due date' press 2, 'reduce  payment'  3, 'To hear about ways to pay' - 4, 'questions about a charge' - 5, 'manage payment cards' -6 , if it's something else' press 7 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_ini_01" cond="(tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_ini_01.wav" text="Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_ini_02" cond="(tag != 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_ini_02.wav" text="Payment help menu  You can say 'get an extension', 'change due date', 'reduce my payment', 'ways to pay', 'question about my charges', 'manage payment cards' Or say, 'its something else' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_nm2_01" cond="(tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_nm2_01.wav" text="Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_nm2_02" cond="(tag != 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_nm2_02.wav" text="To request an extension press 1, To change your due date' press 2, 'reduce  payment'  3, 'To hear about ways to pay' - 4, 'questions about a charge' - 5, 'manage payment cards' -6 , if it's something else' press 7 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_nm2_01" cond="(tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_nm2_01.wav" text="Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_nm2_02" cond="(tag != 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_nm2_02.wav" text="To request an extension press 1, To change your due date' press 2, 'reduce  payment'  3, 'To hear about ways to pay' - 4, 'questions about a charge' - 5, 'manage payment cards' -6 , if it's something else' press 7 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PH1010_ini_01" cond="(tag == 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_ini_01.wav" text="Which would you like, say, 'Get an extension', 'hear balance breakdown', 'make a payment', 'reduce monthly payment' or 'change my due date'"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1010_ini_02" cond="(tag != 'vague-billing')">
                <prompt-segments>
                  <audiofile src="PH1010_ini_02.wav" text="Payment help menu  You can say 'get an extension', 'change due date', 'reduce my payment', 'ways to pay', 'question about my charges', 'manage payment cards' Or say, 'its something else' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
          <grammars filename="PH1010_PaymentHelpMenu_DM.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="PH1010_PaymentHelpMenu_DM_dtmf.jsp" count="1">
            <param name="allowedResponsesDTMF" value="allowedResponsesDTMFVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="PH1010_PaymentHelpMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="PH1025_MakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="PH1025_MakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PH1025_MakePayment_SD_return_CS">
      <if cond="GlobalVars.callType == 'extension'">
        <action next="PH1101_CheckExtensionEligibility_JDA"/>
        <else>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="PH1015_BillCycleReset_SD">
      <gotodialog next="BillCycleReset_Care_Dialog"/>
      <action next="PH1015_BillCycleReset_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PH1015_BillCycleReset_SD_return_CS">
      <session-mapping key="GlobalVars.tag" expr="GlobalVars.lastTag"/>
      <action next="PH1010_PaymentHelpMenu_DM"/>
    </custom-state>

    <subdialog-state id="PH1030_BalanceBreakdown_SD">
      <gotodialog next="BalanceBreakdown_Dialog"/>
      <action next="PH1030_BalanceBreakdown_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PH1030_BalanceBreakdown_SD_return_CS">
      <if cond="GlobalVars.callType == 'mainmenu'">
        <session-mapping key="GlobalVars.callType" expr="undefined"/>
        <action next="getReturnLink()"/>
        <else>
          <action next="PH1010_PaymentHelpMenu_DM"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="PH1035_Faq_SD">
      <gotodialog next="FAQ_Main_Dialog"/>
      <action next="PH1035_Faq_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PH1035_Faq_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="PH1040_PaymentTips_SD">
      <gotodialog next="PaymentTips_Main_Dialog"/>
      <action next="PH1040_PaymentTips_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PH1040_PaymentTips_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="PH1045_ManageCards_SD">
      <gotodialog next="ManageCards_Main_Dialog"/>
      <action next="PH1045_ManageCards_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PH1045_ManageCards_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="PH1020_TransferPaymentHelp_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="PH1020_TransferPaymentHelp_SD_return"/>
    </subdialog-state>
    <play-state id="PH1105_PlayExtensionEligible_PP">
      <audio>
        <prompt id="PH1105_out_01">
          <prompt-segments>
            <audiofile src="PH1105_out_01.wav" text="It looks like your account IS eligible for a payment extension today!"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.extensionSuccess" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.extensionEntryPoint" expr="'payment_help'"/>
      <action next="PH1110_ApplyExtension_SD"/>
    </play-state>

    <subdialog-state id="PH1110_ApplyExtension_SD">
      <gotodialog next="ApplyExtension_Main_Dialog"/>
      <action next="PH1110_ApplyExtension_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PH1110_ApplyExtension_SD_return_CS">
      <action next="PH1010_PaymentHelpMenu_DM"/>
    </custom-state>

    <decision-state id="PH1205_CheckHaveAddOns_DS">
      <if cond="GlobalVars.GetAccountDetails.features.length == 0">
        <action next="PH1215_AskChangePlanYN_DM"/>
        <else>
          <action next="PH1210_OfferPlanOrFeatures_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="PH1210_OfferPlanOrFeatures_DM" type="CUST">
      <session-mapping key="isOnFamilyPlan" value="GlobalVars.GetAccountDetails.isOnFamilyPlan" type="String"/>
      <success>
        <action label="change-plan">
          <audio>
            <prompt id="PH1210_out_01">
              <prompt-segments>
                <audiofile src="PH1210_out_01.wav" text="Okay"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.tag" expr="'change-plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="PH1220_CheckNeedPIN_JDA"/>
        </action>
        <action label="cancel-line">
          <session-mapping key="GlobalVars.callType" expr="'cancel_line'"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="PH1020_TransferPaymentHelp_SD"/>
        </action>
        <action label="remove-feature">
          <audio>
            <prompt id="PH1210_out_02">
              <prompt-segments>
                <audiofile src="PH1210_out_02.wav" text="Sure"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'remove_services'"/>
          <session-mapping key="GlobalVars.tag" expr="'remove-feature'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="PH1020_TransferPaymentHelp_SD"/>
        </action>
        <action label="something-else_ph">
          <audio>
            <prompt id="PH1210_out_03">
              <prompt-segments>
                <audiofile src="PH1210_out_03.wav" text="Okay"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="PH1020_TransferPaymentHelp_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.suspendedOperatorRequest" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="isOnFamilyPlan == true">
                <prompt id="PH1210_ini_02">
                  <prompt-segments>
                    <audiofile src="PH1210_ini_02.wav" text="Which would you like to do? Say 'change my rate plan', 'remove some services' or 'cancel a line' "/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="PH1210_ini_01">
                    <prompt-segments>
                      <audiofile src="PH1210_ini_01.wav" text="Which would you like to do? Say  change my plan  or  remove some services "/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="PH1210_OfferPlanOrFeatures_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="isOnFamilyPlan == true">
                <prompt id="PH1210_ini_02">
                  <prompt-segments>
                    <audiofile src="PH1210_ini_02.wav" text="Which would you like to do? Say 'change my rate plan', 'remove some services' or 'cancel a line' "/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="PH1210_ini_01">
                    <prompt-segments>
                      <audiofile src="PH1210_ini_01.wav" text="Which would you like to do? Say  change my plan  or  remove some services "/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="isOnFamilyPlan == true">
                <prompt id="PH1210_ini_02">
                  <prompt-segments>
                    <audiofile src="PH1210_ini_02.wav" text="Which would you like to do? Say 'change my rate plan', 'remove some services' or 'cancel a line' "/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="PH1210_ini_01">
                    <prompt-segments>
                      <audiofile src="PH1210_ini_01.wav" text="Which would you like to do? Say  change my plan  or  remove some services "/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isOnFamilyPlan == true">
                <prompt id="PH1210_nm2_02">
                  <prompt-segments>
                    <audiofile src="PH1210_nm2_02.wav" text="Please say 'change my rate plan' or press 1, or 'remove services' or press 2  Cancel a line or press 3"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="PH1210_nm2_01">
                    <prompt-segments>
                      <audiofile src="PH1210_nm2_01.wav" text="Please say  change my plan  or press 1, or  remove services  or press 2"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isOnFamilyPlan == true">
                <prompt id="PH1210_nm2_02">
                  <prompt-segments>
                    <audiofile src="PH1210_nm2_02.wav" text="Please say 'change my rate plan' or press 1, or 'remove services' or press 2  Cancel a line or press 3"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="PH1210_nm2_01">
                    <prompt-segments>
                      <audiofile src="PH1210_nm2_01.wav" text="Please say  change my plan  or press 1, or  remove services  or press 2"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isOnFamilyPlan == true">
                <prompt id="PH1210_ini_02">
                  <prompt-segments>
                    <audiofile src="PH1210_ini_02.wav" text="Which would you like to do? Say 'change my rate plan', 'remove some services' or 'cancel a line' "/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="PH1210_ini_01">
                    <prompt-segments>
                      <audiofile src="PH1210_ini_01.wav" text="Which would you like to do? Say  change my plan  or  remove some services "/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isOnFamilyPlan == true">
                <prompt id="PH1210_nm2_02">
                  <prompt-segments>
                    <audiofile src="PH1210_nm2_02.wav" text="Please say 'change my rate plan' or press 1, or 'remove services' or press 2  Cancel a line or press 3"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="PH1210_nm2_01">
                    <prompt-segments>
                      <audiofile src="PH1210_nm2_01.wav" text="Please say  change my plan  or press 1, or  remove services  or press 2"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isOnFamilyPlan == true">
                <prompt id="PH1210_nm2_02">
                  <prompt-segments>
                    <audiofile src="PH1210_nm2_02.wav" text="Please say 'change my rate plan' or press 1, or 'remove services' or press 2  Cancel a line or press 3"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="PH1210_nm2_01">
                    <prompt-segments>
                      <audiofile src="PH1210_nm2_01.wav" text="Please say  change my plan  or press 1, or  remove services  or press 2"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="isOnFamilyPlan == true">
                <prompt id="PH1210_ini_02">
                  <prompt-segments>
                    <audiofile src="PH1210_ini_02.wav" text="Which would you like to do? Say 'change my rate plan', 'remove some services' or 'cancel a line' "/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="PH1210_ini_01">
                    <prompt-segments>
                      <audiofile src="PH1210_ini_01.wav" text="Which would you like to do? Say  change my plan  or  remove some services "/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="PH1210_OfferPlanOrFeatures_DM.grxml" count="1"/>
          <dtmfgrammars filename="PH1210_OfferPlanOrFeatures_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="PH1210_OfferPlanOrFeatures_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="PH1215_AskChangePlanYN_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="PH1215_out_01">
              <prompt-segments>
                <audiofile src="PH1215_out_01.wav" text="Okay"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="PH1220_CheckNeedPIN_JDA"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="PH1215_out_02">
              <prompt-segments>
                <audiofile src="PH1215_out_02.wav" text="No problem"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="PH1010_PaymentHelpMenu_DM"/>
        </action>
        <action label="operator">
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.suspendedOperatorRequest" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PH1215_ini_01">
                <prompt-segments>
                  <audiofile src="PH1215_ini_01.wav" text="I don t see any add-on features on your account that we could remove to reduce your bill But we can change your rate plan! Would you like to do that?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PH1215_ini_01">
                <prompt-segments>
                  <audiofile src="PH1215_ini_01.wav" text="I don t see any add-on features on your account that we could remove to reduce your bill But we can change your rate plan! Would you like to do that?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PH1215_nm1_01">
                <prompt-segments>
                  <audiofile src="PH1215_nm1_01.wav" text="Would you like to change your monthly plan?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1215_nm2_01">
                <prompt-segments>
                  <audiofile src="PH1215_nm2_01.wav" text="To change your monthly plan now, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1215_nm2_01">
                <prompt-segments>
                  <audiofile src="PH1215_nm2_01.wav" text="To change your monthly plan now, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1215_nm1_01">
                <prompt-segments>
                  <audiofile src="PH1215_nm1_01.wav" text="Would you like to change your monthly plan?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1215_nm2_01">
                <prompt-segments>
                  <audiofile src="PH1215_nm2_01.wav" text="To change your monthly plan now, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1215_nm2_01">
                <prompt-segments>
                  <audiofile src="PH1215_nm2_01.wav" text="To change your monthly plan now, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PH1215_ini_01">
                <prompt-segments>
                  <audiofile src="PH1215_ini_01.wav" text="I don t see any add-on features on your account that we could remove to reduce your bill But we can change your rate plan! Would you like to do that?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="PH1215_AskChangePlanYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="PH1215_AskChangePlanYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="PH1220_CheckNeedPIN_DS">
      <if cond="GlobalVars.loggedIn == true">
        <action next="PH1230_ChangePlan_SD"/>
        <else>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <action next="PH1225_GetPIN_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="PH1225_GetPIN_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="PH1225_GetPIN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PH1225_GetPIN_SD_return_CS">
      <action next="PH1230_ChangePlan_SD"/>
    </custom-state>

    <subdialog-state id="PH1230_ChangePlan_SD">
      <gotodialog next="RatePlan_Main_Dialog"/>
      <action next="PH1230_ChangePlan_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PH1230_ChangePlan_SD_return_CS">
      <if cond="GlobalVars.callType == 'extension'">
        <action next="PH1110_ApplyExtension_SD"/>
        <else>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </custom-state>

    <decision-state id="PH1101_CheckExtensionEligibility_DS">
      <if cond="GlobalVars.extensionAllowed == true">
        <session-mapping key="GlobalVars.callType" expr="'extension'"/>
        <session-mapping key="GlobalVars.tag" expr="'request-extension'"/>
        <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        <action next="PH1105_PlayExtensionEligible_PP"/>
        <else>
          <if cond="accountStatus == 'active'">
            <action next="PH1106_PlayExtensionActive_PP"/>
            <else>
              <action next="PH1107_PlayExtensionIneligible_PP"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="PH1106_PlayExtensionActive_PP">
      <audio>
        <prompt id="PH1106_out_01">
          <prompt-segments>
            <audiofile src="PH1106_out_01.wav" text="It looks like your account is still active, so we can't apply an extension to it right now Our agents wouldn't be able to give you one either If you can't make your upcoming payment, call us back when your account gets suspended, and we'll check if you're eligible for an extension then "/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PH1106_out_02">
          <prompt-segments>
            <audiofile src="PH1106_out_02.wav" text="Here are your options again "/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PH1010_PaymentHelpMenu_DM"/>
    </play-state>

    <play-state id="PH1107_PlayExtensionIneligible_PP">
      <audio>
        <prompt id="PH1107_out_01">
          <prompt-segments>
            <audiofile src="PH1107_out_01.wav" text="It doesn't look like your account is eligible for an extension right now Accounts are only eligible for one extension per payment cycle, and only in the first few days of being suspended Our agents wouldn't be able to give you one either So "/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PH1010_PaymentHelpMenu_DM"/>
    </play-state>

    <play-state id="PH1405_PaymentIssuesTransition_PP">
      <audio>
        <prompt id="PH1405_out_01">
          <prompt-segments>
            <audiofile src="PH1405_out_01.wav" text="Here s the usual reasons for that"/>
          </prompt-segments>
        </prompt>
      </audio>
      <audio>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PH1410_PlayPaymentIssuesInfo_PP"/>
    </play-state>

    <play-state id="PH1410_PlayPaymentIssuesInfo_PP">
      <audio>
        <prompt id="PH1410_out_01">
          <prompt-segments>
            <audiofile src="PH1410_out_01.wav" text="First, we don t take Discover cards, and some international cards may not work"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PH1410_out_02">
          <prompt-segments>
            <audiofile src="PH1410_out_02.wav" text="It can also be an error from a wrong CVV or billing zip code or your bank card could be expired"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PH1410_out_03">
          <prompt-segments>
            <audiofile src="PH1410_out_03.wav" text="Also, your bank will decline a payment if the card balance is too low So make sure to check with them first"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PH1410_out_04">
          <prompt-segments>
            <audiofile src="PH1410_out_04.wav" text="And finally, if you ve tried too many times today, you might need to wait 24 hours before trying again"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PH1415_RepeatPmtDeclYN_DM"/>
    </play-state>

    <dm-state id="PH1415_RepeatPmtDeclYN_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="playedOperator" value="false" type="Boolean"/>
          <action next="PH1410_PlayPaymentIssuesInfo_PP"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="PH1415_out_02">
              <prompt-segments>
                <audiofile src="PH1415_out_02.wav" text="Glad I could help!"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator">
          <session-mapping key="operatorReqCount" expr="operatorReqCount+1"/>
          <if cond="(operatorReqCount &gt; maxRequests)">
            <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
            <submit expr="operatorSubmitFunction('Payment_help.dvxml','PH1415_RepeatPmtDeclYN_DM',PH1415_RepeatPmtDeclYN_DM.confidencescore)" namelist="language library version "/>
            <else>
              <audio>
                <prompt id="PH1415_Operator_01">
                  <prompt-segments>
                    <audiofile src="PH1415_Operator_01.wav" text="I m sorry, I can t transfer you right now For more details about your payment, please check with your bank first Would you like to hear the tips again?"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="playedOperator" value="true" type="Boolean"/>
              <action next="PH1415_RepeatPmtDeclYN_DM"/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PH1415_ini_01" cond="playedOperator == false">
                <prompt-segments>
                  <audiofile src="PH1415_ini_01.wav" text="Would you like to hear that again? If you re done, you can simply hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PH1415_ini_01" cond="playedOperator == false">
                <prompt-segments>
                  <audiofile src="PH1415_ini_01.wav" text="Would you like to hear that again? If you re done, you can simply hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PH1415_nm1_01">
                <prompt-segments>
                  <audiofile src="PH1415_nm1_01.wav" text="Would you like to hear our tips again?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1415_nm2_01">
                <prompt-segments>
                  <audiofile src="PH1415_nm2_01.wav" text="To hear the tips again, say  yes  or press 1 If you re done, say  no  or press 2, or you can simply hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1415_nm2_01">
                <prompt-segments>
                  <audiofile src="PH1415_nm2_01.wav" text="To hear the tips again, say  yes  or press 1 If you re done, say  no  or press 2, or you can simply hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1415_nm1_01">
                <prompt-segments>
                  <audiofile src="PH1415_nm1_01.wav" text="Would you like to hear our tips again?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1415_nm2_01">
                <prompt-segments>
                  <audiofile src="PH1415_nm2_01.wav" text="To hear the tips again, say  yes  or press 1 If you re done, say  no  or press 2, or you can simply hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PH1415_nm2_01">
                <prompt-segments>
                  <audiofile src="PH1415_nm2_01.wav" text="To hear the tips again, say  yes  or press 1 If you re done, say  no  or press 2, or you can simply hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PH1415_ini_01" cond="playedOperator == false">
                <prompt-segments>
                  <audiofile src="PH1415_ini_01.wav" text="Would you like to hear that again? If you re done, you can simply hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="PH1415_RepeatPmtDeclYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="PH1415_RepeatPmtDeclYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  