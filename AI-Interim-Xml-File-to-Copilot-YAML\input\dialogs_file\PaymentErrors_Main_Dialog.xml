<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="PaymentErrors_Main_Dialog">
    <decision-state id="PE1001_CheckContext_DS">
      <if cond="(GlobalVars.paymentErrorCount == 1)">
        <session-mapping key="GlobalVars.paymentErrorCount" expr="2"/>
        <action next="PE1010_CheckErrorForTransfer_JDA"/>
        <else>
          <session-mapping key="GlobalVars.paymentErrorCount" expr="1"/>
          <if cond="GlobalVars.authorizationFailureHandling == 'waitPrompting' || GlobalVars.authorizationFailureHandling == 'genericXfer' || GlobalVars.authorizationFailureHandling == 'systemError' || GlobalVars.authorizationFailureHandling == 'sameAmountDeclined'">
            <action next="PE1305_CheckErrorType_JDA"/>
            <elseif cond=" GlobalVars.authorizationFailureHandling == 'maxPaymentExceeded'">
              <action next="PE1205_CheckMaxAttempts24h_JDA"/>
            </elseif>
            <else>
              <action next="PE1105_CheckAccountStatus_JDA"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <decision-state id="PE1010_CheckErrorForTransfer_DS">
      <if cond="GlobalVars.authorizationFailureHandling == 'insufficientFunds'">
        <action next="PE1005_TransferPrompt_PP"/>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'expirationDate'">
          <action next="PE1005_TransferPrompt_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'reconfirmDetails'">
          <action next="PE1005_TransferPrompt_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'standardDeclined'">
          <action next="PE1005_TransferPrompt_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'invalidZipCode'">
          <action next="PE1005_TransferPrompt_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'systemError'">
          <action next="PE1005_TransferPrompt_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'sameAmountDeclined'">
          <action next="PE1005_TransferPrompt_PP"/>
        </elseif>
        <else>
          <action next="PE1005_TransferPrompt_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="PE1005_TransferPrompt_PP">
      <session-mapping key="paymentErrorCount" value="GlobalVars.paymentErrorCount" type="String"/>
      <audio>
        <prompt id="PE1005_out_01" cond="paymentErrorCount == 2">
          <prompt-segments>
            <audiofile text="I'm sorry, I'm still having trouble sending your payment" src="PE1005_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PE1005_out_02" cond="paymentErrorCount != 2">
          <prompt-segments>
            <audiofile text="I'm sorry, I couldn't send your payment" src="PE1005_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.paymentFailure" value="true" type="Boolean"/>
      <action next="getReturnLink()"/>
    </play-state>

    <decision-state id="PE1105_CheckAccountStatus_DS">
      <if cond="((GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'active') || GlobalVars.guestPayment == true || GlobalVars.paymentsEntryPoint == '228Suspended')">
        <action next="PE1135_MetricsExtenionNotAllowed_JDA"/>
        <else>
          <action next="PE1110_CheckNeedUnhotlineEligibility_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="PE1110_CheckNeedUnhotlineEligibility_DS">
      <if cond="(GlobalVars.attemptedCheckUnhotlineEligibility == true)">
        <action next="PE1125_CheckIsExtensionAllowed_JDA"/>
        <else>
          <action next="PE1115_CheckUnhotlineBCSSetting_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="PE1115_CheckUnhotlineBCSSetting_DS">
      <if cond="(isEligibleForUnhotlineCheck == true || isEligibleForUnhotlineCheck == 'true')">
        <action next="PE1120_CheckUnhotlineEligibility_DB_DA"/>
        <else>
          <action next="PE1135_MetricsExtenionNotAllowed_JDA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="PE1120_CheckUnhotlineEligibility_DB_DA">
      <session-mapping key="accountNumber" value="GlobalVars.GetAccountDetails.accountNumber" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="CheckUnhotlineEligibility" classname="com.nuance.metro.dataaccess.CheckUnhotlineEligibility">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="accountNumber" mask="true"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="isEligible"/>
          <output-variable name="numberOfCases"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.CheckUnhotlineEligibility" expr="CheckUnhotlineEligibility"/>
        <action next="PE1125_CheckIsExtensionAllowed_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="PE1125_CheckIsExtensionAllowed_DS">
      <session-mapping key="CheckUnhotlineEligibility" value="GlobalVars.CheckUnhotlineEligibility" type="String"/>
      <if cond="(CheckUnhotlineEligibility.isEligible == 'true' || CheckUnhotlineEligibility.isEligible == true)">
        <session-mapping key="GlobalVars.extensionAllowed" value="true" type="Boolean"/>
        <action next="PE1130_MetricsExtensionAllowed_JDA"/>
        <else>
          <action next="PE1135_MetricsExtenionNotAllowed_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="PE1130_MetricsExtensionAllowed_DS">
      <action next="PE1205_CheckMaxAttempts24h_JDA"/>
    </decision-state>

    <decision-state id="PE1135_MetricsExtenionNotAllowed_DS">
      <action next="PE1205_CheckMaxAttempts24h_JDA"/>
    </decision-state>

    <decision-state id="PE1205_CheckMaxAttempts24h_DS">
      <if cond="GlobalVars.authorizationFailureHandling == 'maxAttemptExceeded'">
        <action next="PE1210_InformMaxAttempts24h_PP"/>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'maxPaymentExceeded'">
          <action next="PE1210_InformMaxAttempts24h_PP"/>
        </elseif>
        <else>
          <action next="PE1305_CheckErrorType_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="PE1210_InformMaxAttempts24h_PP">
      <session-mapping key="authorizationFailureHandling" value="GlobalVars.authorizationFailureHandling" type="String"/>
      <session-mapping key="guestPayment" value="GlobalVars.guestPayment" type="String"/>
      <audio>
        <prompt id="PE1210_out_01" cond="authorizationFailureHandling == 'maxAttemptExceeded'">
          <prompt-segments>
            <audiofile text="I'm sorry, it looks like there have been too many payment attempts on your account in the last 24 hours, so for security reasons I couldn't send your payment Our agents wouldn't be able to either So you'll need to wait until tomorrow before you can try again with a card" src="PE1210_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms" cond="authorizationFailureHandling == 'maxAttemptExceeded'">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PE1210_out_02" cond="authorizationFailureHandling == 'maxPaymentExceeded' &amp;&amp; guestPayment == true">
          <prompt-segments>
            <audiofile text="I'm sorry, I couldn't send this payment" src="PE1210_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PE1210_out_03" cond="authorizationFailureHandling == 'maxPaymentExceeded' &amp;&amp; guestPayment != true">
          <prompt-segments>
            <audiofile text="I'm sorry, it looks like you've already made the maximum number of payments allowed for a single day, so I couldn't send this one " src="PE1210_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms" cond="authorizationFailureHandling == 'maxPaymentExceeded'">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="authorizationFailureHandling == 'maxAttemptExceeded'">
        <action next="PE1215_CheckEligibleForExtension_JDA"/>
        <else>
          <session-mapping key="GlobalVars.paymentFailure" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </play-state>

    <decision-state id="PE1215_CheckEligibleForExtension_DS">
      <if cond="GlobalVars.extensionAllowed == true">
        <action next="PE1220_OfferStoreOrExtension_DM"/>
        <else>
          <action next="PE1225_OfferStore_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="PE1220_OfferStoreOrExtension_DM" type="CUST">
      <success>
        <action label="inquire-store_location">
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'payment'"/>
          <action next="PE1230_FindStore_SD"/>
        </action>
        <action label="request-extension_pe">
          <session-mapping key="GlobalVars.callType" expr="'extension'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.extensionEntryPoint" expr="'failed_payment'"/>
          <audio>
            <prompt id="PE1220_out_01">
              <prompt-segments>
                <audiofile text="Okay, let me do that for you" src="PE1220_out_01.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PE1220_ini_01">
                <prompt-segments>
                  <audiofile text="You can pay cash in a store, or we can give you a payment extension so you keep your service until tomorrow Say 'find a store' or 'get an extension'" src="PE1220_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PE1220_ini_01">
                <prompt-segments>
                  <audiofile text="You can pay cash in a store, or we can give you a payment extension so you keep your service until tomorrow Say 'find a store' or 'get an extension'" src="PE1220_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PE1220_ini_01">
                <prompt-segments>
                  <audiofile text="You can pay cash in a store, or we can give you a payment extension so you keep your service until tomorrow Say 'find a store' or 'get an extension'" src="PE1220_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="PE1220_ni2_01">
                <prompt-segments>
                  <audiofile text="Please say 'find a store' or press 1, or say 'get an extension' or press 2" src="PE1220_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="PE1220_ni3_01">
                <prompt-segments>
                  <audiofile text="Please say 'find a store' or press 1, or say 'get an extension' or press 2" src="PE1220_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1220_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say 'find a store' or 'get an extension'" src="PE1220_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1220_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'find a store' or press 1, or say 'get an extension' or press 2" src="PE1220_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1220_nm3_01">
                <prompt-segments>
                  <audiofile text="Please say 'find a store' or press 1, or say 'get an extension' or press 2" src="PE1220_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PE1220_ini_01">
                <prompt-segments>
                  <audiofile text="You can pay cash in a store, or we can give you a payment extension so you keep your service until tomorrow Say 'find a store' or 'get an extension'" src="PE1220_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="PE1220_OfferStoreOrExtension_DM.grxml" count="1"/>
          <dtmfgrammars filename="PE1220_OfferStoreOrExtension_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="PE1225_OfferStore_DM" type="CUST">
      <success>
        <action label="inquire-store_location">
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'payment'"/>
          <action next="PE1230_FindStore_SD"/>
        </action>
        <action label="request-extension_pe">
          <audio>
            <prompt id="PE1225_out_01">
              <prompt-segments>
                <audiofile text="Sorry, your account's NOT eligible for an extension right now" src="PE1225_out_01.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="PE1225_OfferStore_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PE1225_ini_01">
                <prompt-segments>
                  <audiofile text="To see where you can pay in cash near you, say 'find a store' Otherwise, you can hang up now" src="PE1225_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PE1225_ini_01">
                <prompt-segments>
                  <audiofile text="To see where you can pay in cash near you, say 'find a store' Otherwise, you can hang up now" src="PE1225_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PE1225_ini_01">
                <prompt-segments>
                  <audiofile text="To see where you can pay in cash near you, say 'find a store' Otherwise, you can hang up now" src="PE1225_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="PE1225_ni2_01">
                <prompt-segments>
                  <audiofile text="Please say 'find a store' or press 1 If you're done, you can simply hang up" src="PE1225_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="PE1225_ni3_01">
                <prompt-segments>
                  <audiofile text="Please say 'find a store' or press 1 If you're done, you can simply hang up" src="PE1225_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1225_ini_01">
                <prompt-segments>
                  <audiofile text="To see where you can pay in cash near you, say 'find a store' Otherwise, you can hang up now" src="PE1225_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1225_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'find a store' or press 1 If you're done, you can simply hang up" src="PE1225_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1225_nm3_01">
                <prompt-segments>
                  <audiofile text="Please say 'find a store' or press 1 If you're done, you can simply hang up" src="PE1225_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PE1225_ini_01">
                <prompt-segments>
                  <audiofile text="To see where you can pay in cash near you, say 'find a store' Otherwise, you can hang up now" src="PE1225_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="PE1225_OfferStore_DM.grxml" count="1"/>
          <dtmfgrammars filename="PE1225_OfferStore_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="PE1230_FindStore_SD">
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="PE1230_FindStore_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PE1230_FindStore_SD_return_CS">
      <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="PE1305_CheckErrorType_DS">
      <if cond="GlobalVars.authorizationFailureHandling == 'insufficientFunds'">
        <action next="PE1310_PlayInsufficientFunds_PP"/>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'expirationDate'">
          <action next="PE1315_PlayCardExpired_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'waitPrompting'">
          <action next="PE1330_PlayWaitCheck_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'reconfirmDetails'">
          <action next="PE1325_PlayCardInfoIssues_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'standardDeclined'">
          <action next="PE1320_PlayGenericError_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'invalidZipCode'">
          <action next="PE1331_PlayWrongZip_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'prepaidVoucher'">
          <action next="PE1309_PlayPrepaidError_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'systemError'">
          <action next="PE1332_PlaySystemError_PP"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'sameAmountDeclined'">
          <action next="PE1333_PlaySameAmount_PP"/>
        </elseif>
        <else>
          <action next="PE1005_TransferPrompt_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="PE1309_PlayPrepaidError_PP">
      <audio>
        <prompt id="PE1309_out_01">
          <prompt-segments>
            <audiofile text="I'm sorry, I'm having trouble adding this payment to your account " src="PE1309_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.paymentFailure" value="true" type="Boolean"/>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="PE1310_PlayInsufficientFunds_PP">
      <audio>
        <prompt id="PE1310_out_01">
          <prompt-segments>
            <audiofile text="I'm sorry, your card was declined for insufficient funds If you're not sure why, please call your bank right away to check" src="PE1310_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PE1335_PaymentErrorWrapMenu_DM"/>
    </play-state>

    <play-state id="PE1315_PlayCardExpired_PP">
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet ? GlobalVars.payingWithEWallet : false" type="String"/>
      <audio>
        <prompt id="PE1315_out_01">
          <prompt-segments>
            <audiofile text="I'm sorry, looks like your card is expired, so it was declined" src="PE1315_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PE1315_out_02" cond="payingWithEWallet == true">
          <prompt-segments>
            <audiofile text="You can update your card details in the myMetro app, or online at metrobyt-mobilecom" src="PE1315_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PE1335_PaymentErrorWrapMenu_DM"/>
    </play-state>

    <play-state id="PE1320_PlayGenericError_PP">
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet ? GlobalVars.payingWithEWallet : false" type="String"/>
      <audio>
        <prompt id="PE1320_out_01">
          <prompt-segments>
            <audiofile text="I'm sorry, your payment was declined To find out more, please call your bank to see if there's anything wrong with your account" src="PE1320_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="payingWithEWallet == true">
          <prompt id="silence_250ms">
            <prompt-segments>
              <audiofile text="test" src="silence_250ms.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="PE1320_out_02">
            <prompt-segments>
              <audiofile text="You can also check the card information you've saved in myMetro to make sure it's right" src="PE1320_out_02.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PE1335_PaymentErrorWrapMenu_DM"/>
    </play-state>

    <play-state id="PE1325_PlayCardInfoIssues_PP">
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet ? GlobalVars.payingWithEWallet : false" type="String"/>
      <audio>
        <prompt id="PE1325_out_01">
          <prompt-segments>
            <audiofile text="I'm sorry, your card was declined It seems to be an issue with the information you entered So either the card number, expiration date, verification code or billing zip code is wrong" src="PE1325_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="payingWithEWallet == true">
          <prompt id="silence_250ms">
            <prompt-segments>
              <audiofile text="test" src="silence_250ms.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="PE1325_out_03">
            <prompt-segments>
              <audiofile text="You can update your payment information in the myMetro app, or online at metrobyt-mobilecom" src="PE1325_out_03.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PE1335_PaymentErrorWrapMenu_DM"/>
    </play-state>

    <play-state id="PE1330_PlayWaitCheck_PP">
      <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <session-mapping key="GlobalVars.playFailureGoodbye" value="true" type="Boolean"/>
      <audio>
        <prompt id="PE1330_out_02">
          <prompt-segments>
            <audiofile text="I'm sorry, but I can't tell if your payment went through yet In a few hours, you can check your account by calling back, checking myMetro, or logging in at metrobyt-mobilecom Make sure to wait a few hours in case it goes through, so you don't make your payment twice" src="PE1330_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PE1330_out_03">
          <prompt-segments>
            <audiofile text="We apologize for the inconvenience" src="PE1330_out_03.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="PE1331_PlayWrongZip_PP">
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet ? GlobalVars.payingWithEWallet : false" type="String"/>
      <audio>
        <prompt id="PE1331_out_01">
          <prompt-segments>
            <audiofile text="I'm sorry, that zip code didn't match " src="PE1331_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="payingWithEWallet == true">
          <prompt id="PE1331_out_02">
            <prompt-segments>
              <audiofile text="You can update your card details in the myMetro app, or online at metrobyt-mobilecom " src="PE1331_out_02.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="PE1331_out_03">
          <prompt-segments>
            <audiofile text="Make sure you use the zip code on file with your *bank* It might not be the same as the zip code for your Metro account " src="PE1331_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="PE1335_PaymentErrorWrapMenu_DM"/>
    </play-state>

    <dm-state id="PE1335_PaymentErrorWrapMenu_DM" type="CUST">
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet ? GlobalVars.payingWithEWallet : false" type="String"/>
      <session-mapping key="authorizationFailureHandling" value="GlobalVars.authorizationFailureHandling" type="String"/>
      <session-mapping key="extensionAllowed" value="GlobalVars.extensionAllowed == undefined ? false : GlobalVars.extensionAllowed" type="String"/>
      <session-mapping key="collection_dtmfgrammar1" value="PE1335_PaymentErrorWrapMenu_DM_dtmf.jsp" type="String"/>
      <session-mapping key="collection_grammar1" value="PE1335_PaymentErrorWrapMenu_DM.jsp" type="String"/>
      <success>
        <action label="request-extension_pe">
          <if cond="extensionAllowed == true">
            <session-mapping key="GlobalVars.callType" expr="'extension'"/>
            <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
            <session-mapping key="GlobalVars.extensionEntryPoint" expr="'failed_payment'"/>
            <audio>
              <prompt id="PE1335_out_01">
                <prompt-segments>
                  <audiofile text="Okay, let me do that for you" src="PE1335_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="getReturnLink()"/>
            <else>
              <audio>
                <prompt id="PE1335_out_02">
                  <prompt-segments>
                    <audiofile text="Sorry, your account's NOT eligible for an extension right now" src="PE1335_out_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="PE1335_PaymentErrorWrapMenu_DM"/>
            </else>
          </if>
        </action>
        <action label="start_over">
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="'fail_authorization'"/>
          <session-mapping key="GlobalVars.bankCardNumber" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardZip" expr="undefined"/>
          <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardDate" expr="undefined"/>
          <session-mapping key="GlobalVars.offerPrepaid" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.payingWithPrepaid" value="false" type="Boolean"/>
          <audio>
            <prompt id="PE1335_out_03">
              <prompt-segments>
                <audiofile text="Sure!" src="PE1335_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="inquire-store_location">
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'payment'"/>
          <action next="PE1340_StoreLocator_SD"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="'reenter_zip'"/>
          <session-mapping key="GlobalVars.bankCardZip" expr="PE1335_PaymentErrorWrapMenu_DM.returnvalue"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PE1335_ini_05" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="Say start over, get an extension or find a store Or, try entering a different zip code now  " src="PE1335_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_06" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="Say start over, or find a store Or, try entering a different zip code now " src="PE1335_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_01" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; authorizationFailureHandling == 'reconfirmDetails'">
                <prompt-segments>
                  <audiofile text="To try again" src="PE1335_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_02" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; authorizationFailureHandling != 'reconfirmDetails'">
                <prompt-segments>
                  <audiofile text="To use a different card" src="PE1335_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_03" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="say 'start over' Or say 'get an extension', or 'find a store' if you'd like to pay in cash" src="PE1335_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_04" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; (extensionAllowed != true)">
                <prompt-segments>
                  <audiofile text="say 'start over' or say 'find a store' if you'd like to pay in cash" src="PE1335_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="PE1335_ini_05" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="Say start over, get an extension or find a store Or, try entering a different zip code now  " src="PE1335_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_06" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="Say start over, or find a store Or, try entering a different zip code now " src="PE1335_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_01" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; authorizationFailureHandling == 'reconfirmDetails'">
                <prompt-segments>
                  <audiofile text="To try again" src="PE1335_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_02" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; authorizationFailureHandling != 'reconfirmDetails'">
                <prompt-segments>
                  <audiofile text="To use a different card" src="PE1335_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_03" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="say 'start over' Or say 'get an extension', or 'find a store' if you'd like to pay in cash" src="PE1335_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_04" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; (extensionAllowed != true)">
                <prompt-segments>
                  <audiofile text="say 'start over' or say 'find a store' if you'd like to pay in cash" src="PE1335_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PE1335_ini_05" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="Say start over, get an extension or find a store Or, try entering a different zip code now  " src="PE1335_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_06" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="Say start over, or find a store Or, try entering a different zip code now " src="PE1335_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_01" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; authorizationFailureHandling == 'reconfirmDetails'">
                <prompt-segments>
                  <audiofile text="To try again" src="PE1335_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_02" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; authorizationFailureHandling != 'reconfirmDetails'">
                <prompt-segments>
                  <audiofile text="To use a different card" src="PE1335_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_03" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="say 'start over' Or say 'get an extension', or 'find a store' if you'd like to pay in cash" src="PE1335_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_04" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; (extensionAllowed != true)">
                <prompt-segments>
                  <audiofile text="say 'start over' or say 'find a store' if you'd like to pay in cash" src="PE1335_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="PE1335_nm2_03" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="To pay with a different card, say 'start over' or press 1 For a payment extension until you can pay, say 'get an extension' or press 2 To check where you can pay in cash near you, say 'find a store' or press 3 Or, say or enter a new zip code for this card now " src="PE1335_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm2_04" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="To pay with a different card, say 'start over' or press 1 To check where you can pay in cash near you, say 'find a store' or press 2 Or, say or enter a new zip code for this card now " src="PE1335_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm2_01" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="To pay with a different card, say 'start over' or press 1 For a payment extension until you can pay, say 'get an extension' or press 2 To check where you can pay in cash near you, say 'find a store' or press 3" src="PE1335_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm2_02" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="To pay with a different card, say 'start over' or press 1 Or to check where you can pay in cash near you, say 'find a store' or press 2" src="PE1335_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="PE1335_nm3_03" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="To pay now with a diffrent card, press 1 To get a payment extension, press 2 To seach for store locations near you, press 3 Or, enter a new zip code for this card now " src="PE1335_nm3_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm3_04" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="To pay now with a diffrent card, press 1 To seach for store locations near you, press 2 Or, enter a new zip code for this card now " src="PE1335_nm3_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm3_01" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="To pay now with a diffrent card, press 1 To get a payment extension, press 2 To seach for store locations near you, press 3" src="PE1335_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm3_02" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="To pay now with a diffrent card, press 1 To seach for store locations near you, press 2" src="PE1335_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_05" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="Say start over, get an extension or find a store Or, try entering a different zip code now  " src="PE1335_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_06" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="Say start over, or find a store Or, try entering a different zip code now " src="PE1335_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_01" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; authorizationFailureHandling == 'reconfirmDetails'">
                <prompt-segments>
                  <audiofile text="To try again" src="PE1335_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_02" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; authorizationFailureHandling != 'reconfirmDetails'">
                <prompt-segments>
                  <audiofile text="To use a different card" src="PE1335_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_03" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="say 'start over' Or say 'get an extension', or 'find a store' if you'd like to pay in cash" src="PE1335_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_04" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; (extensionAllowed != true)">
                <prompt-segments>
                  <audiofile text="say 'start over' or say 'find a store' if you'd like to pay in cash" src="PE1335_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm2_03" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="To pay with a different card, say 'start over' or press 1 For a payment extension until you can pay, say 'get an extension' or press 2 To check where you can pay in cash near you, say 'find a store' or press 3 Or, say or enter a new zip code for this card now " src="PE1335_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm2_04" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="To pay with a different card, say 'start over' or press 1 To check where you can pay in cash near you, say 'find a store' or press 2 Or, say or enter a new zip code for this card now " src="PE1335_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm2_01" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="To pay with a different card, say 'start over' or press 1 For a payment extension until you can pay, say 'get an extension' or press 2 To check where you can pay in cash near you, say 'find a store' or press 3" src="PE1335_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm2_02" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="To pay with a different card, say 'start over' or press 1 Or to check where you can pay in cash near you, say 'find a store' or press 2" src="PE1335_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm3_03" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="To pay now with a diffrent card, press 1 To get a payment extension, press 2 To seach for store locations near you, press 3 Or, enter a new zip code for this card now " src="PE1335_nm3_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm3_04" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="To pay now with a diffrent card, press 1 To seach for store locations near you, press 2 Or, enter a new zip code for this card now " src="PE1335_nm3_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm3_01" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="To pay now with a diffrent card, press 1 To get a payment extension, press 2 To seach for store locations near you, press 3" src="PE1335_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_nm3_02" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="To pay now with a diffrent card, press 1 To seach for store locations near you, press 2" src="PE1335_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PE1335_ini_05" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed == true">
                <prompt-segments>
                  <audiofile text="Say start over, get an extension or find a store Or, try entering a different zip code now  " src="PE1335_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_06" cond="authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false &amp;&amp; extensionAllowed != true">
                <prompt-segments>
                  <audiofile text="Say start over, or find a store Or, try entering a different zip code now " src="PE1335_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_01" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; authorizationFailureHandling == 'reconfirmDetails'">
                <prompt-segments>
                  <audiofile text="To try again" src="PE1335_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_02" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; authorizationFailureHandling != 'reconfirmDetails'">
                <prompt-segments>
                  <audiofile text="To use a different card" src="PE1335_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_03" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; (extensionAllowed == true)">
                <prompt-segments>
                  <audiofile text="say 'start over' Or say 'get an extension', or 'find a store' if you'd like to pay in cash" src="PE1335_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PE1335_ini_04" cond="(!(authorizationFailureHandling == 'invalidZipCode' &amp;&amp; payingWithEWallet == false)) &amp;&amp; (extensionAllowed != true)">
                <prompt-segments>
                  <audiofile text="say 'start over' or say 'find a store' if you'd like to pay in cash" src="PE1335_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="PE1335_PaymentErrorWrapMenu_DM.jsp" count="1">          
      </grammars>
          <dtmfgrammars filename="PE1335_PaymentErrorWrapMenu_DM_dtmf.jsp" count="1">
      </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="PE1340_StoreLocator_SD">
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="PE1340_StoreLocator_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="PE1340_StoreLocator_SD_return_CS">
      <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <action next="getReturnLink()"/>
    </custom-state>

    <play-state id="PE1332_PlaySystemError_PP">
      <audio>
        <prompt id="PE1332_out_01">
          <prompt-segments>
            <audiofile text="I'm sorry, this card is either expired or closed" src="PE1332_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <audio>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="PE1333_PlaySameAmount_PP">
      <session-mapping key="guestPayment" value="GlobalVars.guestPayment" type="String"/>
      <audio>
        <prompt id="PE1333_out_02" cond="guestPayment == true">
          <prompt-segments>
            <audiofile text="I'm sorry, I couldn't process a payment in this amount You can stay on the line and choose a different amount, or you can hang up and try again later " src="PE1333_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PE1333_out_01" cond="guestPayment != true">
          <prompt-segments>
            <audiofile text="Actually, your account shows a payment for the same amount recently To make sure it's not a duplicate, stay on the line and choose a different amount Or, you can hang up and make your payment in an hour from now " src="PE1333_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_3000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_3000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

  </dialog>
  