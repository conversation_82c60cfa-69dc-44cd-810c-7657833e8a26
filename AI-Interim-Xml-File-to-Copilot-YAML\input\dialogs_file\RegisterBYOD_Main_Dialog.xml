<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="RegisterBYOD_Main_Dialog">
    <decision-state id="BY1001_CheckContext_DS">
      <session-mapping key="deviceType" value="" type="String"/>
      <if cond="(GlobalVars.operatingSystem == '' ) ||  (GlobalVars.operatingSystem == 'null' || GlobalVars.operatingSystem == null)  || (GlobalVars.operatingSystem == undefined) || (GlobalVars.operatingSystem == 'OTHER')">
        <session-mapping key="GlobalVars.deviceType" expr="undefined"/>
        <action next="BY1005_PlayBYODTransition_PP"/>
        <else>
          <session-mapping key="GlobalVars.deviceType" expr="deviceType"/>
          <action next="BY1015_RegisterBYOD_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="BY1005_PlayBYODTransition_PP">
      <audio>
        <prompt id="BY1005_out_01">
          <prompt-segments>
            <audiofile text="Now, I see you brought your own phone!" src="BY1005_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BY1010_GetDeviceOS_DM"/>
    </play-state>

    <dm-state id="BY1010_GetDeviceOS_DM" type="CUST">
      <success>
        <action label="iphone">
          <session-mapping key="GlobalVars.deviceType" expr="'BYOD-IPHONE'"/>
          <action next="BY1015_RegisterBYOD_DB_DA"/>
        </action>
        <action label="android">
          <session-mapping key="GlobalVars.deviceType" expr="'BYOD-ANDROID'"/>
          <action next="BY1015_RegisterBYOD_DB_DA"/>
        </action>
        <action label="get-device_none">
          <audio>
            <prompt id="BY1010_out_02">
              <prompt-segments>
                <audiofile text="Okay" src="BY1010_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="dont_know">
          <audio>
            <prompt id="BY1010_out_01">
              <prompt-segments>
                <audiofile text="No problem!" src="BY1010_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BY1010_ini_01">
                <prompt-segments>
                  <audiofile text="So I can set up your account Is it an *iPhone*, an *Android* smartphone, something *else* Or say 'I don't know'" src="BY1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BY1010_ini_01">
                <prompt-segments>
                  <audiofile text="So I can set up your account Is it an *iPhone*, an *Android* smartphone, something *else* Or say 'I don't know'" src="BY1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BY1010_ni1_01">
                <prompt-segments>
                  <audiofile text="I need to know the platform of your new phone You can say 'iPhone' or press 1, 'Android' or press 2, 'something else' - 3, or 'I don't know' - 4" src="BY1010_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BY1010_nm1_01">
                <prompt-segments>
                  <audiofile text="I need to know the platform of your new phone You can say 'iPhone' or press 1, 'Android' or press 2, 'something else' - 3, or 'I don't know' - 4" src="BY1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BY1010_ini_01">
                <prompt-segments>
                  <audiofile text="So I can set up your account Is it an *iPhone*, an *Android* smartphone, something *else* Or say 'I don't know'" src="BY1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
          <notoconfirmprompts count="2"/>
          <notoconfirmprompts count="3"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="BY1010_GetDeviceOS_DM.grxml" count="1"/>
          <dtmfgrammars filename="BY1010_GetDeviceOS_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'iphone'">
                <prompt id="BY1010_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="An iPhone" src="BY1010_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'android'">
                <prompt id="BY1010_cnf_ini_03">
                  <prompt-segments>
                    <audiofile text="A smartphone running on Android" src="BY1010_cnf_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'other'">
                <prompt id="BY1010_cnf_ini_04">
                  <prompt-segments>
                    <audiofile text="It's not an iPhone NOR an Android" src="BY1010_cnf_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'dont_know'">
                <prompt id="BY1010_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You're not sure what kind of phone it is" src="BY1010_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="BY1015_RegisterBYOD_DB_DA">
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="deviceType" value="GlobalVars.deviceType" type="String"/>
      <session-mapping key="imeiList" value="GlobalVars.imeiList != undefined ? getListAsCommaSeparatedString(GlobalVars.imeiList) : ''" type="String"/>
      <data-access id="RegisterBYOD" classname="com.nuance.metro.dataaccess.AddYourOwnDevice">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="imei"/>
          <input-variable name="sessionId"/>
          <input-variable name="deviceType"/>
          <input-variable name="imeiList"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.RegisterBYOD" expr="RegisterBYOD"/>
        <if cond="GlobalVars.RegisterBYOD &amp;&amp; GlobalVars.RegisterBYOD.status &amp;&amp; GlobalVars.RegisterBYOD.status.toUpperCase() == 'SUCCESS'">
          <action next="BY1020_PlayBYODSuccess_PP"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <if cond="GlobalVars.isCareTransfer == 'true'">
              <gotodialog next="CallTransfer_Main_Dialog"/>
              <else>
                <gotodialog next="Transfer_Main"/>
              </else>
            </if>
          </else>
        </if>
      </action>
    </data-access-state>

    <play-state id="BY1020_PlayBYODSuccess_PP">
      <session-mapping key="deviceType" value="GlobalVars.deviceType" type="String"/>
      <session-mapping key="operatingSystem" value="GlobalVars.operatingSystem" type="String"/>
      <audio>
        <prompt id="BY1020_out_01" cond="((operatingSystem == '') || (operatingSystem == null || operatingSystem == 'null') || (operatingSystem == 'OTHER')) &amp;&amp; (deviceType == 'BYOD-ANDROID' || deviceType == 'BYOD-IPHONE')">
          <prompt-segments>
            <audiofile text="Got it! " src="BY1020_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="deviceType == 'BYOD-ANDROID' || deviceType == 'BYOD-IPHONE'">
        <session-mapping key="GlobalVars.BYODRegistered" value="true" type="Boolean"/>
      </if>
      <action next="getReturnLink()"/>
    </play-state>

  </dialog>
  