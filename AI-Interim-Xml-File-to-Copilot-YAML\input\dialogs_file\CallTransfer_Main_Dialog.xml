<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="CallTransfer_Main_Dialog">
    <decision-state id="XR0005_CheckEntryMethod_DS">
      <session-mapping key="GlobalVars.isSuspended" expr="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false"/>
      <if cond="TransferTag == 'New_Customer_Sales_En' || TransferTag == 'New_Customer_Sales_Es'">
        <action next="XR0025_PlayTransferPrompt_PP"/>
      </if>
      <if cond="GlobalVars.acctLocked == true">
        <action next="XR0023_GoToBroadcastMessages_SD"/>
      </if>
      <if cond="GlobalVars.tag == 'request-representative_sales'">
        <session-mapping key="TransferTag" expr="'sales'"/>
      </if>
      <if cond="GlobalVars.transferReason == 'dbfail'">
        <session-mapping key="GlobalVars.cti_TransferReason" expr="getctiTransferReason(GlobalVars.GetCTIParameters.ctiTransferReason, 'xferred_backend_failure')"/>
      </if>
      <if cond="GlobalVars.tag == 'purchase-phone' || GlobalVars.tag == 'purchase-accessory'">
        <if cond="language == 'en-US'">
          <session-mapping key="TransferTag" expr="'purchasing_support_english'"/>
          <else>
            <session-mapping key="TransferTag" expr="'purchasing_support_spanish'"/>
          </else>
        </if>
        <elseif cond="GlobalVars.tag == 'T-MobileMoney'">
          <session-mapping key="TransferTag" expr="'tmobile_money_all'"/>
        </elseif>
        <elseif cond="GlobalVars.tag == 'home-internet'">
          <session-mapping key="TransferTag" expr="'home_internet_all'"/>
        </elseif>
      </if>
      <if cond="GlobalVars.fromMyMetroCustomerSupport == true">
        <action next="XR0025_PlayTransferPrompt_PP"/>
        <elseif cond="GlobalVars.enteredFrom == 'RCC'">
          <action next="XR0030_GetTransferDestination_DB_DA"/>
        </elseif>
        <elseif cond="callerSaidOperator == true">
          <session-mapping key="callerSaidOperator" value="false" type="Boolean"/>
          <gotodialog next="CallTransfer_OperatorRequestHandling#XR1005_CheckConfirmationConfidence_DS"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'auto_pay'">
          <action next="XR0006_MetricsAutopayXfer_JDA"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'ThirdPartyFeaturesXfer' || GlobalVars.callType == 'transfer' || GlobalVars.troubleshootingTransfer == true || GlobalVars.dataCollectionTransfer == true">
          <gotodialog next="CallTransfer_Main#XR0023_GoToBroadcastMessages_SD"/>
        </elseif>
        <elseif cond="GlobalVars.saidSomethingElse == true">
          <gotodialog next="CallTransfer_OperatorRequestHandling#XR1015_CheckAllowedConditions_DS"/>
        </elseif>
        <elseif cond="GlobalVars.isOTPMDE == true">
          <action next="XR0023_GoToBroadcastMessages_SD"/>
        </elseif>
        <elseif cond="(isMDE == true || GlobalVars.otherGhostCaller == true) &amp;&amp; !(GlobalVars.tag == 'vague-agent_destination')">
          <session-mapping key="isMDE" value="false" type="Boolean"/>
          <gotodialog next="CallTransfer_MDEHandling#XR2001_CheckMDEType_DS"/>
        </elseif>
        <elseif cond="GlobalVars.skipBroadcastMessage == true">
          <action next="XR0025_PlayTransferPrompt_PP"/>
        </elseif>
        <elseif cond="GlobalVars.playTransferMessage == false &amp;&amp; GlobalVars.twoFactorAuthOutcome == 'not_attempted'">
          <action next="XR0030_GetTransferDestination_DB_DA"/>
        </elseif>
        <else>
          <action next="XR0023_GoToBroadcastMessages_SD"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="XR0006_MetricsAutopayXfer_DS">
      <action next="XR0023_GoToBroadcastMessages_SD"/>
    </decision-state>

    <subdialog-state id="XR0023_GoToBroadcastMessages_SD">
      <session-mapping key="GlobalVars.broadcastMessageKey" expr="'XR'"/>
      <gotodialog next="BroadcastMessages_Dialog"/>
      <action next="XR0023_GoToBroadcastMessages_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="XR0023_GoToBroadcastMessages_SD_return_CS">
      <action next="XR0025_PlayTransferPrompt_PP"/>
    </custom-state>

    <play-state id="XR0025_PlayTransferPrompt_PP">
      <session-mapping key="transferReason" value="GlobalVars.transferReason" type="String"/>
      <session-mapping key="playNoPINTransferPrompt" value="GlobalVars.playNoPINTransferPrompt" type="String"/>
      <session-mapping key="playTransferMessage" value="GlobalVars.playTransferMessage" type="String"/>
      <session-mapping key="paymentsEntryPoint" value="GlobalVars.paymentsEntryPoint" type="String"/>
      <session-mapping key="securityQuestionAttempts" value="GlobalVars.securityQuestionAttempts" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <audio>
        <if cond="transferReason == 'dbfail'">
          <prompt id="XR0025_out_01">
            <prompt-segments>
              <audiofile text="Im sorry, Im having some trouble with the system" src="XR0025_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="GlobalVars.acctLocked == true &amp;&amp; GlobalVars.playedAcctLocked == false">
            <prompt id="XR0025_out_07">
              <prompt-segments>
                <audiofile text="I'm sorry, I'm unable to access  your account" src="XR0025_out_07.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="playNoPINTransferPrompt == true &amp;&amp; securityQuestionAttempts == 3">
            <prompt id="XR0025_out_04">
              <prompt-segments>
                <audiofile text="That's still not right I'll take you to someone now, but they won't be able to look up your account without your security details " src="XR0025_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="playNoPINTransferPrompt == true &amp;&amp; securityQuestionAttempts != 3">
            <prompt id="XR0025_out_02">
              <prompt-segments>
                <audiofile text="Alright I'll take you to someone now, but they won't be able to look up your account without your security details" src="XR0025_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="paymentsEntryPoint != undefined">
            <prompt id="XR0025_out_03">
              <prompt-segments>
                <audiofile text="Just so you know, if you make your payment with an agent today, there will be an additional fee for that" src="XR0025_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
        </if>
        <prompt id="silence_750ms" cond="playTransferMessage == true">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="XR0025_out_08" cond="playTransferMessage == true &amp;&amp; tag == 'purchase-phone_nc'">
          <prompt-segments>
            <audiofile text="One minute while I find a New Service Agent to help you As always, we appreciate your business! Your call may be recorded" src="XR0025_out_08.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="XR0025_ini_02" cond="playTransferMessage == true &amp;&amp; tag != 'purchase-phone_nc'">
          <prompt-segments>
            <audiofile text="One minute while I find someone to help you  As always, we appreciate your business!" src="XR0025_ini_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="XR0025_out_05" cond="callType == 'troubleshoot' || callType == 'help_me_out' || tag == 'transfer-disambig_troubleshooting'">
          <prompt-segments>
            <audiofile text="By the way, it ll be easier to troubleshoot your problem if you re NOT calling from the phone thats having trouble If you are, please hang up and call back from another phone, or a landline " src="XR0025_out_05.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="XR0025_out_06">
          <prompt-segments>
            <audiofile text="Your call may be recorded " src="XR0025_out_06.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.FromXR1510" value="false" type="Boolean"/>
      <action next="XR0030_GetTransferDestination_DB_DA"/>
    </play-state>

    <custom-state id="PromptBeforeTransfer_CS">
      <field name="ForcePromptQueueBeforeExiting">
        <prompt timeout="100ms"/>
        <grammar version="1.0" xml:lang="en-US" tag-format="semantics/1.0" root="empty" xml:base="CallTransfer_Main.dvxml">
          <rule id="empty" scope="public">
            <one-of>
              <item>dummy item</item>
            </one-of>
          </rule>
        </grammar>
        <noinput>
          <action next="XR0030_GetTransferDestination_DB_DA"/>
        </noinput>
        <nomatch>
          <action next="XR0030_GetTransferDestination_DB_DA"/>
        </nomatch>
        <action next="XR0030_GetTransferDestination_DB_DA"/>
      </field>
      <event name="connection.disconnect.hangup">
        <gotodialog next="Exit_Dialog"/>
      </event>
      <catch>
        <action next="XR0030_GetTransferDestination_DB_DA"/>
      </catch>
    </custom-state>

    <decision-state id="XR0029_CheckCTIEnabled_DS">
      <if cond="(GlobalVars.enteredFrom != 'DSG' &amp;&amp; GlobalVars.enteredFrom != 'NRH' &amp;&amp; GlobalVars.enteredFrom != 'RCC') &amp;&amp; (GlobalVars.GetBCSParameters.care_enable_cti_transfer == true || GlobalVars.GetBCSParameters.care_enable_cti_transfer == 'true') &amp;&amp; (GlobalVars.enteredFrom != 'PWR')">
        <action next="XR0031_AttachCTIData_DB_DA"/>
        <else>
          <action next="XR0035_TransferToQueue_CT_CS"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="XR0030_GetTransferDestination_DB_DA">
      <session-mapping key="transferTag" value="TransferTag" type="String"/>
      <data-access id="GetTransferDestination" classname="com.nuance.metro.dataaccess.GetTransferDestination">
        <inputs>
          <input-variable name="transferTag"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="transferNumber"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetTransferDestination.status == 'Success'">
          <session-mapping key="GlobalVars.transferNumber" expr="GetTransferDestination.transferNumber"/>
          <else>
            <if cond="language == 'en-US'">
              <session-mapping key="GlobalVars.transferNumber" expr="'tel:8552246938#'"/>
              <else>
                <session-mapping key="GlobalVars.transferNumber" expr="'tel:8552246920#'"/>
              </else>
            </if>
          </else>
        </if>
        <if cond="TransferTag == 'Payment_Care_English' || TransferTag== 'Payment_Care_Spanish'"/>
        <action next="XR0029_CheckCTIEnabled_JDA"/>
      </action>
    </data-access-state>

    <data-access-state id="XR0031_AttachCTIData_DB_DA">
      <session-mapping key="cti_MDN" value="GlobalVars.cti_MDN" type="String"/>
      <session-mapping key="cti_PIN" value="GlobalVars.cti_PIN" type="String"/>
      <session-mapping key="cti_AuthStatus" value="GlobalVars.cti_AuthStatus" type="String"/>
      <session-mapping key="cti_Intent" value="GlobalVars.cti_Intent" type="String"/>
      <session-mapping key="cti_TransferReason" value="GlobalVars.cti_TransferReason" type="String"/>
      <data-access id="AttachCTIData" classname="com.nuance.metro.dataaccess.AttachCTIData">
        <inputs>
          <input-variable name="cti_MDN"/>
          <input-variable name="cti_PIN" mask="true"/>
          <input-variable name="cti_AuthStatus"/>
          <input-variable name="cti_Intent"/>
          <input-variable name="cti_TransferReason"/>
          <input-variable name="id1"/>
          <input-variable name="id2"/>
          <input-variable name="id3"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="AttachCTIData.status == 'Success'">
          <session-mapping key="ctiAttachVars.eventTypeGMT" expr="getEventTime()"/>
          <session-mapping key="ctiAttachVars.status" expr="'success'"/>
          <session-mapping key="ctiAttachVars.eventType" expr="'attach_cti_data'"/>
          <action next="XR0032_CTITransfer_DB_DA"/>
          <else>
            <session-mapping key="ctiAttachVars.eventTypeGMT" expr="getEventTime()"/>
            <session-mapping key="ctiAttachVars.status" expr="'failure'"/>
            <session-mapping key="ctiAttachVars.eventType" expr="'attach_cti_data'"/>
            <action next="XR0035_TransferToQueue_CT_CS"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <data-access-state id="XR0032_CTITransfer_DB_DA">
      <session-mapping key="transferNumber" value="GlobalVars.transferNumber" type="String"/>
      <data-access id="CTITransfer" classname="com.nuance.metro.dataaccess.CTITransfer">
        <inputs>
          <input-variable name="transferNumber"/>
          <input-variable name="id1"/>
          <input-variable name="id2"/>
          <input-variable name="id3"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="CTITransfer.status == 'Success'">
          <session-mapping key="ctiTransferVars.eventTypeGMT" expr="getEventTime()"/>
          <session-mapping key="ctiTransferVars.status" expr="'success'"/>
          <session-mapping key="ctiTransferVars.eventType" expr="'cti_transfer'"/>
          <gotodialog next="Exit_Dialog"/>
          <else>
            <session-mapping key="ctiTransferVars.eventTypeGMT" expr="getEventTime()"/>
            <session-mapping key="ctiTransferVars.status" expr="'failure'"/>
            <session-mapping key="ctiTransferVars.eventType" expr="'cti_transfer'"/>
            <action next="XR0035_TransferToQueue_CT_CS"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <custom-state id="XR0035_TransferToQueue_CT_CS">
      <transfer name="transfer" destexpr="GlobalVars.transferNumber" type="blind">
        <gotodialog next="Exit_Dialog"/>
      </transfer>
      <gotodialog next="Exit_Dialog"/>
      <event name="error">
        <gotodialog next="Exit_Dialog"/>
      </event>
    </custom-state>

    <custom-state id="XR0036_TransferToQueueCTI_CT_CS">
      <transfer name="transfer" destexpr="GlobalVars.transferNumber" type="blind">
        <gotodialog next="Exit_Dialog"/>
      </transfer>
      <gotodialog next="Exit_Dialog"/>
      <event name="error">
        <gotodialog next="Exit_Dialog"/>
      </event>
    </custom-state>

    <subdialog-state id="XR0040_GoToGoodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="XR0040_GoToGoodbye_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="XR0040_GoToGoodbye_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

  </dialog>
  