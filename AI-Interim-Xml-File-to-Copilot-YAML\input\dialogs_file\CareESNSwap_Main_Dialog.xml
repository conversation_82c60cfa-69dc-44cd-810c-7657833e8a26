<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="CareESNSwap_Main_Dialog">
    <decision-state id="ES1001_CheckContext_DS">
      <session-mapping key="GlobalVars.usingOldSIMForSwap" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.IMEI" expr="''"/>
      <session-mapping key="GlobalVars.imeiFailChecksum" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.ICCID" expr="''"/>
      <session-mapping key="GlobalVars.iccidFailChecksum" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.twoFactorAuthEntryPoint" expr="'care_esn'"/>
      <session-mapping key="GlobalVars.BYODRegistered" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.isByod" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.isOrderApproved" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.compatibilityTransfer" value="false" type="Boolean"/>
      <action next="ES1002_BroadcastMessages_SD"/>
    </decision-state>

    <subdialog-state id="ES1002_BroadcastMessages_SD">
      <session-mapping key="GlobalVars.broadcastMessageKey" expr="'DC'"/>
      <gotodialog next="BroadcastMessages_Dialog"/>
      <action next="ES1002_BroadcastMessages_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES1002_BroadcastMessages_SD_return_CS">
      <action next="ES1003_CheckAniMatch_JDA"/>
    </custom-state>

    <decision-state id="ES1003_CheckAniMatch_DS">
      <if cond="((GlobalVars.aniMatch == true || GlobalVars.aniMatch == 'true') &amp;&amp; GlobalVars.switchLinesSuccess == false)">
        <action next="ES1105_ConfirmANIAccountYN_DM"/>
        <else>
          <action next="ES1010_ListRequiredInfo_PP"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="ES1005_KeepingSameSIMCard_DM" type="CUST">
      <success>
        <action label="same">
          <audio>
            <prompt id="ES1005_out_04">
              <prompt-segments>
                <audiofile text="Great " src="ES1005_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.usingOldSIMForSwap" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.iccidSerialNumber" expr="GlobalVars.GetAccountDetails.iccidSerialNumber"/>
          <gotodialog next="CareESNSwap_Main_Cont#ES1405_ValidateDeviceTransition_PP"/>
        </action>
        <action label="new">
          <if cond="GlobalVars.activityCode == 'BSS'">
            <session-mapping key="GlobalVars.fromESNSwapDialog" value="true" type="Boolean"/>
            <action next="ES1007_SimSwappedBlocked_DM"/>
            <else>
              <audio>
                <prompt id="ES1005_out_02">
                  <prompt-segments>
                    <audiofile text="Great" src="ES1005_out_02.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="ES1305_ICCIDTransitionSkipSBI_DM"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES1005_ini_01">
                <prompt-segments>
                  <audiofile text="Are you keeping the same SIM card, or did you get a new one? Say same one or new one" src="ES1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES1005_ini_01">
                <prompt-segments>
                  <audiofile text="Are you keeping the same SIM card, or did you get a new one? Say same one or new one" src="ES1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES1005_nm1_01">
                <prompt-segments>
                  <audiofile text="If you re keeping the SIM card from your old phone, say same one Otherwise, say new one " src="ES1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you re going to use the same SIM card you have now, say same one or press 1 If you got a new one for your new phone, say new one or press 2" src="ES1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you re going to use the same SIM card you have now, say same one or press 1 If you got a new one for your new phone, say new one or press 2" src="ES1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1005_nm1_01">
                <prompt-segments>
                  <audiofile text="If you re keeping the SIM card from your old phone, say same one Otherwise, say new one " src="ES1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you re going to use the same SIM card you have now, say same one or press 1 If you got a new one for your new phone, say new one or press 2" src="ES1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you re going to use the same SIM card you have now, say same one or press 1 If you got a new one for your new phone, say new one or press 2" src="ES1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="ES1005_ini_01">
                <prompt-segments>
                  <audiofile text="Are you keeping the same SIM card, or did you get a new one? Say same one or new one" src="ES1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="ES1005_KeepingSameSIMCard_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES1005_KeepingSameSIMCard_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="same == 'lastresult'">
                <prompt id="ES1005_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You re keeping your old SIM card" src="ES1005_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="new == 'lastresult'">
                  <prompt id="ES1005_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="You got a new SIM card " src="ES1005_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="same == 'lastresult'">
                <prompt id="ES1005_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You re keeping your old SIM card" src="ES1005_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="new == 'lastresult'">
                  <prompt id="ES1005_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="You got a new SIM card " src="ES1005_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="same == 'lastresult'">
                <prompt id="ES1005_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You re keeping your old SIM card" src="ES1005_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="new == 'lastresult'">
                  <prompt id="ES1005_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="You got a new SIM card " src="ES1005_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="ES1007_SimSwappedBlocked_DM" type="YSNO">
      <success>
        <action label="true">
          <action next="ES1008_CallCare2SelectProtect_SD"/>
        </action>
        <action label="false">
          <gotodialog next="CareESNSwap_Main_Cont#ES2225_Transfer_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES1007_ini_01">
                <prompt-segments>
                  <audiofile src="ES1007_ini_01.wav" text="Your account is currently blocked from sim changes  Would you like to update your accont settings now? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES1007_ini_01">
                <prompt-segments>
                  <audiofile src="ES1007_ini_01.wav" text="Your account is currently blocked from sim changes  Would you like to update your accont settings now? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES1007_nomatch1_01">
                <prompt-segments>
                  <audiofile src="ES1007_nomatch1_01.wav" text="Your account is currently blocked from sim changes  Would you like to update your account settings now? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1007_nm2_01">
                <prompt-segments>
                  <audiofile src="ES1007_nm2_01.wav" text="If you'd like to update your account settings so you can swap your sim card say 'yes' or press 1 Otherwise say 'no' or press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1007_nm2_01">
                <prompt-segments>
                  <audiofile src="ES1007_nm2_01.wav" text="If you'd like to update your account settings so you can swap your sim card say 'yes' or press 1 Otherwise say 'no' or press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1007_nomatch1_01">
                <prompt-segments>
                  <audiofile src="ES1007_nomatch1_01.wav" text="Your account is currently blocked from sim changes  Would you like to update your account settings now? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1007_nm2_01">
                <prompt-segments>
                  <audiofile src="ES1007_nm2_01.wav" text="If you'd like to update your account settings so you can swap your sim card say 'yes' or press 1 Otherwise say 'no' or press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1007_nm2_01">
                <prompt-segments>
                  <audiofile src="ES1007_nm2_01.wav" text="If you'd like to update your account settings so you can swap your sim card say 'yes' or press 1 Otherwise say 'no' or press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="ES1007_ini_01">
                <prompt-segments>
                  <audiofile src="ES1007_ini_01.wav" text="Your account is currently blocked from sim changes  Would you like to update your accont settings now? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="ES1007_SimSwappedBlocked_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES1007_SimSwappedBlocked_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="ES1008_CallCare2SelectProtect_SD">
      <gotodialog next="SelectProtectDialog_Dialog"/>
      <action next="ES1008_CallCare2SelectProtect_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES1008_CallCare2SelectProtect_SD_return_CS">
      <if cond="GlobalVars.restrictionsUpdateSuccessfull == true &amp;&amp; GlobalVars.simSwapRestricted == false">
        <action next="ES1009_SimSwapUnblockedSuccess_PP"/>
        <else>
          <gotodialog next="CareESNSwap_Main_Cont#ES2225_Transfer_SD"/>
        </else>
      </if>
    </custom-state>

    <play-state id="ES1009_SimSwapUnblockedSuccess_PP">
      <audio>
        <prompt id="ES1009_out_01">
          <prompt-segments>
            <audiofile src="ES1009_out_01.wav" text="Ok, sim changes are now allowed Let's continue"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="GlobalVars.deviceChangeTargetSIMChosen == 'ESIM'">
        <gotodialog next="CareESNSwap_Main_Cont#ES2205_AskWiFiConnected_DM"/>
        <else>
          <action next="ES1305_ICCIDTransitionSkipSBI_DM"/>
        </else>
      </if>
    </play-state>

    <play-state id="ES1010_ListRequiredInfo_PP">
      <if cond="!(GlobalVars.GetAccountDetails &amp;&amp; (GlobalVars.GetAccountDetails.hasPhoneInsurance == true || GlobalVars.GetAccountDetails.hasPhoneInsurance == 'true'))">
        <audio>
          <prompt id="ES1010_out_07">
            <prompt-segments>
              <audiofile text="Now, I ll just need to ask you for a couple of things, and you'll be set up before you know it! " src="ES1010_out_07.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="silence_500ms">
            <prompt-segments>
              <audiofile text="test" src="silence_500ms.wav"/>
            </prompt-segments>
          </prompt>
        </audio>
      </if>
      <if cond="(GlobalVars.GetAccountDetails.currentSIMType == 'PSIM') &amp;&amp; (GlobalVars.care_enable_twofactorauth == true || GlobalVars.care_enable_twofactorauth == 'true') &amp;&amp; ((GlobalVars.aniMatch == false || GlobalVars.aniMatch == 'false') || (GlobalVars.switchLinesSuccess == true || GlobalVars.switchLinesSuccess == 'true'))">
        <audio>
          <prompt id="ES1010_out_08">
            <prompt-segments>
              <audiofile text="For now, please keep your SIM card in your *current* phone if you still have it I'll tell you when you can switch it to the new one " src="ES1010_out_08.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="silence_500ms">
            <prompt-segments>
              <audiofile text="test" src="silence_500ms.wav"/>
            </prompt-segments>
          </prompt>
        </audio>
      </if>
      <gotodialog next="CareESNSwap_Main_Cont#ES1814_CheckNeedLogin_DS"/>
    </play-state>

    <dm-state id="ES1105_ConfirmANIAccountYN_DM" type="YSNO">
      <session-mapping key="ANI" value="GlobalVars.trn" type="String"/>
      <success>
        <action label="true">
          <audio>
            <prompt id="ES1105_out_02">
              <prompt-segments>
                <audiofile text="Got it" src="ES1105_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="ES1120_CheckFutureDatedRequest_JDA"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.esnSetupIssue" expr="'differentaccount'"/>
          <session-mapping key="GlobalVars.switchLinesEntryPoint" expr="'esn_swap'"/>
          <audio>
            <prompt id="ES1105_out_01">
              <prompt-segments>
                <audiofile text="Okay " src="ES1105_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="ES1115_SwitchLines_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES1105_ini_01">
                <prompt-segments>
                  <audiofile text="Just to confirm, you want to replace the phone you're calling on now, right? " src="ES1105_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES1105_ini_01">
                <prompt-segments>
                  <audiofile text="Just to confirm, you want to replace the phone you're calling on now, right? " src="ES1105_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES1105_nm1_01">
                <prompt-segments>
                  <audiofile text="I have your phone number as" src="ES1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="ANI">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="ES1105_nm1_02">
                <prompt-segments>
                  <audiofile text="Do you want to switch the phone on this account? " src="ES1105_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="ES1105_nm1_01">
                <prompt-segments>
                  <audiofile text="I have your phone number as" src="ES1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="ANI">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="ES1105_nm1_02">
                <prompt-segments>
                  <audiofile text="Do you want to switch the phone on this account? " src="ES1105_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1105_nm1_01">
                <prompt-segments>
                  <audiofile text="I have your phone number as" src="ES1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="ANI">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="ES1105_nm1_02">
                <prompt-segments>
                  <audiofile text="Do you want to switch the phone on this account? " src="ES1105_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1105_nm1_01">
                <prompt-segments>
                  <audiofile text="I have your phone number as" src="ES1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="ANI">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="ES1105_nm1_02">
                <prompt-segments>
                  <audiofile text="Do you want to switch the phone on this account? " src="ES1105_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1105_nm1_01">
                <prompt-segments>
                  <audiofile text="I have your phone number as" src="ES1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="ANI">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="ES1105_nm1_02">
                <prompt-segments>
                  <audiofile text="Do you want to switch the phone on this account? " src="ES1105_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1105_nm1_01">
                <prompt-segments>
                  <audiofile text="I have your phone number as" src="ES1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="ANI">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="ES1105_nm1_02">
                <prompt-segments>
                  <audiofile text="Do you want to switch the phone on this account? " src="ES1105_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="ES1105_ConfirmANIAccountYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES1105_ConfirmANIAccountYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="ES1115_SwitchLines_SD">
      <gotodialog next="SwitchLines_Main_Dialog"/>
      <action next="ES1115_SwitchLines_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES1115_SwitchLines_SD_return_CS">
      <action next="ES1116_SwitchLinesResumeSwap_PP"/>
    </custom-state>

    <play-state id="ES1116_SwitchLinesResumeSwap_PP">
      <audio>
        <prompt id="ES1116_out_01">
          <prompt-segments>
            <audiofile text="Let's continue switching the phone for *that* line" src="ES1116_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="ES1120_CheckFutureDatedRequest_JDA"/>
    </play-state>

    <decision-state id="ES1120_CheckFutureDatedRequest_DS">
      <if cond="(GlobalVars.GetAccountDetails &amp;&amp; (GlobalVars.GetAccountDetails.hasPhoneInsurance == true || GlobalVars.GetAccountDetails.hasPhoneInsurance == 'true'))">
        <session-mapping key="GlobalVars.callType" expr="'transfer_esn_insurance'"/>
        <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
      </if>
      <if cond="(GlobalVars.accountFutureRequestInd == true || GlobalVars.accountFutureRequestInd == 'true')">
        <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
        <else>
          <action next="ES1010_ListRequiredInfo_PP"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="ES1205_IMEITransitionSkipSBI_DM" type="CUST">
      <session-mapping key="switchLinesSuccess" value="GlobalVars.switchLinesSuccess" type="String"/>
      <session-mapping key="twoFactorAuthOutcome" value="GlobalVars.twoFactorAuthOutcome" type="String"/>
      <success>
        <action label="continue">
          <action next="ES1220_CollectIMEI_DM"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="twoFactorAuthOutcome == 'success'">
                <prompt id="ES1205_ini_04">
                  <prompt-segments>
                    <audiofile text="Next, I ll need the serial number, or I-M-E-I, for the phone you re switching to" src="ES1205_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="switchLinesSuccess == true">
                  <prompt id="ES1205_ini_03">
                    <prompt-segments>
                      <audiofile text="First, the serial number, or I-M-E-I, for the phone you re switching to" src="ES1205_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="ES1205_ini_01">
                    <prompt-segments>
                      <audiofile text="Next, the serial number, or I-M-E-I, for the phone you re switching to" src="ES1205_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1205_ini_02">
                <prompt-segments>
                  <audiofile text="If you already *have* the number, press 1 " src="ES1205_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES1205_ni1_01">
                <prompt-segments>
                  <audiofile text="Otherwise, here's a neat trick to get it! " src="ES1205_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="ES1205_IMEITransitionSkipSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="3000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="ES1210_FindIMEIInstructions_PP">
      <audio>
        <prompt id="ES1210_out_01">
          <prompt-segments>
            <audiofile text="I'll give you a special code you can dial on your new phone That'll display the serial number on the screen Make sure you do it on the phone you're switching *to*, not the one we're talking on Here we go " src="ES1210_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ES1210_out_02">
          <prompt-segments>
            <audiofile text="The code is star, pound, zero, six, pound Dial it on your new phone as if you were making a call  " src="ES1210_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="ES1215_FindIMEIWaitSBI_DM"/>
    </play-state>

    <dm-state id="ES1215_FindIMEIWaitSBI_DM" type="CUST">
      <success>
        <action label="continue">
          <action next="ES1220_CollectIMEI_DM"/>
        </action>
        <action label="not_working">
          <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES1215_ini_01">
                <prompt-segments>
                  <audiofile text="I ll wait while you try that Dial star, pound, zero, six, pound on your new phone When you see the serial number, say continue " src="ES1215_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1215_ini_02">
                <prompt-segments>
                  <audiofile text="10 seconds wait music Dial star, pound, zero, six, pound on your new phone When you see the serial number, say continue Or say It s not working 10 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 2 10 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 2 10 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 210 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 210 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 210 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 210 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say continue or press 1 Or say It s not working or press 2 10 seconds wait music We seem to be having some trouble" src="ES1215_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="ES1215_FindIMEIWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES1215_FindIMEIWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="30000ms" timeout="1000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="1000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="ES1220_CollectIMEI_DM" type="DIGT">
      <success>
        <action label="instructions">
          <audio>
            <prompt id="ES1220_out_01">
              <prompt-segments>
                <audiofile text="Sure, instructions " src="ES1220_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="ES1210_FindIMEIInstructions_PP"/>
        </action>
        <action label="default" next="ES1225_IMEIPassChecksum_JDA">
          <session-mapping key="GlobalVars.imeiSerialNumber" expr="ES1220_CollectIMEI_DM.returnvalue"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="enteringFrom == 'ES1225_IMEIPassChecksum_DS'">
                <prompt id="ES1220_rin_01">
                  <prompt-segments>
                    <audiofile text="That does not look like a valid number Let s try one more time to be sure If you have two different numbers, look for the one labeled I M E I To hear the instructions to  find  the number, press star Otherwise, go ahead and enter it again " src="ES1220_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="ES1220_ini_01">
                    <prompt-segments>
                      <audiofile text="If you see a slash followed by two numbers after your serial number, you dont need to enter those So, what s the 15 digit I M E I number? You can say or enter it " src="ES1220_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="enteringFrom == 'ES1225_IMEIPassChecksum_DS'">
                <prompt id="ES1220_rin_01">
                  <prompt-segments>
                    <audiofile text="That does not look like a valid number Let s try one more time to be sure If you have two different numbers, look for the one labeled I M E I To hear the instructions to  find  the number, press star Otherwise, go ahead and enter it again " src="ES1220_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="ES1220_ini_01">
                    <prompt-segments>
                      <audiofile text="If you see a slash followed by two numbers after your serial number, you dont need to enter those So, what s the 15 digit I M E I number? You can say or enter it " src="ES1220_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES1220_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the 15 digit I M E I for your phone You can skip any numbers that come after a slash To hear how you can find the number again, say  instructions  " src="ES1220_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1220_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have the 15 digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to find the number, press star " src="ES1220_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1220_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have the 15 digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to find the number, press star " src="ES1220_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1220_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the 15 digit I M E I for your phone You can skip any numbers that come after a slash To hear how you can find the number again, say  instructions  " src="ES1220_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1220_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have the 15 digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to find the number, press star " src="ES1220_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1220_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have the 15 digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to find the number, press star " src="ES1220_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="enteringFrom == 'ES1225_IMEIPassChecksum_DS'">
                <prompt id="ES1220_rin_01">
                  <prompt-segments>
                    <audiofile text="That does not look like a valid number Let s try one more time to be sure If you have two different numbers, look for the one labeled I M E I To hear the instructions to  find  the number, press star Otherwise, go ahead and enter it again " src="ES1220_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="ES1220_ini_01">
                    <prompt-segments>
                      <audiofile text="If you see a slash followed by two numbers after your serial number, you dont need to enter those So, what s the 15 digit I M E I number? You can say or enter it " src="ES1220_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="ES1220_CollectIMEI_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES1220_CollectIMEI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="19000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration>
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" interdigittimeout="3000ms" termtimeout="200ms" timeout="19000ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="ALWAYS" highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1220_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Just to be sure, that was" src="ES1220_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1220_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Did I get that right? " src="ES1220_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1220_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Just to be sure, that was" src="ES1220_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1220_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Did I get that right? " src="ES1220_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1220_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Just to be sure, that was" src="ES1220_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1220_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Did I get that right? " src="ES1220_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1220_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Just to be sure, that was" src="ES1220_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1220_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Did I get that right? " src="ES1220_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1220_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Just to be sure, that was" src="ES1220_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1220_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Did I get that right? " src="ES1220_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1220_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Just to be sure, that was" src="ES1220_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1220_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Did I get that right? " src="ES1220_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1220_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Just to be sure, that was" src="ES1220_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1220_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Did I get that right? " src="ES1220_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="ES1225_IMEIPassChecksum_DS">
      <if cond="passMod10Checksum(GlobalVars.imeiSerialNumber)">
        <gotodialog next="CareESNSwap_Main_Cont#ES2105_FirstValidateDeviceTransition_PP"/>
        <else>
          <if cond="GlobalVars.imeiFailChecksum == true">
            <action next="ES1230_NumberFailedChecksum_PP"/>
            <else>
              <session-mapping key="GlobalVars.imeiFailChecksum" value="true" type="Boolean"/>
              <session-mapping key="enteringFrom" expr="'ES1225_IMEIPassChecksum_DS'"/>
              <action next="ES1220_CollectIMEI_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="ES1230_NumberFailedChecksum_PP">
      <audio>
        <prompt id="ES1230_out_01">
          <prompt-segments>
            <audiofile text="I m having some trouble with that, but our agents will be happy to switch your phone " src="ES1230_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
      <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
    </play-state>

    <dm-state id="ES1305_ICCIDTransitionSkipSBI_DM" type="CUST">
      <session-mapping key="simType" value="GlobalVars.ValidateDevice.simType" type="String"/>
      <success>
        <action label="continue">
          <action next="ES1320_CollectICCID_DM"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="simType == 'PESIM'">
                <prompt id="ES1305_ini_03">
                  <prompt-segments>
                    <audiofile src="ES1305_ini_03.wav" text="I'll need the serial number for the Metro SIM card you'll be using"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="ES1305_ini_01">
                    <prompt-segments>
                      <audiofile src="ES1305_ini_01.wav" text="And last, the serial number for the Metro  SIM card you ll be using"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1305_ini_02">
                <prompt-segments>
                  <audiofile text="If you already have it, press 1" src="ES1305_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES1305_ni1_01">
                <prompt-segments>
                  <audiofile text="Otherwise, here's some tips!  " src="ES1305_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="ES1305_ICCIDTransitionSkipSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="3000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="ES1310_FindICCIDInstructions_PP">
      <audio>
        <prompt id="ES1310_out_01">
          <prompt-segments>
            <audiofile text="You can find the SIM card number on the SIM card itself, or on the bigger plastic card it came in " src="ES1310_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ES1310_out_02">
          <prompt-segments>
            <audiofile text="If your SIM card is already in your new phone, you can take it out for now to read the number It can be in a slot on the side of your phone, or inside the battery space" src="ES1310_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ES1310_out_03">
          <prompt-segments>
            <audiofile text="The number is 19 digits long It should start with eight nine zero, and end with the letter F, like Frank " src="ES1310_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="ES1315_FindICCIDWaitSBI_DM"/>
    </play-state>

    <dm-state id="ES1315_FindICCIDWaitSBI_DM" type="CUST">
      <success>
        <action label="continue">
          <audio>
            <prompt id="ES1315_out_01">
              <prompt-segments>
                <audiofile text="Okay " src="ES1315_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="ES1320_CollectICCID_DM"/>
        </action>
        <action label="cant_find">
          <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES1315_ini_01">
                <prompt-segments>
                  <audiofile text="I ll wait while you look for it When you have it, say  continue  " src="ES1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1315_ini_02">
                <prompt-segments>
                  <audiofile text="10 seconds wait music Look for the 19-digit SIM Card number, that ends with the letter F When you have it, say 'continue' Or say 'I can't find it' 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 210 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music When you have your SIM card number, say 'continue' or press 1 Or say 'I can't find it' or press 2 10 seconds wait music We seem to be having some trouble              " src="ES1315_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="ES1315_FindICCIDWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES1315_FindICCIDWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="30000ms" timeout="1000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="1000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="ES1320_CollectICCID_DM" type="DIGT">
      <success>
        <action label="instructions">
          <audio>
            <prompt id="ES1320_out_01">
              <prompt-segments>
                <audiofile text="Alright, instructions " src="ES1320_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="ES1310_FindICCIDInstructions_PP"/>
        </action>
        <action label="default" next="ES1325_ICCIDPassChecksum_JDA">
          <session-mapping key="GlobalVars.iccidSerialNumber" expr="ES1320_CollectICCID_DM.returnvalue"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="enteringFrom == 'ES1325_ICCIDPassChecksum_DS'">
                <prompt id="ES1320_rin_01">
                  <prompt-segments>
                    <audiofile text="That doesn t look like a valid SIM card number Let s try one more time to be sure To hear the instructions to find the number, press star Otherwise, go ahead and enter it one more time, using your keypad " src="ES1320_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="ES1320_ini_01">
                    <prompt-segments>
                      <audiofile text="Say or enter the whole number, but without the letter F " src="ES1320_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="enteringFrom == 'ES1325_ICCIDPassChecksum_DS'">
                <prompt id="ES1320_rin_01">
                  <prompt-segments>
                    <audiofile text="That doesn t look like a valid SIM card number Let s try one more time to be sure To hear the instructions to find the number, press star Otherwise, go ahead and enter it one more time, using your keypad " src="ES1320_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="ES1320_ini_01">
                    <prompt-segments>
                      <audiofile text="Say or enter the whole number, but without the letter F " src="ES1320_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES1320_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the number on the SIM card you want to use in your new phone It s 19 digits long, and ends in the letter F Enter only the numbers To hear how you can find the number again, say instructions " src="ES1320_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1320_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have the 19 digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to find the number, press star " src="ES1320_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1320_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have the 19 digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to find the number, press star " src="ES1320_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1320_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the number on the SIM card you want to use in your new phone It s 19 digits long, and ends in the letter F Enter only the numbers To hear how you can find the number again, say instructions " src="ES1320_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1320_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have the 19 digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to find the number, press star " src="ES1320_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1320_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have the 19 digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to find the number, press star " src="ES1320_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="enteringFrom == 'ES1325_ICCIDPassChecksum_DS'">
                <prompt id="ES1320_rin_01">
                  <prompt-segments>
                    <audiofile text="That doesn t look like a valid SIM card number Let s try one more time to be sure To hear the instructions to find the number, press star Otherwise, go ahead and enter it one more time, using your keypad " src="ES1320_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="ES1320_ini_01">
                    <prompt-segments>
                      <audiofile text="Say or enter the whole number, but without the letter F " src="ES1320_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="ES1320_CollectICCID_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES1320_CollectICCID_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="24000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration>
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" interdigittimeout="3000ms" termtimeout="200ms" timeout="24000ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="ALWAYS" highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1320_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="I got" src="ES1320_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1320_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Is that right? " src="ES1320_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1320_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="I got" src="ES1320_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1320_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Is that right? " src="ES1320_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1320_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="I got" src="ES1320_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1320_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Is that right? " src="ES1320_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1320_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="I got" src="ES1320_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1320_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Is that right? " src="ES1320_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1320_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="I got" src="ES1320_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1320_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Is that right? " src="ES1320_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1320_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="I got" src="ES1320_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1320_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Is that right? " src="ES1320_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="ES1320_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="I got" src="ES1320_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1320_cnf_ini_02">
                  <prompt-segments>
                    <audiofile text="Is that right? " src="ES1320_cnf_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="ES1325_ICCIDPassChecksum_DS">
      <session-mapping key="F" value="F" type="String"/>
      <if cond="passMod10Checksum(GlobalVars.iccidSerialNumber)">
        <session-mapping key="GlobalVars.iccidSerialNumber" expr="GlobalVars.iccidSerialNumber + F"/>
        <gotodialog next="CareESNSwap_Main_Cont#ES1405_ValidateDeviceTransition_PP"/>
        <else>
          <if cond="GlobalVars.iccidFailChecksum == true">
            <action next="ES1230_NumberFailedChecksum_PP"/>
            <else>
              <session-mapping key="GlobalVars.iccidFailChecksum" value="true" type="Boolean"/>
              <session-mapping key="enteringFrom" expr="'ES1325_ICCIDPassChecksum_DS'"/>
              <action next="ES1320_CollectICCID_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

  </dialog>
  