<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="VoiceMailPin_Main_Dialog">
    <data-access-state id="VP1015_SubmitPasswordReset_DB_DA">
      <session-mapping key="min" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <property name="fetchaudiominimum" value="1.5s"/>
      <property name="fetchaudiodelay" value="0"/>
      <data-access id="ResetVoiceMailPIN" classname="com.nuance.metro.dataaccess.ResetVoiceMailPIN">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="min"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="ResetVoiceMailPIN.status == 'Success'">
          <action next="VP1020_PlayResetPasswordSuccess_PP"/>
          <elseif cond="ResetVoiceMailPIN.status == 'NOVOICEMAIL'">
            <action next="VP1030_NoVoicemail_PP"/>
          </elseif>
          <else>
            <action next="VP1025_PlayResetPasswordFailure_PP"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <play-state id="VP1020_PlayResetPasswordSuccess_PP">
      <audio>
        <prompt id="VP1020_ini_01">
          <prompt-segments>
            <audiofile text="Okay, I ve reset your voicemail password to the last 4 digits of your phone number When you get a chance, you should change your password to something else After you log in to your voicemail using the last 4 digits of your phone number, just follow the system prompts to choose a new password" src="VP1020_ini_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="VP1020_ini_02">
          <prompt-segments>
            <audiofile text="To protect your privacy, we suggest setting a mandatory password on your voicemail" src="VP1020_ini_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="VP1035_GoToAnythingElse_SD"/>
    </play-state>

    <play-state id="VP1025_PlayResetPasswordFailure_PP">
      <audio>
        <prompt id="VP1025_ini_01">
          <prompt-segments>
            <audiofile text="Sorry, I wasn t able to reset your password right now" src="VP1025_ini_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="VP1040_GoToCallTransfer_SD"/>
    </play-state>

    <play-state id="VP1030_NoVoicemail_PP">
      <audio>
        <prompt id="VP1030_ini_01">
          <prompt-segments>
            <audiofile text="Actually, you haven t signed up for voice mail yet, but you can sign up for it anytime you like" src="VP1030_ini_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="VP1035_GoToAnythingElse_SD"/>
    </play-state>

    <subdialog-state id="VP1035_GoToAnythingElse_SD">
      <gotodialog next="AnythingElse_Main_Dialog"/>
      <action next="VP1035_GoToAnythingElse_SD"/>
    </subdialog-state>
    <custom-state id="VP1035_GoToAnythingElse_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="VP1040_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="VP1040_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="VP1040_GoToCallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

  </dialog>
  