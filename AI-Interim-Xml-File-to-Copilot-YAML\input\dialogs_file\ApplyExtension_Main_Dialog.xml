<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="ApplyExtension_Main_Dialog">
    <decision-state id="AX1701_CheckContext_DS">
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <if cond="GlobalVars.accountFutureRequestInd == true">
        <action next="AX1702_PlayFutureRequestTransfer_PP"/>
        <elseif cond="(GlobalVars.callType == 'dsgExtension')">
          <action next="AX1706_DSGExtensionTerms_DM"/>
        </elseif>
        <elseif cond="(GlobalVars.tag == 'request-extension') &amp;&amp; (GlobalVars.extensionAllowed == true)">
          <action next="AX1705_ExtensionTermsYN_DM"/>
        </elseif>
        <elseif cond="(GlobalVars.tag == 'request-extension')">
          <action next="AX1805_CheckAccountSuspended_JDA"/>
        </elseif>
        <else>
          <if cond="GlobalVars.extensionEntryPoint == 'failed_payment'">
            <action next="AX1705_ExtensionTermsYN_DM"/>
            <else>
              <action next="AX1704_ExtensionEligibilityCheck_PP"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="AX1702_PlayFutureRequestTransfer_PP">
      <audio>
        <prompt id="AX1702_out_01">
          <prompt-segments>
            <audiofile text="By the way, because your account is suspended, any changes you make now will only take effect *after* you pay your full balance  I see you have an upcoming change on your account, so I'll need to transfer you to an agent for help with get an extension " src="AX1702_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AX1725_CallTransfer_SD"/>
    </play-state>

    <play-state id="AX1704_ExtensionEligibilityCheck_PP">
      <session-mapping key="extensionAllowed" value="GlobalVars.extensionAllowed" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="numberOfCases" value="GlobalVars.CheckUnhotlineEligibility != undefined ? GlobalVars.CheckUnhotlineEligibility.numberOfCases:0" type="String"/>
      <audio>
        <if cond="(tag == 'request-extension')">
          <prompt id="AX1704_out_04">
            <prompt-segments>
              <audiofile text="Let me see if your account *qualifies* for an extension" src="AX1704_out_04.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="AX1704_out_01">
              <prompt-segments>
                <audiofile text="Let me see if your account *qualifies* for one " src="AX1704_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="3secmusic">
          <prompt-segments>
            <audiofile text=" " src="3secmusic.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="(extensionAllowed ==true || extensionAllowed == 'true')">
          <prompt id="AX1704_out_02">
            <prompt-segments>
              <audiofile text="It looks like we *can* give you an extension! " src="AX1704_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="silence_500ms">
            <prompt-segments>
              <audiofile text="test" src="silence_500ms.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="AX1704_out_05" cond="numberOfCases &gt; 0">
              <prompt-segments>
                <audiofile text="You've already been granted an extension in the last 30 days So, you're not eligible for another one right now Our agents wouldn't be able to give you one either" src="AX1704_out_05.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="AX1704_out_03" cond="!(numberOfCases &gt; 0)">
              <prompt-segments>
                <audiofile text="It doesn't look like your account is eligible for an extension right now Accounts are only eligible for one extension in 30 days, and only within 7 days of being suspended Our agents wouldn't be able to give you one either" src="AX1704_out_03.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <if cond="(GlobalVars.extensionAllowed == true)">
        <action next="AX1705_ExtensionTermsYN_DM"/>
        <else>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </play-state>

    <dm-state id="AX1705_ExtensionTermsYN_DM" type="CUST">
      <session-mapping key="lang" value="language" type="String"/>
      <session-mapping key="audioMessageKey" value="care_configurable_extension_terms_audio" type="String"/>
      <session-mapping key="extensionEntryPoint" value="GlobalVars.extensionEntryPoint != undefined  ? GlobalVars.extensionEntryPoint : ''" type="String"/>
      <session-mapping key="care_nlu_enabled" value="GlobalVars.GetBCSParameters.care_nlu_enabled != undefined  ? GlobalVars.GetBCSParameters.care_nlu_enabled : true " type="String"/>
      <success>
        <action label="yes">
          <audio>
            <prompt id="AX1705_out_03">
              <prompt-segments>
                <audiofile text="Okay One moment while I work on that " src="AX1705_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_1000ms">
              <prompt-segments>
                <audiofile text="test" src="silence_1000ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="AX1710_CreateUnhotlineCase_DB_DA"/>
        </action>
        <action label="no">
          <audio>
            <prompt id="AX1705_out_04">
              <prompt-segments>
                <audiofile text="No problem " src="AX1705_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="make-payment">
          <audio>
            <prompt id="AX1705_out_01">
              <prompt-segments>
                <audiofile text="Great" src="AX1705_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.tag" expr="'make-payment'"/>
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careSuspended'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator">
          <session-mapping key="AX1705_operator_counter" expr="AX1705_operator_counter+1"/>
          <if cond="AX1705_operator_counter &lt; 3">
            <action next="AX1705_ExtensionTermsYN_DM"/>
            <else>
              <audio>
                <prompt id="AX1705_operator_03">
                  <prompt-segments>
                    <audiofile text="I can give you a payment extension right here  " src="AX1705_operator_03.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
              <session-mapping key="GlobalVars.playFailureGoodbye" value="true" type="Boolean"/>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="AX1705_operator_counter == 1">
                <prompt id="AX1705_operator_01">
                  <prompt-segments>
                    <audiofile text="I'm sorry, I can't transfer you right now But if you want a payment extension, I can do that right here" src="AX1705_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AX1705_operator_02">
                  <prompt-segments>
                    <audiofile text="Do you agree to a *one-time* extension to restore your phone service for forty-eight hours? Please say 'yes' or 'no' " src="AX1705_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="AX1705_operator_counter == 2">
                  <prompt id="AX1705_operator_04">
                    <prompt-segments>
                      <audiofile text="Let me try to help you here one more time " src="AX1705_operator_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_operator_05">
                    <prompt-segments>
                      <audiofile text="I can give you a payment extension right here  " src="AX1705_operator_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_01">
                    <prompt-segments>
                      <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_750ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_750ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_02">
                    <prompt-segments>
                      <audiofile text="Do you agree to go ahead? " src="AX1705_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="AX1705_ini_01">
                    <prompt-segments>
                      <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="custom" expr="audioMessageKey" bargein="false">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_02">
                    <prompt-segments>
                      <audiofile text="Do you agree to go ahead? " src="AX1705_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_03" cond="extensionEntryPoint == 'payment_amount' &amp;&amp; care_nlu_enabled == true  &amp;&amp; language == 'en-US'">
                    <prompt-segments>
                      <audiofile text="Or say back to payment " src="AX1705_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="AX1705_operator_counter == 1">
                <prompt id="AX1705_operator_01">
                  <prompt-segments>
                    <audiofile text="I'm sorry, I can't transfer you right now But if you want a payment extension, I can do that right here" src="AX1705_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AX1705_operator_02">
                  <prompt-segments>
                    <audiofile text="Do you agree to a *one-time* extension to restore your phone service for forty-eight hours? Please say 'yes' or 'no' " src="AX1705_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="AX1705_operator_counter == 2">
                  <prompt id="AX1705_operator_04">
                    <prompt-segments>
                      <audiofile text="Let me try to help you here one more time " src="AX1705_operator_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_operator_05">
                    <prompt-segments>
                      <audiofile text="I can give you a payment extension right here  " src="AX1705_operator_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_01">
                    <prompt-segments>
                      <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_750ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_750ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_02">
                    <prompt-segments>
                      <audiofile text="Do you agree to go ahead? " src="AX1705_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="AX1705_ini_01">
                    <prompt-segments>
                      <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="custom" expr="audioMessageKey" bargein="false">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_02">
                    <prompt-segments>
                      <audiofile text="Do you agree to go ahead? " src="AX1705_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_03" cond="extensionEntryPoint == 'payment_amount' &amp;&amp; care_nlu_enabled == true  &amp;&amp; language == 'en-US'">
                    <prompt-segments>
                      <audiofile text="Or say back to payment " src="AX1705_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AX1705_ini_01">
                <prompt-segments>
                  <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1705_ini_02">
                <prompt-segments>
                  <audiofile text="Do you agree to go ahead? " src="AX1705_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1705_nm2_01">
                <prompt-segments>
                  <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="AX1705_nm2_02">
                <prompt-segments>
                  <audiofile text="If you agree to go ahead, say yes or press 1 Otherwise, say no or press 2 " src="AX1705_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1705_nm2_01">
                <prompt-segments>
                  <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="AX1705_nm2_02">
                <prompt-segments>
                  <audiofile text="If you agree to go ahead, say yes or press 1 Otherwise, say no or press 2 " src="AX1705_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1705_ini_01">
                <prompt-segments>
                  <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1705_ini_02">
                <prompt-segments>
                  <audiofile text="Do you agree to go ahead? " src="AX1705_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1705_nm2_01">
                <prompt-segments>
                  <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="AX1705_nm2_02">
                <prompt-segments>
                  <audiofile text="If you agree to go ahead, say yes or press 1 Otherwise, say no or press 2 " src="AX1705_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1705_nm2_01">
                <prompt-segments>
                  <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="AX1705_nm2_02">
                <prompt-segments>
                  <audiofile text="If you agree to go ahead, say yes or press 1 Otherwise, say no or press 2 " src="AX1705_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="AX1705_operator_counter == 1">
                <prompt id="AX1705_operator_01">
                  <prompt-segments>
                    <audiofile text="I'm sorry, I can't transfer you right now But if you want a payment extension, I can do that right here" src="AX1705_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AX1705_operator_02">
                  <prompt-segments>
                    <audiofile text="Do you agree to a *one-time* extension to restore your phone service for forty-eight hours? Please say 'yes' or 'no' " src="AX1705_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="AX1705_operator_counter == 2">
                  <prompt id="AX1705_operator_04">
                    <prompt-segments>
                      <audiofile text="Let me try to help you here one more time " src="AX1705_operator_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_operator_05">
                    <prompt-segments>
                      <audiofile text="I can give you a payment extension right here  " src="AX1705_operator_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_01">
                    <prompt-segments>
                      <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_750ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_750ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_02">
                    <prompt-segments>
                      <audiofile text="Do you agree to go ahead? " src="AX1705_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="AX1705_ini_01">
                    <prompt-segments>
                      <audiofile text="By saying yes, you agree to THESE terms " src="AX1705_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="custom" expr="audioMessageKey" bargein="false">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_02">
                    <prompt-segments>
                      <audiofile text="Do you agree to go ahead? " src="AX1705_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="AX1705_ini_03" cond="extensionEntryPoint == 'payment_amount' &amp;&amp; care_nlu_enabled == true  &amp;&amp; language == 'en-US'">
                    <prompt-segments>
                      <audiofile text="Or say back to payment " src="AX1705_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommandsNoOperator_dtmf.grxml">
          <grammars filename="AX1705_ExtensionTermsYN_DM.jsp" count="1">
            <param name="extensionEntryPoint" value="extensionEntryPointVXMLVar"/>
            <param name="care_nlu_enabled" value="care_nlu_enabledVXMLVar"/>
            <param name="language" value="languageVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="AX1705_ExtensionTermsYN_DM_dtmf.jsp" count="1">
            <param name="extensionEntryPoint" value="extensionEntryPointVXMLVar"/>
            <param name="care_nlu_enabled" value="care_nlu_enabledVXMLVar"/>
            <param name="language" value="languageVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="AX1705_ExtensionTermsYN_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AX1706_DSGExtensionTerms_DM" type="CUST">
      <session-mapping key="audioMessageKey" value="care_configurable_extension_terms_audio" type="String"/>
      <success>
        <action label="yes">
          <audio>
            <prompt id="AX1706_out_01">
              <prompt-segments>
                <audiofile text="Hold the line " src="AX1706_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="AX1710_CreateUnhotlineCase_DB_DA"/>
        </action>
        <action label="repeat">
          <audio>
            <prompt id="AX1706_out_02">
              <prompt-segments>
                <audiofile text="Sure " src="AX1706_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="AX1706_DSGExtensionTerms_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AX1706_ini_01">
                <prompt-segments>
                  <audiofile text="Please confirm with the customer that they agree to the following terms " src="AX1706_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_02">
                <prompt-segments>
                  <audiofile text="If they agree, press 1 To hear this again, press 2 Otherwise, please hang up " src="AX1706_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AX1706_DSGExtensionTerms_DM_initial"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AX1706_ini_01">
                <prompt-segments>
                  <audiofile text="Please confirm with the customer that they agree to the following terms " src="AX1706_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_02">
                <prompt-segments>
                  <audiofile text="If they agree, press 1 To hear this again, press 2 Otherwise, please hang up " src="AX1706_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AX1706_ini_01">
                <prompt-segments>
                  <audiofile text="Please confirm with the customer that they agree to the following terms " src="AX1706_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_02">
                <prompt-segments>
                  <audiofile text="If they agree, press 1 To hear this again, press 2 Otherwise, please hang up " src="AX1706_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_01">
                <prompt-segments>
                  <audiofile text="Please confirm with the customer that they agree to the following terms " src="AX1706_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_02">
                <prompt-segments>
                  <audiofile text="If they agree, press 1 To hear this again, press 2 Otherwise, please hang up " src="AX1706_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_01">
                <prompt-segments>
                  <audiofile text="Please confirm with the customer that they agree to the following terms " src="AX1706_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_02">
                <prompt-segments>
                  <audiofile text="If they agree, press 1 To hear this again, press 2 Otherwise, please hang up " src="AX1706_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_01">
                <prompt-segments>
                  <audiofile text="Please confirm with the customer that they agree to the following terms " src="AX1706_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_02">
                <prompt-segments>
                  <audiofile text="If they agree, press 1 To hear this again, press 2 Otherwise, please hang up " src="AX1706_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_01">
                <prompt-segments>
                  <audiofile text="Please confirm with the customer that they agree to the following terms " src="AX1706_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_02">
                <prompt-segments>
                  <audiofile text="If they agree, press 1 To hear this again, press 2 Otherwise, please hang up " src="AX1706_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_01">
                <prompt-segments>
                  <audiofile text="Please confirm with the customer that they agree to the following terms " src="AX1706_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_02">
                <prompt-segments>
                  <audiofile text="If they agree, press 1 To hear this again, press 2 Otherwise, please hang up " src="AX1706_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AX1706_ini_01">
                <prompt-segments>
                  <audiofile text="Please confirm with the customer that they agree to the following terms " src="AX1706_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AX1706_ini_02">
                <prompt-segments>
                  <audiofile text="If they agree, press 1 To hear this again, press 2 Otherwise, please hang up " src="AX1706_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <dtmfgrammars filename="AX1706_DSGExtensionTerms_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="AX1710_CreateUnhotlineCase_DB_DA">
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="accountNumber" value="GlobalVars.GetAccountDetails.accountNumber" type="String"/>
      <data-access id="CreateUnhotlineCase" classname="com.nuance.metro.dataaccess.CreateUnhotlineCase">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="accountNumber" mask="true"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="CreateUnhotlineCase.status == 'success'">
          <if cond="GlobalVars.callType == 'dsgExtension'">
            <action next="AX1716_DSGPlaySuccess_PP"/>
            <else>
              <action next="AX1715_PlaySuccess_PP"/>
            </else>
          </if>
        </if>
        <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
        <gotodialog next="CallTransfer_Main_Dialog"/>
      </action>
      </data-access-state>

    <play-state id="AX1715_PlaySuccess_PP">
      <audio>
        <prompt id="AX1715_out_01">
          <prompt-segments>
            <audiofile text="All set your service should be back on in a few minutes  Be aware that an extension does not reset your payment due date for future payments When you re ready, you can pay over the phone here, or with the myMetro app You can also pay online or get a map of our locations near you at metrobyt-mobilecom" src="AX1715_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.extensionSuccess" value="true" type="Boolean"/>
      <action next="AX1720_Goodbye_SD"/>
    </play-state>

    <play-state id="AX1716_DSGPlaySuccess_PP">
      <audio>
        <prompt id="AX1716_out_01">
          <prompt-segments>
            <audiofile text="The extension was successfully applied Service will be turned back on in a few minutes " src="AX1716_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AX1716_out_02">
          <prompt-segments>
            <audiofile text="Please remind the customer to make their payment as soon as possible to avoid service interruptions You can now hang up " src="AX1716_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AX1716_out_03">
          <prompt-segments>
            <audiofile src="AX1716_out_03.wav" text="Thank you for calling Metro s DSG Goodbye"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.extensionSuccess" value="true" type="Boolean"/>
      <action next="getReturnLink()"/>
    </play-state>

    <subdialog-state id="AX1720_Goodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="AX1720_Goodbye_SD_return"/>
    </subdialog-state>
    <subdialog-state id="AX1725_CallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="AX1725_CallTransfer_SD_return"/>
    </subdialog-state>
    <decision-state id="AX1805_CheckAccountSuspended_DS">
      <if cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended' &amp;&amp; GlobalVars.GetAccountDetails.eligibleForUnhotlineCheck)">
        <action next="AX1810_CheckUnhotlineEligibility_DB_DA"/>
        <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')">
          <action next="AX1704_ExtensionEligibilityCheck_PP"/>
        </elseif>
        <else>
          <action next="AX1815_AccountStillActive_PP"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="AX1810_CheckUnhotlineEligibility_DB_DA">
      <session-mapping key="accountNumber" value="GlobalVars.GetAccountDetails.accountNumber" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="CheckUnhotlineEligibility" classname="com.nuance.metro.dataaccess.CheckUnhotlineEligibility">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="accountNumber" mask="true"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="isEligible"/>
          <output-variable name="numberOfCases"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.CheckUnhotlineEligibility" expr="CheckUnhotlineEligibility"/>
        <if cond="(CheckUnhotlineEligibility.isEligible == 'true' || CheckUnhotlineEligibility.isEligible == true)">
          <session-mapping key="GlobalVars.extensionAllowed" value="true" type="Boolean"/>
        </if>
        <action next="AX1704_ExtensionEligibilityCheck_PP"/>
      </action>
    </data-access-state>

    <play-state id="AX1815_AccountStillActive_PP">
      <audio>
        <prompt id="AX1815_out_01">
          <prompt-segments>
            <audiofile text="It looks like your account is still active, so we can't apply an extension to it right now Our agents wouldn't be able to give you one either If you can't make your upcoming payment, call us back when your account gets suspended, and we'll check if you're eligible for an extension then " src="AX1815_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

  </dialog>
  