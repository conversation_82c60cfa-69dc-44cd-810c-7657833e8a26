<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="HighSpeedDataAvailable_Main_Dialog">
    <decision-state id="DA1001_CheckContext_DS">
      <session-mapping key="GlobalVars.eligibleForDataAddons" value="false" type="Boolean"/>
      <action next="DA1005_CheckIntent_JDA"/>
    </decision-state>

    <decision-state id="DA1010_CheckHighSpeedData_DS">
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <if cond="GlobalVars.GetAccountDetails.hasHighSpeedData == 'FromRatePlan' || GlobalVars.GetAccountDetails.hasHighSpeedData == 'FromFeature'">
        <action next="DA1015_CheckNeedPIN_JDA"/>
        <else>
          <action next="DA1105_NotifyNoHighSpeedData_PP"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="DA1015_CheckNeedPIN_DS">
      <if cond="(GlobalVars.loggedIn == true) ||(GlobalVars.aniMatch == true &amp;&amp; (GlobalVars.tag == 'hear-data_usage' || GlobalVars.callType == 'dataUsage'))">
        <action next="DA1025_CheckIntent_JDA"/>
        <else>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <action next="DA1020_LoginPIN_SD"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="DA1005_CheckIntent_DS">
      <if cond="GlobalVars.tag == 'hear-data_usage' || GlobalVars.callType == 'dataUsage'">
        <action next="DA1010_CheckHighSpeedData_JDA"/>
        <else>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <action next="DA1015_CheckNeedPIN_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="DA1025_CheckIntent_DS">
      <if cond="GlobalVars.callType == 'add_data' || GlobalVars.tag=='buy-data_topup'">
        <action next="DA1305_GetAvailableFeatureOffers_DB_DA"/>
        <else>
          <action next="DA1205_RouteToIntent_JDA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="DA1305_GetAvailableFeatureOffers_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="ratePlan" value="GlobalVars.GetAccountDetails.ratePlan" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <data-access id="GetAvailableFeatureOffers" classname="com.nuance.metro.dataaccess.GetAvailableFeatureOffers">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="JWTToken"/>
          <input-variable name="ratePlan"/>
          <input-variable name="sessionId"/>
          <input-variable name="languageCode"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="message"/>
          <output-variable name="features"/>
          <output-variable name="featureSocs"/>
          <output-variable name="featureSocsThirdParty"/>
          <output-variable name="featureActionMap"/>
          <output-variable name="unfilteredFeaturesList"/>
          <output-variable name="futureDate"/>
          <output-variable name="FeatureGrammarURL"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetAvailableFeatureOffers.status == 'Success'">
          <session-mapping key="GlobalVars.GetAvailableFeatureOffers" expr="GetAvailableFeatureOffers"/>
          <session-mapping key="GlobalVars.futureDate" expr="GetAvailableFeatureOffers.futureDate"/>
          <session-mapping key="GlobalVars.FeatureGrammarURL" expr="GetAvailableFeatureOffers.FeatureGrammarURL"/>
          <session-mapping key="GlobalVars.features" expr="GetAvailableFeatureOffers.features"/>
          <session-mapping key="GlobalVars.eligibleForDataAddons" expr="GetAvailableFeatureOffers.eligibleForDataAddons"/>
          <action next="DA1310_CheckAvailableOptions_JDA"/>
          <else>
            <action next="DA1115_CallTransfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="DA1310_CheckAvailableOptions_DS">
      <session-mapping key="supportedPlanIndicator" value="GlobalVars.GetAccountDetails.isSupported" type="String"/>
      <session-mapping key="topupEligibility" value="GlobalVars.GetAccountDetails.topupEligibility" type="String"/>
      <session-mapping key="eligibleForDataAddons" value="GlobalVars.eligibleForDataAddons" type="String"/>
      <session-mapping key="isDataUnlimited" value="GlobalVars.GetAccountDetails.isDataUnlimited != undefined ? GlobalVars.GetAccountDetails.isDataUnlimited : false" type="String"/>
      <session-mapping key="GlobalVars.eligibleForDataAddons" expr="eligibleForDataAddons"/>
      <if cond="(topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == true) || (supportedPlanIndicator == 'false' || supportedPlanIndicator == false)">
        <action next="DA1115_CallTransfer_SD"/>
        <elseif cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == true">
          <action next="DA1320_TransitionTopup_PP"/>
        </elseif>
        <elseif cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == true">
          <action next="DA1325_TransitionAddon_PP"/>
        </elseif>
        <elseif cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == false">
          <action next="DA1330_TransitionPlan_PP"/>
        </elseif>
        <else>
          <action next="DA1314_ExplainDataOptions_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="DA1314_ExplainDataOptions_PP">
      <session-mapping key="topupEligibility" value="GlobalVars.GetAccountDetails.topupEligibility" type="String"/>
      <session-mapping key="eligibleForDataAddons" value="GlobalVars.eligibleForDataAddons" type="String"/>
      <session-mapping key="isDataUnlimited" value="GlobalVars.GetAccountDetails.isDataUnlimited != undefined ? GlobalVars.GetAccountDetails.isDataUnlimited  : false" type="String"/>
      <audio>
        <prompt id="DA1314_out_01">
          <prompt-segments>
            <audiofile text="There's a few ways we can keep you connected!" src="DA1314_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DA1314_out_02" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == true">
          <prompt-segments>
            <audiofile text="A one-time top-up gives you more data until your next due date, but doesnt renew If you want more data each month, you can add a monthly data service So " src="DA1314_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DA1314_out_03" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == false">
          <prompt-segments>
            <audiofile text="A one-time top-up gives you more data until your next due date, but doesn't renew If you want more data *each month*, you can get on a plan with more included data So " src="DA1314_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DA1314_out_04" cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
          <prompt-segments>
            <audiofile text="For more data every month, you can add a monthly data service, or get on a plan with more included data So " src="DA1314_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DA1314_out_05" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
          <prompt-segments>
            <audiofile text="A one-time top-up gives you more data until your next due date, but doesn't renew If you want more data *each month*, you can add a monthly data service, or get on a plan with more included data So " src="DA1314_out_05.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="DA1315_OfferDataOptions_DM"/>
    </play-state>

    <dm-state id="DA1315_OfferDataOptions_DM" type="CUST">
      <session-mapping key="topupEligibility" value="GlobalVars.GetAccountDetails.topupEligibility" type="String"/>
      <session-mapping key="eligibleForDataAddons" value="GlobalVars.eligibleForDataAddons" type="String"/>
      <session-mapping key="isDataUnlimited" value="GlobalVars.GetAccountDetails.isDataUnlimited != undefined ? GlobalVars.GetAccountDetails.isDataUnlimited  : false" type="String"/>
      <session-mapping key="collection_dtmfgrammar1" value="DA1315_OfferDataOptions_DM_dtmf.jsp" type="String"/>
      <session-mapping key="collection_grammar1" value="DA1315_OfferDataOptions_DM.jsp" type="String"/>
      <success>
        <action label="buy-data_topup" next="DA1205_RouteToIntent_JDA">
          <session-mapping key="GlobalVars.tag" expr="'buy-data_topup'"/>
          <session-mapping key="GlobalVars.callType" expr="'dataTopup'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="add-feature" next="DA1205_RouteToIntent_JDA">
          <session-mapping key="GlobalVars.tag" expr="'add-feature_active'"/>
          <session-mapping key="GlobalVars.callType" expr="'add_feature'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="change-plan" next="DA1205_RouteToIntent_JDA">
          <session-mapping key="GlobalVars.tag" expr="'change-plan'"/>
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DA1315_ini_01" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == true">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up, or a monthly add-on? " src="DA1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_02" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up, or a different plan? " src="DA1315_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_03" cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A monthly add-on, or a different plan? " src="DA1315_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_04" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up a monthly add-on or  a different plan? " src="DA1315_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="DA1315_OfferDataOptions_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DA1315_ini_01" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == true">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up, or a monthly add-on? " src="DA1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_02" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up, or a different plan? " src="DA1315_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_03" cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A monthly add-on, or a different plan? " src="DA1315_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_04" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up a monthly add-on or  a different plan? " src="DA1315_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DA1315_ini_01" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == true">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up, or a monthly add-on? " src="DA1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_02" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up, or a different plan? " src="DA1315_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_03" cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A monthly add-on, or a different plan? " src="DA1315_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_04" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up a monthly add-on or  a different plan? " src="DA1315_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_01" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == true">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, or monthly add-on or press 2 " src="DA1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_02" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, or a different plan or press 2" src="DA1315_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_03" cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say 'monthly add-on or press 1, or a different plan or press 2 " src="DA1315_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_04" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, a monthly add-on or press 2, or  a different plan - 3" src="DA1315_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_01" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == true">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, or monthly add-on or press 2 " src="DA1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_02" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, or a different plan or press 2" src="DA1315_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_03" cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say 'monthly add-on or press 1, or a different plan or press 2 " src="DA1315_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_04" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, a monthly add-on or press 2, or  a different plan - 3" src="DA1315_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_01" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == true">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up, or a monthly add-on? " src="DA1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_02" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up, or a different plan? " src="DA1315_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_03" cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A monthly add-on, or a different plan? " src="DA1315_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_04" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up a monthly add-on or  a different plan? " src="DA1315_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_01" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == true">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, or monthly add-on or press 2 " src="DA1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_02" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, or a different plan or press 2" src="DA1315_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_03" cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say 'monthly add-on or press 1, or a different plan or press 2 " src="DA1315_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_04" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, a monthly add-on or press 2, or  a different plan - 3" src="DA1315_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_01" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == true">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, or monthly add-on or press 2 " src="DA1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_02" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, or a different plan or press 2" src="DA1315_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_03" cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say 'monthly add-on or press 1, or a different plan or press 2 " src="DA1315_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_nm2_04" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Please say one-time top-up or press 1, a monthly add-on or press 2, or  a different plan - 3" src="DA1315_nm2_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DA1315_ini_01" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == true">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up, or a monthly add-on? " src="DA1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_02" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == false  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up, or a different plan? " src="DA1315_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_03" cond="topupEligibility == 'NOT-ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A monthly add-on, or a different plan? " src="DA1315_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DA1315_ini_04" cond="topupEligibility == 'ELIGIBLE' &amp;&amp; eligibleForDataAddons == true  &amp;&amp; isDataUnlimited == false">
                <prompt-segments>
                  <audiofile text="Which would you like? A one-time top-up a monthly add-on or  a different plan? " src="DA1315_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="DA1315_OfferDataOptions_DM.jsp" count="1">
	  </grammars>
          <dtmfgrammars filename="DA1315_OfferDataOptions_DM_dtmf.jsp" count="1">
      </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="false" filename="" text="" id="DA1315_OfferDataOptions_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="DA1320_TransitionTopup_PP">
      <audio>
        <prompt id="DA1320_out_01">
          <prompt-segments>
            <audiofile text="The one option I see for you is a one-time data top-up " src="DA1320_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.tag" expr="'buy-data_topup'"/>
      <session-mapping key="GlobalVars.callType" expr="'dataTopup'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <action next="DA1205_RouteToIntent_JDA"/>
    </play-state>

    <play-state id="DA1325_TransitionAddon_PP">
      <audio>
        <prompt id="DA1325_out_01">
          <prompt-segments>
            <audiofile text="The option I see for you is to add a monthly data service " src="DA1325_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.tag" expr="'add-feature_active'"/>
      <session-mapping key="GlobalVars.callType" expr="'add_feature'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <action next="DA1335_AskContinueYN_DM"/>
    </play-state>

    <play-state id="DA1330_TransitionPlan_PP">
      <audio>
        <prompt id="DA1330_out_01">
          <prompt-segments>
            <audiofile text="The option I see for you is to change to a plan with more data " src="DA1330_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.tag" expr="'change-plan'"/>
      <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <action next="DA1335_AskContinueYN_DM"/>
    </play-state>

    <subdialog-state id="DA1020_LoginPIN_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="DA1020_LoginPIN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DA1020_LoginPIN_SD_return_CS">
      <action next="DA1025_CheckIntent_JDA"/>
    </custom-state>

    <play-state id="DA1105_NotifyNoHighSpeedData_PP">
      <audio>
        <prompt id="DA1105_out_01">
          <prompt-segments>
            <audiofile text="Actually, it looks like your current plan doesn t come with *any* high-speed data" src="DA1105_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="DA1110_OfferGetDataPlanYN_DM"/>
    </play-state>

    <dm-state id="DA1110_OfferGetDataPlanYN_DM" type="YSNO">
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="topupEligibility" value="GlobalVars.GetAccountDetails.topupEligibility" type="String"/>
      <success>
        <action label="true" next="DA1015_CheckNeedPIN_JDA">
          <session-mapping key="GlobalVars.tag" expr="'change-plan'"/>
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="false" next="DA1115_CallTransfer_SD">
          <session-mapping key="GlobalVars.callType" expr="'transfer'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="repeat" next="DA1105_NotifyNoHighSpeedData_PP"/>
        <action label="operator"/>
        <action label="repeat" next="DA1105_NotifyNoHighSpeedData_PP"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="tag == 'buy-data_topup' &amp;&amp; topupEligibility == 'NOT-ELIGIBLE'">
                <prompt id="DA1110_ini_02">
                  <prompt-segments>
                    <audiofile text="Would you like to change to a different plan with more data ?" src="DA1110_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1110_ini_01">
                    <prompt-segments>
                      <audiofile text="Would you like to change to a plan with High Speed data?" src="DA1110_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="(tag == 'buy-data_topup') &amp;&amp; topupEligibility == 'NOT-ELIGIBLE'">
                <prompt id="DA1110_nm1_02">
                  <prompt-segments>
                    <audiofile text="Your current plan doesn't allow data top-ups So would you like to change to a different one ?" src="DA1110_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1110_nm1_01">
                    <prompt-segments>
                      <audiofile text="Your current plan doesn t have any high speed data So would you like to change to one that does?" src="DA1110_nm1_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(tag == 'buy-data_topup') &amp;&amp; topupEligibility == 'NOT-ELIGIBLE'">
                <prompt id="DA1110_nm2_02">
                  <prompt-segments>
                    <audiofile text="Would you like to change to a plan with more data? Say 'yes' or press 1, or say 'no' or press 2" src="DA1110_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1110_nm2_01">
                    <prompt-segments>
                      <audiofile text="Would you like to change to a plan with High Speed data? Say  yes  or press 1, or say  no  or press 2" src="DA1110_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(tag == 'buy-data_topup') &amp;&amp; topupEligibility == 'NOT-ELIGIBLE'">
                <prompt id="DA1110_nm2_02">
                  <prompt-segments>
                    <audiofile text="Would you like to change to a plan with more data? Say 'yes' or press 1, or say 'no' or press 2" src="DA1110_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1110_nm2_01">
                    <prompt-segments>
                      <audiofile text="Would you like to change to a plan with High Speed data? Say  yes  or press 1, or say  no  or press 2" src="DA1110_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(tag == 'buy-data_topup') &amp;&amp; topupEligibility == 'NOT-ELIGIBLE'">
                <prompt id="DA1110_nm1_02">
                  <prompt-segments>
                    <audiofile text="Your current plan doesn't allow data top-ups So would you like to change to a different one ?" src="DA1110_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1110_nm1_01">
                    <prompt-segments>
                      <audiofile text="Your current plan doesn t have any high speed data So would you like to change to one that does?" src="DA1110_nm1_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(tag == 'buy-data_topup') &amp;&amp; topupEligibility == 'NOT-ELIGIBLE'">
                <prompt id="DA1110_nm2_02">
                  <prompt-segments>
                    <audiofile text="Would you like to change to a plan with more data? Say 'yes' or press 1, or say 'no' or press 2" src="DA1110_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1110_nm2_01">
                    <prompt-segments>
                      <audiofile text="Would you like to change to a plan with High Speed data? Say  yes  or press 1, or say  no  or press 2" src="DA1110_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(tag == 'buy-data_topup') &amp;&amp; topupEligibility == 'NOT-ELIGIBLE'">
                <prompt id="DA1110_nm2_02">
                  <prompt-segments>
                    <audiofile text="Would you like to change to a plan with more data? Say 'yes' or press 1, or say 'no' or press 2" src="DA1110_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1110_nm2_01">
                    <prompt-segments>
                      <audiofile text="Would you like to change to a plan with High Speed data? Say  yes  or press 1, or say  no  or press 2" src="DA1110_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="NLU_GlobalCommands.grxml" dtmfcommandgrammar="NLU_GlobalCommands_dtmf.grxml">
          <grammars filename="DA1110_OfferGetDataPlanYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="DA1110_OfferGetDataPlanYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1">
          <audio>
            <if cond="(collect.value.dm_root == 'true')">
              <prompt id="DA1110_out_01">
                <prompt-segments>
                  <audiofile text="Great!" src="DA1110_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <else>
                <prompt id="DA1110_out_02">
                  <prompt-segments>
                    <audiofile text="All right" src="DA1110_out_02.wav"/>
                  </prompt-segments>
                </prompt>
              </else>
            </if>
          </audio>
        </successprompts>
        <successprompts count="2">
          <audio>
            <if cond="(collect.value.dm_root == 'true')">
              <prompt id="DA1110_out_01">
                <prompt-segments>
                  <audiofile text="Great!" src="DA1110_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <else>
                <prompt id="DA1110_out_02">
                  <prompt-segments>
                    <audiofile text="All right" src="DA1110_out_02.wav"/>
                  </prompt-segments>
                </prompt>
              </else>
            </if>
          </audio>
        </successprompts>
        <successprompts count="3">
          <audio>
            <if cond="(collect.value.dm_root == 'true')">
              <prompt id="DA1110_out_01">
                <prompt-segments>
                  <audiofile text="Great!" src="DA1110_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <else>
                <prompt id="DA1110_out_02">
                  <prompt-segments>
                    <audiofile text="All right" src="DA1110_out_02.wav"/>
                  </prompt-segments>
                </prompt>
              </else>
            </if>
          </audio>
        </successprompts>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="DA1115_CallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="DA1115_CallTransfer_SD_return"/>
    </subdialog-state>
    <decision-state id="DA1205_RouteToIntent_DS">
      <if cond="(GlobalVars.tag == 'buy-data_topup')">
        <action next="DA1210_DataTopup_SD"/>
        <elseif cond="(GlobalVars.tag == 'hear-data_usage')">
          <action next="DA1215_DataUsage_SD"/>
        </elseif>
        <elseif cond="(GlobalVars.tag == 'add-feature_active')">
          <action next="DA1220_AddDataFeature_SD"/>
        </elseif>
        <else>
          <action next="DA1225_RatePlan_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="DA1220_AddDataFeature_SD">
      <gotodialog next="Voicestore_Main_Dialog"/>
      <action next="DA1220_AddDataFeature_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DA1220_AddDataFeature_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="DA1210_DataTopup_SD">
      <gotodialog next="DataTopUp_Main_Dialog"/>
      <action next="DA1210_DataTopup_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DA1210_DataTopup_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="DA1215_DataUsage_SD">
      <gotodialog next="DataUsage_Main_Dialog"/>
      <action next="DA1215_DataUsage_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DA1215_DataUsage_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="DA1225_RatePlan_SD">
      <gotodialog next="RatePlan_Main_Dialog"/>
      <action next="DA1225_RatePlan_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DA1225_RatePlan_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <dm-state id="DA1335_AskContinueYN_DM" type="YSNO">
      <session-mapping key="isDataUnlimited" value="GlobalVars.GetAccountDetails.isDataUnlimited != undefined ? GlobalVars.GetAccountDetails.isDataUnlimited : false" type="String"/>
      <success>
        <action label="true" next="DA1205_RouteToIntent_JDA">
          <audio>
            <prompt id="DA1335_out_01">
              <prompt-segments>
                <audiofile text="Great! " src="DA1335_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="false">
          <audio>
            <prompt id="DA1335_out_02">
              <prompt-segments>
                <audiofile text="No problem " src="DA1335_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3" maxturns="4"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DA1335_ini_01">
                <prompt-segments>
                  <audiofile text="Do you want to go ahead? " src="DA1335_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DA1335_ini_01">
                <prompt-segments>
                  <audiofile text="Do you want to go ahead? " src="DA1335_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="isDataUnlimited == true">
                <prompt id="DA1335_nm1_01">
                  <prompt-segments>
                    <audiofile text="Do you want to hear about our monthly data add-ons? " src="DA1335_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1335_nm1_02">
                    <prompt-segments>
                      <audiofile text="Do you want to hear about our other plan options? " src="DA1335_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isDataUnlimited == true">
                <prompt id="DA1335_nm2_01">
                  <prompt-segments>
                    <audiofile text="To hear about our monthly data add-ons, say 'yes' or press 1 To cancel, say 'no' or press 2 " src="DA1335_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1335_nm2_02">
                    <prompt-segments>
                      <audiofile text="To hear about our otehr rate plans, say 'yes' or press 1 To cancel, say 'no' or press 2 " src="DA1335_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isDataUnlimited == true">
                <prompt id="DA1335_nm2_01">
                  <prompt-segments>
                    <audiofile text="To hear about our monthly data add-ons, say 'yes' or press 1 To cancel, say 'no' or press 2 " src="DA1335_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1335_nm2_02">
                    <prompt-segments>
                      <audiofile text="To hear about our otehr rate plans, say 'yes' or press 1 To cancel, say 'no' or press 2 " src="DA1335_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isDataUnlimited == true">
                <prompt id="DA1335_nm1_01">
                  <prompt-segments>
                    <audiofile text="Do you want to hear about our monthly data add-ons? " src="DA1335_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1335_nm1_02">
                    <prompt-segments>
                      <audiofile text="Do you want to hear about our other plan options? " src="DA1335_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isDataUnlimited == true">
                <prompt id="DA1335_nm2_01">
                  <prompt-segments>
                    <audiofile text="To hear about our monthly data add-ons, say 'yes' or press 1 To cancel, say 'no' or press 2 " src="DA1335_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1335_nm2_02">
                    <prompt-segments>
                      <audiofile text="To hear about our otehr rate plans, say 'yes' or press 1 To cancel, say 'no' or press 2 " src="DA1335_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="isDataUnlimited == true">
                <prompt id="DA1335_nm2_01">
                  <prompt-segments>
                    <audiofile text="To hear about our monthly data add-ons, say 'yes' or press 1 To cancel, say 'no' or press 2 " src="DA1335_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DA1335_nm2_02">
                    <prompt-segments>
                      <audiofile text="To hear about our otehr rate plans, say 'yes' or press 1 To cancel, say 'no' or press 2 " src="DA1335_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DA1335_ini_01">
                <prompt-segments>
                  <audiofile text="Do you want to go ahead? " src="DA1335_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="DA1335_AskContinueYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="DA1335_AskContinueYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  