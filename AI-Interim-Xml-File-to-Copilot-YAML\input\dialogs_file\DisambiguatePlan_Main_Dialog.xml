<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="DisambiguatePlan_Main_Dialog">
    <dm-state id="DP1000_DisambiguatePlan_DM" type="CUST">
      <session-mapping key="allowedResponses" value="" type="String"/>
      <session-mapping key="planGrammarURL" value="GlobalVars.RatePlanUnambiguousGrammarURL" type="String"/>
      <session-mapping key="planGrammarDtmfURL" value="GlobalVars.RatePlanDTMFGrammarURL" type="String"/>
      <session-mapping key="availablePlansArray" value="GlobalVars.availablePlansArray" type="String"/>
      <session-mapping key="price" value="" type="String"/>
      <session-mapping key="availablePlansList" value="" type="String"/>
      <session-mapping key="numberOfAvailablePlans" value="" type="String"/>
      <session-mapping key="ratePlanSelectionType" value="GlobalVars.ratePlanSelectionType" type="String"/>
      <session-mapping key="ratePlans" value="GlobalVars.ratePlans" type="String"/>
      <session-mapping key="RatePlanUnambiguousGrammarURL" value="GlobalVars.RatePlanUnambiguousGrammarURL" type="String"/>
      <if cond="ratePlanSelectionType == 'price'">
        <session-mapping key="price" expr="GlobalVars.nbestresults[0].interpretation.dm_root"/>
        <else>
          <session-mapping key="price" expr="getPriceFromNBest(GlobalVars.nbestresults)"/>
        </else>
      </if>
      <if cond="GlobalVars.serviceDialed == '228'">
        <session-mapping key="availablePlansList" expr="getAllRatePlans(availablePlansArray)"/>
        <else>
          <if cond="GlobalVars.activationEntryPoint == 'care'">
            <session-mapping key="availablePlansList" expr="getAllRatePlans(availablePlansArray)"/>
            <else>
              <session-mapping key="availablePlansList" expr="getAllRatePlansExcludingCurrentPlan(availablePlansArray, GlobalVars.GetAccountDetails.ratePlan)"/>
            </else>
          </if>
        </else>
      </if>
      <session-mapping key="numberOfAvailablePlans" expr="numberOfRatePlans(availablePlansArray)"/>
      <success>
        <action label="choose-plan_none" next="return">
          <audio>
            <prompt id="DP1000_out_01">
              <prompt-segments>
                <audiofile text="That s all right" src="DP1000_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.disambigNoMatches" value="true" type="Boolean"/>
        </action>
        <action label="default" next="return">
          <session-mapping key="GlobalVars.disambigNoMatches" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.newPlanSOC" expr="DP1000_DisambiguatePlan_DM.returnvalue"/>
          <session-mapping key="GlobalVars.newRatePlan" expr="DP1000_DisambiguatePlan_DM.returnvalue"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="ratePlanSelectionType == 'price'">
                <prompt id="DP1000_ini_02">
                  <prompt-segments>
                    <audiofile text="I have a few plans at that price I'll read them out When you hear the one you want, say its full name back to me You can say" src="DP1000_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DP1000_ini_04">
                    <prompt-segments>
                      <audiofile text="I have a few plans that match that I'll read them out When you hear the one you want, say its full name back to me You can say" src="DP1000_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
              <prompt id="DP1000_ini_07">
                <prompt-segments>
                  <audiofile text="So, which of those plans do you want?" src="DP1000_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_ini_03" cond="numberOfAvailablePlans &gt; 2">
                <prompt-segments>
                  <audiofile text="You can also say, none of them" src="DP1000_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_ini_09" cond="numberOfAvailablePlans == 2">
                <prompt-segments>
                  <audiofile text="You can also say, neither of them" src="DP1000_ini_09.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="" text="" id="DP1000_DisambiguatePlan_DM_initial"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="ratePlanSelectionType == 'price'">
                <prompt id="DP1000_ini_02">
                  <prompt-segments>
                    <audiofile text="I have a few plans at that price I'll read them out When you hear the one you want, say its full name back to me You can say" src="DP1000_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DP1000_ini_04">
                    <prompt-segments>
                      <audiofile text="I have a few plans that match that I'll read them out When you hear the one you want, say its full name back to me You can say" src="DP1000_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
              <prompt id="DP1000_ini_07">
                <prompt-segments>
                  <audiofile text="So, which of those plans do you want?" src="DP1000_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_ini_03" cond="numberOfAvailablePlans &gt; 2">
                <prompt-segments>
                  <audiofile text="You can also say, none of them" src="DP1000_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_ini_09" cond="numberOfAvailablePlans == 2">
                <prompt-segments>
                  <audiofile text="You can also say, neither of them" src="DP1000_ini_09.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DP1000_nm1_01">
                <prompt-segments>
                  <audiofile text="Please choose one of the following plans - You can say" src="DP1000_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
              <prompt id="DP1000_nm1_04" cond="numberOfAvailablePlans &gt; 2">
                <prompt-segments>
                  <audiofile text="Or if none of those is right, say none of them" src="DP1000_nm1_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_nm1_02" cond="numberOfAvailablePlans == 2">
                <prompt-segments>
                  <audiofile text="Or if neither of those is right, say neither of them " src="DP1000_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_nm2_01">
                <prompt-segments>
                  <audiofile text="Say" src="DP1000_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndDtmf_DP1000"/>
                <param name="ratePlans" value="ratePlans" scope="request"/>
              </prompt>
              <prompt id="DP1000_nm1_05" cond="numberOfAvailablePlans &gt; 2">
                <prompt-segments>
                  <audiofile text="Or if none of these are right, say none of them or press 9" src="DP1000_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_nm2_01">
                <prompt-segments>
                  <audiofile text="Say" src="DP1000_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndDtmf_DP1000"/>
              </prompt>
              <prompt id="DP1000_nm1_05" cond="numberOfAvailablePlans &gt; 2">
                <prompt-segments>
                  <audiofile text="Or if none of these are right, say none of them or press 9" src="DP1000_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_nm2_02" cond="numberOfAvailablePlans == 2">
                <prompt-segments>
                  <audiofile text="Or again, if neither of those is right, say  neither of them  or press 9" src="DP1000_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_nm1_01">
                <prompt-segments>
                  <audiofile text="Please choose one of the following plans - You can say" src="DP1000_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
              <prompt id="DP1000_nm1_04" cond="numberOfAvailablePlans &gt; 2">
                <prompt-segments>
                  <audiofile text="Or if none of those is right, say none of them" src="DP1000_nm1_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_nm1_02" cond="numberOfAvailablePlans == 2">
                <prompt-segments>
                  <audiofile text="Or if neither of those is right, say neither of them " src="DP1000_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_nm2_01">
                <prompt-segments>
                  <audiofile text="Say" src="DP1000_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndDtmf_DP1000"/>
              </prompt>
              <prompt id="DP1000_nm1_05" cond="numberOfAvailablePlans &gt; 2">
                <prompt-segments>
                  <audiofile text="Or if none of these are right, say none of them or press 9" src="DP1000_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_nm2_02" cond="numberOfAvailablePlans == 2">
                <prompt-segments>
                  <audiofile text="Or again, if neither of those is right, say  neither of them  or press 9" src="DP1000_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_nm2_01">
                <prompt-segments>
                  <audiofile text="Say" src="DP1000_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndDtmf_DP1000"/>
              </prompt>
              <prompt id="DP1000_nm1_05" cond="numberOfAvailablePlans &gt; 2">
                <prompt-segments>
                  <audiofile text="Or if none of these are right, say none of them or press 9" src="DP1000_nm1_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_nm2_02" cond="numberOfAvailablePlans == 2">
                <prompt-segments>
                  <audiofile text="Or again, if neither of those is right, say  neither of them  or press 9" src="DP1000_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="ratePlanSelectionType == 'price'">
                <prompt id="DP1000_ini_02">
                  <prompt-segments>
                    <audiofile text="I have a few plans at that price I'll read them out When you hear the one you want, say its full name back to me You can say" src="DP1000_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DP1000_ini_04">
                    <prompt-segments>
                      <audiofile text="I have a few plans that match that I'll read them out When you hear the one you want, say its full name back to me You can say" src="DP1000_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanPromptURLOnly"/>
              </prompt>
              <prompt id="DP1000_ini_07">
                <prompt-segments>
                  <audiofile text="So, which of those plans do you want?" src="DP1000_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_ini_03" cond="numberOfAvailablePlans &gt; 2">
                <prompt-segments>
                  <audiofile text="You can also say, none of them" src="DP1000_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DP1000_ini_09" cond="numberOfAvailablePlans == 2">
                <prompt-segments>
                  <audiofile text="You can also say, neither of them" src="DP1000_ini_09.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="DP1000_DisambiguatePlan_DM.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="DP1000_DisambiguatePlan_DM_dtmf.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'none'">
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
                <else>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayPlanMedialPromptURL"/>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="DP1000_DisambiguatePlan_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'none'">
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
                <else>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayPlanMedialPromptURL"/>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'none'">
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
                <else>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayPlanMedialPromptURL"/>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  