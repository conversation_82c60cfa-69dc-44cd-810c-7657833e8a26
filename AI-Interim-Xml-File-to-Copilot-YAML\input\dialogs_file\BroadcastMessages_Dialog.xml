<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="BroadcastMessages_Dialog">
    <decision-state id="BM1005_CheckEntryConditions_DS">
      <if cond="GlobalVars.broadcastMessageKey == 'IH'">
        <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.care_initial_broadcastMessage"/>
        <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.care_initial_broadcastMessageAction"/>
        <session-mapping key="GlobalVars.careInitialBroadcastMessagePlayed" value="true" type="Boolean"/>
        <elseif cond="GlobalVars.broadcastMessageKey == 'MF'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.care_metroFlex_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.care_metroFlex_broadcastMessageAction"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'IHPrivacy'">
          <session-mapping key="GlobalVars.playedCCPA" value="true" type="Boolean"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'RP'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.care_ratePlan_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.care_ratePlan_broadcastMessageAction"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'MC'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.care_mdnChange_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.care_mdnChange_broadcastMessageAction"/>
          <session-mapping key="GlobalVars.careMdnChangeBroadcastMessagePlayed" value="true" type="Boolean"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'MP'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.care_makeAPayment_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.care_makeAPayment_broadcastMessageAction"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'VS'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.care_voiceStore_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.care_voiceStore_broadcastMessageAction"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'XR'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.care_transfer_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.care_transfer_broadcastMessageAction"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'DC'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.care_deviceChange_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.care_deviceChange_broadcastMessageAction"/>
        </elseif>
      </if>
      <if cond="GlobalVars.broadcastMessageKey == 'IH' &amp;&amp; GlobalVars.isMarketOutageFlag != false &amp;&amp; GlobalVars.marketId != null">
        <session-mapping key="broadcastMessage" expr="GlobalVars.marketOutageType"/>
        <session-mapping key="broadcastMessageAction" expr="GlobalVars.marketOutageAction"/>
        <session-mapping key="GlobalVars.MarketOutagemessagePlayed_IH" value="true" type="Boolean"/>
        <action next="BM1007_IHMarketOutageMsg_PP"/>
        <elseif cond="GlobalVars.broadcastMessageKey == 'MF'">
          <session-mapping key="GlobalVars.messagePlayed_MF" value="true" type="Boolean"/>
          <action next="BM1060_MFBroadcastMsg_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'IH' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_IH" value="true" type="Boolean"/>
          <action next="BM1010_IHBroadcastMsg_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'IHPrivacy'">
          <action next="BM1031_StateSpcificPrivacyMessage_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'RP' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_RP" value="true" type="Boolean"/>
          <action next="BM1015_RPBroadcastMsg_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'MC' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_MC" value="true" type="Boolean"/>
          <action next="BM1050_MdnChange_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'MP' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_MP" value="true" type="Boolean"/>
          <action next="BM1020_MPBroadcastMsg_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'VS' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_VS" value="true" type="Boolean"/>
          <action next="BM1025_VSBroadcastMsg_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'XR' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_XR" value="true" type="Boolean"/>
          <action next="BM1030_XRBroadcastMsg_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'DC' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_ES" value="true" type="Boolean"/>
          <action next="BM1055BrodcastMsg_PP"/>
        </elseif>
        <else>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </decision-state>

    <play-state id="BM1007_IHMarketOutageMsg_PP">
      <session-mapping key="broadcastMessage" value="GlobalVars.marketOutageType" type="String"/>
      <audio>
        <prompt type="custom" expr="broadcastMessage">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayMarketOutageMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM1035_CheckMessageActions_JDA"/>
    </play-state>

    <play-state id="BM1010_IHBroadcastMsg_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt id="BM1010_out_01" cond="broadcastMessage == 'Predefined Transfer Message'">
          <prompt-segments>
            <audiofile text="Actually, we re having a problem At the moment, I can t give you any information about your account or make any changes to it" src="BM1010_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="BM1010_out_02" cond="broadcastMessage == 'Predefined Goodbye Message'">
          <prompt-segments>
            <audiofile text="Hmm, we seem to be having some problems accessing accounts at the moment It may take a few hours for the problems to be resolved Sorry about that " src="BM1010_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <if type="java">
          <condition classname="com.nuance.metro.sentence.conditionals.IfMessageIsOutageByMarketMessage">
            <param name="message" value="broadcastMessage" scope="request"/>
          </condition>
          <prompt type="custom" expr="broadcastMessage">
            <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayMarketOutageMessage"/>
          </prompt>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM1035_CheckMessageActions_JDA"/>
    </play-state>

    <play-state id="BM1031_StateSpcificPrivacyMessage_PP">
      <session-mapping key="sbpStateCode" value="GlobalVars.GetAccountDetails ? GlobalVars.GetAccountDetails.sbpStateCode :GlobalVars.sbpStateCode" type="String"/>
      <session-mapping key="broadcastMessageKey" value="" type="String"/>
      <audio>
        <prompt type="custom" expr="broadcastMessageKey">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="BM1015_RPBroadcastMsg_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt id="BM1015_out_01" cond="broadcastMessage == 'Predefined Transfer Message'">
          <prompt-segments>
            <audiofile text="What feature are you calling about? You can say things like Unlimited Music from Rhapsody, Metro411 Unlimited Directory Assistance, Ringtones Bundle, Voicemail-to-text, Ringback Tones Bundle, Metro Backup, Metro Block-it, or Unlimited Mexico " src="BM1015_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM1035_CheckMessageActions_JDA"/>
    </play-state>

    <play-state id="BM1020_MPBroadcastMsg_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt id="BM1020_out_01" cond="broadcastMessage == 'Predefined Transfer Message'">
          <prompt-segments>
            <audiofile text="Actually, I m having trouble accessing your payment information" src="BM1020_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM1035_CheckMessageActions_JDA"/>
    </play-state>

    <play-state id="BM1025_VSBroadcastMsg_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt id="BM1025_out_01" cond="broadcastMessage == 'Predefined Transfer Message'">
          <prompt-segments>
            <audiofile text="Actually, I m having trouble accessing information about your account" src="BM1025_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM1035_CheckMessageActions_JDA"/>
    </play-state>

    <play-state id="BM1030_XRBroadcastMsg_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt id="BM1030_out_01" cond="broadcastMessage == 'Predefined Goodbye Message'">
          <prompt-segments>
            <audiofile text="Actually, we re experiencing some problems in our call center We re working to resolve them as quickly as possible, but it looks like it may take a few hours Sorry about that " src="BM1030_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM1035_CheckMessageActions_JDA"/>
    </play-state>

    <decision-state id="BM1035_CheckMessageActions_DS">
      <if cond="broadcastMessageAction != null &amp;&amp; broadcastMessageAction != ''">
        <session-mapping key="broadcastMessageAction" expr="broadcastMessageAction.toLowerCase()"/>
      </if>
      <if cond="broadcastMessageAction == 'transfer'">
        <if cond="GlobalVars.broadcastMessageKey == 'XR'">
          <action next="getReturnLink()"/>
        </if>
        <action next="BM1070_GoToTransfer_SD"/>
        <elseif cond="broadcastMessageAction == 'disconnect'">
          <action next="BM1075_GoToGoodbye_SD"/>
        </elseif>
        <else>
          <if cond="GlobalVars.broadcastMessageKey == 'XR'">
            <session-mapping key="GlobalVars.skipBroadcastMessage" value="true" type="Boolean"/>
            <action next="getReturnLink()"/>
            <else>
              <action next="BM1040_PlayReturningMessages_PP"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="BM1040_PlayReturningMessages_PP">
      <session-mapping key="playReturningMessage" value="false" type="Boolean"/>
      <session-mapping key="MarketOutageplayReturningMessage" value="false" type="Boolean"/>
      <if cond="(GlobalVars.broadcastMessageKey == 'IH' &amp;&amp; GlobalVars.messagePlayed_IH == true) || (GlobalVars.broadcastMessageKey == 'RP' &amp;&amp; GlobalVars.messagePlayed_RP == true) || (GlobalVars.broadcastMessageKey == 'MP' &amp;&amp; GlobalVars.messagePlayed_MP == true) || (GlobalVars.broadcastMessageKey == 'VS' &amp;&amp; GlobalVars.messagePlayed_VS == true) || (GlobalVars.broadcastMessageKey == 'MC' &amp;&amp; GlobalVars.messagePlayed_MC == true)">
        <session-mapping key="playReturningMessage" value="true" type="Boolean"/>
        <elseif cond="GlobalVars.broadcastMessageKey == 'IH' &amp;&amp; GlobalVars.MarketOutagemessagePlayed_IH == true">
          <session-mapping key="MarketOutageplayReturningMessage" value="true" type="Boolean"/>
        </elseif>
      </if>
      <audio>
        <if cond="MarketOutageplayReturningMessage == true">
          <prompt id="silence_500ms">
            <prompt-segments>
              <audiofile text="test" src="silence_500ms.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <if cond="playReturningMessage == true">
          <prompt id="silence_500ms">
            <prompt-segments>
              <audiofile text="test" src="silence_500ms.wav"/>
            </prompt-segments>
          </prompt>
        </if>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="BM1055BrodcastMsg_PP">
      <session-mapping key="broadcastMessage" value="GlobalVars.GetBCSParameters.care_deviceChange_broadcastMessage" type="String"/>
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt id="BM1055BrodcastMsg_PP"/>
      </audio>
      <action next="BM1035_CheckMessageActions_JDA"/>
    </play-state>

    <play-state id="BM1050_MdnChange_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt id="BM1050_out_01" cond="broadcastMessage == 'Predefined Transfer Message'">
          <prompt-segments>
            <audiofile text="Actually,  I can t access or update your account at this time " src="BM1050_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM1035_CheckMessageActions_JDA"/>
    </play-state>

    <subdialog-state id="BM1070_GoToTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="BM1070_GoToTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BM1070_GoToTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="BM1075_GoToGoodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="BM1075_GoToGoodbye_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BM1075_GoToGoodbye_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <decision-state id="BM1110_CheckPlayMessage_DS">
      <if cond="GlobalVars.broadcastMessageKey == 'IH'">
        <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.star228_initial_broadcastMessage"/>
        <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.star228_initial_broadcastMessageAction"/>
        <elseif cond="GlobalVars.broadcastMessageKey == 'AV'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.star228_activations_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.star228_activations_broadcastMessageAction"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'AR'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.star228_activationsRatePlan_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.star228_activationsRatePlan_broadcastMessageAction"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'NP'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.star228_numberPortIn_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.star228_numberPortIn_broadcastMessageAction"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'EC'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.star228_esnChange_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.star228_esnChange_broadcastMessageAction"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'XR'">
          <session-mapping key="broadcastMessage" expr="GlobalVars.GetBCSParameters.star228_transfer_broadcastMessage"/>
          <session-mapping key="broadcastMessageAction" expr="GlobalVars.GetBCSParameters.star228_transfer_broadcastMessageAction"/>
        </elseif>
      </if>
      <if cond="GlobalVars.broadcastMessageKey == 'IH' &amp;&amp; broadcastMessage == 'Custom'">
        <session-mapping key="GlobalVars.messagePlayed_IH" value="true" type="Boolean"/>
        <action next="BM1120_Initial_PP"/>
        <elseif cond="GlobalVars.broadcastMessageKey == 'AV' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_AV" value="true" type="Boolean"/>
          <action next="BM1130_Activations_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'AR' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_AR" value="true" type="Boolean"/>
          <action next="BM1140_ActivationsRatePlan_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'NP' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_NP" value="true" type="Boolean"/>
          <action next="BM1150_NumberPortIn_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'EC' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_EC" value="true" type="Boolean"/>
          <action next="BM1160_EsnChange_PP"/>
        </elseif>
        <elseif cond="GlobalVars.broadcastMessageKey == 'XR' &amp;&amp; broadcastMessage == 'Custom'">
          <session-mapping key="GlobalVars.messagePlayed_XR" value="true" type="Boolean"/>
          <action next="BM1190_Transfer_PP"/>
        </elseif>
        <else>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </decision-state>

    <play-state id="BM1120_Initial_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM5500_CheckAction_JDA"/>
    </play-state>

    <play-state id="BM1130_Activations_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM5500_CheckAction_JDA"/>
    </play-state>

    <play-state id="BM1140_ActivationsRatePlan_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM5500_CheckAction_JDA"/>
    </play-state>

    <play-state id="BM1150_NumberPortIn_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM5500_CheckAction_JDA"/>
    </play-state>

    <play-state id="BM1160_EsnChange_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM5500_CheckAction_JDA"/>
    </play-state>

    <play-state id="BM1190_Transfer_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="BM5500_CheckAction_JDA"/>
    </play-state>

    <decision-state id="BM5500_CheckAction_DS">
      <if cond="broadcastMessageAction != null &amp;&amp; broadcastMessageAction != ''">
        <session-mapping key="broadcastMessageAction" expr="broadcastMessageAction.toLowerCase()"/>
      </if>
      <if cond="broadcastMessageAction == 'transfer'">
        <if cond="GlobalVars.broadcastMessageKey == 'XR'">
          <action next="getReturnLink()"/>
        </if>
        <action next="BM5600_GoToCallTransfer_SD"/>
        <elseif cond="broadcastMessageAction == 'disconnect'">
          <action next="BM5700_GoToGoodbyeHandling_SD"/>
        </elseif>
        <else>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="BM5600_GoToCallTransfer_SD">
      <gotodialog next="Transfer_Main#CT9910_RequestedAgent_DS"/>
      <action next="BM5600_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BM5600_GoToCallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="BM5700_GoToGoodbyeHandling_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="BM5700_GoToGoodbyeHandling_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BM5700_GoToGoodbyeHandling_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <dm-state id="BM1080_MetroFlexWrap_DM" type="CUST">
      <success>
        <action label="main-menu">
          <session-mapping key="GlobalVars.tag" expr="undefined"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="repeat">
          <action next="BM1060_MFBroadcastMsg_PP"/>
        </action>
        <action label="send-text_metroflex">
          <action next="BM1081_SendMetroFlexSMS_DB_DA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BM1080_ini_01">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that'  Or say' Main Menu'  If you'd like to receive a link with more information say 'send me a text'" src="BM1080_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id="BM1080_MetroFlexWrap_DM_initial"/>
          <helpprompts count="2" bargein="true" filename="" text="" id="BM1080_MetroFlexWrap_DM_initial"/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BM1080_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say 'repeat that', ' Main Menu'  Or, if you'd like to receive a link with more information say 'send me a text'" src="BM1080_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BM1080_Nomatch2_01">
                <prompt-segments>
                  <audiofile text="To hear the information again press 1  For the main menu, press 2  To receive a text press 3" src="BM1080_Nomatch2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BM1080_MetroFlexWrap_DM_noinput_3"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BM1080_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say 'repeat that', ' Main Menu'  Or, if you'd like to receive a link with more information say 'send me a text'" src="BM1080_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BM1080_Nomatch2_01">
                <prompt-segments>
                  <audiofile text="To hear the information again press 1  For the main menu, press 2  To receive a text press 3" src="BM1080_Nomatch2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="BM1080_MetroFlexWrap_DM_nomatch_3"/>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BM1080_ini_01">
                <prompt-segments>
                  <audiofile text="To hear that again say 'repeat that'  Or say' Main Menu'  If you'd like to receive a link with more information say 'send me a text'" src="BM1080_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="BM1080_MetroFlexWrap_DM.grxml" count="1"/>
          <dtmfgrammars filename="BM1080_MetroFlexWrap_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="2" maxnomatches="2" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="BM1080_MetroFlexWrap_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BM1080_MetroFlexWrap_DM_cnf_nomatch_1"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="BM1080_MetroFlexWrap_DM_cnf_nomatch_1"/>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="BM1081_SendMetroFlexSMS_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="campaignId" value="GlobalVars.GetBCSParameters.metroFlex_campaign_id" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <data-access id="SendTextMessage" classname="com.nuance.metro.dataaccess.SendTextMessage">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="mdn"/>
          <input-variable name="campaignId"/>
          <input-variable name="languageCode"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.SendTextMessage" expr="SendTextMessage"/>
        <if cond="GlobalVars.SendTextMessage.status.toUpperCase() == 'SUCCESS'">
          <session-mapping key="GlobalVars.MetroFlexSMSSuccess" value="true" type="Boolean"/>
        </if>
        <gotodialog next="BroadcastMessages#BM1083_MetroFlexSMSStatus_PP"/>
      </action>
    </data-access-state>

    <play-state id="BM1083_MetroFlexSMSStatus_PP">
      <if cond="GlobalVars.MetroFlexSMSSuccess != true">
        <audio>
          <prompt id="BM1083_out_01">
            <prompt-segments>
              <audiofile text="Sorry, I wasn't able to sent the text" src="BM1083_out_01.wav"/>
            </prompt-segments>
          </prompt>
        </audio>
        <action next="BM1070_GoToTransfer_SD"/>
        <else>
          <audio>
            <prompt id="BM1083_out_02">
              <prompt-segments>
                <audiofile text="Ok, your text has been sent" src="BM1083_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </play-state>

    <play-state id="BM1060_MFBroadcastMsg_PP">
      <session-mapping key="broadcastMessageKey" value="broadcastMessageKeyApplication + GlobalVars.broadcastMessageKey" type="String"/>
      <audio>
        <prompt type="custom" expr="broadcastMessageKey">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
      </audio>
      <action next="BM1080_MetroFlexWrap_DM"/>
    </play-state>

  </dialog>
  