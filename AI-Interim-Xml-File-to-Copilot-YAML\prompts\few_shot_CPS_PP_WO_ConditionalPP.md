memorize below yaml format
- kind: SendActivity
  id: Play_Welcome_PP
  activity:
    text:
      - Thank you for using our service. Have a great day!
    speak:
      - Thank you for using our service. Have a great day!
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: Play_name_PP
      


prefix = topic 

also use these instruction while conversion 
1) kind should always be "SendActivity"  
2) replace "id" value with "play-state" tag id.
3) replace "text" and "speak" value with audiofile tag's "text" field value.
4) If the "next" value of "action" tag ends with _DM, _DS, _DA, _DB or _PP, output this format:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0410_CleanUpNeeded_DS

actionId should be next id.
Replace "GotoAction" id with "goto_REPLACE_THIS".

Input:
<action next="rb0520_CollectVerificationPhrase_DM" />
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0520_CollectVerificationPhrase_DM

5) If the next value ends with _Dialog, output this format:
- kind: BeginDialog
  id: begin_REPLACE_THIS
  dialog: topic.enroll_Dialog

"dialog" should be next value with prefix.
Replace "BeginDialog" id with "begin_REPLACE_THIS".

Example Input:
<action label="retry" next="rb0310_Enrollment_DM">
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0310_Enrollment_DM

Output should be generated based on the next value alone, without considering the label.
6) "actionId" of "GotoAction" should be "next" id of "action" tag from input.
7) if there is no "text" in the input. replace text with "custom text from java class" like below
    text:
      - custom text from java class 
    speak:
      - custom text from java class 
8) if there is <if type="java"> in any input, just create below yaml like example 3
- kind: SendActivity
  id: sv3020_UsageInfo_PP
  activity:
    text:
      - custom text from java class
    speak:
      - custom text from java class

- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: sv3025_HearUsageInfoAgain_DM

  actionId should be next value.
7) indentation for next yaml should be same as first one or as memorized yaml
8) do not provide duplicate yaml.. generate only for provided inputs.

### Example 1:
**Input XML:**
```xml
<play-state id="Play_Welcome_PP">
<script className="com.nuance.ps.telefonica.scripts.KPIAddNode" />
			<audio>
				<prompt id="play_welcome_01">
					<prompt-segments>
						<audiofile src="play_welcome_01.wav" text="Hello, Welcome to our AI Generated Voice App." />
					</prompt-segments>
				</prompt>
			</audio>
			<action next="Play_name_PP">
				<session-mapping key="IVR_SMSsent" value="true" />
			</action>
</play-state>

```

**Output yaml:**
```yaml
- kind: SendActivity
  id: Play_Welcome_PP
  activity:
    text:
      - Thank you for using our service. Have a great day!
    speak:
      - Thank you for using our service. Have a great day!

- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: Play_name_PP
```

### Example 2:
**Input XML:**
```xml
<play-state id="PlayForPune_PP">
			<audio>
				<if type="java">
					<prompt id="pune_pp">
						<prompt-segments>
							<audiofile src="pune_pp.wav" text="You have chosen Pune" />
						</prompt-segments>
					</prompt>
				</if>
			</audio>
			<action label="call_end_SD" next="EndofConversation" />
</play-state>
```

**Output yaml:**
```yaml
- kind: SendActivity
  id: PlayForPune_PP
  activity:
    text:
      - You have chosen Pune
    speak:
      - You have chosen Pune

- kind: BeginDialog
  id: begin_REPLACE_THIS
  dialog: topic.EndofConversation
```
### Example 3:
**Input XML:**
```xml
    <play-state id="sv3020_UsageInfo_PP">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode" />
			<audio><prompt type="custom" bargein="false">
  			<param name="className" value="com.nuance.ps.telefonica.prompts.UsageInfoPrompt" /></prompt></audio><action label="default" next="sv3025_HearUsageInfoAgain_DM">
				<script className="com.nuance.ps.telefonica.scripts.ReportMobileUsageInfo">
					<param name="STATUS" value="1" />
				</script>
        	</action>
		</play-state>
```  
**Output yaml:**
```yaml
- kind: SendActivity
  id: sv3020_UsageInfo_PP
  activity:
    text:
      - custom text from java class
    speak:
      - custom text from java class

- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: sv3025_HearUsageInfoAgain_DM