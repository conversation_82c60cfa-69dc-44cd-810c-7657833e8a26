<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="RatePlan_Change_Dialog">
    <dm-state id="RP1305_ConfirmRatePlan_DM" type="YSNO">
      <session-mapping key="plan" value="GlobalVars.selectedPlan" type="String"/>
      <session-mapping key="enteringFrom" value="GlobalVars.confirmingRatePlanFromState" type="String"/>
      <session-mapping key="ratePlans" value="GlobalVars.ratePlans" type="String"/>
      <success>
        <action label="true">
          <gotodialog next="RatePlan_Main#RP0301_CheckPlanOptions_DS"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="gl_thatsfine">
              <prompt-segments>
                <audiofile text="That s fine" src="gl_thatsfine.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="RP1335_GoToAnythingElse_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP1305_ini_01" cond="enteringFrom == 'RP1055_GoToDisambiguateRatePlan_SD_return'">
                <prompt-segments>
                  <audiofile text="Just to reconfirm, you want to switch to" src="RP1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1305_ini_02" cond="enteringFrom != 'RP1055_GoToDisambiguateRatePlan_SD_return'">
                <prompt-segments>
                  <audiofile text="Okay, you want to switch to" src="RP1305_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="RP1305_ini_12">
                <prompt-segments>
                  <audiofile text="Is that right?" src="RP1305_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="RP1305_ini_01" cond="enteringFrom == 'RP1055_GoToDisambiguateRatePlan_SD_return'">
                <prompt-segments>
                  <audiofile text="Just to reconfirm, you want to switch to" src="RP1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1305_ini_02" cond="enteringFrom != 'RP1055_GoToDisambiguateRatePlan_SD_return'">
                <prompt-segments>
                  <audiofile text="Okay, you want to switch to" src="RP1305_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="RP1305_ini_12">
                <prompt-segments>
                  <audiofile text="Is that right?" src="RP1305_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP1305_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no  - do you want to switch to that plan?" src="RP1305_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1305_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2" src="RP1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1305_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2" src="RP1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1305_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no  - do you want to switch to that plan?" src="RP1305_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1305_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2" src="RP1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1305_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like to switch to that plan, say  yes  or press 1 Otherwise, to stay on your current plan, say  no  or press 2" src="RP1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP1305_ini_01" cond="enteringFrom == 'RP1055_GoToDisambiguateRatePlan_SD_return'">
                <prompt-segments>
                  <audiofile text="Just to reconfirm, you want to switch to" src="RP1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1305_ini_02" cond="enteringFrom != 'RP1055_GoToDisambiguateRatePlan_SD_return'">
                <prompt-segments>
                  <audiofile text="Okay, you want to switch to" src="RP1305_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="plan">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansPlayPlanLongPromptURLOnly"/>
              </prompt>
              <prompt id="RP1305_ini_12">
                <prompt-segments>
                  <audiofile text="Is that right?" src="RP1305_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP1305_ConfirmRatePlan_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP1305_ConfirmRatePlan_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="RP1316_DetectConflictingOffers_SD">
      <gotodialog next="DetectConflictingOffer_Dialog"/>
      <action next="RP1316_DetectConflictingOffers_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="RP1316_DetectConflictingOffers_SD_return_CS">
      <if cond="(GlobalVars.cancelPlanChange == true)">
        <session-mapping key="GlobalVars.cancelPlanChange" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.callType" expr="undefined"/>
        <action next="RP1335_GoToAnythingElse_SD"/>
        <else>
          <action next="RP1319_PlaySubmitMessage_PP"/>
        </else>
      </if>
    </custom-state>

    <play-state id="RP1319_PlaySubmitMessage_PP">
      <audio>
        <prompt id="RP1320_out_01">
          <prompt-segments>
            <audiofile text="Okay" src="RP1320_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="RP1320_SubmitChangeOfferPlan_SD"/>
    </play-state>

    <subdialog-state id="RP1320_SubmitChangeOfferPlan_SD">
      <gotodialog next="SubmitChangeOffer_Dialog"/>
      <action next="RP1320_SubmitChangeOfferPlan_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="RP1320_SubmitChangeOfferPlan_SD_return_CS">
      <if cond="(GlobalVars.cancelPlanChange == true)">
        <session-mapping key="GlobalVars.cancelPlanChange" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.callType" expr="undefined"/>
        <action next="RP1335_GoToAnythingElse_SD"/>
        <else>
          <action next="RP1325_CheckPlanChangeSuccess_JDA"/>
        </else>
      </if>
    </custom-state>

    <decision-state id="RP1325_CheckPlanChangeSuccess_DS">
      <if cond="GlobalVars.suspendedRatePlanChange == true">
        <action next="RP1331_SuspendedChangePlanSuccess_PP"/>
        <else>
          <action next="RP1505_PlanChangeSuccess_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="RP1331_SuspendedChangePlanSuccess_PP">
      <audio>
        <prompt id="RP1331_out_01">
          <prompt-segments>
            <audiofile text="All set I ll send you a text message with your new monthly payment in a minute " src="RP1331_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.callType" expr="undefined"/>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="RP1505_PlanChangeSuccess_PP">
      <session-mapping key="futureChangeOffer" value="GlobalVars.futureChangeOffer" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="accountUpdateFailed" value="GlobalVars.accountUpdateFailed" type="String"/>
      <session-mapping key="futureDate" value="GlobalVars.futureDate" type="String"/>
      <audio>
        <prompt id="RP1505_out_01">
          <prompt-segments>
            <audiofile text="All set! " src="RP1505_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="futureChangeOffer == true">
          <prompt id="RP1505_out_02">
            <prompt-segments>
              <audiofile text="Your plan will change automatically on " src="RP1505_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <prompt type="date" expr="futureDate">
            <param name="dateFormat" value="yyyyMMdd"/>
            <param name="playDayOfMonth" value="true"/>
            <param name="playYear" value="false"/>
            <param name="playDayOfTheWeek" value="true"/>
            <param name="intonation" value="f"/>
          </prompt>
          <prompt id="RP1505_out_03">
            <prompt-segments>
              <audiofile text="Your next payment statement will also change to show that" src="RP1505_out_03.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="RP1505_out_04">
              <prompt-segments>
                <audiofile text="That change will be effective immediately I'll send you a text message with your new monthly payment in a little while" src="RP1505_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <if cond="((parseFloat(dueImmediatelyAmount) &gt; 3) &amp;&amp; (accountUpdateFailed == false))">
        <action next="RP1510_PayNowYN_DM"/>
        <else>
          <action next="RP1520_CheckNextStep_JDA"/>
        </else>
      </if>
    </play-state>

    <dm-state id="RP1510_PayNowYN_DM" type="YSNO">
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentAmount" expr="dueImmediatelyAmount"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'carePlan'"/>
          <action next="RP1515_MakePayment_SD"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="RP1510_out_01">
              <prompt-segments>
                <audiofile text="Okay, just don't forget to do it today to keep your service active!  " src="RP1510_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_750ms">
              <prompt-segments>
                <audiofile text="test" src="silence_750ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="RP1520_CheckNextStep_JDA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP1510_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay that " src="RP1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="m" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="RP1510_ini_02">
                <prompt-segments>
                  <audiofile text=" right now? " src="RP1510_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="RP1510_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay that " src="RP1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="m" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="RP1510_ini_02">
                <prompt-segments>
                  <audiofile text=" right now? " src="RP1510_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP1510_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay that " src="RP1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="m" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="RP1510_ini_02">
                <prompt-segments>
                  <audiofile text=" right now? " src="RP1510_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1510_nm2_01">
                <prompt-segments>
                  <audiofile text="You have a payment of " src="RP1510_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="m" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="RP1510_nm2_02">
                <prompt-segments>
                  <audiofile text="due right now Would you like to pay it now over the phone? Say 'yes' or press 1 or 'No' or press 2 " src="RP1510_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1510_nm2_01">
                <prompt-segments>
                  <audiofile text="You have a payment of " src="RP1510_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="m" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="RP1510_nm2_02">
                <prompt-segments>
                  <audiofile text="due right now Would you like to pay it now over the phone? Say 'yes' or press 1 or 'No' or press 2 " src="RP1510_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1510_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay that " src="RP1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="m" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="RP1510_ini_02">
                <prompt-segments>
                  <audiofile text=" right now? " src="RP1510_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1510_nm2_01">
                <prompt-segments>
                  <audiofile text="You have a payment of " src="RP1510_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="m" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="RP1510_nm2_02">
                <prompt-segments>
                  <audiofile text="due right now Would you like to pay it now over the phone? Say 'yes' or press 1 or 'No' or press 2 " src="RP1510_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1510_nm2_01">
                <prompt-segments>
                  <audiofile text="You have a payment of " src="RP1510_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="m" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="RP1510_nm2_02">
                <prompt-segments>
                  <audiofile text="due right now Would you like to pay it now over the phone? Say 'yes' or press 1 or 'No' or press 2 " src="RP1510_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP1510_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay that " src="RP1510_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="m" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="RP1510_ini_02">
                <prompt-segments>
                  <audiofile text=" right now? " src="RP1510_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP1510_PayNowYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP1510_PayNowYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="RP1515_MakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="RP1515_MakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="RP1515_MakePayment_SD_return_CS">
      <action next="RP1520_CheckNextStep_JDA"/>
    </custom-state>

    <decision-state id="RP1520_CheckNextStep_DS">
      <session-mapping key="care_nlu_enabled" value="GlobalVars.GetBCSParameters.care_nlu_enabled" type="String"/>
      <if cond="(GlobalVars.accountUpdateFailed == true)">
        <action next="RP1335_GoToAnythingElse_SD"/>
        <elseif cond="((care_nlu_enabled == true || care_nlu_enabled == 'true') &amp;&amp; (language == 'en-US'))">
          <action next="getReturnLink()"/>
        </elseif>
        <else>
          <action next="RP1335_GoToAnythingElse_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="RP1335_GoToAnythingElse_SD">
      <gotodialog next="AnythingElse_Main_Dialog"/>
      <action next="RP1335_GoToAnythingElse_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="RP1335_GoToAnythingElse_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <decision-state id="RP1401_CheckContext_DS">
      <if cond="(GlobalVars.ratePlans.length == 0)">
        <gotodialog next="RatePlan_Main#RP0510_PlayNoPlansAvailable_PP"/>
        <elseif cond="(GlobalVars.ratePlans.length == 1)">
          <action next="RP1411_OfferOnePlanYN_DM"/>
        </elseif>
        <else>
          <action next="RP1415_ListRatePlans_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="RP1411_OfferOnePlanYN_DM" type="CUST">
      <session-mapping key="ratePlans" value="GlobalVars.ratePlans" type="String"/>
      <session-mapping key="comingFrom" expr="'RP1411'"/>
      <session-mapping key="soc" expr="GlobalVars.ratePlans[0].soc"/>
      <success>
        <action label="yes">
          <session-mapping key="GlobalVars.selectedPlan" expr="soc"/>
          <session-mapping key="GlobalVars.isComingFromRp1411" value="true" type="Boolean"/>
          <gotodialog next="RatePlan_Main#RP0301_CheckPlanOptions_DS"/>
        </action>
        <action label="no">
          <action next="RP1425_NoOtherPlans_DM"/>
        </action>
        <action label="operator">
          <action next="RP1425_NoOtherPlans_DM"/>
        </action>
        <action label="repeat">
          <action next="RP1411_OfferOnePlanYN_DM"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP1411_ini_01">
                <prompt-segments>
                  <audiofile text="There s one plan I can offer you today  " src="RP1411_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlanAndFeatures_RP1411"/>
              </prompt>
              <prompt id="RP1411_ini_05">
                <prompt-segments>
                  <audiofile text="Do you want to switch to that plan " src="RP1411_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="RP1411_ini_01">
                <prompt-segments>
                  <audiofile text="There s one plan I can offer you today  " src="RP1411_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlanAndFeatures_RP1411"/>
              </prompt>
              <prompt id="RP1411_ini_05">
                <prompt-segments>
                  <audiofile text="Do you want to switch to that plan " src="RP1411_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP1411_nm1_01">
                <prompt-segments>
                  <audiofile text="Sorry, do you want to switch to that plan To hear about it again, say repeat  " src="RP1411_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1411_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to switch to that plan, say yes or press 1 Otherwise, say no or press 2 To hear about the plan again, say repeat or press star  " src="RP1411_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1411_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to switch to that plan, say yes or press 1 Otherwise, say no or press 2 To hear about the plan again, say repeat or press star  " src="RP1411_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="RP1411_nm1_01">
                <prompt-segments>
                  <audiofile text="Sorry, do you want to switch to that plan To hear about it again, say repeat  " src="RP1411_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1411_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to switch to that plan, say yes or press 1 Otherwise, say no or press 2 To hear about the plan again, say repeat or press star  " src="RP1411_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1411_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to switch to that plan, say yes or press 1 Otherwise, say no or press 2 To hear about the plan again, say repeat or press star  " src="RP1411_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP1411_ini_01">
                <prompt-segments>
                  <audiofile text="There s one plan I can offer you today  " src="RP1411_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlanAndFeatures_RP1411"/>
              </prompt>
              <prompt id="RP1411_ini_05">
                <prompt-segments>
                  <audiofile text="Do you want to switch to that plan " src="RP1411_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="RP1411_OfferOnePlanYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP1411_OfferOnePlanYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="extension == 'lastresult'">
                <prompt id="RP1411_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You want a payment extension " src="RP1411_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="RP1411_OfferOnePlanYN_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="RP1415_ListRatePlans_DM" type="CUST">
      <session-mapping key="FirstTimeRP1415" value="GlobalVars.FirstTimeRP1415" type="String"/>
      <session-mapping key="choseInvalidPlan" value="GlobalVars.choseInvalidPlan" type="String"/>
      <session-mapping key="declineChangeCounter" value="GlobalVars.declineChangeCounter" type="String"/>
      <session-mapping key="revisit" value="GlobalVars.revisit == undefined ? false : GlobalVars.revisit" type="String"/>
      <session-mapping key="plansList" value="" type="String"/>
      <session-mapping key="subPlansList" value="" type="String"/>
      <session-mapping key="finalList" value="" type="String"/>
      <session-mapping key="planType" expr="'Both'"/>
      <session-mapping key="reentry" expr="GlobalVars.RP1415reentry == undefined ? false : GlobalVars.RP1415reentry"/>
      <success>
        <session-mapping key="GlobalVars.FirstTimeRP1415" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.RP1415reentry" value="true" type="Boolean"/>
        <action label="dont_change">
          <audio>
            <prompt id="RP1415_out_01">
              <prompt-segments>
                <audiofile text="No problem" src="RP1415_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="RP1335_GoToAnythingElse_SD"/>
        </action>
        <action label="none_of_those">
          <session-mapping key="noneOfThoseCount" expr="noneOfThoseCount + 1"/>
          <session-mapping key="GlobalVars.revisit" value="true" type="Boolean"/>
          <if cond="noneOfThoseCount &lt; 2">
            <if cond="(GlobalVars.ratePlans.length &lt;= 6)">
              <gotodialog next="RatePlan_Main#RP1280_GoToCallTransfer_SD"/>
              <elseif cond="(GlobalVars.ratePlans.length == 7)">
                <audio>
                  <prompt id="RP1415_out_02">
                    <prompt-segments>
                      <audiofile text="Ok I have one more plan For " src="RP1415_out_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </audio>
                <action next="RP1415_ListRatePlans_DM"/>
              </elseif>
              <elseif cond="(GlobalVars.ratePlans.length &gt; 7)">
                <audio>
                  <prompt id="RP1415_out_03">
                    <prompt-segments>
                      <audiofile text="Ok, here are the last ones For " src="RP1415_out_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </audio>
                <action next="RP1415_ListRatePlans_DM"/>
              </elseif>
            </if>
            <else>
              <gotodialog next="RatePlan_Main#RP1280_GoToCallTransfer_SD"/>
            </else>
          </if>
        </action>
        <action label="main_menu">
          <action next="getReturnLink()"/>
        </action>
        <action label="start_over">
          <session-mapping key="GlobalVars.FirstTimeRP1415" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.RP1415reentry" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.revisit" value="false" type="Boolean"/>
          <action next="RP1415_ListRatePlans_DM"/>
        </action>
        <action label="default">
          <if cond="RP1415_ListRatePlans_DM.nbestresults != undefined">
            <session-mapping key="GlobalVars.ratePlanSelectionType" expr="RP1415_ListRatePlans_DM.nbestresults[0].interpretation.selectionType"/>
            <session-mapping key="GlobalVars.nbestresults" expr="RP1415_ListRatePlans_DM.nbestresults"/>
          </if>
          <session-mapping key="GlobalVars.selectedPlan" expr="RP1415_ListRatePlans_DM.returnvalue"/>
          <if cond="GlobalVars.ratePlanSelectionType != undefined &amp;&amp; GlobalVars.ratePlanSelectionType == 'dtmfOption'">
            <action next="RP1416_DescribePlanAskChange_DM"/>
          </if>
          <gotodialog next="RatePlan_GetPlan#RP1050_CheckDisambigNeeded_DS"/>
        </action>
        <action label="operator"/>
        <action label="repeat" next="RP1415_ListRatePlans_DM"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RP1415_ini_02" cond="FirstTimeRP1415 == true">
                <prompt-segments>
                  <audiofile text="To make sure all the features on your new plan will work on your phone, you can check out metrobyt-mobilecom" src="RP1415_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="FirstTimeRP1415 == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_ini_03" cond="FirstTimeRP1415 == true">
                <prompt-segments>
                  <audiofile text="These prices include all taxes and regulatory fees You'll get a chance to hear further plan details once you've made a selection" src="RP1415_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_ini_04" cond="FirstTimeRP1415 == true">
                <prompt-segments>
                  <audiofile text="Please choose the one you're interested " src="RP1415_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_ini_05" cond="FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1">
                <prompt-segments>
                  <audiofile text="Ok, please make another selection  or say 'dont change my plan'" src="RP1415_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_ini_06" cond="(FirstTimeRP1415 == true) || (FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1)">
                <prompt-segments>
                  <audiofile text="for " src="RP1415_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_rin_02" cond="reentry &amp;&amp; choseInvalidPlan == true &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP1415 == true) || (FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="Here are the plans we DO have " src="RP1415_rin_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_rin_01" cond="reentry &amp;&amp; choseInvalidPlan != true &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP1415 == true) || (FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="Ok, so choose the plan you may be interested in " src="RP1415_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="reentry &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP1415 == true) || (FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP1415"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_ini_14">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu'  " src="RP1415_ini_14.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="RP1415_ini_07">
                <prompt-segments>
                  <audiofile text="for " src="RP1415_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP1415"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_ini_14">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu'  " src="RP1415_ini_14.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP1415_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu'  " src="RP1415_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP1415"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="RP1415_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu'" src="RP1415_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP0520"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="RP1415_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu'" src="RP1415_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu'  " src="RP1415_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP1415"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="RP1415_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu'" src="RP1415_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP1415"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="RP1415_nm2_02">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu'" src="RP1415_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RP1415_ini_02" cond="FirstTimeRP1415 == true">
                <prompt-segments>
                  <audiofile text="To make sure all the features on your new plan will work on your phone, you can check out metrobyt-mobilecom" src="RP1415_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="FirstTimeRP1415 == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_ini_03" cond="FirstTimeRP1415 == true">
                <prompt-segments>
                  <audiofile text="These prices include all taxes and regulatory fees You'll get a chance to hear further plan details once you've made a selection" src="RP1415_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_ini_04" cond="FirstTimeRP1415 == true">
                <prompt-segments>
                  <audiofile text="Please choose the one you're interested " src="RP1415_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_ini_05" cond="FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1">
                <prompt-segments>
                  <audiofile text="Ok, please make another selection  or say 'dont change my plan'" src="RP1415_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_ini_06" cond="(FirstTimeRP1415 == true) || (FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1)">
                <prompt-segments>
                  <audiofile text="for " src="RP1415_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_rin_02" cond="reentry &amp;&amp; choseInvalidPlan == true &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP1415 == true) || (FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="Here are the plans we DO have " src="RP1415_rin_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_rin_01" cond="reentry &amp;&amp; choseInvalidPlan != true &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP1415 == true) || (FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="Ok, so choose the plan you may be interested in " src="RP1415_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="reentry &amp;&amp; noneOfThoseCount == 0 &amp;&amp; !((FirstTimeRP1415 == true) || (FirstTimeRP1415 == false  &amp;&amp; declineChangeCounter == 1))">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="availablePlansList">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailablePlansList_RP1415"/>
                <param name="revisit" value="revisit" scope="request"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1415_ini_14">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'none of those', or 'main menu'  " src="RP1415_ini_14.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP1415_ListRatePlans_DM.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="RP1415_ListRatePlans_DM_dtmf.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="RP1416_DescribePlanAskChange_DM" type="YSNO">
      <session-mapping key="audioMessageKey" value="care_configurable_rate_plan_downgrade_audio" type="String"/>
      <session-mapping key="soc" value="GlobalVars.selectedPlan" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.newRatePlan" expr="GlobalVars.selectedPlan"/>
          <gotodialog next="RatePlan_Main#RP0301_CheckPlanOptions_DS"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.declineChangeCounter" expr="GlobalVars.declineChangeCounter+1"/>
          <session-mapping key="GlobalVars.revisit" value="false" type="Boolean"/>
          <gotodialog next="RatePlan_Main#RP0326_CheckDeclineNextSteps_DS"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_RP1416"/>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="RP1416_ini_04">
                <prompt-segments>
                  <audiofile text="Would you like to switch to this plan? " src="RP1416_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_RP1416"/>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="RP1416_ini_04">
                <prompt-segments>
                  <audiofile text="Would you like to switch to this plan? " src="RP1416_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="RP1416_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to switch to this plan? " src="RP1416_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1416_nm2_01">
                <prompt-segments>
                  <audiofile text="To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 " src="RP1416_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1416_nm2_01">
                <prompt-segments>
                  <audiofile text="To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 " src="RP1416_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1416_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to switch to this plan? " src="RP1416_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1416_nm2_01">
                <prompt-segments>
                  <audiofile text="To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 " src="RP1416_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="RP1416_nm2_01">
                <prompt-segments>
                  <audiofile text="To switch to this plan say 'yes' or press 1  Or say 'no' or press 2 " src="RP1416_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt type="custom" expr="soc">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayRatePlansAndFeatures_RP1416"/>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
              <prompt id="RP1416_ini_04">
                <prompt-segments>
                  <audiofile text="Would you like to switch to this plan? " src="RP1416_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="RP1416_DescribePlanAskChange_DM.grxml" count="1"/>
          <dtmfgrammars filename="RP1416_DescribePlanAskChange_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="RP1425_NoOtherPlans_DM" type="CUST">
      <session-mapping key="extensionAllowed" value="GlobalVars.extensionAllowed == undefined ? false : GlobalVars.extensionAllowed" type="String"/>
      <session-mapping key="ratePlans" value="GlobalVars.ratePlans.length" type="String"/>
      <session-mapping key="heardDataExceededInfo" value="GlobalVars.heardDataExceededInfo" type="String"/>
      <success>
        <action label="request-extension">
          <audio>
            <prompt id="RP1425_out_01">
              <prompt-segments>
                <audiofile text="Sure !" src="RP1425_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'extension'"/>
          <session-mapping key="GlobalVars.tag" expr="'request-extension'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="go_back">
          <if cond="(comingFrom == 'RP1415')">
            <action next="RP1415_ListRatePlans_DM"/>
            <elseif cond="(comingFrom == 'RP1420')">
              <action next="RP1420_ListPlanDetails_DM"/>
            </elseif>
            <else>
              <action next="RP1411_OfferOnePlanYN_DM"/>
            </else>
          </if>
        </action>
        <action label="operator">
          <session-mapping key="RP1425_operator_counter" expr="RP1425_operator_counter+1"/>
          <if cond="RP1425_operator_counter &lt; 2">
            <action next="RP1425_NoOtherPlans_DM"/>
            <else>
              <audio>
                <prompt id="RP1425_operator_05">
                  <prompt-segments>
                    <audiofile text="Unfortunately, I can t take you to an agent right now " src="RP1425_operator_05.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
              <session-mapping key="GlobalVars.playFailureGoodbye" value="true" type="Boolean"/>
              <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
              <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
        <action label="repeat">
          <action next="RP1411_OfferOnePlanYN_DM"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="(RP1425_operator_counter &gt; 0)">
                <prompt id="RP1425_operator_01">
                  <prompt-segments>
                    <audiofile text="I m sorry, I can t transfer you right now" src="RP1425_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_02">
                  <prompt-segments>
                    <audiofile text="To see if you're eligible for a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_04">
                  <prompt-segments>
                    <audiofile text="If you re done, you can simply hang up" src="RP1425_operator_04.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP1425_ini_01" cond="(ratePlans &gt; 1)">
                    <prompt-segments>
                      <audiofile text="Those are the only plans I can offer you right now " src="RP1425_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_02" cond="(ratePlans == 1)">
                    <prompt-segments>
                      <audiofile text="This is the only plan I can offer you right now  " src="RP1425_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_03" cond="(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To check if you can get a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_04" cond="!(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To hear the plan information again say go back  " src="RP1425_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_05">
                    <prompt-segments>
                      <audiofile text="If you re done, you can simply hang up " src="RP1425_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="(RP1425_operator_counter &gt; 0)">
                <prompt id="RP1425_operator_01">
                  <prompt-segments>
                    <audiofile text="I m sorry, I can t transfer you right now" src="RP1425_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_02">
                  <prompt-segments>
                    <audiofile text="To see if you're eligible for a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_04">
                  <prompt-segments>
                    <audiofile text="If you re done, you can simply hang up" src="RP1425_operator_04.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP1425_ini_01" cond="(ratePlans &gt; 1)">
                    <prompt-segments>
                      <audiofile text="Those are the only plans I can offer you right now " src="RP1425_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_02" cond="(ratePlans == 1)">
                    <prompt-segments>
                      <audiofile text="This is the only plan I can offer you right now  " src="RP1425_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_03" cond="(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To check if you can get a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_04" cond="!(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To hear the plan information again say go back  " src="RP1425_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_05">
                    <prompt-segments>
                      <audiofile text="If you re done, you can simply hang up " src="RP1425_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="(RP1425_operator_counter &gt; 0)">
                <prompt id="RP1425_operator_01">
                  <prompt-segments>
                    <audiofile text="I m sorry, I can t transfer you right now" src="RP1425_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_02">
                  <prompt-segments>
                    <audiofile text="To see if you're eligible for a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_04">
                  <prompt-segments>
                    <audiofile text="If you re done, you can simply hang up" src="RP1425_operator_04.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP1425_ini_01" cond="(ratePlans &gt; 1)">
                    <prompt-segments>
                      <audiofile text="Those are the only plans I can offer you right now " src="RP1425_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_02" cond="(ratePlans == 1)">
                    <prompt-segments>
                      <audiofile text="This is the only plan I can offer you right now  " src="RP1425_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_03" cond="(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To check if you can get a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_04" cond="!(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To hear the plan information again say go back  " src="RP1425_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_05">
                    <prompt-segments>
                      <audiofile text="If you re done, you can simply hang up " src="RP1425_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(RP1425_operator_counter &gt; 0)">
                <prompt id="RP1425_operator_01">
                  <prompt-segments>
                    <audiofile text="I m sorry, I can t transfer you right now" src="RP1425_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_02">
                  <prompt-segments>
                    <audiofile text="To see if you're eligible for a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_04">
                  <prompt-segments>
                    <audiofile text="If you re done, you can simply hang up" src="RP1425_operator_04.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP1425_ini_01" cond="(ratePlans &gt; 1)">
                    <prompt-segments>
                      <audiofile text="Those are the only plans I can offer you right now " src="RP1425_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_02" cond="(ratePlans == 1)">
                    <prompt-segments>
                      <audiofile text="This is the only plan I can offer you right now  " src="RP1425_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_03" cond="(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To check if you can get a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_04" cond="!(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To hear the plan information again say go back  " src="RP1425_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_05">
                    <prompt-segments>
                      <audiofile text="If you re done, you can simply hang up " src="RP1425_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(RP1425_operator_counter &gt; 0)">
                <prompt id="RP1425_operator_01">
                  <prompt-segments>
                    <audiofile text="I m sorry, I can t transfer you right now" src="RP1425_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_02">
                  <prompt-segments>
                    <audiofile text="To see if you're eligible for a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_04">
                  <prompt-segments>
                    <audiofile text="If you re done, you can simply hang up" src="RP1425_operator_04.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP1425_ini_01" cond="(ratePlans &gt; 1)">
                    <prompt-segments>
                      <audiofile text="Those are the only plans I can offer you right now " src="RP1425_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_02" cond="(ratePlans == 1)">
                    <prompt-segments>
                      <audiofile text="This is the only plan I can offer you right now  " src="RP1425_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_03" cond="(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To check if you can get a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_04" cond="!(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To hear the plan information again say go back  " src="RP1425_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_05">
                    <prompt-segments>
                      <audiofile text="If you re done, you can simply hang up " src="RP1425_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if cond="(RP1425_operator_counter &gt; 0)">
                <prompt id="RP1425_operator_01">
                  <prompt-segments>
                    <audiofile text="I m sorry, I can t transfer you right now" src="RP1425_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_02">
                  <prompt-segments>
                    <audiofile text="To see if you're eligible for a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_04">
                  <prompt-segments>
                    <audiofile text="If you re done, you can simply hang up" src="RP1425_operator_04.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP1425_ini_01" cond="(ratePlans &gt; 1)">
                    <prompt-segments>
                      <audiofile text="Those are the only plans I can offer you right now " src="RP1425_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_02" cond="(ratePlans == 1)">
                    <prompt-segments>
                      <audiofile text="This is the only plan I can offer you right now  " src="RP1425_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_03" cond="(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To check if you can get a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_04" cond="!(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To hear the plan information again say go back  " src="RP1425_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_05">
                    <prompt-segments>
                      <audiofile text="If you re done, you can simply hang up " src="RP1425_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(RP1425_operator_counter &gt; 0)">
                <prompt id="RP1425_operator_01">
                  <prompt-segments>
                    <audiofile text="I m sorry, I can t transfer you right now" src="RP1425_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_02">
                  <prompt-segments>
                    <audiofile text="To see if you're eligible for a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_04">
                  <prompt-segments>
                    <audiofile text="If you re done, you can simply hang up" src="RP1425_operator_04.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP1425_ini_01" cond="(ratePlans &gt; 1)">
                    <prompt-segments>
                      <audiofile text="Those are the only plans I can offer you right now " src="RP1425_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_02" cond="(ratePlans == 1)">
                    <prompt-segments>
                      <audiofile text="This is the only plan I can offer you right now  " src="RP1425_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_03" cond="(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To check if you can get a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_04" cond="!(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To hear the plan information again say go back  " src="RP1425_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_05">
                    <prompt-segments>
                      <audiofile text="If you re done, you can simply hang up " src="RP1425_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(RP1425_operator_counter &gt; 0)">
                <prompt id="RP1425_operator_01">
                  <prompt-segments>
                    <audiofile text="I m sorry, I can t transfer you right now" src="RP1425_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_02">
                  <prompt-segments>
                    <audiofile text="To see if you're eligible for a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_04">
                  <prompt-segments>
                    <audiofile text="If you re done, you can simply hang up" src="RP1425_operator_04.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP1425_ini_01" cond="(ratePlans &gt; 1)">
                    <prompt-segments>
                      <audiofile text="Those are the only plans I can offer you right now " src="RP1425_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_02" cond="(ratePlans == 1)">
                    <prompt-segments>
                      <audiofile text="This is the only plan I can offer you right now  " src="RP1425_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_03" cond="(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To check if you can get a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_04" cond="!(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To hear the plan information again say go back  " src="RP1425_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_05">
                    <prompt-segments>
                      <audiofile text="If you re done, you can simply hang up " src="RP1425_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="(RP1425_operator_counter &gt; 0)">
                <prompt id="RP1425_operator_01">
                  <prompt-segments>
                    <audiofile text="I m sorry, I can t transfer you right now" src="RP1425_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_02">
                  <prompt-segments>
                    <audiofile text="To see if you're eligible for a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="RP1425_operator_04">
                  <prompt-segments>
                    <audiofile text="If you re done, you can simply hang up" src="RP1425_operator_04.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="RP1425_ini_01" cond="(ratePlans &gt; 1)">
                    <prompt-segments>
                      <audiofile text="Those are the only plans I can offer you right now " src="RP1425_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_02" cond="(ratePlans == 1)">
                    <prompt-segments>
                      <audiofile text="This is the only plan I can offer you right now  " src="RP1425_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_03" cond="(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To check if you can get a payment extension, say 'extension' or to hear the plan information again say 'go back' " src="RP1425_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_04" cond="!(extensionAllowed==true &amp;&amp; heardDataExceededInfo==false)">
                    <prompt-segments>
                      <audiofile text="To hear the plan information again say go back  " src="RP1425_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="RP1425_ini_05">
                    <prompt-segments>
                      <audiofile text="If you re done, you can simply hang up " src="RP1425_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="RP1425_NoOtherPlans_DM.jsp" count="1">
            <param name="extensionAllowed" value="extensionAllowedVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="RP1425_NoOtherPlans_DM_dtmf.jsp" count="1">
            <param name="extensionAllowed" value="extensionAllowedVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="RP1425_cnf_ini_01">
                <prompt-segments>
                  <audiofile text="You want an extension " src="RP1425_cnf_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  