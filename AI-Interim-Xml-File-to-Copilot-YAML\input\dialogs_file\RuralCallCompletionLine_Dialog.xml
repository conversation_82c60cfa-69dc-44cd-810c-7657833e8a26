<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="RuralCallCompletionLine_Dialog">
    <decision-state id="RC0100_InitializeRCCL_DS">
      <session-mapping key="TransferTag" expr="'RCC_Customer_English'"/>
      <action next="RC0105_PlayGreeting_PP"/>
    </decision-state>

    <play-state id="RC0105_PlayGreeting_PP">
      <audio>
        <prompt id="RC0105_out_01" bargein="false">
          <prompt-segments>
            <audiofile text="You've reached the Metro by T-Mobile Rural Call Completion support line" src="RC0105_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="RC0110_OfferSpanish_PP"/>
    </play-state>

    <play-state id="RC0110_OfferSpanish_PP">
      <audio>
        <prompt id="RC0110_out_01" bargein="true">
          <prompt-segments>
            <audiofile text="Para continuar en Espanol, marque 9" src="RC0110_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_2000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_2000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="RC0115_CustomerOrCarrier_DM"/>
    </play-state>

    <dm-state id="RC0115_CustomerOrCarrier_DM" type="CUST">
      <success>
        <action label="spanish">
          <session-mapping key="language" expr="'es-US'"/>
          <session-mapping key="TransferTag" expr="'RCC_Customer_Spanish'"/>
          <action next="RC0125_MetricsRCCCustomer_JDA"/>
        </action>
        <action label="carrier">
          <session-mapping key="TransferTag" expr="'RCC_Carrier'"/>
          <audio>
            <prompt id="RC0115_out_01">
              <prompt-segments>
                <audiofile text="Putting you through One moment" src="RC0115_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="RC0120_MetricsRCCCarrier_JDA"/>
        </action>
        <action label="customer">
          <action next="RC0125_MetricsRCCCustomer_JDA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="RC0115_ini_01">
                <prompt-segments>
                  <audiofile text="As part of this call, and subsequent interactions from this call, we may collect information about you to improve service To learn more, go to metro by T dash Mobile dot com slash privacy If you are a customer calling from Metro or another telephone service provider, please stay on the line and we'll get you to an representative who can help If you are a rural landline carrier, please press one " src="RC0115_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="RC0115_ini_01">
                <prompt-segments>
                  <audiofile text="As part of this call, and subsequent interactions from this call, we may collect information about you to improve service To learn more, go to metro by T dash Mobile dot com slash privacy If you are a customer calling from Metro or another telephone service provider, please stay on the line and we'll get you to an representative who can help If you are a rural landline carrier, please press one " src="RC0115_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="RC0115_CustomerOrCarrier_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxrepeats="0" maxnomatches="0" maxnoinputs="0"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="RC0120_MetricsRCCCarrier_DS">
      <action next="RC0130_TransferToRCC_SD"/>
    </decision-state>

    <decision-state id="RC0125_MetricsRCCCustomer_DS">
      <action next="RC0130_TransferToRCC_SD"/>
    </decision-state>

    <subdialog-state id="RC0130_TransferToRCC_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
    </subdialog-state>
  </dialog>
  