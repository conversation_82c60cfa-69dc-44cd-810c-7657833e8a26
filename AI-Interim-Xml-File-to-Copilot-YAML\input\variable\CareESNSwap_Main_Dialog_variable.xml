<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="GlobalVars.aniMatch" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.switchLinesSuccess" value="false" type="string"/>
  <session-mapping key="GlobalVars.activityCode" value="BSS" type="string"/>
  <session-mapping key="same" value="lastresult" type="string"/>
  <session-mapping key="new" value="lastresult" type="string"/>
  <session-mapping key="GlobalVars.restrictionsUpdateSuccessfull" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.simSwapRestricted" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.deviceChangeTargetSIMChosen" value="ESIM" type="string"/>
  <session-mapping key="GetAccountDetails.hasPhoneInsurance" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.currentSIMType" value="PSIM" type="string"/>
  <session-mapping key="GlobalVars.care_enable_twofactorauth" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.accountFutureRequestInd" value="true" type="boolean"/>
  <session-mapping key="twoFactorAuthOutcome" value="success" type="string"/>
  <session-mapping key="switchLinesSuccess" value="true" type="boolean"/>
  <session-mapping key="enteringFrom" value="ES1225_IMEIPassChecksum_DS" type="string"/>
  <session-mapping key="interpretation.dm_root" value="instructions" type="string"/>
  <session-mapping key="GlobalVars.imeiFailChecksum" value="true" type="boolean"/>
  <session-mapping key="simType" value="PESIM" type="string"/>
  <session-mapping key="GlobalVars.iccidFailChecksum" value="true" type="boolean"/>
</session-mappings>
