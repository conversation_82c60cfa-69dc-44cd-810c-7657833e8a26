<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Goodbye_Dialog">
    <custom-state id="Goodbye_Form_CS">
      <action next="PromptBeforeTransfer_CS"/>
    </custom-state>

    <custom-state id="PromptBeforeTransfer_CS">
      <field name="ForcePromptQueueBeforeExiting">
        <prompt timeout="50ms"/>
        <grammar version="1.0" xml:lang="en-US" tag-format="semantics/1.0" root="empty" xml:base="Goodbye.dvxml">
          <rule id="empty" scope="public">
            <one-of>
              <item>dummy item</item>
            </one-of>
          </rule>
        </grammar>
        <noinput>
          <action next="end_CS"/>
        </noinput>
        <nomatch>
          <action next="end_CS"/>
        </nomatch>
        <action next="end_CS"/>
      </field>
      <catch>
        <action next="end_CS"/>
      </catch>
    </custom-state>

    <custom-state id="end_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

  </dialog>
  