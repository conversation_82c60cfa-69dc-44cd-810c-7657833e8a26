<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="UpfrontAuthentication_Main_Dialog">
    <decision-state id="UA1001_CheckContext_DS">
      <action next="UA1005_ExistingAccountYN_DM"/>
    </decision-state>

    <dm-state id="UA1005_ExistingAccountYN_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="UA1005_out_01">
              <prompt-segments>
                <audiofile text="Great" src="UA1005_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="UA1105_CollectMDN_DM"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="UA1005_out_02">
              <prompt-segments>
                <audiofile text="Okay" src="UA1005_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="UA1205_NewCustomerInfoYN_DM"/>
        </action>
        <action label="operator">
          <session-mapping key="UA1005_operator_counter" expr="UA1005_operator_counter+1"/>
          <if cond="UA1005_operator_counter &lt; 3">
            <action next="UA1005_ExistingAccountYN_DM"/>
            <else>
              <audio>
                <prompt id="UA1005_operator_05">
                  <prompt-segments>
                    <audiofile text="Im sorry, I cant take you to an agent right now" src="UA1005_operator_05.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="UA1005_operator_06">
                  <prompt-segments>
                    <audiofile src="UA1005_operator_06.wav" text="You can find out more about Metro, or manage your account online You can also get help at one of our stores or authorized dealers You can find a map of our locations, Information about plans and phones, and Access to your account at metrobyt-mobilecom"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
              <session-mapping key="GlobalVars.playFailureGoodbye" value="true" type="Boolean"/>
              <action next="UA1010_Goodbye_SD"/>
            </else>
          </if>
        </action>
        <action label="spanish">
          <session-mapping key="language" expr="'es-US'"/>
          <gotodialog next="UpfrontAuthentication_Main#UA1005_ExistingAccountYN_DM"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="UA1005_operator_counter == 1">
                <prompt id="UA1005_operator_01">
                  <prompt-segments>
                    <audiofile text="Im sorry, I cant transfer you right now" src="UA1005_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="UA1005_operator_02">
                  <prompt-segments>
                    <audiofile src="UA1005_operator_02.wav" text="Do you already have a Metro  account?"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="UA1005_operator_counter == 2">
                  <prompt id="UA1005_operator_03">
                    <prompt-segments>
                      <audiofile text="Let me try to help you here one more time" src="UA1005_operator_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1005_operator_04">
                    <prompt-segments>
                      <audiofile src="UA1005_operator_04.wav" text="If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="UA1005_ini_01">
                    <prompt-segments>
                      <audiofile text="Do you already have an account with us?" src="UA1005_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="UA1005_operator_counter == 1">
                <prompt id="UA1005_operator_01">
                  <prompt-segments>
                    <audiofile text="Im sorry, I cant transfer you right now" src="UA1005_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="UA1005_operator_02">
                  <prompt-segments>
                    <audiofile src="UA1005_operator_02.wav" text="Do you already have a Metro  account?"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="UA1005_operator_counter == 2">
                  <prompt id="UA1005_operator_03">
                    <prompt-segments>
                      <audiofile text="Let me try to help you here one more time" src="UA1005_operator_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1005_operator_04">
                    <prompt-segments>
                      <audiofile src="UA1005_operator_04.wav" text="If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="UA1005_ini_01">
                    <prompt-segments>
                      <audiofile text="Do you already have an account with us?" src="UA1005_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="UA1005_nm1_01">
                <prompt-segments>
                  <audiofile src="UA1005_nm1_01.wav" text="Do you already have a Metro  account?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1005_nm2_01">
                <prompt-segments>
                  <audiofile src="UA1005_nm2_01.wav" text="If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1005_nm2_01">
                <prompt-segments>
                  <audiofile src="UA1005_nm2_01.wav" text="If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1005_nm1_01">
                <prompt-segments>
                  <audiofile src="UA1005_nm1_01.wav" text="Do you already have a Metro  account?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1005_nm2_01">
                <prompt-segments>
                  <audiofile src="UA1005_nm2_01.wav" text="If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1005_nm2_01">
                <prompt-segments>
                  <audiofile src="UA1005_nm2_01.wav" text="If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="UA1005_operator_counter == 1">
                <prompt id="UA1005_operator_01">
                  <prompt-segments>
                    <audiofile text="Im sorry, I cant transfer you right now" src="UA1005_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="UA1005_operator_02">
                  <prompt-segments>
                    <audiofile src="UA1005_operator_02.wav" text="Do you already have a Metro  account?"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="UA1005_operator_counter == 2">
                  <prompt id="UA1005_operator_03">
                    <prompt-segments>
                      <audiofile text="Let me try to help you here one more time" src="UA1005_operator_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1005_operator_04">
                    <prompt-segments>
                      <audiofile src="UA1005_operator_04.wav" text="If you already have a Metro  account, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="UA1005_ini_01">
                    <prompt-segments>
                      <audiofile text="Do you already have an account with us?" src="UA1005_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommandsNoOperator_dtmf.grxml">
          <grammars filename="UA1005_ExistingAccountYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="UA1005_ExistingAccountYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="UA1010_Goodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="UA1010_Goodbye_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="UA1010_Goodbye_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <dm-state id="UA1105_CollectMDN_DM" type="CUST">
      <success>
        <action label="dont_know">
          <session-mapping key="GlobalVars.existingCustomerInfoType" expr="'forgotMDN'"/>
          <action next="UA1125_ExistingCustomerInfoYN_DM"/>
        </action>
        <action label="lost_phone">
          <session-mapping key="GlobalVars.existingCustomerInfoType" expr="'lostPhone'"/>
          <action next="UA1125_ExistingCustomerInfoYN_DM"/>
        </action>
        <action label="default">
          <session-mapping key="MDN" expr="UA1105_CollectMDN_DM.returnvalue"/>
          <action next="UA1110_GetAccountDetails_DB_DA"/>
        </action>
        <action label="operator">
          <session-mapping key="UA1105_operator_counter" expr="UA1105_operator_counter+1"/>
          <if cond="UA1105_operator_counter &lt; 3">
            <action next="UA1105_CollectMDN_DM"/>
            <else>
              <audio>
                <prompt id="UA1105_operator_05">
                  <prompt-segments>
                    <audiofile text="Im sorry, we cant go on without your phone number" src="UA1105_operator_05.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
              <session-mapping key="GlobalVars.playFailureGoodbye" value="true" type="Boolean"/>
              <action next="UA1125_ExistingCustomerInfoYN_DM"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="UA1105_operator_counter == 1">
                <prompt id="UA1105_operator_01">
                  <prompt-segments>
                    <audiofile text="Im sorry, I cant transfer you right now" src="UA1105_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="UA1105_operator_02">
                  <prompt-segments>
                    <audiofile src="UA1105_operator_02.wav" text="To get started, tell me your Metro  phone number If you don t know it, press 1  Or if your phone is lost or damaged, press 2"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="UA1105_operator_counter == 2">
                  <prompt id="UA1105_operator_03">
                    <prompt-segments>
                      <audiofile text="Let me try to help you here one more time" src="UA1105_operator_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1105_operator_04">
                    <prompt-segments>
                      <audiofile src="UA1105_operator_04.wav" text="Starting with the area code,  please enter your Metro  phone number If you don t know it, press 1  Or if your phone is lost or damaged, press 2"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="invalidMDNCount == 1">
                  <prompt id="UA1105_rin_01">
                    <prompt-segments>
                      <audiofile text="Sorry, I cant seem to find that phone numberTry entering it again, area code first If you dont know it, press 1 Or if your phone is lost or damaged, press 2" src="UA1105_rin_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="invalidMDNCount == 2">
                  <prompt id="UA1105_rin_02">
                    <prompt-segments>
                      <audiofile text="Hmm, I still cant find that numberPlease enter it again, starting with the area code If you dont know it, press 1Or if your phone is lost or damaged, press 2" src="UA1105_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="UA1105_ini_01">
                    <prompt-segments>
                      <audiofile src="UA1105_ini_01.wav" text="To get started, please say or enter your Metro  phone number"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="UA1105_operator_counter == 1">
                <prompt id="UA1105_operator_01">
                  <prompt-segments>
                    <audiofile text="Im sorry, I cant transfer you right now" src="UA1105_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="UA1105_operator_02">
                  <prompt-segments>
                    <audiofile src="UA1105_operator_02.wav" text="To get started, tell me your Metro  phone number If you don t know it, press 1  Or if your phone is lost or damaged, press 2"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="UA1105_operator_counter == 2">
                  <prompt id="UA1105_operator_03">
                    <prompt-segments>
                      <audiofile text="Let me try to help you here one more time" src="UA1105_operator_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1105_operator_04">
                    <prompt-segments>
                      <audiofile src="UA1105_operator_04.wav" text="Starting with the area code,  please enter your Metro  phone number If you don t know it, press 1  Or if your phone is lost or damaged, press 2"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="invalidMDNCount == 1">
                  <prompt id="UA1105_rin_01">
                    <prompt-segments>
                      <audiofile text="Sorry, I cant seem to find that phone numberTry entering it again, area code first If you dont know it, press 1 Or if your phone is lost or damaged, press 2" src="UA1105_rin_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="invalidMDNCount == 2">
                  <prompt id="UA1105_rin_02">
                    <prompt-segments>
                      <audiofile text="Hmm, I still cant find that numberPlease enter it again, starting with the area code If you dont know it, press 1Or if your phone is lost or damaged, press 2" src="UA1105_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="UA1105_ini_01">
                    <prompt-segments>
                      <audiofile src="UA1105_ini_01.wav" text="To get started, please say or enter your Metro  phone number"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="UA1105_nm1_01">
                <prompt-segments>
                  <audiofile src="UA1105_nm1_01.wav" text="Please enter your Metro area code and phone number"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1105_nm2_01">
                <prompt-segments>
                  <audiofile src="UA1105_nm2_01.wav" text="Starting with the area code, enter your metro phone number on your keypad If you don t know it, press 1  Or if your phone is lost or damaged, press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1105_nm3_01">
                <prompt-segments>
                  <audiofile src="UA1105_nm3_01.wav" text="Starting with the area code, enter your metro phone number on your keypad If you don t know it, press 1  Or if your phone is lost or damaged, press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1105_nm1_01">
                <prompt-segments>
                  <audiofile src="UA1105_nm1_01.wav" text="Please enter your Metro area code and phone number"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1105_nm2_01">
                <prompt-segments>
                  <audiofile src="UA1105_nm2_01.wav" text="Starting with the area code, enter your metro phone number on your keypad If you don t know it, press 1  Or if your phone is lost or damaged, press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1105_nm3_01">
                <prompt-segments>
                  <audiofile src="UA1105_nm3_01.wav" text="Starting with the area code, enter your metro phone number on your keypad If you don t know it, press 1  Or if your phone is lost or damaged, press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="UA1105_CollectMDN_DM.grxml" count="1"/>
          <dtmfgrammars filename="UA1105_CollectMDN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="2000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'dont_know'">
                <prompt id="UA1105_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You dont know it" src="UA1105_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="lastresult.interpretation.dm_root == 'lost_phone'">
                  <prompt id="UA1105_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Your phone is lost or damaged" src="UA1105_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="digits" expr="lastresult.interpretation.dm_root">
                    <param name="runtime" value="ecmascript"/>
                    <param name="intonation" value="m"/>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
        </prompt_configuration>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="UA1110_GetAccountDetails_DB_DA">
      <session-mapping key="mdn" value="MDN" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <data-access id="GetSubscriberDetails" classname="com.nuance.metro.dataaccess.GetSubscriberDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="languageCode"/>
          <input-variable name="sessionId"/>
          <input-variable name="imei"/>
          <input-variable name="clearCache"/>
          <input-variable name="multiLineDetails"/>
        </inputs>
        <outputs>
          <output-variable name="mdn"/>
          <output-variable name="accountNumber" mask="true"/>
          <output-variable name="active"/>
          <output-variable name="serialNumber"/>
          <output-variable name="features"/>
          <output-variable name="thirdPartyFeatures"/>
          <output-variable name="payDate"/>
          <output-variable name="taxInclusive"/>
          <output-variable name="accountStatus"/>
          <output-variable name="dueImmediatelyAmount"/>
          <output-variable name="highSecurity"/>
          <output-variable name="ratePlan"/>
          <output-variable name="marketID"/>
          <output-variable name="language"/>
          <output-variable name="isAutopayEnabled"/>
          <output-variable name="hasPhoneInsurance"/>
          <output-variable name="balance"/>
          <output-variable name="isDeviceAllowed"/>
          <output-variable name="isSuspended"/>
          <output-variable name="accountType"/>
          <output-variable name="validAccount"/>
          <output-variable name="roamingBucket"/>
          <output-variable name="paymentStatus"/>
          <output-variable name="ratePlanAttributesMap"/>
          <output-variable name="networkType"/>
          <output-variable name="securityQuestionCode"/>
          <output-variable name="hasEWallet"/>
          <output-variable name="eligibleForBillCycleReset"/>
          <output-variable name="billCycleLength"/>
          <output-variable name="firstName"/>
          <output-variable name="lastName"/>
          <output-variable name="eligibleForUnhotlineCheck"/>
          <output-variable name="securityQuestionString"/>
          <output-variable name="iccid"/>
          <output-variable name="imei"/>
          <output-variable name="hasAutopayPending"/>
          <output-variable name="insuranceCode"/>
          <output-variable name="subscriberId"/>
          <output-variable name="usedESNAllowed"/>
          <output-variable name="emailVerificationStatus"/>
          <output-variable name="emailAddress"/>
          <output-variable name="currentPromo"/>
          <output-variable name="futurePromo"/>
          <output-variable name="featureCodes"/>
          <output-variable name="superRegion"/>
          <output-variable name="accountFutureRequestInd"/>
          <output-variable name="subscriberFuturePricePlanInd"/>
          <output-variable name="ratePlanPrice"/>
          <output-variable name="currentPlanPromptURL"/>
          <output-variable name="currentPlanPromptTTS"/>
          <output-variable name="familyPlanIndicator"/>
          <output-variable name="topupEligibility"/>
          <output-variable name="customerTreatmentLevel"/>
          <output-variable name="HasHighSpeedData"/>
          <output-variable name="includedFeatures"/>
          <output-variable name="hasDataMaximizer"/>
          <output-variable name="eligibleForUpgrade"/>
          <output-variable name="upgradeEligibilityDate"/>
          <output-variable name="arBalance"/>
          <output-variable name="multilineList"/>
          <output-variable name="planMedialPromptURL"/>
          <output-variable name="planMedialPromptTTS"/>
          <output-variable name="hasDefaultAccountValues"/>
          <output-variable name="zipCode" mask="true"/>
          <output-variable name="accountRestrictIndicator"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetSubscriberDetails &amp;&amp; GetSubscriberDetails.validAccount">
          <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
          <session-mapping key="GlobalVars.trn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.unCoopMaxRequest" expr="GetSubscriberDetails.unCoopMaxRequest"/>
          <session-mapping key="GlobalVars.coopMaxRequest" expr="GetSubscriberDetails.coopMaxRequest"/>
          <session-mapping key="ActivationTable.OLD_MDN_NUM" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.networkType" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="ActivationTable.NETWORK_TYPE" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="GlobalVars.marketID" expr="GetSubscriberDetails.marketID"/>
          <session-mapping key="GlobalVars.zipCode" expr="GetSubscriberDetails.zipCode"/>
          <session-mapping key="GlobalVars.mdn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.isOnFamilyPlan" expr="GetSubscriberDetails.isOnFamilyPlan"/>
          <session-mapping key="GlobalVars.accountStatus" expr="GetSubscriberDetails.accountStatus"/>
          <session-mapping key="GlobalVars.accountFutureRequestInd" expr="GetSubscriberDetails.accountFutureRequestInd"/>
          <session-mapping key="GlobalVars.subscriberFuturePricePlanInd" expr="GetSubscriberDetails.subscriberFuturePricePlanInd"/>
        </if>
        <action next="UA1115_CheckResults_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="UA1115_CheckResults_DS">
      <if cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.validAccount">
        <session-mapping key="GlobalVars.collectedMDNUpfront" value="true" type="Boolean"/>
        <session-mapping key="GlobalVars.cti_MDN" expr="MDN"/>
        <if cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ9')">
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'not_authenticated_high_security_account')"/>
          <action next="UA1135_Transfer_SD"/>
          <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ8')">
            <action next="UA1010_Goodbye_SD"/>
          </elseif>
          <elseif cond="GlobalVars.accountStatus == 'suspended'">
            <action next="UA1130_SuspendedHandling_SD"/>
          </elseif>
          <else>
            <action next="UA1116_ActiveMDNTransition_PP"/>
          </else>
        </if>
        <else>
          <if cond="invalidMDNCount == 2">
            <session-mapping key="invalidMDNCount" expr="3"/>
            <action next="UA1117_InvalidMDNTransition_PP"/>
            <else>
              <if cond="invalidMDNCount == 1">
                <session-mapping key="invalidMDNCount" expr="2"/>
                <action next="UA1105_CollectMDN_DM"/>
                <else>
                  <session-mapping key="invalidMDNCount" expr="1"/>
                  <action next="UA1105_CollectMDN_DM"/>
                </else>
              </if>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="UA1116_ActiveMDNTransition_PP">
      <audio>
        <prompt id="UA1116_out_01">
          <prompt-segments>
            <audiofile text="Thanks! " src="UA1116_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="UA1117_InvalidMDNTransition_PP">
      <audio>
        <prompt id="UA1117_out_01">
          <prompt-segments>
            <audiofile text="Sorry, that still doesnt match my records, and we cant go on without your phone number" src="UA1117_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="UA1125_ExistingCustomerInfoYN_DM"/>
    </play-state>

    <subdialog-state id="UA1120_MainMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
      <action next="UA1120_MainMenu_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="UA1120_MainMenu_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <dm-state id="UA1125_ExistingCustomerInfoYN_DM" type="YSNO">
      <session-mapping key="existingCustomerInfoType" value="GlobalVars.existingCustomerInfoType" type="String"/>
      <success>
        <action label="true">
          <audio>
            <prompt id="UA1125_out_01">
              <prompt-segments>
                <audiofile text="Sure" src="UA1125_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="UA1125_ExistingCustomerInfoYN_DM"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="UA1125_out_02">
              <prompt-segments>
                <audiofile text="Alright" src="UA1125_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="UA1010_Goodbye_SD"/>
        </action>
        <action label="operator">
          <audio>
            <prompt id="UA1125_operator_01">
              <prompt-segments>
                <audiofile text="Im sorry, I cant take you to an agent right now" src="UA1125_operator_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="UA1125_operator_02">
              <prompt-segments>
                <audiofile src="UA1125_operator_02.wav" text="To find out your phone number, dial #-6-8-6-# from your Metro phone You can also manage your account by dialing 611 directly from your Metro phone, with the myMetro app, or online at metrobyt-mobilecom If your phone is lost or damaged, you can fill out a claim and order a replacement online, or at one of our stores or authorized dealers For a map of our locations near you, visit metrobyt-mobilecom"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.playFailureGoodbye" value="true" type="Boolean"/>
          <action next="UA1010_Goodbye_SD"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="existingCustomerInfoType == 'lostPhone'">
                <prompt id="UA1125_ini_01">
                  <prompt-segments>
                    <audiofile text="You can fill out an insurance claim, and order a replacement phone online or you can get help at one of our stores or authorized dealers For a map of our locations near you, visit metrobyt-mobilecom" src="UA1125_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="UA1125_ini_02" cond="existingCustomerInfoType == 'forgotMDN'">
                    <prompt-segments>
                      <audiofile src="UA1125_ini_02.wav" text="To find out your phone number, you can hang up here and dial #-6-8-6-# from your Metro  phone Hit  Send  and your phone number will appear on your screen That s #-6-8-6-#"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1125_ini_03" cond="!(existingCustomerInfoType == 'forgotMDN')">
                    <prompt-segments>
                      <audiofile src="UA1125_ini_03.wav" text="If you forgot your number, you can hang up here and dial #-6-8-6-# from your Metro  phone Hit  Send  and your phone number will appear on your screen That s #-6-8-6-#"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_1000ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1125_ini_04">
                    <prompt-segments>
                      <audiofile src="UA1125_ini_04.wav" text="You can also manage your account and make payments by dialing 611 directly from your Metro phone, with the myMetro app, or at metrobyt-mobilecom"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1125_ini_05">
                    <prompt-segments>
                      <audiofile text="If your phone is lost or damaged, you can fill out a claim and order a replacement online, or at one of our stores or authorized dealers For a map of our locations near you, visit metrobyt-mobilecom" src="UA1125_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1125_ini_06">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="UA1125_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="existingCustomerInfoType == 'lostPhone'">
                <prompt id="UA1125_ini_01">
                  <prompt-segments>
                    <audiofile text="You can fill out an insurance claim, and order a replacement phone online or you can get help at one of our stores or authorized dealers For a map of our locations near you, visit metrobyt-mobilecom" src="UA1125_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="UA1125_ini_02" cond="existingCustomerInfoType == 'forgotMDN'">
                    <prompt-segments>
                      <audiofile src="UA1125_ini_02.wav" text="To find out your phone number, you can hang up here and dial #-6-8-6-# from your Metro  phone Hit  Send  and your phone number will appear on your screen That s #-6-8-6-#"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1125_ini_03" cond="!(existingCustomerInfoType == 'forgotMDN')">
                    <prompt-segments>
                      <audiofile src="UA1125_ini_03.wav" text="If you forgot your number, you can hang up here and dial #-6-8-6-# from your Metro  phone Hit  Send  and your phone number will appear on your screen That s #-6-8-6-#"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_1000ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1125_ini_04">
                    <prompt-segments>
                      <audiofile src="UA1125_ini_04.wav" text="You can also manage your account and make payments by dialing 611 directly from your Metro phone, with the myMetro app, or at metrobyt-mobilecom"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1125_ini_05">
                    <prompt-segments>
                      <audiofile text="If your phone is lost or damaged, you can fill out a claim and order a replacement online, or at one of our stores or authorized dealers For a map of our locations near you, visit metrobyt-mobilecom" src="UA1125_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1125_ini_06">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="UA1125_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="UA1125_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="UA1125_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1125_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up" src="UA1125_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1125_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up" src="UA1125_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1125_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="UA1125_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1125_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up" src="UA1125_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1125_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up" src="UA1125_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="existingCustomerInfoType == 'lostPhone'">
                <prompt id="UA1125_ini_01">
                  <prompt-segments>
                    <audiofile text="You can fill out an insurance claim, and order a replacement phone online or you can get help at one of our stores or authorized dealers For a map of our locations near you, visit metrobyt-mobilecom" src="UA1125_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="UA1125_ini_02" cond="existingCustomerInfoType == 'forgotMDN'">
                    <prompt-segments>
                      <audiofile src="UA1125_ini_02.wav" text="To find out your phone number, you can hang up here and dial #-6-8-6-# from your Metro  phone Hit  Send  and your phone number will appear on your screen That s #-6-8-6-#"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1125_ini_03" cond="!(existingCustomerInfoType == 'forgotMDN')">
                    <prompt-segments>
                      <audiofile src="UA1125_ini_03.wav" text="If you forgot your number, you can hang up here and dial #-6-8-6-# from your Metro  phone Hit  Send  and your phone number will appear on your screen That s #-6-8-6-#"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_1000ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1125_ini_04">
                    <prompt-segments>
                      <audiofile src="UA1125_ini_04.wav" text="You can also manage your account and make payments by dialing 611 directly from your Metro phone, with the myMetro app, or at metrobyt-mobilecom"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1125_ini_05">
                    <prompt-segments>
                      <audiofile text="If your phone is lost or damaged, you can fill out a claim and order a replacement online, or at one of our stores or authorized dealers For a map of our locations near you, visit metrobyt-mobilecom" src="UA1125_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1125_ini_06">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="UA1125_ini_06.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommandsNoOperator_dtmf.grxml">
          <grammars filename="UA1125_ExistingCustomerInfoYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="UA1125_ExistingCustomerInfoYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="UA1130_SuspendedHandling_SD">
      <gotodialog next="SuspendedHandling_Main_Dialog"/>
      <action next="UA1130_SuspendedHandling_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="UA1130_SuspendedHandling_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="UA1135_Transfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="UA1135_Transfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="UA1135_Transfer_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <dm-state id="UA1205_NewCustomerInfoYN_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="UA1205_out_01">
              <prompt-segments>
                <audiofile text="Sure" src="UA1205_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="UA1205_NewCustomerInfoYN_DM"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="UA1205_out_02">
              <prompt-segments>
                <audiofile text="Alright" src="UA1205_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="UA1010_Goodbye_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="UA1205_operator_counter" expr="UA1205_operator_counter+1"/>
          <if cond="UA1205_operator_counter &lt; 3">
            <action next="UA1205_NewCustomerInfoYN_DM"/>
            <else>
              <audio>
                <prompt id="UA1205_operator_05">
                  <prompt-segments>
                    <audiofile text="Im sorry, I cant take you to an agent right now" src="UA1205_operator_05.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="UA1205_operator_06">
                  <prompt-segments>
                    <audiofile text="If you have a phone you re ready to activate, call *-2-2-8 from that phone and we ll get you set up with an account Otherwise, you can find out more about our phones, plans and features online You can also sign up in person at one of our stores or authorized dealers For more information, and a map of our locations, visit us at metrobyt-mobilecom" src="UA1205_operator_06.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
              <session-mapping key="GlobalVars.playFailureGoodbye" value="true" type="Boolean"/>
              <action next="UA1010_Goodbye_SD"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="UA1205_operator_counter == 1">
                <prompt id="UA1205_operator_01">
                  <prompt-segments>
                    <audiofile text="Im sorry, I cant transfer you right now" src="UA1205_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="UA1205_operator_02">
                  <prompt-segments>
                    <audiofile text="Would you like to hear that information again?" src="UA1205_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="UA1205_operator_counter == 2">
                  <prompt id="UA1205_operator_03">
                    <prompt-segments>
                      <audiofile text="Let me try to help you here one more time" src="UA1205_operator_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_operator_04">
                    <prompt-segments>
                      <audiofile text="To hear the information again, say 'repeat that' or press 1If you are done, you can simply hang up" src="UA1205_operator_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="UA1205_ini_01">
                    <prompt-segments>
                      <audiofile src="UA1205_ini_01.wav" text="If you have a Metro  phone ready to activate, just dial *228 from that phone, and we ll help you set up your account and choose a plan That s *-2-2-8"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_ini_02">
                    <prompt-segments>
                      <audiofile text="Otherwise  You can buy a phone online at metrobyt-mobilecom, from one of our stores, or from any authorized Metro dealer You can also use an unlocked cellphone from another carrier We use the nationwide, 4G-LTE T-Mobile network so you can download, stream and upload faster than ever from coast-to-coast And our plans are simple and straightforward Get one low price with taxes and regulatory fees included There are no hidden fees and no annual contract" src="UA1205_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_1000ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_ini_03">
                    <prompt-segments>
                      <audiofile text="You can find a map of our locations, and details about our plans, at metrobyt-mobilecom" src="UA1205_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_ini_04">
                    <prompt-segments>
                      <audiofile text="Would you like to hear that again?" src="UA1205_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="UA1205_operator_counter == 1">
                <prompt id="UA1205_operator_01">
                  <prompt-segments>
                    <audiofile text="Im sorry, I cant transfer you right now" src="UA1205_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="UA1205_operator_02">
                  <prompt-segments>
                    <audiofile text="Would you like to hear that information again?" src="UA1205_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="UA1205_operator_counter == 2">
                  <prompt id="UA1205_operator_03">
                    <prompt-segments>
                      <audiofile text="Let me try to help you here one more time" src="UA1205_operator_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_operator_04">
                    <prompt-segments>
                      <audiofile text="To hear the information again, say 'repeat that' or press 1If you are done, you can simply hang up" src="UA1205_operator_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="UA1205_ini_01">
                    <prompt-segments>
                      <audiofile src="UA1205_ini_01.wav" text="If you have a Metro  phone ready to activate, just dial *228 from that phone, and we ll help you set up your account and choose a plan That s *-2-2-8"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_ini_02">
                    <prompt-segments>
                      <audiofile text="Otherwise  You can buy a phone online at metrobyt-mobilecom, from one of our stores, or from any authorized Metro dealer You can also use an unlocked cellphone from another carrier We use the nationwide, 4G-LTE T-Mobile network so you can download, stream and upload faster than ever from coast-to-coast And our plans are simple and straightforward Get one low price with taxes and regulatory fees included There are no hidden fees and no annual contract" src="UA1205_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_1000ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_ini_03">
                    <prompt-segments>
                      <audiofile text="You can find a map of our locations, and details about our plans, at metrobyt-mobilecom" src="UA1205_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_ini_04">
                    <prompt-segments>
                      <audiofile text="Would you like to hear that again?" src="UA1205_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="UA1205_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="UA1205_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1205_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up" src="UA1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1205_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up" src="UA1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1205_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="UA1205_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1205_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up" src="UA1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="UA1205_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say 'repeat' or press 1If you are done, you can simply hang up" src="UA1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="UA1205_operator_counter == 1">
                <prompt id="UA1205_operator_01">
                  <prompt-segments>
                    <audiofile text="Im sorry, I cant transfer you right now" src="UA1205_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="UA1205_operator_02">
                  <prompt-segments>
                    <audiofile text="Would you like to hear that information again?" src="UA1205_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="UA1205_operator_counter == 2">
                  <prompt id="UA1205_operator_03">
                    <prompt-segments>
                      <audiofile text="Let me try to help you here one more time" src="UA1205_operator_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_operator_04">
                    <prompt-segments>
                      <audiofile text="To hear the information again, say 'repeat that' or press 1If you are done, you can simply hang up" src="UA1205_operator_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="UA1205_ini_01">
                    <prompt-segments>
                      <audiofile src="UA1205_ini_01.wav" text="If you have a Metro  phone ready to activate, just dial *228 from that phone, and we ll help you set up your account and choose a plan That s *-2-2-8"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_ini_02">
                    <prompt-segments>
                      <audiofile text="Otherwise  You can buy a phone online at metrobyt-mobilecom, from one of our stores, or from any authorized Metro dealer You can also use an unlocked cellphone from another carrier We use the nationwide, 4G-LTE T-Mobile network so you can download, stream and upload faster than ever from coast-to-coast And our plans are simple and straightforward Get one low price with taxes and regulatory fees included There are no hidden fees and no annual contract" src="UA1205_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_1000ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_ini_03">
                    <prompt-segments>
                      <audiofile text="You can find a map of our locations, and details about our plans, at metrobyt-mobilecom" src="UA1205_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="UA1205_ini_04">
                    <prompt-segments>
                      <audiofile text="Would you like to hear that again?" src="UA1205_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommandsNoOperator_dtmf.grxml">
          <grammars filename="UA1205_NewCustomerInfoYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="UA1205_NewCustomerInfoYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  