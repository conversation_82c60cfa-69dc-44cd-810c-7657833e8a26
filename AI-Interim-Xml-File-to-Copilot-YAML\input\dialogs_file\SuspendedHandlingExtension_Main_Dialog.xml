<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="SuspendedHandlingExtension_Main_Dialog">
    <decision-state id="SH1101_CheckContext_DS">
      <action next="SH1115_Login_SD"/>
    </decision-state>

    <subdialog-state id="SH1115_Login_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="SH1115_Login_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SH1115_Login_SD_return_CS">
      <if cond="(GlobalVars.loggedIn == true)">
        <action next="SH1205_RatePlan_SD"/>
        <else>
          <action next="SH1220_PlanChangeIssue_PP"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="SH1205_RatePlan_SD">
      <gotodialog next="RatePlan_Main_Dialog"/>
      <action next="SH1205_RatePlan_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SH1205_RatePlan_SD_return_CS">
      <if cond="(GlobalVars.SubmitChangeOffer &amp;&amp; GlobalVars.SubmitChangeOffer.status == 'Success')">
        <action next="SH1210_GetAccountDetails_DB_DA"/>
        <else>
          <if cond="(GlobalVars.callType == 'extension')">
            <action next="SH1301_CheckContext_JDA"/>
            <else>
              <action next="SH1315_PaymentMethods_PP"/>
            </else>
          </if>
        </else>
      </if>
    </custom-state>

    <data-access-state id="SH1210_GetAccountDetails_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <data-access id="GetSubscriberDetails" classname="com.nuance.metro.dataaccess.GetSubscriberDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="languageCode"/>
          <input-variable name="sessionId"/>
          <input-variable name="imei"/>
          <input-variable name="clearCache"/>
          <input-variable name="multiLineDetails"/>
        </inputs>
        <outputs>
          <output-variable name="mdn"/>
          <output-variable name="accountNumber" mask="true"/>
          <output-variable name="active"/>
          <output-variable name="serialNumber"/>
          <output-variable name="features"/>
          <output-variable name="thirdPartyFeatures"/>
          <output-variable name="payDate"/>
          <output-variable name="taxInclusive"/>
          <output-variable name="accountStatus"/>
          <output-variable name="dueImmediatelyAmount"/>
          <output-variable name="highSecurity"/>
          <output-variable name="ratePlan"/>
          <output-variable name="marketID"/>
          <output-variable name="language"/>
          <output-variable name="isAutopayEnabled"/>
          <output-variable name="hasPhoneInsurance"/>
          <output-variable name="balance"/>
          <output-variable name="isDeviceAllowed"/>
          <output-variable name="isSuspended"/>
          <output-variable name="accountType"/>
          <output-variable name="validAccount"/>
          <output-variable name="roamingBucket"/>
          <output-variable name="paymentStatus"/>
          <output-variable name="ratePlanAttributesMap"/>
          <output-variable name="networkType"/>
          <output-variable name="securityQuestionCode"/>
          <output-variable name="hasEWallet"/>
          <output-variable name="eligibleForBillCycleReset"/>
          <output-variable name="billCycleLength"/>
          <output-variable name="firstName"/>
          <output-variable name="lastName"/>
          <output-variable name="eligibleForUnhotlineCheck"/>
          <output-variable name="securityQuestionString"/>
          <output-variable name="iccid"/>
          <output-variable name="imei"/>
          <output-variable name="hasAutopayPending"/>
          <output-variable name="insuranceCode"/>
          <output-variable name="subscriberId"/>
          <output-variable name="usedESNAllowed"/>
          <output-variable name="emailVerificationStatus"/>
          <output-variable name="emailAddress"/>
          <output-variable name="currentPromo"/>
          <output-variable name="futurePromo"/>
          <output-variable name="featureCodes"/>
          <output-variable name="superRegion"/>
          <output-variable name="accountFutureRequestInd"/>
          <output-variable name="subscriberFuturePricePlanInd"/>
          <output-variable name="ratePlanPrice"/>
          <output-variable name="currentPlanPromptURL"/>
          <output-variable name="currentPlanPromptTTS"/>
          <output-variable name="familyPlanIndicator"/>
          <output-variable name="topupEligibility"/>
          <output-variable name="customerTreatmentLevel"/>
          <output-variable name="HasHighSpeedData"/>
          <output-variable name="includedFeatures"/>
          <output-variable name="hasDataMaximizer"/>
          <output-variable name="eligibleForUpgrade"/>
          <output-variable name="upgradeEligibilityDate"/>
          <output-variable name="arBalance"/>
          <output-variable name="multilineList"/>
          <output-variable name="planMedialPromptURL"/>
          <output-variable name="planMedialPromptTTS"/>
          <output-variable name="hasDefaultAccountValues"/>
          <output-variable name="zipCode" mask="true"/>
          <output-variable name="accountRestrictIndicator"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetSubscriberDetails &amp;&amp; GetSubscriberDetails.status.toUpperCase() == 'SUCCESS' &amp;&amp; GetSubscriberDetails.validAccount">
          <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
          <session-mapping key="GlobalVars.trn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.unCoopMaxRequest" expr="GetSubscriberDetails.unCoopMaxRequest"/>
          <session-mapping key="GlobalVars.coopMaxRequest" expr="GetSubscriberDetails.coopMaxRequest"/>
          <session-mapping key="ActivationTable.OLD_MDN_NUM" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.networkType" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="ActivationTable.NETWORK_TYPE" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="GlobalVars.marketID" expr="GetSubscriberDetails.marketID"/>
          <session-mapping key="GlobalVars.zipCode" expr="GetSubscriberDetails.zipCode"/>
          <session-mapping key="GlobalVars.mdn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.isOnFamilyPlan" expr="GetSubscriberDetails.isOnFamilyPlan"/>
          <session-mapping key="GlobalVars.accountStatus" expr="GetSubscriberDetails.accountStatus"/>
          <session-mapping key="GlobalVars.accountFutureRequestInd" expr="GetSubscriberDetails.accountFutureRequestInd"/>
          <session-mapping key="GlobalVars.eligibleForUpgrade" expr="GetSubscriberDetails.isCurrentlyEligibleForDeviceUpgrade"/>
          <session-mapping key="GlobalVars.upgradeEligibilityDate" expr="GetSubscriberDetails.upgradeEligibilityDate"/>
          <session-mapping key="GlobalVars.subscriberFuturePricePlanInd" expr="GetSubscriberDetails.subscriberFuturePricePlanInd"/>
          <if cond="dueImmediatelyAmount &gt; 0">
            <action next="SH1215_PayNowYN_DM"/>
            <else>
              <gotodialog next="SuspendedHandling_Main#SH1035_Goodbye_SD"/>
            </else>
          </if>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
          <else>
            <gotodialog next="SuspendedHandling_Main#SH1035_Goodbye_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <dm-state id="SH1215_PayNowYN_DM" type="YSNO">
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentAmount" expr="dueImmediatelyAmount"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careSuspended'"/>
          <gotodialog next="SuspendedHandling_Main#SH1010_MakePayment_SD"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="SH1215_out_01">
              <prompt-segments>
                <audiofile text="No problem" src="SH1215_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="SH1315_PaymentMethods_PP"/>
        </action>
        <action label="operator">
          <session-mapping key="SH1215_operator_counter" expr="SH1215_operator_counter+1"/>
          <if cond="SH1215_operator_counter &lt; 2">
            <action next="SH1215_PayNowYN_DM"/>
            <else>
              <audio>
                <prompt id="SH1215_operator_03">
                  <prompt-segments>
                    <audiofile text="Unfortunately I cant take you to an agent right now" src="SH1215_operator_03.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
              <session-mapping key="GlobalVars.playFailureGoodbye" value="true" type="Boolean"/>
              <action next="SH1315_PaymentMethods_PP"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="SH1215_operator_counter == 1">
                <prompt id="SH1215_operator_01">
                  <prompt-segments>
                    <audiofile text="Im sorry I cant transfer you right now" src="SH1215_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SH1215_operator_02">
                  <prompt-segments>
                    <audiofile text="Would you like to make your payment now" src="SH1215_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1215_ini_01">
                    <prompt-segments>
                      <audiofile text="To turn your service back on you still need to make a payment for" src="SH1215_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="currency" expr="dueImmediatelyAmount">
                    <param value="f" name="intonation"/>
                    <param value="false" name="playZeroCents"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="SH1215_ini_02">
                    <prompt-segments>
                      <audiofile text="Would you like to pay that right now" src="SH1215_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="SH1215_operator_counter == 1">
                <prompt id="SH1215_operator_01">
                  <prompt-segments>
                    <audiofile text="Im sorry I cant transfer you right now" src="SH1215_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SH1215_operator_02">
                  <prompt-segments>
                    <audiofile text="Would you like to make your payment now" src="SH1215_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1215_ini_01">
                    <prompt-segments>
                      <audiofile text="To turn your service back on you still need to make a payment for" src="SH1215_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="currency" expr="dueImmediatelyAmount">
                    <param value="f" name="intonation"/>
                    <param value="false" name="playZeroCents"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="SH1215_ini_02">
                    <prompt-segments>
                      <audiofile text="Would you like to pay that right now" src="SH1215_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SH1215_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to pay your new amount now" src="SH1215_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1215_ini_01">
                <prompt-segments>
                  <audiofile text="To turn your service back on you still need to make a payment for" src="SH1215_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1215_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to pay that right now" src="SH1215_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1215_ini_01">
                <prompt-segments>
                  <audiofile text="To turn your service back on you still need to make a payment for" src="SH1215_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1215_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to pay that right now" src="SH1215_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1215_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to pay your new amount now" src="SH1215_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1215_ini_01">
                <prompt-segments>
                  <audiofile text="To turn your service back on you still need to make a payment for" src="SH1215_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1215_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to pay that right now" src="SH1215_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1215_ini_01">
                <prompt-segments>
                  <audiofile text="To turn your service back on you still need to make a payment for" src="SH1215_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1215_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to pay that right now" src="SH1215_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="SH1215_operator_counter == 1">
                <prompt id="SH1215_operator_01">
                  <prompt-segments>
                    <audiofile text="Im sorry I cant transfer you right now" src="SH1215_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SH1215_operator_02">
                  <prompt-segments>
                    <audiofile text="Would you like to make your payment now" src="SH1215_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1215_ini_01">
                    <prompt-segments>
                      <audiofile text="To turn your service back on you still need to make a payment for" src="SH1215_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="currency" expr="dueImmediatelyAmount">
                    <param value="f" name="intonation"/>
                    <param value="false" name="playZeroCents"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="SH1215_ini_02">
                    <prompt-segments>
                      <audiofile text="Would you like to pay that right now" src="SH1215_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SH1215_PayNowYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="SH1215_PayNowYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="SH1220_PlanChangeIssue_PP">
      <session-mapping key="isLoggedIn" value="GlobalVars.loggedIn" type="String"/>
      <audio>
        <if type="vxml" cond="(isLoggedIn == 'false' || isLoggedIn == false)">
          <prompt id="SH1220_out_01">
            <prompt-segments>
              <audiofile text="Unfortunately we wont be able to change your plan without your account PIN" src="SH1220_out_01.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="(GlobalVars.extensionAllowed == true)">
        <action next="SH1301_CheckContext_JDA"/>
        <else>
          <session-mapping key="GlobalVars.playFailureGoodbye" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
          <action next="SH1315_PaymentMethods_PP"/>
        </else>
      </if>
    </play-state>

    <decision-state id="SH1301_CheckContext_DS">
      <if cond="(GlobalVars.callType == 'extension')">
        <action next="SH1310_ApplyExtension_SD"/>
        <elseif cond="(GlobalVars.extensionAllowed == true)">
          <action next="SH1305_OfferExtensionYN_DM"/>
        </elseif>
        <else>
          <action next="SH1312_PayNowYN_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="SH1305_OfferExtensionYN_DM" type="YSNO">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <action label="true" next="SH1310_ApplyExtension_SD">
          <session-mapping key="GlobalVars.callType" expr="'extension'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="false" next="SH1312_PayNowYN_DM"/>
        <action label="operator">
          <session-mapping key="SH1305_operator_counter" expr="SH1305_operator_counter+1"/>
          <if cond="SH1305_operator_counter &lt; 2">
            <action next="SH1305_OfferExtensionYN_DM"/>
            <else>
              <audio>
                <prompt id="SH1305_operator_02">
                  <prompt-segments>
                    <audiofile text="Im sorry I cant take you to an agent right now" src="SH1305_operator_02.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
              <session-mapping key="GlobalVars.playFailureGoodbye" value="true" type="Boolean"/>
              <action next="SH1315_PaymentMethods_PP"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="(SH1305_operator_counter == 1)">
                <prompt id="SH1305_operator_01">
                  <prompt-segments>
                    <audiofile text="Our agents wont be able to give you other options But I can give you an extension right here Would you like to do that" src="SH1305_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1305_ini_02">
                    <prompt-segments>
                      <audiofile text="I can also check if you can get an extension on your payment Would you like to try *that*? " src="SH1305_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="(SH1305_operator_counter == 1)">
                <prompt id="SH1305_operator_01">
                  <prompt-segments>
                    <audiofile text="Our agents wont be able to give you other options But I can give you an extension right here Would you like to do that" src="SH1305_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1305_ini_02">
                    <prompt-segments>
                      <audiofile text="I can also check if you can get an extension on your payment Would you like to try *that*? " src="SH1305_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="(SH1305_operator_counter == 1)">
                <prompt id="SH1305_operator_01">
                  <prompt-segments>
                    <audiofile text="Our agents wont be able to give you other options But I can give you an extension right here Would you like to do that" src="SH1305_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1305_ini_02">
                    <prompt-segments>
                      <audiofile text="I can also check if you can get an extension on your payment Would you like to try *that*? " src="SH1305_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1305_nm2_01">
                <prompt-segments>
                  <audiofile text="Would you like to check if you can get an extension? Say 'yes' or press 1, or 'no' or press 2 If you're done, you can simply hang up " src="SH1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1305_nm2_01">
                <prompt-segments>
                  <audiofile text="Would you like to check if you can get an extension? Say 'yes' or press 1, or 'no' or press 2 If you're done, you can simply hang up " src="SH1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(callType == 'change_plan')">
                <prompt id="SH1305_ini_02">
                  <prompt-segments>
                    <audiofile text="I can also check if you can get an extension on your payment Would you like to try *that*? " src="SH1305_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1305_ini_03">
                    <prompt-segments>
                      <audiofile text="We can also give you a onetime extension on your payment Would you like to do that" src="SH1305_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1305_nm2_01">
                <prompt-segments>
                  <audiofile text="Would you like to check if you can get an extension? Say 'yes' or press 1, or 'no' or press 2 If you're done, you can simply hang up " src="SH1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1305_nm2_01">
                <prompt-segments>
                  <audiofile text="Would you like to check if you can get an extension? Say 'yes' or press 1, or 'no' or press 2 If you're done, you can simply hang up " src="SH1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="(SH1305_operator_counter == 1)">
                <prompt id="SH1305_operator_01">
                  <prompt-segments>
                    <audiofile text="Our agents wont be able to give you other options But I can give you an extension right here Would you like to do that" src="SH1305_operator_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1305_ini_02">
                    <prompt-segments>
                      <audiofile text="I can also check if you can get an extension on your payment Would you like to try *that*? " src="SH1305_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SH1305_OfferExtensionYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="SH1305_OfferExtensionYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1">
          <audio>
            <if type="vxml" cond="(collect.value.dm_root === 'true')">
              <prompt id="SH1305_out_01">
                <prompt-segments>
                  <audiofile text="Sure" src="SH1305_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <else>
                <prompt id="SH1305_out_02">
                  <prompt-segments>
                    <audiofile text="Alright" src="SH1305_out_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </else>
            </if>
          </audio>
        </successprompts>
        <successprompts count="2">
          <audio>
            <if type="vxml" cond="(collect.value.dm_root === 'true')">
              <prompt id="SH1305_out_01">
                <prompt-segments>
                  <audiofile text="Sure" src="SH1305_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <else>
                <prompt id="SH1305_out_02">
                  <prompt-segments>
                    <audiofile text="Alright" src="SH1305_out_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </else>
            </if>
          </audio>
        </successprompts>
        <successprompts count="3">
          <audio>
            <if type="vxml" cond="(collect.value.dm_root === 'true')">
              <prompt id="SH1305_out_01">
                <prompt-segments>
                  <audiofile text="Sure" src="SH1305_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <else>
                <prompt id="SH1305_out_02">
                  <prompt-segments>
                    <audiofile text="Alright" src="SH1305_out_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </else>
            </if>
          </audio>
        </successprompts>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="SH1310_ApplyExtension_SD">
      <gotodialog next="ApplyExtension_Main_Dialog"/>
      <action next="SH1310_ApplyExtension_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SH1310_ApplyExtension_SD_return_CS">
      <if cond="GlobalVars.extensionEntryPoint == 'failed_payment'">
        <action next="SH1315_PaymentMethods_PP"/>
        <else>
          <action next="SH1312_PayNowYN_DM"/>
        </else>
      </if>
    </custom-state>

    <dm-state id="SH1312_PayNowYN_DM" type="YSNO">
      <session-mapping key="extensionEntryPoint" value="GlobalVars.extensionEntryPoint" type="String"/>
      <success>
        <action label="true" next="SH1313_MakePayment_SD">
          <audio>
            <prompt id="SH1312_out_01">
              <prompt-segments>
                <audiofile text="Great" src="SH1312_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careSuspended'"/>
        </action>
        <action label="false" next="SH1315_PaymentMethods_PP">
          <audio>
            <prompt id="SH1312_out_02">
              <prompt-segments>
                <audiofile text="No problem" src="SH1312_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.suspendedOperatorRequest" value="true" type="Boolean"/>
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.operatorPaymentsReqCount" expr="GlobalVars.operatorPaymentsReqCount + 1"/>
          <submit expr="operatorSubmitFunction('SuspendedHandlingExtension_Main.dvxml','SH1312_PayNowYN_DM',SH1312_PayNowYN_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="(extensionEntryPoint == 'payment_amount')">
                <prompt id="SH1312_ini_01">
                  <prompt-segments>
                    <audiofile text="Would you like to continue with your payment?" src="SH1312_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1312_ini_02">
                    <prompt-segments>
                      <audiofile text="Would you like to make a payment now?" src="SH1312_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="(extensionEntryPoint == 'payment_amount')">
                <prompt id="SH1312_ini_01">
                  <prompt-segments>
                    <audiofile text="Would you like to continue with your payment?" src="SH1312_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1312_ini_02">
                    <prompt-segments>
                      <audiofile text="Would you like to make a payment now?" src="SH1312_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="(extensionEntryPoint == 'payment_amount')">
                <prompt id="SH1312_ini_01">
                  <prompt-segments>
                    <audiofile text="Would you like to continue with your payment?" src="SH1312_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1312_ini_02">
                    <prompt-segments>
                      <audiofile text="Would you like to make a payment now?" src="SH1312_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1312_nm2_01">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment now? Say 'yes' or press 1, or say 'no' or press 2" src="SH1312_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1312_nm2_01">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment now? Say 'yes' or press 1, or say 'no' or press 2" src="SH1312_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(extensionEntryPoint == 'payment_amount')">
                <prompt id="SH1312_ini_01">
                  <prompt-segments>
                    <audiofile text="Would you like to continue with your payment?" src="SH1312_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1312_ini_02">
                    <prompt-segments>
                      <audiofile text="Would you like to make a payment now?" src="SH1312_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1312_nm2_01">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment now? Say 'yes' or press 1, or say 'no' or press 2" src="SH1312_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SH1312_nm2_01">
                <prompt-segments>
                  <audiofile text="Would you like to make a payment now? Say 'yes' or press 1, or say 'no' or press 2" src="SH1312_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="(extensionEntryPoint == 'payment_amount')">
                <prompt id="SH1312_ini_01">
                  <prompt-segments>
                    <audiofile text="Would you like to continue with your payment?" src="SH1312_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="SH1312_ini_02">
                    <prompt-segments>
                      <audiofile text="Would you like to make a payment now?" src="SH1312_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SH1312_PayNowYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="SH1312_PayNowYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="SH1313_MakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="SH1313_MakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SH1313_MakePayment_SD_return_CS">
      <action next="SH1310_ApplyExtension_SD"/>
    </custom-state>

    <play-state id="SH1315_PaymentMethods_PP">
      <session-mapping key="extensionEntryPoint" value="GlobalVars.extensionEntryPoint" type="String"/>
      <audio>
        <if cond="(extensionEntryPoint == 'failed_payment')">
          <prompt id="SH1315_out_02">
            <prompt-segments>
              <audiofile text="When you've checked with your bank are ready to pay, you can call back here, or use the myMetro app You can also pay online or at one of our locations For a map, go to metrobyt-mobilecom" src="SH1315_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="SH1315_out_01">
              <prompt-segments>
                <audiofile text="When youre ready to make a payment you can call back here or use the myMetro app You can also pay online or get a map of our locations near you at metrobyt-mobilecom " src="SH1315_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <gotodialog next="SuspendedHandling_Main#SH1035_Goodbye_SD"/>
    </play-state>

  </dialog>
  