<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="activityCode" value="empty" type="String"/>
  <session-mapping key="blockingInd" value="empty" type="String"/>
  <session-mapping key="campaignId" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="GlobalVars.tag" value="cancel-service_transfer" type="string"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="boolean"/>
  <session-mapping key="tag" value="unlock-sim" type="string"/>
  <session-mapping key="GlobalVars.restrictionsUpdateSuccessfull" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.fromESNSwapDialog" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.simSwapRestricted" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.unblockingNotAllowed" value="true" type="string"/>
  <session-mapping key="GlobalVars.portOutRestricted" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.hitRestrictActivitiesSuccess" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.changedBothSP" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.visited_SE2120" value="true" type="boolean"/>
  <session-mapping key="simSwapRestricted" value="true" type="boolean"/>
  <session-mapping key="portOutRestricted" value="true" type="boolean"/>
  <session-mapping key="isOnFamilyPlan" value="true" type="boolean"/>
  <session-mapping key="visited_SE2130" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.operatorReqCount_SE2156" value="1" type="integer"/>
  <session-mapping key="visited_SE2156_operator" value="false" type="boolean"/>
  <session-mapping key="saidRepeatSE2156" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.secondTimeSE2166" value="false" type="boolean"/>
</session-mappings>
