<states-library xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../xsd/states.xsd">
	<dialog id="Welcome">
		<play-state id="dlhd0002_Welcome_PP">
			<audio>
				<prompt id="dlhd0002_Welcome_PP_out_01">
					<prompt-segments>
						<audiofile text="Welcome to Deltas Information Technology Service Desk" src="tcc/dlhd0002_Welcome_PP_out_01.wav"/>
					</prompt-segments>
				</prompt>
			</audio>
			<audio>
				<prompt id="dlhd0002_Welcome_PP_sil_01">
					<prompt-segments>
						<audiofile text="..." src="tcc/dlhd0002_Welcome_PP_sil_01.wav"/>
					</prompt-segments>
				</prompt>
			</audio>
			<gotodialog next="CollectPPRNumber"/>
		</play-state>
	</dialog>
</states-library>