<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Login_Security_Dialog">
    <decision-state id="LG1110_CheckAccountDetails_DS">
      <if cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ9')">
        <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'not_authenticated_high_security_account')"/>
        <action next="LG1215_GoToCallTransfer_SD"/>
        <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ8')">
          <action next="LG1206_GoToGoodbye_SD"/>
        </elseif>
        <elseif cond="(GlobalVars.tag == 'request-extension')">
          <action next="getReturnLink()"/>
        </elseif>
        <elseif cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended'">
          <if cond="GlobalVars.callType == 'make_pmt'">
            <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careSuspended'"/>
          </if>
          <action next="LG1113_GetOTP_SD"/>
        </elseif>
        <elseif cond="GlobalVars.securityRequired == true">
          <action next="LG1113_GetOTP_SD"/>
        </elseif>
        <else>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="LG1113_GetOTP_SD">
      <gotodialog next="TwoFactorAuth_Main_Dialog"/>
      <action next="LG1113_GetOTP_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="LG1113_GetOTP_SD_Return_CS">
      <action next="LG1115_GetSecurityCode_DM"/>
    </custom-state>

    <dm-state id="LG1115_GetSecurityCode_DM" type="CUST">
      <session-mapping key="acceptedBCR" value="GlobalVars.acceptedBCR" type="String"/>
      <session-mapping key="shortSecurityCodePrompt" value="GlobalVars.shortSecurityCodePrompt" type="String"/>
      <session-mapping key="playTransitionalPINprompt" value="GlobalVars.playTransitionalPINprompt" type="String"/>
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="collectedMDNUpfront" value="GlobalVars.collectedMDNUpfront" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="lastPinTry" value="GlobalVars.lastPinTry" type="String"/>
      <session-mapping key="reentry" value="GlobalVars.LG1115reentry == undefined ? false : GlobalVars.LG1115reentry" type="String"/>
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <success>
        <session-mapping key="GlobalVars.playTransitionalPINprompt" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.LG1115reentry" value="true" type="Boolean"/>
        <action label="wait">
          <action next="LG1120_SecurityCodeWait_DM"/>
        </action>
        <action label="dont_know">
          <audio>
            <prompt id="LG1115_out_01">
              <prompt-segments>
                <audiofile text="No problem" src="LG1115_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.dontKnowPIN" value="true" type="Boolean"/>
          <action next="LG1301_CheckSQEligibility_JDA"/>
        </action>
        <action label="default" next="LG1124_Authenticate_DB_DA">
          <session-mapping key="pin" expr="LG1115_GetSecurityCode_DM.returnvalue"/>
          <session-mapping key="pinAttempts" expr="pinAttempts+1"/>
          <session-mapping key="GlobalVars.verificationType" expr="'pin'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="LG1115_GetSecurityCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.securityCode" expr="LG1115_GetSecurityCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.cti_PIN" expr="LG1115_GetSecurityCode_DM.returnvalue"/>
        </action>
        <action label="operator">
          <if cond="(GlobalVars.callType == undefined) &amp;&amp;(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')">
            <session-mapping key="GlobalVars.suspendedOperatorRequest" value="true" type="Boolean"/>
          </if>
          <session-mapping key="GlobalVars.playTransitionalPINprompt" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.transferFrom" expr="'LoginSD_XR'"/>
          <submit expr="operatorSubmitFunction('Login_Security.dvxml','LG1115_GetSecurityCode_DM',LG1115_GetSecurityCode_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration reco_suppress_logs="true" highconfidencelevel="0.990">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="shortSecurityCodePrompt == true">
                <prompt id="LG1115_ini_01" cond="accountPinToggleOn == true">
                  <prompt-segments>
                    <audiofile text="Please say or enter your 6-to-15-digit account PIN, one digit at a time You can also say 'wait a minute'" src="LG1115_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="playTransitionalPINprompt == true &amp;&amp; pinAttempts == 0 &amp;&amp; reentry == false">
                  <prompt id="LG1115_ini_03" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="And now I need your 6-to-15-digit account PIN Say or enter it now" src="LG1115_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="callType == 'switch_lines'">
                  <prompt id="LG1115_ini_05" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="Just so you know, Metro Cares about your security! So first, I'll need the 6-to-15-digit account PIN for the phone you're *calling from* Say or enter it now " src="LG1115_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="playTransitionalPINprompt != true &amp;&amp; (aniMatch == 'true' || aniMatch == true || collectedMDNUpfront == true) &amp;&amp; callType == 'switch_phone' &amp;&amp; pinAttempts == 0 &amp;&amp; ! reentry">
                  <prompt id="LG1115_ini_08" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="Let's start with your 6-to-15-digit account PIN Say or enter it now  " src="LG1115_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="acceptedBCR == true">
                  <prompt id="LG1115_ini_10" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="What's your 6-to-15-digit account PIN?" src="LG1115_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="LG1115_ini_12" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="Now I need your 6-to-15-digit account PIN" src="LG1115_ini_12.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="shortSecurityCodePrompt == true">
                <prompt id="LG1115_ini_02" cond="accountPinToggleOn != true">
                  <prompt-segments>
                    <audiofile text="Please say or enter your 8-digit account PIN, one digit at a time You can also say 'wait a minute'  " src="LG1115_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="playTransitionalPINprompt == true &amp;&amp; pinAttempts == 0 &amp;&amp; reentry == false">
                  <prompt id="LG1115_ini_04" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="And now I need your 8-digit account PIN Say or enter it now" src="LG1115_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="callType == 'switch_lines'">
                  <prompt id="LG1115_ini_06" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="Just so you know, Metro Cares about your security! So first, I'll need the 8-digit account PIN for the phone you're *calling from* Say or enter it now" src="LG1115_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="playTransitionalPINprompt != true &amp;&amp; (aniMatch == 'true' || aniMatch == true || collectedMDNUpfront == true) &amp;&amp; callType == 'switch_phone' &amp;&amp; pinAttempts == 0 &amp;&amp; ! reentry">
                  <prompt id="LG1115_ini_09" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="Let's start with your 8-digit account PIN Say or enter it now " src="LG1115_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="acceptedBCR == true">
                  <prompt id="LG1115_ini_11" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="What's your 8-digit account PIN?  " src="LG1115_ini_11.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="LG1115_ini_13" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="Now I need your 8-digit account PIN" src="LG1115_ini_13.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_3000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_3000ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="callType == 'make_pmt'">
                <prompt id="LG1115_ini_07">
                  <prompt-segments>
                    <audiofile text="You can also say 'wait a minute', or 'I don't know it'" src="LG1115_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="LG1115_GetSecurityCode_DM_reinvoke"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="shortSecurityCodePrompt == true">
                <prompt id="LG1115_ini_01" cond="accountPinToggleOn == true">
                  <prompt-segments>
                    <audiofile text="Please say or enter your 6-to-15-digit account PIN, one digit at a time You can also say 'wait a minute'" src="LG1115_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="playTransitionalPINprompt == true &amp;&amp; pinAttempts == 0 &amp;&amp; reentry == false">
                  <prompt id="LG1115_ini_03" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="And now I need your 6-to-15-digit account PIN Say or enter it now" src="LG1115_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="callType == 'switch_lines'">
                  <prompt id="LG1115_ini_05" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="Just so you know, Metro Cares about your security! So first, I'll need the 6-to-15-digit account PIN for the phone you're *calling from* Say or enter it now " src="LG1115_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="playTransitionalPINprompt != true &amp;&amp; (aniMatch == 'true' || aniMatch == true || collectedMDNUpfront == true) &amp;&amp; callType == 'switch_phone' &amp;&amp; pinAttempts == 0 &amp;&amp; ! reentry">
                  <prompt id="LG1115_ini_08" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="Let's start with your 6-to-15-digit account PIN Say or enter it now  " src="LG1115_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="acceptedBCR == true">
                  <prompt id="LG1115_ini_10" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="What's your 6-to-15-digit account PIN?" src="LG1115_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="LG1115_ini_12" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="Now I need your 6-to-15-digit account PIN" src="LG1115_ini_12.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="shortSecurityCodePrompt == true">
                <prompt id="LG1115_ini_02" cond="accountPinToggleOn != true">
                  <prompt-segments>
                    <audiofile text="Please say or enter your 8-digit account PIN, one digit at a time You can also say 'wait a minute'  " src="LG1115_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="playTransitionalPINprompt == true &amp;&amp; pinAttempts == 0 &amp;&amp; reentry == false">
                  <prompt id="LG1115_ini_04" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="And now I need your 8-digit account PIN Say or enter it now" src="LG1115_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="callType == 'switch_lines'">
                  <prompt id="LG1115_ini_06" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="Just so you know, Metro Cares about your security! So first, I'll need the 8-digit account PIN for the phone you're *calling from* Say or enter it now" src="LG1115_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="playTransitionalPINprompt != true &amp;&amp; (aniMatch == 'true' || aniMatch == true || collectedMDNUpfront == true) &amp;&amp; callType == 'switch_phone' &amp;&amp; pinAttempts == 0 &amp;&amp; ! reentry">
                  <prompt id="LG1115_ini_09" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="Let's start with your 8-digit account PIN Say or enter it now " src="LG1115_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="acceptedBCR == true">
                  <prompt id="LG1115_ini_11" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="What's your 8-digit account PIN?  " src="LG1115_ini_11.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="LG1115_ini_13" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="Now I need your 8-digit account PIN" src="LG1115_ini_13.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_3000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_3000ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="callType == 'make_pmt'">
                <prompt id="LG1115_ini_07">
                  <prompt-segments>
                    <audiofile text="You can also say 'wait a minute', or 'I don't know it'" src="LG1115_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="LG1115_nm1_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="LG1115_nm1_01.wav" text="Please say or enter your 6-to-15-digit Metro account PIN  If you need to find it, say 'wait a minute' 15 seconds  You can also say 'I don't' know it' "/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1115_nm1_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="LG1115_nm1_02.wav" text="Please say or enter your 8-digit Metro account PIN  If you need to find it, say 'wait a minute' 15 seconds  You can also say 'I don't' know it' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1115_nm2_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="LG1115_nm2_01.wav" text="You selected a Metro account PIN when you first got your phone  Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it' "/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1115_nm2_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="LG1115_nm2_02.wav" text="You selected a Metro account PIN when you first got your phone  Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it'  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1115_nm2_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="LG1115_nm2_01.wav" text="You selected a Metro account PIN when you first got your phone  Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it' "/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1115_nm2_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="LG1115_nm2_02.wav" text="You selected a Metro account PIN when you first got your phone  Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it'  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1115_nm1_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="LG1115_nm1_01.wav" text="Please say or enter your 6-to-15-digit Metro account PIN  If you need to find it, say 'wait a minute' 15 seconds  You can also say 'I don't' know it' "/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1115_nm1_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="LG1115_nm1_02.wav" text="Please say or enter your 8-digit Metro account PIN  If you need to find it, say 'wait a minute' 15 seconds  You can also say 'I don't' know it' "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1115_nm2_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="LG1115_nm2_01.wav" text="You selected a Metro account PIN when you first got your phone  Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it' "/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1115_nm2_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="LG1115_nm2_02.wav" text="You selected a Metro account PIN when you first got your phone  Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it'  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1115_nm2_01" cond="accountPinToggleOn == true">
                <prompt-segments>
                  <audiofile src="LG1115_nm2_01.wav" text="You selected a Metro account PIN when you first got your phone  Please enter your 6-to-15-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it' "/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1115_nm2_02" cond="accountPinToggleOn != true">
                <prompt-segments>
                  <audiofile src="LG1115_nm2_02.wav" text="You selected a Metro account PIN when you first got your phone  Please enter your 8-digit account PIN on your phone's keypad  If you need a minute to find it, say 'wait a minute'  Or if you can't find your security code account PIN, say 'I don't' know it'  "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="shortSecurityCodePrompt == true">
                <prompt id="LG1115_ini_01" cond="accountPinToggleOn == true">
                  <prompt-segments>
                    <audiofile text="Please say or enter your 6-to-15-digit account PIN, one digit at a time You can also say 'wait a minute'" src="LG1115_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="playTransitionalPINprompt == true &amp;&amp; pinAttempts == 0 &amp;&amp; reentry == false">
                  <prompt id="LG1115_ini_03" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="And now I need your 6-to-15-digit account PIN Say or enter it now" src="LG1115_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="callType == 'switch_lines'">
                  <prompt id="LG1115_ini_05" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="Just so you know, Metro Cares about your security! So first, I'll need the 6-to-15-digit account PIN for the phone you're *calling from* Say or enter it now " src="LG1115_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="playTransitionalPINprompt != true &amp;&amp; (aniMatch == 'true' || aniMatch == true || collectedMDNUpfront == true) &amp;&amp; callType == 'switch_phone' &amp;&amp; pinAttempts == 0 &amp;&amp; ! reentry">
                  <prompt id="LG1115_ini_08" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="Let's start with your 6-to-15-digit account PIN Say or enter it now  " src="LG1115_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="acceptedBCR == true">
                  <prompt id="LG1115_ini_10" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="What's your 6-to-15-digit account PIN?" src="LG1115_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="LG1115_ini_12" cond="accountPinToggleOn == true">
                    <prompt-segments>
                      <audiofile text="Now I need your 6-to-15-digit account PIN" src="LG1115_ini_12.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="shortSecurityCodePrompt == true">
                <prompt id="LG1115_ini_02" cond="accountPinToggleOn != true">
                  <prompt-segments>
                    <audiofile text="Please say or enter your 8-digit account PIN, one digit at a time You can also say 'wait a minute'  " src="LG1115_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="playTransitionalPINprompt == true &amp;&amp; pinAttempts == 0 &amp;&amp; reentry == false">
                  <prompt id="LG1115_ini_04" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="And now I need your 8-digit account PIN Say or enter it now" src="LG1115_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="callType == 'switch_lines'">
                  <prompt id="LG1115_ini_06" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="Just so you know, Metro Cares about your security! So first, I'll need the 8-digit account PIN for the phone you're *calling from* Say or enter it now" src="LG1115_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="playTransitionalPINprompt != true &amp;&amp; (aniMatch == 'true' || aniMatch == true || collectedMDNUpfront == true) &amp;&amp; callType == 'switch_phone' &amp;&amp; pinAttempts == 0 &amp;&amp; ! reentry">
                  <prompt id="LG1115_ini_09" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="Let's start with your 8-digit account PIN Say or enter it now " src="LG1115_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="acceptedBCR == true">
                  <prompt id="LG1115_ini_11" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="What's your 8-digit account PIN?  " src="LG1115_ini_11.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="LG1115_ini_13" cond="accountPinToggleOn != true">
                    <prompt-segments>
                      <audiofile text="Now I need your 8-digit account PIN" src="LG1115_ini_13.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_3000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_3000ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="callType == 'make_pmt'">
                <prompt id="LG1115_ini_07">
                  <prompt-segments>
                    <audiofile text="You can also say 'wait a minute', or 'I don't know it'" src="LG1115_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="LG1115_GetSecurityCode_DM.grxml" count="1"/>
          <dtmfgrammars filename="LG1115_GetSecurityCode_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration reco_suppress_logs="true" highconfidencelevel="0.990">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlaySecurityCode"/>
                  <param name="intonation" value="f"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="LG1120_SecurityCodeWait_DM" type="CUST">
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <success>
        <action label="ready">
          <audio>
            <prompt id="LG1120_out_01">
              <prompt-segments>
                <audiofile text="Let s continue" src="LG1120_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="LG1115_GetSecurityCode_DM"/>
        </action>
        <action label="dont_know">
          <audio>
            <prompt id="LG1120_out_02">
              <prompt-segments>
                <audiofile text="No problem" src="LG1120_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.dontKnowPIN" value="true" type="Boolean"/>
          <action next="LG1301_CheckSQEligibility_JDA"/>
        </action>
        <action label="default">
          <session-mapping key="pin" expr="LG1120_SecurityCodeWait_DM.returnvalue"/>
          <session-mapping key="GlobalVars.verificationType" expr="'pin'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="LG1120_SecurityCodeWait_DM.returnvalue"/>
          <action next="LG1124_Authenticate_DB_DA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1120_ini_01">
                  <prompt-segments>
                    <audiofile text="No problem I ll wait Once you re ready with your 6-to-15-digit account PIN, say Continue  When you re ready, say  Continue , or press 1,  I don t know it  or press 2  You can say  Continue , or press 1,  I don t know it  or press 2  If you have your 6-to-15-digit account PIN, say Continue  or press 1 You can also say  I don t know it  or press 2  If you re ready,  say  Continue  or press 1 Otherwise say  I don t know it , or press 2  You can say  Continue , press 1,  I don t know it  or press 2 Hmmm I seem to be having some trouble" src="LG1120_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1120_ini_02">
                    <prompt-segments>
                      <audiofile text=" No problem I'll wait Once you're ready with your 8-digit account PIN, say Continue When you're ready, say 'Continue', or press 1, 'I don't know it' or press 2 You can say 'Continue', or press 1, 'I don't know it' or press 2 If you have your 6-to-15-digit account PIN, say Continue' or press 1 You can also say 'I don't know it' or press 2 If you're ready,  say 'Continue' or press 1 Otherwise say 'I don't know it', or press 2 You can say 'Continue', press 1, 'I don't know it' or press 2 Hmmm I seem to be having some trouble " src="LG1120_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1120_ini_01">
                  <prompt-segments>
                    <audiofile text="No problem I ll wait Once you re ready with your 6-to-15-digit account PIN, say Continue  When you re ready, say  Continue , or press 1,  I don t know it  or press 2  You can say  Continue , or press 1,  I don t know it  or press 2  If you have your 6-to-15-digit account PIN, say Continue  or press 1 You can also say  I don t know it  or press 2  If you re ready,  say  Continue  or press 1 Otherwise say  I don t know it , or press 2  You can say  Continue , press 1,  I don t know it  or press 2 Hmmm I seem to be having some trouble" src="LG1120_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1120_ini_02">
                    <prompt-segments>
                      <audiofile text=" No problem I'll wait Once you're ready with your 8-digit account PIN, say Continue When you're ready, say 'Continue', or press 1, 'I don't know it' or press 2 You can say 'Continue', or press 1, 'I don't know it' or press 2 If you have your 6-to-15-digit account PIN, say Continue' or press 1 You can also say 'I don't know it' or press 2 If you're ready,  say 'Continue' or press 1 Otherwise say 'I don't know it', or press 2 You can say 'Continue', press 1, 'I don't know it' or press 2 Hmmm I seem to be having some trouble " src="LG1120_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1120_ini_01">
                  <prompt-segments>
                    <audiofile text="No problem I ll wait Once you re ready with your 6-to-15-digit account PIN, say Continue  When you re ready, say  Continue , or press 1,  I don t know it  or press 2  You can say  Continue , or press 1,  I don t know it  or press 2  If you have your 6-to-15-digit account PIN, say Continue  or press 1 You can also say  I don t know it  or press 2  If you re ready,  say  Continue  or press 1 Otherwise say  I don t know it , or press 2  You can say  Continue , press 1,  I don t know it  or press 2 Hmmm I seem to be having some trouble" src="LG1120_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1120_ini_02">
                    <prompt-segments>
                      <audiofile text=" No problem I'll wait Once you're ready with your 8-digit account PIN, say Continue When you're ready, say 'Continue', or press 1, 'I don't know it' or press 2 You can say 'Continue', or press 1, 'I don't know it' or press 2 If you have your 6-to-15-digit account PIN, say Continue' or press 1 You can also say 'I don't know it' or press 2 If you're ready,  say 'Continue' or press 1 Otherwise say 'I don't know it', or press 2 You can say 'Continue', press 1, 'I don't know it' or press 2 Hmmm I seem to be having some trouble " src="LG1120_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="LG1120_SecurityCodeWait_DM.grxml" count="1"/>
          <dtmfgrammars filename="LG1120_SecurityCodeWait_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="700ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="LG1124_Authenticate_DB_DA">
      <session-mapping key="accessToken" value="GlobalVars.accessToken" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="verificationType" value="GlobalVars.verificationType" type="String"/>
      <session-mapping key="verificationValue" value="GlobalVars.verificationValue" type="String"/>
      <session-mapping key="pin" value="GlobalVars.verificationValue" type="String"/>
      <data-access id="Authenticate" classname="com.nuance.metro.dataaccess.ValidatePinForAuthenticate">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="pin" mask="true"/>
          <input-variable name="accessToken" mask="false"/>
          <input-variable name="verificationType"/>
          <input-variable name="verificationValue" mask="true"/>
          <input-variable name="sessionId"/>
          <input-variable name="providerId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="JWTToken"/>
          <output-variable name="expiresIn"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="Authenticate.status == 'FAILURE'">
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          <gotodialog next="CallTransfer_Main_Dialog"/>
          <elseif cond="Authenticate.acctLocked == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.acctLocked" value="true" type="Boolean"/>
            <action next="LG1126_PINMaxErrors_PP"/>
          </elseif>
          <elseif cond="Authenticate.onePinTryRemaining == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.onePinTryRemaining" value="true" type="Boolean"/>
            <action next="LG1125_CheckSecurityCodeMatch_JDA"/>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <action next="LG1125_CheckSecurityCodeMatch_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="LG1125_CheckSecurityCodeMatch_DS">
      <if cond="GlobalVars.Authenticate &amp;&amp; GlobalVars.Authenticate.status != 'SUCCESS'">
        <if cond=" GlobalVars.acctLocked != true &amp;&amp; GlobalVars.onePinTryRemaining == true ">
          <action next="LG1127_AskResetInformation_DM"/>
          <elseif cond="GlobalVars.acctLocked != true &amp;&amp; GlobalVars.onePinTryRemaining != true ">
            <action next="LG1115_GetSecurityCode_DM"/>
          </elseif>
        </if>
        <else>
          <session-mapping key="GlobalVars.loggedIn" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'authenticated_by_pin')"/>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </decision-state>

    <play-state id="LG1126_PINMaxErrors_PP">
      <audio>
        <prompt id="LG1126_out_01">
          <prompt-segments>
            <audiofile text="That's still not right," src="LG1126_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="LG1301_CheckSQEligibility_JDA"/>
    </play-state>

    <dm-state id="LG1127_AskResetInformation_DM" type="YSNO">
      <session-mapping key="requestOperatorUnidentified" value="GlobalVars.requestOperatorUnidentified" type="String"/>
      <session-mapping key="IU1051saidOperator" value="GlobalVars.IU1051saidOperator" type="String"/>
      <success>
        <action label="true">
          <action next="LG1130_GoToAccountPinReset_SD"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.lastPinTry" value="true" type="Boolean"/>
          <action next="LG1115_GetSecurityCode_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="LG1127_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, that still didnt match  Would you like information on how to reset your pin online? " src="LG1127_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="LG1127_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, that still didnt match  Would you like information on how to reset your pin online? " src="LG1127_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="LG1127_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes  Otherwise say No" src="LG1127_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1127_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1  If you'd like to try your pin once more say 'no' or press 2" src="LG1127_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1127_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1  If you'd like to try your pin once more say 'no' or press 2" src="LG1127_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1127_nm1_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes  Otherwise say No" src="LG1127_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1127_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1  If you'd like to try your pin once more say 'no' or press 2" src="LG1127_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1127_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like information on how to reset your PIN online say Yes or press 1  If you'd like to try your pin once more say 'no' or press 2" src="LG1127_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="LG1127_AskResetInformation_DM.grxml" count="1"/>
          <dtmfgrammars filename="LG1127_AskResetInformation_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="LG1205_GoToStoreLocator_SD">
      <session-mapping key="GlobalVars.storeLocatorReason" expr="'sign up'"/>
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="Login_TerminalSDReturn_CS"/>
    </subdialog-state>
    <subdialog-state id="LG1206_GoToGoodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </subdialog-state>
    <subdialog-state id="LG1210_GoToDeviceHandling_SD">
      <gotodialog next="DeviceHandling_Main_Dialog"/>
      <action next="Login_TerminalSDReturn_CS"/>
    </subdialog-state>
    <subdialog-state id="LG1215_GoToCallTransfer_SD">
      <if cond="language == 'en-US'">
        <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
        <else>
          <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
        </else>
      </if>
      <session-mapping key="GlobalVars.transferFrom" expr="'LoginSD_XR'"/>
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="Login_TerminalSDReturn_CS"/>
    </subdialog-state>
    <subdialog-state id="LG1225_GoToMainMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
      <action next="Login_TerminalSDReturn_CS"/>
    </subdialog-state>
    <custom-state id="Login_TerminalSDReturn_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <decision-state id="LG1301_CheckSQEligibility_DS">
      <if cond="GlobalVars.guestPaymentReattemptLogin == true">
        <action next="LG1215_GoToCallTransfer_SD"/>
        <elseif cond="GlobalVars.dontKnowPIN == true &amp;&amp; GlobalVars.callType == 'make_pmt' &amp;&amp; (GlobalVars.GetBCSParameters.care_enable_guest_payment == true || GlobalVars.GetBCSParameters.care_enable_guest_payment == 'true')">
          <action next="LG1605_GuestPaymentTransition_PP"/>
        </elseif>
        <else>
          <if cond="GlobalVars.suspendedRatePlanChange == true">
            <action next="getReturnLink()"/>
            <else>
              <if cond="GlobalVars.lastPinTry == true">
                <action next="LG1215_GoToCallTransfer_SD"/>
                <else>
                  <action next="LG1130_GoToAccountPinReset_SD"/>
                </else>
              </if>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <decision-state id="LG1401_CheckContext_DS">
      <if cond="(GlobalVars.GetAccountDetails == null || GlobalVars.GetAccountDetails == '' || GlobalVars.GetAccountDetails == undefined)">
        <action next="LG1405_DSGCollectMDN_DM"/>
        <else>
          <action next="LG1403_GetOTP_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="LG1403_GetOTP_SD">
      <gotodialog next="TwoFactorAuth_Main_Dialog"/>
      <action next="LG1403_GetOTP_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="LG1403_GetOTP_SD_Return_CS">
      <action next="LG1505_DSGCollectPIN_DM"/>
    </custom-state>

    <dm-state id="LG1405_DSGCollectMDN_DM" type="CUST">
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <success>
        <action label="default">
          <session-mapping key="MDN" expr="LG1405_DSGCollectMDN_DM.returnvalue"/>
          <action next="LG1410_DSGGetAccountDetails_DB_DA"/>
        </action>
        <action label="operator">
          <action next="LG1215_GoToCallTransfer_SD"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration inputmodes="dtmf" highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="LG1405_ini_01">
                <prompt-segments>
                  <audiofile text="First, please enter the 10-digit phone number " src="LG1405_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="LG1405_DSGCollectMDN_DM_reinvoke"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="LG1405_ini_01">
                <prompt-segments>
                  <audiofile text="First, please enter the 10-digit phone number " src="LG1405_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="LG1405_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the phone number for the account you want to work with, including the area code " src="LG1405_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1405_nm2_01">
                <prompt-segments>
                  <audiofile text="Please enter the phone number for the account you want to work with, including the area code " src="LG1405_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1405_nm2_01">
                <prompt-segments>
                  <audiofile text="Please enter the phone number for the account you want to work with, including the area code " src="LG1405_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1405_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the phone number for the account you want to work with, including the area code " src="LG1405_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1405_nm2_01">
                <prompt-segments>
                  <audiofile text="Please enter the phone number for the account you want to work with, including the area code " src="LG1405_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1405_nm2_01">
                <prompt-segments>
                  <audiofile text="Please enter the phone number for the account you want to work with, including the area code " src="LG1405_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="LG1405_ini_01">
                <prompt-segments>
                  <audiofile text="First, please enter the 10-digit phone number " src="LG1405_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <dtmfgrammars filename="LG1405_DSGCollectMDN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="2000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1">
          <audio>
            <prompt id="LG1405_out_01">
              <prompt-segments>
                <audiofile text="Looking that up Please listen carefully " src="LG1405_out_01.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </successprompts>
        <successprompts count="2">
          <audio>
            <prompt id="LG1405_out_01">
              <prompt-segments>
                <audiofile text="Looking that up Please listen carefully " src="LG1405_out_01.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </successprompts>
        <successprompts count="3">
          <audio>
            <prompt id="LG1405_out_01">
              <prompt-segments>
                <audiofile text="Looking that up Please listen carefully " src="LG1405_out_01.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </successprompts>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.450">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="LG1410_DSGGetAccountDetails_DB_DA">
      <session-mapping key="mdn" value="MDN" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <data-access id="GetSubscriberDetails" classname="com.nuance.metro.dataaccess.GetSubscriberDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="languageCode"/>
          <input-variable name="sessionId"/>
          <input-variable name="imei"/>
          <input-variable name="clearCache"/>
          <input-variable name="multiLineDetails"/>
        </inputs>
        <outputs>
          <output-variable name="mdn"/>
          <output-variable name="accountNumber" mask="true"/>
          <output-variable name="active"/>
          <output-variable name="serialNumber"/>
          <output-variable name="features"/>
          <output-variable name="thirdPartyFeatures"/>
          <output-variable name="payDate"/>
          <output-variable name="taxInclusive"/>
          <output-variable name="accountStatus"/>
          <output-variable name="dueImmediatelyAmount"/>
          <output-variable name="highSecurity"/>
          <output-variable name="ratePlan"/>
          <output-variable name="marketID"/>
          <output-variable name="language"/>
          <output-variable name="isAutopayEnabled"/>
          <output-variable name="hasPhoneInsurance"/>
          <output-variable name="balance"/>
          <output-variable name="isDeviceAllowed"/>
          <output-variable name="isSuspended"/>
          <output-variable name="accountType"/>
          <output-variable name="validAccount"/>
          <output-variable name="roamingBucket"/>
          <output-variable name="paymentStatus"/>
          <output-variable name="ratePlanAttributesMap"/>
          <output-variable name="networkType"/>
          <output-variable name="securityQuestionCode"/>
          <output-variable name="hasEWallet"/>
          <output-variable name="eligibleForBillCycleReset"/>
          <output-variable name="billCycleLength"/>
          <output-variable name="firstName"/>
          <output-variable name="lastName"/>
          <output-variable name="eligibleForUnhotlineCheck"/>
          <output-variable name="securityQuestionString"/>
          <output-variable name="iccid"/>
          <output-variable name="imei"/>
          <output-variable name="hasAutopayPending"/>
          <output-variable name="insuranceCode"/>
          <output-variable name="subscriberId"/>
          <output-variable name="usedESNAllowed"/>
          <output-variable name="emailVerificationStatus"/>
          <output-variable name="emailAddress"/>
          <output-variable name="currentPromo"/>
          <output-variable name="futurePromo"/>
          <output-variable name="featureCodes"/>
          <output-variable name="superRegion"/>
          <output-variable name="accountFutureRequestInd"/>
          <output-variable name="subscriberFuturePricePlanInd"/>
          <output-variable name="ratePlanPrice"/>
          <output-variable name="currentPlanPromptURL"/>
          <output-variable name="currentPlanPromptTTS"/>
          <output-variable name="familyPlanIndicator"/>
          <output-variable name="topupEligibility"/>
          <output-variable name="customerTreatmentLevel"/>
          <output-variable name="HasHighSpeedData"/>
          <output-variable name="includedFeatures"/>
          <output-variable name="hasDataMaximizer"/>
          <output-variable name="eligibleForUpgrade"/>
          <output-variable name="upgradeEligibilityDate"/>
          <output-variable name="arBalance"/>
          <output-variable name="multilineList"/>
          <output-variable name="planMedialPromptURL"/>
          <output-variable name="planMedialPromptTTS"/>
          <output-variable name="hasDefaultAccountValues"/>
          <output-variable name="zipCode" mask="true"/>
          <output-variable name="accountRestrictIndicator"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetSubscriberDetails &amp;&amp; GetSubscriberDetails.validAccount">
          <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
          <session-mapping key="GlobalVars.trn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.unCoopMaxRequest" expr="GetSubscriberDetails.unCoopMaxRequest"/>
          <session-mapping key="GlobalVars.coopMaxRequest" expr="GetSubscriberDetails.coopMaxRequest"/>
          <session-mapping key="ActivationTable.OLD_MDN_NUM" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.networkType" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="ActivationTable.NETWORK_TYPE" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="GlobalVars.marketID" expr="GetSubscriberDetails.marketID"/>
          <session-mapping key="GlobalVars.zipCode" expr="GetSubscriberDetails.zipCode"/>
          <session-mapping key="GlobalVars.mdn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.isOnFamilyPlan" expr="GetSubscriberDetails.isOnFamilyPlan"/>
          <session-mapping key="GlobalVars.eligibleForUpgrade" expr="GetSubscriberDetails.isCurrentlyEligibleForDeviceUpgrade"/>
          <session-mapping key="GlobalVars.upgradeEligibilityDate" expr="GetSubscriberDetails.upgradeEligibilityDate"/>
          <session-mapping key="GlobalVars.accountStatus" expr="GetSubscriberDetails.accountStatus"/>
          <session-mapping key="GlobalVars.accountFutureRequestInd" expr="GetSubscriberDetails.accountFutureRequestInd"/>
          <session-mapping key="GlobalVars.subscriberFuturePricePlanInd" expr="GetSubscriberDetails.subscriberFuturePricePlanInd"/>
          <if cond="(GetSubscriberDetails.accountStatus == 'active' || GetSubscriberDetails.accountStatus == 'suspended')">
            <session-mapping key="GlobalVars.aniMatch" value="true" type="Boolean"/>
          </if>
        </if>
        <action next="LG1415_DSGCheckAccountValidated_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="LG1415_DSGCheckAccountValidated_DS">
      <if cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.validAccount">
        <session-mapping key="GlobalVars.cti_MDN" expr="GlobalVars.mdn"/>
        <action next="getReturnLink()"/>
        <else>
          <if cond="invalidMDNCount == 1">
            <session-mapping key="invalidMDNCount" expr="2"/>
            <action next="LG1420_DSGMDNMaxAttempts_PP"/>
            <else>
              <session-mapping key="invalidMDNCount" expr="1"/>
              <action next="LG1405_DSGCollectMDN_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="LG1420_DSGMDNMaxAttempts_PP">
      <audio>
        <prompt id="LG1420_out_01">
          <prompt-segments>
            <audiofile text="This *still* doesn't match any open accounts" src="LG1420_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="LG1420_out_02">
          <prompt-segments>
            <audiofile text="I'll take you to an agent for next steps One moment " src="LG1420_out_02.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.playTransferMessage" value="false" type="Boolean"/>
      <action next="LG1215_GoToCallTransfer_SD"/>
    </play-state>

    <dm-state id="LG1505_DSGCollectPIN_DM" type="CUST">
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <success>
        <action label="default">
          <session-mapping key="pin" expr="LG1505_DSGCollectPIN_DM.returnvalue"/>
          <session-mapping key="GlobalVars.verificationType" expr="'pin'"/>
          <session-mapping key="GlobalVars.verificationValue" expr="LG1505_DSGCollectPIN_DM.returnvalue"/>
          <session-mapping key="GlobalVars.cti_PIN" expr="LG1505_DSGCollectPIN_DM.returnvalue"/>
          <action next="LG1509_Authenticate_DB_DA"/>
        </action>
        <action label="operator">
          <action next="LG1215_GoToCallTransfer_SD"/>
        </action>
      </success>
      <catch/>
      <collection_configuration inputmodes="dtmf" highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1505_ini_01">
                  <prompt-segments>
                    <audiofile text="Now, please enter the 6-to-15-digit account PIN" src="LG1505_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1505_ini_02">
                    <prompt-segments>
                      <audiofile text="Now, please enter the 8-digit account PIN" src="LG1505_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="LG1505_DSGCollectPIN_DM_reinvoke"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1505_ini_01">
                  <prompt-segments>
                    <audiofile text="Now, please enter the 6-to-15-digit account PIN" src="LG1505_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1505_ini_02">
                    <prompt-segments>
                      <audiofile text="Now, please enter the 8-digit account PIN" src="LG1505_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1505_nm1_01">
                  <prompt-segments>
                    <audiofile text="Please enter the customer's 6-to-15-digit account PIN" src="LG1505_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1505_nm1_02">
                    <prompt-segments>
                      <audiofile text="Please enter the customer's 8-digit account PIN" src="LG1505_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1505_nm2_01">
                  <prompt-segments>
                    <audiofile text="Using the keypad, please enter the customer's 6-to-15-digit account PIN" src="LG1505_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1505_nm2_02">
                    <prompt-segments>
                      <audiofile text="Using the keypad, please enter the customer's 8-digit account PIN" src="LG1505_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1505_nm2_01">
                  <prompt-segments>
                    <audiofile text="Using the keypad, please enter the customer's 6-to-15-digit account PIN" src="LG1505_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1505_nm2_02">
                    <prompt-segments>
                      <audiofile text="Using the keypad, please enter the customer's 8-digit account PIN" src="LG1505_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1505_nm1_01">
                  <prompt-segments>
                    <audiofile text="Please enter the customer's 6-to-15-digit account PIN" src="LG1505_nm1_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1505_nm1_02">
                    <prompt-segments>
                      <audiofile text="Please enter the customer's 8-digit account PIN" src="LG1505_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1505_nm2_01">
                  <prompt-segments>
                    <audiofile text="Using the keypad, please enter the customer's 6-to-15-digit account PIN" src="LG1505_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1505_nm2_02">
                    <prompt-segments>
                      <audiofile text="Using the keypad, please enter the customer's 8-digit account PIN" src="LG1505_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1505_nm2_01">
                  <prompt-segments>
                    <audiofile text="Using the keypad, please enter the customer's 6-to-15-digit account PIN" src="LG1505_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1505_nm2_02">
                    <prompt-segments>
                      <audiofile text="Using the keypad, please enter the customer's 8-digit account PIN" src="LG1505_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="accountPinToggleOn == true">
                <prompt id="LG1505_ini_01">
                  <prompt-segments>
                    <audiofile text="Now, please enter the 6-to-15-digit account PIN" src="LG1505_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="LG1505_ini_02">
                    <prompt-segments>
                      <audiofile text="Now, please enter the 8-digit account PIN" src="LG1505_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <dtmfgrammars filename="LG1505_DSGCollectPIN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="2000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="suppress">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.450">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="LG1509_Authenticate_DB_DA">
      <session-mapping key="accessToken" value="GlobalVars.accessToken" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="verificationType" value="GlobalVars.verificationType" type="String"/>
      <session-mapping key="verificationValue" value="GlobalVars.verificationValue" type="String"/>
      <session-mapping key="pin" value="GlobalVars.verificationValue" type="String"/>
      <data-access id="Authenticate" classname="com.nuance.metro.dataaccess.ValidatePinForAuthenticate">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="pin" mask="true"/>
          <input-variable name="accessToken" mask="false"/>
          <input-variable name="verificationType"/>
          <input-variable name="verificationValue" mask="true"/>
          <input-variable name="sessionId"/>
          <input-variable name="providerId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="JWTToken"/>
          <output-variable name="expiresIn"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="Authenticate.status == 'FAILURE'">
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          <gotodialog next="CallTransfer_Main_Dialog"/>
          <elseif cond="Authenticate.acctLocked == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.acctLocked" value="true" type="Boolean"/>
            <action next="LG1510_DSGCheckSecurityCodeMatch_JDA"/>
          </elseif>
          <elseif cond="Authenticate.onePinTryRemaining == true">
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <session-mapping key="GlobalVars.onePinTryRemaining" value="true" type="Boolean"/>
            <action next="LG1510_DSGCheckSecurityCodeMatch_JDA"/>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.Authenticate" expr="Authenticate"/>
            <action next="LG1510_DSGCheckSecurityCodeMatch_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="LG1510_DSGCheckSecurityCodeMatch_DS">
      <if cond="GlobalVars.Authenticate &amp;&amp; GlobalVars.Authenticate.status == 'SUCCESS'">
        <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.loggedIn" value="true" type="Boolean"/>
        <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'authenticated_by_pin')"/>
        <action next="getReturnLink()"/>
        <else>
          <if cond="GlobalVars.acctLocked == true">
            <action next="LG1515_DSGPINMaxAttempts_PP"/>
            <else>
              <action next="LG1505_DSGCollectPIN_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="LG1515_DSGPINMaxAttempts_PP">
      <audio>
        <prompt id="LG1515_out_01">
          <prompt-segments>
            <audiofile text="Sorry, I am unable to access this account" src="LG1515_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="LG1215_GoToCallTransfer_SD"/>
    </play-state>

    <play-state id="LG1605_GuestPaymentTransition_PP">
      <audio>
        <prompt id="LG1605_out_01">
          <prompt-segments>
            <audiofile text="We can still take care of your payment I just won't be able to tell you the latest balance for this phone number If this account is suspended, make sure your payment covers the full amount due, or we won't be able to turn the service back on So if you're not sure how much you need to pay right now, please call us back " src="LG1605_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="LG1610_ConfirmMDNYN_DM"/>
    </play-state>

    <dm-state id="LG1610_ConfirmMDNYN_DM" type="YSNO">
      <session-mapping key="MDN" value="GlobalVars.mdn" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.guestPayment" value="true" type="Boolean"/>
          <action next="LG1611_MetricsGuestPayment_JDA"/>
        </action>
        <action label="false">
          <action next="LG1615_PlayRetryLogin_PP"/>
        </action>
        <action label="repeat">
          <action next="LG1605_GuestPaymentTransition_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="LG1610_ini_01">
                <prompt-segments>
                  <audiofile text="So to confirm, you're paying for phone number " src="LG1610_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="MDN">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1610_ini_02">
                <prompt-segments>
                  <audiofile text="Is that right?" src="LG1610_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="LG1610_nm1_01">
                <prompt-segments>
                  <audiofile text="I got the phone number " src="LG1610_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="MDN">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="LG1610_nm1_02">
                <prompt-segments>
                  <audiofile text="Is that the account you want to pay for?" src="LG1610_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1610_nm2_01">
                <prompt-segments>
                  <audiofile text="I got the phone number " src="LG1610_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="MDN">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="LG1610_nm2_02">
                <prompt-segments>
                  <audiofile text="If that's the account you want to pay for, say 'yes' or press 1 To pay for a different account, say 'no' or press 2" src="LG1610_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1610_nm2_01">
                <prompt-segments>
                  <audiofile text="I got the phone number " src="LG1610_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="MDN">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="LG1610_nm2_02">
                <prompt-segments>
                  <audiofile text="If that's the account you want to pay for, say 'yes' or press 1 To pay for a different account, say 'no' or press 2" src="LG1610_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1610_nm1_01">
                <prompt-segments>
                  <audiofile text="I got the phone number " src="LG1610_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="MDN">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="LG1610_nm1_02">
                <prompt-segments>
                  <audiofile text="Is that the account you want to pay for?" src="LG1610_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1610_nm2_01">
                <prompt-segments>
                  <audiofile text="I got the phone number " src="LG1610_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="MDN">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="LG1610_nm2_02">
                <prompt-segments>
                  <audiofile text="If that's the account you want to pay for, say 'yes' or press 1 To pay for a different account, say 'no' or press 2" src="LG1610_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="LG1610_nm2_01">
                <prompt-segments>
                  <audiofile text="I got the phone number " src="LG1610_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="MDN">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="LG1610_nm2_02">
                <prompt-segments>
                  <audiofile text="If that's the account you want to pay for, say 'yes' or press 1 To pay for a different account, say 'no' or press 2" src="LG1610_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="LG1610_ConfirmMDNYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="LG1610_ConfirmMDNYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="LG1611_MetricsGuestPayment_DS">
      <action next="getReturnLink()"/>
    </decision-state>

    <play-state id="LG1615_PlayRetryLogin_PP">
      <audio>
        <prompt id="LG1615_out_01">
          <prompt-segments>
            <audiofile text="Okay, let's try your phone number and account PIN again" src="LG1615_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.GetAccountDetails" expr="undefined"/>
      <session-mapping key="GlobalVars.guestPaymentReattemptLogin" value="true" type="Boolean"/>
      <session-mapping key="pinAttempts" expr="0"/>
      <gotodialog next="Login_Main_Dialog"/>
    </play-state>

    <subdialog-state id="LG1130_GoToAccountPinReset_SD">
      <gotodialog next="AccountPINReset_Main_Dialog"/>
      <action next="LG1130_GoToAccountPinReset_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="LG1130_GoToAccountPinReset_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  