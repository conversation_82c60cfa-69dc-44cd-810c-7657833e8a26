<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="CallTransfer_MDEHandling_Dialog">
    <decision-state id="XR2001_CheckMDEType_DS">
      <if cond="GlobalVars.maxNoInput == true">
        <session-mapping key="GlobalVars.maxNoInput" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.eventCapture" expr="''"/>
        <action next="XR2005_MaxNoInput_DM"/>
        <else>
          <action next="XR2002_CheckMaxNoMatchConfig_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="XR2002_CheckMaxNoMatchConfig_DS">
      <if cond="careAlterHandContainment == 'true' &amp;&amp; GlobalVars.isSuspended == true">
        <action next="XR2015_PlayDisconnectPrompt_PP"/>
        <else>
          <action next="XR2010_MaxNoMatch_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="XR2005_MaxNoInput_DM" type="CUST">
      <success>
        <action label="main_menu">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardNumber" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardDate" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardZip" expr="undefined"/>
          <session-mapping key="GlobalVars.cardStatus" expr="undefined"/>
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
          <gotodialog next="CallTransfer_OperatorRequestHandling#XR1065_GoToMainMenu_SD"/>
        </action>
      </success>
      <catch/>
      <collection_configuration inputmodes="dtmf" highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="XR2005_ini_01">
                <prompt-segments>
                  <audiofile text="Are you still there? To go back to the main menu, please press 1 If you're done, you can simply hang up" src="XR2005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="XR2005_ini_01">
                <prompt-segments>
                  <audiofile text="Are you still there? To go back to the main menu, please press 1 If you're done, you can simply hang up" src="XR2005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="XR2005_MaxNoInput_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="XR2010_MaxNoMatch_DM" type="CUST">
      <success>
        <action label="main_menu">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardNumber" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardDate" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardZip" expr="undefined"/>
          <session-mapping key="GlobalVars.cardStatus" expr="undefined"/>
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
          <gotodialog next="CallTransfer_OperatorRequestHandling#XR1065_GoToMainMenu_SD"/>
        </action>
        <action label="request-representative">
          <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
          <if cond="!(GlobalVars.GetAccountDetails == null || GlobalVars.GetAccountDetails == '' || GlobalVars.GetAccountDetails == undefined) &amp;&amp; GlobalVars.loggedIn != true">
            <gotodialog next="CallTransfer_OperatorRequestHandling#XR1505_PreTransferCode_SD"/>
            <else>
              <gotodialog next="CallTransfer_Main#XR0025_PlayTransferPrompt_PP"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration inputmodes="dtmf" highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="XR2010_ini_01">
                <prompt-segments>
                  <audiofile text="Im sorry, we seem to be having trouble To speak to someone, press 1 To go back to the main menu, press 2 If you're done, you can simply hang up" src="XR2010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="XR2010_ini_01">
                <prompt-segments>
                  <audiofile text="Im sorry, we seem to be having trouble To speak to someone, press 1 To go back to the main menu, press 2 If you're done, you can simply hang up" src="XR2010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="XR2010_MaxNoMatch_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="XR2015_PlayDisconnectPrompt_PP">
      <audio>
        <prompt id="XR2015_out_01">
          <prompt-segments>
            <audiofile text="Im sorry, we seem to be having some trouble" src="XR2015_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <audio>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <audio>
        <prompt id="XR2015_out_02">
          <prompt-segments>
            <audiofile text="You can manage your account and make payments online, or by visiting one of our stores or authorized dealers To access your account or for a map of our locations near you, visit us at metrobyt-mobilecom" src="XR2015_out_02.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.playFailureGoodbye" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
      <gotodialog next="CallTransfer_Main#XR0040_GoToGoodbye_SD"/>
    </play-state>

  </dialog>
  