<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="GlobalVars.tag" value="buy-data_topup" type="string"/>
  <session-mapping key="needMDN" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.GetAccountDetails" value="undefined" type="string"/>
  <session-mapping key="securityRequired" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="string"/>
  <session-mapping key="isSuspended" value="true" type="boolean"/>
  <session-mapping key="GetBCSParameters.metroFlexEnabled" value="true" type="boolean"/>
  <session-mapping key="GetBCSParameters.metroFlexMessageToPlay" value="true" type="boolean"/>
  <session-mapping key="GetTagData.destinationName" value="NR1130_Goodbye_SD" type="string"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="active" type="string"/>
</session-mappings>
