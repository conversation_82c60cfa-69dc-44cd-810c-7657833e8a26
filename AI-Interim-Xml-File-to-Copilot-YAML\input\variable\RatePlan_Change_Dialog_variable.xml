<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="enteringFrom" value="RP1055_GoToDisambiguateRatePlan_SD_return" type="string"/>
  <session-mapping key="GlobalVars.cancelPlanChange" value="true" type="string"/>
  <session-mapping key="GlobalVars.suspendedRatePlanChange" value="true" type="boolean"/>
  <session-mapping key="futureChangeOffer" value="true" type="boolean"/>
  <session-mapping key="accountUpdateFailed" value="false" type="string"/>
  <session-mapping key="GlobalVars.accountUpdateFailed" value="true" type="string"/>
  <session-mapping key="care_nlu_enabled" value="true" type="boolean"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="ratePlans.length" value="0" type="string"/>
  <session-mapping key="extension" value="lastresult" type="string"/>
  <session-mapping key="noneOfThoseCount" value="2" type="integer"/>
  <session-mapping key="RP1415_ListRatePlans_DM.nbestresults" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.ratePlanSelectionType" value="undefined" type="string"/>
  <session-mapping key="FirstTimeRP1415" value="true" type="boolean"/>
  <session-mapping key="declineChangeCounter" value="1" type="integer"/>
  <session-mapping key="choseInvalidPlan" value="true" type="boolean"/>
  <session-mapping key="comingFrom" value="RP1415" type="string"/>
  <session-mapping key="RP1425_operator_counter" value="2" type="integer"/>
  <session-mapping key="ratePlans" value="1" type="string"/>
  <session-mapping key="extensionAllowed" value="true" type="boolean"/>
  <session-mapping key="heardDataExceededInfo" value="false" type="string"/>
</session-mappings>
