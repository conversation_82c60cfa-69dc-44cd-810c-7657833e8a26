<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Activation_Common_Dialog">
    <decision-state id="AC0001_CheckContext_DS">
      <if cond="GlobalVars.GetBCSParameters.activations_enable_prepaid_methods == true || GlobalVars.GetBCSParameters.activations_enable_prepaid_methods == 'true'">
        <session-mapping key="GlobalVars.offerPrepaid" value="true" type="Boolean"/>
        <else>
          <session-mapping key="GlobalVars.offerPrepaid" value="false" type="Boolean"/>
        </else>
      </if>
      <session-mapping key="GlobalVars.activationResult" expr="'unknown'"/>
      <session-mapping key="GlobalVars.haveActivationAddress" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.numberPINPayments" expr="0"/>
      <session-mapping key="GlobalVars.needResidualPayment" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.voucherNumberList" expr="''"/>
      <session-mapping key="GlobalVars.numberVouchersRedeemed" expr="0"/>
      <session-mapping key="GlobalVars.amountPaidOnCall" expr="0.00"/>
      <gotodialog next="Activation_Main#AC0990_GoToBroadcastMessages_SD"/>
    </decision-state>

    <play-state id="AC1131_PlayHaveCoverage_PP">
      <audio>
        <prompt id="AC1131_out_01">
          <prompt-segments>
            <audiofile src="AC1131_out_01.wav" text="All good!"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AC1135_CheckCallingApp_JDA"/>
    </play-state>

    <decision-state id="AC1135_CheckCallingApp_DS">
      <if cond="GlobalVars.activationEntryPoint == 'care'">
        <action next="AC2205_IMEITransitionSkipSBI_DM"/>
        <else>
          <if cond="GlobalVars.BYODNotInInventory == true &amp;&amp; GlobalVars.byodCompatibility.toUpperCase() == 'FULLY COMPATIBLE'">
            <gotodialog next="Activation_RatePlan#AC1155_RegisterBYOD_SD"/>
            <elseif cond="GlobalVars.BYODNotInInventory == true &amp;&amp; GlobalVars.byodCompatibility.toUpperCase() != 'FULLY COMPATIBLE'">
              <action next="AC2505_CompatibilityIssues_SD"/>
            </elseif>
            <else>
              <gotodialog next="Activation_RatePlan#AC1220_NumberPortIn_SD"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="AC1425_PlaySecurityTransition_PP">
      <audio>
        <prompt id="AC1425_out_01">
          <prompt-segments>
            <audiofile src="AC1425_out_01.wav" text="We're almost done! Let's set up your account PIN"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <gotodialog next="Activation_ValidateAddress#AC1500_CreateSecurityCode_DM"/>
    </play-state>

    <dm-state id="AC2205_IMEITransitionSkipSBI_DM" type="CUST">
      <success>
        <action label="continue">
          <action next="AC2220_CollectIMEI_DM"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AC2205_ini_01">
                <prompt-segments>
                  <audiofile src="AC2205_ini_01.wav" text="Next, let s work with the phone you re activating I ll need its 15-digit I-M-E-I number"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2205_ini_02">
                <prompt-segments>
                  <audiofile src="AC2205_ini_02.wav" text="If you already *have* that, press 1"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="AC2205_IMEITransitionSkipSBI_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AC2205_ini_01">
                <prompt-segments>
                  <audiofile src="AC2205_ini_01.wav" text="Next, let s work with the phone you re activating I ll need its 15-digit I-M-E-I number"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2205_ini_02">
                <prompt-segments>
                  <audiofile src="AC2205_ini_02.wav" text="If you already *have* that, press 1"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AC2205_ini_01">
                <prompt-segments>
                  <audiofile src="AC2205_ini_01.wav" text="Next, let s work with the phone you re activating I ll need its 15-digit I-M-E-I number"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2205_ini_02">
                <prompt-segments>
                  <audiofile src="AC2205_ini_02.wav" text="If you already *have* that, press 1"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="AC2205_IMEITransitionSkipSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="3000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AC2205_IMEITransitionSkipSBI_DM_confirmation_initial"/>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="AC2205_IMEITransitionSkipSBI_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AC2205_IMEITransitionSkipSBI_DM_cnf_nomatch_1"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="AC2205_IMEITransitionSkipSBI_DM_cnf_nomatch_1"/>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="AC2210_FindIMEIInstructions_PP">
      <audio>
        <prompt id="AC2210_out_01">
          <prompt-segments>
            <audiofile src="AC2210_out_01.wav" text="I ll give you a special code you can dial on your new phone That ll display the IMEI on the screen Make sure you do it on the phone you re *activating*, not the one we re talking on Here we go"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC2210_out_02">
          <prompt-segments>
            <audiofile src="AC2210_out_02.wav" text="The code is star, pound, zero, six, pound Dial it on your new phone as if you were making a call"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AC2215_FindIMEIWaitSBI_DM"/>
    </play-state>

    <dm-state id="AC2215_FindIMEIWaitSBI_DM" type="CUST">
      <success>
        <action label="continue">
          <action next="AC2220_CollectIMEI_DM"/>
        </action>
        <action label="not_working">
          <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
          <action next="getReturnLink()"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AC2215_ini_01">
                <prompt-segments>
                  <audiofile src="AC2215_ini_01.wav" text="I ll wait while you try that Dial star, pound, zero, six, pound on your new phone When you see the serial number, say  continue "/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2215_ini_02">
                <prompt-segments>
                  <audiofile src="AC2215_ini_02.wav" text="10 seconds wait music Dial star, pound, zero, six, pound on your new phone When you see the serial number, say  continue  Or say  It s not working   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait music We seem to be having some trouble"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="AC2215_FindIMEIWaitSBI_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AC2215_ini_01">
                <prompt-segments>
                  <audiofile src="AC2215_ini_01.wav" text="I ll wait while you try that Dial star, pound, zero, six, pound on your new phone When you see the serial number, say  continue "/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2215_ini_02">
                <prompt-segments>
                  <audiofile src="AC2215_ini_02.wav" text="10 seconds wait music Dial star, pound, zero, six, pound on your new phone When you see the serial number, say  continue  Or say  It s not working   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait music We seem to be having some trouble"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AC2215_ini_01">
                <prompt-segments>
                  <audiofile src="AC2215_ini_01.wav" text="I ll wait while you try that Dial star, pound, zero, six, pound on your new phone When you see the serial number, say  continue "/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2215_ini_02">
                <prompt-segments>
                  <audiofile src="AC2215_ini_02.wav" text="10 seconds wait music Dial star, pound, zero, six, pound on your new phone When you see the serial number, say  continue  Or say  It s not working   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait music Dial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait musicDial star, pound, zero, six, pound When you see the serial number, say  continue  or press 1 Or say  It s not working  or press 2   10 seconds wait music We seem to be having some trouble"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="AC2215_FindIMEIWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="AC2215_FindIMEIWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="30000ms" timeout="1000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="1000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AC2215_FindIMEIWaitSBI_DM_confirmation_initial"/>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="AC2215_FindIMEIWaitSBI_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AC2215_FindIMEIWaitSBI_DM_cnf_nomatch_1"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="AC2215_FindIMEIWaitSBI_DM_cnf_nomatch_1"/>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AC2220_CollectIMEI_DM" type="DIGT">
      <session-mapping key="imeiFailChecksum" value="GlobalVars.imeiFailChecksum != undefined ? GlobalVars.imeiFailChecksum : false" type="String"/>
      <success>
        <action label="instructions">
          <audio>
            <prompt id="AC2220_out_01">
              <prompt-segments>
                <audiofile src="AC2220_out_01.wav" text="Sure, instructions"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.imeiFailChecksum" value="false" type="Boolean"/>
          <action next="AC2210_FindIMEIInstructions_PP"/>
        </action>
        <action label="default" next="AC2225_IMEIPassChecksum_JDA">
          <session-mapping key="GlobalVars.imeiSerialNumber" expr="AC2220_CollectIMEI_DM.returnvalue"/>
          <session-mapping key="GlobalVars.IMEI" expr="AC2220_CollectIMEI_DM.returnvalue"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.imeiFailChecksum" value="false" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.imeiFailChecksum" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="imeiFailChecksum == true">
                <prompt id="AC2220_ini_01">
                  <prompt-segments>
                    <audiofile src="AC2220_ini_01.wav" text="That doesn t look like a valid number Let s try one more time to be sure If you have two different numbers, look for the one labeled I-M-E-I To hear the instructions to *find* the number, press star Otherwise, go ahead and enter it again"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC2220_ini_02">
                    <prompt-segments>
                      <audiofile src="AC2220_ini_02.wav" text="If you see a slash followed by two digits after the number, I *don t* need those So, what s the 15-digit I-M-E-I? You can say or enter it"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="AC2220_CollectIMEI_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="imeiFailChecksum == true">
                <prompt id="AC2220_ini_01">
                  <prompt-segments>
                    <audiofile src="AC2220_ini_01.wav" text="That doesn t look like a valid number Let s try one more time to be sure If you have two different numbers, look for the one labeled I-M-E-I To hear the instructions to *find* the number, press star Otherwise, go ahead and enter it again"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC2220_ini_02">
                    <prompt-segments>
                      <audiofile src="AC2220_ini_02.wav" text="If you see a slash followed by two digits after the number, I *don t* need those So, what s the 15-digit I-M-E-I? You can say or enter it"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AC2220_nm1_01">
                <prompt-segments>
                  <audiofile src="AC2220_nm1_01.wav" text="Please enter the 15-digit I-M-E-I for your phone You can skip any numbers that come after a slash To hear how you can find the number again, say  instructions "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AC2220_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2220_nm2_01.wav" text="If you have the 15-digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to *find* the number, press star"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AC2220_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2220_nm2_01.wav" text="If you have the 15-digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to *find* the number, press star"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2220_nm1_01">
                <prompt-segments>
                  <audiofile src="AC2220_nm1_01.wav" text="Please enter the 15-digit I-M-E-I for your phone You can skip any numbers that come after a slash To hear how you can find the number again, say  instructions "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2220_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2220_nm2_01.wav" text="If you have the 15-digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to *find* the number, press star"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2220_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2220_nm2_01.wav" text="If you have the 15-digit IMEI number for your new phone, please enter it now on your keypad Skip any numbers that come after a slash To repeat the instructions to *find* the number, press star"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="imeiFailChecksum == true">
                <prompt id="AC2220_ini_01">
                  <prompt-segments>
                    <audiofile src="AC2220_ini_01.wav" text="That doesn t look like a valid number Let s try one more time to be sure If you have two different numbers, look for the one labeled I-M-E-I To hear the instructions to *find* the number, press star Otherwise, go ahead and enter it again"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC2220_ini_02">
                    <prompt-segments>
                      <audiofile src="AC2220_ini_02.wav" text="If you see a slash followed by two digits after the number, I *don t* need those So, what s the 15-digit I-M-E-I? You can say or enter it"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="AC2220_CollectIMEI_DM.grxml" count="1"/>
          <dtmfgrammars filename="AC2220_CollectIMEI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="19000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" interdigittimeout="3000ms" termtimeout="200ms" timeout="19000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="AC2220_cnf_ini_01">
                  <prompt-segments>
                    <audiofile src="AC2220_cnf_ini_01.wav" text="Just to be sure, that was"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AC2220_cnf_ini_02">
                  <prompt-segments>
                    <audiofile src="AC2220_cnf_ini_02.wav" text="Did I get that right?"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="AC2220_CollectIMEI_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="AC2220_cnf_ini_01">
                  <prompt-segments>
                    <audiofile src="AC2220_cnf_ini_01.wav" text="Just to be sure, that was"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AC2220_cnf_ini_02">
                  <prompt-segments>
                    <audiofile src="AC2220_cnf_ini_02.wav" text="Did I get that right?"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="AC2220_cnf_ini_01">
                  <prompt-segments>
                    <audiofile src="AC2220_cnf_ini_01.wav" text="Just to be sure, that was"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AC2220_cnf_ini_02">
                  <prompt-segments>
                    <audiofile src="AC2220_cnf_ini_02.wav" text="Did I get that right?"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="AC2225_IMEIPassChecksum_DS">
      <if cond="passMod10Checksum(GlobalVars.IMEI)">
        <action next="AC2605_FirstValidateDeviceForActivation_DB_DA"/>
        <else>
          <if cond="GlobalVars.imeiFailChecksum == true">
            <action next="AC2230_NumberFailedChecksum_PP"/>
            <else>
              <session-mapping key="GlobalVars.imeiFailChecksum" value="true" type="Boolean"/>
              <action next="AC2220_CollectIMEI_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="AC2230_NumberFailedChecksum_PP">
      <audio>
        <prompt id="AC2230_out_01">
          <prompt-segments>
            <audiofile src="AC2230_out_01.wav" text="I m having some trouble with that, but our agents will be happy to switch your phone!"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
      <action next="getReturnLink()"/>
    </play-state>

    <dm-state id="AC2305_ICCIDTransitionSkipSBI_DM" type="CUST">
      <success>
        <action label="continue">
          <action next="AC2320_CollectICCID_DM"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AC2305_ini_01">
                <prompt-segments>
                  <audiofile src="AC2305_ini_01.wav" text="And now, the serial number for the Metro SIM card you ll be using"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2305_ini_02">
                <prompt-segments>
                  <audiofile src="AC2305_ini_02.wav" text="If you already have it, press 1"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AC2305_ni1_01">
                <prompt-segments>
                  <audiofile src="AC2305_ni1_01.wav" text="Otherwise, here s some tips!"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="AC2305_ICCIDTransitionSkipSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="3000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="AC2310_FindICCIDInstructions_PP">
      <audio>
        <prompt id="AC2310_out_01">
          <prompt-segments>
            <audiofile src="AC2310_out_01.wav" text="You can find the SIM card number *on* the SIM card itself, or on the bigger plastic card it came in"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC2310_out_02">
          <prompt-segments>
            <audiofile src="AC2310_out_02.wav" text="If your SIM card is already in your new phone, you can take it out for now to read the number It can be in a slot on the side of your phone, or inside the battery space"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC2310_out_03">
          <prompt-segments>
            <audiofile src="AC2310_out_03.wav" text="The number is 19 digits long It should start with eight-nine-zero, and end with the letter F, like fantastic"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AC2315_FindICCIDWaitSBI_DM"/>
    </play-state>

    <dm-state id="AC2315_FindICCIDWaitSBI_DM" type="CUST">
      <success>
        <action label="continue">
          <audio>
            <prompt id="AC2315_out_01">
              <prompt-segments>
                <audiofile src="AC2315_out_01.wav" text="Okay"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="AC2320_CollectICCID_DM"/>
        </action>
        <action label="cant_find">
          <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
          <action next="getReturnLink()"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AC2315_ini_01">
                <prompt-segments>
                  <audiofile src="AC2315_ini_01.wav" text="I ll wait while you look for it When you have it, say  continue "/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2315_ini_02">
                <prompt-segments>
                  <audiofile src="AC2315_ini_02.wav" text="10 seconds wait musicLook for the 19-digit SIM Card number, that ends with the letter F When you have it, say  continue  Or say  I can t find it   10 seconds wait musicWhen you have your SIM card number, say  continue  or press 1 Or say  I can t find it  or press 2  10 seconds wait musicWhen you have your SIM card number, say  continue  or press 1 Or say  I can t find it  or press 2  10 seconds wait musicWhen you have your SIM card number, say  continue  or press 1 Or say  I can t find it  or press 2  10 seconds wait musicWhen you have your SIM card number, say  continue  or press 1 Or say  I can t find it  or press 2  10 seconds wait musicWhen you have your SIM card number, say  continue  or press 1 Or say  I can t find it  or press 2  10 seconds wait musicWhen you have your SIM card number, say  continue  or press 1 Or say  I can t find it  or press 2  10 seconds wait musicWhen you have your SIM card number, say  continue  or press 1 Or say  I can t find it  or press 2  10 seconds wait music We seem to be having some trouble"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="AC2315_FindICCIDWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="AC2315_FindICCIDWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="10000ms" timeout="1000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="1000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AC2320_CollectICCID_DM" type="DIGT">
      <session-mapping key="iccidFailChecksum" value="GlobalVars.iccidFailChecksum != undefined ? GlobalVars.iccidFailChecksum : false  " type="String"/>
      <success>
        <action label="instructions">
          <session-mapping key="GlobalVars.iccidFailChecksum" value="false" type="Boolean"/>
          <audio>
            <prompt id="AC2320_out_01">
              <prompt-segments>
                <audiofile src="AC2320_out_01.wav" text="Alright, instructions"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="AC2310_FindICCIDInstructions_PP"/>
        </action>
        <action label="default" next="AC2325_ICCIDPassChecksum_JDA">
          <session-mapping key="GlobalVars.iccidSerialNumber" expr="AC2320_CollectICCID_DM.returnvalue"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.iccidFailChecksum" value="false" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.iccidFailChecksum" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="iccidFailChecksum == true">
                <prompt id="AC2320_ini_01">
                  <prompt-segments>
                    <audiofile src="AC2320_ini_01.wav" text="That doesn t look like a valid SIM card number Let s try one more time to be sure To hear the instructions to *find* the number, press star Otherwise, go ahead and enter it one more time, using your keypad"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC2320_ini_02">
                    <prompt-segments>
                      <audiofile src="AC2320_ini_02.wav" text="Say or enter the whole number, but without the letter F"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AC2320_nm1_01">
                <prompt-segments>
                  <audiofile src="AC2320_nm1_01.wav" text="Please enter the number on the SIM card you want to use in your new phone It s 19 digits long, and ends in the letter F Enter only the *numbers* To hear how you can find the number again, say  instructions "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AC2320_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2320_nm2_01.wav" text="If you have the 19-digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to *find* the number, press star"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AC2320_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2320_nm2_01.wav" text="If you have the 19-digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to *find* the number, press star"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2320_nm1_01">
                <prompt-segments>
                  <audiofile src="AC2320_nm1_01.wav" text="Please enter the number on the SIM card you want to use in your new phone It s 19 digits long, and ends in the letter F Enter only the *numbers* To hear how you can find the number again, say  instructions "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2320_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2320_nm2_01.wav" text="If you have the 19-digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to *find* the number, press star"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2320_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2320_nm2_01.wav" text="If you have the 19-digit SIM card number, please enter it now on your keypad, without the letter F To repeat the instructions to *find* the number, press star"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="iccidFailChecksum == true">
                <prompt id="AC2320_ini_01">
                  <prompt-segments>
                    <audiofile src="AC2320_ini_01.wav" text="That doesn t look like a valid SIM card number Let s try one more time to be sure To hear the instructions to *find* the number, press star Otherwise, go ahead and enter it one more time, using your keypad"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC2320_ini_02">
                    <prompt-segments>
                      <audiofile src="AC2320_ini_02.wav" text="Say or enter the whole number, but without the letter F"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="AC2320_CollectICCID_DM.grxml" count="1"/>
          <dtmfgrammars filename="AC2320_CollectICCID_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="24000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" interdigittimeout="3000ms" termtimeout="200ms" timeout="24000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="AC2320_cnf_ini_01">
                  <prompt-segments>
                    <audiofile src="AC2320_cnf_ini_01.wav" text="I got"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AC2320_cnf_ini_02">
                  <prompt-segments>
                    <audiofile src="AC2320_cnf_ini_02.wav" text="Is that right?"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="AC2320_cnf_ini_01">
                  <prompt-segments>
                    <audiofile src="AC2320_cnf_ini_01.wav" text="I got"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AC2320_cnf_ini_02">
                  <prompt-segments>
                    <audiofile src="AC2320_cnf_ini_02.wav" text="Is that right?"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="lastresult.interpretation.dm_root != 'instructions'">
                <prompt id="AC2320_cnf_ini_01">
                  <prompt-segments>
                    <audiofile src="AC2320_cnf_ini_01.wav" text="I got"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AC2320_cnf_ini_02">
                  <prompt-segments>
                    <audiofile src="AC2320_cnf_ini_02.wav" text="Is that right?"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="AC2325_ICCIDPassChecksum_DS">
      <session-mapping key="F" value="F" type="String"/>
      <if cond="passMod10Checksum(GlobalVars.iccidSerialNumber)">
        <session-mapping key="GlobalVars.iccidSerialNumber" expr="GlobalVars.iccidSerialNumber + F"/>
        <action next="AC2405_ValidateDeviceForActivation_DB_DA"/>
        <else>
          <if cond="GlobalVars.iccidFailChecksum == true">
            <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
            <action next="AC2230_NumberFailedChecksum_PP"/>
            <else>
              <session-mapping key="GlobalVars.iccidFailChecksum" value="true" type="Boolean"/>
              <action next="AC2320_CollectICCID_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <data-access-state id="AC2405_ValidateDeviceForActivation_DB_DA">
      <session-mapping key="imei2" value="(GlobalVars.imeiList != null &amp;&amp; GlobalVars.imeiList.length &gt; 1) ? GlobalVars.imeiList[1]:''" type="String"/>
      <session-mapping key="imei" value="GlobalVars.activationType == 'eSIM' ? imei2: GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="iccid" value="GlobalVars.activationType == 'eSIM' ? GlobalVars.GetDeviceStatus.eid: GlobalVars.iccidSerialNumber" type="String"/>
      <session-mapping key="ignoreDsDeviceProperty" value="GlobalVars.GetBCSParameters.activations_esim_disable" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="GetDeviceStatus" classname="com.nuance.metro.dataaccess.GetDeviceStatus">
        <inputs>
          <input-variable name="trn"/>
          <input-variable name="imei"/>
          <input-variable name="iccid"/>
          <input-variable name="ignoreDsDeviceProperty"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="esn"/>
          <output-variable name="imeiAllowSwap"/>
          <output-variable name="iccidAllowSwap"/>
          <output-variable name="imeiIsEligibleForActivation"/>
          <output-variable name="iccidIsEligibleForActivation"/>
          <output-variable name="imeiHasBeenActivePrev"/>
          <output-variable name="iccidHasBeenActivePrev"/>
          <output-variable name="imeiItemId"/>
          <output-variable name="iccidItemId"/>
          <output-variable name="imeiDeviceType"/>
          <output-variable name="networkType"/>
          <output-variable name="imeiSerialNumber"/>
          <output-variable name="iccidSerialNumber"/>
          <output-variable name="imeiEquipmentActive"/>
          <output-variable name="iccidEquipmentActive"/>
          <output-variable name="imeiEquipmentInNegativeList"/>
          <output-variable name="iccidEquipmentInNegativeList"/>
          <output-variable name="imeiInvalidDevice"/>
          <output-variable name="iccidInvalidDevice"/>
          <output-variable name="imeiAllowInsurance"/>
          <output-variable name="iccidAllowInsurance"/>
          <output-variable name="simExpired"/>
          <output-variable name="BYODNotInInventory"/>
          <output-variable name="imeiList"/>
          <output-variable name="isDualSimDevice"/>
          <output-variable name="defaultSIM"/>
          <output-variable name="simType"/>
          <output-variable name="pairing"/>
          <output-variable name="eid"/>
          <output-variable name="deviceType"/>
          <output-variable name="actualSupportedImei"/>
          <output-variable name="operatingSystem"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetDeviceStatus.status == 'Success'">
          <session-mapping key="GlobalVars.GetDeviceStatus" expr="GetDeviceStatus"/>
          <session-mapping key="GlobalVars.esn" expr="GetDeviceStatus.esn"/>
          <session-mapping key="GlobalVars.networkType" expr="'G'"/>
          <session-mapping key="GlobalVars.imeiSerialNumber" expr="GetDeviceStatus.imeiSerialNumber"/>
          <session-mapping key="GlobalVars.iccidSerialNumber" expr="GetDeviceStatus.iccidSerialNumber"/>
          <session-mapping key="GlobalVars.imeiHasBeenActivePrev" expr="GetDeviceStatus.imeiHasBeenActivePrev"/>
          <session-mapping key="GlobalVars.iccidHasBeenActivePrev" expr="GetDeviceStatus.iccidHasBeenActivePrev"/>
          <session-mapping key="GlobalVars.imeiItemId" expr="GetDeviceStatus.imeiItemId"/>
          <session-mapping key="GlobalVars.iccidItemId" expr="GetDeviceStatus.iccidItemId"/>
          <session-mapping key="GlobalVars.imeiDeviceType" expr="GetDeviceStatus.imeiDeviceType"/>
          <session-mapping key="GlobalVars.imeiEquipmentActive" expr="GetDeviceStatus.imeiEquipmentActive"/>
          <session-mapping key="GlobalVars.iccidEquipmentActive" expr="GetDeviceStatus.iccidEquipmentActive"/>
          <session-mapping key="GlobalVars.imeiEquipmentInNegativeList" expr="GetDeviceStatus.imeiEquipmentInNegativeList"/>
          <session-mapping key="GlobalVars.iccidEquipmentInNegativeList" expr="GetDeviceStatus.iccidEquipmentInNegativeList"/>
          <session-mapping key="GlobalVars.imeiInvalidDevice" expr="GetDeviceStatus.imeiInvalidDevice"/>
          <session-mapping key="GlobalVars.iccidInvalidDevice" expr="GetDeviceStatus.iccidInvalidDevice"/>
          <session-mapping key="GlobalVars.imeiIsEligibleForActivation" expr="GetDeviceStatus.imeiIsEligibleForActivation"/>
          <session-mapping key="GlobalVars.iccidIsEligibleForActivation" expr="GetDeviceStatus.iccidIsEligibleForActivation"/>
          <session-mapping key="GlobalVars.simExpired" expr="GetDeviceStatus.simExpired"/>
          <session-mapping key="GlobalVars.BYODNotInInventory" expr="GetDeviceStatus.BYODNotInInventory"/>
          <session-mapping key="GlobalVars.isByod" expr="GetDeviceStatus.BYODNotInInventory == true ? true : false"/>
          <session-mapping key="GlobalVars.byodCompatibility" expr="GetDeviceStatus.byodCompatibility"/>
          <session-mapping key="GlobalVars.imeiList" expr="GetDeviceStatus.imeiList"/>
          <session-mapping key="GlobalVars.isDualSimDevice" expr="GetDeviceStatus.isDualSimDevice"/>
          <session-mapping key="GlobalVars.deviceType" expr="GetDeviceStatus.deviceType"/>
          <session-mapping key="GlobalVars.actualSupportedImei" expr="GetDeviceStatus.actualSupportedImei"/>
          <session-mapping key="GlobalVars.simType" expr="GetDeviceStatus.simType"/>
          <session-mapping key="GlobalVars.eid" expr="GetDeviceStatus.EID"/>
          <session-mapping key="GlobalVars.operatingSystem" expr="GetDeviceStatus.operatingSystem"/>
          <action next="AC2410_CheckDeviceStatus_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
            <action next="getReturnLink()"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="AC2410_CheckDeviceStatus_DS">
      <session-mapping key="BYODRegistered" value="GlobalVars.BYODRegistered != undefined ? GlobalVars.BYODRegistered : false" type="String"/>
      <if cond="GlobalVars.iccidEquipmentActive == true">
        <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
        <action next="getReturnLink()"/>
        <elseif cond="GlobalVars.simExpired == true">
          <action next="AC2420_PlaySIMExpired_PP"/>
        </elseif>
        <elseif cond="(GlobalVars.iccidEquipmentInNegativeList == true)">
          <action next="AC2435_PlayLostStolenSIM_PP"/>
        </elseif>
        <elseif cond="GlobalVars.iccidIsEligibleForActivation == false  || GlobalVars.iccidInvalidDevice == true">
          <action next="AC2425_PlayInvalidEquipment_PP"/>
        </elseif>
        <else>
          <if cond="GlobalVars.GetDeviceStatus.isDualSimDevice == true &amp;&amp; !(GlobalVars.GetDeviceStatus.actualSupportedImei == undefined || GlobalVars.GetDeviceStatus.actualSupportedImei == 'null' || GlobalVars.GetDeviceStatus.actualSupportedImei == '' || GlobalVars.GetDeviceStatus.actualSupportedImei == null)">
            <session-mapping key="GlobalVars.imeiSerialNumber" expr="GlobalVars.GetDeviceStatus.actualSupportedImei"/>
            <action next="AC2415_PlayThanksDeviceNums_PP"/>
            <else>
              <action next="AC2415_PlayThanksDeviceNums_PP"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="AC2430_PlayLostStolenPhone_PP">
      <audio>
        <prompt id="AC2430_out_01">
          <prompt-segments>
            <audiofile src="AC2430_out_01.wav" text="Actually, your phone is reported as lost or stolen, so I wouldn't be able to use it to open an account today"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC2430_out_02">
          <prompt-segments>
            <audiofile src="AC2430_out_02.wav" text="Our agents couldn't work with it either You'll need to contact the carrier with whom the phone was previously registered to resolve this"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_2000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_2000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.activationResult" expr="'goodbye'"/>
      <action next="getReturnLink()"/>
    </play-state>

    <play-state id="AC2435_PlayLostStolenSIM_PP">
      <audio>
        <prompt id="AC2435_out_01">
          <prompt-segments>
            <audiofile src="AC2435_out_01.wav" text="Actually, your SIM card is reported as lost or stolen, so I wouldn't be able to use it to open an account today"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC2435_out_02">
          <prompt-segments>
            <audiofile src="AC2435_out_02.wav" text="You'll need to get help at one of our stores"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC2435_out_03">
          <prompt-segments>
            <audiofile src="AC2425_out_03.wav" text="If you wanna stay on the line, I'll help you look for one near you Otherwise, you can find a map at metro by t dash mobile dot com"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_2000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_2000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.storeLocatorReason" expr="'invalidEquipment'"/>
      <gotodialog next="Activation_Main#AC0096_StoreLocator_SD"/>
    </play-state>

    <decision-state id="AC2511_CheckNextStep_DS">
      <if cond="GlobalVars.BYODNotInInventory == true">
        <gotodialog next="Activation_RatePlan#AC1155_RegisterBYOD_SD"/>
        <else>
          <gotodialog next="Activation_RatePlan#AC1220_NumberPortIn_SD"/>
        </else>
      </if>
    </decision-state>

    <play-state id="AC2415_PlayThanksDeviceNums_PP">
      <session-mapping key="BYODRegistered" value="GlobalVars.BYODRegistered != undefined ? GlobalVars.BYODRegistered : false" type="String"/>
      <audio>
        <prompt id="AC2415_out_01">
          <prompt-segments>
            <audiofile src="AC2415_out_01.wav" text="Perfect, thanks!"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="GlobalVars.activationType == 'eSIM' &amp;&amp; GlobalVars.GetDeviceStatus.iccidNotInInventory == true">
        <action next="AC2625_ReserveeSim_DB_DA"/>
        <else>
          <gotodialog next="Activation_RatePlan#AC1220_NumberPortIn_SD"/>
        </else>
      </if>
    </play-state>

    <play-state id="AC2420_PlaySIMExpired_PP">
      <audio>
        <prompt id="AC2420_out_01">
          <prompt-segments>
            <audiofile src="AC2420_out_01.wav" text="Actually, there s something wrong with your SIM card, so we wouldn t be able to use it to activate a phone today"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC2420_out_02">
          <prompt-segments>
            <audiofile src="AC2420_out_02.wav" text="Our agents couldn t work with it either, so you ll need to get help at one of our stores"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC2420_out_03">
          <prompt-segments>
            <audiofile src="AC2420_out_03.wav" text="If you wanna stay on the line, I ll help you look for one near you Otherwise, you can find a map at metro by t dash mobile dot com"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_2000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_2000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.storeLocatorReason" expr="'invalidEquipment'"/>
      <gotodialog next="Activation_Main#AC0096_StoreLocator_SD"/>
    </play-state>

    <play-state id="AC2425_PlayInvalidEquipment_PP">
      <audio>
        <prompt id="AC2425_out_01">
          <prompt-segments>
            <audiofile src="AC2425_out_01.wav" text="Actually, there s something wrong with your phone, so we wouldn t be able to use it to open an account today"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC2425_out_02">
          <prompt-segments>
            <audiofile src="AC2425_out_02.wav" text="Our agents couldn t work with it either, so you ll need to get help at one of our stores"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AC2425_out_03">
          <prompt-segments>
            <audiofile src="AC2425_out_03.wav" text="If you wanna stay on the line, I ll help you look for one near you Otherwise, you can find a map at metro by t dash mobile dot com"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_2000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_2000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.storeLocatorReason" expr="'invalidEquipment'"/>
      <gotodialog next="Activation_Main#AC0096_StoreLocator_SD"/>
    </play-state>

    <subdialog-state id="AC1160_BYODInfo_SD">
      <gotodialog next="BYODInfo_Main_Dialog"/>
      <action next="AC1160_BYODInfo_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AC1160_BYODInfo_SD_return_CS">
      <action next="AC2605_FirstValidateDeviceForActivation_DB_DA"/>
    </custom-state>

    <subdialog-state id="AC2505_CompatibilityIssues_SD">
      <gotodialog next="CompatibilityIssues_Dialog"/>
      <action next="AC2505_CompatibilityIssues_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AC2505_CompatibilityIssues_SD_return_CS">
      <if cond="GlobalVars.activationResult == 'transfer'">
        <action next="getReturnLink()"/>
        <else>
          <action next="AC2511_CheckNextStep_JDA"/>
        </else>
      </if>
    </custom-state>

    <decision-state id="AC1401_CheckAddressEnabled_DS">
      <session-mapping key="activations_enable_address_collection" value="GlobalVars.GetBCSParameters.activations_enable_address_collection != undefined ? GlobalVars.GetBCSParameters.activations_enable_address_collection: false " type="String"/>
      <if cond="activations_enable_address_collection == true  || activations_enable_address_collection == 'true'">
        <gotodialog next="Activation_GetName#AC1400_TransitionToAddress_PP"/>
        <else>
          <gotodialog next="Activation_Common#AC1425_PlaySecurityTransition_PP"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="AC1515_AcctDetsSMS_SD">
      <gotodialog next="ActivationSMS_Dialog"/>
      <action next="AC1515_AcctDetsSMS_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AC1515_AcctDetsSMS_SD_return_CS">
      <gotodialog next="Activation_ValidateAddress#AC1540_NeedAreaCodeCheck_DS"/>
    </custom-state>

    <data-access-state id="AC2605_FirstValidateDeviceForActivation_DB_DA">
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="ignoreDsDeviceProperty" value="GlobalVars.GetBCSParameters.activations_esim_disable" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="GetDeviceStatus" classname="com.nuance.metro.dataaccess.GetDeviceStatus">
        <inputs>
          <input-variable name="trn"/>
          <input-variable name="imei"/>
          <input-variable name="iccid"/>
          <input-variable name="ignoreDsDeviceProperty"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="esn"/>
          <output-variable name="imeiAllowSwap"/>
          <output-variable name="iccidAllowSwap"/>
          <output-variable name="imeiIsEligibleForActivation"/>
          <output-variable name="iccidIsEligibleForActivation"/>
          <output-variable name="imeiHasBeenActivePrev"/>
          <output-variable name="iccidHasBeenActivePrev"/>
          <output-variable name="imeiItemId"/>
          <output-variable name="iccidItemId"/>
          <output-variable name="imeiDeviceType"/>
          <output-variable name="networkType"/>
          <output-variable name="imeiSerialNumber"/>
          <output-variable name="iccidSerialNumber"/>
          <output-variable name="imeiEquipmentActive"/>
          <output-variable name="iccidEquipmentActive"/>
          <output-variable name="imeiEquipmentInNegativeList"/>
          <output-variable name="iccidEquipmentInNegativeList"/>
          <output-variable name="imeiInvalidDevice"/>
          <output-variable name="iccidInvalidDevice"/>
          <output-variable name="imeiAllowInsurance"/>
          <output-variable name="iccidAllowInsurance"/>
          <output-variable name="simExpired"/>
          <output-variable name="BYODNotInInventory"/>
          <output-variable name="imeiList"/>
          <output-variable name="isDualSimDevice"/>
          <output-variable name="defaultSIM"/>
          <output-variable name="simType"/>
          <output-variable name="pairing"/>
          <output-variable name="eid"/>
          <output-variable name="deviceType"/>
          <output-variable name="actualSupportedImei"/>
          <output-variable name="operatingSystem"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetDeviceStatus.status == 'Success'">
          <session-mapping key="GlobalVars.GetDeviceStatus" expr="GetDeviceStatus"/>
          <session-mapping key="GlobalVars.imeiSerialNumber" expr="GetDeviceStatus.imeiSerialNumber"/>
          <session-mapping key="GlobalVars.imeiEquipmentActive" expr="GetDeviceStatus.imeiEquipmentActive"/>
          <session-mapping key="GlobalVars.imeiIsEligibleForActivation" expr="GetDeviceStatus.imeiIsEligibleForActivation"/>
          <session-mapping key="GlobalVars.imeiEquipmentInNegativeList" expr="GetDeviceStatus.imeiEquipmentInNegativeList"/>
          <session-mapping key="GlobalVars.imeiInvalidDevice" expr="GetDeviceStatus.imeiInvalidDevice"/>
          <session-mapping key="GlobalVars.BYODNotInInventory" expr="GetDeviceStatus.BYODNotInInventory"/>
          <session-mapping key="GlobalVars.byodCompatibility" expr="GetDeviceStatus.byodCompatibility"/>
          <session-mapping key="GlobalVars.imeiList" expr="GetDeviceStatus.imeiList"/>
          <session-mapping key="GlobalVars.isDualSimDevice" expr="GetDeviceStatus.isDualSimDevice"/>
          <session-mapping key="GlobalVars.EID" expr="GetDeviceStatus.EID"/>
          <session-mapping key="GlobalVars.deviceType" expr="GetDeviceStatus.deviceType"/>
          <session-mapping key="GlobalVars.actualSupportedImei" expr="GetDeviceStatus.actualSupportedImei"/>
          <session-mapping key="GlobalVars.simType" expr="GetDeviceStatus.simType"/>
          <session-mapping key="GlobalVars.operatingSystem" expr="GetDeviceStatus.operatingSystem"/>
          <session-mapping key="GlobalVars.isByod" expr="GetDeviceStatus.BYODNotInInventory == true ? true : false"/>
          <if cond="GlobalVars.GetDeviceStatus.deviceType == 'IOT' || GlobalVars.GetDeviceStatus.deviceType == 'WEARABLE' || GlobalVars.GetDeviceStatus.deviceType == 'TABLET'">
            <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
            <action next="getReturnLink()"/>
            <else>
              <action next="AC2610_CheckIMEIStatus_JDA"/>
            </else>
          </if>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
            <action next="getReturnLink()"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="AC2610_CheckIMEIStatus_DS">
      <session-mapping key="BYODRegistered" value="GlobalVars.BYODRegistered != undefined ? GlobalVars.BYODRegistered : false" type="String"/>
      <if cond="((GlobalVars.GetBCSParameters.activations_esim_disable == true || GlobalVars.GetBCSParameters.activations_esim_disable == 'true') &amp;&amp; GlobalVars.GetDeviceStatus.simType == 'ESIM')">
        <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
        <action next="getReturnLink()"/>
        <elseif cond="(GlobalVars.GetDeviceStatus.isDualSimDevice == true) &amp;&amp; (GlobalVars.GetDeviceStatus.imeiList == null || GlobalVars.GetDeviceStatus.imeiList.length &lt; 2) ">
          <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
          <action next="getReturnLink()"/>
        </elseif>
        <elseif cond="((GlobalVars.BYODNotInInventory == true &amp;&amp; BYODRegistered == false) &amp;&amp; (GlobalVars.byodCompatibility.toUpperCase() == 'FULLY COMPATIBLE'))">
          <gotodialog next="Activation_RatePlan#AC1155_RegisterBYOD_SD"/>
        </elseif>
        <elseif cond="((GlobalVars.BYODNotInInventory == true &amp;&amp; BYODRegistered == false) &amp;&amp; (GlobalVars.byodCompatibility.toUpperCase() != 'FULLY COMPATIBLE'))">
          <action next="AC2505_CompatibilityIssues_SD"/>
        </elseif>
        <elseif cond="GlobalVars.imeiEquipmentActive == true">
          <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
          <action next="getReturnLink()"/>
        </elseif>
        <elseif cond="GlobalVars.imeiEquipmentInNegativeList == true">
          <action next="AC2430_PlayLostStolenPhone_PP"/>
        </elseif>
        <else>
          <if cond="GlobalVars.GetDeviceStatus.simType == 'ESIM'">
            <if cond="(GlobalVars.GetDeviceStatus.eid == undefined || GlobalVars.GetDeviceStatus.eid == '' || GlobalVars.GetDeviceStatus.eid == null || GlobalVars.GetDeviceStatus.eid == 'null')">
              <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
              <action next="getReturnLink()"/>
              <else>
                <session-mapping key="GlobalVars.activationType" expr="'eSIM'"/>
                <action next="AC2620_AskWiFiConnected_DM"/>
              </else>
            </if>
            <elseif cond="GlobalVars.GetDeviceStatus.simType == 'PESIM' &amp;&amp; GlobalVars.GetBCSParameters.activations_esim_disable == 'false'">
              <action next="AC2615_CollecteSIMorpSIM_DM"/>
            </elseif>
            <else>
              <action next="AC2305_ICCIDTransitionSkipSBI_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <dm-state id="AC2615_CollecteSIMorpSIM_DM" type="CUST">
      <success>
        <session-mapping key="GlobalVars.AC2615ReRecognition" value="false" type="Boolean"/>
        <action label="eSim">
          <if cond="GlobalVars.GetDeviceStatus.eid == undefined || GlobalVars.GetDeviceStatus.eid == '' || GlobalVars.GetDeviceStatus.eid == null || GlobalVars.GetDeviceStatus.eid == 'null'">
            <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
            <action next="getReturnLink()"/>
            <else>
              <session-mapping key="GlobalVars.activationType" expr="'eSIM'"/>
              <action next="AC2620_AskWiFiConnected_DM"/>
            </else>
          </if>
        </action>
        <action label="pSim">
          <session-mapping key="GlobalVars.activationType" expr="'pSIM'"/>
          <action next="AC2305_ICCIDTransitionSkipSBI_DM"/>
        </action>
        <action label="more_info">
          <action next="AC2615_CollecteSIMorpSIM_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AC2615_ini_01">
                <prompt-segments>
                  <audiofile src="AC2615_ini_01.wav" text="Your phone has an eSIm AND a physical SIM Which one would you like to activate today, say 'eSIM', 'physical SIM' or more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AC2615_CollecteSIMorpSIM_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AC2615_ini_01">
                <prompt-segments>
                  <audiofile src="AC2615_ini_01.wav" text="Your phone has an eSIm AND a physical SIM Which one would you like to activate today, say 'eSIM', 'physical SIM' or more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AC2615_nm1_01">
                <prompt-segments>
                  <audiofile src="AC2615_nm1_01.wav" text="Which would you like to activate today, say 'eSIM' or 'physical SIM' You can also say 'more information'"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AC2615_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2615_nm2_01.wav" text="Say 'eSIM' or press 1, or 'physical SIM' or press 2 You can also say 'more information' or press 3 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AC2615_nm3_01">
                <prompt-segments>
                  <audiofile src="AC2615_nm3_01.wav" text="If you'd like to activate the eSIM, press 1 To activate the physical SIM, press 2 For more information, press 3 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2615_nm1_01">
                <prompt-segments>
                  <audiofile src="AC2615_nm1_01.wav" text="Which would you like to activate today, say 'eSIM' or 'physical SIM' You can also say 'more information'"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2615_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2615_nm2_01.wav" text="Say 'eSIM' or press 1, or 'physical SIM' or press 2 You can also say 'more information' or press 3 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2615_nm3_01">
                <prompt-segments>
                  <audiofile src="AC2615_nm3_01.wav" text="If you'd like to activate the eSIM, press 1 To activate the physical SIM, press 2 For more information, press 3 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AC2615_ini_01">
                <prompt-segments>
                  <audiofile src="AC2615_ini_01.wav" text="Your phone has an eSIm AND a physical SIM Which one would you like to activate today, say 'eSIM', 'physical SIM' or more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="AC2615_CollecteSIMorpSIM_DM.grxml" count="1"/>
          <dtmfgrammars filename="AC2615_CollecteSIMorpSIM_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AC2620_AskWiFiConnected_DM" type="YSNO">
      <session-mapping key="simType" value="GlobalVars.GetDeviceStatus.simType" type="String"/>
      <success>
        <action label="true">
          <audio>
            <prompt id="AC2620_out_01">
              <prompt-segments>
                <audiofile src="AC2620_out_01.wav" text="OK, let me check your phone"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="AC2405_ValidateDeviceForActivation_DB_DA"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="AC2620_out_02">
              <prompt-segments>
                <audiofile src="AC2620_out_02.wav" text="No problem"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AC2620_ini_02" cond="simType  == 'ESIM'">
                <prompt-segments>
                  <audiofile src="AC2620_ini_02.wav" text="Just so you know, the phone you want to activate only has an eSIM, so we will be activating it today "/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="simType  == 'ESIM'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2620_ini_01">
                <prompt-segments>
                  <audiofile src="AC2620_ini_01.wav" text="After this call you will need a WiFi connection to complete your activation Will you be able to connect to WiFi from your new phone? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AC2620_ini_02" cond="simType  == 'ESIM'">
                <prompt-segments>
                  <audiofile src="AC2620_ini_02.wav" text="Just so you know, the phone you want to activate only has an eSIM, so we will be activating it today "/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="simType  == 'ESIM'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2620_ini_01">
                <prompt-segments>
                  <audiofile src="AC2620_ini_01.wav" text="After this call you will need a WiFi connection to complete your activation Will you be able to connect to WiFi from your new phone? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AC2620_nm1_01">
                <prompt-segments>
                  <audiofile src="AC2620_nm1_01.wav" text="Can you connect your new phone to WiFi? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AC2620_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2620_nm2_01.wav" text="If you have WIFi connection, say 'yes' or press 1 Otherwise, say 'no' or press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AC2620_nm3_01">
                <prompt-segments>
                  <audiofile src="AC2620_nm3_01.wav" text="If you have WiFI connection, press 1 If not, press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2620_nm1_01">
                <prompt-segments>
                  <audiofile src="AC2620_nm1_01.wav" text="Can you connect your new phone to WiFi? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2620_nm2_01">
                <prompt-segments>
                  <audiofile src="AC2620_nm2_01.wav" text="If you have WIFi connection, say 'yes' or press 1 Otherwise, say 'no' or press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2620_nm3_01">
                <prompt-segments>
                  <audiofile src="AC2620_nm3_01.wav" text="If you have WiFI connection, press 1 If not, press 2 "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AC2620_ini_02" cond="simType  == 'ESIM'">
                <prompt-segments>
                  <audiofile src="AC2620_ini_02.wav" text="Just so you know, the phone you want to activate only has an eSIM, so we will be activating it today "/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="simType  == 'ESIM'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC2620_ini_01">
                <prompt-segments>
                  <audiofile src="AC2620_ini_01.wav" text="After this call you will need a WiFi connection to complete your activation Will you be able to connect to WiFi from your new phone? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="AC2620_AskWiFiConnected_DM.grxml" count="1"/>
          <dtmfgrammars filename="AC2620_AskWiFiConnected_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="AC2625_ReserveeSim_DB_DA">
      <session-mapping key="simNumber" value="(GlobalVars.activationType == 'eSIM')? GlobalVars.GetDeviceStatus.eid : GlobalVars.iccidSerialNumber" type="String"/>
      <session-mapping key="transactionType" value="ACTIVATION" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="primaryIMEI" value="GlobalVars.imeiSerialNumber" type="String"/>
      <data-access id="AddOrUpdateEsim" classname="com.nuance.metro.dataaccess.AddOrUpdateEsim">
        <inputs>
          <input-variable name="simNumber"/>
          <input-variable name="transactionType"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="AddOrUpdateEsim.status == 'Success'">
          <gotodialog next="Activation_RatePlan#AC1220_NumberPortIn_SD"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <action next="getReturnLink()"/>
          </else>
        </if>
      </action>
    </data-access-state>

  </dialog>
  