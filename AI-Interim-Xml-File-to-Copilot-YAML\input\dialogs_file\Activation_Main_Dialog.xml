<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Activation_Main_Dialog">
    <subdialog-state id="AC0990_GoToBroadcastMessages_SD">
      <session-mapping key="GlobalVars.broadcastMessageKey" expr="'AV'"/>
      <gotodialog next="BroadcastMessages_Dialog"/>
      <action next="AC0990_GoToBroadcastMessages_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AC0990_GoToBroadcastMessages_SD_return_CS">
      <if cond="GlobalVars.activationEntryPoint == '228'">
        <action next="AC0095_PaymentDisclaimer_DM"/>
        <else>
          <action next="AC1000_ActivationsRouting_JDA"/>
        </else>
      </if>
    </custom-state>

    <dm-state id="AC0095_PaymentDisclaimer_DM" type="CUST">
      <session-mapping key="offerPrepaid" value="GlobalVars.offerPrepaid != undefined ? GlobalVars.offerPrepaid : false" type="String"/>
      <success>
        <action label="continue" next="AC1000_ActivationsRouting_JDA">
          <session-mapping key="ActivationTable.ACTIVATION_TYPE" expr="2"/>
          <session-mapping key="ActivationTable.ACTIVATION_STARTED" expr="new Date()"/>
          <session-mapping key="ActivationTable.ACTIVATION_STARTED" expr="getGMTTime()"/>
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="41"/>
          <session-mapping key="activationVars.eventTypeGMT" expr="getEventTime()"/>
          <session-mapping key="activationVars.status" expr="'incomplete'"/>
          <session-mapping key="activationVars.eventType" expr="'Activation'"/>
        </action>
        <action label="inquire-store_location" next="AC0096_StoreLocator_SD">
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'sign up'"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AC0095_ini_01">
                <prompt-segments>
                  <audiofile text="Just so you know, in order to open an account, you'll need to pay your first month's charges on this call" src="AC0095_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="offerPrepaid == true">
                <prompt id="AC0095_ini_02">
                  <prompt-segments>
                    <audiofile text="You can pay with a credit or debit card, or with a  Payment PIN" src="AC0095_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC0095_ini_03">
                    <prompt-segments>
                      <audiofile text="You can pay with a credit or debit card " src="AC0095_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="AC0095_ini_04">
                <prompt-segments>
                  <audiofile text="If you'll be able to make that payment, say 'continue'  Otherwise, to pay in cash, say 'find a store'" src="AC0095_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AC0095_PaymentDisclaimer_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="AC0095_PaymentDisclaimer_DM_initial"/>
          <helpprompts count="2" bargein="true" filename="" text="" id="AC0095_PaymentDisclaimer_DM_initial"/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AC0095_rin_01">
                <prompt-segments>
                  <audiofile text="In order to open your account, you'll need to pay your first month's charges on this call" src="AC0095_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="offerPrepaid == true">
                <prompt id="AC0095_rin_02">
                  <prompt-segments>
                    <audiofile text="If you have a credit or debit card, or a  Payment PIN you can use, say 'continue'" src="AC0095_rin_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC0095_rin_03">
                    <prompt-segments>
                      <audiofile text="If you have a credit or debit card you can use, say 'continue'" src="AC0095_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="AC0095_rin_04">
                <prompt-segments>
                  <audiofile text="If you'd like to pay in cash instead, say 'find a store'" src="AC0095_rin_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2">
            <audio>
              <prompt id="AC0095_rin_01">
                <prompt-segments>
                  <audiofile text="In order to open your account, you'll need to pay your first month's charges on this call" src="AC0095_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="offerPrepaid == true">
                <prompt id="AC0095_rin_02">
                  <prompt-segments>
                    <audiofile text="If you have a credit or debit card, or a  Payment PIN you can use, say 'continue'" src="AC0095_rin_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC0095_rin_03">
                    <prompt-segments>
                      <audiofile text="If you have a credit or debit card you can use, say 'continue'" src="AC0095_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="AC0095_rin_04">
                <prompt-segments>
                  <audiofile text="If you'd like to pay in cash instead, say 'find a store'" src="AC0095_rin_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="3">
            <audio>
              <prompt id="AC0095_rin_01">
                <prompt-segments>
                  <audiofile text="In order to open your account, you'll need to pay your first month's charges on this call" src="AC0095_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="offerPrepaid == true">
                <prompt id="AC0095_rin_02">
                  <prompt-segments>
                    <audiofile text="If you have a credit or debit card, or a  Payment PIN you can use, say 'continue'" src="AC0095_rin_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC0095_rin_03">
                    <prompt-segments>
                      <audiofile text="If you have a credit or debit card you can use, say 'continue'" src="AC0095_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="AC0095_rin_04">
                <prompt-segments>
                  <audiofile text="If you'd like to pay in cash instead, say 'find a store'" src="AC0095_rin_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AC0095_nm1_01">
                <prompt-segments>
                  <audiofile text="If youll be able to pay on this call, say continue Otherwise, say find a store" src="AC0095_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AC0095_nm2_01">
                <prompt-segments>
                  <audiofile text="If youll be able to pay your first months charges on this call, say continue or press 1 Otherwise, say find a store or press 2" src="AC0095_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AC0095_nm3_01">
                <prompt-segments>
                  <audiofile text="If you have a credit or debit card, or a  Payment PIN to pay for your first month's charges today, press 1 If you'd rather pay cash at Metro location, press 2" src="AC0095_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC0095_nm1_01">
                <prompt-segments>
                  <audiofile text="If youll be able to pay on this call, say continue Otherwise, say find a store" src="AC0095_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC0095_nm2_01">
                <prompt-segments>
                  <audiofile text="If youll be able to pay your first months charges on this call, say continue or press 1 Otherwise, say find a store or press 2" src="AC0095_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AC0095_nm3_01">
                <prompt-segments>
                  <audiofile text="If you have a credit or debit card, or a  Payment PIN to pay for your first month's charges today, press 1 If you'd rather pay cash at Metro location, press 2" src="AC0095_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AC0095_ini_01">
                <prompt-segments>
                  <audiofile text="Just so you know, in order to open an account, you'll need to pay your first month's charges on this call" src="AC0095_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="offerPrepaid == true">
                <prompt id="AC0095_ini_02">
                  <prompt-segments>
                    <audiofile text="You can pay with a credit or debit card, or with a  Payment PIN" src="AC0095_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AC0095_ini_03">
                    <prompt-segments>
                      <audiofile text="You can pay with a credit or debit card " src="AC0095_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="AC0095_ini_04">
                <prompt-segments>
                  <audiofile text="If you'll be able to make that payment, say 'continue'  Otherwise, to pay in cash, say 'find a store'" src="AC0095_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="AC0095_PaymentDisclaimer_DM.grxml" count="1"/>
          <dtmfgrammars filename="AC0095_PaymentDisclaimer_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="AC0095_PaymentDisclaimer_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="AC0096_StoreLocator_SD">
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="AC0096_StoreLocator_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AC0096_StoreLocator_SD_return_CS">
      <action next="AC0097_HappyToHelp_PP"/>
    </custom-state>

    <play-state id="AC0097_HappyToHelp_PP">
      <audio>
        <prompt id="AC0097_out_01">
          <prompt-segments>
            <audiofile text="Our staff will be happy to help you with your activation" src="AC0097_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.activationResult" expr="'goodbye'"/>
      <action next="getReturnLink()"/>
    </play-state>

    <decision-state id="AC1000_ActivationsRouting_DS">
      <if cond="(GlobalVars.GetBCSParameters.activations_transferEnabled == 'true')||(GlobalVars.GetBCSParameters.activations_transferEnabled == true)">
        <session-mapping key="GlobalVars.activationResult" expr="'transfer'"/>
        <action next="getReturnLink()"/>
        <else>
          <gotodialog next="Activation_RatePlan#AC1100_GetZIPCode_DM"/>
        </else>
      </if>
    </decision-state>

  </dialog>
  