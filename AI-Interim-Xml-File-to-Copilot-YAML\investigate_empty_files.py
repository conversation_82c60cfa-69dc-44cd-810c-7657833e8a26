"""
<PERSON><PERSON><PERSON> to investigate why 33 files are empty (LLM generation failures)
"""
import os
from pathlib import Path

def investigate_empty_files():
    """Investigate empty files to understand LLM generation failures"""
    
    unformatted_dir = Path("./output/unformatted_topic_yaml/")
    input_dir = Path("./input/")
    
    print("🔍 INVESTIGATING EMPTY FILES")
    print("=" * 50)
    
    # Find all empty files
    empty_files = []
    files_with_content = []
    
    for file_path in unformatted_dir.glob("*.yml"):
        if file_path.stat().st_size == 0:
            empty_files.append(file_path.name)
        else:
            files_with_content.append(file_path.name)
    
    print(f"📊 Found {len(empty_files)} empty files")
    print(f"📊 Found {len(files_with_content)} files with content")
    
    print("\n❌ EMPTY FILES:")
    for i, filename in enumerate(empty_files, 1):
        print(f"{i:2d}. {filename}")
    
    # Check if corresponding input files exist
    print("\n🔍 CHECKING INPUT FILES FOR EMPTY OUTPUTS:")
    
    input_folders = [
        ("dialogs_file", "./input/dialogs_file/"),
        ("custom_file", "./input/custom_file/"),
        ("dvxml_file", "./input/dvxml_file/")
    ]
    
    missing_inputs = []
    existing_inputs = []
    
    for empty_file in empty_files:
        base_name = empty_file.replace('.yml', '.xml')
        found_input = False
        
        for folder_name, folder_path in input_folders:
            input_file_path = Path(folder_path) / base_name
            if input_file_path.exists():
                file_size = input_file_path.stat().st_size
                existing_inputs.append((empty_file, folder_name, file_size))
                found_input = True
                break
        
        if not found_input:
            missing_inputs.append(empty_file)
    
    if existing_inputs:
        print(f"\n✅ EMPTY FILES WITH EXISTING INPUT ({len(existing_inputs)} files):")
        print("These indicate LLM generation failures:")
        for empty_file, folder, size in existing_inputs:
            print(f"   {empty_file} <- {folder}/{empty_file.replace('.yml', '.xml')} ({size:,} bytes)")
    
    if missing_inputs:
        print(f"\n❌ EMPTY FILES WITH MISSING INPUT ({len(missing_inputs)} files):")
        for missing_file in missing_inputs:
            print(f"   {missing_file} (no corresponding XML found)")
    
    # Analyze file size distribution of successful files
    print(f"\n📈 FILE SIZE ANALYSIS FOR SUCCESSFUL FILES:")
    sizes = []
    for filename in files_with_content:
        file_path = unformatted_dir / filename
        size = file_path.stat().st_size
        sizes.append(size)
    
    if sizes:
        sizes.sort()
        print(f"   Smallest: {min(sizes):,} bytes")
        print(f"   Largest: {max(sizes):,} bytes")
        print(f"   Average: {sum(sizes)//len(sizes):,} bytes")
        print(f"   Median: {sizes[len(sizes)//2]:,} bytes")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if existing_inputs:
        print(f"1. 🔧 {len(existing_inputs)} files failed during LLM generation")
        print("   - Check Azure OpenAI API limits/quotas")
        print("   - Check prompt effectiveness")
        print("   - Check input XML file complexity")
        print("   - Consider retry mechanism")
    
    if missing_inputs:
        print(f"2. 📁 {len(missing_inputs)} files have no corresponding input")
        print("   - These might be generated during processing")
        print("   - Check if they should exist")
    
    print(f"\n3. ✅ Current success rate: {len(files_with_content)}/{len(files_with_content) + len(empty_files)} = {len(files_with_content)/(len(files_with_content) + len(empty_files))*100:.1f}%")
    
    return empty_files, existing_inputs, missing_inputs

def create_retry_script():
    """Create a script to retry processing empty files"""
    
    empty_files, existing_inputs, missing_inputs = investigate_empty_files()
    
    if existing_inputs:
        print(f"\n🔄 Creating retry script for {len(existing_inputs)} failed files...")
        
        retry_script = '''"""
Retry script for files that failed LLM generation
"""
import os
import sys
from llm_response import convertXmltoYaml
from fixed_indentation import fixed_topic_yaml_indentation
from util import remove_null_from_action
import time

def retry_failed_files():
    """Retry processing files that resulted in empty output"""
    
    failed_files = [
'''
        
        for empty_file, folder, size in existing_inputs:
            base_name = empty_file.replace('.yml', '')
            retry_script += f'        ("{base_name}", "{folder}"),\n'
        
        retry_script += '''    ]
    
    topic_template_path = './YAML-template/'
    output_yaml_path = './output/unformatted_topic_yaml/'
    final_topic_yaml = './output/topic_yaml/'
    
    prompts = {
        "dialogs_file": "prompts/few_shot_CPS_CS_topic.md",
        "custom_file": "prompts/few_shot_CPS_CS_topic.md", 
        "dvxml_file": "prompts/few_shot_CPS_DVXML.md"
    }
    
    success_count = 0
    total_count = len(failed_files)
    
    print(f"🔄 Retrying {total_count} failed files...")
    
    for file_name, folder_type in failed_files:
        try:
            print(f"⏳ Processing {file_name}...")
            
            # Paths
            input_file = f"./input/{folder_type}/{file_name}.xml"
            topic_template = topic_template_path + file_name + '.yml'
            output_file = output_yaml_path + file_name + '.yml'
            final_file = final_topic_yaml + file_name + '_topic.yml'
            
            # Check if input exists
            if not os.path.exists(input_file):
                print(f"❌ Input file not found: {input_file}")
                continue
            
            # Get prompt path
            prompt_path = prompts.get(folder_type, "prompts/few_shot_CPS_CS_topic.md")
            
            # Process with LLM
            convertXmltoYaml(input_file, prompt_path, topic_template)
            
            # Check if output was created and has content
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                # Apply fixed indentation
                fixed_topic_yaml_indentation(output_file, final_file)
                
                # Remove null from action
                remove_null_from_action(final_file)
                
                print(f"✅ Successfully processed {file_name}")
                success_count += 1
            else:
                print(f"❌ LLM generation failed again for {file_name}")
            
            # Add delay to avoid rate limiting
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ Error processing {file_name}: {e}")
    
    print(f"\\n🎉 Retry complete: {success_count}/{total_count} files recovered")

if __name__ == "__main__":
    retry_failed_files()
'''
        
        with open("retry_failed_files.py", "w", encoding="utf-8") as f:
            f.write(retry_script)
        
        print("📝 Created retry_failed_files.py")
        print("   Run it with: python retry_failed_files.py")

if __name__ == "__main__":
    create_retry_script()
