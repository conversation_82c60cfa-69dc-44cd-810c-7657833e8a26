<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="features" value="empty" type="String"/>
  <session-mapping key="featuresThirdParty" value="empty" type="String"/>
  <session-mapping key="ratePlan" value="empty" type="String"/>
  <session-mapping key="currentPromo" value="empty" type="String"/>
  <session-mapping key="futurePromo" value="empty" type="String"/>
  <session-mapping key="newRatePlan" value="empty" type="String"/>
  <session-mapping key="addFeatures" value="empty" type="String"/>
  <session-mapping key="prohibitSoc" value="empty" type="String"/>
  <session-mapping key="compatibleAddons" value="empty" type="String"/>
  <session-mapping key="isRatePlanChange" value="empty" type="String"/>
  <session-mapping key="isAddFeature" value="empty" type="String"/>
  <session-mapping key="newRatePlan" value="null" type="string"/>
  <session-mapping key="switchPlanAction" value="transferAfterSelected" type="string"/>
  <session-mapping key="doesAccountHasCurrentPromo" value="false" type="boolean"/>
  <session-mapping key="doesAccountHasFuturePromo" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.isOfferConflictDetected" value="true)&amp;" type="string"/>
  <session-mapping key="compatibleExistingPromo" value="false" type="string"/>
  <session-mapping key="compatibleFuturePromo" value="false" type="string"/>
  <session-mapping key="GlobalVars.isCareTransfer" value="true" type="boolean"/>
  <session-mapping key="isCurrentPromoURLorTTSAvailable" value="false)|" type="string"/>
  <session-mapping key="isFuturePromoURLorTTSAvailable" value="false" type="string"/>
  <session-mapping key="GlobalVars.callType" value="switch_phone" type="string"/>
  <session-mapping key="interpretation.dm_root" value="continue" type="string"/>
</session-mappings>
