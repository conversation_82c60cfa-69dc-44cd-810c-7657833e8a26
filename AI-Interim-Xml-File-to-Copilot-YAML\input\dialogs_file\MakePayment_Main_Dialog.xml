<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="MakePayment_Main_Dialog">
    <decision-state id="MP0000_CheckContext_DS">
      <session-mapping key="GlobalVars.guestPayment" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.nonANIPayment" value="false" type="Boolean"/>
      <action next="MP1000_BroadcastMessages_SD"/>
    </decision-state>

    <subdialog-state id="MP1000_BroadcastMessages_SD">
      <session-mapping key="GlobalVars.broadcastMessageKey" expr="'MP'"/>
      <gotodialog next="BroadcastMessages_Dialog"/>
      <action next="MP1000_BroadcastMessages_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MP1000_BroadcastMessages_SD_return_CS">
      <action next="MP1001_CheckEntryPoint_JDA"/>
    </custom-state>

    <decision-state id="MP1001_CheckEntryPoint_DS">
      <if cond="GlobalVars.firstStepFrom99Entry == true">
        <session-mapping key="GlobalVars.firstStepFrom99Entry" value="false" type="Boolean"/>
        <action next="MP2101_CheckPayNowContext_JDA"/>
        <elseif cond="(GlobalVars.tag == 'make-payment')">
          <action next="MP2101_CheckPayNowContext_JDA"/>
        </elseif>
      </if>
      <if cond="(GlobalVars.callType == 'make_pmt') &amp;&amp; (GlobalVars.GetAccountDetails == undefined || (GlobalVars.GetAccountDetails != null &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == undefined))">
        <action next="MP2050_PayByPhoneConfirmYN_DM"/>
        <elseif cond="GlobalVars.GetAccountDetails == undefined || (GlobalVars.GetAccountDetails != null &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == undefined)">
          <action next="MP2025_PaymentOptions_DM"/>
        </elseif>
        <elseif cond="GlobalVars.IsAddingFeature == true || GlobalVars.paymentsEntryPoint == 'careVoiceStore'">
          <session-mapping key="GlobalVars.IsAddingFeature" value="false" type="Boolean"/>
          <session-mapping key="dueImmediatelyAmount" value="(GlobalVars.GetAccountDetails == null)?0:GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
          <if cond="dueImmediatelyAmount &lt;= 3">
            <action next="MP2050_PayByPhoneConfirmYN_DM"/>
            <else>
              <action next="MP2025_PaymentOptions_DM"/>
            </else>
          </if>
        </elseif>
        <elseif cond="GlobalVars.GetAccountDetails.ratePlan.toLowerCase() == 'emp' || GlobalVars.acceptPayByPhone == true">
          <action next="MP2101_CheckPayNowContext_JDA"/>
        </elseif>
        <else>
          <if cond="GlobalVars.callType == 'make_pmt'">
            <action next="MP2050_PayByPhoneConfirmYN_DM"/>
            <else>
              <action next="MP2025_PaymentOptions_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <dm-state id="MP2025_PaymentOptions_DM" type="CUST">
      <session-mapping key="isReentry" value="GlobalVars.mp2025_reentry" type="String"/>
      <session-mapping key="IsAddingFeature" value="GlobalVars.IsAddingFeature" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="IsFromStoreLocator" value="GlobalVars.IsFromStoreLocator" type="String"/>
      <session-mapping key="IsFromVoiceStore" value="GlobalVars.IsFromVoiceStore" type="String"/>
      <session-mapping key="acceptPayByPhone" value="GlobalVars.acceptPayByPhone == undefined ? false : GlobalVars.acceptPayByPhone" type="String"/>
      <session-mapping key="offeredPayByPhone" value="GlobalVars.offeredPayByPhone == undefined ? false : GlobalVars.offeredPayByPhone" type="String"/>
      <session-mapping key="operatorPaymentsReqCount" value="GlobalVars.operatorPaymentsReqCount" type="String"/>
      <session-mapping key="care_allow_payment_transfers" value="GlobalVars.GetBCSParameters.care_allow_payment_transfers == 'true' || GlobalVars.GetBCSParameters.care_allow_payment_transfers == true" type="String"/>
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="switchLinesSuccess" value="GlobalVars.switchLinesSuccess" type="String"/>
      <session-mapping key="collection_grammar1" value="MP2025_PaymentOptions_DM.grxml" type="String"/>
      <session-mapping key="collection_dtmfgrammar1" value="MP2025_PaymentOptions_DM_dtmf.grxml" type="String"/>
      <if cond="aniMatch == true &amp;&amp; callType == 'make_pmt'">
        <session-mapping key="collection_grammar1" expr="collection_grammar1"/>
        <session-mapping key="collection_dtmfgrammar1" expr="collection_dtmfgrammar1"/>
        <else>
          <session-mapping key="collection_grammar1" expr="collection_grammar1 + '?SWI_vars.disallow=switch_account'"/>
          <session-mapping key="collection_dtmfgrammar1" expr="collection_dtmfgrammar1 + '?SWI_vars.disallow=switch_account'"/>
        </else>
      </if>
      <success>
        <session-mapping key="GlobalVars.mp2025_reentry" value="true" type="Boolean"/>
        <action label="find_payment_center">
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'payment'"/>
          <action next="MP2040_StoreLocator_SD"/>
        </action>
        <action label="make-payment">
          <action next="MP2101_CheckPayNowContext_JDA"/>
        </action>
        <action label="vague-autopay">
          <session-mapping key="GlobalVars.tag" expr="'vague-autopay'"/>
          <action next="MP2070_GoToAutoPay_SD"/>
        </action>
        <action label="change-plan">
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.mainMenuChoice" expr="'change_plan'"/>
          <if cond="GlobalVars.loggedIn == true">
            <action next="MP2060_RatePlan_SD"/>
            <else>
              <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
              <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
              <action next="MP2059_Login_SD"/>
            </else>
          </if>
        </action>
        <action label="switch-account_mp">
          <audio>
            <prompt id="MP2025_out_01">
              <prompt-segments>
                <audiofile text="Sure To manage a different account, you can call 611 from the phone you want to work with If you don t have it, you ll need to call from a landline , and dial 888 8METRO8 Again, that s 888 8 METRO 8, from a  landline Then, you ll just enter the number you want to work with " src="MP2025_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_750ms">
              <prompt-segments>
                <audiofile text="test" src="silence_750ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="MP2115_Goodbye_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.operatorFromPayment" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.operatorPaymentsReqCount" expr="GlobalVars.operatorPaymentsReqCount + 1"/>
          <submit expr="operatorSubmitFunction('MakePayment_Main.dvxml','MP2025_PaymentOptions_DM',MP2025_PaymentOptions_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="isReentry == false">
                <prompt id="MP2025_ini_05" cond="(acceptPayByPhone == false &amp;&amp; offeredPayByPhone == true) &amp;&amp; (aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                  <prompt-segments>
                    <audiofile text="If you d like to pay in person instead, say find a store You can say also say set up autopay, change my rate plan, or switch account You can also pay with the myMetro app on your phone, or online at metropcscom If you re done here you can simply hang up " src="MP2025_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MP2025_ini_02" cond="(acceptPayByPhone == false &amp;&amp; offeredPayByPhone == true) &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                  <prompt-segments>
                    <audiofile text="If you d like to pay in person instead, say  find a store  You can also say  set up autopay , or  change my rate plan  You can also pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MP2025_ini_01" cond="(acceptPayByPhone != false || offeredPayByPhone != true) &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                  <prompt-segments>
                    <audiofile text="You can say  pay now ,  find a store , or  set up autopay  You can also pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="care_allow_payment_transfers == false &amp;&amp; operatorPaymentsReqCount &gt; 0">
                  <prompt id="MP2025_rin_03" cond="operatorPaymentsReqCount == 1 &amp;&amp; (aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                    <prompt-segments>
                      <audiofile text="You can say  pay now ,  find a store ,  set up autopay ,  change my rate plan , or  switch account  You can also pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_rin_02" cond="operatorPaymentsReqCount == 1 &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                    <prompt-segments>
                      <audiofile text="You can say  pay now ,  find a store  You can say also say  set up autopay  or you can pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_rin_04" cond="operatorPaymentsReqCount &gt; 1 &amp;&amp; (aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                    <prompt-segments>
                      <audiofile text="Please say pay now, find a store, set up autopay, change my rate plan, or switch account If you don t want to pay right now, you can simply hang up" src="MP2025_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_ini_03" cond="operatorPaymentsReqCount &gt; 1 &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                    <prompt-segments>
                      <audiofile text="Please say 'pay now' 'find a store' or 'set up autopay' If you don't want to pay right now, you can simply hang up" src="MP2025_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="MP2025_ini_06" cond="(aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                    <prompt-segments>
                      <audiofile text="Please say pay now, find a store, set up autopay, change my rate plan, or switch account You can also pay with the myMetro app on your phone, or online at metropcscom If you re done here you can simply hang up " src="MP2025_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_ree_01" cond="(aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                    <prompt-segments>
                      <audiofile text="Please say  pay now ,  find a store  You can say also say  set up autopay  or you can pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_ree_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MP2025_PaymentOptions_DM_initial"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="isReentry == false">
                <prompt id="MP2025_ini_05" cond="(acceptPayByPhone == false &amp;&amp; offeredPayByPhone == true) &amp;&amp; (aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                  <prompt-segments>
                    <audiofile text="If you d like to pay in person instead, say find a store You can say also say set up autopay, change my rate plan, or switch account You can also pay with the myMetro app on your phone, or online at metropcscom If you re done here you can simply hang up " src="MP2025_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MP2025_ini_02" cond="(acceptPayByPhone == false &amp;&amp; offeredPayByPhone == true) &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                  <prompt-segments>
                    <audiofile text="If you d like to pay in person instead, say  find a store  You can also say  set up autopay , or  change my rate plan  You can also pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MP2025_ini_01" cond="(acceptPayByPhone != false || offeredPayByPhone != true) &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                  <prompt-segments>
                    <audiofile text="You can say  pay now ,  find a store , or  set up autopay  You can also pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="care_allow_payment_transfers == false &amp;&amp; operatorPaymentsReqCount &gt; 0">
                  <prompt id="MP2025_rin_03" cond="operatorPaymentsReqCount == 1 &amp;&amp; (aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                    <prompt-segments>
                      <audiofile text="You can say  pay now ,  find a store ,  set up autopay ,  change my rate plan , or  switch account  You can also pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_rin_02" cond="operatorPaymentsReqCount == 1 &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                    <prompt-segments>
                      <audiofile text="You can say  pay now ,  find a store  You can say also say  set up autopay  or you can pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_rin_04" cond="operatorPaymentsReqCount &gt; 1 &amp;&amp; (aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                    <prompt-segments>
                      <audiofile text="Please say pay now, find a store, set up autopay, change my rate plan, or switch account If you don t want to pay right now, you can simply hang up" src="MP2025_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_ini_03" cond="operatorPaymentsReqCount &gt; 1 &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                    <prompt-segments>
                      <audiofile text="Please say 'pay now' 'find a store' or 'set up autopay' If you don't want to pay right now, you can simply hang up" src="MP2025_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="MP2025_ini_06" cond="(aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                    <prompt-segments>
                      <audiofile text="Please say pay now, find a store, set up autopay, change my rate plan, or switch account You can also pay with the myMetro app on your phone, or online at metropcscom If you re done here you can simply hang up " src="MP2025_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_ree_01" cond="(aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                    <prompt-segments>
                      <audiofile text="Please say  pay now ,  find a store  You can say also say  set up autopay  or you can pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_ree_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="(aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                <prompt id="MP2025_nm1_02">
                  <prompt-segments>
                    <audiofile text="You can say pay now, or, if you want to pay in cash, say  find a payment center You can also say set up autopay, change my rate plan or to work with a different account say switch account If you re finished, just hang up  " src="MP2025_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MP2025_nm1_01">
                    <prompt-segments>
                      <audiofile text="You can say 'pay now', or, if you want to pay in cash, say  'find a payment center' You can also say 'set up autopay' pause 750 ms If you're finished, just hang up " src="MP2025_nm1_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                <prompt id="MP2025_nm2_02">
                  <prompt-segments>
                    <audiofile text="Please say 'pay now' or press 1 To see where you can pay in person, say 'find a store' or press 2  If you're calling about automatic payments , say autopay' or press 3 You can also say 'change my rate plan' or press 4 Or to work with a different account, say 'switch account' or press 5 If you're finished, just hang up" src="MP2025_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MP2025_nm2_01">
                    <prompt-segments>
                      <audiofile text="Please say 'pay now' or press 1 To see where you can pay in person, say 'find a store' or press 2 If you're calling about automatic payments , say autopay' or press 3 You can also say 'change my rate plan' or press 4 If you're finished, just hang up" src="MP2025_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                <prompt id="MP2025_nm2_02">
                  <prompt-segments>
                    <audiofile text="Please say 'pay now' or press 1 To see where you can pay in person, say 'find a store' or press 2  If you're calling about automatic payments , say autopay' or press 3 You can also say 'change my rate plan' or press 4 Or to work with a different account, say 'switch account' or press 5 If you're finished, just hang up" src="MP2025_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MP2025_nm2_01">
                    <prompt-segments>
                      <audiofile text="Please say 'pay now' or press 1 To see where you can pay in person, say 'find a store' or press 2 If you're calling about automatic payments , say autopay' or press 3 You can also say 'change my rate plan' or press 4 If you're finished, just hang up" src="MP2025_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                <prompt id="MP2025_nm1_02">
                  <prompt-segments>
                    <audiofile text="You can say pay now, or, if you want to pay in cash, say  find a payment center You can also say set up autopay, change my rate plan or to work with a different account say switch account If you re finished, just hang up  " src="MP2025_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MP2025_nm1_01">
                    <prompt-segments>
                      <audiofile text="You can say 'pay now', or, if you want to pay in cash, say  'find a payment center' You can also say 'set up autopay' pause 750 ms If you're finished, just hang up " src="MP2025_nm1_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                <prompt id="MP2025_nm2_02">
                  <prompt-segments>
                    <audiofile text="Please say 'pay now' or press 1 To see where you can pay in person, say 'find a store' or press 2  If you're calling about automatic payments , say autopay' or press 3 You can also say 'change my rate plan' or press 4 Or to work with a different account, say 'switch account' or press 5 If you're finished, just hang up" src="MP2025_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MP2025_nm2_01">
                    <prompt-segments>
                      <audiofile text="Please say 'pay now' or press 1 To see where you can pay in person, say 'find a store' or press 2 If you're calling about automatic payments , say autopay' or press 3 You can also say 'change my rate plan' or press 4 If you're finished, just hang up" src="MP2025_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                <prompt id="MP2025_nm2_02">
                  <prompt-segments>
                    <audiofile text="Please say 'pay now' or press 1 To see where you can pay in person, say 'find a store' or press 2  If you're calling about automatic payments , say autopay' or press 3 You can also say 'change my rate plan' or press 4 Or to work with a different account, say 'switch account' or press 5 If you're finished, just hang up" src="MP2025_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MP2025_nm2_01">
                    <prompt-segments>
                      <audiofile text="Please say 'pay now' or press 1 To see where you can pay in person, say 'find a store' or press 2 If you're calling about automatic payments , say autopay' or press 3 You can also say 'change my rate plan' or press 4 If you're finished, just hang up" src="MP2025_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="isReentry == false">
                <prompt id="MP2025_ini_05" cond="(acceptPayByPhone == false &amp;&amp; offeredPayByPhone == true) &amp;&amp; (aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                  <prompt-segments>
                    <audiofile text="If you d like to pay in person instead, say find a store You can say also say set up autopay, change my rate plan, or switch account You can also pay with the myMetro app on your phone, or online at metropcscom If you re done here you can simply hang up " src="MP2025_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MP2025_ini_02" cond="(acceptPayByPhone == false &amp;&amp; offeredPayByPhone == true) &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                  <prompt-segments>
                    <audiofile text="If you d like to pay in person instead, say  find a store  You can also say  set up autopay , or  change my rate plan  You can also pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MP2025_ini_01" cond="(acceptPayByPhone != false || offeredPayByPhone != true) &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                  <prompt-segments>
                    <audiofile text="You can say  pay now ,  find a store , or  set up autopay  You can also pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="care_allow_payment_transfers == false &amp;&amp; operatorPaymentsReqCount &gt; 0">
                  <prompt id="MP2025_rin_03" cond="operatorPaymentsReqCount == 1 &amp;&amp; (aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                    <prompt-segments>
                      <audiofile text="You can say  pay now ,  find a store ,  set up autopay ,  change my rate plan , or  switch account  You can also pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_rin_02" cond="operatorPaymentsReqCount == 1 &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                    <prompt-segments>
                      <audiofile text="You can say  pay now ,  find a store  You can say also say  set up autopay  or you can pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_rin_04" cond="operatorPaymentsReqCount &gt; 1 &amp;&amp; (aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                    <prompt-segments>
                      <audiofile text="Please say pay now, find a store, set up autopay, change my rate plan, or switch account If you don t want to pay right now, you can simply hang up" src="MP2025_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_ini_03" cond="operatorPaymentsReqCount &gt; 1 &amp;&amp; (aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                    <prompt-segments>
                      <audiofile text="Please say 'pay now' 'find a store' or 'set up autopay' If you don't want to pay right now, you can simply hang up" src="MP2025_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="MP2025_ini_06" cond="(aniMatch == true &amp;&amp; callType == 'make_pmt' &amp;&amp; switchLinesSuccess == false)">
                    <prompt-segments>
                      <audiofile text="Please say pay now, find a store, set up autopay, change my rate plan, or switch account You can also pay with the myMetro app on your phone, or online at metropcscom If you re done here you can simply hang up " src="MP2025_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MP2025_ree_01" cond="(aniMatch != true || callType != 'make_pmt' || switchLinesSuccess != false)">
                    <prompt-segments>
                      <audiofile text="Please say  pay now ,  find a store  You can say also say  set up autopay  or you can pay with the myMetro app on your phone, or online at metrobyt-mobilecom If you re done here you can simply hang up" src="MP2025_ree_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MP2025_PaymentOptions_DM.grxml" count="1"/>
          <dtmfgrammars filename="MP2025_PaymentOptions_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="MP2040_StoreLocator_SD">
      <session-mapping key="GlobalVars.storeLocatorReason" expr="'payment'"/>
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="MP2040_StoreLocator_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MP2040_StoreLocator_SD_return_CS">
      <action next="MP2115_Goodbye_SD"/>
    </custom-state>

    <subdialog-state id="MP2045_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="MP2045_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MP2045_GoToCallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Dialog"/>
    </custom-state>

    <subdialog-state id="MP2059_Login_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="MP2059_Login_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MP2059_Login_SD_return_CS">
      <action next="MP2060_RatePlan_SD"/>
    </custom-state>

    <subdialog-state id="MP2060_RatePlan_SD">
      <gotodialog next="RatePlan_Main_Dialog"/>
      <action next="MP2060_RatePlan_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MP2060_RatePlan_SD_return_CS">
      <action next="MP2115_Goodbye_SD"/>
    </custom-state>

    <dm-state id="MP2050_PayByPhoneConfirmYN_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.offeredPayByPhone" value="true" type="Boolean"/>
          <action next="MP2101_CheckPayNowContext_JDA"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.acceptPayByPhone" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.offeredPayByPhone" value="true" type="Boolean"/>
          <action next="MP2025_PaymentOptions_DM"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.acceptPayByPhone" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.offeredPayByPhone" value="true" type="Boolean"/>
          <action next="MP2025_PaymentOptions_DM"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MP2050_ini_01">
                <prompt-segments>
                  <audiofile text="Youd like to pay over the phone, right? " src="MP2050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="MP2050_PayByPhoneConfirmYN_DM_reprompt"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MP2050_ini_01">
                <prompt-segments>
                  <audiofile text="Youd like to pay over the phone, right? " src="MP2050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MP2050_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to pay over the phone now, say yes or press 1 To hear other ways you can make a payment, say no or press 2 " src="MP2050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MP2050_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to pay over the phone now, say yes or press 1 To hear other ways you can make a payment, say no or press 2 " src="MP2050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MP2050_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to pay over the phone now, say yes or press 1 To hear other ways you can make a payment, say no or press 2 " src="MP2050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MP2050_ree_01">
                <prompt-segments>
                  <audiofile text="Did you want to pay over the phone? Please say yes or no " src="MP2050_ree_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MP2050_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to pay over the phone now, say yes or press 1 To hear other ways you can make a payment, say no or press 2 " src="MP2050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MP2050_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to pay over the phone now, say yes or press 1 To hear other ways you can make a payment, say no or press 2 " src="MP2050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="MP2050_PayByPhoneConfirmYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="MP2050_PayByPhoneConfirmYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="MP2070_GoToAutoPay_SD">
      <gotodialog next="AutoPay_Main_Dialog"/>
      <action next="MP2070_GoToAutoPay_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MP2070_GoToAutoPay_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="MP2101_CheckPayNowContext_DS">
      <session-mapping key="paymentVars.eventTypeGMT" expr="getEventTime()"/>
      <session-mapping key="paymentVars.status" expr="'start'"/>
      <session-mapping key="paymentVars.eventType" expr="'Payment'"/>
      <session-mapping key="GlobalVars.operatorFromPayment" value="false" type="Boolean"/>
      <session-mapping key="dueImmediatelyAmount" value="(GlobalVars.GetAccountDetails == null)?0:GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="accountStatus" value="(GlobalVars.GetAccountDetails == null)?'':GlobalVars.GetAccountDetails.accountStatus" type="String"/>
      <session-mapping key="isLoggedIn" value="GlobalVars.loggedIn" type="String"/>
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <if cond="GlobalVars.paymentsEntryPoint == undefined || GlobalVars.paymentsEntryPoint == ''">
        <if cond="accountStatus == 'suspended' &amp;&amp; dueImmediatelyAmount &gt; 3">
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careSuspended'"/>
          <else>
            <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careMisc'"/>
          </else>
        </if>
      </if>
      <if cond="PaymentTable.ACTIVATION_STATUS == undefined || PaymentTable.ACTIVATION_STATUS == ''">
        <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="'100'"/>
        <session-mapping key="PaymentTable.ACTIVATION_STARTED" expr="getGMTTime()"/>
        <session-mapping key="PaymentTable.ACTIVATION_TYPE" expr="'9'"/>
        <session-mapping key="PaymentTable.ACTIVATION_MODE" expr="'0'"/>
        <session-mapping key="PaymentTable.CARD_TYPE" expr="'not_set'"/>
        <session-mapping key="GlobalVars.operatorFromPayment" value="false" type="Boolean"/>
      </if>
      <action next="MP2102_SendPMTEvent_DB_DA"/>
    </decision-state>

    <data-access-state id="MP2102_SendPMTEvent_DB_DA">
      <session-mapping key="monitoringFullString" value="" type="String"/>
      <data-access id="SendFrontendEvent" classname="com.nuance.metro.dataaccess.SendFrontendEvents">
        <inputs>
          <input-variable name="monitoringFullString"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="message"/>
        </outputs>
      </data-access>
      <action label="default">
        <action next="MP2103_CheckNeedLogin_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="MP2103_CheckNeedLogin_DS">
      <if cond="GlobalVars.aniMatch == true || GlobalVars.loggedIn == true">
        <if cond="language == 'en-US'">
          <session-mapping key="TransferTag" expr="'Payment_Support_English'"/>
          <else>
            <session-mapping key="TransferTag" expr="'Payment_Support_Spanish'"/>
          </else>
        </if>
        <action next="MP2110_StartServicePayment_SD"/>
        <else>
          <if cond="GlobalVars.GetAccountDetails == undefined">
            <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
            <session-mapping key="GlobalVars.nonANIPayment" value="true" type="Boolean"/>
          </if>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <action next="MP2105_Login_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="MP2105_Login_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="MP2105_Login_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MP2105_Login_SD_return_CS">
      <session-mapping key="dueImmediatelyAmount" value="(GlobalVars.GetAccountDetails == null)?0:GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="accountStatus" value="(GlobalVars.GetAccountDetails == null)?'':GlobalVars.GetAccountDetails.accountStatus" type="String"/>
      <if cond="accountStatus == 'suspended' &amp;&amp; dueImmediatelyAmount &gt; 3">
        <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careSuspended'"/>
      </if>
      <if cond="language == 'en-US'">
        <session-mapping key="TransferTag" expr="'Payment_Support_English'"/>
        <else>
          <session-mapping key="TransferTag" expr="'Payment_Support_Spanish'"/>
        </else>
      </if>
      <action next="MP2110_StartServicePayment_SD"/>
    </custom-state>

    <subdialog-state id="MP2110_StartServicePayment_SD">
      <gotodialog next="StartServicePayment_Main_Dialog"/>
      <action next="MP2110_StartServicePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MP2110_StartServicePayment_SD_return_CS">
      <if cond="GlobalVars.callType == 'make_pmt_auto_pay'">
        <session-mapping key="GlobalVars.tag" expr="'setup_autopay'"/>
        <action next="MP2070_GoToAutoPay_SD"/>
        <elseif cond="GlobalVars.authFailure == true || GlobalVars.paymentFailure == true || GlobalVars.bcrPendingPmtTransfer == true">
          <action next="MP2045_GoToCallTransfer_SD"/>
        </elseif>
        <else>
          <if cond="GlobalVars.callType == 'goodbye'">
            <action next="MP2115_Goodbye_SD"/>
            <else>
              <if cond="language == 'en-US'">
                <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
                <elseif cond="language == 'es-US'">
                  <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
                </elseif>
              </if>
              <if cond="GlobalVars.callType == 'extension'">
                <session-mapping key="GlobalVars.tag" expr="'request-extension'"/>
              </if>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="MP2115_Goodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </subdialog-state>
    <custom-state id="MP2115_Goodbye_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

  </dialog>
  