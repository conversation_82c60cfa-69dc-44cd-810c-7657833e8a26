import yaml
import ruamel.yaml
import uuid
import ruamel.yaml
from collections import OrderedDict
import logging
import os, re
import xml.etree.ElementTree as ET
import xml.dom.minidom as minidom


logging.basicConfig(filename='agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')


# Function to generate unique values in the given format
def generate_unique_value():
    return f"_{uuid.uuid4().hex[:6]}"


# Function to replace '_REPLACE_THIS' with unique values in the given YAML content
def update_yaml_content(yaml_content):
    updated_content = yaml_content
    while '_REPLACE_THIS' in updated_content:
        updated_content = updated_content.replace('_REPLACE_THIS', generate_unique_value(), 1)
    return updated_content


# Function to read YAML content from a file, update it, and return the updated content
def update_yaml_file(file_path):
    with open(file_path, 'r') as file:
        yaml_content = file.read()
    
    updated_content = update_yaml_content(yaml_content)
    return updated_content


def handle_yaml_error(yaml_content, reference_yaml_path):
    # Function to handle the error and fix the YAML
    print("An error occurred while processing the YAML. Attempting to fix the error...")
    with open(reference_yaml_path, 'r') as ref_file:
        reference_yaml = yaml.safe_load(ref_file)
    fixed_yaml = yaml.dump(reference_yaml, default_flow_style=False)
    print("YAML has been fixed.")
    return fixed_yaml

#function to validate yaml
def validate_and_fix_yaml(yaml_content, reference_yaml_path):
    try:
        yaml.safe_load(yaml_content)
        print("YAML is valid.")
        return yaml_content
    except yaml.YAMLError as exc:
        print(f"YAML has issues: {exc}. Fixing using reference YAML...")
        return handle_yaml_error(yaml_content, reference_yaml_path)
    
# Function to delete existing files from folder
def delete_existing_files(path):

    files_name = read_filenames_in_folder(path)
    for file in files_name:
        if os.path.exists(path+file):
            os.remove(path+file)
            logging.debug(f"Deleted existing file: {path+file}")
        else:
            logging.debug(f"No existing file found: {path+file}")

# Function to read filenames in a multiple folder 
def read_filenames_in_folders(folder_path1, folder_path2,folder_path3):
    logging.debug("Util: read_filenames_in_folders")
    
    # Retrieve files from both folders
    files_folder1 = read_filenames_in_folder(folder_path1)
    files_folder2 = read_filenames_in_folder(folder_path2)
    files_folder3 = read_filenames_in_folder(folder_path3)
    
    # Combine the file lists
    all_files = files_folder1 + files_folder2 + files_folder3

    return all_files

# Function to read filenames from folder 
def read_filenames_in_folder(folder_path):
    logging.debug("util: read_filenames_in_folder")
    try:
        # List all files in the given directory
        filenames = os.listdir(folder_path)
        
        # Filter out directories, only keeping files
        files = [f for f in filenames if os.path.isfile(os.path.join(folder_path, f))]

        return files
    except FileNotFoundError:
        logging.error(f"The folder {folder_path} does not exist.")
        return []
    except Exception as e:
        logging.error(f"An error occurred: {str(e)}")
        return []

#REFORMATE THE CONTENT OF DA NODE
def reformat_content(content):
    # Remove the leading '={', trailing '}', and extra spaces
    content = content.strip()[2:-1].strip()
    # Split the content into individual lines for formatting
    lines = content.split(',')
    # Format the content as desired with indentation
    formatted_content = "={\n" + ",\n".join(f"    {line.strip()}" for line in lines) + "\n}"
    
    return formatted_content

def log_failed_file(file_name):
    with open('failed_files.txt', 'a') as f:
        f.write(f"{file_name}\n")

#function to remove null from yaml action
def remove_null_from_action(fileName):
    #Repalcing null with empty for key:ACTION
    with open(fileName, 'r') as yml_file:
        yml_content = yml_file.read()

    # Replace 'actions' key with an empty string
    updated_content = yml_content.replace('actions: null', 'actions: ')
    with open(fileName, 'w') as yml_file:
        yml_file.write(updated_content)

# Function to fix content of DA node
def fix_DA_input_format(file_path):
    yaml = ruamel.yaml.YAML()
    yaml.preserve_quotes = True  # Preserve quotes when loading the YAML file

    with open(file_path, 'r') as file:
        data = yaml.load(file)

    # Traverse through the data to find 'HttpRequestAction' and update 'content' field
    def traverse_and_update(node):
        if isinstance(node, dict):
            # # Check if 'kind' is 'HttpRequestAction' and 'content' is in 'body'
            # if node.get('kind') == 'HttpRequestAction' and 'content' in node.get('body', {}):
            #     # Update the content field using the reformat_content function
            #     formatted_content  = reformat_content(node['body']['content'])
            #     node['body']['content'] = ruamel.yaml.scalarstring.LiteralScalarString(formatted_content)

            # Check if 'kind' is 'ConditionGroup'
            if node.get('kind') == 'ConditionGroup' and 'conditions' in node:
                for condition in node['conditions']:
                    if 'condition' in condition:
                       # Update the condition to use the pipe syntax
                        condition['condition'] = ruamel.yaml.scalarstring.LiteralScalarString(
                             condition['condition'].strip()
                        )

            for key, value in node.items():
                traverse_and_update(value)

        elif isinstance(node, list):
            # Recursively update each item in lists
            for item in node:
                traverse_and_update(item)

    # Call the traversal function to update content fields
    traverse_and_update(data)

    # Save the updated data back to the YAML file
    with open(file_path, 'w') as file:
        yaml.dump(data, file)

#function to update the conditions in yaml file
def update_conditions(file_path):
    yaml = ruamel.yaml.YAML()
    yaml.preserve_quotes = True  # Preserve quotes when loading the YAML file

    with open(file_path, 'r') as file:
        data = yaml.load(file)

    # Function to recursively traverse the YAML structure and update conditions
    def traverse_and_update(node):
        if isinstance(node, dict):
            # Check if 'kind' is 'ConditionGroup'
            if node.get('kind') == 'ConditionGroup' and 'conditions' in node:
                for condition in node['conditions']:
                    if 'condition' in condition:
                        # Update the condition to use the pipe syntax
                        condition['condition'] = ruamel.yaml.scalarstring.LiteralScalarString(
                            f"|\n    {condition['condition'].strip()}"
                        )
                        # Set the style to preserve the line breaks correctly
                        condition['condition'].style = None  # Ensures no chomped indicator

            # Recursively check nested dictionaries
            for key, value in node.items():
                traverse_and_update(value)

        elif isinstance(node, list):
            # Recursively check each item in lists
            for item in node:
                traverse_and_update(item)

    # Update the conditions in the loaded data
    traverse_and_update(data)

    # Save the updated data back to the YAML file
    with open(file_path, 'w') as file:
        yaml.dump(data, file)

#Function to merge variable files
def merge_variable_files(input_directory, output_file):
    logging.info('util: merge_variable_files')
    merged_root = ET.Element('conditions')
    unique_mappings = {}

    for filename in os.listdir(input_directory):
        if filename.endswith('_variable.xml'):
            file_path = os.path.join(input_directory, filename)
            tree = ET.parse(file_path)
            root = tree.getroot()

            for mapping in root.findall('session-mapping'):
                key = mapping.get('key')
                value = mapping.get('value')
                type_ = mapping.get('type')

                # Only add if the key doesn't exist or if it has a non-empty value
                if key not in unique_mappings or (value and value != "undefined"):
                    unique_mappings[key] = (value, type_)

    # Create new XML elements for unique mappings
    for key, (value, type_) in unique_mappings.items():
        mapping = ET.SubElement(merged_root, 'session-mapping')
        mapping.set('key', key)
        mapping.set('value', value)
        mapping.set('type', type_)

    # Create a new tree
    merged_tree = ET.ElementTree(merged_root)

    # Convert to a string and prettify
    xml_string = ET.tostring(merged_root, 'utf-8')
    pretty_xml = minidom.parseString(xml_string).toprettyxml(indent="  ")

    # Write the prettified XML to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(pretty_xml)

    logging.info(f'Merged variable file created: {output_file}')
