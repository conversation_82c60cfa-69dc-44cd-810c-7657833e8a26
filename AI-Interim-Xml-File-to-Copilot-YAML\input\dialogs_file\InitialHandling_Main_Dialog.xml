<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="InitialHandling_Main_Dialog">
    <decision-state id="IH1002_CheckBCSConfigSuccess_DS">
      <if cond="GlobalVars.isBCSSuccess == true">
        <if cond="GlobalVars.returnToMainMenu == true">
          <session-mapping key="GlobalVars.nluReEntryAfterSelfService" value="true" type="Boolean"/>
          <gotodialog next="InitialHandling_PreMenuLogic#IH1805_CheckLanguage_DS"/>
        </if>
        <if cond="(GlobalVars.operatorRequestMidFlowNLU == true) || (GlobalVars.switchLinesSuccess == true)">
          <gotodialog next="InitialHandling_PreMenuLogic#IH1805_CheckLanguage_DS"/>
          <else>
            <action next="IH1004_SetEntryVariables_DB_DA"/>
          </else>
        </if>
        <else>
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
          <action next="IH1090_CallTransfer_SD"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="IH1004_SetEntryVariables_DB_DA">
      <action label="default">
        <session-mapping key="GlobalVars.isMarketOutageFlag" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.IsFromStoreLocator" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.isCareTransfer" expr="'true'"/>
        <session-mapping key="GlobalVars.coopMaxRequest" expr="GlobalVars.GetBCSParameters.accountrating_maxrequest_cooperative_medium"/>
        <session-mapping key="GlobalVars.unCoopMaxRequest" expr="GlobalVars.GetBCSParameters.accountrating_maxrequest_uncooperative_medium"/>
        <session-mapping key="GlobalVars.heardThirdPartyInfo" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
        <session-mapping key="GlobalVars.troubleshootingTransfer" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.loggedIn" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.returningFromPayments" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.firstStepFrom99Entry" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.dataCollectionTransfer" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.switchLinesSuccess" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.verificationType" expr="'pin'"/>
        <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'not_attempted'"/>
        <session-mapping key="GlobalVars.cti_MDN" expr="'0000000000'"/>
        <session-mapping key="GlobalVars.cti_PIN" expr="'00000000'"/>
        <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'authenticated_default')"/>
        <session-mapping key="GlobalVars.cti_Intent" expr="'000'"/>
        <session-mapping key="GlobalVars.cti_TransferReason" expr="getctiTransferReason(GlobalVars.GetCTIParameters.ctiTransferReason, 'xferred_default')"/>
        <session-mapping key="GlobalVars.suspendedRatePlanChange" value="false" type="Boolean"/>
        <if cond="(GlobalVars.enteredFrom == '490' || GlobalVars.enteredFrom == '491')">
          <if cond="GlobalVars.enteredFrom == '490'  || GlobalVars.enteredFrom == '611_228Entry_english'">
            <session-mapping key="language" expr="'en-US'"/>
            <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
            <session-mapping key="haveLangbasedTFN" value="true" type="Boolean"/>
            <elseif cond="GlobalVars.enteredFrom == '491'  || GlobalVars.enteredFrom == '611_228Entry_spanish' ">
              <session-mapping key="language" expr="'es-US'"/>
              <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
              <session-mapping key="haveLangbasedTFN" value="true" type="Boolean"/>
            </elseif>
            <else>
              <session-mapping key="language" expr="'en-US'"/>
            </else>
          </if>
          <elseif cond="(GlobalVars.enteredFrom == '888_datacollection' || GlobalVars.enteredFrom == '611_datacollection')">
            <session-mapping key="GlobalVars.serviceDialed" expr="'888-111'"/>
            <session-mapping key="haveLangbasedTFN" value="false" type="Boolean"/>
            <session-mapping key="GlobalVars.networkType" expr="'G'"/>
          </elseif>
          <elseif cond="GlobalVars.enteredFrom == '888'">
            <session-mapping key="GlobalVars.serviceDialed" expr="'888'"/>
          </elseif>
          <elseif cond="GlobalVars.enteredFrom == '99'">
            <session-mapping key="GlobalVars.serviceDialed" expr="'99'"/>
            <session-mapping key="language" expr="'en-US'"/>
            <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
            <session-mapping key="haveLangbasedTFN" value="false" type="Boolean"/>
          </elseif>
          <elseif cond="GlobalVars.enteredFrom == '99_english'">
            <session-mapping key="GlobalVars.serviceDialed" expr="'99'"/>
            <session-mapping key="language" expr="'en-US'"/>
            <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
            <session-mapping key="haveLangbasedTFN" value="true" type="Boolean"/>
          </elseif>
          <elseif cond="GlobalVars.enteredFrom == '99_spanish'">
            <session-mapping key="GlobalVars.serviceDialed" expr="'99'"/>
            <session-mapping key="language" expr="'es-US'"/>
            <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
            <session-mapping key="haveLangbasedTFN" value="true" type="Boolean"/>
          </elseif>
          <elseif cond="GlobalVars.enteredFrom == 'PWR' || GlobalVars.enteredFrom == 'PC'">
            <session-mapping key="haveLangbasedTFN" value="true" type="Boolean"/>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.serviceDialed" expr="'611'"/>
            <session-mapping key="GlobalVars.networkType" expr="'G'"/>
          </else>
        </if>
        <if cond="GlobalVars.enteredFrom == '490' || GlobalVars.enteredFrom == '491'">
          <session-mapping key="GlobalVars.fromMyMetroCustomerSupport" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
          <session-mapping key="haveLangbasedTFN" value="true" type="Boolean"/>
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'MyMetro_Support_English'"/>
            <else>
              <session-mapping key="language" expr="'es-US'"/>
              <session-mapping key="TransferTag" expr="'MyMetro_Support_Spanish'"/>
            </else>
          </if>
          <action next="IH1090_CallTransfer_SD"/>
          <else>
            <action next="IH1008_PlayWelcome_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="IH1008_PlayWelcome_DS">
      <if cond="GlobalVars.enteredFrom == 'DSG' || GlobalVars.enteredFrom == 'NRH' || GlobalVars.enteredFrom == 'WNP' || GlobalVars.enteredFrom == 'PWR' || GlobalVars.enteredFrom == 'PC'">
        <if cond="GlobalVars.outageByColo">
          <action next="IH1016_CheckRouting_JDA"/>
          <else>
            <action next="IH1010_GetAccountDetails_DB_DA"/>
          </else>
        </if>
        <else>
          <action next="IH1015_PlayWelcome_PP"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="IH1010_GetAccountDetails_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.trn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <session-mapping key="haveMDN" value="false" type="Boolean"/>
      <session-mapping key="MDN" value="" type="String"/>
      <session-mapping key="GlobalVars.aniMatch" value="false" type="Boolean"/>
      <data-access id="GetSubscriberDetails" classname="com.nuance.metro.dataaccess.GetSubscriberDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="languageCode"/>
          <input-variable name="sessionId"/>
          <input-variable name="imei"/>
          <input-variable name="clearCache"/>
          <input-variable name="multiLineDetails"/>
        </inputs>
        <outputs>
          <output-variable name="mdn"/>
          <output-variable name="accountNumber" mask="true"/>
          <output-variable name="active"/>
          <output-variable name="serialNumber"/>
          <output-variable name="features"/>
          <output-variable name="thirdPartyFeatures"/>
          <output-variable name="payDate"/>
          <output-variable name="taxInclusive"/>
          <output-variable name="accountStatus"/>
          <output-variable name="dueImmediatelyAmount"/>
          <output-variable name="highSecurity"/>
          <output-variable name="ratePlan"/>
          <output-variable name="marketID"/>
          <output-variable name="language"/>
          <output-variable name="isAutopayEnabled"/>
          <output-variable name="hasPhoneInsurance"/>
          <output-variable name="balance"/>
          <output-variable name="isDeviceAllowed"/>
          <output-variable name="isSuspended"/>
          <output-variable name="accountType"/>
          <output-variable name="validAccount"/>
          <output-variable name="roamingBucket"/>
          <output-variable name="paymentStatus"/>
          <output-variable name="ratePlanAttributesMap"/>
          <output-variable name="networkType"/>
          <output-variable name="securityQuestionCode"/>
          <output-variable name="hasEWallet"/>
          <output-variable name="eligibleForBillCycleReset"/>
          <output-variable name="billCycleLength"/>
          <output-variable name="firstName"/>
          <output-variable name="lastName"/>
          <output-variable name="eligibleForUnhotlineCheck"/>
          <output-variable name="securityQuestionString"/>
          <output-variable name="iccid"/>
          <output-variable name="imei"/>
          <output-variable name="hasAutopayPending"/>
          <output-variable name="insuranceCode"/>
          <output-variable name="subscriberId"/>
          <output-variable name="usedESNAllowed"/>
          <output-variable name="emailVerificationStatus"/>
          <output-variable name="emailAddress"/>
          <output-variable name="currentPromo"/>
          <output-variable name="futurePromo"/>
          <output-variable name="featureCodes"/>
          <output-variable name="superRegion"/>
          <output-variable name="accountFutureRequestInd"/>
          <output-variable name="subscriberFuturePricePlanInd"/>
          <output-variable name="ratePlanPrice"/>
          <output-variable name="currentPlanPromptURL"/>
          <output-variable name="currentPlanPromptTTS"/>
          <output-variable name="familyPlanIndicator"/>
          <output-variable name="topupEligibility"/>
          <output-variable name="customerTreatmentLevel"/>
          <output-variable name="HasHighSpeedData"/>
          <output-variable name="includedFeatures"/>
          <output-variable name="hasDataMaximizer"/>
          <output-variable name="eligibleForUpgrade"/>
          <output-variable name="upgradeEligibilityDate"/>
          <output-variable name="arBalance"/>
          <output-variable name="multilineList"/>
          <output-variable name="planMedialPromptURL"/>
          <output-variable name="planMedialPromptTTS"/>
          <output-variable name="hasDefaultAccountValues"/>
          <output-variable name="zipCode" mask="true"/>
          <output-variable name="accountRestrictIndicator"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetSubscriberDetails.status &amp;&amp; GetSubscriberDetails.status.toUpperCase() == 'SUCCESS' &amp;&amp; GetSubscriberDetails.validAccount">
          <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
          <session-mapping key="GlobalVars.unCoopMaxRequest" expr="GetSubscriberDetails.unCoopMaxRequest"/>
          <session-mapping key="GlobalVars.coopMaxRequest" expr="GetSubscriberDetails.coopMaxRequest"/>
          <session-mapping key="GlobalVars.marketID" expr="GetSubscriberDetails.marketID"/>
          <session-mapping key="GlobalVars.zipCode" expr="GetSubscriberDetails.zipCode"/>
          <session-mapping key="GlobalVars.mdn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.networkType" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="GlobalVars.trn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.accountNumber" expr="GetSubscriberDetails.accountNumber"/>
          <session-mapping key="GlobalVars.isOnFamilyPlan" expr="GetSubscriberDetails.isOnFamilyPlan"/>
          <session-mapping key="GlobalVars.firstName" expr="GetSubscriberDetails.firstName"/>
          <session-mapping key="GlobalVars.lastName" expr="GetSubscriberDetails.lastName"/>
          <session-mapping key="GlobalVars.eligibleForUpgrade" expr="GetSubscriberDetails.isCurrentlyEligibleForDeviceUpgrade"/>
          <session-mapping key="GlobalVars.upgradeEligibilityDate" expr="GetSubscriberDetails.upgradeEligibilityDate"/>
          <if cond="(GetSubscriberDetails.accountStatus == 'active' || GetSubscriberDetails.accountStatus == 'suspended')">
            <session-mapping key="GlobalVars.aniMatch" value="true" type="Boolean"/>
          </if>
          <session-mapping key="haveMDN" expr="GlobalVars.GetAccountDetails != null  &amp;&amp;(GlobalVars.GetAccountDetails.validAccount == 'true' || GlobalVars.GetAccountDetails.validAccount == true) &amp;&amp; GlobalVars.GetAccountDetails.mdn != undefined"/>
          <if cond="haveMDN == true">
            <session-mapping key="MDN" expr="GetSubscriberDetails.mdn"/>
            <session-mapping key="GlobalVars.MDN" expr="MDN"/>
            <session-mapping key="GlobalVars.areacode" expr="MDN.substring(0,3)"/>
            <session-mapping key="GlobalVars.NXX" expr="MDN.substring(3,6)"/>
            <session-mapping key="GlobalVars.cti_MDN" expr="MDN"/>
          </if>
          <session-mapping key="GlobalVars.accountFutureRequestInd" expr="GetSubscriberDetails.accountFutureRequestInd"/>
          <session-mapping key="GlobalVars.subscriberFuturePricePlanInd" expr="GetSubscriberDetails.subscriberFuturePricePlanInd"/>
        </if>
        <action next="IH1016_CheckRouting_JDA"/>
      </action>
    </data-access-state>

    <play-state id="IH1015_PlayWelcome_PP">
      <session-mapping key="min" value="GlobalVars.trn" type="String"/>
      <session-mapping key="serviceDialed" value="GlobalVars.serviceDialed" type="String"/>
      <session-mapping key="audioMessageKey" value="care_configurable_greeting_audio" type="String"/>
      <audio>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="IH1015_out_01" cond="serviceDialed == '99'">
          <prompt-segments>
            <audiofile text="Hi there, it's Metro, by T-Mobile! Welcome to our Payments line" src="IH1015_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <if type="java">
          <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
            <param name="numToMatch" value="1"/>
            <param name="startNum" value="1"/>
            <param name="endNum" value="5"/>
            <param name="optionalBoolean" value="isActive" scope="request"/>
          </condition>
          <prompt id="IH1015_out_02" cond="serviceDialed != '99'">
            <prompt-segments>
              <audiofile text="Hi there, it's Metro, by T-Mobile! " src="IH1015_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <elseif>
            <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
              <param name="numToMatch" value="2"/>
              <param name="startNum" value="1"/>
              <param name="endNum" value="5"/>
              <param name="optionalBoolean" value="isActive" scope="request"/>
            </condition>
            <prompt id="IH1015_out_03" cond="serviceDialed != '99'">
              <prompt-segments>
                <audiofile text="Welcome to Metro, by T-Mobile! " src="IH1015_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif>
            <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
              <param name="numToMatch" value="3"/>
              <param name="startNum" value="1"/>
              <param name="endNum" value="5"/>
              <param name="optionalBoolean" value="isActive" scope="request"/>
            </condition>
            <prompt id="IH1015_out_04" cond="serviceDialed != '99'">
              <prompt-segments>
                <audiofile text="You've reached Metro, by T-Mobile! " src="IH1015_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif>
            <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
              <param name="numToMatch" value="4"/>
              <param name="startNum" value="1"/>
              <param name="endNum" value="5"/>
              <param name="optionalBoolean" value="isActive" scope="request"/>
            </condition>
            <prompt id="IH1015_out_05" cond="serviceDialed != '99'">
              <prompt-segments>
                <audiofile text="It's Metro, by T-Mobile, thanks for calling in!" src="IH1015_out_05.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif>
            <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
              <param name="numToMatch" value="5"/>
              <param name="startNum" value="1"/>
              <param name="endNum" value="5"/>
              <param name="optionalBoolean" value="isActive" scope="request"/>
            </condition>
            <prompt type="custom" expr="audioMessageKey" cond="serviceDialed != '99'" bargein="false">
              <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayAudioMessage"/>
            </prompt>
          </elseif>
        </if>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="IH1010_GetAccountDetails_DB_DA"/>
    </play-state>

    <decision-state id="IH1016_CheckRouting_DS">
      <session-mapping key="min" value="GlobalVars.trn" type="String"/>
      <if cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountRestrictIndicator == true">
        <action next="IH1025_FraudSuspension_PP"/>
        <elseif cond="min.substring(0,6) == '000000'">
          <session-mapping key="GlobalVars.invalidMDN" value="true" type="Boolean"/>
          <action next="IH1100_NeedLanguage_JDA"/>
        </elseif>
      </if>
      <if cond="GlobalVars.enteredFrom == 'WNP'">
        <if cond="GlobalVars.aniMatch == true &amp;&amp; GlobalVars.GetAccountDetails &amp;&amp;  GlobalVars.GetAccountDetails.accountStatus == 'suspended'">
          <gotodialog next="InitialHandling_PreMenuLogic#IH1210_SuspendedHandling_SD"/>
          <else>
            <action next="getReturnLink()"/>
          </else>
        </if>
        <elseif cond="GlobalVars.aniMatch == true &amp;&amp; GlobalVars.enteredFrom == 'OMNI'">
          <action next="IH1020_IntentPrediction_SD"/>
        </elseif>
        <else>
          <action next="IH1100_NeedLanguage_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="IH1025_FraudSuspension_PP">
      <audio>
        <prompt id="IH1025_out_01">
          <prompt-segments>
            <audiofile text="Your account requires assistance " src="IH1025_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="IH1090_CallTransfer_SD"/>
    </play-state>

    <subdialog-state id="IH1020_IntentPrediction_SD">
      <gotodialog next="IntentPrediction_Dialog"/>
      <action next="IH1020_IntentPrediction_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1020_IntentPrediction_SD_return_CS">
      <action next="IH1100_NeedLanguage_JDA"/>
    </custom-state>

    <decision-state id="IH1100_NeedLanguage_DS">
      <if cond="haveLangbasedTFN == true || GlobalVars.enteredFrom == 'DSG' || GlobalVars.enteredFrom == 'NRH'">
        <action next="IH1117_CheckForStatePrivacyMessage_JDA"/>
        <else>
          <if cond="GlobalVars.aniMatch == true">
            <action next="IH1105_QueryLanguage_DB_DA"/>
            <else>
              <action next="IH1125_TeachLanguage_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <data-access-state id="IH1105_QueryLanguage_DB_DA">
      <session-mapping key="min" value="GlobalVars.trn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="QueryLanguage" classname="com.nuance.metro.dataaccess.QueryLanguage">
        <inputs>
          <input-variable name="min"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="lang"/>
          <output-variable name="count"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="QueryLanguage.status == 'Success'">
          <session-mapping key="GlobalVars.QueryLanguage" expr="QueryLanguage"/>
          <action next="IH1110_CheckLanguagePreSelected_JDA"/>
          <else>
            <action next="IH1125_TeachLanguage_DM"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="IH1110_CheckLanguagePreSelected_DS">
      <if cond="GlobalVars.QueryLanguage.lang !='unknown'">
        <if cond="GlobalVars.QueryLanguage.lang == 'en-US'">
          <action next="IH1115_SetLanguage_DB_DA"/>
          <else>
            <action next="IH1125_TeachLanguage_DM"/>
          </else>
        </if>
        <else>
          <action next="IH1125_TeachLanguage_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="IH1125_TeachLanguage_DM" type="CUST">
      <session-mapping key="GlobalVars.langAskedAlready" value="true" type="Boolean"/>
      <success>
        <action label="default" next="IH1002_CheckBCSConfigSuccess_JDA">
          <session-mapping key="GlobalVars.langAskedAlready" value="true" type="Boolean"/>
        </action>
        <action label="spanish">
          <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
          <session-mapping key="GlobalVars.langAskedAlready" value="true" type="Boolean"/>
          <if cond="language == 'en-US'">
            <session-mapping key="language" expr="'es-US'"/>
            <elseif cond="language == 'es-US'">
              <session-mapping key="language" expr="'en-US'"/>
            </elseif>
          </if>
          <if cond="GlobalVars.aniMatch == true">
            <action next="IH1115_SetLanguage_DB_DA"/>
            <else>
              <action next="IH1118_CheckTFN_JDA"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="IH1125_ini_01">
                <prompt-segments>
                  <audiofile text="To continue in English, press 9" src="IH1125_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="IH1125_ini_01">
                <prompt-segments>
                  <audiofile text="To continue in English, press 9" src="IH1125_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="IH1030_TeachLanguage_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="3000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="3000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="IH1115_SetLanguage_DB_DA">
      <session-mapping key="min" value="GlobalVars.trn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="lang" value="language" type="String"/>
      <session-mapping key="isGreetByNameEnabled" value="GlobalVars.GetBCSParameters.care_greetByName_enabled" type="String"/>
      <data-access id="SetLanguage" classname="com.nuance.metro.dataaccess.SetLanguage">
        <inputs>
          <input-variable name="min"/>
          <input-variable name="lang"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="isGreetByNameEnabled"/>
        </outputs>
      </data-access>
      <action label="default">
        <action next="IH1118_CheckTFN_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="IH1116_PlayCCPADisclaimer_DS">
      <session-mapping key="playedCCPA" value="GlobalVars.playedCCPA" type="String"/>
      <if cond="GlobalVars.GetAccountDetails == undefined &amp;&amp; playedCCPA != true">
        <session-mapping key="GlobalVars.broadcastMessageCallingDialog" expr="'IHPrivacy'"/>
        <session-mapping key="GlobalVars.sbpStateCode" expr="'NA'"/>
        <action next="IH1130_GoToBroadcastMessages_SD"/>
      </if>
      <if cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.sbpStateCode != null &amp;&amp; GlobalVars.GetAccountDetails.sbpStateCode != '' &amp;&amp; playedCCPA != true">
        <session-mapping key="GlobalVars.broadcastMessageCallingDialog" expr="'IHPrivacy'"/>
        <action next="IH1130_GoToBroadcastMessages_SD"/>
      </if>
      <if cond="(GlobalVars.serviceDialed == '611') &amp;&amp; (!(GlobalVars.enteredFrom == 'DSG' || GlobalVars.enteredFrom == 'NRH')) &amp;&amp; (GlobalVars.aniMatch == true || GlobalVars.aniMatch =='true') &amp;&amp; (GlobalVars.GetAccountDetails != null &amp;&amp; GlobalVars.GetAccountDetails != undefined) ">
        <session-mapping key="GlobalVars.broadcastMessageCallingDialog" expr="'IH'"/>
        <action next="IH1120_NameGreeting_PP"/>
        <else>
          <session-mapping key="GlobalVars.broadcastMessageCallingDialog" expr="'IH'"/>
          <action next="IH1130_GoToBroadcastMessages_SD"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="IH1117_CheckForStatePrivacyMessage_DS">
      <if cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.sbpStateCode  != null &amp;&amp; GlobalVars.GetAccountDetails.sbpStateCode  != ''">
        <session-mapping key="GlobalVars.broadcastMessageCallingDialog" expr="'IHPrivacy'"/>
        <action next="IH1130_GoToBroadcastMessages_SD"/>
        <else>
          <action next="IH1116_PlayCCPADisclaimer_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="IH1120_NameGreeting_PP">
      <session-mapping key="min" value="GlobalVars.trn" type="String"/>
      <session-mapping key="firstName" value="GlobalVars.firstName" type="String"/>
      <session-mapping key="lang" value="language" type="String"/>
      <session-mapping key="isGreetByNameEnabled" value="GlobalVars.GetBCSParameters.care_greetByName_enabled" type="String"/>
      <audio>
        <if cond="(isGreetByNameEnabled == true || isGreetByNameEnabled == 'true') &amp;&amp; (lang == 'en-US')">
          <prompt type="custom" expr="firstName">
            <param name="className" value="com.nuance.metro.audio.custom.PlayFirstNamePrompt"/>
          </prompt>
          <prompt id="silence_250ms">
            <prompt-segments>
              <audiofile text="test" src="silence_250ms.wav"/>
            </prompt-segments>
          </prompt>
        </if>
      </audio>
      <action next="IH1121_DynamicGreeting_PP"/>
    </play-state>

    <play-state id="IH1121_DynamicGreeting_PP">
      <session-mapping key="dnis" value="GlobalVars.enteredFrom" type="String"/>
      <session-mapping key="lastCustomerIntent" value="GlobalVars.lastCustomerIntent" type="String"/>
      <session-mapping key="delaypmt_audio" value="care_omnigreeting_delaypmt_audio" type="String"/>
      <session-mapping key="maxlogin_audio" value="care_omnigreeting_maxlogin_audio" type="String"/>
      <session-mapping key="greetingTranscript" value="language == 'en-US' ? GlobalVars.GetAccountDetails.greetingTranscript : GlobalVars.GetAccountDetails.greetingTranscript_ES" type="String"/>
      <session-mapping key="greetingPromptURL" value="language == 'en-US' ? GlobalVars.GetAccountDetails.greetingPromptURL : GlobalVars.GetAccountDetails.greetingPromptURL_ES" type="String"/>
      <session-mapping key="hasDefaultAccountValues" value="GlobalVars.GetAccountDetails.hasDefaultAccountValues" type="String"/>
      <session-mapping key="accountStatus" value="GlobalVars.GetAccountDetails.accountStatus" type="String"/>
      <session-mapping key="care_enable_acct_dets_reminder_SMS" value="GlobalVars.GetBCSParameters.care_enable_acct_dets_reminder_SMS" type="String"/>
      <audio>
        <if cond="(dnis == 'OMNI' &amp;&amp; lastCustomerIntent == 'delaypayment')">
          <prompt type="custom" expr="delaypmt_audio">
            <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayAudioMessage"/>
          </prompt>
        </if>
        <if cond="(dnis == 'OMNI' &amp;&amp; lastCustomerIntent == 'loginmaxtrieserror')">
          <prompt type="custom" expr="maxlogin_audio">
            <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayAudioMessage"/>
          </prompt>
        </if>
        <if cond="(!(dnis == 'OMNI' &amp;&amp; (lastCustomerIntent != undefined &amp;&amp; lastCustomerIntent != '')))&amp;&amp;((greetingPromptURL != null &amp;&amp; greetingPromptURL != '') || (greetingTranscript != null &amp;&amp; greetingTranscript != ''))">
          <prompt type="custom">
            <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.PlayDynamicGreetingURL"/>
            <param name="greetingTranscript" value="greetingTranscript" scope="request"/>
            <param name="greetingPromptURL" value="greetingPromptURL" scope="request"/>
          </prompt>
          <prompt id="silence_250ms">
            <prompt-segments>
              <audiofile text="test" src="silence_250ms.wav"/>
            </prompt-segments>
          </prompt>
        </if>
      </audio>
      <if cond="dnis == 'OMNI' &amp;&amp; lastCustomerIntent != undefined &amp;&amp; lastCustomerIntent != '' &amp;&amp; lastCustomerIntent != 'delaypayment' &amp;&amp; lastCustomerIntent != 'loginmaxtrieserror'  ">
        <session-mapping key="GlobalVars.lastCustomerIntent" expr="'misc'"/>
      </if>
      <if cond="(hasDefaultAccountValues == true || hasDefaultAccountValues == 'true') &amp;&amp; accountStatus == 'active' &amp;&amp; (care_enable_acct_dets_reminder_SMS == true || care_enable_acct_dets_reminder_SMS == 'true')  ">
        <action next="IH1122_MissingSecurityInfo_SD"/>
        <else>
          <action next="IH1130_GoToBroadcastMessages_SD"/>
        </else>
      </if>
    </play-state>

    <subdialog-state id="IH1122_MissingSecurityInfo_SD">
      <gotodialog next="MissingSecurityInfo_Dialog"/>
      <action next="IH1122_MissingSecurityInfo_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1122_MissingSecurityInfo_SD_return_CS">
      <action next="IH1130_GoToBroadcastMessages_SD"/>
    </custom-state>

    <subdialog-state id="IH1130_GoToBroadcastMessages_SD">
      <if cond="GlobalVars.isBCSSuccess">
        <if cond="GlobalVars.broadcastMessageCallingDialog == 'IH'">
          <session-mapping key="GlobalVars.broadcastMessageKey" expr="'IH'"/>
          <else>
            <session-mapping key="GlobalVars.broadcastMessageKey" expr="'IHPrivacy'"/>
          </else>
        </if>
        <gotodialog next="BroadcastMessages_Dialog"/>
        <else>
          <action next="IH1605_GoToCallTransfer_SD"/>
        </else>
      </if>
      <action next="IH1130_GoToBroadcastMessages_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1130_GoToBroadcastMessages_SD_return_CS">
      <if cond="GlobalVars.careInitialBroadcastMessagePlayed == true">
        <action next="IH1135_CheckBackendOutage_JDA"/>
        <else>
          <action next="IH1116_PlayCCPADisclaimer_JDA"/>
        </else>
      </if>
    </custom-state>

    <decision-state id="IH1135_CheckBackendOutage_DS">
      <if cond="GlobalVars.outageByColo == true">
        <gotodialog next="InitialHandling_PreMenuLogic#IH1615_MainMenu_SD"/>
        <else>
          <action next="IH2205_CheckIntentFromMobile_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="IH2101_CheckSMSOfferEnabled_DS">
      <session-mapping key="campaignId" value="GlobalVars.GetBCSParameters.care_upfront_sms_offer_campaign_id" type="String"/>
      <if cond="(GlobalVars.GetBCSParameters.care_upfront_sms_offer_broadcast_message == 'Custom')&amp;&amp; campaignId != null &amp;&amp; campaignId != '' ">
        <action next="IH2105_CheckANIMatch_JDA"/>
        <else>
          <action next="IH2001_CheckInfoOfferEnbled_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="IH2105_CheckANIMatch_DS">
      <if cond="(GlobalVars.aniMatch == true || GlobalVars.aniMatch == 'true')">
        <action next="IH2110_AskWantSMSYN_DM"/>
        <else>
          <action next="IH2001_CheckInfoOfferEnbled_JDA"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="IH2110_AskWantSMSYN_DM" type="CUST">
      <session-mapping key="broadcastMessageKey" value="CARE_SMS" type="String"/>
      <success>
        <action label="yes">
          <action next="IH2115_SendConfigurabeSMS_DB_DA"/>
        </action>
      </success>
      <catch>
        <gotodialog next="InitialHandling_PreMenuLogic#IH1205_CheckAccountStatus_DS"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt type="custom" expr="broadcastMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayBroadcastMessage"/>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="IH2110_AskWantSMSYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="3500ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="IH2115_SendConfigurabeSMS_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="campaignId" value="GlobalVars.GetBCSParameters.care_upfront_sms_offer_campaign_id" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <data-access id="SendTextMessage" classname="com.nuance.metro.dataaccess.SendTextMessage">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="mdn"/>
          <input-variable name="campaignId"/>
          <input-variable name="languageCode"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.SendTextMessage" expr="SendTextMessage"/>
        <action next="IH2120_CheckSMSResult_JDA"/>
      </action>
    </data-access-state>

    <decision-state id="IH2120_CheckSMSResult_DS">
      <if cond="(GlobalVars.SendTextMessage &amp;&amp; GlobalVars.SendTextMessage.status &amp;&amp; GlobalVars.SendTextMessage.status.toUpperCase() == 'SUCCESS')">
        <action next="IH2125_PlaySMSSuccess_PP"/>
        <else>
          <action next="IH2145_PlaySMSFailure_CS"/>
        </else>
      </if>
    </decision-state>

    <play-state id="IH2125_PlaySMSSuccess_PP">
      <audio>
        <prompt id="IH2125_out_01">
          <prompt-segments>
            <audiofile text="Okay, I texted the phone you re calling on It could take a minute or two to arrive " src="IH2125_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="IH2130_CheckPostSMSAction_JDA"/>
    </play-state>

    <decision-state id="IH2130_CheckPostSMSAction_DS">
      <session-mapping key="broadcastMessageAction" value="GlobalVars.GetBCSParameters.care_upfront_sms_offer_broadcastMessageAction" type="String"/>
      <if cond="broadcastMessageAction != null &amp;&amp; broadcastMessageAction != ''">
        <session-mapping key="broadcastMessageAction" expr="broadcastMessageAction.toLowerCase()"/>
      </if>
      <if cond="broadcastMessageAction == 'disconnect'">
        <action next="IH2135_SMSEndCall_SD"/>
        <elseif cond="broadcastMessageAction == 'transfer'">
          <action next="IH2140_SMSTransfer_SD"/>
        </elseif>
        <else>
          <gotodialog next="InitialHandling_PreMenuLogic#IH1205_CheckAccountStatus_DS"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="IH2135_SMSEndCall_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </subdialog-state>
    <subdialog-state id="IH2140_SMSTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
    </subdialog-state>
    <custom-state id="IH2145_PlaySMSFailure_CS">
      <audio>
        <prompt id="IH2145_out_01">
          <prompt-segments>
            <audiofile text="Sorry, something's gone wrong and I couldnt send the message" src="IH2145_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="IH2010_PlayConfigurableInfo_PP"/>
    </custom-state>

    <decision-state id="IH2001_CheckInfoOfferEnbled_DS">
      <if cond="(GlobalVars.GetBCSParameters.care_upfrontInfo_broadcastMessage == 'Custom')">
        <action next="IH2005_AskCallingAboutConfigYN_DM"/>
        <else>
          <gotodialog next="InitialHandling_PreMenuLogic#IH1205_CheckAccountStatus_DS"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="IH2005_AskCallingAboutConfigYN_DM" type="CUST">
      <session-mapping key="audioMessageKey" value="care_upfront_info_offer" type="String"/>
      <success>
        <action label="yes">
          <action next="IH2010_PlayConfigurableInfo_PP"/>
        </action>
      </success>
      <catch>
        <gotodialog next="InitialHandling_PreMenuLogic#IH1205_CheckAccountStatus_DS"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt type="custom" expr="audioMessageKey" bargein="false">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage"/>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="IH2005_AskCallingAboutConfigYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="3500ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="IH2010_PlayConfigurableInfo_PP">
      <session-mapping key="broadcastMessageKey" value="CARE_UP" type="String"/>
      <session-mapping key="broadcastMessage" value="GlobalVars.GetBCSParameters.care_upfrontInfo_broadcastMessage" type="String"/>
      <audio>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'" bargein="false">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <gotodialog next="InitialHandling_PreMenuLogic#IH1205_CheckAccountStatus_DS"/>
    </play-state>

    <subdialog-state id="IH1605_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="IH1605_GoToCallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1605_GoToCallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="IH1090_CallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="IH1090_CallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IH1090_CallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <decision-state id="IH2205_CheckIntentFromMobile_DS">
      <if cond="(GlobalVars.enteredFrom == 'OMNI' &amp;&amp; GlobalVars.lastCustomerIntent != undefined &amp;&amp; GlobalVars.lastCustomerIntent != '' )">
        <action next="IH2210_CheckRoutingFromMobile_JDA"/>
        <else>
          <action next="IH2101_CheckSMSOfferEnabled_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="IH2210_CheckRoutingFromMobile_DS">
      <if cond="(GlobalVars.lastCustomerIntent.toLowerCase() == 'delaypayment')">
        <action next="IH1090_CallTransfer_SD"/>
        <elseif cond="(GlobalVars.lastCustomerIntent.toLowerCase() == 'loginmaxtrieserror' )">
          <action next="IH1090_CallTransfer_SD"/>
        </elseif>
        <else>
          <gotodialog next="InitialHandling_PreMenuLogic#IH1205_CheckAccountStatus_DS"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="IH1118_CheckTFN_DS">
      <if cond="GlobalVars.enteredFrom == 'VR'">
        <if cond="GlobalVars.aniMatch == true">
          <action next="IH1116_PlayCCPADisclaimer_JDA"/>
          <else>
            <session-mapping key="GlobalVars.callType" expr="'transfer'"/>
            <if cond="language == 'en-US'">
              <session-mapping key="TransferTag" expr="'New_Customer_Sales_en'"/>
              <action next="IH1090_CallTransfer_SD"/>
              <else>
                <session-mapping key="TransferTag" expr="'New_Customer_Sales_es'"/>
                <action next="IH1090_CallTransfer_SD"/>
              </else>
            </if>
          </else>
        </if>
        <else>
          <action next="IH1116_PlayCCPADisclaimer_JDA"/>
        </else>
      </if>
    </decision-state>

  </dialog>
  