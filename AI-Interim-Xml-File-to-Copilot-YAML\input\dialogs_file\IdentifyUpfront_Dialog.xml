<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="IdentifyUpfront_Dialog">
    <decision-state id="IU1001_InitializeIdentifyUpfront_DS">
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <if cond="aniMatch==false">
        <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="0"/>
        <action next="IU1048_AskExistingCustomer_DM"/>
        <else>
          <session-mapping key="GlobalVars.identifyUpfrontMDNAttempts" expr="0"/>
          <session-mapping key="GlobalVars.pinFromIDUpfront " value="false" type="Boolean"/>
          <action next="IU1050_NewCustomerMenu_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="IU1005_GetMDN_DM" type="CUST">
      <session-mapping key="providedFindMDNhint" value="GlobalVars.providedFindMDNhint" type="String"/>
      <session-mapping key="requestOperatorUnidentified" value="GlobalVars.requestOperatorUnidentified" type="String"/>
      <session-mapping key="IU1005saidOperator" value="GlobalVars.IU1005saidOperator" type="String"/>
      <success>
        <action label="dont-know_mdn">
          <if cond="providedFindMDNhint==true">
            <action next="IU1065_GoToTransfer_SD"/>
            <else>
              <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="0"/>
              <action next="IU1006_AskIfPhoneAvailable_DM"/>
            </else>
          </if>
        </action>
        <action label="default">
          <session-mapping key="mdnEntered" expr="IU1005_GetMDN_DM.returnvalue"/>
          <action next="IU1010_GetAccountDetails_DB_DA"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.IU1005saidOperator" value="true" type="Boolean"/>
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <if cond="requestOperatorUnidentified == 0">
            <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
            <audio>
              <prompt id="IU1005_Operator_01">
                <prompt-segments>
                  <audiofile text="In order for our agents to help you we need the phone number you're calling about Please say or enter the 10 digit mobile number" src="IU1005_Operator_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="IU1005_GetMDN_DM"/>
            <elseif cond="requestOperatorUnidentified == 1">
              <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
              <audio>
                <prompt id="IU1005_Operator_02">
                  <prompt-segments>
                    <audiofile text="Please enter the number you're calling about or say I don't know" src="IU1005_Operator_02.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="IU1005_GetMDN_DM"/>
            </elseif>
            <else>
              <session-mapping key="GlobalVars.IU1005saidOperator" value="false" type="Boolean"/>
              <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1005_GetMDN_DM',IU1005_GetMDN_DM.confidencescore)" namelist="language library version "/>
            </else>
          </if>
          <session-mapping key="GlobalVars.IU1005saidOperator" value="false" type="Boolean"/>
          <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1005_GetMDN_DM',IU1005_GetMDN_DM.confidencescore)" namelist="language library version "/>
        </action>
        <action label="spanish">
          <session-mapping key="language" expr="'es-US'"/>
          <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
          <gotodialog next="IdentifyUpfront#IU1005_GetMDN_DM"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="IU1005_ini_01" cond="(identifyUpfrontMDNAttempts == 0 || providedFindMDNhint == true) &amp;&amp; IU1005saidOperator != true">
                <prompt-segments>
                  <audiofile src="IU1005_ini_01.wav" text="What's your 10-digit Metro phone number?"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1005_ini_02" cond="identifyUpfrontMDNAttempts == 1 &amp;&amp; (IU1005saidOperator != true)">
                <prompt-segments>
                  <audiofile src="IU1005_ini_02.wav" text="Try your number again using the keypad "/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1005_ini_03" cond="identifyUpfrontMDNAttempts == 2 &amp;&amp; (IU1005saidOperator != true)">
                <prompt-segments>
                  <audiofile text="Enter the number once more" src="IU1005_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="IU1005_ini_01" cond="(identifyUpfrontMDNAttempts == 0 || providedFindMDNhint == true) &amp;&amp; IU1005saidOperator != true">
                <prompt-segments>
                  <audiofile src="IU1005_ini_01.wav" text="What's your 10-digit Metro phone number?"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1005_ini_02" cond="identifyUpfrontMDNAttempts == 1 &amp;&amp; (IU1005saidOperator != true)">
                <prompt-segments>
                  <audiofile src="IU1005_ini_02.wav" text="Try your number again using the keypad "/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1005_ini_03" cond="identifyUpfrontMDNAttempts == 2 &amp;&amp; (IU1005saidOperator != true)">
                <prompt-segments>
                  <audiofile text="Enter the number once more" src="IU1005_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="IU1005_nm1_01">
                <prompt-segments>
                  <audiofile src="IU1005_nm1_01.wav" text="Please enter your Metro phone number, including the area code"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Enter the number again, or if you don't know press 1" src="IU1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Enter the number again, or if you don't know press 1" src="IU1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1005_nm1_01">
                <prompt-segments>
                  <audiofile src="IU1005_nm1_01.wav" text="Please enter your Metro phone number, including the area code"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Enter the number again, or if you don't know press 1" src="IU1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Enter the number again, or if you don't know press 1" src="IU1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="IU1005_ini_01" cond="(identifyUpfrontMDNAttempts == 0 || providedFindMDNhint == true) &amp;&amp; IU1005saidOperator != true">
                <prompt-segments>
                  <audiofile src="IU1005_ini_01.wav" text="What's your 10-digit Metro phone number?"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1005_ini_02" cond="identifyUpfrontMDNAttempts == 1 &amp;&amp; (IU1005saidOperator != true)">
                <prompt-segments>
                  <audiofile src="IU1005_ini_02.wav" text="Try your number again using the keypad "/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1005_ini_03" cond="identifyUpfrontMDNAttempts == 2 &amp;&amp; (IU1005saidOperator != true)">
                <prompt-segments>
                  <audiofile text="Enter the number once more" src="IU1005_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="IU1005_GetMDN_DM.grxml" count="1"/>
          <dtmfgrammars filename="IU1005_GetMDN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration>
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="IF_NECESSARY" highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                  <param name="intonation" value="f"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                  <param name="intonation" value="f"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                  <param name="className" value="com.nuance.metro.audio.custom.PlayNewPhoneNumber"/>
                  <param name="intonation" value="f"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="IU1010_GetAccountDetails_DB_DA">
      <session-mapping key="mdn" value="mdnEntered" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <session-mapping key="haveMDN" value="false" type="Boolean"/>
      <session-mapping key="MDN" value="" type="String"/>
      <session-mapping key="doesStatusMsgHasNoAccountFound" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.aniMatch" value="false" type="Boolean"/>
      <data-access id="GetSubscriberDetails" classname="com.nuance.metro.dataaccess.GetSubscriberDetails">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="languageCode"/>
          <input-variable name="sessionId"/>
          <input-variable name="imei"/>
          <input-variable name="clearCache"/>
          <input-variable name="multiLineDetails"/>
        </inputs>
        <outputs>
          <output-variable name="mdn"/>
          <output-variable name="accountNumber" mask="true"/>
          <output-variable name="active"/>
          <output-variable name="serialNumber"/>
          <output-variable name="features"/>
          <output-variable name="thirdPartyFeatures"/>
          <output-variable name="payDate"/>
          <output-variable name="taxInclusive"/>
          <output-variable name="accountStatus"/>
          <output-variable name="dueImmediatelyAmount"/>
          <output-variable name="highSecurity"/>
          <output-variable name="ratePlan"/>
          <output-variable name="marketID"/>
          <output-variable name="language"/>
          <output-variable name="isAutopayEnabled"/>
          <output-variable name="hasPhoneInsurance"/>
          <output-variable name="balance"/>
          <output-variable name="isDeviceAllowed"/>
          <output-variable name="isSuspended"/>
          <output-variable name="accountType"/>
          <output-variable name="validAccount"/>
          <output-variable name="roamingBucket"/>
          <output-variable name="paymentStatus"/>
          <output-variable name="ratePlanAttributesMap"/>
          <output-variable name="networkType"/>
          <output-variable name="securityQuestionCode"/>
          <output-variable name="hasEWallet"/>
          <output-variable name="eligibleForBillCycleReset"/>
          <output-variable name="billCycleLength"/>
          <output-variable name="firstName"/>
          <output-variable name="lastName"/>
          <output-variable name="eligibleForUnhotlineCheck"/>
          <output-variable name="securityQuestionString"/>
          <output-variable name="iccid"/>
          <output-variable name="imei"/>
          <output-variable name="hasAutopayPending"/>
          <output-variable name="insuranceCode"/>
          <output-variable name="subscriberId"/>
          <output-variable name="usedESNAllowed"/>
          <output-variable name="emailVerificationStatus"/>
          <output-variable name="emailAddress"/>
          <output-variable name="currentPromo"/>
          <output-variable name="futurePromo"/>
          <output-variable name="featureCodes"/>
          <output-variable name="superRegion"/>
          <output-variable name="accountFutureRequestInd"/>
          <output-variable name="subscriberFuturePricePlanInd"/>
          <output-variable name="ratePlanPrice"/>
          <output-variable name="currentPlanPromptURL"/>
          <output-variable name="currentPlanPromptTTS"/>
          <output-variable name="familyPlanIndicator"/>
          <output-variable name="topupEligibility"/>
          <output-variable name="customerTreatmentLevel"/>
          <output-variable name="HasHighSpeedData"/>
          <output-variable name="includedFeatures"/>
          <output-variable name="hasDataMaximizer"/>
          <output-variable name="eligibleForUpgrade"/>
          <output-variable name="upgradeEligibilityDate"/>
          <output-variable name="arBalance"/>
          <output-variable name="multilineList"/>
          <output-variable name="planMedialPromptURL"/>
          <output-variable name="planMedialPromptTTS"/>
          <output-variable name="hasDefaultAccountValues"/>
          <output-variable name="zipCode" mask="true"/>
          <output-variable name="accountRestrictIndicator"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetSubscriberDetails.status &amp;&amp; GetSubscriberDetails.status.toUpperCase() == 'SUCCESS' &amp;&amp; GetSubscriberDetails.validAccount">
          <session-mapping key="GlobalVars.GetAccountDetails" expr="GetSubscriberDetails"/>
          <session-mapping key="GlobalVars.unCoopMaxRequest" expr="GetSubscriberDetails.unCoopMaxRequest"/>
          <session-mapping key="GlobalVars.coopMaxRequest" expr="GetSubscriberDetails.coopMaxRequest"/>
          <session-mapping key="GlobalVars.marketID" expr="GetSubscriberDetails.marketID"/>
          <session-mapping key="GlobalVars.zipCode" expr="GetSubscriberDetails.zipCode"/>
          <session-mapping key="GlobalVars.mdn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.networkType" expr="GetSubscriberDetails.networkType"/>
          <session-mapping key="GlobalVars.trn" expr="GetSubscriberDetails.mdn"/>
          <session-mapping key="GlobalVars.accountNumber" expr="GetSubscriberDetails.accountNumber"/>
          <session-mapping key="GlobalVars.isOnFamilyPlan" expr="GetSubscriberDetails.isOnFamilyPlan"/>
          <session-mapping key="GlobalVars.firstName" expr="GetSubscriberDetails.firstName"/>
          <session-mapping key="GlobalVars.lastName" expr="GetSubscriberDetails.lastName"/>
          <session-mapping key="GlobalVars.eligibleForUpgrade" expr="GetSubscriberDetails.isCurrentlyEligibleForDeviceUpgrade"/>
          <session-mapping key="GlobalVars.upgradeEligibilityDate" expr="GetSubscriberDetails.upgradeEligibilityDate"/>
          <session-mapping key="GlobalVars.accountRestrictIndicator" expr="GetSubscriberDetails.accountRestrictIndicator"/>
          <session-mapping key="haveMDN" expr="GlobalVars.GetAccountDetails != null  &amp;&amp;(GlobalVars.GetAccountDetails.validAccount == 'true' || GlobalVars.GetAccountDetails.validAccount == true) &amp;&amp; GlobalVars.GetAccountDetails.mdn != undefined"/>
          <if cond="haveMDN == true">
            <session-mapping key="MDN" expr="GetSubscriberDetails.mdn"/>
            <session-mapping key="GlobalVars.MDN" expr="MDN"/>
            <session-mapping key="GlobalVars.areacode" expr="MDN.substring(0,3)"/>
            <session-mapping key="GlobalVars.NXX" expr="MDN.substring(3,6)"/>
            <session-mapping key="GlobalVars.cti_MDN" expr="MDN"/>
          </if>
          <session-mapping key="GlobalVars.accountFutureRequestInd" expr="GetSubscriberDetails.accountFutureRequestInd"/>
          <session-mapping key="GlobalVars.subscriberFuturePricePlanInd" expr="GetSubscriberDetails.subscriberFuturePricePlanInd"/>
        </if>
        <if cond="GetSubscriberDetails.status &amp;&amp; GetSubscriberDetails.status.toUpperCase() == 'SUCCESS'">
          <if cond="GlobalVars.GetAccountDetails.parentDeviceType == 'INT'">
            <session-mapping key="GlobalVars.tag" expr="'home-internet_active'"/>
            <session-mapping key="GlobalVars.enteredHintLine" value="true" type="Boolean"/>
          </if>
          <action next="IU1015_CheckAccountFound_JDA"/>
          <elseif cond="GetSubscriberDetails.status &amp;&amp; GetSubscriberDetails.status.toUpperCase() != 'SUCCESS' &amp;&amp; doesStatusMsgHasNoAccountFound">
            <action next="IU1015_CheckAccountFound_JDA"/>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="IU1015_CheckAccountFound_DS">
      <if cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.validAccount">
        <session-mapping key="GlobalVars.collectedMDNUpfront" value="true" type="Boolean"/>
        <session-mapping key="GlobalVars.cti_MDN" expr="GlobalVars.cti_MDN"/>
        <if cond="(GlobalVars.GetAccountDetails.accountRestrictIndicator == true)">
          <action next="IU1045_FraudSuspension_PP"/>
          <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ9')">
            <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'not_authenticated_high_security_account')"/>
            <action next="IU1040_CallTransfer_SD"/>
          </elseif>
          <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ8')">
            <action next="IU1035_Goodbye_SD"/>
          </elseif>
          <elseif cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended'">
            <session-mapping key="GlobalVars.pinFromIDUpfront" value="true" type="Boolean"/>
            <action next="IU1016_GetPIN_SD"/>
          </elseif>
          <else>
            <action next="IU1020_PlayAccountFound_PP"/>
          </else>
        </if>
        <else>
          <session-mapping key="identifyUpfrontMDNAttempts" expr="identifyUpfrontMDNAttempts+1"/>
          <action next="IU1105_CheckNumMDNAttempts_JDA"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="IU1016_GetPIN_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="IU1016_GetPIN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IU1016_GetPIN_SD_return_CS">
      <action next="IU1020_PlayAccountFound_PP"/>
    </custom-state>

    <play-state id="IU1020_PlayAccountFound_PP">
      <audio>
        <if type="java">
          <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
            <param name="numToMatch" value="1"/>
            <param name="startNum" value="1"/>
            <param name="endNum" value="3"/>
          </condition>
          <prompt id="IU1020_out_01">
            <prompt-segments>
              <audiofile src="IU1020_out_01.wav" text="Found it"/>
            </prompt-segments>
          </prompt>
          <elseif>
            <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
              <param name="numToMatch" value="2"/>
              <param name="startNum" value="1"/>
              <param name="endNum" value="3"/>
            </condition>
            <prompt id="IU1020_out_02">
              <prompt-segments>
                <audiofile src="IU1020_out_02.wav" text="Got it"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="IU1020_out_03">
              <prompt-segments>
                <audiofile text="Thanks" src="IU1020_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <subdialog-state id="IU1035_Goodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="IU1035_Goodbye_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IU1035_Goodbye_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <subdialog-state id="IU1040_CallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="IU1040_CallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="IU1040_CallTransfer_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <decision-state id="IU1105_CheckNumMDNAttempts_DS">
      <if cond="identifyUpfrontMDNAttempts &lt; 3">
        <action next="IU1110_PlayNoAccountFound_PP"/>
        <else>
          <action next="IU1115_AskReopenAccountYN_DM"/>
        </else>
      </if>
    </decision-state>

    <play-state id="IU1110_PlayNoAccountFound_PP">
      <audio>
        <prompt id="IU1110_out_01" cond="identifyUpfrontMDNAttempts == 1">
          <prompt-segments>
            <audiofile text="I cant seem to find that" src="IU1110_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms" cond="identifyUpfrontMDNAttempts == 1">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="IU1110_out_02" cond="identifyUpfrontMDNAttempts != 1">
          <prompt-segments>
            <audiofile text="I still couldnt find that If its been more than 30 days since the last payment, then this account was closed To make sure, you can call 611 from the phone for that number " src="IU1110_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms" cond="identifyUpfrontMDNAttempts != 1">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="IU1005_GetMDN_DM"/>
    </play-state>

    <play-state id="IU1045_FraudSuspension_PP">
      <audio>
        <prompt id="IU1045_out_01">
          <prompt-segments>
            <audiofile text="Your account requires assistance" src="IU1045_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="IU1040_CallTransfer_SD"/>
    </play-state>

    <dm-state id="IU1115_AskReopenAccountYN_DM" type="CUST">
      <success>
        <action label="yes">
          <action next="IU1040_CallTransfer_SD"/>
        </action>
        <action label="no">
          <audio>
            <prompt id="IU1115_out_01">
              <prompt-segments>
                <audiofile src="IU1115_out_01.wav" text="Okay You can double check the phone number and call us back You can also manage your account with the myMetro app, or at metro by t dash mobile dot com"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="IU1035_Goodbye_SD"/>
        </action>
      </success>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="IU1115_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, Im still not finding it If youre sure youve got the right phone number, then this account was closed because of an overdue payment " src="IU1115_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1115_ini_02">
                <prompt-segments>
                  <audiofile src="IU1115_ini_02.wav" text="Would you like to reopen it?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="IU1115_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, Im still not finding it If youre sure youve got the right phone number, then this account was closed because of an overdue payment " src="IU1115_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1115_ini_02">
                <prompt-segments>
                  <audiofile src="IU1115_ini_02.wav" text="Would you like to reopen it?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="IU1115_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, Im still not finding it If youre sure youve got the right phone number, then this account was closed because of an overdue payment " src="IU1115_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1115_ini_02">
                <prompt-segments>
                  <audiofile src="IU1115_ini_02.wav" text="Would you like to reopen it?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1115_nm2_01">
                <prompt-segments>
                  <audiofile text="To reopen this account, press 1 If you'd like to check the phone number again and call us back, you can just hang up" src="IU1115_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1115_nm2_01">
                <prompt-segments>
                  <audiofile text="To reopen this account, press 1 If you'd like to check the phone number again and call us back, you can just hang up" src="IU1115_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1115_nm1_01">
                <prompt-segments>
                  <audiofile text="This account is probably closed Would you like to reopen it? " src="IU1115_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1115_nm2_01">
                <prompt-segments>
                  <audiofile text="To reopen this account, press 1 If you'd like to check the phone number again and call us back, you can just hang up" src="IU1115_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1115_nm2_01">
                <prompt-segments>
                  <audiofile text="To reopen this account, press 1 If you'd like to check the phone number again and call us back, you can just hang up" src="IU1115_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="IU1115_ini_01">
                <prompt-segments>
                  <audiofile text="Sorry, Im still not finding it If youre sure youve got the right phone number, then this account was closed because of an overdue payment " src="IU1115_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1115_ini_02">
                <prompt-segments>
                  <audiofile src="IU1115_ini_02.wav" text="Would you like to reopen it?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommandsNoOperator_dtmf.grxml">
          <grammars filename="IU1115_AskReopenAccountYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="IU1115_AskReopenAccountYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="IU1205_PlayFindMDNInfo_PP">
      <audio>
        <prompt id="IU1205_out_01">
          <prompt-segments>
            <audiofile src="IU1205_out_01.wav" text="If you still have the phone for that account, you can check the settings - its in the section called Phone or ABOUT Phone "/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="IU1205_out_02">
          <prompt-segments>
            <audiofile text="You can also dial 6 8 6 from that phone and that will display your number " src="IU1205_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="IU1205_out_03">
          <prompt-segments>
            <audiofile src="IU1205_out_03.wav" text="Or, just call a friend and ask them what number shows up"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="IU1205_out_04">
          <prompt-segments>
            <audiofile text="If you DONT have that phone anymore, you can ask someone who has the number saved in their contacts to tell you what it is" src="IU1205_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="IU1210_RepeatFindMDNInfoYN_DM"/>
    </play-state>

    <dm-state id="IU1210_RepeatFindMDNInfoYN_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="IU1210_out_01">
              <prompt-segments>
                <audiofile text="Sure" src="IU1210_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="IU1205_PlayFindMDNInfo_PP"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="IU1210_out_02">
              <prompt-segments>
                <audiofile text="Okay Call us back when you have your number! You can also manage your account with the myMetro app, or at metro by t dash mobile dot com" src="IU1210_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="IU1035_Goodbye_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="IU1210_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear about finding your number again?" src="IU1210_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="IU1210_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear about finding your number again?" src="IU1210_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="IU1210_nm1_01">
                <prompt-segments>
                  <audiofile src="IU1210_nm1_01.wav" text="Would you like to hear that again? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1210_nm2_01">
                <prompt-segments>
                  <audiofile src="IU1210_nm2_01.wav" text="To hear about finding your number again, press 1 If youre done, you can just hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1210_nm2_01">
                <prompt-segments>
                  <audiofile src="IU1210_nm2_01.wav" text="To hear about finding your number again, press 1 If youre done, you can just hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1210_nm1_01">
                <prompt-segments>
                  <audiofile src="IU1210_nm1_01.wav" text="Would you like to hear that again? "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1210_nm2_01">
                <prompt-segments>
                  <audiofile src="IU1210_nm2_01.wav" text="To hear about finding your number again, press 1 If youre done, you can just hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1210_nm2_01">
                <prompt-segments>
                  <audiofile src="IU1210_nm2_01.wav" text="To hear about finding your number again, press 1 If youre done, you can just hang up"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="IU1210_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear about finding your number again?" src="IU1210_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="IU1210_RepeatFindMDNInfoYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="IU1210_RepeatFindMDNInfoYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="IU1065_GoToTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="IU1065_GoToTransfer_SD"/>
    </subdialog-state>
    <dm-state id="IU1006_AskIfPhoneAvailable_DM" type="YSNO">
      <session-mapping key="requestOperatorUnidentified" value="GlobalVars.requestOperatorUnidentified" type="String"/>
      <session-mapping key="IU1006saidOperator" value="GlobalVars.IU1006saidOperator" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.providedFindMDNhint" value="true" type="Boolean"/>
          <audio>
            <prompt id="IU1006_out_01">
              <prompt-segments>
                <audiofile text="Great On or Metro activated phone, dial pound N U M pound to find your 10-digit phone number Again that is pound, N U M, Pound" src="IU1006_out_01.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_3000ms">
              <prompt-segments>
                <audiofile text="test" src="silence_3000ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="IU1005_GetMDN_DM"/>
        </action>
        <action label="false">
          <action next="IU1065_GoToTransfer_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.IU1006saidOperator" value="true" type="Boolean"/>
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <if cond="requestOperatorUnidentified == 0">
            <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
            <audio>
              <prompt id="IU1006_Operator_01">
                <prompt-segments>
                  <audiofile text="If you have your Metro activated phone available I can help you find your number So, do you have your phone handy?" src="IU1006_Operator_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="IU1006_AskIfPhoneAvailable_DM"/>
            <elseif cond="requestOperatorUnidentified == 1">
              <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
              <audio>
                <prompt id="IU1006_Operator_02">
                  <prompt-segments>
                    <audiofile text="I can better serve you if I know the phone number you're calling about If you have your Metro activated phone handy press 1, if not press 2" src="IU1006_Operator_02.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="IU1006_AskIfPhoneAvailable_DM"/>
            </elseif>
            <else>
              <session-mapping key="GlobalVars.IU1006saidOperator" value="false" type="Boolean"/>
              <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1006_AskIfPhoneAvailable_DM',IU1006_AskIfPhoneAvailable_DM.confidencescore)" namelist="language library version "/>
            </else>
          </if>
          <session-mapping key="GlobalVars.IU1006saidOperator" value="false" type="Boolean"/>
          <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1006_AskIfPhoneAvailable_DM',IU1006_AskIfPhoneAvailable_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="IU1006_ini_01" cond="IU1006saidOperator != true">
                <prompt-segments>
                  <audiofile text="Do you have access to your Metro activated phone?" src="IU1006_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="IU1006_ini_01" cond="IU1006saidOperator != true">
                <prompt-segments>
                  <audiofile text="Do you have access to your Metro activated phone?" src="IU1006_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="IU1006_match1_01">
                <prompt-segments>
                  <audiofile text="Do you have access to your Metro activated Phone?" src="IU1006_match1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1006_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have access to your Metro activated phone say 'yes' or press 1 If not say 'no' or press 2" src="IU1006_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1006_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have access to your Metro activated phone say 'yes' or press 1 If not say 'no' or press 2" src="IU1006_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1006_match1_01">
                <prompt-segments>
                  <audiofile text="Do you have access to your Metro activated Phone?" src="IU1006_match1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1006_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have access to your Metro activated phone say 'yes' or press 1 If not say 'no' or press 2" src="IU1006_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1006_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have access to your Metro activated phone say 'yes' or press 1 If not say 'no' or press 2" src="IU1006_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="IU1006_ini_01" cond="IU1006saidOperator != true">
                <prompt-segments>
                  <audiofile text="Do you have access to your Metro activated phone?" src="IU1006_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="IU1006_AskIfPhoneAvailable_DM.grxml" count="1"/>
          <dtmfgrammars filename="IU1006_AskIfPhoneAvailable_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration>
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="IU1048_AskExistingCustomer_DM" type="YSNO">
      <session-mapping key="requestOperatorUnidentified" value="GlobalVars.requestOperatorUnidentified" type="String"/>
      <session-mapping key="IU1048saidOperator" value="GlobalVars.IU1048saidOperator" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="0"/>
          <action next="IU1005_GetMDN_DM"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.skipBroadcastMessage" value="true" type="Boolean"/>
          <action next="IU1050_NewCustomerMenu_DM"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.IU1048saidOperator" value="true" type="Boolean"/>
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <if cond="requestOperatorUnidentified == 0">
            <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
            <audio>
              <prompt id="IU1048_Operator_01">
                <prompt-segments>
                  <audiofile text="To get you to the right place, I need to know if you are currently a Metro by Tmoble customer Say 'yes' or press 1 or say 'no' or press 2" src="IU1048_Operator_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="IU1048_AskExistingCustomer_DM"/>
            <elseif cond="requestOperatorUnidentified == 1">
              <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
              <audio>
                <prompt id="IU1048_Operator_02">
                  <prompt-segments>
                    <audiofile text="If you're currently a Metro by Tmobile customer press 1  If not Press 2" src="IU1048_Operator_02.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="IU1048_AskExistingCustomer_DM"/>
            </elseif>
            <else>
              <session-mapping key="GlobalVars.IU1048saidOperator" value="false" type="Boolean"/>
              <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1048_AskExistingCustomer_DM',IU1048_AskExistingCustomer_DM.confidencescore)" namelist="language library version "/>
            </else>
          </if>
          <session-mapping key="GlobalVars.IU1048saidOperator" value="false" type="Boolean"/>
          <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1048_AskExistingCustomer_DM',IU1048_AskExistingCustomer_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="IU1048_ini_01" cond="IU1048saidOperator != true">
                <prompt-segments>
                  <audiofile text="I don't recognize the phone number you're calling from Are you currently an existing Metro by Tmobile customer?" src="IU1048_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="IU1048_ini_01" cond="IU1048saidOperator != true">
                <prompt-segments>
                  <audiofile text="I don't recognize the phone number you're calling from Are you currently an existing Metro by Tmobile customer?" src="IU1048_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="IU1048_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you currently have an existing Metro by Tmobile account?" src="IU1048_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1048_nm2_01">
                <prompt-segments>
                  <audiofile text="If you're currently a Metro by Tmobile customer press 1 If you don't have an account press 2" src="IU1048_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1048_nm2_01">
                <prompt-segments>
                  <audiofile text="If you're currently a Metro by Tmobile customer press 1 If you don't have an account press 2" src="IU1048_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1048_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you currently have an existing Metro by Tmobile account?" src="IU1048_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1048_nm2_01">
                <prompt-segments>
                  <audiofile text="If you're currently a Metro by Tmobile customer press 1 If you don't have an account press 2" src="IU1048_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1048_nm2_01">
                <prompt-segments>
                  <audiofile text="If you're currently a Metro by Tmobile customer press 1 If you don't have an account press 2" src="IU1048_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="IU1048_ini_01" cond="IU1048saidOperator != true">
                <prompt-segments>
                  <audiofile text="I don't recognize the phone number you're calling from Are you currently an existing Metro by Tmobile customer?" src="IU1048_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="IU1048_AskExistingCustomer_DM.grxml" count="1"/>
          <dtmfgrammars filename="IU1048_AskExistingCustomer_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration>
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="IU1051_AskMetroDevice_DM" type="YSNO">
      <session-mapping key="requestOperatorUnidentified" value="GlobalVars.requestOperatorUnidentified" type="String"/>
      <session-mapping key="IU1051saidOperator" value="GlobalVars.IU1051saidOperator" type="String"/>
      <success>
        <action label="true">
          <action next="IU1055_ActivationInformation_DM"/>
        </action>
        <action label="false">
          <action next="IU1065_GoToTransfer_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.IU1051saidOperator" value="true" type="Boolean"/>
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <if cond="requestOperatorUnidentified == 0">
            <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
            <audio>
              <prompt id="IU1051_Operator_01">
                <prompt-segments>
                  <audiofile text="Was the device you'd like to activate purchased directly from Metro by Tmobile? say 'yes' or press 1 Or say 'no' or press 2" src="IU1051_Operator_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="IU1051_AskMetroDevice_DM"/>
            <elseif cond="requestOperatorUnidentified == 1">
              <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
              <audio>
                <prompt id="IU1051_Operator_02">
                  <prompt-segments>
                    <audiofile text="If the device you are activating was purchased directly from Metro by Tmobile press 1  If not press 2" src="IU1051_Operator_02.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="IU1051_AskMetroDevice_DM"/>
            </elseif>
            <else>
              <session-mapping key="GlobalVars.IU1051saidOperator" value="false" type="Boolean"/>
              <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1051_AskMetroDevice_DM',IU1051_AskMetroDevice_DM.confidencescore)" namelist="language library version "/>
            </else>
          </if>
          <session-mapping key="GlobalVars.IU1051saidOperator" value="false" type="Boolean"/>
          <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1051_AskMetroDevice_DM',IU1051_AskMetroDevice_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="IU1051_ini_01" cond="IU1051saidOperator != true">
                <prompt-segments>
                  <audiofile text="Is your device a Metro by Tmobile device?" src="IU1051_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="IU1051_ini_01" cond="IU1051saidOperator != true">
                <prompt-segments>
                  <audiofile text="Is your device a Metro by Tmobile device?" src="IU1051_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="IU1051_nm1_01">
                <prompt-segments>
                  <audiofile text="If the device you're trying to activate puchased directly from Metro by Tmobile say 'yes' or press '  If not say 'no' or press 2" src="IU1051_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1051_nm2_01">
                <prompt-segments>
                  <audiofile text="If you purchased your device directly from Metro by Tmobile press 1 If not press 2" src="IU1051_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1051_nm2_01">
                <prompt-segments>
                  <audiofile text="If you purchased your device directly from Metro by Tmobile press 1 If not press 2" src="IU1051_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1051_nm1_01">
                <prompt-segments>
                  <audiofile text="If the device you're trying to activate puchased directly from Metro by Tmobile say 'yes' or press '  If not say 'no' or press 2" src="IU1051_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1051_nm2_01">
                <prompt-segments>
                  <audiofile text="If you purchased your device directly from Metro by Tmobile press 1 If not press 2" src="IU1051_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1051_nm2_01">
                <prompt-segments>
                  <audiofile text="If you purchased your device directly from Metro by Tmobile press 1 If not press 2" src="IU1051_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="IU1051_AskMetroDevice_DM.grxml" count="1"/>
          <dtmfgrammars filename="IU1051_AskMetroDevice_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration>
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="IU1050_NewCustomerMenu_DM" type="CUST">
      <session-mapping key="requestOperatorUnidentified" value="GlobalVars.requestOperatorUnidentified" type="String"/>
      <session-mapping key="IU1050saidOperator" value="GlobalVars.IU1050saidOperator" type="String"/>
      <success>
        <action label="activate-phone_nc">
          <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="0"/>
          <action next="IU1051_AskMetroDevice_DM"/>
        </action>
        <action label="purchase-phone_nc">
          <session-mapping key="GlobalVars.callType" expr="'transfer'"/>
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'New_Customer_Sales_en'"/>
            <else>
              <session-mapping key="TransferTag" expr="'New_Customer_Sales_Es'"/>
              <session-mapping key="GlobalVars.tag" expr="'purchase-phone_nc'"/>
              <action next="IU1040_CallTransfer_SD"/>
            </else>
          </if>
          <action next="IU1040_CallTransfer_SD"/>
        </action>
        <action label="something-else_nc">
          <action next="IU1040_CallTransfer_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.IU1050saidOperator" value="true" type="Boolean"/>
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <if cond="requestOperatorUnidentified == 0">
            <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
            <audio>
              <prompt id="IU1050_Operator_01">
                <prompt-segments>
                  <audiofile text="To get you to the right place please choose from the following If you already have a device that you need to activate, say activate my device Or, if you're interested in becoming a new Metro by T-Mobile customer and need to order a new device or equipment, say, New Customer You can also say 'I'm calling for something else" src="IU1050_Operator_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="IU1050_NewCustomerMenu_DM"/>
            <elseif cond="requestOperatorUnidentified == 1">
              <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
              <audio>
                <prompt id="IU1050_Operator_02">
                  <prompt-segments>
                    <audiofile text="If you have a device you are trying to activate press 1 If you'd like to be come a new customer and need to purchase a device press 2 If you're calling about something else press 3" src="IU1050_Operator_02.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="IU1050_NewCustomerMenu_DM"/>
            </elseif>
            <else>
              <session-mapping key="GlobalVars.IU1050saidOperator" value="false" type="Boolean"/>
              <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1050_NewCustomerMenu_DM',IU1050_NewCustomerMenu_DM.confidencescore)" namelist="language library version "/>
            </else>
          </if>
          <session-mapping key="GlobalVars.IU1050saidOperator" value="false" type="Boolean"/>
          <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1050_NewCustomerMenu_DM',IU1050_NewCustomerMenu_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="IU1050_ini_01" cond="IU1050saidOperator != true">
                <prompt-segments>
                  <audiofile text="If you already have a device that you need to activate, say 'activate my device'  Or, if you're interested in becoming a new Metro by T-Mobile customer and need to order a new device or equipment, say, 'New Customer'" src="IU1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="IU1050_ini_01" cond="IU1050saidOperator != true">
                <prompt-segments>
                  <audiofile text="If you already have a device that you need to activate, say 'activate my device'  Or, if you're interested in becoming a new Metro by T-Mobile customer and need to order a new device or equipment, say, 'New Customer'" src="IU1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="IU1050_nm1_01">
                <prompt-segments>
                  <audiofile text="If you already have a device that you need to activate, say 'activate my device'or press 1  If  you're interested in becoming a new Metro by T-Mobile customer and need to order a new device or equipment, say,'New Customer' or press 2You can also say, I'm calling about something else' or press 3" src="IU1050_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1050_nm2_01">
                <prompt-segments>
                  <audiofile text="If you're calling to activate a device press 1If you're interested in becoming a Metro byTmobile customer and are looking to purchase a device or equipment press 2  If you're calling for something else press 3" src="IU1050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1050_nm2_01">
                <prompt-segments>
                  <audiofile text="If you're calling to activate a device press 1If you're interested in becoming a Metro byTmobile customer and are looking to purchase a device or equipment press 2  If you're calling for something else press 3" src="IU1050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1050_nm1_01">
                <prompt-segments>
                  <audiofile text="If you already have a device that you need to activate, say 'activate my device'or press 1  If  you're interested in becoming a new Metro by T-Mobile customer and need to order a new device or equipment, say,'New Customer' or press 2You can also say, I'm calling about something else' or press 3" src="IU1050_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1050_nm2_01">
                <prompt-segments>
                  <audiofile text="If you're calling to activate a device press 1If you're interested in becoming a Metro byTmobile customer and are looking to purchase a device or equipment press 2  If you're calling for something else press 3" src="IU1050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1050_nm2_01">
                <prompt-segments>
                  <audiofile text="If you're calling to activate a device press 1If you're interested in becoming a Metro byTmobile customer and are looking to purchase a device or equipment press 2  If you're calling for something else press 3" src="IU1050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="IU1050_ini_01" cond="IU1050saidOperator != true">
                <prompt-segments>
                  <audiofile text="If you already have a device that you need to activate, say 'activate my device'  Or, if you're interested in becoming a new Metro by T-Mobile customer and need to order a new device or equipment, say, 'New Customer'" src="IU1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="IU1050_NewCustomerMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="IU1050_NewCustomerMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration>
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="IF_NECESSARY" highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="IU1050_NewCustomerMenu_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="IU1050_ini_01" cond="IU1050saidOperator != true">
                <prompt-segments>
                  <audiofile text="If you already have a device that you need to activate, say 'activate my device'  Or, if you're interested in becoming a new Metro by T-Mobile customer and need to order a new device or equipment, say, 'New Customer'" src="IU1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="IU1050_ini_01" cond="IU1050saidOperator != true">
                <prompt-segments>
                  <audiofile text="If you already have a device that you need to activate, say 'activate my device'  Or, if you're interested in becoming a new Metro by T-Mobile customer and need to order a new device or equipment, say, 'New Customer'" src="IU1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="IU1055_ActivationInformation_DM" type="YSNO">
      <session-mapping key="requestOperatorUnidentified" value="GlobalVars.requestOperatorUnidentified" type="String"/>
      <session-mapping key="IU1055saidOperator" value="GlobalVars.IU1055saidOperator" type="String"/>
      <success>
        <action label="operator">
          <session-mapping key="GlobalVars.IU1055saidOperator" value="true" type="Boolean"/>
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <if cond="requestOperatorUnidentified == 0">
            <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
            <audio>
              <prompt id="IU1055_Operator_01">
                <prompt-segments>
                  <audiofile text="To hear the information again say 'repeat that'  If you're finsihed you can simply hang up " src="IU1055_Operator_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="IU1055_ActivationInformation_DM"/>
            <elseif cond="requestOperatorUnidentified == 1">
              <session-mapping key="GlobalVars.requestOperatorUnidentified" expr="GlobalVars.requestOperatorUnidentified+1"/>
              <audio>
                <prompt id="IU1055_Operator_02">
                  <prompt-segments>
                    <audiofile text="To hear the information again press 7  Otherwise you an simply hang up" src="IU1055_Operator_02.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="IU1055_ActivationInformation_DM"/>
            </elseif>
            <else>
              <session-mapping key="GlobalVars.IU1055saidOperator" value="false" type="Boolean"/>
              <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1055_ActivationInformation_DM',IU1055_ActivationInformation_DM.confidencescore)" namelist="language library version "/>
            </else>
          </if>
          <session-mapping key="GlobalVars.IU1055saidOperator" value="false" type="Boolean"/>
          <submit expr="operatorSubmitFunction('IdentifyUpfront.dvxml','IU1055_ActivationInformation_DM',IU1055_ActivationInformation_DM.confidencescore)" namelist="language library version "/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="IU1055_ini_01" cond="IU1055saidOperator != true">
                <prompt-segments>
                  <audiofile text="Great You should have received an email at the address you provided when you placed your order This will contain your order ID A separate email will provide simple steps to activate your device If you have your order ID please go online at at metro by tmobile com / activate to complete your activation At the top of the page select phones and activations and find the option to activate at the bottom Do you still have questions about activation?" src="IU1055_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="IU1055_ini_01" cond="IU1055saidOperator != true">
                <prompt-segments>
                  <audiofile text="Great You should have received an email at the address you provided when you placed your order This will contain your order ID A separate email will provide simple steps to activate your device If you have your order ID please go online at at metro by tmobile com / activate to complete your activation At the top of the page select phones and activations and find the option to activate at the bottom Do you still have questions about activation?" src="IU1055_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="IU1055_ini_01">
                <prompt-segments>
                  <audiofile text="Great You should have received an email at the address you provided when you placed your order This will contain your order ID A separate email will provide simple steps to activate your device If you have your order ID please go online at at metro by tmobile com / activate to complete your activation At the top of the page select phones and activations and find the option to activate at the bottom Do you still have questions about activation?" src="IU1055_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1055_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or press 7  Otherwise you can simply hang up" src="IU1055_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1055_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or press 7  Otherwise you can simply hang up" src="IU1055_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1055_ini_01">
                <prompt-segments>
                  <audiofile text="Great You should have received an email at the address you provided when you placed your order This will contain your order ID A separate email will provide simple steps to activate your device If you have your order ID please go online at at metro by tmobile com / activate to complete your activation At the top of the page select phones and activations and find the option to activate at the bottom Do you still have questions about activation?" src="IU1055_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1055_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or press 7  Otherwise you can simply hang up" src="IU1055_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="IU1055_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or press 7  Otherwise you can simply hang up" src="IU1055_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
    </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration>
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  