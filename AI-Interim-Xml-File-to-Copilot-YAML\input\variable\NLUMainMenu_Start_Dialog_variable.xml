<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="callerIntent" value="empty" type="String"/>
  <session-mapping key="ssmScore" value="empty" type="String"/>
  <session-mapping key="hasConfirmed" value="empty" type="String"/>
  <session-mapping key="GlobalVars.tag" value="null" type="string"/>
  <session-mapping key="GlobalVars.metrics_hasVisitedMainMenu" value="true" type="boolean"/>
  <session-mapping key="care_enable_switch_lines" value="true" type="boolean"/>
  <session-mapping key="aniMatch" value="true" type="boolean"/>
  <session-mapping key="switchLinesSuccess" value="false" type="boolean"/>
  <session-mapping key="NL1005_NLUMainMenu_DM.inputmode" value="voice" type="string"/>
  <session-mapping key="lastCustomerIntent" value="misc" type="string"/>
  <session-mapping key="accountStatus" value="suspended" type="string"/>
  <session-mapping key="remainingDays" value="3" type="integer"/>
  <session-mapping key="GetTagData.returnCode" value="0" type="integer"/>
  <session-mapping key="GlobalVars.originalIntent" value="vague-service" type="string"/>
  <session-mapping key="GetTagData.tagName" value="request-representative_reprompt" type="string"/>
  <session-mapping key="GetTagData.destinationType" value="APPTAG" type="string"/>
  <session-mapping key="GetTagData.confirmTag" value="true" type="string"/>
  <session-mapping key="GetTagData.landmarkPrompt" value="undefined" type="string"/>
  <session-mapping key="playPrompt" value="undefined" type="string"/>
  <session-mapping key="playPromptTTS" value="undefined" type="string"/>
  <session-mapping key="NL1035_BackOffMenu_DM.inputmode" value="voice" type="string"/>
  <session-mapping key="maxAttempts" value="2" type="string"/>
  <session-mapping key="nluOperatorCount" value="1" type="integer"/>
  <session-mapping key="GetTagData.tagVariables" value="undefined" type="string"/>
  <session-mapping key="tagVariables.playVanityLandmark" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="suspended" type="string"/>
  <session-mapping key="GlobalVars.callType" value="make_pmt" type="string"/>
  <session-mapping key="GlobalVars.aniMatch" value="true" type="string"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="string"/>
  <session-mapping key="GetBCSParameters.payments_enable_prepaid_methods" value="true" type="boolean"/>
</session-mappings>
