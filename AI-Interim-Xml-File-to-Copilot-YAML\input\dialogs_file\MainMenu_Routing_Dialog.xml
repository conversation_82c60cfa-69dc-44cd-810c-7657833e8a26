<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="MainMenu_Routing_Dialog">
    <decision-state id="MM1510_RouteToDestination_DS">
      <session-mapping key="accountStatus" value="(GlobalVars.GetAccountDetails)?GlobalVars.GetAccountDetails.accountStatus :''" type="String"/>
      <if cond="false">
        <session-mapping key="GlobalVars.playTransferMessage " value="true" type="Boolean"/>
        <action next="MM1545_GoToCallTransfer_SD"/>
        <elseif cond="GlobalVars.callType == 'extension'">
          <action next="MM1595_ApplyExtension_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'switch_lines'">
          <action next="MM1598_GoToSwitchLines_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'auto_pay'">
          <session-mapping key="GlobalVars.callType" expr="'auto_pay'"/>
          <gotodialog next="MainMenu_Main#MM1610_GoToAutoPay_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'make_pmt'">
          <action next="MM1520_GoToMakePayment_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType== 'my_features'">
          <action next="MM1565_GoToCurrentFeatures_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'new_customer'">
          <action next="MM1570_GoToGettingStarted_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'acct_bal'">
          <if cond="GlobalVars.tag == 'home-internet_active'">
            <action next="MM1525_GoToRatePlan_SD"/>
            <else>
              <action next="MM1575_GoToBalanceBreakdown_SD"/>
            </else>
          </if>
        </elseif>
        <elseif cond="GlobalVars.callType == 'plan_details'">
          <if cond="GlobalVars.tag == 'home-internet_active'">
            <action next="MM1525_GoToRatePlan_SD"/>
            <else>
              <action next="MM1575_GoToBalanceBreakdown_SD"/>
            </else>
          </if>
        </elseif>
        <elseif cond="GlobalVars.callType == 'change_plan'">
          <action next="MM1525_GoToRatePlan_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'add_feature'">
          <action next="MM1530_GoToVoiceStore_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'reset_pin'">
          <action next="MM1560_GoToVoicemailPIN_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'troubleshoot'">
          <action next="MM1535_GoToTroubleshooting_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'find_store'">
          <action next="MM1540_GoToStoreLocator_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'help_me_out'">
          <action next="MM1585_GoToFAQ_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'troubleshoot' || GlobalVars.callType == 'payment_tip'">
          <action next="MM1535_GoToTroubleshooting_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'data_usage'">
          <action next="MM1590_GoToDataUsage_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'transfer' || GlobalVars.callType == 'tmomoney'">
          <action next="MM1545_GoToCallTransfer_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'lost_phone'">
          <action next="MM1550_GoToLostPhone_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'pmt_help'">
          <action next="MM1596_GoToPaymentHelp_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'bal_details'">
          <action next="MM1575_GoToBalanceBreakdown_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'add_data'">
          <action next="MM1597_GoToHighSpeedDataAvailable_SD"/>
        </elseif>
        <else>
          <action next="MM1555_GoToDeviceHandling_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="MM1515_GoToAccountBalance_SD">
      <gotodialog next="AccountBalance_Main"/>
      <action next="MM1515_GoToAccountBalance_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MM1515_GoToAccountBalance_SD_return_CS">
      <gotodialog next="MainMenu_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1520_GoToMakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="MM1520_GoToMakePayment_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="MM1520_GoToMakePayment_SD_Return_CS">
      <if cond="(GlobalVars.tag == 'request-extension')">
        <action next="MM1595_ApplyExtension_SD"/>
        <else>
          <gotodialog next="MainMenu_Main#MM1005_CheckCallerType_DS"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="MM1595_ApplyExtension_SD">
      <gotodialog next="ApplyExtension_Main_Dialog"/>
      <action next="MM1595_ApplyExtension_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MM1595_ApplyExtension_SD_return_CS">
      <if cond="(GlobalVars.tag == 'make-payment')">
        <action next="MM1520_GoToMakePayment_SD"/>
        <elseif cond="(GlobalVars.callType == 'extension')">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <gotodialog next="MainMenu_Main#MM1005_CheckCallerType_DS"/>
        </elseif>
        <else>
          <gotodialog next="MainMenu_Main#MM1005_CheckCallerType_DS"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="MM1525_GoToRatePlan_SD">
      <gotodialog next="RatePlan_Main_Dialog"/>
      <action next="MM1525_GoToRatePlan_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MM1525_GoToRatePlan_SD_return_CS">
      <session-mapping key="GlobalVars.callType" expr="undefined"/>
      <gotodialog next="MainMenu_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1530_GoToVoiceStore_SD">
      <gotodialog next="Voicestore_Main_Dialog"/>
      <action next="MM1530_GoToVoiceStore_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MM1530_GoToVoiceStore_SD_return_CS">
      <gotodialog next="MainMenu_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1535_GoToTroubleshooting_SD">
      <gotodialog next="TechnicalSupport_Dialog"/>
      <action next="MM1535_GoToTroubleshooting_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="MM1535_GoToTroubleshooting_SD_Return_CS">
      <gotodialog next="MainMenu_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1545_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="MM1545_GoToCallTransfer_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="MM1545_GoToCallTransfer_SD_Return_CS">
      <gotodialog next="Goodbye_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1540_GoToStoreLocator_SD">
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="MM1540_GoToStoreLocator_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="MM1540_GoToStoreLocator_SD_Return_CS">
      <action next="MM1580_GoToAnythingElse_SD"/>
    </custom-state>

    <subdialog-state id="MM1550_GoToLostPhone_SD">
      <gotodialog next="LostPhone_Main_Dialog"/>
      <action next="MM1550_GoToLostPhone_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="MM1550_GoToLostPhone_SD_Return_CS">
      <gotodialog next="Goodbye_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1555_GoToDeviceHandling_SD">
      <gotodialog next="DeviceHandling_Main_Dialog"/>
      <action next="MM1555_GoToDeviceHandling_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="MM1555_GoToDeviceHandling_SD_Return_CS">
      <gotodialog next="MainMenu_Main#MM1005_CheckCallerType_DS"/>
    </custom-state>

    <subdialog-state id="MM1560_GoToVoicemailPIN_SD">
      <gotodialog next="VoiceMailPin_Main_Dialog"/>
      <action next="MM1560_GoToVoicemailPIN_SD_Return_CS"/>
    </subdialog-state>
    <custom-state id="MM1560_GoToVoicemailPIN_SD_Return_CS">
      <gotodialog next="Goodbye_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1565_GoToCurrentFeatures_SD">
      <gotodialog next="CurrentFeatures_Main_Dialog"/>
      <action next="MM1565_GoToCurrentFeatures_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MM1565_GoToCurrentFeatures_SD_return_CS">
      <gotodialog next="MainMenu_Main#MM1005_CheckCallerType_DS"/>
    </custom-state>

    <subdialog-state id="MM1570_GoToGettingStarted_SD">
      <gotodialog next="GettingStarted_Main_Dialog"/>
      <action next="MM1570_GoToGettingStarted_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="MM1570_GoToGettingStarted_SDReturn_CS">
      <gotodialog next="Goodbye_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1580_GoToAnythingElse_SD">
      <gotodialog next="AnythingElse_Main_Dialog"/>
      <action next="MM1580_GoToAnythingElse_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="MM1580_GoToAnythingElse_SDReturn_CS">
      <gotodialog next="Goodbye_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1585_GoToFAQ_SD">
      <gotodialog next="FAQ_Main_Dialog"/>
      <action next="MM1585_GoToFAQ_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="MM1585_GoToFAQ_SDReturn_CS">
      <gotodialog next="MainMenu_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1590_GoToDataUsage_SD">
      <gotodialog next="DataUsage_Main_Dialog"/>
      <action next="MM1590_GoToDataUsage_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="MM1590_GoToDataUsage_SDReturn_CS">
      <gotodialog next="MainMenu_Main#MM1005_CheckCallerType_DS"/>
    </custom-state>

    <subdialog-state id="MM1575_GoToBalanceBreakdown_SD">
      <gotodialog next="BalanceBreakdown_Dialog"/>
      <action next="MM1575_GoToBalanceBreakdown_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="MM1575_GoToBalanceBreakdown_SDReturn_CS">
      <session-mapping key="GlobalVars.callType" expr="undefined"/>
      <gotodialog next="MainMenu_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1596_GoToPaymentHelp_SD">
      <gotodialog next="Payment_Help_Dialog"/>
      <action next="MM1596_GoToPaymentHelp_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="MM1596_GoToPaymentHelp_SDReturn_CS">
      <session-mapping key="GlobalVars.callType" expr="undefined"/>
      <gotodialog next="MainMenu_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1597_GoToHighSpeedDataAvailable_SD">
      <gotodialog next="HighSpeedDataAvailable_Main_Dialog"/>
      <action next="MM1597_GoToHighSpeedDataAvailable_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="MM1597_GoToHighSpeedDataAvailable_SDReturn_CS">
      <session-mapping key="GlobalVars.callType" expr="undefined"/>
      <gotodialog next="MainMenu_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="MM1599_GoToSuspended_SD">
      <gotodialog next="SuspendedHandling_Main_Dialog"/>
      <action next="MM1599_GoToSuspended_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MM1599_GoToSuspended_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="MM1598_GoToSwitchLines_SD">
      <gotodialog next="SwitchLines_Main_Dialog"/>
      <action next="MM1598_GoToSwitchLines_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MM1598_GoToSwitchLines_SD_return_CS">
      <gotodialog next="MainMenu_Main#MM1005_CheckCallerType_DS"/>
    </custom-state>

    <subdialog-state id="MM1615_AccountPinReset_SD">
      <gotodialog next="AccountPINReset_Main_Dialog"/>
      <action next="MM1615_AccountPinReset_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MM1615_AccountPinReset_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  