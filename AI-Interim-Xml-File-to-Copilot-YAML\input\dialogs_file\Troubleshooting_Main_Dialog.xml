<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Troubleshooting_Main_Dialog">
    <decision-state id="TT1000_CheckContext_DS">
      <if cond="(GlobalVars.tag == 'troubleshooting-internet' || GlobalVars.tag == 'troubleshooting-phone_features' || GlobalVars.tag == 'troubleshooting-phone_service' || GlobalVars.tag == 'troubleshooting-text_msg') &amp;&amp; (GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'active')">
        <session-mapping key="TransferTag" expr="'Technical_Support_English'"/>
        <action next="TT1005_UsingDeviceWithProblemYN_DM"/>
        <elseif cond="(GlobalVars.tag == 'troubleshooting-internet' || GlobalVars.tag == 'troubleshooting-phone_features' || GlobalVars.tag == 'troubleshooting-phone_service' || GlobalVars.tag == 'troubleshooting-text_msg') &amp;&amp; (GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus != 'active')">
          <session-mapping key="TransferTag" expr="'Technical_Support_English'"/>
          <action next="TT1015_OnNetworkTip_DM"/>
        </elseif>
        <else>
          <action next="TT1001_MainTroubleshootingOptions_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="TT1001_MainTroubleshootingOptions_DM" type="CUST">
      <success>
        <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
        <action label="network_connection">
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'Technical_Support_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'Technical_Support_Spanish'"/>
            </else>
          </if>
          <if cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'active'">
            <action next="TT1005_UsingDeviceWithProblemYN_DM"/>
            <else>
              <action next="TT1015_OnNetworkTip_DM"/>
            </else>
          </if>
        </action>
        <action label="calls">
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'Technical_Support_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'Technical_Support_Spanish'"/>
            </else>
          </if>
          <if cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus  == 'active'">
            <action next="TT1005_UsingDeviceWithProblemYN_DM"/>
            <else>
              <action next="TT1015_OnNetworkTip_DM"/>
            </else>
          </if>
        </action>
        <action label="feature">
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'Technical_Support_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'Technical_Support_Spanish'"/>
            </else>
          </if>
          <if cond="GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus  == 'active'">
            <action next="TT1005_UsingDeviceWithProblemYN_DM"/>
            <else>
              <action next="TT1015_OnNetworkTip_DM"/>
            </else>
          </if>
        </action>
        <action label="payment" next="TT1050_TroubleshootingPayment_DM">
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
            </else>
          </if>
          <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'payment_tip'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="voicemail_pin" next="TT1055_CheckAlreadyHavePIN_JDA">
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
            </else>
          </if>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.callType" expr="'reset_pin'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="none">
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'Technical_Support_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'Technical_Support_Spanish'"/>
            </else>
          </if>
          <session-mapping key="GlobalVars.callType" expr="'help_me_out'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="TT1060_GoTo_CareFAQ_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1001_ini_01">
                <prompt-segments>
                  <audiofile text="Which of these are you having trouble with   The Metro network ,  making or receiving calls,   a feature or service,  or  a payment  You can also say  I need to reset my voicemail PIN  or say  it s none of those'" src="TT1001_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TT1001_ini_01">
                <prompt-segments>
                  <audiofile text="Which of these are you having trouble with   The Metro network ,  making or receiving calls,   a feature or service,  or  a payment  You can also say  I need to reset my voicemail PIN  or say  it s none of those'" src="TT1001_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TT1001_ni1_01">
                <prompt-segments>
                  <audiofile text="You can say   Metro network,   making or receiving calls,   a feature or service,  or  a payment  You can also say  I need to reset my voicemail PIN  or say  it s none of those " src="TT1001_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="TT1001_ni2_01">
                <prompt-segments>
                  <audiofile text="Please say  Metro network  or press 1,  making or receiving calls  or press 2,  a feature or service  - 3,  or  a payment  - 4  You can also say  I need to reset my voicemail PIN or press 5 or  it s none of those  - 6" src="TT1001_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1001_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say   Metro network,   making or receiving calls,   a feature or service,  or  a payment  You can also say  I need to reset my voicemail PIN  or say  it s none of those " src="TT1001_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1001_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say  Metro network  or press 1,  making or receiving calls  or press 2,  a feature or service  - 3,  or  a payment  - 4  You can also say  I need to reset my voicemail PIN or press 5 or  it s none of those  - 6" src="TT1001_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TT1001_ini_01">
                <prompt-segments>
                  <audiofile text="Which of these are you having trouble with   The Metro network ,  making or receiving calls,   a feature or service,  or  a payment  You can also say  I need to reset my voicemail PIN  or say  it s none of those'" src="TT1001_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="TT1001_MainTroubleshootingOptions_DM.grxml" count="1"/>
          <dtmfgrammars filename="TT1001_MainTroubleshootingOptions_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="TT1005_UsingDeviceWithProblemYN_DM" type="YSNO">
      <success>
        <action label="true" next="TT1010_CallOnOtherDevice_PP">
          <session-mapping key="usingBrokenDevice" expr="'yes'"/>
        </action>
        <action label="false" next="TT1015_OnNetworkTip_DM">
          <session-mapping key="usingBrokenDevice" expr="'no'"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1005_ini_01">
                <prompt-segments>
                  <audiofile text="Are you calling on the device you're having trouble with?" src="TT1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TT1005_ini_01">
                <prompt-segments>
                  <audiofile text="Are you calling on the device you're having trouble with?" src="TT1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TT1005_ni1_01">
                <prompt-segments>
                  <audiofile text="Are you calling on the device you're having trouble with?" src="TT1005_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="TT1005_ni2_01">
                <prompt-segments>
                  <audiofile text="If you're calling on the device you're having trouble with, say 'yes' or press 1 If you're not, say 'no' or press 2" src="TT1005_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1005_nm1_01">
                <prompt-segments>
                  <audiofile text="Are you calling on the device you're having trouble with?" src="TT1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you're calling on the device you're having trouble with, say 'yes' or press 1 If you're not, say 'no' or press 2" src="TT1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TT1005_ini_01">
                <prompt-segments>
                  <audiofile text="Are you calling on the device you're having trouble with?" src="TT1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="TT1005_UsingDeviceWithProblemYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="TT1005_UsingDeviceWithProblemYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="TT1010_CallOnOtherDevice_PP">
      <session-mapping key="serviceDialed" value="GlobalVars.serviceDialed" type="String"/>
      <audio>
        <prompt id="TT1010_ini_01" cond="serviceDialed == '611'">
          <prompt-segments>
            <audiofile text="Actually, it'll be easier to fix your problem if you aren't calling on the device that we're trying to fix But let's continue anyway If you get disconnected, please call back on a different phone, using our toll free number That's 888 8METRO8 And then choose the 'troubleshooting tips' option again" src="TT1010_ini_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TT1010_ini_02" cond="serviceDialed != '611'">
          <prompt-segments>
            <audiofile text="Actually, it'll be easier to fix your problem if you aren't calling on the device that we're trying to fix But let's continue anyway If you get disconnected, please call back on a different phone, and choose the 'troubleshooting tips' option again" src="TT1010_ini_02.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="TT1015_OnNetworkTip_DM"/>
    </play-state>

    <dm-state id="TT1015_OnNetworkTip_DM" type="CUST">
      <success>
        <action label="repeat" next="TT1015_OnNetworkTip_DM"/>
        <action label="continue" next="TT1020_PowerCycleTipOfferYN_DM"/>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1015_ini_01" cond="usingBrokenDevice == 'yes'">
                <prompt-segments>
                  <audiofile text="Ok, let's get started" src="TT1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1015_ini_02" cond="transferDestination == 'agent'">
                <prompt-segments>
                  <audiofile text="I have 3 tips In most cases, one of these will fix your problem If we don t fix it here, I ll get you to an agent Here s the first tip Make sure you re either in a Metro coverage area or on a working Wi-Fi network To find out if you re in a Metro coverage area, you can check the coverage map at metrobyt-mobilecom" src="TT1015_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1015_ini_03" cond="transferDestination != 'agent'">
                <prompt-segments>
                  <audiofile text="I have 3 tips In most cases, one of these will fix your problem If we don t fix it here, we can help you at a Metro store or authorized dealer Here s the first tip Make sure you re either in a Metro coverage area or on a working Wi-Fi network To find out if you re in a Metro coverage area, you can check the coverage map at metrobyt-mobilecom " src="TT1015_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1015_ini_04">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' When you're ready for the next tip, say 'continue'" src="TT1015_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TT1015_ree_01">
                <prompt-segments>
                  <audiofile text="Sure Make sure you re either in a Metro coverage area or on a working Wi-Fi network To find out if you re in a Metro coverage area, you can check the coverage map at metrobyt-mobilecom " src="TT1015_ree_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1015_ree_02">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' When you're ready for the next tip, say 'continue'" src="TT1015_ree_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TT1015_ni1_01">
                <prompt-segments>
                  <audiofile text="Please say 'repeat' or 'continue'" src="TT1015_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="TT1015_nm1_01">
                <prompt-segments>
                  <audiofile text="Sorry, please say 'repeat' or 'continue'" src="TT1015_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TT1015_ini_01" cond="usingBrokenDevice == 'yes'">
                <prompt-segments>
                  <audiofile text="Ok, let's get started" src="TT1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1015_ini_02" cond="transferDestination == 'agent'">
                <prompt-segments>
                  <audiofile text="I have 3 tips In most cases, one of these will fix your problem If we don t fix it here, I ll get you to an agent Here s the first tip Make sure you re either in a Metro coverage area or on a working Wi-Fi network To find out if you re in a Metro coverage area, you can check the coverage map at metrobyt-mobilecom" src="TT1015_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1015_ini_03" cond="transferDestination != 'agent'">
                <prompt-segments>
                  <audiofile text="I have 3 tips In most cases, one of these will fix your problem If we don t fix it here, we can help you at a Metro store or authorized dealer Here s the first tip Make sure you re either in a Metro coverage area or on a working Wi-Fi network To find out if you re in a Metro coverage area, you can check the coverage map at metrobyt-mobilecom " src="TT1015_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1015_ini_04">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' When you're ready for the next tip, say 'continue'" src="TT1015_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="TT1015_OnNetworkTip_DM.grxml" count="1"/>
          <dtmfgrammars filename="TT1015_OnNetworkTip_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="TT1020_PowerCycleTipOfferYN_DM" type="YSNO">
      <success>
        <action label="true" next="TT1030_WiFiTipOfferYN_DM">
          <session-mapping key="GlobalVars.fromTT1020" value="true" type="Boolean"/>
        </action>
        <action label="false">
          <if cond="usingBrokenDevice == 'yes'">
            <action next="TT1026_PowerCycleTipBrokenDevice_DM"/>
            <else>
              <action next="TT1025_PowerCycleTipWaitSBI_DM"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1020_ini_01">
                <prompt-segments>
                  <audiofile text="Alright My second tip is that turning your device off and then back on will usually fix this problem Have you already tried that?" src="TT1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TT1020_ini_01">
                <prompt-segments>
                  <audiofile text="Alright My second tip is that turning your device off and then back on will usually fix this problem Have you already tried that?" src="TT1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TT1020_ni1_01">
                <prompt-segments>
                  <audiofile text="Have you tried turning your device off and then back on?" src="TT1020_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="TT1020_ni2_01">
                <prompt-segments>
                  <audiofile text="If you've already tried turning your device off and then back on, say 'yes' or press 1 If you haven't, say 'no' or press 2" src="TT1020_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1020_nm1_01">
                <prompt-segments>
                  <audiofile text="Have you tried turning your device off and then back on?" src="TT1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you've already tried turning your device off and then back on, say 'yes' or press 1 If you haven't, say 'no' or press 2" src="TT1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TT1020_ini_01">
                <prompt-segments>
                  <audiofile text="Alright My second tip is that turning your device off and then back on will usually fix this problem Have you already tried that?" src="TT1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="TT1020_PowerCycleTipOfferYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="TT1020_PowerCycleTipOfferYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="TT1025_PowerCycleTipWaitSBI_DM" type="CUST">
      <success>
        <action label="not_fixed" next="TT1030_WiFiTipOfferYN_DM"/>
        <action label="skip" next="TT1030_WiFiTipOfferYN_DM"/>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1025_ini_01">
                <prompt-segments>
                  <audiofile text="Ok, go ahead and turn your device off and back on again using the power button on the top or side of the device When the device is back on, check if your problem's been fixed I'll wait here If you still have the same problem, say 'it's not fixed' If the problem is corrected, feel free to hang up You can also say 'skip this step'" src="TT1025_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1025_ini_02">
                <prompt-segments>
                  <audiofile text="When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or you can just hang up Let's move on" src="TT1025_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TT1025_ini_01">
                <prompt-segments>
                  <audiofile text="Ok, go ahead and turn your device off and back on again using the power button on the top or side of the device When the device is back on, check if your problem's been fixed I'll wait here If you still have the same problem, say 'it's not fixed' If the problem is corrected, feel free to hang up You can also say 'skip this step'" src="TT1025_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1025_ini_02">
                <prompt-segments>
                  <audiofile text="When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or you can just hang up Let's move on" src="TT1025_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TT1025_ini_01">
                <prompt-segments>
                  <audiofile text="Ok, go ahead and turn your device off and back on again using the power button on the top or side of the device When the device is back on, check if your problem's been fixed I'll wait here If you still have the same problem, say 'it's not fixed' If the problem is corrected, feel free to hang up You can also say 'skip this step'" src="TT1025_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1025_ini_02">
                <prompt-segments>
                  <audiofile text="When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or you can just hang up Let's move on" src="TT1025_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="TT1025_PowerCycleTipWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="TT1025_PowerCycleTipWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="0ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="TT1026_PowerCycleTipBrokenDevice_DM" type="CUST">
      <success>
        <action label="repeat" next="TT1026_PowerCycleTipBrokenDevice_DM"/>
        <action label="continue" next="TT1030_WiFiTipOfferYN_DM"/>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1026_ini_01">
                <prompt-segments>
                  <audiofile text="After you end this call, turn your device off and back on again using the power button on the top or side of the device When the device is back on, check if your problem's been fixed" src="TT1026_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1026_ini_02">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' When you're ready for the next tip, say 'continue'" src="TT1026_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TT1026_ree_01">
                <prompt-segments>
                  <audiofile text="Sure" src="TT1026_ree_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1026_ree_02">
                <prompt-segments>
                  <audiofile text="After you end this call, turn your device off and back on again using the power button on the top or side of the device When the device is back on, check if your problem's been fixed" src="TT1026_ree_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1026_ree_03">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' When you're ready for the next tip, say 'continue'" src="TT1026_ree_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TT1026_ni1_01">
                <prompt-segments>
                  <audiofile text="Please say 'repeat' or 'continue'" src="TT1026_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="TT1026_nm1_01">
                <prompt-segments>
                  <audiofile text="Sorry, please say 'repeat' or 'continue'" src="TT1026_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TT1026_ini_01">
                <prompt-segments>
                  <audiofile text="After you end this call, turn your device off and back on again using the power button on the top or side of the device When the device is back on, check if your problem's been fixed" src="TT1026_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1026_ini_02">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' When you're ready for the next tip, say 'continue'" src="TT1026_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="TT1026_PowerCycleTipBrokenDevice_DM.grxml" count="1"/>
          <dtmfgrammars filename="TT1026_PowerCycleTipBrokenDevice_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="TT1030_WiFiTipOfferYN_DM" type="YSNO">
      <session-mapping key="fromTT1020" value="GlobalVars.fromTT1020" type="String"/>
      <success>
        <session-mapping key="GlobalVars.fromTT1020" value="false" type="Boolean"/>
        <action label="true" next="TT1035_WiFiTipWaitSBI_DM"/>
        <action label="false">
          <if cond="transferDestination == 'agent'">
            <if cond="usingBrokenDevice == 'yes'">
              <action next="TT1040_TransferOffer_DM"/>
              <else>
                <session-mapping key="GlobalVars.maxRetryTroubleshooting" value="false" type="Boolean"/>
                <action next="TT1045_Transfer_PP"/>
              </else>
            </if>
            <else>
              <action next="TT1038_StoreOffer_DM"/>
            </else>
          </if>
        </action>
        <action label="operator">
          <if cond="transferDestination == 'agent'">
            <if cond="usingBrokenDevice == 'yes'">
              <action next="TT1040_TransferOffer_DM"/>
              <else>
                <session-mapping key="GlobalVars.maxRetryTroubleshooting" value="false" type="Boolean"/>
                <action next="TT1045_Transfer_PP"/>
              </else>
            </if>
            <else>
              <action next="TT1038_StoreOffer_DM"/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.fromTT1020" value="false" type="Boolean"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1030_ini_03" cond="fromTT1020 == true">
                <prompt-segments>
                  <audiofile text="Alright" src="TT1030_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="fromTT1020 == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1030_ini_01" cond="transferDestination == 'agent'">
                <prompt-segments>
                  <audiofile text="I have one last tip for you  and if this one doesn t work, I ll get you to an agent Here s the tip You can use your phone on the Metro network or over Wi-Fi To make sure that Wi-Fi is not the cause of your problem, you should try turning it off but you must be in a Metro coverage area Do you want to try that now" src="TT1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1030_ini_02" cond="transferDestination != 'agent'">
                <prompt-segments>
                  <audiofile text="I have one last tip for you Here it is You can use your phone on the Metro network or over Wi-Fi To make sure that Wi-Fi is not the cause of your problem, you should try turning it off but you must be in a Metro coverage area Do you want to try that now?" src="TT1030_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TT1030_ini_03" cond="fromTT1020 == true">
                <prompt-segments>
                  <audiofile text="Alright" src="TT1030_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="fromTT1020 == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1030_ini_01" cond="transferDestination == 'agent'">
                <prompt-segments>
                  <audiofile text="I have one last tip for you  and if this one doesn t work, I ll get you to an agent Here s the tip You can use your phone on the Metro network or over Wi-Fi To make sure that Wi-Fi is not the cause of your problem, you should try turning it off but you must be in a Metro coverage area Do you want to try that now" src="TT1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1030_ini_02" cond="transferDestination != 'agent'">
                <prompt-segments>
                  <audiofile text="I have one last tip for you Here it is You can use your phone on the Metro network or over Wi-Fi To make sure that Wi-Fi is not the cause of your problem, you should try turning it off but you must be in a Metro coverage area Do you want to try that now?" src="TT1030_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TT1030_ni1_01">
                <prompt-segments>
                  <audiofile text="Do you want to try turning off WiFi now?" src="TT1030_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="TT1030_ni2_01">
                <prompt-segments>
                  <audiofile text="If you want to turn off WiFi now, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="TT1030_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1030_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to try turning off WiFi now?" src="TT1030_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1030_nm2_01">
                <prompt-segments>
                  <audiofile text="If you want to turn off WiFi now, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="TT1030_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TT1030_ini_03" cond="fromTT1020 == true">
                <prompt-segments>
                  <audiofile text="Alright" src="TT1030_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="fromTT1020 == true">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1030_ini_01" cond="transferDestination == 'agent'">
                <prompt-segments>
                  <audiofile text="I have one last tip for you  and if this one doesn t work, I ll get you to an agent Here s the tip You can use your phone on the Metro network or over Wi-Fi To make sure that Wi-Fi is not the cause of your problem, you should try turning it off but you must be in a Metro coverage area Do you want to try that now" src="TT1030_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1030_ini_02" cond="transferDestination != 'agent'">
                <prompt-segments>
                  <audiofile text="I have one last tip for you Here it is You can use your phone on the Metro network or over Wi-Fi To make sure that Wi-Fi is not the cause of your problem, you should try turning it off but you must be in a Metro coverage area Do you want to try that now?" src="TT1030_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="TT1030_WiFiTipOfferYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="TT1030_WiFiTipOfferYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="TT1035_WiFiTipWaitSBI_DM" type="CUST">
      <success>
        <action label="not_fixed">
          <if cond="transferDestination == 'agent'">
            <if cond="usingBrokenDevice == 'yes'">
              <action next="TT1040_TransferOffer_DM"/>
              <else>
                <session-mapping key="GlobalVars.maxRetryTroubleshooting" value="false" type="Boolean"/>
                <action next="TT1045_Transfer_PP"/>
              </else>
            </if>
            <else>
              <action next="TT1038_StoreOffer_DM"/>
            </else>
          </if>
        </action>
        <action label="skip">
          <if cond="transferDestination == 'agent'">
            <if cond="usingBrokenDevice == 'yes'">
              <action next="TT1040_TransferOffer_DM"/>
              <else>
                <session-mapping key="GlobalVars.maxRetryTroubleshooting" value="false" type="Boolean"/>
                <action next="TT1045_Transfer_PP"/>
              </else>
            </if>
            <else>
              <action next="TT1038_StoreOffer_DM"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1035_ini_01">
                <prompt-segments>
                  <audiofile text="Ok, please open your device's Settings and turn off Wi-Fi I'll wait for you to do that If you still have the same problem, say 'it's not fixed' If the problem is corrected, feel free to hang up You can also say 'skip this step' You're turning off Wi-Fi in your device's settings If you still have the same problem after you've done that, say 'it's not fixed' If the problem is corrected, feel free to hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You're turning off Wi-Fi in your device's settings If you still have the same problem after you've done that, say 'it's not fixed' or press 1 If the problem is corrected, feel free to hang up When you're ready, say 'it's not fixed' or press 1 or you can just hang up" src="TT1035_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TT1035_ini_01">
                <prompt-segments>
                  <audiofile text="Ok, please open your device's Settings and turn off Wi-Fi I'll wait for you to do that If you still have the same problem, say 'it's not fixed' If the problem is corrected, feel free to hang up You can also say 'skip this step' You're turning off Wi-Fi in your device's settings If you still have the same problem after you've done that, say 'it's not fixed' If the problem is corrected, feel free to hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You're turning off Wi-Fi in your device's settings If you still have the same problem after you've done that, say 'it's not fixed' or press 1 If the problem is corrected, feel free to hang up When you're ready, say 'it's not fixed' or press 1 or you can just hang up" src="TT1035_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TT1035_ini_01">
                <prompt-segments>
                  <audiofile text="Ok, please open your device's Settings and turn off Wi-Fi I'll wait for you to do that If you still have the same problem, say 'it's not fixed' If the problem is corrected, feel free to hang up You can also say 'skip this step' You're turning off Wi-Fi in your device's settings If you still have the same problem after you've done that, say 'it's not fixed' If the problem is corrected, feel free to hang up You can say 'it's not fixed' or press 1 at any time or you can just hang up When you're ready, say 'it's not fixed' or press 1 or if your problem is fixed, you can just hang up You're turning off Wi-Fi in your device's settings If you still have the same problem after you've done that, say 'it's not fixed' or press 1 If the problem is corrected, feel free to hang up When you're ready, say 'it's not fixed' or press 1 or you can just hang up" src="TT1035_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommandsNoOperator_dtmf.grxml">
          <grammars filename="TT1035_WiFiTipWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="TT1035_WiFiTipWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="0ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="TT1038_StoreOffer_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <action label="find_store" next="TT1080_GoTo_StoreLocator_SD">
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'unknown'"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1038_ini_01" cond="callType == 'payment_tip'">
                <prompt-segments>
                  <audiofile text="For more help, you can visit a Metro  store or authorized dealer You can say  find a store  or if you re finished, you can just hang up" src="TT1038_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1038_ini_02" cond="callType != 'payment_tip'">
                <prompt-segments>
                  <audiofile text="For more help, you can bring your device to a Metro  store or authorized dealer You can say  find a store  or if you re finished, you can just hang up" src="TT1038_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TT1038_ini_01" cond="callType == 'payment_tip'">
                <prompt-segments>
                  <audiofile text="For more help, you can visit a Metro  store or authorized dealer You can say  find a store  or if you re finished, you can just hang up" src="TT1038_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1038_ini_02" cond="callType != 'payment_tip'">
                <prompt-segments>
                  <audiofile text="For more help, you can bring your device to a Metro  store or authorized dealer You can say  find a store  or if you re finished, you can just hang up" src="TT1038_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TT1038_ni1_01">
                <prompt-segments>
                  <audiofile text="You can say help me find a MetroPCS store or if you're finished, you can just hang up" src="TT1038_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1038_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say help me find a MetroPCS store or if you're finished, you can just hang up" src="TT1038_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TT1038_ini_01" cond="callType == 'payment_tip'">
                <prompt-segments>
                  <audiofile text="For more help, you can visit a Metro  store or authorized dealer You can say  find a store  or if you re finished, you can just hang up" src="TT1038_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1038_ini_02" cond="callType != 'payment_tip'">
                <prompt-segments>
                  <audiofile text="For more help, you can bring your device to a Metro  store or authorized dealer You can say  find a store  or if you re finished, you can just hang up" src="TT1038_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="TT1038_StoreOffer_DM.grxml" count="1"/>
          <dtmfgrammars filename="TT1038_StoreOffer_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1038_cnf_ini_01">
                <prompt-segments>
                  <audiofile text="You want help finding a Metro store Is that right?" src="TT1038_cnf_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="TT1040_TransferOffer_DM" type="CUST">
      <success>
        <action label="transfer" next="TT1045_Transfer_PP">
          <session-mapping key="GlobalVars.maxRetryTroubleshooting" value="false" type="Boolean"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.maxRetryTroubleshooting" value="false" type="Boolean"/>
          <action next="TT1045_Transfer_PP"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1040_ini_01">
                <prompt-segments>
                  <audiofile text="I can transfer you to an agent now but the agent may ask you to hang up and call again from another phone You can say 'transfer me anyway' However, I recommend that you hang up and call back from another phone when it's convenient for you When you call back, choose troubleshooting tips from the main menu and then say 'agent'" src="TT1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TT1040_ini_01">
                <prompt-segments>
                  <audiofile text="I can transfer you to an agent now but the agent may ask you to hang up and call again from another phone You can say 'transfer me anyway' However, I recommend that you hang up and call back from another phone when it's convenient for you When you call back, choose troubleshooting tips from the main menu and then say 'agent'" src="TT1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TT1040_ni1_01">
                <prompt-segments>
                  <audiofile text="You can say 'transfer me'Or hang up and call back from another phone" src="TT1040_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1040_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'transfer me'Or hang up and call back from another phone" src="TT1040_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TT1040_ini_01">
                <prompt-segments>
                  <audiofile text="I can transfer you to an agent now but the agent may ask you to hang up and call again from another phone You can say 'transfer me anyway' However, I recommend that you hang up and call back from another phone when it's convenient for you When you call back, choose troubleshooting tips from the main menu and then say 'agent'" src="TT1040_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommandsNoOperator_dtmf.grxml">
          <grammars filename="TT1040_TransferOffer_DM.grxml" count="1"/>
          <dtmfgrammars filename="TT1040_TransferOffer_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <commandconfirmationprompts count="1" filename="" text="" id="TT_cmd_confirmation_initial"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="TT_cmd_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="TT1045_Transfer_PP">
      <session-mapping key="maxRetryTroubleshooting" value="GlobalVars.maxRetryTroubleshooting" type="String"/>
      <audio>
        <prompt id="TT1045_out_02" cond="maxRetryTroubleshooting == true">
          <prompt-segments>
            <audiofile text="Sorry I'm having trouble I'll find someone to help One minute, please As always, we appreciate your business" src="TT1045_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TT1045_out_01" cond="maxRetryTroubleshooting !=  true">
          <prompt-segments>
            <audiofile text="Ok, I'll find someone to help One minute, please As always, we appreciate your business" src="TT1045_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.playTransferPrompt" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.playTransferMessage" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.troubleshootingTransfer" value="true" type="Boolean"/>
      <action next="TT1075_CallTransfer_SD"/>
    </play-state>

    <dm-state id="TT1050_TroubleshootingPayment_DM" type="CUST">
      <success>
        <action label="not_posted">
          <if cond="transferDestination == 'agent'">
            <session-mapping key="GlobalVars.maxRetryTroubleshooting" value="false" type="Boolean"/>
            <action next="TT1045_Transfer_PP"/>
            <else>
              <action next="TT1038_StoreOffer_DM"/>
            </else>
          </if>
        </action>
        <action label="operator">
          <if cond="transferDestination == 'agent'">
            <session-mapping key="GlobalVars.maxRetryTroubleshooting" value="false" type="Boolean"/>
            <action next="TT1045_Transfer_PP"/>
            <else>
              <action next="TT1038_StoreOffer_DM"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1050_ini_01" cond="transferDestination == 'agent'">
                <prompt-segments>
                  <audiofile text="Keep in mind that payments might take a little while to post If you submitted a payment more than 2 hours ago but it hasn't posted, say my payment hasn't posted and I'll find someone to help you Otherwise, please hang up for now and call back if it takes more than 2 hours to post " src="TT1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1050_ini_02" cond="transferDestination != 'agent'">
                <prompt-segments>
                  <audiofile text="Keep in mind that payments might take a little while to post If you submitted a payment more than 2 hours ago but it hasn't posted, say my payment hasn't posted Otherwise, please hang up for now and call back if it takes more than 2 hours to post" src="TT1050_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TT1050_ini_01" cond="transferDestination == 'agent'">
                <prompt-segments>
                  <audiofile text="Keep in mind that payments might take a little while to post If you submitted a payment more than 2 hours ago but it hasn't posted, say my payment hasn't posted and I'll find someone to help you Otherwise, please hang up for now and call back if it takes more than 2 hours to post " src="TT1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1050_ini_02" cond="transferDestination != 'agent'">
                <prompt-segments>
                  <audiofile text="Keep in mind that payments might take a little while to post If you submitted a payment more than 2 hours ago but it hasn't posted, say my payment hasn't posted Otherwise, please hang up for now and call back if it takes more than 2 hours to post" src="TT1050_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TT1050_ni1_01">
                <prompt-segments>
                  <audiofile text="You can say my payment hasn't posted Otherwise, please hang up and call back if your payment takes more than 2 hours to post" src="TT1050_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1050_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say my payment hasn't posted Otherwise, please hang up and call back if your payment takes more than 2 hours to post" src="TT1050_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TT1050_ini_01" cond="transferDestination == 'agent'">
                <prompt-segments>
                  <audiofile text="Keep in mind that payments might take a little while to post If you submitted a payment more than 2 hours ago but it hasn't posted, say my payment hasn't posted and I'll find someone to help you Otherwise, please hang up for now and call back if it takes more than 2 hours to post " src="TT1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TT1050_ini_02" cond="transferDestination != 'agent'">
                <prompt-segments>
                  <audiofile text="Keep in mind that payments might take a little while to post If you submitted a payment more than 2 hours ago but it hasn't posted, say my payment hasn't posted Otherwise, please hang up for now and call back if it takes more than 2 hours to post" src="TT1050_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="TT1050_TroubleshootingPayment_DM.grxml" count="1"/>
          <dtmfgrammars filename="TT1050_TroubleshootingPayment_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TT1050_cnf_ini_01">
                <prompt-segments>
                  <audiofile text="Your payment hasn't posted Is that right?" src="TT1050_cnf_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="TT1055_CheckAlreadyHavePIN_DS">
      <if cond="GlobalVars.collectedPIN == true">
        <action next="TT1070_GoTo_CareVoicemailPIN_SD"/>
        <else>
          <action next="TT1065_GoTo_CareLogin_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="TT1060_GoTo_CareFAQ_SD">
      <gotodialog next="FAQ_Main_Dialog"/>
      <action next="TT1060_GoTo_CareFAQ_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="TT1060_GoTo_CareFAQ_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="TT1065_GoTo_CareLogin_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="TT1065_GoTo_CareLogin_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="TT1065_GoTo_CareLogin_SD_return_CS">
      <action next="TT1070_GoTo_CareVoicemailPIN_SD"/>
    </custom-state>

    <subdialog-state id="TT1070_GoTo_CareVoicemailPIN_SD">
      <gotodialog next="VoiceMailPin_Main_Dialog"/>
      <action next="TT1070_GoTo_CareVoicemailPIN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="TT1070_GoTo_CareVoicemailPIN_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="TT1075_CallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="TT1075_CallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="TT1075_CallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="TT1080_GoTo_StoreLocator_SD">
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="TT1080_GoTo_StoreLocator_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="TT1080_GoTo_StoreLocator_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="TT1085_GoTo_GoodBye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="TT1085_GoTo_GoodBye_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="TT1085_GoTo_GoodBye_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

  </dialog>
  