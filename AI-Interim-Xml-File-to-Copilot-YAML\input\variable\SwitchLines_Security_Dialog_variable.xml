<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="acctLocked" value="true" type="boolean"/>
  <session-mapping key="switchLinesPINAttempts" value="1" type="integer"/>
  <session-mapping key="GlobalVars.acctLocked" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.switchLinesPINAttempts" value="1" type="integer"/>
  <session-mapping key="GlobalVars.accountNumber" value="GlobalVars.originalLine_accountNumber" type="string"/>
  <session-mapping key="accountStatus" value="active" type="string"/>
  <session-mapping key="GetAccountDetails.parentDeviceType" value="INT" type="string"/>
  <session-mapping key="GlobalVars.switchLinesEntryPoint" value="esn_swap" type="string"/>
  <session-mapping key="balance" value="0" type="integer"/>
  <session-mapping key="dueImmediatelyAmount" value="0" type="string"/>
  <session-mapping key="hasAutopay" value="true" type="string"/>
  <session-mapping key="remainingDays" value="20" type="integer"/>
  <session-mapping key="GlobalVars.aniMatch" value="false" type="boolean"/>
  <session-mapping key="GetBCSParameters.care_enable_switch_lines" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.switchLinesSuccess" value="true" type="boolean"/>
  <session-mapping key="accountPinToggleOn" value="true" type="boolean"/>
</session-mappings>
