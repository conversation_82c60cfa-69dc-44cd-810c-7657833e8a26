<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Voicestore_Main_Dialog">
    <subdialog-state id="VS1000_GoToBroadcastMessages_SD">
      <session-mapping key="GlobalVars.broadcastMessageKey" expr="'VS'"/>
      <gotodialog next="BroadcastMessages_Dialog"/>
      <action next="VS1000_GoToBroadcastMessages_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="VS1000_GoToBroadcastMessages_SD_return_CS">
      <action next="VS1002_CheckFutureDatedRequest_JDA"/>
    </custom-state>

    <data-access-state id="VS1001_GetAvailableFeatureOffers_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="ratePlan" value="GlobalVars.GetAccountDetails.ratePlan" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="eligibleForDataAddons" value="GlobalVars.eligibleForDataAddons" type="String"/>
      <session-mapping key="useCustomServiceAction" value="true" type="Boolean"/>
      <data-access id="GetAvailableFeatureOffers_VS1001" classname="com.nuance.metro.dataaccess.GetAvailableFeatureOffers_VS1001">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="JWTToken"/>
          <input-variable name="ratePlan"/>
          <input-variable name="sessionId"/>
          <input-variable name="languageCode"/>
          <input-variable name="eligibleForDataAddons"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="message"/>
          <output-variable name="features"/>
          <output-variable name="featureSocs"/>
          <output-variable name="featureSocsThirdParty"/>
          <output-variable name="featureActionMap"/>
          <output-variable name="unfilteredFeaturesList"/>
          <output-variable name="futureDate"/>
          <output-variable name="FeatureGrammarURL"/>
          <output-variable name="addServiceIndicator"/>
          <output-variable name="serviceIndicatorAddMessageEnglish"/>
          <output-variable name="serviceIndicatorAddMessageSpanish"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetAvailableFeatureOffers_VS1001.status == 'Success'">
          <session-mapping key="GlobalVars.GetAvailableFeatureOffers" expr="GetAvailableFeatureOffers_VS1001"/>
          <session-mapping key="GlobalVars.futureDate" expr="GetAvailableFeatureOffers_VS1001.futureDate"/>
          <session-mapping key="GlobalVars.FeatureGrammarURL" expr="GetAvailableFeatureOffers_VS1001.FeatureGrammarURL"/>
          <session-mapping key="GlobalVars.features" expr="GetAvailableFeatureOffers_VS1001.features"/>
          <action next="VS1010_CheckCanUseVoicestore_JDA"/>
          <else>
            <gotodialog next="Voicestore_Routing#VS1315_GoToCallTransfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="VS1002_CheckFutureDatedRequest_DS">
      <session-mapping key="GlobalVars.heardFutureDatedFeatureInstructions" value="false" type="Boolean"/>
      <if cond="(GlobalVars.subscriberFuturePricePlanInd == true)">
        <action next="VS1005_PlayFutureRequestTansferMsg_PP"/>
        <else>
          <action next="VS1001_GetAvailableFeatureOffers_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="VS1005_PlayFutureRequestTansferMsg_PP">
      <audio>
        <prompt id="VS1005_out_01">
          <prompt-segments>
            <audiofile text="I see you have an upcoming change on your account, so I'll need to transfer you to an agent for help with your services " src="VS1005_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <gotodialog next="Voicestore_Routing#VS1315_GoToCallTransfer_SD"/>
    </play-state>

    <decision-state id="VS1010_CheckCanUseVoicestore_DS">
      <session-mapping key="addFeatureIndicator" value="false" type="Boolean"/>
      <session-mapping key="addFeatureAction" value="" type="String"/>
      <session-mapping key="addFeature" value="false" type="Boolean"/>
      <session-mapping key="accountStatus" value="" type="String"/>
      <session-mapping key="GlobalVars.playOneFeature" value="false" type="Boolean"/>
      <if cond="GlobalVars.GetAccountDetails.accountStatus == 'suspended'">
        <gotodialog next="Voicestore_Routing#VS1315_GoToCallTransfer_SD"/>
      </if>
      <if cond="(GlobalVars.GetAvailableFeatureOffers.featureSocs.length + GlobalVars.GetAvailableFeatureOffers.featureSocsThirdParty.length) == 1">
        <session-mapping key="GlobalVars.playOneFeature" value="true" type="Boolean"/>
        <session-mapping key="GlobalVars.selectedFeature" expr="(GlobalVars.GetAvailableFeatureOffers.featureSocs.length&gt;0)?GlobalVars.GetAvailableFeatureOffers.featureSocs[0].toLowerCase():GlobalVars.GetAvailableFeatureOffers.featureSocsThirdParty[0].toLowerCase()"/>
        <action next="VS1025_SignUp_DM"/>
        <elseif cond="(GlobalVars.GetAvailableFeatureOffers.featureSocs.length + GlobalVars.GetAvailableFeatureOffers.featureSocsThirdParty.length) == 0">
          <gotodialog next="Voicestore_Routing#VS1315_GoToCallTransfer_SD"/>
        </elseif>
        <else>
          <action next="VS1015_FeatureMenu_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="VS1015_FeatureMenu_DM" type="CUST">
      <session-mapping key="featureGrammarURL" value="" type="String"/>
      <session-mapping key="featureSocs" value="GlobalVars.GetAvailableFeatureOffers.featureSocs.toString()" type="String"/>
      <session-mapping key="featureSocsThirdParty" value="GlobalVars.GetAvailableFeatureOffers.featureSocsThirdParty.toString()" type="String"/>
      <session-mapping key="allowedResponses" value="" type="String"/>
      <session-mapping key="allowedResponsesDTMF" value="" type="String"/>
      <session-mapping key="ifFromCurrentFeatures" value="GlobalVars.ifFromCurrentFeatures" type="String"/>
      <session-mapping key="featureGrammarURL" expr="GlobalVars.FeatureGrammarURL"/>
      <success>
        <action label="signedup_services">
          <action next="VS1085_GoToCurrentFeatures_SD"/>
        </action>
        <action label="more_features">
          <if cond="(GlobalVars.GetAvailableFeatureOffers.featureSocs.length + GlobalVars.GetAvailableFeatureOffers.featureSocsThirdParty.length) &gt; 8">
            <action next="VS1017_AdditionalFeatureMenu_DM"/>
            <else>
              <gotodialog next="Voicestore_Routing#VS1315_GoToCallTransfer_SD"/>
            </else>
          </if>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.selectedFeature" expr="VS1015_FeatureMenu_DM.returnvalue.toLowerCase()"/>
          <audio>
            <prompt id="VS1015_out_01">
              <prompt-segments>
                <audiofile text="Okay" src="VS1015_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="VS1025_SignUp_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="VS1015_ini_01">
                <prompt-segments>
                  <audiofile text="Which feature would you like Just say its name back to me" src="VS1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1015"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
              <prompt id="VS1015_ini_08" cond="ifFromCurrentFeatures == true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or 'hear more features' " src="VS1015_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_05" cond="ifFromCurrentFeatures != true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' " src="VS1015_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="VS1015_FeatureMenu_DM_reinvoke"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="VS1015_ini_01">
                <prompt-segments>
                  <audiofile text="Which feature would you like Just say its name back to me" src="VS1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1015"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
              <prompt id="VS1015_ini_08" cond="ifFromCurrentFeatures == true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or 'hear more features' " src="VS1015_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_05" cond="ifFromCurrentFeatures != true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' " src="VS1015_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="VS1015_ini_01">
                <prompt-segments>
                  <audiofile text="Which feature would you like Just say its name back to me" src="VS1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1015"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
              <prompt id="VS1015_ini_08" cond="ifFromCurrentFeatures == true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or 'hear more features' " src="VS1015_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_05" cond="ifFromCurrentFeatures != true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' " src="VS1015_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_01">
                <prompt-segments>
                  <audiofile text="Which feature would you like Just say its name back to me" src="VS1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1015"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
              <prompt id="VS1015_ini_08" cond="ifFromCurrentFeatures == true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or 'hear more features' " src="VS1015_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_05" cond="ifFromCurrentFeatures != true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' " src="VS1015_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_01">
                <prompt-segments>
                  <audiofile text="Which feature would you like Just say its name back to me" src="VS1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1015"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
              <prompt id="VS1015_ini_08" cond="ifFromCurrentFeatures == true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or 'hear more features' " src="VS1015_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_05" cond="ifFromCurrentFeatures != true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' " src="VS1015_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_01">
                <prompt-segments>
                  <audiofile text="Which feature would you like Just say its name back to me" src="VS1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1015"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
              <prompt id="VS1015_ini_08" cond="ifFromCurrentFeatures == true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or 'hear more features' " src="VS1015_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_05" cond="ifFromCurrentFeatures != true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' " src="VS1015_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_01">
                <prompt-segments>
                  <audiofile text="Which feature would you like Just say its name back to me" src="VS1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1015"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
              <prompt id="VS1015_ini_08" cond="ifFromCurrentFeatures == true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or 'hear more features' " src="VS1015_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_05" cond="ifFromCurrentFeatures != true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' " src="VS1015_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_01">
                <prompt-segments>
                  <audiofile text="Which feature would you like Just say its name back to me" src="VS1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1015"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
              <prompt id="VS1015_ini_08" cond="ifFromCurrentFeatures == true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or 'hear more features' " src="VS1015_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_05" cond="ifFromCurrentFeatures != true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' " src="VS1015_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="VS1015_ini_01">
                <prompt-segments>
                  <audiofile text="Which feature would you like Just say its name back to me" src="VS1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1015"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
              <prompt id="VS1015_ini_08" cond="ifFromCurrentFeatures == true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' or 'hear more features' " src="VS1015_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1015_ini_05" cond="ifFromCurrentFeatures != true">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that', 'hear more features', or to hear features you currently have on your plan, say 'plan details' " src="VS1015_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="VS1015_FeatureMenu_DM.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="VS1015_FeatureMenu_DM_dtmf.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesDTMFVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <vxml_properties termchar="" confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="gl_cnf_ini_03">
                <prompt-segments>
                  <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
                <else>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayFeatureFinalURL"/>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
                <else>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayFeatureFinalURL"/>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
                <else>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayFeatureFinalURL"/>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="VS1017_AdditionalFeatureMenu_DM" type="CUST">
      <session-mapping key="featureGrammarURL" value="GlobalVars.FeatureGrammarURL" type="String"/>
      <session-mapping key="length" value="GlobalVars.GetAvailableFeatureOffers.featureSocs.length;" type="String"/>
      <session-mapping key="lengthThirdParty" value="GlobalVars.GetAvailableFeatureOffers.featureSocsThirdParty.length;" type="String"/>
      <session-mapping key="featureSocs" value="" type="String"/>
      <session-mapping key="featureSocsThirdParty" value="" type="String"/>
      <session-mapping key="allowedResponses" value="" type="String"/>
      <session-mapping key="allowedResponsesDTMF" value="" type="String"/>
      <success>
        <action label="more_features">
          <gotodialog next="Voicestore_Routing#VS1315_GoToCallTransfer_SD"/>
        </action>
        <action label="go_back">
          <session-mapping key="GlobalVars.ifFromCurrentFeatures" value="false" type="Boolean"/>
          <action next="VS1015_FeatureMenu_DM"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.selectedFeature" expr="VS1017_AdditionalFeatureMenu_DM.returnvalue.toLowerCase()"/>
          <audio>
            <prompt id="VS1017_out_01">
              <prompt-segments>
                <audiofile text="Okay" src="VS1017_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="VS1025_SignUp_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1017"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1017"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1017"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1017"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1017"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1017"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1017"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1017"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt type="custom">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAvailableFeatureLists_VS1017"/>
                <param name="featureSocs" value="featureSocs" scope="request"/>
                <param name="featureSocsThirdParty" value="featureSocsThirdParty" scope="request"/>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="VS1017_AdditionalFeatureMenu_DM.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="VS1017_AdditionalFeatureMenu_DM_dtmf.jsp" count="1">
            <param name="allowedResponses" value="allowedResponsesDTMFVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnoinputs="1" maxnomatches="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="gl_cnf_ini_03">
                <prompt-segments>
                  <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
                <else>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayFeatureFinalURL"/>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
                <else>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayFeatureFinalURL"/>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
                <else>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_root">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayFeatureFinalURL"/>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="VS1025_SignUp_DM" type="CUST">
      <session-mapping key="selectedFeature" value="GlobalVars.selectedFeature" type="String"/>
      <session-mapping key="isThirdPartyFeature" value="isSelectedFeatureAThirdPartyFeature(GlobalVars.GetAvailableFeatureOffers.featureSocsThirdParty, GlobalVars.selectedFeature)" type="String"/>
      <session-mapping key="playOneFeature" value="GlobalVars.playOneFeature" type="String"/>
      <session-mapping key="fromIOM" value="GlobalVars.fromIOM" type="String"/>
      <session-mapping key="heardThirdPartyInfo" value="GlobalVars.heardThirdPartyInfo" type="String"/>
      <session-mapping key="addFeatureAction" value="" type="String"/>
      <success>
        <if cond="isThirdPartyFeature == true">
          <session-mapping key="GlobalVars.heardThirdPartyInfo" value="true" type="Boolean"/>
        </if>
        <action label="yes">
          <if cond="addFeatureAction == 'Custom after selected'">
            <action next="VS1205_CheckServiceIndicatorAddMessage_JDA"/>
          </if>
          <if cond="addFeatureAction == 'Transfer after selected'">
            <gotodialog next="Voicestore_Routing#VS1315_GoToCallTransfer_SD"/>
          </if>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.addFeatures" expr="GlobalVars.selectedFeature"/>
          <action next="VS1035_CheckNeedPIN_JDA"/>
        </action>
        <action label="no">
          <action next="VS1045_AnotherFeature_DM"/>
        </action>
        <action label="repeat">
          <action next="VS1025_SignUp_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="(playOneFeature == true) &amp;&amp; (isThirdPartyFeature != true) ">
                <prompt id="VS1025_ini_01">
                  <prompt-segments>
                    <audiofile text="Theres one feature you can sign up for" src="VS1025_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(playOneFeature == true) &amp;&amp; (isThirdPartyFeature == true) ">
                <prompt id="VS1025_ini_02">
                  <prompt-segments>
                    <audiofile text="Theres one third party feature you can sign up for" src="VS1025_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(playOneFeature == true)">
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="custom" expr="selectedFeature">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureFinalURL"/>
                </prompt>
                <prompt id="silence_750ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_750ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureDetails_detailsPrompt"/>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1025_ini_04" cond="(isThirdPartyFeature == true) &amp;&amp; (heardThirdPartyInfo != true)">
                <prompt-segments>
                  <audiofile text="Third party charges will appear separately on your bill To block those purchases, check your account settings on metrobyt-mobilecom, or say operator now to find out more" src="VS1025_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms" cond="(isThirdPartyFeature == true) &amp;&amp; ((fromIOM == true) || (heardThirdPartyInfo != true))">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1025_ini_11">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again, sign up, or cancel?" src="VS1025_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1025_ini_12">
                <prompt-segments>
                  <audiofile text="You can also say 'repeat that'" src="VS1025_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="(playOneFeature == true) &amp;&amp; (isThirdPartyFeature != true) ">
                <prompt id="VS1025_ini_01">
                  <prompt-segments>
                    <audiofile text="Theres one feature you can sign up for" src="VS1025_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(playOneFeature == true) &amp;&amp; (isThirdPartyFeature == true) ">
                <prompt id="VS1025_ini_02">
                  <prompt-segments>
                    <audiofile text="Theres one third party feature you can sign up for" src="VS1025_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(playOneFeature == true)">
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="custom" expr="selectedFeature">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureFinalURL"/>
                </prompt>
                <prompt id="silence_750ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_750ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureDetails_detailsPrompt"/>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1025_ini_04" cond="(isThirdPartyFeature == true) &amp;&amp; (heardThirdPartyInfo != true)">
                <prompt-segments>
                  <audiofile text="Third party charges will appear separately on your bill To block those purchases, check your account settings on metrobyt-mobilecom, or say operator now to find out more" src="VS1025_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms" cond="(isThirdPartyFeature == true) &amp;&amp; ((fromIOM == true) || (heardThirdPartyInfo != true))">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1025_ini_11">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again, sign up, or cancel?" src="VS1025_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1025_ini_12">
                <prompt-segments>
                  <audiofile text="You can also say 'repeat that'" src="VS1025_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="VS1025_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to sign up for " src="VS1025_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureDetails_MedialPromptURL"/>
              </prompt>
              <if cond="(fromIOM == true)">
                <prompt id="VS1025_nm1_03">
                  <prompt-segments>
                    <audiofile text="say 'repeat' Otherwise, please say 'sign Up' or 'skip it'" src="VS1025_nm1_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="VS1025_nm1_02">
                    <prompt-segments>
                      <audiofile text="Please say Yes or No " src="VS1025_nm1_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureDetails_detailsPrompt"/>
              </prompt>
              <if cond="(fromIOM == true)">
                <prompt id="VS1025_nm2_02">
                  <prompt-segments>
                    <audiofile text="Please say 'sign up' or press 1, or say 'skip it' or press 2" src="VS1025_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="VS1025_nm2_01">
                    <prompt-segments>
                      <audiofile text="Would you like to sign up for this? Say yes or press 1, or no or press 2 " src="VS1025_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureDetails_detailsPrompt"/>
              </prompt>
              <if cond="(fromIOM == true)">
                <prompt id="VS1025_nm2_02">
                  <prompt-segments>
                    <audiofile text="Please say 'sign up' or press 1, or say 'skip it' or press 2" src="VS1025_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="VS1025_nm2_01">
                    <prompt-segments>
                      <audiofile text="Would you like to sign up for this? Say yes or press 1, or no or press 2 " src="VS1025_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1025_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to sign up for " src="VS1025_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureDetails_MedialPromptURL"/>
              </prompt>
              <prompt id="VS1025_nm1_02">
                <prompt-segments>
                  <audiofile text="Please say Yes or No " src="VS1025_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureDetails_detailsPrompt"/>
              </prompt>
              <prompt id="VS1025_nm2_01">
                <prompt-segments>
                  <audiofile text="Would you like to sign up for this? Say yes or press 1, or no or press 2 " src="VS1025_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureDetails_detailsPrompt"/>
              </prompt>
              <if cond="(fromIOM == true)">
                <prompt id="VS1025_nm2_02">
                  <prompt-segments>
                    <audiofile text="Please say 'sign up' or press 1, or say 'skip it' or press 2" src="VS1025_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="VS1025_nm2_01">
                    <prompt-segments>
                      <audiofile text="Would you like to sign up for this? Say yes or press 1, or no or press 2 " src="VS1025_nm2_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="(playOneFeature == true) &amp;&amp; (isThirdPartyFeature != true) ">
                <prompt id="VS1025_ini_01">
                  <prompt-segments>
                    <audiofile text="Theres one feature you can sign up for" src="VS1025_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(playOneFeature == true) &amp;&amp; (isThirdPartyFeature == true) ">
                <prompt id="VS1025_ini_02">
                  <prompt-segments>
                    <audiofile text="Theres one third party feature you can sign up for" src="VS1025_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(playOneFeature == true)">
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="custom" expr="selectedFeature">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureFinalURL"/>
                </prompt>
                <prompt id="silence_750ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_750ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureDetails_detailsPrompt"/>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1025_ini_04" cond="(isThirdPartyFeature == true) &amp;&amp; (heardThirdPartyInfo != true)">
                <prompt-segments>
                  <audiofile text="Third party charges will appear separately on your bill To block those purchases, check your account settings on metrobyt-mobilecom, or say operator now to find out more" src="VS1025_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms" cond="(isThirdPartyFeature == true) &amp;&amp; ((fromIOM == true) || (heardThirdPartyInfo != true))">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1025_ini_11">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again, sign up, or cancel?" src="VS1025_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1025_ini_12">
                <prompt-segments>
                  <audiofile text="You can also say 'repeat that'" src="VS1025_ini_12.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="VS1025_SignUp_DM.grxml" count="1"/>
          <dtmfgrammars filename="VS1025_SignUp_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureFinalURL"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureFinalURL"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt type="custom" expr="selectedFeature">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlaySelectedFeatureFinalURL"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="VS1035_CheckNeedPIN_DS">
      <if cond="GlobalVars.loggedIn == true">
        <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
        <gotodialog next="Voicestore_AddFeature#VS1118_DetectConflictingOffers_SD"/>
        <else>
          <action next="VS1040_GoToLogin_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="VS1040_GoToLogin_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="VS1040_GoToLogin_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="VS1040_GoToLogin_SD_return_CS">
      <gotodialog next="Voicestore_AddFeature#VS1118_DetectConflictingOffers_SD"/>
    </custom-state>

    <dm-state id="VS1045_AnotherFeature_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.fromVS1045" value="true" type="Boolean"/>
          <action next="VS1001_GetAvailableFeatureOffers_DB_DA"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.fromVS1045" value="true" type="Boolean"/>
          <gotodialog next="Voicestore_Routing#VS1320_GoToAnythingElse_SD"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.fromVS1045" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.selectedFeature" expr="VS1045_AnotherFeature_DM.returnvalue"/>
          <gotodialog next="Voicestore_Routing#VS1320_GoToAnythingElse_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="VS1045_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear about any of our other features?" src="VS1045_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="VS1045_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear about any of our other features?" src="VS1045_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="VS1045_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no   Would you like to hear about any of the other services that we offer?" src="VS1045_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1045_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no   Would you like to hear about any of the other services that we offer?" src="VS1045_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1045_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no   Would you like to hear about any of the other services that we offer?" src="VS1045_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1045_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no   Would you like to hear about any of the other services that we offer?" src="VS1045_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1045_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no   Would you like to hear about any of the other services that we offer?" src="VS1045_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="VS1045_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no   Would you like to hear about any of the other services that we offer?" src="VS1045_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="VS1045_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to hear about any of our other features?" src="VS1045_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="VS1045_AnotherFeature_DM.jsp" count="1"/>
          <dtmfgrammars filename="VS1045_AnotherFeature_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="VS1085_GoToCurrentFeatures_SD">
      <gotodialog next="CurrentFeatures_Main_Dialog"/>
      <action next="VS1085_GoToCurrentFeatures_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="VS1085_GoToCurrentFeatures_SD_return_CS">
      <session-mapping key="GlobalVars.ifFromCurrentFeatures" value="true" type="Boolean"/>
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="VS1205_CheckServiceIndicatorAddMessage_DS">
      <session-mapping key="selectedFeature" value="GlobalVars.selectedFeature" type="String"/>
      <session-mapping key="serviceIndicatorAddMessageEnglish" value="" type="String"/>
      <session-mapping key="serviceIndicatorAddMessageSpanish" value="" type="String"/>
      <if cond="language == 'en-US' &amp;&amp; serviceIndicatorAddMessageEnglish != ''">
        <action next="VS1210_PlayServiceIndicatorAddMessage_PP"/>
        <elseif cond="language != 'en-US' &amp;&amp; serviceIndicatorAddMessageSpanish != ''">
          <action next="VS1210_PlayServiceIndicatorAddMessage_PP"/>
        </elseif>
        <else>
          <action next="VS1215_CheckAddServiceIndicator_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="VS1210_PlayServiceIndicatorAddMessage_PP">
      <session-mapping key="urlOrTts" value="language == 'en-US'?GlobalVars.serviceIndicatorAddMessageEnglish:GlobalVars.serviceIndicatorAddMessageSpanish" type="String"/>
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayURL"/>
          <param name="urlOrTts" value="urlOrTts" scope="request"/>
        </prompt>
      </audio>
      <action next="VS1215_CheckAddServiceIndicator_JDA"/>
    </play-state>

    <decision-state id="VS1215_CheckAddServiceIndicator_DS">
      <session-mapping key="selectedFeature" value="GlobalVars.selectedFeature" type="String"/>
      <session-mapping key="addServiceIndicator" value="" type="String"/>
      <if cond="addServiceIndicator  == 'Transfer after selected'">
        <gotodialog next="Voicestore_Routing#VS1315_GoToCallTransfer_SD"/>
        <elseif cond="addServiceIndicator  == 'Continue after selected'">
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.addFeatures" expr="GlobalVars.selectedFeature"/>
          <action next="VS1035_CheckNeedPIN_JDA"/>
        </elseif>
        <else>
          <gotodialog next="Voicestore_Routing#VS1340_GoToGoodbye_SD"/>
        </else>
      </if>
    </decision-state>

  </dialog>
  