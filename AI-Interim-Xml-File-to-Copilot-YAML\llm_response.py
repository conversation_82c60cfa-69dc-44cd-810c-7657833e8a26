import os
from openai import AzureOpenAI
from dotenv import load_dotenv
import re
import logging
from util import validate_and_fix_yaml

logging.basicConfig(filename='agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')

# Global variable to track if this is the first execution
is_first_time = True

# Load environment variables from a .env file if present
load_dotenv()

# Reading input file
def read_markdown_file(file_path):
    logging.info('llm_response:read_markdown_file')
    with open(file_path, 'r') as file:
        content = file.read()
    return content

# Generating final prompt to give GPT
def generate_prompt(chunk, prompt_file):
    logging.info('llm_response:generate_prompt')
    prompt = f'Generate output YML for the given Input XML: {chunk}. \n\n'
    finalPrompt = read_markdown_file(prompt_file) + prompt
    return finalPrompt

# To get response from LLM for provided prompt
def get_response_from_llm(prompt):
    logging.info('llm_response:get_response_from_llm')
    try:
        client = AzureOpenAI(
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"), 
            api_key=os.getenv("AZURE_OPENAI_KEY"),  
            api_version=os.getenv("AZURE_OPENAI_VERSION")
        )

        response = client.chat.completions.create(
            model='gpt-4o',
            temperature=0,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
        )

        return response.choices[0].message.content
    except Exception as e:
        return f"Error generating response: {str(e)}"

def extract_yaml_from_response(response, formatted_yml_file):
    logging.info('llm_response:extract_yaml_from_response')
    try:
        yaml_content = re.search(r'```yaml(.*?)```', response, re.DOTALL).group(1)
        with open(formatted_yml_file, 'w') as file:
            file.write(yaml_content)
            logging.info(f'Output: {yaml_content}')
        return formatted_yml_file
    except Exception as e:
        logging.info(f"Error extracting YAML from response: {e}")

# Convert XML to YAML
def convertXmltoYaml(input_content, prompt_file, template):
    logging.info('llm_response:convertXmltoYaml')
    global is_first_time
    #print(f'is_first_time = {is_first_time}')
   # Extract just the file name from the path
    yml_output_filename = os.path.basename(template)
    logging.info(f'YAML output file name: {yml_output_filename}')

    # Define paths for the files
    formatted_yml = 'output/formatted_yaml.yml'
    intermidiate_yml = 'output/unformatted_topic_yaml/'+yml_output_filename
    
    # Check if the files exist, if not, create them
    for file in [formatted_yml, intermidiate_yml]:
        if not os.path.exists(file):
            print(f'{file} does not exist. Creating it.')
            with open(file, 'w') as f:
                pass  # Create an empty file

    prompt = generate_prompt(input_content, prompt_file)
    response = get_response_from_llm(prompt)

    #validate the generated YAML before appending it to the final topic YAML file
    reference_yaml_path = 'output/reference_yaml.yml'
    validate_and_fix_yaml(formatted_yml, reference_yaml_path)
    from indentation import indent_yaml
    formatted_yml = indent_yaml(extract_yaml_from_response(response, formatted_yml), formatted_yml)

    if formatted_yml:
        from merge_topic import concatenate_yaml_files
        if is_first_time:
            #print("This is the first time the code is being executed.")
            is_first_time = False
            concatenate_yaml_files(template, formatted_yml, intermidiate_yml)
            indent_yaml(intermidiate_yml, intermidiate_yml)
        else:
            #print("This is not the first time the code is being executed.")
            concatenate_yaml_files(intermidiate_yml, formatted_yml, intermidiate_yml)
            indent_yaml(intermidiate_yml, intermidiate_yml)
    else:
        print("Failed to extract YAML content from the response.")

   