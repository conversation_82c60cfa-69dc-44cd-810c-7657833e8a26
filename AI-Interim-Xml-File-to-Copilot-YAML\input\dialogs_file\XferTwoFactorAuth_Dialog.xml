<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="XferTwoFactorAuth_Dialog">
    <decision-state id="XT1000_CheckTFEnabled_DS">
      <session-mapping key="GlobalVars.twoFactorAuthNumberRetries" expr="0"/>
      <session-mapping key="GlobalVars.twoFactorHoldCounter" expr="0"/>
      <session-mapping key="GlobalVars.fromMultilineFallBack" value="false" type="Boolean"/>
      <if cond="((GlobalVars.care_enable_twofactorauth == true || GlobalVars.care_enable_twofactorauth == 'true'))">
        <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'attempted'"/>
        <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_attempted_incomplete')"/>
        <action next="XT1005_PlayTFTransition_PP"/>
        <elseif cond="((GlobalVars.twoFactorAuthEntryPoint == '228_esn') &amp;&amp;(GlobalVars.star228_enable_twofactorauth == true || GlobalVars.star228_enable_twofactorauth == 'true'))">
          <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'attempted'"/>
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_attempted_incomplete')"/>
          <action next="XT1005_PlayTFTransition_PP"/>
        </elseif>
        <elseif cond="((GlobalVars.twoFactorAuthEntryPoint == 'CARE_SE') &amp;&amp;(GlobalVars.care_enable_twofactorauth == true || GlobalVars.care_enable_twofactorauth == 'true'))">
          <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'attempted'"/>
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_attempted_incomplete')"/>
          <action next="XT1005_PlayTFTransition_PP"/>
        </elseif>
        <else>
          <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'disabled'"/>
          <action next="XT1525_GoToTransfer_SD"/>
        </else>
      </if>
    </decision-state>

    <play-state id="XT1005_PlayTFTransition_PP">
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="switchLinesSuccess" value="GlobalVars.switchLinesSuccess" type="String"/>
      <session-mapping key="usingOldSIMForSwap" value="GlobalVars.usingOldSIMForSwap" type="String"/>
      <session-mapping key="switchLinesMDNMatch" value="GlobalVars.switchLinesMDNMatch" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <audio>
        <prompt id="XT1005_out_01">
          <prompt-segments>
            <audiofile text="First, for added security, I'm going to text you a one time security code" src="XT1005_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="(aniMatch == true &amp;&amp; switchLinesSuccess != true &amp;&amp; switchLinesMDNMatch != true)">
          <prompt id="XT1005_out_02">
            <prompt-segments>
              <audiofile text="I'll send it to the phone you're calling from" src="XT1005_out_02.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="XT1005_out_03">
              <prompt-segments>
                <audiofile text="I'll send it to the phone that's *currently* on" src="XT1005_out_03.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="digits" expr="mdn">
              <param name="intonation" value="f"/>
            </prompt>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="XT1005_out_04">
              <prompt-segments>
                <audiofile text="So please make sure you have it handy, that it's turned on, AND able to get text messages" src="XT1005_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <if cond="(usingOldSIMForSwap  == true &amp;&amp; aniMatch == false)">
          <prompt id="XT1005_out_05">
            <prompt-segments>
              <audiofile text="That's why your SIM card needs to stay in that phone for now " src="XT1005_out_05.wav"/>
            </prompt-segments>
          </prompt>
        </if>
      </audio>
      <action next="XT1010_CheckNeedConfirmPhone_JDA"/>
    </play-state>

    <decision-state id="XT1010_CheckNeedConfirmPhone_DS">
      <if cond="((GlobalVars.aniMatch == true) &amp;&amp;(GlobalVars.switchLinesSuccess == false))">
        <action next="XT1205_PlaySendingTempCode_PP"/>
        <else>
          <action next="XT1105_ConfirmPhoneReadyYN_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="XT1105_ConfirmPhoneReadyYN_DM" type="CUST">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <success>
        <action label="yes">
          <action next="XT1205_PlaySendingTempCode_PP"/>
        </action>
        <action label="no">
          <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'no_phone'"/>
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_nophone')"/>
          <audio>
            <prompt id="XT1105_out_01">
              <prompt-segments>
                <audiofile text="All Right" src="XT1105_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="XT1113_CheckMultiline_JDA"/>
        </action>
        <action label="wait">
          <audio>
            <prompt id="XT1105_out_02">
              <prompt-segments>
                <audiofile text="Sure " src="XT1105_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="XT1110_WaitPhoneReadySBI_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
        <session-mapping key="GlobalVars.isOTPMDE" value="true" type="Boolean"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="XT1105_ini_01">
                <prompt-segments>
                  <audiofile text="Do you have that phone ready" src="XT1105_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="XT1105_ConfirmPhoneReadyYN_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="XT1105_rin_01">
                <prompt-segments>
                  <audiofile text="I'll send an authentication code to the phone that's currently on" src="XT1105_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="mdn">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="XT1105_rin_02">
                <prompt-segments>
                  <audiofile text="Do you have that phone ready? Or, if you need time to set it up, say 'gimme a minute' " src="XT1105_rin_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="XT1105_rin_01">
                <prompt-segments>
                  <audiofile text="I'll send an authentication code to the phone that's currently on" src="XT1105_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="mdn">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="XT1105_rin_02">
                <prompt-segments>
                  <audiofile text="Do you have that phone ready? Or, if you need time to set it up, say 'gimme a minute' " src="XT1105_rin_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="XT1105_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have your current phone available and ready to get text messages, say yes or press 1 If you can't get to it now, say 'no' or press 2 If you need some time to set it up, say 'gimme a minute' or press 3" src="XT1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="XT1105_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have your current phone available and ready to get text messages, say yes or press 1 If you can't get to it now, say 'no' or press 2 If you need some time to set it up, say 'gimme a minute' or press 3" src="XT1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1105_rin_01">
                <prompt-segments>
                  <audiofile text="I'll send an authentication code to the phone that's currently on" src="XT1105_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="mdn">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="XT1105_rin_02">
                <prompt-segments>
                  <audiofile text="Do you have that phone ready? Or, if you need time to set it up, say 'gimme a minute' " src="XT1105_rin_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1105_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have your current phone available and ready to get text messages, say yes or press 1 If you can't get to it now, say 'no' or press 2 If you need some time to set it up, say 'gimme a minute' or press 3" src="XT1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1105_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have your current phone available and ready to get text messages, say yes or press 1 If you can't get to it now, say 'no' or press 2 If you need some time to set it up, say 'gimme a minute' or press 3" src="XT1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="XT1105_rin_01">
                <prompt-segments>
                  <audiofile text="I'll send an authentication code to the phone that's currently on" src="XT1105_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="mdn">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="XT1105_rin_02">
                <prompt-segments>
                  <audiofile text="Do you have that phone ready? Or, if you need time to set it up, say 'gimme a minute' " src="XT1105_rin_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="XT1105_ConfirmPhoneReadyYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="XT1105_ConfirmPhoneReadyYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxrepeats="4" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="XT1110_WaitPhoneReadySBI_DM" type="CUST">
      <success>
        <action label="continue">
          <action next="XT1205_PlaySendingTempCode_PP"/>
        </action>
        <action label="cant_get">
          <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'no_phone'"/>
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_nophone')"/>
          <action next="XT1113_CheckMultiline_JDA"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
        <session-mapping key="GlobalVars.isOTPMDE" value="true" type="Boolean"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="XT1110_ini_01">
                <prompt-segments>
                  <audiofile text="I can wait a minute When your phone is ready to receive text messages, say 'continue' or press 1 At any time, say 'continue' or press 1 Or say 'I can't get it' or press 2 At any time, say 'continue' or press 1 Or say 'I can't get it' or press 2 When you have your phone ready, say 'continue' or press 1 If you don't think you can get it to work, say 'I can't get it' or press 2 I'll wait a little bit longer At any time, say 'continue' or press 1 Or say 'I can't get it' or press 2 If your phone is ready to get text messages, say 'continue' or press 1 Or say 'I can't get it' or press 2" src="XT1110_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="XT1110_WaitPhoneReadySBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="XT1110_WaitPhoneReadySBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="1500ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="1500ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="XT1114_CheckNoPhoneConfig_DS">
      <if cond="(GlobalVars.GetBCSParameters.care_transfer_twofactorauth_nophone == true || GlobalVars.GetBCSParameters.care_transfer_twofactorauth_nophone == 'true')">
        <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'no_phone_transfer'"/>
        <action next="getReturnLink()"/>
        <else>
          <action next="XT1115_PlayNoPhoneInfo_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="XT1115_PlayNoPhoneInfo_PP">
      <session-mapping key="fromMultilineFallBack" value="GlobalVars.fromMultilineFallBack" type="String"/>
      <audio>
        <if cond="(fromMultilineFallBack == true)">
          <prompt id="XT1115_out_01">
            <prompt-segments>
              <audiofile text="We *won't* be able to make the switch without sending you a security code" src="XT1115_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="XT1115_out_02">
              <prompt-segments>
                <audiofile text="We *won't* be able to make the switch without your *current phone*" src="XT1115_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="XT1115_out_03">
          <prompt-segments>
            <audiofile text="Our agents can't do it either So please come see us in-store! There's a map of our locations on metrobyt-mobilecom Or if you find that phone and can get it to work, call us right back! Thanks for calling Metro by T-Mobile Goodbye! " src="XT1115_out_03.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.skipGoodbye" value="true" type="Boolean"/>
      <action next="XT1117_GoToGoodbye_SD"/>
    </play-state>

    <play-state id="XT1205_PlaySendingTempCode_PP">
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="switchLinesSuccess" value="GlobalVars.switchLinesSuccess" type="String"/>
      <session-mapping key="fromMultilineFallBack" value="GlobalVars.fromMultilineFallBack" type="String"/>
      <audio>
        <if cond="(aniMatch == true &amp;&amp; switchLinesSuccess == false)">
          <prompt id="XT1205_out_01">
            <prompt-segments>
              <audiofile text="Texting you now" src="XT1205_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="(fromMultilineFallBack == true)">
            <prompt id="XT1205_out_02">
              <prompt-segments>
                <audiofile text="Great, sending that now! " src="XT1205_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="XT1205_out_03">
              <prompt-segments>
                <audiofile text="Okay, texting you now!" src="XT1205_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="XT1210_GenerateTempCode_DB_DA"/>
    </play-state>

    <data-access-state id="XT1210_GenerateTempCode_DB_DA">
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.fromMultilineFallBack?GlobalVars.OTPAltPhone:GlobalVars.mdn" type="String"/>
      <data-access id="GenerateTempCode" classname="com.nuance.metro.dataaccess.GenerateTempCode">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="JWTToken"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="mode"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GenerateTempCode.status == 'Success'">
          <session-mapping key="GlobalVars.mode" expr="GenerateTempCode.mode"/>
          <action next="XT1313_AskReceivedCode_DM"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'db_fail'"/>
            <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_backend')"/>
            <action next="XT1525_GoToTransfer_SD"/>
          </else>
        </if>
      </action>
      </data-access-state>

    <play-state id="XT1310_WaitTempCodeSBI_PP">
      <session-mapping key="twoFactorHoldCounter" value="GlobalVars.twoFactorHoldCounter != undefined ? GlobalVars.twoFactorHoldCounter: 0 " type="String"/>
      <audio>
        <prompt id="XT1310_out_01" cond="twoFactorHoldCounter == 0 || twoFactorHoldCounter == 2 ">
          <prompt-segments>
            <audiofile text="A text from customer care is on its way" src="XT1310_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="XT1310_out_02" cond="!(twoFactorHoldCounter == 0 || twoFactorHoldCounter == 2) &amp;&amp; (twoFactorHoldCounter == 1 || twoFactorHoldCounter == 3)">
          <prompt-segments>
            <audiofile text="Let's give it a few more seconds" src="XT1310_out_02.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.twoFactorHoldCounter" expr="GlobalVars.twoFactorHoldCounter+1"/>
      <action next="XT1313_AskReceivedCode_DM"/>
    </play-state>

    <dm-state id="XT1313_AskReceivedCode_DM" type="YSNO">
      <session-mapping key="twoFactorHoldCounter" value="GlobalVars.twoFactorHoldCounter != undefined ? GlobalVars.twoFactorHoldCounter: 0" type="String"/>
      <success>
        <action label="true">
          <audio>
            <prompt id="XT1313_out_01">
              <prompt-segments>
                <audiofile text="Great!" src="XT1313_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="XT1311_CollectTempCode_DM"/>
        </action>
        <action label="false">
          <if cond="GlobalVars.twoFactorHoldCounter == 1 || GlobalVars.twoFactorHoldCounter == 3">
            <audio>
              <prompt id="XT1313_out_02">
                <prompt-segments>
                  <audiofile text="No problem" src="XT1313_out_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="XT1310_WaitTempCodeSBI_PP"/>
            <elseif cond="GlobalVars.twoFactorHoldCounter == 0 || GlobalVars.twoFactorHoldCounter == 2">
              <audio>
                <prompt id="XT1313_out_03">
                  <prompt-segments>
                    <audiofile text="Okay" src="XT1313_out_03.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="XT1310_WaitTempCodeSBI_PP"/>
            </elseif>
            <else>
              <action next="XT1315_PlayTFTimeout_PP"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="silence_250ms" cond="twoFactorHoldCounter == 0">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1313_ini_01" cond="twoFactorHoldCounter == 0 || twoFactorHoldCounter == 2 || twoFactorHoldCounter == 4">
                <prompt-segments>
                  <audiofile text="Have you received the text message? " src="XT1313_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1313_ini_02" cond="!(twoFactorHoldCounter == 0 || twoFactorHoldCounter == 2 || twoFactorHoldCounter == 4) &amp;&amp; (twoFactorHoldCounter == 1 || twoFactorHoldCounter == 3)">
                <prompt-segments>
                  <audiofile text="How about now did you receive the text? " src="XT1313_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="silence_250ms" cond="twoFactorHoldCounter == 0">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1313_ini_01" cond="twoFactorHoldCounter == 0 || twoFactorHoldCounter == 2 || twoFactorHoldCounter == 4">
                <prompt-segments>
                  <audiofile text="Have you received the text message? " src="XT1313_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1313_ini_02" cond="!(twoFactorHoldCounter == 0 || twoFactorHoldCounter == 2 || twoFactorHoldCounter == 4) &amp;&amp; (twoFactorHoldCounter == 1 || twoFactorHoldCounter == 3)">
                <prompt-segments>
                  <audiofile text="How about now did you receive the text? " src="XT1313_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="XT1313_ni1_01">
                <prompt-segments>
                  <audiofile text="Have you received the text message? " src="XT1313_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1313_ni2_01">
                <prompt-segments>
                  <audiofile text="Have you received the text message? " src="XT1313_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="XT1313_AskReceivedCode_DM_noinput_3"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1313_nm1_01">
                <prompt-segments>
                  <audiofile text="Have you received the text message? " src="XT1313_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1313_nm2_01">
                <prompt-segments>
                  <audiofile text="Have you received the text message? " src="XT1313_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="XT1313_AskReceivedCode_DM_nomatch_3"/>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="silence_250ms" cond="twoFactorHoldCounter == 0">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1313_ini_01" cond="twoFactorHoldCounter == 0 || twoFactorHoldCounter == 2 || twoFactorHoldCounter == 4">
                <prompt-segments>
                  <audiofile text="Have you received the text message? " src="XT1313_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1313_ini_02" cond="!(twoFactorHoldCounter == 0 || twoFactorHoldCounter == 2 || twoFactorHoldCounter == 4) &amp;&amp; (twoFactorHoldCounter == 1 || twoFactorHoldCounter == 3)">
                <prompt-segments>
                  <audiofile text="How about now did you receive the text? " src="XT1313_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="XT1313_AskReceivedCode_DM.grxml" count="1"/>
          <dtmfgrammars filename="XT1313_AskReceivedCode_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="XT1311_CollectTempCode_DM" type="CUST">
      <success>
        <action label="didnt_get">
          <audio>
            <prompt id="XT1311_out_01">
              <prompt-segments>
                <audiofile text="No worries Let me get you to someone who can help" src="XT1311_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'code_timeout'"/>
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_smstimeout')"/>
          <session-mapping key="GlobalVars.playTransferMessage" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.playTransferPrompt" value="false" type="Boolean"/>
          <action next="XT1525_GoToTransfer_SD"/>
        </action>
        <action label="default">
          <session-mapping key="tempCode" expr="XT1311_CollectTempCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.temporaryCodeORPin" expr="XT1311_CollectTempCode_DM.returnvalue"/>
          <action next="XT1405_ValidateTempCode_DB_DA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
        <session-mapping key="GlobalVars.isOTPMDE" value="true" type="Boolean"/>
      </catch>
      <collection_configuration reco_suppress_logs="true" highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="XT1311_ini_01">
                <prompt-segments>
                  <audiofile text="What's the 6-digit code? " src="XT1311_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="XT1311_ini_01">
                <prompt-segments>
                  <audiofile text="What's the 6-digit code? " src="XT1311_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="XT1311_ini_01">
                <prompt-segments>
                  <audiofile text="What's the 6-digit code? " src="XT1311_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="XT1311_nm2_01">
                <prompt-segments>
                  <audiofile text="What's the 6-digit code you received from Customer Care? Say it or enter it on your keypad Or say 'I didn't get it' or press 1 " src="XT1311_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="XT1311_nm2_01">
                <prompt-segments>
                  <audiofile text="What's the 6-digit code you received from Customer Care? Say it or enter it on your keypad Or say 'I didn't get it' or press 1 " src="XT1311_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1311_ini_01">
                <prompt-segments>
                  <audiofile text="What's the 6-digit code? " src="XT1311_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1311_nm2_01">
                <prompt-segments>
                  <audiofile text="What's the 6-digit code you received from Customer Care? Say it or enter it on your keypad Or say 'I didn't get it' or press 1 " src="XT1311_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1311_nm2_01">
                <prompt-segments>
                  <audiofile text="What's the 6-digit code you received from Customer Care? Say it or enter it on your keypad Or say 'I didn't get it' or press 1 " src="XT1311_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="XT1311_ini_01">
                <prompt-segments>
                  <audiofile text="What's the 6-digit code? " src="XT1311_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="XT1311_CollectTempCode_DM.grxml" count="1"/>
          <dtmfgrammars filename="XT1311_CollectTempCode_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="suppress" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="XT1311_CollectTempCode_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="XT1315_PlayTFTimeout_PP">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <audio>
        <prompt id="XT1315_out_01">
          <prompt-segments>
            <audiofile text="All right, looks like it s taking a little too long! Let me get you to someone who can help" src="XT1315_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'code_timeout'"/>
      <session-mapping key="GlobalVars.playTransferMessage" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.playTransferPrompt" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_smstimeout')"/>
      <action next="XT1525_GoToTransfer_SD"/>
    </play-state>

    <data-access-state id="XT1405_ValidateTempCode_DB_DA">
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.fromMultilineFallBack?GlobalVars.OTPAltPhone:GlobalVars.mdn" type="String"/>
      <session-mapping key="otpMode" value="GlobalVars.mode" type="String"/>
      <data-access id="ValidateTempCode" classname="com.nuance.metro.dataaccess.ValidateTempCode">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="tempCode" mask="true"/>
          <input-variable name="JWTToken"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="accessToken"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="ValidateTempCode.status == 'Failure'">
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'db_fail'"/>
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_backend')"/>
          <action next="XT1525_GoToTransfer_SD"/>
          <elseif cond="ValidateTempCode.acctLocked  != undefined &amp;&amp; ValidateTempCode.acctLocked == true">
            <session-mapping key="GlobalVars.acctLocked" expr="ValidateTempCode.acctLocked"/>
            <action next="XT1505_CheckNumErrors_JDA"/>
          </elseif>
          <elseif cond="ValidateTempCode.status == 'INVALID_OTP'">
            <session-mapping key="GlobalVars.TF1405Status" expr="'INVALID_OTP'"/>
            <session-mapping key="GlobalVars.validateTempCodeStatus" expr="ValidateTempCode.status"/>
            <action next="XT1410_CheckTempCodeValid_JDA"/>
          </elseif>
          <else>
            <session-mapping key="GlobalVars.ValidateTempCode" expr="ValidateTempCode"/>
            <session-mapping key="GlobalVars.validateTempCodeStatus" expr="ValidateTempCode.status"/>
            <session-mapping key="GlobalVars.accountAuthenticationToken" expr="ValidateTempCode.accountAuthenticationToken"/>
            <session-mapping key="GlobalVars.accessToken" expr="ValidateTempCode.accountAuthenticationToken"/>
            <action next="XT1410_CheckTempCodeValid_JDA"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="XT1410_CheckTempCodeValid_DS">
      <if cond="GlobalVars.validateTempCodeStatus == 'Success'">
        <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'success'"/>
        <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_success')"/>
        <action next="XT1415_CheckNumberAttempts_JDA"/>
        <else>
          <action next="XT1505_CheckNumErrors_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="XT1415_CheckNumberAttempts_DS">
      <if cond="GlobalVars.twoFactorAuthNumberRetries == 0">
        <action next="XT1420_MetricsTFASuccessFirstTry_JDA"/>
        <else>
          <action next="XT1425_PlayThanks_PP"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="XT1420_MetricsTFASuccessFirstTry_DS">
      <action next="XT1425_PlayThanks_PP"/>
    </decision-state>

    <play-state id="XT1425_PlayThanks_PP">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <audio>
        <if type="java">
          <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
            <param name="numToMatch" value="1"/>
            <param name="startNum" value="1"/>
            <param name="endNum" value="3"/>
          </condition>
          <prompt id="TF1425_out_01">
            <prompt-segments>
              <audiofile text="Got it!" src="TF1425_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <elseif>
            <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
              <param name="numToMatch" value="2"/>
              <param name="startNum" value="1"/>
              <param name="endNum" value="3"/>
            </condition>
            <prompt id="XT1425_out_02">
              <prompt-segments>
                <audiofile text="That s the one!" src="XT1425_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="XT1425_out_03">
              <prompt-segments>
                <audiofile text="Bingo!" src="XT1425_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="getReturnLink()"/>
    </play-state>

    <decision-state id="XT1505_CheckNumErrors_DS">
      <if cond="GlobalVars.acctLocked == true">
        <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'max_retries'"/>
        <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_maxretries')"/>
        <action next="XT1520_PlayTFMaxRetries_PP"/>
        <elseif cond="GlobalVars.TF1405Status == 'INVALID_OTP'">
          <session-mapping key="GlobalVars.twoFactorAuthNumberRetries" expr="GlobalVars.twoFactorAuthNumberRetries+1"/>
          <action next="XT1510_PlayTFRetry_PP"/>
        </elseif>
        <else>
          <session-mapping key="GlobalVars.visitedTFRetry" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.visitedTFRetry_counter" expr="0"/>
          <action next="XT1510_PlayTFRetry_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="XT1510_PlayTFRetry_PP">
      <session-mapping key="visitedTFRetry" value="GlobalVars.visitedTFRetry != undefined ? GlobalVars.visitedTFRetry : false" type="String"/>
      <session-mapping key="visitedTFRetry_counter" value="GlobalVars.visitedTFRetry_counter != undefined ? GlobalVars.visitedTFRetry_counter: 0" type="String"/>
      <audio>
        <prompt id="XT1510_out_01" cond="visitedTFRetry_counter == 0">
          <prompt-segments>
            <audiofile text="That's not the one I have Look for a text message from Customer Care that says l 'Your Metro by T-Mobile single-use authentication code is" src="XT1510_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="XT1510_out_02" cond="visitedTFRetry_counter != 0">
          <prompt-segments>
            <audiofile text="That still is not correct" src="XT1510_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="XT1515_RetryTempCode_DM"/>
    </play-state>

    <dm-state id="XT1515_RetryTempCode_DM" type="CUST">
      <session-mapping key="twoFactorAuthNumberRetries" value="GlobalVars.twoFactorAuthNumberRetries" type="String"/>
      <success>
        <action label="didnt_get">
          <audio>
            <prompt id="XT1515_out_01">
              <prompt-segments>
                <audiofile text="No worries Let me get you to someone who can help" src="XT1515_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'code_timeout'"/>
          <session-mapping key="GlobalVars.playTransferMessage" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.playTransferPrompt" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_failed_smstimeout')"/>
          <action next="XT1525_GoToTransfer_SD"/>
        </action>
        <action label="default">
          <session-mapping key="tempCode" expr="XT1515_RetryTempCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.visitedTFRetry_counter" expr="GlobalVars.visitedTFRetry_counter+1"/>
          <action next="XT1405_ValidateTempCode_DB_DA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
        <session-mapping key="GlobalVars.isOTPMDE" value="true" type="Boolean"/>
      </catch>
      <collection_configuration reco_suppress_logs="true" highconfidencelevel="0.500">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="XT1515_ini_02">
                <prompt-segments>
                  <audiofile text="Try again, or say 'I didn't get it'" src="XT1515_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="XT1515_ini_02">
                <prompt-segments>
                  <audiofile text="Try again, or say 'I didn't get it'" src="XT1515_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="XT1515_ni1_01">
                <prompt-segments>
                  <audiofile text="What s the 6-digit code in the text message I just sent you? Or say  I didn t get it" src="XT1515_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="XT1515_ni2_01">
                <prompt-segments>
                  <audiofile text="Please enter the 6-digit code in the text message using your keypad If you didn t get it, press 1" src="XT1515_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="XT1515_nm2_01">
                <prompt-segments>
                  <audiofile text="Please enter the 6-digit code in the text message using your keypad If you didn t get it, press 1" src="XT1515_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1515_nm1_01">
                <prompt-segments>
                  <audiofile text="What s the 6-digit code in the text message I just sent you? Or say I didn t get it" src="XT1515_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1515_nm2_01">
                <prompt-segments>
                  <audiofile text="Please enter the 6-digit code in the text message using your keypad If you didn t get it, press 1" src="XT1515_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1515_nm2_01">
                <prompt-segments>
                  <audiofile text="Please enter the 6-digit code in the text message using your keypad If you didn t get it, press 1" src="XT1515_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="XT1515_ini_02">
                <prompt-segments>
                  <audiofile text="Try again, or say 'I didn't get it'" src="XT1515_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="XT1515_RetryTempCode_DM.grxml" count="1"/>
          <dtmfgrammars filename="XT1515_RetryTempCode_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dm_suppress_logs="true" tts_suppress_logs="suppress" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="XT1515_RetryTempCode_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="XT1520_PlayTFMaxRetries_PP">
      <audio>
        <prompt id="XT1520_out_01">
          <prompt-segments>
            <audiofile text="Sorry, I am unable to access your account" src="XT1520_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.playTransferMessage" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.playTransferPrompt" value="false" type="Boolean"/>
      <action next="XT1525_GoToTransfer_SD"/>
    </play-state>

    <subdialog-state id="XT1525_GoToTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="XT1525_GoToTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="XT1525_GoToTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <decision-state id="XT1113_CheckMultiline_DS">
      <if cond="GlobalVars.isOnFamilyPlan == true">
        <action next="XT1605_PlayMultilineFallback_PP"/>
        <else>
          <action next="XT1114_CheckNoPhoneConfig_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="XT1605_PlayMultilineFallback_PP">
      <audio>
        <prompt id="XT1605_out_01">
          <prompt-segments>
            <audiofile text="I can also send your authentication code to another line on your account  It'll only be good for a few minutes though, so you'll need to have that other phone nearby as well " src="XT1605_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.multilineFallBackAttempts" expr="0"/>
      <action next="XT1610_ChooseOtherLine_DM"/>
    </play-state>

    <dm-state id="XT1610_ChooseOtherLine_DM" type="CUST">
      <success>
        <action label="default" next="XT1615_CheckInOnAccount_JDA">
          <session-mapping key="GlobalVars.OTPAltPhone" expr="XT1610_ChooseOtherLine_DM.returnvalue"/>
        </action>
        <action label="cancel" next="XT1114_CheckNoPhoneConfig_JDA">
          <session-mapping key="GlobalVars.fromMultilineFallBack" value="true" type="Boolean"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
        <session-mapping key="GlobalVars.isOTPMDE" value="true" type="Boolean"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3" maxturns="4"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="XT1610_ini_01">
                <prompt-segments>
                  <audiofile text="Tell me the 10-digit Metro number I should text, or say cancel" src="XT1610_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="XT1610_ini_01">
                <prompt-segments>
                  <audiofile text="Tell me the 10-digit Metro number I should text, or say cancel" src="XT1610_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="XT1610_nm1_01">
                <prompt-segments>
                  <audiofile text="Starting with the area code, tell me another number on your account that I can text If none of the other phones are handy, say 'cancel'" src="XT1610_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="XT1610_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say or enter another 10-digit phone number from your account It you can't currently receive texts on any of them, say 'cancel' or press 1 " src="XT1610_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="XT1610_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say or enter another 10-digit phone number from your account It you can't currently receive texts on any of them, say 'cancel' or press 1 " src="XT1610_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1610_nm1_01">
                <prompt-segments>
                  <audiofile text="Starting with the area code, tell me another number on your account that I can text If none of the other phones are handy, say 'cancel'" src="XT1610_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1610_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say or enter another 10-digit phone number from your account It you can't currently receive texts on any of them, say 'cancel' or press 1 " src="XT1610_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XT1610_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say or enter another 10-digit phone number from your account It you can't currently receive texts on any of them, say 'cancel' or press 1 " src="XT1610_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="XT1610_ini_01">
                <prompt-segments>
                  <audiofile text="Tell me the 10-digit Metro number I should text, or say cancel" src="XT1610_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="XT1610_ChooseOtherLine_DM.grxml" count="1"/>
          <dtmfgrammars filename="XT1610_ChooseOtherLine_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="19000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" interdigittimeout="3000ms" termtimeout="200ms" timeout="19000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="XT1610_ChooseOtherLine_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="XT1615_CheckInOnAccount_DS">
      <session-mapping key="OTPAltPhone" value="GlobalVars.OTPAltPhone" type="String"/>
      <session-mapping key="matchCaseTrue" value="false" type="Boolean"/>
      <if cond="matchCaseTrue == true">
        <action next="XT1620_SetMultilineVars_JDA"/>
        <else>
          <session-mapping key="GlobalVars.multilineFallBackAttempts" expr="GlobalVars.multilineFallBackAttempts + 1"/>
          <action next="XT1616_PlayNotOnAccount_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="XT1616_PlayNotOnAccount_PP">
      <if cond="(GlobalVars.multilineFallBackAttempts == 1)">
        <audio>
          <prompt id="XT1616_out_01">
            <prompt-segments>
              <audiofile text="Sorry, I don't see that number on your account Let's try one more time " src="XT1616_out_01.wav"/>
            </prompt-segments>
          </prompt>
        </audio>
        <action next="XT1610_ChooseOtherLine_DM"/>
        <else>
          <audio>
            <prompt id="XT1616_out_02">
              <prompt-segments>
                <audiofile text="I'm still not finding that number on your account" src="XT1616_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.fromMultilineFallBack" value="true" type="Boolean"/>
          <action next="XT1114_CheckNoPhoneConfig_JDA"/>
        </else>
      </if>
    </play-state>

    <decision-state id="XT1620_SetMultilineVars_DS">
      <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'tfa_attempted_incomplete')"/>
      <session-mapping key="GlobalVars.twoFactorAuthOutcome" expr="'attempted'"/>
      <session-mapping key="GlobalVars.fromMultilineFallBack" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.MDN" expr="GlobalVars.OTPAltPhone"/>
      <action next="XT1205_PlaySendingTempCode_PP"/>
    </decision-state>

    <subdialog-state id="XT1117_GoToGoodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="XT1117_GoToGoodbye_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="XT1117_GoToGoodbye_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

  </dialog>
