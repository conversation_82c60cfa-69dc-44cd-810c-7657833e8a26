<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="BankCard_Details_Dialog">
    <decision-state id="BC1001_CheckContext_DS">
      <if cond="GlobalVars.paymentAmtMinMaxError == true">
        <action next="BC1401_CheckContext_JDA"/>
        <elseif cond="GlobalVars.tryOtherCardReason == 'reenter_zip'">
          <action next="BC1401_CheckContext_JDA"/>
        </elseif>
        <elseif cond="GlobalVars.payingWithEWallet == true">
          <action next="BC1401_CheckContext_JDA"/>
        </elseif>
        <elseif cond="GlobalVars.authorizationFailureHandling == 'sameAmountDeclined'">
          <action next="BC1401_CheckContext_JDA"/>
        </elseif>
        <else>
          <action next="BC1005_GetCardNumber_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="BC1005_GetCardNumber_DM" type="DIGT">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorBC1005?GlobalVars.saidOperatorBC1005:false" type="String"/>
      <session-mapping key="preferredPaymentMethod" value="GlobalVars.preferredPaymentMethod" type="String"/>
      <session-mapping key="failedChecksum" value="GlobalVars.failedChecksum" type="String"/>
      <session-mapping key="disconfirmedDetails" value="GlobalVars.disconfirmedDetails" type="String"/>
      <session-mapping key="correctAllDetails" value="GlobalVars.correctAllDetails" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <action label="default">
          <session-mapping key="GlobalVars.saidOperatorBC1005" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardNumber" expr="BC1005_GetCardNumber_DM.returnvalue"/>
          <if cond="GlobalVars.bankCardNumber.substring(0,2) == '34' || GlobalVars.bankCardNumber.substring(0,2) == '37'">
            <session-mapping key="GlobalVars.cardTypeAmex" value="true" type="Boolean"/>
            <else>
              <session-mapping key="GlobalVars.cardTypeAmex" value="false" type="Boolean"/>
              <if cond="GlobalVars.disconfirmedDetails == true &amp;&amp; GlobalVars.correctAllDetails == false">
                <audio>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                </audio>
              </if>
              <if cond="GlobalVars.bankCardNumber.substring(0,2) == '65' || GlobalVars.bankCardNumber.substring(0,4) == '6011'">
                <session-mapping key="GlobalVars.tryOtherCardReason" expr="'failed_checksum'"/>
                <session-mapping key="unsupportedCardIssuer" expr="'discover'"/>
                <action next="BC1015_UseOtherCardYN_DM"/>
                <else>
                  <audio>
                    <prompt id="BC1005_out_01">
                      <prompt-segments>
                        <audiofile text="Thanks" src="BC1005_out_01.wav"/>
                      </prompt-segments>
                    </prompt>
                  </audio>
                  <audio>
                    <prompt id="silence_500ms">
                      <prompt-segments>
                        <audiofile text="test" src="silence_500ms.wav"/>
                      </prompt-segments>
                    </prompt>
                  </audio>
                </else>
              </if>
            </else>
          </if>
          <action next="BC1010_VerifyChecksum_JDA"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperatorBC1005" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BC1005_ini_05">
                <prompt-segments>
                  <audiofile text="Now I ll get your card details and I ll read them back to you You can say or enter each of them If I get something wrong, you can say that s not right otherwise we ll move on" src="BC1005_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1005_ini_02" cond="preferredPaymentMethod == 'credit'">
                <prompt-segments>
                  <audiofile text="First, what's your credit card number?" src="BC1005_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1005_ini_03" cond="preferredPaymentMethod != 'credit'">
                <prompt-segments>
                  <audiofile text="First, what's your debit card number?" src="BC1005_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="BC1005_GetCardNumber_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="BC1005_GetCardNumber_DM_help"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="BC1005_rin_01" cond="preferredPaymentMethod == 'credit'">
                  <prompt-segments>
                    <audiofile text="Please enter your credit card number" src="BC1005_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="BC1005_rin_02" cond="preferredPaymentMethod != 'credit'">
                  <prompt-segments>
                    <audiofile text="Please enter your debit card number" src="BC1005_rin_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="failedChecksum == true">
                  <prompt id="BC1005_rin_03">
                    <prompt-segments>
                      <audiofile text="Just to be sure, please enter it one more time using your keypad" src="BC1005_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="disconfirmedDetails == true">
                  <prompt id="BC1005_rin_04" cond="correctAllDetails == true">
                    <prompt-segments>
                      <audiofile text="First, please enter your card number using your keypad" src="BC1005_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1005_rin_05" cond="correctAllDetails != true">
                    <prompt-segments>
                      <audiofile text="Please enter it again using your keypad" src="BC1005_rin_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="BC1005_rin_06">
                    <prompt-segments>
                      <audiofile text="Okay, I'll get your card details again, and then I'll read them back to you If I get something wrong, say 'that's not right'" src="BC1005_rin_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1005_ini_04">
                    <prompt-segments>
                      <audiofile text="First, please enter your card number" src="BC1005_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1005_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter your card number now, or say 'more info'" src="BC1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have your card ready, please enter the number using your keypad If you have more questions, say 'more info' or press star" src="BC1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1005_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter your card number now For more information, press star" src="BC1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1005_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter your card number now, or say 'more info'" src="BC1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have your card ready, please enter the number using your keypad If you have more questions, say 'more info' or press star" src="BC1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1005_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter your card number now For more information, press star" src="BC1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="BC1005_rin_01" cond="preferredPaymentMethod == 'credit'">
                  <prompt-segments>
                    <audiofile text="Please enter your credit card number" src="BC1005_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="BC1005_rin_02" cond="preferredPaymentMethod != 'credit'">
                  <prompt-segments>
                    <audiofile text="Please enter your debit card number" src="BC1005_rin_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="failedChecksum == true">
                  <prompt id="BC1005_rin_03">
                    <prompt-segments>
                      <audiofile text="Just to be sure, please enter it one more time using your keypad" src="BC1005_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="disconfirmedDetails == true">
                  <prompt id="BC1005_rin_04" cond="correctAllDetails == true">
                    <prompt-segments>
                      <audiofile text="First, please enter your card number using your keypad" src="BC1005_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1005_rin_05" cond="correctAllDetails != true">
                    <prompt-segments>
                      <audiofile text="Please enter it again using your keypad" src="BC1005_rin_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="BC1005_rin_06">
                    <prompt-segments>
                      <audiofile text="Okay, I'll get your card details again, and then I'll read them back to you If I get something wrong, say 'that's not right'" src="BC1005_rin_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1005_ini_04">
                    <prompt-segments>
                      <audiofile text="First, please enter your card number" src="BC1005_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="BC1005_GetCardNumber_DM.grxml" count="1"/>
          <dtmfgrammars filename="BC1005_GetCardNumber_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="25000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="2000ms" timeout="22000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="BC1010_VerifyChecksum_DS">
      <if cond="GlobalVars.tryOtherCardReason == 'failed_checksum'">
        <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
      </if>
      <if cond="passMod10Checksum(GlobalVars.bankCardNumber)">
        <session-mapping key="GlobalVars.failedChecksum" value="false" type="Boolean"/>
        <if cond="(GlobalVars.disconfirmedDetails == true) &amp;&amp; (GlobalVars.correctAllDetails == false)">
          <action next="BC1401_CheckContext_JDA"/>
          <else>
            <action next="BC1101_CheckContext_JDA"/>
          </else>
        </if>
        <else>
          <if cond="GlobalVars.failedChecksum != true">
            <session-mapping key="GlobalVars.failedChecksum" value="true" type="Boolean"/>
            <action next="BC1005_GetCardNumber_DM"/>
            <else>
              <action next="BC1015_UseOtherCardYN_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <dm-state id="BC1015_UseOtherCardYN_DM" type="YSNO">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorBC1015?GlobalVars.saidOperatorBC1015:false" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorBC1015" value="false" type="Boolean"/>
        <action label="true">
          <session-mapping key="GlobalVars.failedChecksum" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="'failed_checksum'"/>
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'not_set'"/>
          <session-mapping key="GlobalVars.disconfirmedDetails" value="false" type="Boolean"/>
          <audio>
            <prompt id="BC1015_out_01">
              <prompt-segments>
                <audiofile text="Alright" src="BC1015_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="false">
          <action next="BC1020_GoTo_ErrorHandling_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperatorBC1015" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BC1015_ini_01" cond="saidOperator == false">
                <prompt-segments>
                  <audiofile text="Actually" src="BC1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="unsupportedCardIssuer == 'discover'">
                <prompt id="BC1015_ini_04">
                  <prompt-segments>
                    <audiofile text="It looks like you re using a Discover card, and we don t take those right now We do take Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel " src="BC1015_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="BC1015_ini_05" cond="callType == 'activate'">
                  <prompt-segments>
                    <audiofile text="I wont be able to complete your actiation without a payment, so" src="BC1015_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="callType == 'activate'">
                  <prompt id="BC1015_rin_01">
                    <prompt-segments>
                      <audiofile text="I couldn't validate the card number you gave me And I wont be able to complete your actiation without a payment" src="BC1015_rin_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="BC1015_ini_03">
                    <prompt-segments>
                      <audiofile text="I couldn't validate the card number you gave me" src="BC1015_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1015_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to use a different card?" src="BC1015_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BC1015_ini_01" cond="saidOperator == false">
                <prompt-segments>
                  <audiofile text="Actually" src="BC1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="unsupportedCardIssuer == 'discover'">
                <prompt id="BC1015_ini_04">
                  <prompt-segments>
                    <audiofile text="It looks like you re using a Discover card, and we don t take those right now We do take Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel " src="BC1015_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="BC1015_ini_05" cond="callType == 'activate'">
                  <prompt-segments>
                    <audiofile text="I wont be able to complete your actiation without a payment, so" src="BC1015_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="callType == 'activate'">
                  <prompt id="BC1015_rin_01">
                    <prompt-segments>
                      <audiofile text="I couldn't validate the card number you gave me And I wont be able to complete your actiation without a payment" src="BC1015_rin_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="BC1015_ini_03">
                    <prompt-segments>
                      <audiofile text="I couldn't validate the card number you gave me" src="BC1015_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1015_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to use a different card?" src="BC1015_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1015_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to try with a different card?" src="BC1015_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1015_nm2_01">
                <prompt-segments>
                  <audiofile text="I couldn't validate the card number you gave me If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="BC1015_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1015_nm3_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2" src="BC1015_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1015_nm3_02" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your payment without a valid card If you have a different card you can use, press 1 Otherwise, press 2" src="BC1015_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1015_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to try with a different card?" src="BC1015_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1015_nm2_01">
                <prompt-segments>
                  <audiofile text="I couldn't validate the card number you gave me If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="BC1015_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1015_nm3_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2" src="BC1015_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1015_nm3_02" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your payment without a valid card If you have a different card you can use, press 1 Otherwise, press 2" src="BC1015_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BC1015_ini_01" cond="saidOperator == false">
                <prompt-segments>
                  <audiofile text="Actually" src="BC1015_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="unsupportedCardIssuer == 'discover'">
                <prompt id="BC1015_ini_04">
                  <prompt-segments>
                    <audiofile text="It looks like you re using a Discover card, and we don t take those right now We do take Visa, MasterCard, American Express, Star, NYCE, Pulse, and Accel " src="BC1015_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="BC1015_ini_05" cond="callType == 'activate'">
                  <prompt-segments>
                    <audiofile text="I wont be able to complete your actiation without a payment, so" src="BC1015_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="callType == 'activate'">
                  <prompt id="BC1015_rin_01">
                    <prompt-segments>
                      <audiofile text="I couldn't validate the card number you gave me And I wont be able to complete your actiation without a payment" src="BC1015_rin_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="BC1015_ini_03">
                    <prompt-segments>
                      <audiofile text="I couldn't validate the card number you gave me" src="BC1015_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1015_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to use a different card?" src="BC1015_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="BC1015_UseOtherCardYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="BC1015_UseOtherCardYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="BC1020_GoTo_ErrorHandling_SD">
      <gotodialog next="ErrorHandlingPayment_Dialog"/>
      <action next="BC1020_GoTo_ErrorHandling_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BC1020_GoTo_ErrorHandling_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="BC1101_CheckContext_DS">
      <action next="BC1105_GetExpirationDate_DM"/>
    </decision-state>

    <dm-state id="BC1105_GetExpirationDate_DM" type="DIGT">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorBC1105?GlobalVars.saidOperatorBC1105:false" type="String"/>
      <session-mapping key="implicitConfirmReject" value="GlobalVars.implicitConfirmReject" type="String"/>
      <session-mapping key="fromBC1120" value="GlobalVars.fromBC1120" type="String"/>
      <session-mapping key="disconfirmedDetails" value="GlobalVars.disconfirmedDetails" type="String"/>
      <session-mapping key="correctAllDetails" value="GlobalVars.correctAllDetails" type="String"/>
      <session-mapping key="tryOtherCardReason" value="GlobalVars.tryOtherCardReason" type="String"/>
      <success>
        <session-mapping key="GlobalVars.fromBC1120" expr="undefined"/>
        <action label="default">
          <session-mapping key="GlobalVars.saidOperatorBC1105" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.implicitConfirmReject" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardDate" expr="BC1105_GetExpirationDate_DM.returnvalue"/>
          <session-mapping key="GlobalVars.bankCardDateIsValid" expr="BC1105_GetExpirationDate_DM.nbestresults[0].interpretation.isValid"/>
          <action next="BC1110_CheckDateFuture_JDA"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperatorBC1105" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BC1105_ini_01">
                <prompt-segments>
                  <audiofile text="And the expiration date?" src="BC1105_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="BC1105_GetExpirationDate_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="BC1105_GetExpirationDate_DM_help"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="BC1105_rin_01">
                  <prompt-segments>
                    <audiofile text="Pleas enter your card's four-digit expiration date" src="BC1105_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="BC1105_rin_02" cond="implicitConfirmReject == true">
                  <prompt-segments>
                    <audiofile text="Please enter it again using your keypad" src="BC1105_rin_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="fromBC1120 == true">
                  <prompt id="BC1105_rin_03">
                    <prompt-segments>
                      <audiofile text="Okay Please enter the four-digit date again using your keypad" src="BC1105_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="disconfirmedDetails == true">
                  <prompt id="BC1105_rin_04" cond="correctAllDetails == true">
                    <prompt-segments>
                      <audiofile text="Next, enter the expiration date" src="BC1105_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1105_rin_05" cond="correctAllDetails != true">
                    <prompt-segments>
                      <audiofile text="Please enter it again using your keypad" src="BC1105_rin_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="BC1105_rin_06" cond="tryOtherCardReason == 'card_expired'">
                    <prompt-segments>
                      <audiofile text="And now the four-digit expiration date" src="BC1105_rin_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1105_rin_07" cond="tryOtherCardReason != 'card_expired'">
                    <prompt-segments>
                      <audiofile text="And the expiration date? " src="BC1105_rin_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1105_nm1_01">
                <prompt-segments>
                  <audiofile text="Please give me your four-digit expiration date, like 'oh five, seventeen' Or say 'more info'" src="BC1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1105_nm2_01">
                <prompt-segments>
                  <audiofile text="Please enter the four-digit expiration date on your card You can also say 'more info' or press star " src="BC1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1105_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter the four digits of the expiration date, including any zeros For more information, press star" src="BC1105_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1105_nm1_01">
                <prompt-segments>
                  <audiofile text="Please give me your four-digit expiration date, like 'oh five, seventeen' Or say 'more info'" src="BC1105_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1105_nm2_01">
                <prompt-segments>
                  <audiofile text="Please enter the four-digit expiration date on your card You can also say 'more info' or press star " src="BC1105_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1105_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter the four digits of the expiration date, including any zeros For more information, press star" src="BC1105_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="BC1105_rin_01">
                  <prompt-segments>
                    <audiofile text="Pleas enter your card's four-digit expiration date" src="BC1105_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="BC1105_rin_02" cond="implicitConfirmReject == true">
                  <prompt-segments>
                    <audiofile text="Please enter it again using your keypad" src="BC1105_rin_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="fromBC1120 == true">
                  <prompt id="BC1105_rin_03">
                    <prompt-segments>
                      <audiofile text="Okay Please enter the four-digit date again using your keypad" src="BC1105_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="disconfirmedDetails == true">
                  <prompt id="BC1105_rin_04" cond="correctAllDetails == true">
                    <prompt-segments>
                      <audiofile text="Next, enter the expiration date" src="BC1105_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1105_rin_05" cond="correctAllDetails != true">
                    <prompt-segments>
                      <audiofile text="Please enter it again using your keypad" src="BC1105_rin_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="BC1105_rin_06" cond="tryOtherCardReason == 'card_expired'">
                    <prompt-segments>
                      <audiofile text="And now the four-digit expiration date" src="BC1105_rin_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1105_rin_07" cond="tryOtherCardReason != 'card_expired'">
                    <prompt-segments>
                      <audiofile text="And the expiration date? " src="BC1105_rin_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="BC1105_GetExpirationDate_DM.grxml" count="1"/>
          <dtmfgrammars filename="BC1105_GetExpirationDate_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="7000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="2000ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="BC1110_CheckDateFuture_DS">
      <session-mapping key="GlobalVars.fromBC1110" value="true" type="Boolean"/>
      <if cond="GlobalVars.tryOtherCardReason == 'card_expired'">
        <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
      </if>
      <if cond="GlobalVars.bankCardDateIsValid == 1">
        <if cond="(GlobalVars.disconfirmedDetails == true) &amp;&amp; (GlobalVars.correctAllDetails == false)">
          <action next="BC1401_CheckContext_JDA"/>
          <else>
            <action next="BC1201_CheckContext_JDA"/>
          </else>
        </if>
        <else>
          <action next="BC1120_UseOtherCardYN_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="BC1120_UseOtherCardYN_DM" type="CUST">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperator" type="String"/>
      <session-mapping key="bankCardDate" value="GlobalVars.bankCardDate" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <session-mapping key="GlobalVars.saidOperator" value="false" type="Boolean"/>
        <action label="yes">
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="'card_expired'"/>
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'not_set'"/>
          <session-mapping key="GlobalVars.disconfirmedDetails" value="false" type="Boolean"/>
          <audio>
            <prompt id="BC1120_out_01">
              <prompt-segments>
                <audiofile text="Alright" src="BC1120_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="no">
          <action next="BC1125_GoTo_ErrorHandling_SD"/>
        </action>
        <action label="try_again">
          <session-mapping key="GlobalVars.fromBC1120" value="true" type="Boolean"/>
          <action next="BC1105_GetExpirationDate_DM"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperator" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BC1120_ini_01">
                <prompt-segments>
                  <audiofile text="I got " src="BC1120_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="bankCardDate">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1120_ini_03" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="That would mean your credit card is expired If I got it wrong, say 'try again' Otherwise, I wont be able to complete your activation without a payment Would you like to use a different card?" src="BC1120_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1120_ini_04" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="That would mean your card is expired If I got it wrong, say 'try again' Otherwise, I wont be able to complete your payment Would you like to use a different card?" src="BC1120_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BC1120_ini_01">
                <prompt-segments>
                  <audiofile text="I got " src="BC1120_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="bankCardDate">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1120_ini_03" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="That would mean your credit card is expired If I got it wrong, say 'try again' Otherwise, I wont be able to complete your activation without a payment Would you like to use a different card?" src="BC1120_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1120_ini_04" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="That would mean your card is expired If I got it wrong, say 'try again' Otherwise, I wont be able to complete your payment Would you like to use a different card?" src="BC1120_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1120_nm1_01">
                <prompt-segments>
                  <audiofile text="If I got your date wrong, say 'try again' Otherwise, would you like to try with a different card?" src="BC1120_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1120_nm2_01">
                <prompt-segments>
                  <audiofile text="This card seems to be expired If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="BC1120_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1120_nm3_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2" src="BC1120_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1120_nm3_02" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your payment without an active card If you have a different card you can use, press 1 Otherwise, press 2" src="BC1120_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1120_nm1_01">
                <prompt-segments>
                  <audiofile text="If I got your date wrong, say 'try again' Otherwise, would you like to try with a different card?" src="BC1120_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1120_nm2_01">
                <prompt-segments>
                  <audiofile text="This card seems to be expired If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="BC1120_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1120_nm3_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2" src="BC1120_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1120_nm3_02" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your payment without an active card If you have a different card you can use, press 1 Otherwise, press 2" src="BC1120_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BC1120_ini_01">
                <prompt-segments>
                  <audiofile text="I got " src="BC1120_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="bankCardDate">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1120_ini_03" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="That would mean your credit card is expired If I got it wrong, say 'try again' Otherwise, I wont be able to complete your activation without a payment Would you like to use a different card?" src="BC1120_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1120_ini_04" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="That would mean your card is expired If I got it wrong, say 'try again' Otherwise, I wont be able to complete your payment Would you like to use a different card?" src="BC1120_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="BC1120_UseOtherCardYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="BC1120_UseOtherCardYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms" termchar=""/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="BC1125_GoTo_ErrorHandling_SD">
      <gotodialog next="ErrorHandlingPayment_Dialog"/>
      <action next="BC1125_GoTo_ErrorHandling_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BC1125_GoTo_ErrorHandling_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="BC1201_CheckContext_DS">
      <action next="BC1205_GetVerificationCode_DM"/>
    </decision-state>

    <dm-state id="BC1205_GetVerificationCode_DM" type="DIGT">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorBC1205?GlobalVars.saidOperatorBC1205:false" type="String"/>
      <session-mapping key="preferredPaymentMethod" value="GlobalVars.preferredPaymentMethod" type="String"/>
      <session-mapping key="failedChecksum" value="GlobalVars.failedChecksum" type="String"/>
      <session-mapping key="disconfirmedDetails" value="GlobalVars.disconfirmedDetails" type="String"/>
      <session-mapping key="correctAllDetails" value="GlobalVars.correctAllDetails" type="String"/>
      <session-mapping key="bankCardDate" value="GlobalVars.bankCardDate" type="String"/>
      <session-mapping key="bankCardDateFirst2Digits" value="(bankCardDate!=null &amp;&amp; bankCardDate !=undefined)?bankCardDate.substring(0, 2):bankCardDate" type="String"/>
      <session-mapping key="bankCardDateLast2Digits" value="(bankCardDate!=null &amp;&amp; bankCardDate !=undefined)?bankCardDate.substring(2, 4):bankCardDate" type="String"/>
      <session-mapping key="fromBC1110" value="GlobalVars.fromBC1110" type="String"/>
      <session-mapping key="providedCVV" value="GlobalVars.providedCVV" type="String"/>
      <session-mapping key="cardTypeAmex" value="GlobalVars.cardTypeAmex ? GlobalVars.cardTypeAmex : false" type="String"/>
      <session-mapping key="implicitConfirmReject" value="GlobalVars.implicitConfirmReject" type="String"/>
      <session-mapping key="voiceOrDtmf" value="application.lastresult$.inputmode" type="String"/>
      <success>
        <session-mapping key="GlobalVars.fromBC1110" expr="undefined"/>
        <session-mapping key="GlobalVars.FromBC1205" value="true" type="Boolean"/>
        <session-mapping key="GlobalVars.saidOperatorBC1205" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.implicitConfirmReject" value="false" type="Boolean"/>
        <action label="thats_not_right">
          <session-mapping key="GlobalVars.implicitConfirmReject" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardDate " expr=""/>
          <action next="BC1105_GetExpirationDate_DM"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.providedCVV" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardCVV" expr="BC1205_GetVerificationCode_DM.returnvalue"/>
          <session-mapping key="GlobalVars.VerificationCode_voiceOrDtmf" expr="application.lastresult$.inputmode"/>
          <if cond="GlobalVars.disconfirmedDetails == true &amp;&amp; GlobalVars.correctAllDetails == false">
            <action next="BC1401_CheckContext_JDA"/>
            <else>
              <action next="BC1301_CheckContext_JDA"/>
            </else>
          </if>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperatorBC1205" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BC1205_ini_04" cond="fromBC1110 == true &amp;&amp; voiceOrDtmf == 'voice'">
                <prompt-segments>
                  <audiofile text="okay" src="BC1205_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="natural" expr="bankCardDateFirst2Digits" cond="fromBC1110 == true &amp;&amp; voiceOrDtmf == 'voice'">
                <param value="m" name="intonation"/>
                <param value="true" name="PlayZeroUnits"/>
              </prompt>
              <prompt type="natural" expr="bankCardDateLast2Digits" cond="fromBC1110 == true &amp;&amp; voiceOrDtmf == 'voice' ">
                <param value="f" name="intonation"/>
                <param value="true" name="PlayZeroUnits"/>
              </prompt>
              <prompt id="silence_2000ms" cond="fromBC1110 == true &amp;&amp; voiceOrDtmf == 'voice'">
                <prompt-segments>
                  <audiofile text="test" src="silence_2000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1205_ini_01" cond="providedCVV != true &amp;&amp; cardTypeAmex == true ">
                <prompt-segments>
                  <audiofile text="Now, the four-digit verification code" src="BC1205_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1205_ini_02" cond="providedCVV != true &amp;&amp; cardTypeAmex != true">
                <prompt-segments>
                  <audiofile text="Now, the three digit verification code on the back of the card" src="BC1205_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_2000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1205_ini_03">
                <prompt-segments>
                  <audiofile text="You can also say 'more info'" src="BC1205_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="BC1205_GetVerificationCode_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="BC1205_GetVerificationCode_DM_help"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="BC1205_rin_01" cond="cardTypeAmex == true">
                  <prompt-segments>
                    <audiofile text="Please enter the four-digit verification code, or say 'more info'" src="BC1205_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="BC1205_rin_02" cond="cardTypeAmex != true">
                  <prompt-segments>
                    <audiofile text="Please enter the three-digit verification code, or say 'more info'" src="BC1205_rin_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="implicitConfirmReject == true">
                  <prompt id="BC1205_rin_03">
                    <prompt-segments>
                      <audiofile text="Please enter it again using your keypad" src="BC1205_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="disconfirmedDetails == true">
                  <prompt id="BC1205_rin_04" cond="cardTypeAmex == true &amp;&amp; correctAllDetails == true">
                    <prompt-segments>
                      <audiofile text="Now, enter the four-digit verification code on the front of the card" src="BC1205_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1205_rin_05" cond="cardTypeAmex != true &amp;&amp; correctAllDetails == true">
                    <prompt-segments>
                      <audiofile text="Now, enter the three-digit verification code on the back of the card" src="BC1205_rin_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1205_rin_06" cond="correctAllDetails != true">
                    <prompt-segments>
                      <audiofile text="Please enter it again using your keypad" src="BC1205_rin_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="BC1205_rin_07" cond="cardTypeAmex == true">
                    <prompt-segments>
                      <audiofile text="Now, your four-digit verification code" src="BC1205_rin_07.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1205_rin_08" cond="cardTypeAmex != true">
                    <prompt-segments>
                      <audiofile text="Now, your three digit verification code" src="BC1205_rin_08.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_2000ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_2000ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1205_rin_09">
                    <prompt-segments>
                      <audiofile text="Or say 'more info'" src="BC1205_rin_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1205_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the verification code, including any zeros Or say 'more info'" src="BC1205_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1205_nm2_01" cond="cardTypeAmex == true">
                <prompt-segments>
                  <audiofile text="Please enter the four-digit verification code on the front of your card You can also say 'more info' or press star" src="BC1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1205_nm2_02" cond="cardTypeAmex != true">
                <prompt-segments>
                  <audiofile text="Please enter the three-digit verification code on the back of your card You can also say 'more info' or press star " src="BC1205_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1205_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter the verification code on your card For more information, press star" src="BC1205_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1205_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the verification code, including any zeros Or say 'more info'" src="BC1205_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1205_nm2_01" cond="cardTypeAmex == true">
                <prompt-segments>
                  <audiofile text="Please enter the four-digit verification code on the front of your card You can also say 'more info' or press star" src="BC1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1205_nm2_02" cond="cardTypeAmex != true">
                <prompt-segments>
                  <audiofile text="Please enter the three-digit verification code on the back of your card You can also say 'more info' or press star " src="BC1205_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1205_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter the verification code on your card For more information, press star" src="BC1205_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="BC1205_rin_01" cond="cardTypeAmex == true">
                  <prompt-segments>
                    <audiofile text="Please enter the four-digit verification code, or say 'more info'" src="BC1205_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="BC1205_rin_02" cond="cardTypeAmex != true">
                  <prompt-segments>
                    <audiofile text="Please enter the three-digit verification code, or say 'more info'" src="BC1205_rin_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="implicitConfirmReject == true">
                  <prompt id="BC1205_rin_03">
                    <prompt-segments>
                      <audiofile text="Please enter it again using your keypad" src="BC1205_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="disconfirmedDetails == true">
                  <prompt id="BC1205_rin_04" cond="cardTypeAmex == true &amp;&amp; correctAllDetails == true">
                    <prompt-segments>
                      <audiofile text="Now, enter the four-digit verification code on the front of the card" src="BC1205_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1205_rin_05" cond="cardTypeAmex != true &amp;&amp; correctAllDetails == true">
                    <prompt-segments>
                      <audiofile text="Now, enter the three-digit verification code on the back of the card" src="BC1205_rin_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1205_rin_06" cond="correctAllDetails != true">
                    <prompt-segments>
                      <audiofile text="Please enter it again using your keypad" src="BC1205_rin_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="BC1205_rin_07" cond="cardTypeAmex == true">
                    <prompt-segments>
                      <audiofile text="Now, your four-digit verification code" src="BC1205_rin_07.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1205_rin_08" cond="cardTypeAmex != true">
                    <prompt-segments>
                      <audiofile text="Now, your three digit verification code" src="BC1205_rin_08.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_2000ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_2000ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1205_rin_09">
                    <prompt-segments>
                      <audiofile text="Or say 'more info'" src="BC1205_rin_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="BC1205_GetVerificationCode_DM.jsp" count="1">
            <param name="cardTypeAmex" value="cardTypeAmexVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="BC1205_GetVerificationCode_DM_dtmf.jsp" count="1">
            <param name="cardTypeAmex" value="cardTypeAmexVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="7000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="2000ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="BC1301_CheckContext_DS">
      <if cond="GlobalVars.disconfirmedDetails == true">
        <if cond="GlobalVars.correctAllDetails == false">
          <action next="BC1310_GetBillingZipCode_DM"/>
          <else>
            <if cond="GlobalVars.useSameZipCode == 'no' || GlobalVars.useSameZipCode == undefined">
              <action next="BC1310_GetBillingZipCode_DM"/>
              <else>
                <action next="BC1305_UseSameAsAboveYN_DM"/>
              </else>
            </if>
          </else>
        </if>
        <else>
          <if cond="GlobalVars.callType == 'activate'">
            <action next="BC1305_UseSameAsAboveYN_DM"/>
            <else>
              <session-mapping key="GlobalVars.useSameZipCode" expr="'no'"/>
              <action next="BC1310_GetBillingZipCode_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <dm-state id="BC1305_UseSameAsAboveYN_DM" type="CUST">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorBC1305?GlobalVars.saidOperatorBC1305:false" type="String"/>
      <session-mapping key="providedZipCode" value="GlobalVars.providedZipCode" type="String"/>
      <session-mapping key="zipCode" value="GlobalVars.callerEnteredZipCode" type="String"/>
      <session-mapping key="bankCardCVV" value="GlobalVars.bankCardCVV" type="String"/>
      <session-mapping key="voiceOrDtmf" value="GlobalVars.VerificationCode_voiceOrDtmf" type="String"/>
      <session-mapping key="FromBC1205" value="GlobalVars.FromBC1205" type="String"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorBC1305" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.FromBC1205" expr="undefined"/>
        <session-mapping key="GlobalVars.MDEinBC1305" value="false" type="Boolean"/>
        <action label="yes">
          <session-mapping key="GlobalVars.useSameZipCode" expr="'yes'"/>
          <session-mapping key="GlobalVars.providedZipCode" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardZip" expr="GlobalVars.callerEnteredZipCode"/>
          <action next="BC1401_CheckContext_JDA"/>
        </action>
        <action label="no">
          <if cond="GlobalVars.disconfirmedDetails == true">
            <session-mapping key="GlobalVars.useSameZipCode" expr="'disconfirmed'"/>
            <else>
              <session-mapping key="GlobalVars.useSameZipCode" expr="'no'"/>
            </else>
          </if>
          <action next="BC1310_GetBillingZipCode_DM"/>
        </action>
        <action label="thats_not_right">
          <session-mapping key="GlobalVars.implicitConfirmReject" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
          <action next="BC1205_GetVerificationCode_DM"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.MDEinBC1305" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperatorBC1305" value="true" type="Boolean"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="FromBC1205 == true &amp;&amp; voiceOrDtmf == 'voice'">
                <prompt type="number" expr="bankCardCVV">
                  <param value="digits" name="speakAs"/>
                  <param value="true" name="PlayZeroUnits"/>
                  <param value="f" name="intonation"/>
                </prompt>
                <prompt id="silence_2000ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_2000ms.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="BC1305_ini_01" cond="providedZipCode == false">
                <prompt-segments>
                  <audiofile text="Last, is the billing zip code for this card the same zip code you gave me earlier?" src="BC1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_2000ms" cond="providedZipCode == false">
                <prompt-segments>
                  <audiofile text="test" src="silence_2000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1305_ini_02" cond="providedZipCode == false">
                <prompt-segments>
                  <audiofile text="Is it also" src="BC1305_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="zipCode" cond="providedZipCode == false">
                <param name="intonation" value="f"/>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="BC1305_UseSameAsAboveYN_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="BC1305_rin_01">
                  <prompt-segments>
                    <audiofile text="Is the billing zip code for this card" src="BC1305_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BC1305_rin_02">
                    <prompt-segments>
                      <audiofile text="And is the billing Zip code for this card" src="BC1305_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="digits" expr="zipCode">
                <param name="intonation" value="f"/>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1305_nm1_01">
                <prompt-segments>
                  <audiofile text="Is the billing zip code for this card" src="BC1305_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="zipCode">
                <param name="intonation" value="f"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1305_nm2_01">
                <prompt-segments>
                  <audiofile text="If the billing zip code for this card the same as the one you gave me earlier, press 1 To enter a different one, press 2" src="BC1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1305_nm3_01">
                <prompt-segments>
                  <audiofile text="Do you receive the bills for this card in the zip code" src="BC1305_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="zipCode">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1305_nm3_02">
                <prompt-segments>
                  <audiofile text="If yes, press 1 If not, press 2" src="BC1305_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1305_nm1_01">
                <prompt-segments>
                  <audiofile text="Is the billing zip code for this card" src="BC1305_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="zipCode">
                <param name="intonation" value="f"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1305_nm2_01">
                <prompt-segments>
                  <audiofile text="If the billing zip code for this card the same as the one you gave me earlier, press 1 To enter a different one, press 2" src="BC1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1305_nm3_01">
                <prompt-segments>
                  <audiofile text="Do you receive the bills for this card in the zip code" src="BC1305_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="zipCode">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1305_nm3_02">
                <prompt-segments>
                  <audiofile text="If yes, press 1 If not, press 2" src="BC1305_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="BC1305_rin_01">
                  <prompt-segments>
                    <audiofile text="Is the billing zip code for this card" src="BC1305_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BC1305_rin_02">
                    <prompt-segments>
                      <audiofile text="And is the billing Zip code for this card" src="BC1305_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="digits" expr="zipCode">
                <param name="intonation" value="f"/>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="BC1305_UseSameAsAboveYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="BC1305_UseSameAsAboveYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="BC1310_GetBillingZipCode_DM" type="DIGT">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorBC1310?GlobalVars.saidOperatorBC1310:false" type="String"/>
      <session-mapping key="disconfirmedDetails" value="GlobalVars.disconfirmedDetails" type="String"/>
      <session-mapping key="correctAllDetails" value="GlobalVars.correctAllDetails" type="String"/>
      <session-mapping key="useSameZipCode" value="GlobalVars.useSameZipCode" type="String"/>
      <session-mapping key="fromBC1320" value="GlobalVars.fromBC1320" type="String"/>
      <session-mapping key="bankCardCVV" value="GlobalVars.bankCardCVV" type="String"/>
      <session-mapping key="voiceOrDtmf" value="GlobalVars.VerificationCode_voiceOrDtmf" type="String"/>
      <session-mapping key="MDEinBC1305" value="GlobalVars.MDEinBC1305" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <session-mapping key="GlobalVars.fromBC1320" expr="undefined"/>
        <session-mapping key="GlobalVars.MDEinBC1305" expr="undefined"/>
        <session-mapping key="GlobalVars.saidOperatorBC1310" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.implicitConfirmReject" value="false" type="Boolean"/>
        <action label="more_info">
          <action next="BC1320_MoreInfoZipWaitSBI_DM"/>
        </action>
        <action label="thats_not_right">
          <session-mapping key="GlobalVars.implicitConfirmReject" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
          <action next="BC1205_GetVerificationCode_DM"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.bankCardZip" expr="BC1310_GetBillingZipCode_DM.returnvalue"/>
          <action next="BC1401_CheckContext_JDA"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperatorBC1310" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="MDEinBC1305 == true">
                <prompt id="BC1310_ini_04">
                  <prompt-segments>
                    <audiofile text="Please enter the billing zip code for this card" src="BC1310_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="disconfirmedDetails == true &amp;&amp; correctAllDetails == false">
                  <prompt id="BC1310_ini_05">
                    <prompt-segments>
                      <audiofile text="Please enter the correct one now" src="BC1310_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="callType != 'activate'">
                  <prompt type="number" expr="bankCardCVV" cond="voiceOrDtmf == 'voice'">
                    <param value="digits" name="speakAs"/>
                    <param value="true" name="PlayZeroUnits"/>
                    <param value="f" name="intonation"/>
                  </prompt>
                  <prompt id="silence_2000ms" cond="voiceOrDtmf == 'voice'">
                    <prompt-segments>
                      <audiofile text="test" src="silence_2000ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1310_ini_06">
                    <prompt-segments>
                      <audiofile text="Last, what s the zip code where you get the bill for this card? It doesn t have to be the same as your Metro  account " src="BC1310_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="useSameZipCode == 'disconfirmed'">
                  <prompt id="BC1310_ini_01">
                    <prompt-segments>
                      <audiofile text="Ok, then enter the correct one now" src="BC1310_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="BC1310_ini_02">
                    <prompt-segments>
                      <audiofile text="Ok, then go ahead and enter it now" src="BC1310_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_200ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_200ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1310_ini_03">
                <prompt-segments>
                  <audiofile text="Or say 'more info'" src="BC1310_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="BC1310_GetBillingZipCode_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="BC1310_rin_01">
                  <prompt-segments>
                    <audiofile text="Please enter the billing zip code for this card" src="BC1310_rin_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="fromBC1320 == true">
                  <prompt id="BC1310_rin_06">
                    <prompt-segments>
                      <audiofile text="So! what's your billing zip code?" src="BC1310_rin_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="disconfirmedDetails == true &amp;&amp; correctAllDetails == false &amp;&amp; useSameZipCode == 'no'">
                  <prompt id="BC1310_rin_02">
                    <prompt-segments>
                      <audiofile text="Please enter it again using your keypad" src="BC1310_rin_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="BC1310_rin_04">
                    <prompt-segments>
                      <audiofile text="And last, please enter the billing zip code for this card" src="BC1310_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_2000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_2000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1310_rin_07">
                <prompt-segments>
                  <audiofile text="Or say 'more info'" src="BC1310_rin_07.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1310_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the five-digit zip code where you receive the bills for this card Or say 'more info'" src="BC1310_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1310_nm2_01">
                <prompt-segments>
                  <audiofile text="Using your keypad, enter the five-digit zip code where you receive the bills for this card For help finding it, press star" src="BC1310_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1310_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter the zip code where your bank sends the bills and statements for this card, including any zeroes If you're not sure what it is, press star" src="BC1310_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1310_nm1_01">
                <prompt-segments>
                  <audiofile text="Please enter the five-digit zip code where you receive the bills for this card Or say 'more info'" src="BC1310_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1310_nm2_01">
                <prompt-segments>
                  <audiofile text="Using your keypad, enter the five-digit zip code where you receive the bills for this card For help finding it, press star" src="BC1310_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1310_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter the zip code where your bank sends the bills and statements for this card, including any zeroes If you're not sure what it is, press star" src="BC1310_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BC1310_rin_05">
                <prompt-segments>
                  <audiofile text="Please enter it again using your keypad" src="BC1310_rin_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="BC1310_GetBillingZipCode_DM.grxml" count="1"/>
          <dtmfgrammars filename="BC1310_GetBillingZipCode_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="8000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="2000ms" timeout="8000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="BC1310_GetBillingZipCode_DM_confirmation_reentry"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="BC1320_MoreInfoZipWaitSBI_DM" type="CUST">
      <success>
        <session-mapping key="GlobalVars.saidOperator" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.fromBC1320" value="true" type="Boolean"/>
        <action label="continue">
          <action next="BC1310_GetBillingZipCode_DM"/>
        </action>
        <action label="cant_find">
          <action next="BC1325_GoToErrorHandling_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperator" value="true" type="Boolean"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BC1320_ini_01">
                <prompt-segments>
                  <audiofile text="Sure, here's more information I need the zip code where you receive the bill for this credit card We'll need it to verify your identity with your bank If you're not sure what it is, you can find it on a recent bill I can wait while you look When you're ready, say 'continue'" src="BC1320_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1320_ini_02">
                <prompt-segments>
                  <audiofile text="When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your zip code, say 'Continue' or press 1, or say 'I can't find it' or press 2 If you're ready,  say 'Continue' or press 1, or say 'I can't find it' or press 2 You can say 'Continue' or press 1, or 'I can't find it' or press 2 Hmmm, I seem to be having some trouble" src="BC1320_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BC1320_ini_01">
                <prompt-segments>
                  <audiofile text="Sure, here's more information I need the zip code where you receive the bill for this credit card We'll need it to verify your identity with your bank If you're not sure what it is, you can find it on a recent bill I can wait while you look When you're ready, say 'continue'" src="BC1320_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1320_ini_02">
                <prompt-segments>
                  <audiofile text="When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your zip code, say 'Continue' or press 1, or say 'I can't find it' or press 2 If you're ready,  say 'Continue' or press 1, or say 'I can't find it' or press 2 You can say 'Continue' or press 1, or 'I can't find it' or press 2 Hmmm, I seem to be having some trouble" src="BC1320_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BC1320_ini_01">
                <prompt-segments>
                  <audiofile text="Sure, here's more information I need the zip code where you receive the bill for this credit card We'll need it to verify your identity with your bank If you're not sure what it is, you can find it on a recent bill I can wait while you look When you're ready, say 'continue'" src="BC1320_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1320_ini_02">
                <prompt-segments>
                  <audiofile text="When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2 At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your zip code, say 'Continue' or press 1, or say 'I can't find it' or press 2 If you're ready,  say 'Continue' or press 1, or say 'I can't find it' or press 2 You can say 'Continue' or press 1, or 'I can't find it' or press 2 Hmmm, I seem to be having some trouble" src="BC1320_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="BC1320_MoreInfoZipWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="BC1320_MoreInfoZipWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="0ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="BC1325_GoToErrorHandling_SD">
      <gotodialog next="ErrorHandlingPayment_Dialog"/>
      <action next="BC1325_GoToErrorHandling_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BC1325_GoToErrorHandling_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="BC1401_CheckContext_DS">
      <if cond="GlobalVars.tryOtherCardReason == 'fail_validation'">
        <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
        <else>
          <action next="BC1405_ValidateCardOptionsRest_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="BC1405_ValidateCardOptionsRest_DB_DA">
      <session-mapping key="paymentType" value="(GlobalVars.callType == 'activate')?'activation':'service'" type="String"/>
      <session-mapping key="paymentMethod" value="GlobalVars.payingWithEWallet?'wallet':'card'" type="String"/>
      <session-mapping key="cardNumber" value="GlobalVars.bankCardNumber" type="String"/>
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount != undefined ? GlobalVars.paymentAmount : 0.0" type="String"/>
      <session-mapping key="expirationDate" value="GlobalVars.bankCardDate" type="String"/>
      <session-mapping key="securityCode" value="GlobalVars.bankCardCVV" type="String"/>
      <session-mapping key="billingZip" value="GlobalVars.bankCardZip" type="String"/>
      <session-mapping key="dueImmediately" value="(GlobalVars.callType == 'activate')?'0.00':GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="serviceAccountBalance" value="(GlobalVars.callType == 'activate')?'0.00':GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="ValidateCardOptionsREST" classname="com.nuance.metro.dataaccess.ValidateCardOptionsREST">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="paymentType"/>
          <input-variable name="paymentMethod"/>
          <input-variable name="cardNumber" mask="true"/>
          <input-variable name="paymentAmount"/>
          <input-variable name="expirationDate" mask="true"/>
          <input-variable name="securityCode" mask="true"/>
          <input-variable name="billingZip" mask="true"/>
          <input-variable name="dueImmediately"/>
          <input-variable name="serviceAccountBalance"/>
          <input-variable name="validatePaymentAmountOnly"/>
        </inputs>
        <outputs>
          <output-variable name="minLimit"/>
          <output-variable name="maxLimit"/>
          <output-variable name="cardStatus"/>
          <output-variable name="cardType"/>
          <output-variable name="cardBrand"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="ValidateCardOptionsREST.status == 'Success'">
          <session-mapping key="GlobalVars.ValidateCardOptions" expr="ValidateCardOptionsREST"/>
          <action next="BC1410_CheckValidationResults_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
            <action next="BC1430_CallTransfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="BC1410_CheckValidationResults_DS">
      <session-mapping key="GlobalVars.paymentAmtMinMaxError" value="false" type="Boolean"/>
      <if cond="GlobalVars.ValidateCardOptions.cardType == 'credit'">
        <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'credit'"/>
        <elseif cond="GlobalVars.ValidateCardOptions.cardType == 'debit'">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'debit'"/>
        </elseif>
      </if>
      <session-mapping key="GlobalVars.cardStatus" expr="GlobalVars.ValidateCardOptions ? GlobalVars.ValidateCardOptions.cardStatus : undefined"/>
      <if cond="(GlobalVars.ValidateCardOptions &amp;&amp; GlobalVars.ValidateCardOptions.cardStatus == 'amount_under_min') || (GlobalVars.ValidateCardOptions &amp;&amp; GlobalVars.ValidateCardOptions.cardStatus == 'amount_over_max')">
        <if cond="GlobalVars.callType != 'activate'">
          <session-mapping key="GlobalVars.paymentAmtMinMaxError" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
        </if>
        <else>
          <session-mapping key="GlobalVars.paymentAmtMinMaxError" value="false" type="Boolean"/>
          <if cond="(GlobalVars.ValidateCardOptions &amp;&amp; GlobalVars.ValidateCardOptions.cardStatus == 'invalid') &amp;&amp; GlobalVars.payingWithEWallet == true">
            <action next="BC1501_CheckContext_JDA"/>
            <else>
              <if cond="GlobalVars.ValidateCardOptions.cardType == 'both'">
                <action next="BC1435_AskCreditorDebit_DM"/>
                <else>
                  <action next="BC1415_ConfirmBankCardDetailsYN_DM"/>
                </else>
              </if>
            </else>
          </if>
        </else>
      </if>
      <action next="BC1415_ConfirmBankCardDetailsYN_DM"/>
    </decision-state>

    <dm-state id="BC1415_ConfirmBankCardDetailsYN_DM" type="YSNO">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorBC1415?GlobalVars.saidOperatorBC1415:false" type="String"/>
      <session-mapping key="cardStatus" value="GlobalVars.ValidateCardOptions.cardStatus" type="String"/>
      <session-mapping key="disconfirmedDetails" value="GlobalVars.disconfirmedDetails" type="String"/>
      <session-mapping key="ratePlanPrice" value="GlobalVars.selectedPlanPrice" type="String"/>
      <session-mapping key="bankCardDate" value="GlobalVars.bankCardDate" type="String"/>
      <session-mapping key="bankCardDateFirst2Digits" value="(bankCardDate!=null &amp;&amp; bankCardDate !=undefined)?bankCardDate.substring(0, 2):bankCardDate" type="String"/>
      <session-mapping key="bankCardDateLast2Digits" value="(bankCardDate!=null &amp;&amp; bankCardDate !=undefined)?bankCardDate.substring(2, 4):bankCardDate" type="String"/>
      <session-mapping key="bankCardCVV" value="GlobalVars.bankCardCVV" type="String"/>
      <session-mapping key="preferredPaymentMethod" value="GlobalVars.preferredPaymentMethod" type="String"/>
      <session-mapping key="bankCardZip" value="GlobalVars.bankCardZip" type="String"/>
      <session-mapping key="bankCardNumber" value="(GlobalVars.payingWithEWallet == true)?GlobalVars.lastFourDigitsOfCard:GlobalVars.bankCardNumber" type="String"/>
      <session-mapping key="bankCardNumlength" value="bankCardNumber.length" type="String"/>
      <session-mapping key="bankcardnumMinusfour" value="bankCardNumlength - 4" type="String"/>
      <session-mapping key="last4bankCardNumber" value="bankCardNumber.substring(bankcardnumMinusfour, bankCardNumlength)" type="String"/>
      <session-mapping key="last4bankCardNumberFirstTwoDigits" value="last4bankCardNumber.substring(0, 2)" type="String"/>
      <session-mapping key="last4bankCardNumberLastTwoDigits" value="last4bankCardNumber.substring(2, 4)" type="String"/>
      <session-mapping key="authorizationFailureHandling" value="GlobalVars.authorizationFailureHandling" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet" type="String"/>
      <session-mapping key="walletConvenienceFee" value="(GlobalVars.GetConvenienceFee)?GlobalVars.GetConvenienceFee.walletConvenienceFee : '0.00'" type="String"/>
      <session-mapping key="cardConvenienceFee" value="(GlobalVars.GetConvenienceFee)?GlobalVars.GetConvenienceFee.cardConvenienceFee : '0.00'" type="String"/>
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount" type="String"/>
      <session-mapping key="care_convenience_fees_threshold_amount" value="GlobalVars.GetBCSParameters.care_convenience_fees_threshold_amount" type="String"/>
      <session-mapping key="tryOtherCardReason" value="GlobalVars.tryOtherCardReason" type="String"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorBC1415" value="false" type="Boolean"/>
        <action label="true">
          <session-mapping key="GlobalVars.disconfirmedDetails" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.tryOtherCardReason " expr="undefined"/>
          <if cond="GlobalVars.ValidateCardOptions &amp;&amp; GlobalVars.ValidateCardOptions.cardStatus == 'invalid'">
            <session-mapping key="GlobalVars.bankCardNumber" expr="undefined"/>
            <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
            <session-mapping key="PaymentTable.CARD_TYPE" expr="'not_set'"/>
            <session-mapping key="GlobalVars.bankCardDate" expr="undefined"/>
            <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
            <session-mapping key="GlobalVars.bankCardZip" expr="undefined"/>
            <action next="BC1501_CheckContext_JDA"/>
            <else>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.disconfirmedDetails" value="true" type="Boolean"/>
          <action next="BC1420_BankCardCorrection_DM"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperatorBC1415" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BC1415_ini_02" cond="saidOperator == false &amp;&amp; (cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="actually" src="BC1415_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_03" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="I'm having trouble validating your card details Let me read them all back to you to make sure" src="BC1415_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_04" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="Your card number is" src="BC1415_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="bankCardNumber" cond="cardStatus != 'valid'">
                <param name="className" value="com.nuance.metro.audio.custom.PlayFullCreditCardNumber"/>
                <param name="variableName" value="bankCardNumber"/>
                <param name="variableScope" value="request"/>
              </prompt>
              <prompt id="silence_750ms" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_06" cond="(saidOperator == false) &amp;&amp; (payingWithEWallet == false) &amp;&amp; (!(cardStatus != 'valid'))">
                <prompt-segments>
                  <audiofile text="Got it" src="BC1415_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="saidOperator == false &amp;&amp; (!(cardStatus != 'valid'))">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_07" cond="((disconfirmedDetails == true || tryOtherCardReason == 'reenter_zip' || authorizationFailureHandling == 'sameAmountDeclined') &amp;&amp; (!(cardStatus != 'valid'))) ">
                <prompt-segments>
                  <audiofile text="To confirm again" src="BC1415_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_08" cond="(!(disconfirmedDetails == true || tryOtherCardReason == 'reenter_zip' || authorizationFailureHandling == 'sameAmountDeclined'))  &amp;&amp; (!(cardStatus != 'valid'))">
                <prompt-segments>
                  <audiofile text="Just to confirm" src="BC1415_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_09" cond="callType == 'activate' &amp;&amp; paymentAmount == 35 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro  to charge 35" src="BC1415_ini_09.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_10" cond="callType == 'activate' &amp;&amp; paymentAmount == 40 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 40" src="BC1415_ini_10.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_11" cond="callType == 'activate' &amp;&amp; paymentAmount == 45 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 45" src="BC1415_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_12" cond="callType == 'activate' &amp;&amp; paymentAmount == 50 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 50" src="BC1415_ini_12.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_13" cond="callType == 'activate' &amp;&amp; paymentAmount == 55 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 55" src="BC1415_ini_13.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_14" cond="callType == 'activate' &amp;&amp; paymentAmount == 60 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 60" src="BC1415_ini_14.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_15" cond="callType == 'activate' &amp;&amp; paymentAmount == 65 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 65" src="BC1415_ini_15.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_16" cond="callType == 'activate' &amp;&amp; paymentAmount == 70 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 70" src="BC1415_ini_16.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_17" cond="callType == 'activate' &amp;&amp; paymentAmount == 75 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 75" src="BC1415_ini_17.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_18" cond="callType == 'activate' &amp;&amp; paymentAmount == 80 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 80" src="BC1415_ini_18.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_19" cond="callType == 'activate' &amp;&amp; paymentAmount == 85 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 85" src="BC1415_ini_19.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_20" cond="callType == 'activate' &amp;&amp; paymentAmount == 90 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 90" src="BC1415_ini_20.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_21" cond="!(cardStatus != 'valid') &amp;&amp; callType == 'activate' &amp;&amp; (!((paymentAmount== 35 ) || (paymentAmount == 40 )|| (paymentAmount == 45 )|| (paymentAmount == 50 )|| (paymentAmount == 55 ) || (paymentAmount == 60 )|| (paymentAmount == 65 ) || (paymentAmount == 70 )|| (paymentAmount == 75 ) || (paymentAmount == 80 )|| (paymentAmount == 85 ) || (paymentAmount == 90 )))">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge" src="BC1415_ini_21.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="paymentAmount" cond="!(cardStatus != 'valid') &amp;&amp; callType == 'activate' &amp;&amp; (!((paymentAmount== 35 ) || (paymentAmount == 40 )|| (paymentAmount == 45 )|| (paymentAmount == 50 )|| (paymentAmount == 55 ) || (paymentAmount == 60 )|| (paymentAmount == 65 ) || (paymentAmount == 70 )|| (paymentAmount == 75 ) || (paymentAmount == 80 )|| (paymentAmount == 85 ) || (paymentAmount == 90 )))">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="BC1415_ini_30" cond="callType != 'activate' &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge" src="BC1415_ini_30.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="paymentAmount" cond="callType != 'activate' &amp;&amp; !(cardStatus != 'valid')">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="BC1415_ini_31" cond="(parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee != 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee != 0))">
                <prompt-segments>
                  <audiofile text="plus a service fee of" src="BC1415_ini_31.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="walletConvenienceFee" cond="((parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee != 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee != 0)) &amp;&amp; (payingWithEWallet == true))">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="cardConvenienceFee" cond="((parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee != 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee != 0)) &amp;&amp; (payingWithEWallet == false))">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_22" cond="preferredPaymentMethod == 'credit' &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="to your credit card ending in" src="BC1415_ini_22.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_23" cond="preferredPaymentMethod != 'credit' &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="to your debit card ending in" src="BC1415_ini_23.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="last4bankCardNumberFirstTwoDigits" cond="!(cardStatus != 'valid')">
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="silence_100ms" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="test" src="silence_100ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="last4bankCardNumberLastTwoDigits" cond="!(cardStatus != 'valid')">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_25" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="It expires on" src="BC1415_ini_25.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="natural" expr="bankCardDateFirst2Digits" cond="payingWithEWallet == false">
                <param value="m" name="intonation"/>
                <param value="true" name="PlayZeroUnits"/>
              </prompt>
              <prompt type="natural" expr="bankCardDateLast2Digits" cond="payingWithEWallet == false">
                <param value="f" name="intonation"/>
                <param value="true" name="PlayZeroUnits"/>
              </prompt>
              <prompt id="silence_500ms" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_26" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="The verification code is" src="BC1415_ini_26.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="number" expr="bankCardCVV" cond="payingWithEWallet == false">
                <param value="digits" name="speakAs"/>
                <param value="true" name="PlayZeroUnits"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_27" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="And the billing zip code is" src="BC1415_ini_27.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="bankCardZip" cond="payingWithEWallet == false">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_750ms" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_28" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="Are you sure that's correct" src="BC1415_ini_28.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_29" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="Is that correct" src="BC1415_ini_29.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BC1415_ini_02" cond="saidOperator == false &amp;&amp; (cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="actually" src="BC1415_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_03" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="I'm having trouble validating your card details Let me read them all back to you to make sure" src="BC1415_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_04" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="Your card number is" src="BC1415_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="bankCardNumber" cond="cardStatus != 'valid'">
                <param name="className" value="com.nuance.metro.audio.custom.PlayFullCreditCardNumber"/>
                <param name="variableName" value="bankCardNumber"/>
                <param name="variableScope" value="request"/>
              </prompt>
              <prompt id="silence_750ms" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_06" cond="(saidOperator == false) &amp;&amp; (payingWithEWallet == false) &amp;&amp; (!(cardStatus != 'valid'))">
                <prompt-segments>
                  <audiofile text="Got it" src="BC1415_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="saidOperator == false &amp;&amp; (!(cardStatus != 'valid'))">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_07" cond="((disconfirmedDetails == true || tryOtherCardReason == 'reenter_zip' || authorizationFailureHandling == 'sameAmountDeclined') &amp;&amp; (!(cardStatus != 'valid'))) ">
                <prompt-segments>
                  <audiofile text="To confirm again" src="BC1415_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_08" cond="(!(disconfirmedDetails == true || tryOtherCardReason == 'reenter_zip' || authorizationFailureHandling == 'sameAmountDeclined'))  &amp;&amp; (!(cardStatus != 'valid'))">
                <prompt-segments>
                  <audiofile text="Just to confirm" src="BC1415_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_09" cond="callType == 'activate' &amp;&amp; paymentAmount == 35 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro  to charge 35" src="BC1415_ini_09.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_10" cond="callType == 'activate' &amp;&amp; paymentAmount == 40 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 40" src="BC1415_ini_10.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_11" cond="callType == 'activate' &amp;&amp; paymentAmount == 45 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 45" src="BC1415_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_12" cond="callType == 'activate' &amp;&amp; paymentAmount == 50 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 50" src="BC1415_ini_12.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_13" cond="callType == 'activate' &amp;&amp; paymentAmount == 55 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 55" src="BC1415_ini_13.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_14" cond="callType == 'activate' &amp;&amp; paymentAmount == 60 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 60" src="BC1415_ini_14.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_15" cond="callType == 'activate' &amp;&amp; paymentAmount == 65 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 65" src="BC1415_ini_15.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_16" cond="callType == 'activate' &amp;&amp; paymentAmount == 70 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 70" src="BC1415_ini_16.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_17" cond="callType == 'activate' &amp;&amp; paymentAmount == 75 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 75" src="BC1415_ini_17.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_18" cond="callType == 'activate' &amp;&amp; paymentAmount == 80 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 80" src="BC1415_ini_18.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_19" cond="callType == 'activate' &amp;&amp; paymentAmount == 85 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 85" src="BC1415_ini_19.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_20" cond="callType == 'activate' &amp;&amp; paymentAmount == 90 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 90" src="BC1415_ini_20.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_21" cond="!(cardStatus != 'valid') &amp;&amp; callType == 'activate' &amp;&amp; (!((paymentAmount== 35 ) || (paymentAmount == 40 )|| (paymentAmount == 45 )|| (paymentAmount == 50 )|| (paymentAmount == 55 ) || (paymentAmount == 60 )|| (paymentAmount == 65 ) || (paymentAmount == 70 )|| (paymentAmount == 75 ) || (paymentAmount == 80 )|| (paymentAmount == 85 ) || (paymentAmount == 90 )))">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge" src="BC1415_ini_21.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="paymentAmount" cond="!(cardStatus != 'valid') &amp;&amp; callType == 'activate' &amp;&amp; (!((paymentAmount== 35 ) || (paymentAmount == 40 )|| (paymentAmount == 45 )|| (paymentAmount == 50 )|| (paymentAmount == 55 ) || (paymentAmount == 60 )|| (paymentAmount == 65 ) || (paymentAmount == 70 )|| (paymentAmount == 75 ) || (paymentAmount == 80 )|| (paymentAmount == 85 ) || (paymentAmount == 90 )))">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="BC1415_ini_30" cond="callType != 'activate' &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge" src="BC1415_ini_30.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="paymentAmount" cond="callType != 'activate' &amp;&amp; !(cardStatus != 'valid')">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="BC1415_ini_31" cond="(parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee != 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee != 0))">
                <prompt-segments>
                  <audiofile text="plus a service fee of" src="BC1415_ini_31.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="walletConvenienceFee" cond="((parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee != 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee != 0)) &amp;&amp; (payingWithEWallet == true))">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="cardConvenienceFee" cond="((parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee != 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee != 0)) &amp;&amp; (payingWithEWallet == false))">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_22" cond="preferredPaymentMethod == 'credit' &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="to your credit card ending in" src="BC1415_ini_22.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_23" cond="preferredPaymentMethod != 'credit' &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="to your debit card ending in" src="BC1415_ini_23.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="last4bankCardNumberFirstTwoDigits" cond="!(cardStatus != 'valid')">
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="silence_100ms" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="test" src="silence_100ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="last4bankCardNumberLastTwoDigits" cond="!(cardStatus != 'valid')">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_25" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="It expires on" src="BC1415_ini_25.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="natural" expr="bankCardDateFirst2Digits" cond="payingWithEWallet == false">
                <param value="m" name="intonation"/>
                <param value="true" name="PlayZeroUnits"/>
              </prompt>
              <prompt type="natural" expr="bankCardDateLast2Digits" cond="payingWithEWallet == false">
                <param value="f" name="intonation"/>
                <param value="true" name="PlayZeroUnits"/>
              </prompt>
              <prompt id="silence_500ms" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_26" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="The verification code is" src="BC1415_ini_26.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="number" expr="bankCardCVV" cond="payingWithEWallet == false">
                <param value="digits" name="speakAs"/>
                <param value="true" name="PlayZeroUnits"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_27" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="And the billing zip code is" src="BC1415_ini_27.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="bankCardZip" cond="payingWithEWallet == false">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_750ms" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_28" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="Are you sure that's correct" src="BC1415_ini_28.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_29" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="Is that correct" src="BC1415_ini_29.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1415_nm1_01">
                <prompt-segments>
                  <audiofile text="If your payment details were correct, press 1 Otherwise, press 2 To hear them again, press 7" src="BC1415_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1415_nm2_01">
                <prompt-segments>
                  <audiofile text="To confirm your card details and authorize the payment, press 1 To change your card details, press 2 To hear what I got, press 7" src="BC1415_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1415_nm3_01">
                <prompt-segments>
                  <audiofile text="I can't proceed with your payment until you confirm that the payment details were correct If they were, press 1 To modify them, press 2 To hear what I got again, press 7" src="BC1415_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_nm1_01">
                <prompt-segments>
                  <audiofile text="If your payment details were correct, press 1 Otherwise, press 2 To hear them again, press 7" src="BC1415_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_nm2_01">
                <prompt-segments>
                  <audiofile text="To confirm your card details and authorize the payment, press 1 To change your card details, press 2 To hear what I got, press 7" src="BC1415_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_nm3_01">
                <prompt-segments>
                  <audiofile text="I can't proceed with your payment until you confirm that the payment details were correct If they were, press 1 To modify them, press 2 To hear what I got again, press 7" src="BC1415_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BC1415_ini_02" cond="saidOperator == false &amp;&amp; (cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="actually" src="BC1415_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_03" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="I'm having trouble validating your card details Let me read them all back to you to make sure" src="BC1415_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_04" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="Your card number is" src="BC1415_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="bankCardNumber" cond="cardStatus != 'valid'">
                <param name="className" value="com.nuance.metro.audio.custom.PlayFullCreditCardNumber"/>
                <param name="variableName" value="bankCardNumber"/>
                <param name="variableScope" value="request"/>
              </prompt>
              <prompt id="silence_750ms" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_06" cond="(saidOperator == false) &amp;&amp; (payingWithEWallet == false) &amp;&amp; (!(cardStatus != 'valid'))">
                <prompt-segments>
                  <audiofile text="Got it" src="BC1415_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_250ms" cond="saidOperator == false &amp;&amp; (!(cardStatus != 'valid'))">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_07" cond="((disconfirmedDetails == true || tryOtherCardReason == 'reenter_zip' || authorizationFailureHandling == 'sameAmountDeclined') &amp;&amp; (!(cardStatus != 'valid'))) ">
                <prompt-segments>
                  <audiofile text="To confirm again" src="BC1415_ini_07.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_08" cond="(!(disconfirmedDetails == true || tryOtherCardReason == 'reenter_zip' || authorizationFailureHandling == 'sameAmountDeclined'))  &amp;&amp; (!(cardStatus != 'valid'))">
                <prompt-segments>
                  <audiofile text="Just to confirm" src="BC1415_ini_08.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_09" cond="callType == 'activate' &amp;&amp; paymentAmount == 35 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro  to charge 35" src="BC1415_ini_09.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_10" cond="callType == 'activate' &amp;&amp; paymentAmount == 40 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 40" src="BC1415_ini_10.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_11" cond="callType == 'activate' &amp;&amp; paymentAmount == 45 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 45" src="BC1415_ini_11.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_12" cond="callType == 'activate' &amp;&amp; paymentAmount == 50 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 50" src="BC1415_ini_12.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_13" cond="callType == 'activate' &amp;&amp; paymentAmount == 55 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 55" src="BC1415_ini_13.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_14" cond="callType == 'activate' &amp;&amp; paymentAmount == 60 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 60" src="BC1415_ini_14.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_15" cond="callType == 'activate' &amp;&amp; paymentAmount == 65 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 65" src="BC1415_ini_15.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_16" cond="callType == 'activate' &amp;&amp; paymentAmount == 70 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 70" src="BC1415_ini_16.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_17" cond="callType == 'activate' &amp;&amp; paymentAmount == 75 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 75" src="BC1415_ini_17.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_18" cond="callType == 'activate' &amp;&amp; paymentAmount == 80 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 80" src="BC1415_ini_18.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_19" cond="callType == 'activate' &amp;&amp; paymentAmount == 85 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 85" src="BC1415_ini_19.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_20" cond="callType == 'activate' &amp;&amp; paymentAmount == 90 &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge 90" src="BC1415_ini_20.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_21" cond="!(cardStatus != 'valid') &amp;&amp; callType == 'activate' &amp;&amp; (!((paymentAmount== 35 ) || (paymentAmount == 40 )|| (paymentAmount == 45 )|| (paymentAmount == 50 )|| (paymentAmount == 55 ) || (paymentAmount == 60 )|| (paymentAmount == 65 ) || (paymentAmount == 70 )|| (paymentAmount == 75 ) || (paymentAmount == 80 )|| (paymentAmount == 85 ) || (paymentAmount == 90 )))">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge" src="BC1415_ini_21.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="paymentAmount" cond="!(cardStatus != 'valid') &amp;&amp; callType == 'activate' &amp;&amp; (!((paymentAmount== 35 ) || (paymentAmount == 40 )|| (paymentAmount == 45 )|| (paymentAmount == 50 )|| (paymentAmount == 55 ) || (paymentAmount == 60 )|| (paymentAmount == 65 ) || (paymentAmount == 70 )|| (paymentAmount == 75 ) || (paymentAmount == 80 )|| (paymentAmount == 85 ) || (paymentAmount == 90 )))">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="BC1415_ini_30" cond="callType != 'activate' &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="you authorize Metro to charge" src="BC1415_ini_30.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="paymentAmount" cond="callType != 'activate' &amp;&amp; !(cardStatus != 'valid')">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="BC1415_ini_31" cond="(parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee != 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee != 0))">
                <prompt-segments>
                  <audiofile text="plus a service fee of" src="BC1415_ini_31.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="walletConvenienceFee" cond="((parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee != 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee != 0)) &amp;&amp; (payingWithEWallet == true))">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt type="currency" expr="cardConvenienceFee" cond="((parseFloat(paymentAmount) &gt; care_convenience_fees_threshold_amount) &amp;&amp; ((payingWithEWallet == true &amp;&amp; walletConvenienceFee != 0) || (payingWithEWallet == false &amp;&amp; cardConvenienceFee != 0)) &amp;&amp; (payingWithEWallet == false))">
                <param value="false" name="playZeroCents"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_22" cond="preferredPaymentMethod == 'credit' &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="to your credit card ending in" src="BC1415_ini_22.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_23" cond="preferredPaymentMethod != 'credit' &amp;&amp; !(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="to your debit card ending in" src="BC1415_ini_23.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="last4bankCardNumberFirstTwoDigits" cond="!(cardStatus != 'valid')">
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="silence_100ms" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="test" src="silence_100ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="last4bankCardNumberLastTwoDigits" cond="!(cardStatus != 'valid')">
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_25" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="It expires on" src="BC1415_ini_25.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="natural" expr="bankCardDateFirst2Digits" cond="payingWithEWallet == false">
                <param value="m" name="intonation"/>
                <param value="true" name="PlayZeroUnits"/>
              </prompt>
              <prompt type="natural" expr="bankCardDateLast2Digits" cond="payingWithEWallet == false">
                <param value="f" name="intonation"/>
                <param value="true" name="PlayZeroUnits"/>
              </prompt>
              <prompt id="silence_500ms" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_26" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="The verification code is" src="BC1415_ini_26.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="number" expr="bankCardCVV" cond="payingWithEWallet == false">
                <param value="digits" name="speakAs"/>
                <param value="true" name="PlayZeroUnits"/>
                <param value="f" name="intonation"/>
              </prompt>
              <prompt id="silence_500ms" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_27" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="And the billing zip code is" src="BC1415_ini_27.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="digits" expr="bankCardZip" cond="payingWithEWallet == false">
                <param name="intonation" value="f"/>
              </prompt>
              <prompt id="silence_750ms" cond="payingWithEWallet == false">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_28" cond="cardStatus != 'valid'">
                <prompt-segments>
                  <audiofile text="Are you sure that's correct" src="BC1415_ini_28.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1415_ini_29" cond="!(cardStatus != 'valid')">
                <prompt-segments>
                  <audiofile text="Is that correct" src="BC1415_ini_29.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="BC1415_ConfirmBankCardDetailsYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="BC1415_ConfirmBankCardDetailsYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never" dm_suppress_logs="true" tts_suppress_logs="true">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="BC1420_BankCardCorrection_DM" type="CUST">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperator" type="String"/>
      <session-mapping key="cardStatus" value="GlobalVars.ValidateCardOptions.cardStatus" type="String"/>
      <session-mapping key="tryOtherCardReason" value="GlobalVars.tryOtherCardReason" type="String"/>
      <session-mapping key="payingWithEWallet" value="(GlobalVars.payingWithEWallet)?GlobalVars.payingWithEWallet:false" type="String"/>
      <session-mapping key="gcallType" value="(GlobalVars.callType =='activate')?'active':'notActive'" type="String"/>
      <session-mapping key="FirstEntryBC1420" value="GlobalVars.FirstEntryBC1420 == false ? GlobalVars.FirstEntryBC1420 : true" type="String"/>
      <session-mapping key="eWalletAndCallType" value="payingWithEWallet + ',' + gcallType" type="String"/>
      <success>
        <session-mapping key="GlobalVars.FirstEntryBC1420" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.saidOperator" value="false" type="Boolean"/>
        <action label="cardnumber">
          <session-mapping key="GlobalVars.correctAllDetails" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardNumber" expr="undefined"/>
          <action next="BC1001_CheckContext_JDA"/>
        </action>
        <action label="date">
          <session-mapping key="GlobalVars.correctAllDetails" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardDate" expr="undefined"/>
          <action next="BC1101_CheckContext_JDA"/>
        </action>
        <action label="cvv">
          <session-mapping key="GlobalVars.correctAllDetails" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
          <action next="BC1201_CheckContext_JDA"/>
        </action>
        <action label="zipcode">
          <session-mapping key="GlobalVars.correctAllDetails" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardZip" expr="undefined"/>
          <action next="BC1301_CheckContext_JDA"/>
        </action>
        <action label="startover">
          <if cond="GlobalVars.payingWithEWallet == true">
            <session-mapping key="GlobalVars.payingWithEWallet" value="false" type="Boolean"/>
            <session-mapping key="GlobalVars.tryOtherCardReason" expr="'disconfirm_wallet'"/>
            <else>
              <session-mapping key="GlobalVars.tryOtherCardReason" expr="'startover'"/>
              <session-mapping key="GlobalVars.correctAllDetails" value="true" type="Boolean"/>
            </else>
          </if>
          <session-mapping key="GlobalVars.bankCardNumber" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardDate" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardZip" expr="undefined"/>
          <session-mapping key="GlobalVars.ValidateCardOptions.cardStatus" expr="undefined"/>
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
          <session-mapping key="GlobalVars.authorizationFailureHandling" expr="undefined"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="allfine">
          <session-mapping key="GlobalVars.disconfirmedDetails" value="false" type="Boolean"/>
          <if cond="GlobalVars.ValidateCardOptions &amp;&amp; GlobalVars.ValidateCardOptions.cardStatus == 'invalid'">
            <session-mapping key="GlobalVars.bankCardNumber" expr="undefined"/>
            <session-mapping key="GlobalVars.bankCardDate" expr="undefined"/>
            <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
            <session-mapping key="GlobalVars.bankCardZip" expr="undefined"/>
            <action next="BC1501_CheckContext_JDA"/>
            <else>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.FirstEntryBC1420" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperator" value="true" type="Boolean"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BC1420_ini_01" cond="FirstEntryBC1420 != false">
                <prompt-segments>
                  <audiofile text="alright" src="BC1420_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="payingWithEWallet == true &amp;&amp; tryOtherCardReason != 'fail_authorization'">
                <prompt id="BC1420_ini_05">
                  <prompt-segments>
                    <audiofile text="To pay with a different card, say start over Or to continue with the one you picked, say continue" src="BC1420_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BC1420_ini_02">
                    <prompt-segments>
                      <audiofile text="Which would you like to change  The card number  The expiration date  The verification code   Or the zip code" src="BC1420_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1420_ini_03" cond="cardStatus != 'valid' || tryOtherCardReason == 'fail_authorization'">
                    <prompt-segments>
                      <audiofile text="You can also say 'start over'" src="BC1420_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1420_ini_04" cond="!(cardStatus != 'valid' || tryOtherCardReason == 'fail_authorization')">
                    <prompt-segments>
                      <audiofile text="You can also say 'start over' or 'they're all fine'" src="BC1420_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BC1420_ini_01" cond="FirstEntryBC1420 != false">
                <prompt-segments>
                  <audiofile text="alright" src="BC1420_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="payingWithEWallet == true &amp;&amp; tryOtherCardReason != 'fail_authorization'">
                <prompt id="BC1420_ini_05">
                  <prompt-segments>
                    <audiofile text="To pay with a different card, say start over Or to continue with the one you picked, say continue" src="BC1420_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BC1420_ini_02">
                    <prompt-segments>
                      <audiofile text="Which would you like to change  The card number  The expiration date  The verification code   Or the zip code" src="BC1420_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1420_ini_03" cond="cardStatus != 'valid' || tryOtherCardReason == 'fail_authorization'">
                    <prompt-segments>
                      <audiofile text="You can also say 'start over'" src="BC1420_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1420_ini_04" cond="!(cardStatus != 'valid' || tryOtherCardReason == 'fail_authorization')">
                    <prompt-segments>
                      <audiofile text="You can also say 'start over' or 'they're all fine'" src="BC1420_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1420_nm1_02" cond="payingWithEWallet == true">
                <prompt-segments>
                  <audiofile text="To pay with a different card, say start over If you want to continue to pay with the card you picked, say continue" src="BC1420_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1420_nm1_01" cond="payingWithEWallet != true">
                <prompt-segments>
                  <audiofile text="Which do you want to change say 'card number', 'expiration date', 'verification code' or 'zip code' You can also say 'start over', or 'they're all fine'" src="BC1420_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1420_nm2_02" cond="payingWithEWallet == true">
                <prompt-segments>
                  <audiofile text="To start over with a different card, press 1 and I'll collect your payment information To authorize your payment from the card you picked, press 2" src="BC1420_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1420_nm2_01" cond="payingWithEWallet != true">
                <prompt-segments>
                  <audiofile text="To change your card number, press 1 For the expiration date, press 2 For the verification code - 3 For the zip code - 4 To re-enter all of them - 5 If they're all good, press 6 " src="BC1420_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1420_nm3_02" cond="payingWithEWallet == true">
                <prompt-segments>
                  <audiofile text="To start over with a different card, press 1 and I'll collect your payment information To authorize your payment from the card you picked, press 2" src="BC1420_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1420_nm3_01" cond="payingWithEWallet != true">
                <prompt-segments>
                  <audiofile text="To change your card number, press 1 For the expiration date, press 2 For the verification code - 3 For the zip code - 4 To re-enter all of them - 5 If they're all good, press 6" src="BC1420_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1420_nm1_02" cond="payingWithEWallet == true">
                <prompt-segments>
                  <audiofile text="To pay with a different card, say start over If you want to continue to pay with the card you picked, say continue" src="BC1420_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1420_nm1_01" cond="payingWithEWallet != true">
                <prompt-segments>
                  <audiofile text="Which do you want to change say 'card number', 'expiration date', 'verification code' or 'zip code' You can also say 'start over', or 'they're all fine'" src="BC1420_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1420_nm2_02" cond="payingWithEWallet == true">
                <prompt-segments>
                  <audiofile text="To start over with a different card, press 1 and I'll collect your payment information To authorize your payment from the card you picked, press 2" src="BC1420_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1420_nm2_01" cond="payingWithEWallet != true">
                <prompt-segments>
                  <audiofile text="To change your card number, press 1 For the expiration date, press 2 For the verification code - 3 For the zip code - 4 To re-enter all of them - 5 If they're all good, press 6 " src="BC1420_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1420_nm3_02" cond="payingWithEWallet == true">
                <prompt-segments>
                  <audiofile text="To start over with a different card, press 1 and I'll collect your payment information To authorize your payment from the card you picked, press 2" src="BC1420_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1420_nm3_01" cond="payingWithEWallet != true">
                <prompt-segments>
                  <audiofile text="To change your card number, press 1 For the expiration date, press 2 For the verification code - 3 For the zip code - 4 To re-enter all of them - 5 If they're all good, press 6" src="BC1420_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BC1420_ini_01" cond="FirstEntryBC1420 != false">
                <prompt-segments>
                  <audiofile text="alright" src="BC1420_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="payingWithEWallet == true &amp;&amp; tryOtherCardReason != 'fail_authorization'">
                <prompt id="BC1420_ini_05">
                  <prompt-segments>
                    <audiofile text="To pay with a different card, say start over Or to continue with the one you picked, say continue" src="BC1420_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BC1420_ini_02">
                    <prompt-segments>
                      <audiofile text="Which would you like to change  The card number  The expiration date  The verification code   Or the zip code" src="BC1420_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1420_ini_03" cond="cardStatus != 'valid' || tryOtherCardReason == 'fail_authorization'">
                    <prompt-segments>
                      <audiofile text="You can also say 'start over'" src="BC1420_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1420_ini_04" cond="!(cardStatus != 'valid' || tryOtherCardReason == 'fail_authorization')">
                    <prompt-segments>
                      <audiofile text="You can also say 'start over' or 'they're all fine'" src="BC1420_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="BC1420_BankCardCorrection_DM.jsp" count="1">
            <param name="eWalletAndCallType" value="eWalletAndCallTypeVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="BC1420_BankCardCorrection_DM_dtmf.jsp" count="1">
            <param name="eWalletAndCallType" value="eWalletAndCallTypeVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="7000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="BC1425_StartOverYN_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.correctAllDetails" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.bankCardNumber" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardDate" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardCVV" expr="undefined"/>
          <session-mapping key="GlobalVars.bankCardZip" expr="undefined"/>
          <action next="BC1001_CheckContext_JDA"/>
        </action>
        <action label="false">
          <action next="BC1430_CallTransfer_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperator" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BC1425_ini_01">
                <prompt-segments>
                  <audiofile text="We seem to be having some trouble I can't complete your activation without a payment on this call, and if we stop now, all the account information you gave me before will be lost So if you'd like to go back and re-enter your card details again, please press 1 Otherwise, press 2" src="BC1425_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BC1425_ini_01">
                <prompt-segments>
                  <audiofile text="We seem to be having some trouble I can't complete your activation without a payment on this call, and if we stop now, all the account information you gave me before will be lost So if you'd like to go back and re-enter your card details again, please press 1 Otherwise, press 2" src="BC1425_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1425_nm1_01">
                <prompt-segments>
                  <audiofile text="To start over with your details, press 1 Otherwise, press 2" src="BC1425_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1425_nm2_01">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment on this call To start over with your card details, press 1 Otherwise, press 2" src="BC1425_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1425_nm3_01">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment on this call, and if we stop now, all the account information you gave me before will be lost To start over with your card details, press 1 Otherwise, press 2" src="BC1425_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1425_nm1_01">
                <prompt-segments>
                  <audiofile text="To start over with your details, press 1 Otherwise, press 2" src="BC1425_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1425_nm2_01">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment on this call To start over with your card details, press 1 Otherwise, press 2" src="BC1425_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1425_nm3_01">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment on this call, and if we stop now, all the account information you gave me before will be lost To start over with your card details, press 1 Otherwise, press 2" src="BC1425_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BC1425_ini_01">
                <prompt-segments>
                  <audiofile text="We seem to be having some trouble I can't complete your activation without a payment on this call, and if we stop now, all the account information you gave me before will be lost So if you'd like to go back and re-enter your card details again, please press 1 Otherwise, press 2" src="BC1425_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="BC1425_StartOverYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="BC1425_StartOverYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="BC1435_AskCreditorDebit_DM" type="CUST">
      <session-mapping key="preferredPaymentMethod" value="GlobalVars.preferredPaymentMethod" type="String"/>
      <success>
        <action label="credit">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'credit'"/>
          <action next="BC1415_ConfirmBankCardDetailsYN_DM"/>
        </action>
        <action label="debit">
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="'debit'"/>
          <action next="BC1415_ConfirmBankCardDetailsYN_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BC1435_ini_01">
                <prompt-segments>
                  <audiofile text="How would you like to process your payment today? Please say 'credit' or 'debit'" src="BC1435_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="BC1435_AskCreditorDebit_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="BC1435_ini_01">
                <prompt-segments>
                  <audiofile text="How would you like to process your payment today? Please say 'credit' or 'debit'" src="BC1435_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1435_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say how you would like to process your payment credit or debit" src="BC1435_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1435_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'credit, or press 1, or say 'debit' or press 2" src="BC1435_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1435_nm3_01">
                <prompt-segments>
                  <audiofile text="To pay with credit, press 1 For debit, press 2" src="BC1435_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1435_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say how you would like to process your payment credit or debit" src="BC1435_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1435_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'credit, or press 1, or say 'debit' or press 2" src="BC1435_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1435_nm3_01">
                <prompt-segments>
                  <audiofile text="To pay with credit, press 1 For debit, press 2" src="BC1435_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BC1435_ini_01">
                <prompt-segments>
                  <audiofile text="How would you like to process your payment today? Please say 'credit' or 'debit'" src="BC1435_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="BC1435_AskCreditorDebit_DM.grxml" count="1"/>
          <dtmfgrammars filename="BC1435_AskCreditorDebit_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1435_AskCreditorDebit_DM_confirmation_noinput_1"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_05">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="BC1430_CallTransfer_SD">
      <if cond="GlobalVars.isCareTransfer == 'true'">
        <gotodialog next="CallTransfer_Main_Dialog"/>
        <else>
          <gotodialog next="Transfer_Main"/>
        </else>
      </if>
    </subdialog-state>
    <decision-state id="BC1501_CheckContext_DS">
      <action next="BC1505_UseDifferentCardYN_DM"/>
    </decision-state>

    <dm-state id="BC1505_UseDifferentCardYN_DM" type="YSNO">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="cardStatus" value="GlobalVars.ValidateCardOptions.cardStatus" type="String"/>
      <session-mapping key="payingWithEWallet" value="GlobalVars.payingWithEWallet" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="'fail_validation'"/>
          <session-mapping key="GlobalVars.preferredPaymentMethod" expr="undefined"/>
          <session-mapping key="PaymentTable.CARD_TYPE" expr="'not_set'"/>
          <session-mapping key="GlobalVars.disconfirmedDetails" value="false" type="Boolean"/>
          <audio>
            <prompt id="BC1505_out_01">
              <prompt-segments>
                <audiofile text="alright" src="BC1505_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="false">
          <action next="BC1510_GoTo_ErrorHandling_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperator" value="true" type="Boolean"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="callType == 'activate'">
                <prompt id="BC1505_ini_01">
                  <prompt-segments>
                    <audiofile text="I can't match the information you gave me information to a bank account,  and I wont be able to complete your activation without a payment" src="BC1505_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BC1505_ini_04" cond="cardStatus != 'valid' &amp;&amp; payingWithEWallet == true">
                    <prompt-segments>
                      <audiofile text="Actually, your card isn't going through You can verify the information you've entered for it in the myMetro app on your phone, or online at metrobyt-mobilecom For now" src="BC1505_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1505_ini_03" cond="!(cardStatus != 'valid' &amp;&amp; payingWithEWallet == true)">
                    <prompt-segments>
                      <audiofile text="I can't match the information you gave me to a bank account, and I wont be able to complete your payment" src="BC1505_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms" cond="!(cardStatus != 'valid' &amp;&amp; payingWithEWallet == true)">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="BC1505_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to use a different card?" src="BC1505_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="callType == 'activate'">
                <prompt id="BC1505_ini_01">
                  <prompt-segments>
                    <audiofile text="I can't match the information you gave me information to a bank account,  and I wont be able to complete your activation without a payment" src="BC1505_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BC1505_ini_04" cond="cardStatus != 'valid' &amp;&amp; payingWithEWallet == true">
                    <prompt-segments>
                      <audiofile text="Actually, your card isn't going through You can verify the information you've entered for it in the myMetro app on your phone, or online at metrobyt-mobilecom For now" src="BC1505_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1505_ini_03" cond="!(cardStatus != 'valid' &amp;&amp; payingWithEWallet == true)">
                    <prompt-segments>
                      <audiofile text="I can't match the information you gave me to a bank account, and I wont be able to complete your payment" src="BC1505_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms" cond="!(cardStatus != 'valid' &amp;&amp; payingWithEWallet == true)">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="BC1505_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to use a different card?" src="BC1505_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BC1505_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to try with a different card" src="BC1505_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BC1505_nm2_01">
                <prompt-segments>
                  <audiofile text="I couldn't validate the card details you gave me If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="BC1505_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BC1505_nm3_02" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2" src="BC1505_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1505_nm3_01" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your payment without a valid card If you have a different card you can use, press 1 Otherwise, press 2" src="BC1505_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1505_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to try with a different card" src="BC1505_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1505_nm2_01">
                <prompt-segments>
                  <audiofile text="I couldn't validate the card details you gave me If you have a different card you can use, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="BC1505_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1505_nm3_02" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your activation without a payment If you have a different card you can use, press 1 Otherwise, press 2" src="BC1505_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BC1505_nm3_01" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="I can't complete your payment without a valid card If you have a different card you can use, press 1 Otherwise, press 2" src="BC1505_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="callType == 'activate'">
                <prompt id="BC1505_ini_01">
                  <prompt-segments>
                    <audiofile text="I can't match the information you gave me information to a bank account,  and I wont be able to complete your activation without a payment" src="BC1505_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="BC1505_ini_04" cond="cardStatus != 'valid' &amp;&amp; payingWithEWallet == true">
                    <prompt-segments>
                      <audiofile text="Actually, your card isn't going through You can verify the information you've entered for it in the myMetro app on your phone, or online at metrobyt-mobilecom For now" src="BC1505_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="BC1505_ini_03" cond="!(cardStatus != 'valid' &amp;&amp; payingWithEWallet == true)">
                    <prompt-segments>
                      <audiofile text="I can't match the information you gave me to a bank account, and I wont be able to complete your payment" src="BC1505_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms" cond="!(cardStatus != 'valid' &amp;&amp; payingWithEWallet == true)">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="BC1505_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to use a different card?" src="BC1505_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="BC1505_UseDifferentCardYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="BC1505_UseDifferentCardYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="BC1510_GoTo_ErrorHandling_SD">
      <gotodialog next="ErrorHandlingPayment_Dialog"/>
      <action next="BC1510_GoTo_ErrorHandling_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="BC1510_GoTo_ErrorHandling_SD_return_CS">
      <session-mapping key="GlobalVars.paymentFailure" value="true" type="Boolean"/>
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  