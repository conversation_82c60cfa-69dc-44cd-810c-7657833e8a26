<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="AskSecurityQuestion_SendSMS_Dialog">
    <decision-state id="AQ1201_CheckCallType_DS">
      <session-mapping key="GlobalVars.smsZipCodeWrong" value="false" type="Boolean"/>
      <if cond="GlobalVars.callType == 'pair_sim' || GlobalVars.aniMatch == true ">
        <action next="AQ1205_GetAcctZip_DM"/>
        <else>
          <action next="AQ1305_HaveMetroPhoneYN_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="AQ1205_GetAcctZip_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorAQ1205?GlobalVars.saidOperatorAQ1205:false" type="String"/>
      <session-mapping key="askSQEntry" value="GlobalVars.askSQEntry" type="String"/>
      <session-mapping key="returnFromWait" value="GlobalVars.returnFromWait" type="String"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorAQ1205" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.returnFromWait" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.callerZipCode" expr="AQ1205_GetAcctZip_DM.returnvalue"/>
        <action label="wait">
          <action next="AQ1210_GetZipWaitSBI_DM"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.callerZipCode" expr="AQ1205_GetAcctZip_DM.returnvalue"/>
          <if cond="GlobalVars.callerZipCode == GlobalVars.GetAccountDetails.zipCode">
            <session-mapping key="GlobalVars.smsZipCodeWrong" value="false" type="Boolean"/>
            <action next="AQ1405_SMSTransitionPrompt_PP"/>
            <else>
              <if cond="GlobalVars.smsZipCodeWrong == false">
                <session-mapping key="GlobalVars.smsZipCodeWrong" value="true" type="Boolean"/>
                <action next="AQ1205_GetAcctZip_DM"/>
                <else>
                  <audio>
                    <prompt id="AQ1205_out_01">
                      <prompt-segments>
                        <audiofile text="I'm sorry, that's still not right" src="AQ1205_out_01.wav"/>
                      </prompt-segments>
                    </prompt>
                  </audio>
                  <session-mapping key="GlobalVars.authFailure" value="true" type="Boolean"/>
                  <action next="getReturnLink()"/>
                </else>
              </if>
            </else>
          </if>
        </action>
        <action label="operator">
          <if cond="GlobalVars.saidOperatorAQ1205 == false">
            <session-mapping key="GlobalVars.saidOperatorAQ1205" value="true" type="Boolean"/>
            <action next="AQ1205_GetAcctZip_DM"/>
            <else>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="aniMatch == true">
                <prompt id="AQ1205_ini_01" cond="askSQEntry == 'EWallet'">
                  <prompt-segments>
                    <audiofile text="I can text you the answer to your security questionIn order to send that, I ll need to verify the Zip code on your Metro PCS account It doesn t have to be the same as your bank card You can say or enter it now Or say wait a minute " src="AQ1205_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AQ1205_ini_04" cond="askSQEntry == 'careLogin'">
                  <prompt-segments>
                    <audiofile text="I can text you the answer to your security question In order to send that, I'll need to verify the Zip code on your Metro PCS account You can say or enter it now Or say wait a minute" src="AQ1205_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="AQ1205_ini_02" cond="askSQEntry != 'EWallet' &amp;&amp; askSQEntry != 'careLogin'">
                  <prompt-segments>
                    <audiofile text="In order to send that, I'll need to verify the Zip code on your account You can say or enter it now Or say 'wait a minute'" src="AQ1205_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="AQ1205_ini_03">
                    <prompt-segments>
                      <audiofile text="And last, I just need to verify the zip code on your account You can say or enter it now Or say 'wait a minute'" src="AQ1205_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AQ1205_GetAcctZip_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1205_rin_02">
                  <prompt-segments>
                    <audiofile text="I just need your Zip code before I send your message Please say or enter it now" src="AQ1205_rin_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="returnFromWait == true">
                  <prompt id="AQ1205_rin_03">
                    <prompt-segments>
                      <audiofile text="Alright What's your Zip code" src="AQ1205_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="AQ1205_rin_04">
                    <prompt-segments>
                      <audiofile text="I'm sorry, that's not the zip code I have on file for you Please enter it again using your keypad" src="AQ1205_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AQ1205_nm1_01">
                <prompt-segments>
                  <audiofile text="What's the Zip code on your account If you need time to find it, say 'wait a minute' " src="AQ1205_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AQ1205_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say or enter the Zip code for your account You can also say 'wait a minute' or press star" src="AQ1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AQ1205_nm3_01">
                <prompt-segments>
                  <audiofile text="If you know the Zip code for this account, enter it now on your keypad If you need time to look for it, press star" src="AQ1205_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1205_nm1_01">
                <prompt-segments>
                  <audiofile text="What's the Zip code on your account If you need time to find it, say 'wait a minute' " src="AQ1205_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1205_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say or enter the Zip code for your account You can also say 'wait a minute' or press star" src="AQ1205_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1205_nm3_01">
                <prompt-segments>
                  <audiofile text="If you know the Zip code for this account, enter it now on your keypad If you need time to look for it, press star" src="AQ1205_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="saidOperator == true">
                <prompt id="AQ1205_rin_02">
                  <prompt-segments>
                    <audiofile text="I just need your Zip code before I send your message Please say or enter it now" src="AQ1205_rin_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="returnFromWait == true">
                  <prompt id="AQ1205_rin_03">
                    <prompt-segments>
                      <audiofile text="Alright What's your Zip code" src="AQ1205_rin_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="AQ1205_rin_04">
                    <prompt-segments>
                      <audiofile text="I'm sorry, that's not the zip code I have on file for you Please enter it again using your keypad" src="AQ1205_rin_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="AQ1205_GetAcctZip_DM.grxml" count="1"/>
          <dtmfgrammars filename="AQ1205_GetAcctZip_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="AQ1205_GetAcctZip_DM_confirmation_reentry"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root">
                  <param value="f" name="intonation"/>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AQ1210_GetZipWaitSBI_DM" type="CUST">
      <success>
        <action label="continue">
          <session-mapping key="GlobalVars.returnFromWait" value="true" type="Boolean"/>
          <action next="AQ1205_GetAcctZip_DM"/>
        </action>
        <action label="cant_find">
          <audio>
            <prompt id="AQ1210_out_01">
              <prompt-segments>
                <audiofile text="Then, I wont be able to send you the answer to your security question" src="AQ1210_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.authFailure" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator">
          <action next="getReturnLink()"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AQ1210_ini_01">
                <prompt-segments>
                  <audiofile text="When you're ready with your Zip code, just say 'Continue' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2  At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your zip code, say 'Continue' or press 1, or say 'I can't find it' or press 2 If you're ready,  say 'Continue' or press 1, or say 'I can't find it' or press 2You can say 'Continue' or press 1, or 'I can't find it' or press 2  It looks like we're having some trouble " src="AQ1210_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AQ1210_ini_01">
                <prompt-segments>
                  <audiofile text="When you're ready with your Zip code, just say 'Continue' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2  At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your zip code, say 'Continue' or press 1, or say 'I can't find it' or press 2 If you're ready,  say 'Continue' or press 1, or say 'I can't find it' or press 2You can say 'Continue' or press 1, or 'I can't find it' or press 2  It looks like we're having some trouble " src="AQ1210_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AQ1210_ini_01">
                <prompt-segments>
                  <audiofile text="When you're ready with your Zip code, just say 'Continue' When you're ready, say 'Continue' or press 1, or say 'I can't find it' or press 2  At any time, you can say 'Continue' or press 1, or 'I can't find it' or press 2 If you're ready with your zip code, say 'Continue' or press 1, or say 'I can't find it' or press 2 If you're ready,  say 'Continue' or press 1, or say 'I can't find it' or press 2You can say 'Continue' or press 1, or 'I can't find it' or press 2  It looks like we're having some trouble " src="AQ1210_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="AQ1210_GetZipWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="AQ1210_GetZipWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="0ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AQ1305_HaveMetroPhoneYN_DM" type="YSNO">
      <session-mapping key="askSQEntry" value="GlobalVars.askSQEntry" type="String"/>
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorAQ1305?GlobalVars.saidOperatorAQ1305:false" type="String"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorAQ1305" value="false" type="Boolean"/>
        <action label="true">
          <action next="AQ1205_GetAcctZip_DM"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.securityCheckNoPhone" value="true" type="Boolean"/>
          <if cond="careAlterHandContainment == 'true'">
            <audio>
              <prompt id="AQ1305_out_01">
                <prompt-segments>
                  <audiofile text="Unfortunately our agents couldn't help you without your security information, or your MetroPCS phone You can manage your account or get help through the myMetro app, online, or at one of our stores or authorized dealers For a map of our locations near you, please visit metropcscom" src="AQ1305_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="getReturnLink()"/>
            <else>
              <action next="AQ1310_NoPhoneCallBack_DM"/>
            </else>
          </if>
        </action>
        <action label="operator">
          <if cond="saidOperator == false">
            <session-mapping key="GlobalVars.saidOperatorAQ1305" value="true" type="Boolean"/>
            <action next="AQ1305_HaveMetroPhoneYN_DM"/>
            <else>
              <session-mapping key="GlobalVars.securityCheckNoPhone" value="true" type="Boolean"/>
              <action next="AQ1310_NoPhoneCallBack_DM"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AQ1305_ini_01" cond="askSQEntry == '228login'">
                <prompt-segments>
                  <audiofile text="I can text you the answer so we can continue switching your phone" src="AQ1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="askSQEntry == '228login'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_ini_06" cond="askSQEntry == 'EWallet' || askSQEntry == 'careLogin'">
                <prompt-segments>
                  <audiofile text="I can text you the answer to your security question" src="AQ1305_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_ini_03" cond="callType == 'esn_swap' || callType == 'switch_phone'">
                <prompt-segments>
                  <audiofile text="Do you have your old phone handy and turned on? That's where I'll be sending the message" src="AQ1305_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_ini_04" cond=" !(callType == 'esn_swap' || callType == 'switch_phone')">
                <prompt-segments>
                  <audiofile text="Do you have your MetroPCS phone handy and turned on? That's where I'll be sending the message" src="AQ1305_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AQ1305_HaveMetroPhoneYN_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AQ1305_ini_01" cond="askSQEntry == '228login'">
                <prompt-segments>
                  <audiofile text="I can text you the answer so we can continue switching your phone" src="AQ1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="askSQEntry == '228login'">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_ini_06" cond="askSQEntry == 'EWallet' || askSQEntry == 'careLogin'">
                <prompt-segments>
                  <audiofile text="I can text you the answer to your security question" src="AQ1305_ini_06.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_ini_03" cond="callType == 'esn_swap' || callType == 'switch_phone'">
                <prompt-segments>
                  <audiofile text="Do you have your old phone handy and turned on? That's where I'll be sending the message" src="AQ1305_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_ini_04" cond=" !(callType == 'esn_swap' || callType == 'switch_phone')">
                <prompt-segments>
                  <audiofile text="Do you have your MetroPCS phone handy and turned on? That's where I'll be sending the message" src="AQ1305_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AQ1305_nm1_01" cond="callType == 'esn_swap' || callType == 'switch_phone'">
                <prompt-segments>
                  <audiofile text="Do you still have your old phone handy and turned on" src="AQ1305_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_nm1_02" cond="callType != 'esn_swap' &amp;&amp; callType != 'switch_phone'">
                <prompt-segments>
                  <audiofile text="Do you have your MetroPCS phone handy and turned on" src="AQ1305_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AQ1305_nm2_01" cond="callType == 'esn_swap' || callType == 'switch_phone'">
                <prompt-segments>
                  <audiofile text="I'll send the text message to the old phone on your account If you have it handy, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="AQ1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_nm2_02" cond="callType != 'esn_swap' &amp;&amp; callType != 'switch_phone'">
                <prompt-segments>
                  <audiofile text="I'll send the text message to the phone on your MetroPCS account If you have it handy, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="AQ1305_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AQ1305_nm3_01" cond="callType == 'esn_swap' || callType == 'switch_phone'">
                <prompt-segments>
                  <audiofile text="If you have your old  phone with you, press 1 If not, press 2" src="AQ1305_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_nm3_02" cond="callType != 'esn_swap' &amp;&amp; callType != 'switch_phone'">
                <prompt-segments>
                  <audiofile text="If you have your MetroPCS phone with you, press 1 If not, press 2" src="AQ1305_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_nm1_01" cond="callType == 'esn_swap' || callType == 'switch_phone'">
                <prompt-segments>
                  <audiofile text="Do you still have your old phone handy and turned on" src="AQ1305_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_nm1_02" cond="callType != 'esn_swap' &amp;&amp; callType != 'switch_phone'">
                <prompt-segments>
                  <audiofile text="Do you have your MetroPCS phone handy and turned on" src="AQ1305_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_nm2_01" cond="callType == 'esn_swap' || callType == 'switch_phone'">
                <prompt-segments>
                  <audiofile text="I'll send the text message to the old phone on your account If you have it handy, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="AQ1305_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_nm2_02" cond="callType != 'esn_swap' &amp;&amp; callType != 'switch_phone'">
                <prompt-segments>
                  <audiofile text="I'll send the text message to the phone on your MetroPCS account If you have it handy, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="AQ1305_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_nm3_01" cond="callType == 'esn_swap' || callType == 'switch_phone'">
                <prompt-segments>
                  <audiofile text="If you have your old  phone with you, press 1 If not, press 2" src="AQ1305_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_nm3_02" cond="callType != 'esn_swap' &amp;&amp; callType != 'switch_phone'">
                <prompt-segments>
                  <audiofile text="If you have your MetroPCS phone with you, press 1 If not, press 2" src="AQ1305_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AQ1305_ini_05" cond="saidOperator == true &amp;&amp; (callType == 'esn_swap' || callType == 'switch_phone')">
                <prompt-segments>
                  <audiofile text="Sorry, I wont be able to text you unless you have your old phone Do you have it handy? " src="AQ1305_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_rin_03" cond="saidOperator == true &amp;&amp; !(callType == 'esn_swap' || callType == 'switch_phone')">
                <prompt-segments>
                  <audiofile text="Sorry, I wont be able to text you unless you have the phone on your Metro PCS account Do you have it handy?" src="AQ1305_rin_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_ini_01" cond="askSQEntry == '228login' &amp;&amp; !(saidOperator == true)">
                <prompt-segments>
                  <audiofile text="I can text you the answer so we can continue switching your phone" src="AQ1305_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="askSQEntry == '228login' &amp;&amp; !(saidOperator == true)">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_ini_03" cond="(callType == 'esn_swap' || callType == 'switch_phone') &amp;&amp; !(saidOperator == true)">
                <prompt-segments>
                  <audiofile text="Do you have your old phone handy and turned on? That's where I'll be sending the message" src="AQ1305_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1305_ini_04" cond="!(callType == 'esn_swap' || callType == 'switch_phone') &amp;&amp; !(saidOperator == true)">
                <prompt-segments>
                  <audiofile text="Do you have your MetroPCS phone handy and turned on? That's where I'll be sending the message" src="AQ1305_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="AQ1305_HaveMetroPhoneYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="AQ1305_HaveMetroPhoneYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="AQ1310_NoPhoneCallBack_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorAQ1310?GlobalVars.saidOperatorAQ1310:false" type="String"/>
      <success>
        <session-mapping key="GlobalVars.saidOperatorAQ1310" value="false" type="Boolean"/>
        <action label="find_store">
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'securityfail'"/>
          <audio>
            <prompt id="AQ1310_out_01">
              <prompt-segments>
                <audiofile text="Alright" src="AQ1310_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="AQ1315_GoToStoreLocator_SD"/>
        </action>
        <action label="operator">
          <if cond="saidOperator == false">
            <session-mapping key="GlobalVars.saidOperatorAQ1310" value="true" type="Boolean"/>
            <action next="AQ1310_NoPhoneCallBack_DM"/>
            <else>
              <audio>
                <prompt id="AQ1310_Operator_01">
                  <prompt-segments>
                    <audiofile text="Sorry We can't go on without your security details Please call back when you have your 8-digit security code, the answer to your security question, or your MetroPCS phone" src="AQ1310_Operator_01.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AQ1310_ini_01" cond="(callType == 'esn_swap' || callType == 'switch_phone') &amp;&amp; !(saidOperator == true)" bargein="false">
                <prompt-segments>
                  <audiofile text="Unfortunately I can't switch your phone without your security information, or your old phone An agent couldn't do it either To check where you can get help in-person, say 'find a store' Otherwise, you can simply hang up" src="AQ1310_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1310_ini_02" cond="!(callType == 'esn_swap' || callType == 'switch_phone') &amp;&amp; !(saidOperator == true)" bargein="false">
                <prompt-segments>
                  <audiofile text="Unfortunately our agents couldn't help you without your security information, or your MetroPCS phone To check where you can get help in-person, say 'find a store' Otherwise, you can simply hang up" src="AQ1310_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1310_rin_01" cond="saidOperator == true" bargein="false">
                <prompt-segments>
                  <audiofile text="I'm sorry, our agents wont be able to help you without your security information, or your phone To get help in-person at a MetroPCS location near you, say 'find a store' Otherwise, you can simply hang up" src="AQ1310_rin_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AQ1310_NoPhoneCallBack_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AQ1310_ini_01" cond="(callType == 'esn_swap' || callType == 'switch_phone') &amp;&amp; !(saidOperator == true)" bargein="false">
                <prompt-segments>
                  <audiofile text="Unfortunately I can't switch your phone without your security information, or your old phone An agent couldn't do it either To check where you can get help in-person, say 'find a store' Otherwise, you can simply hang up" src="AQ1310_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1310_ini_02" cond="!(callType == 'esn_swap' || callType == 'switch_phone') &amp;&amp; !(saidOperator == true)" bargein="false">
                <prompt-segments>
                  <audiofile text="Unfortunately our agents couldn't help you without your security information, or your MetroPCS phone To check where you can get help in-person, say 'find a store' Otherwise, you can simply hang up" src="AQ1310_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1310_rin_01" cond="saidOperator == true" bargein="false">
                <prompt-segments>
                  <audiofile text="I'm sorry, our agents wont be able to help you without your security information, or your phone To get help in-person at a MetroPCS location near you, say 'find a store' Otherwise, you can simply hang up" src="AQ1310_rin_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AQ1310_nm1_01" bargein="false">
                <prompt-segments>
                  <audiofile text="To get help in-person at a MetroPCS location near you, say 'find a store' Otherwise, you can simply hang up" src="AQ1310_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01" bargein="false">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1310_nm1_01" bargein="false">
                <prompt-segments>
                  <audiofile text="To get help in-person at a MetroPCS location near you, say 'find a store' Otherwise, you can simply hang up" src="AQ1310_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AQ1310_ini_01" cond="(callType == 'esn_swap' || callType == 'switch_phone') &amp;&amp; !(saidOperator == true)" bargein="false">
                <prompt-segments>
                  <audiofile text="Unfortunately I can't switch your phone without your security information, or your old phone An agent couldn't do it either To check where you can get help in-person, say 'find a store' Otherwise, you can simply hang up" src="AQ1310_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1310_ini_02" cond="!(callType == 'esn_swap' || callType == 'switch_phone') &amp;&amp; !(saidOperator == true)" bargein="false">
                <prompt-segments>
                  <audiofile text="Unfortunately our agents couldn't help you without your security information, or your MetroPCS phone To check where you can get help in-person, say 'find a store' Otherwise, you can simply hang up" src="AQ1310_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1310_rin_01" cond="saidOperator == true" bargein="false">
                <prompt-segments>
                  <audiofile text="I'm sorry, our agents wont be able to help you without your security information, or your phone To get help in-person at a MetroPCS location near you, say 'find a store' Otherwise, you can simply hang up" src="AQ1310_rin_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="AQ1310_NoPhoneCallBack_DM.grxml" count="1"/>
          <dtmfgrammars filename="AQ1310_NoPhoneCallBack_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <reentryprompt count="1" bargein="true" filename="" text="" id=""/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="AQ1315_GoToStoreLocator_SD">
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="AQ1315_GoToStoreLocator_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AQ1315_GoToStoreLocator_SD_return_CS">
      <if cond="GlobalVars.askSQEntry == 'EWallet'">
        <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
        <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      </if>
      <action next="getReturnLink()"/>
    </custom-state>

    <play-state id="AQ1405_SMSTransitionPrompt_PP">
      <audio>
        <prompt id="AQ1405_out_01">
          <prompt-segments>
            <audiofile text="Thanks I'm sending your text message now" src="AQ1405_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AQ1410_SubmitSMSRequest_DB_DA"/>
    </play-state>

    <data-access-state id="AQ1410_SubmitSMSRequest_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.mdn:''" type="String"/>
      <session-mapping key="DID" value="dnis" type="String"/>
      <session-mapping key="smsType" value="(GlobalVars.askSQEntry == '228login')?'SECURITY_ANSWER_IVR_LOGIN':'SECURITY_ANSWER_IVR_TRANSFER'" type="String"/>
      <session-mapping key="smsParamValue" value="" type="String"/>
      <session-mapping key="task" value="AQ1410_SubmitSMSRequest_DB_DA" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="SendSMS" classname="com.nuance.metro.dataaccess.SendSMS">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="mdn"/>
          <input-variable name="DID"/>
          <input-variable name="smsType"/>
          <input-variable name="task"/>
          <input-variable name="smsParamValue"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="!(SendSMS.status == 'Success')">
          <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
          <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
          <action next="AQ1420_MessageProblemTransfer_PP"/>
          <else>
            <action next="AQ1415_SMSWaitSBI_DM"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <dm-state id="AQ1415_SMSWaitSBI_DM" type="CUST">
      <session-mapping key="askSQEntry" value="GlobalVars.askSQEntry" type="String"/>
      <success>
        <action label="got_it">
          <if cond="GlobalVars.askSQEntry == 'transfer'">
            <session-mapping key="GlobalVars.securityCheckPassed " value="true" type="Boolean"/>
            <audio>
              <prompt id="AQ1415_out_02">
                <prompt-segments>
                  <audiofile text="Great When you get to the agent, you can give them the answer in the message" src="AQ1415_out_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <audio>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="AQ1430_ReceivedSMSXfer_JDA"/>
            <else>
              <session-mapping key="GlobalVars.smsReceived" value="true" type="Boolean"/>
              <session-mapping key="GlobalVars.securityQuestionAttempts" expr="0"/>
              <audio>
                <prompt id="AQ1415_out_01">
                  <prompt-segments>
                    <audiofile text="Great" src="AQ1415_out_01.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="AQ1425_ReceivedSMSLogin_JDA"/>
            </else>
          </if>
        </action>
        <action label="operator">
          <action next="getReturnLink()"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AQ1415_ini_02" cond="askSQEntry == 'transfer'">
                <prompt-segments>
                  <audiofile text="When you receive it, just say 'I got it' Your message is on the way You'll need the answer to your security question when you talk to the agent When you receive it, say 'I got it' It shouldn't be much longer When you have it, just say 'I got it', and I'll take you to an agent" src="AQ1415_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1415_ini_01" cond="!(askSQEntry == 'transfer')">
                <prompt-segments>
                  <audiofile text="When you receive it, just say 'I got it' Your message is on the way When you receive it, say 'I got it' I'll ask you for the answer again before we continue It shouldn't be much longer When you have it, just say 'I got it'" src="AQ1415_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AQ1415_ini_02" cond="askSQEntry == 'transfer'">
                <prompt-segments>
                  <audiofile text="When you receive it, just say 'I got it' Your message is on the way You'll need the answer to your security question when you talk to the agent When you receive it, say 'I got it' It shouldn't be much longer When you have it, just say 'I got it', and I'll take you to an agent" src="AQ1415_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1415_ini_01" cond="!(askSQEntry == 'transfer')">
                <prompt-segments>
                  <audiofile text="When you receive it, just say 'I got it' Your message is on the way When you receive it, say 'I got it' I'll ask you for the answer again before we continue It shouldn't be much longer When you have it, just say 'I got it'" src="AQ1415_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AQ1415_ini_02" cond="askSQEntry == 'transfer'">
                <prompt-segments>
                  <audiofile text="When you receive it, just say 'I got it' Your message is on the way You'll need the answer to your security question when you talk to the agent When you receive it, say 'I got it' It shouldn't be much longer When you have it, just say 'I got it', and I'll take you to an agent" src="AQ1415_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AQ1415_ini_01" cond="!(askSQEntry == 'transfer')">
                <prompt-segments>
                  <audiofile text="When you receive it, just say 'I got it' Your message is on the way When you receive it, say 'I got it' I'll ask you for the answer again before we continue It shouldn't be much longer When you have it, just say 'I got it'" src="AQ1415_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="AQ1415_SMSWaitSBI_DM.grxml" count="1"/>
          <dtmfgrammars filename="AQ1415_SMSWaitSBI_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="0ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="selective" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="AQ1420_MessageProblemTransfer_PP">
      <session-mapping key="smsWaitTimeout" value="GlobalVars.smsWaitTimeout" type="String"/>
      <audio>
        <prompt id="AQ1420_out_01" cond="smsWaitTimeout == true">
          <prompt-segments>
            <audiofile text="It looks like there's some trouble with your message - let me transfer you to an agent If you don't get the message before you talk to them, they'll ask for your zip code and try to text you again" src="AQ1420_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AQ1420_out_02" cond="smsWaitTimeout != true">
          <prompt-segments>
            <audiofile text="It looks like I'm having trouble sending that message - let me transfer you to an agent They'll ask for your zip code and try to text you again" src="AQ1420_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.authFailure" value="true" type="Boolean"/>
      <action next="getReturnLink()"/>
    </play-state>

    <data-access-state id="AQ1425_ReceivedSMSLogin_JDA">
      <data-access id="WhatsNext">
        <outputs>
          <output-variable name="label" type="String"/>
        </outputs>
      </data-access>
      <action next="AQ1425_ReceivedSMSLogin_DS"/>
    </data-access-state>

    <decision-state id="AQ1425_ReceivedSMSLogin_DS">
      <gotodialog next="AskSecurityQuestion_Main_Dialog"/>
    </decision-state>

    <decision-state id="AQ1430_ReceivedSMSXfer_DS">
      <action next="getReturnLink()"/>
    </decision-state>

  </dialog>
  