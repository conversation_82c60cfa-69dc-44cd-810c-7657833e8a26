<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="CareESNSwap_Main_Cont_Dialog">
    <play-state id="ES1405_ValidateDeviceTransition_PP">
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="switchLinesSuccess" value="GlobalVars.switchLinesSuccess" type="String"/>
      <session-mapping key="usingOldSIMForSwap" value="GlobalVars.usingOldSIMForSwap == undefined ? false : GlobalVars.usingOldSIMForSwap" type="String"/>
      <session-mapping key="twoFactorAuthOutcome" value="GlobalVars.twoFactorAuthOutcome" type="String"/>
      <audio>
        <prompt id="ES1405_out_05">
          <prompt-segments>
            <audiofile text="Don't hang up, we're not done just yet! " src="ES1405_out_05.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="(usingOldSIMForSwap == true) &amp;&amp; (aniMatch == 'false' || aniMatch == false) &amp;&amp; (twoFactorAuthOutcome == 'success')">
          <prompt id="ES1405_out_04">
            <prompt-segments>
              <audiofile text="Meanwhile, you can now put your SIM card into your new phone" src="ES1405_out_04.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="(aniMatch == 'false' || aniMatch == false) || switchLinesSuccess == true">
            <prompt id="ES1405_out_03">
              <prompt-segments>
                <audiofile text="Meanwhile, please make sure your SIM card is inserted into your new phone " src="ES1405_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
        </if>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="ES1410_ValidateDeviceAndPlan_DB_DA"/>
    </play-state>

    <data-access-state id="ES1410_ValidateDeviceAndPlan_DB_DA">
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="iccid" value="(GlobalVars.deviceChangeTargetSIMChosen == 'ESIM') ? GlobalVars.ValidateDevice.eid : GlobalVars.iccidSerialNumber" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="ignoreDsDeviceProperty" value="GlobalVars.GetBCSParameters.device_change_esim_disable" type="String"/>
      <data-access id="ValidateDevice" classname="com.nuance.metro.dataaccess.ValidateDevice">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="imei"/>
          <input-variable name="mdn"/>
          <input-variable name="JWTToken"/>
          <input-variable name="sessionId"/>
          <input-variable name="iccid"/>
          <input-variable name="ignoreDsDeviceProperty"/>
        </inputs>
        <outputs>
          <output-variable name="imeiEquipmentActive"/>
          <output-variable name="iccidEquipmentActive"/>
          <output-variable name="imeiIsEligibleForActivation"/>
          <output-variable name="iccidIsEligibleForActivation"/>
          <output-variable name="imeiAllowSwap"/>
          <output-variable name="iccidAllowSwap"/>
          <output-variable name="imeiEquipmentInNegativeList"/>
          <output-variable name="iccidEquipmentInNegativeList"/>
          <output-variable name="imeiInvalidDevice"/>
          <output-variable name="iccidInvalidDevice"/>
          <output-variable name="simExpired"/>
          <output-variable name="BYODNotInInventory"/>
          <output-variable name="imeiSerialNumber"/>
          <output-variable name="iccidSerialNumber"/>
          <output-variable name="imeiItemId"/>
          <output-variable name="iccidItemId"/>
          <output-variable name="imeiHasBeenActivePrev"/>
          <output-variable name="iccidHasBeenActivePrev"/>
          <output-variable name="imeiAllowInsurance"/>
          <output-variable name="iccidAllowInsurance"/>
          <output-variable name="networkType"/>
          <output-variable name="imeiList"/>
          <output-variable name="isDualSimDevice"/>
          <output-variable name="itemIdDesc"/>
          <output-variable name="eid"/>
          <output-variable name="deviceType"/>
          <output-variable name="actualSupportedImei"/>
          <output-variable name="simType"/>
          <output-variable name="operatingSystem"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="ValidateDevice.status == 'Success'">
          <session-mapping key="GlobalVars.ValidateDevice" expr="ValidateDevice"/>
          <session-mapping key="GlobalVars.ValidateDevice.BYODNotInInventory" expr="ValidateDevice.BYODNotInInventory"/>
          <session-mapping key="GlobalVars.byodCompatibility" expr="ValidateDevice.byodCompatibility"/>
          <session-mapping key="GlobalVars.imeiList" expr="ValidateDevice.imeiList"/>
          <session-mapping key="GlobalVars.isDualSimDevice" expr="ValidateDevice.isDualSimDevice"/>
          <session-mapping key="GlobalVars.itemIdDesc" expr="ValidateDevice.itemIdDesc"/>
          <session-mapping key="GlobalVars.operatingSystem" expr="ValidateDevice.operatingSystem"/>
          <if cond="GlobalVars.ValidateDevice.BYODNotInInventory == true">
            <session-mapping key="GlobalVars.isByod" value="true" type="Boolean"/>
          </if>
          <session-mapping key="ActivationTable.NETWORKTYPE" expr="'G'"/>
          <session-mapping key="ActivationTable.DEVICEID" expr="ValidateDevice.imeiItemId"/>
          <session-mapping key="ActivationTable.ESN" expr="ValidateDevice.imeiSerialNumber"/>
          <action next="ES1415_CheckDeviceStatus_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail.ValidateDevice'"/>
            <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'133'"/>
            <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="ES1415_CheckDeviceStatus_DS">
      <if cond="GlobalVars.ValidateDevice.simExpired == true || GlobalVars.ValidateDevice.iccidEquipmentInNegativeList == true">
        <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
        <action next="ES1701_CheckIssue_JDA"/>
        <elseif cond="GlobalVars.ValidateDevice.imeiInvalidDevice == true">
          <action next="ES1701_CheckIssue_JDA"/>
        </elseif>
        <elseif cond="GlobalVars.ValidateDevice.currentAddOnEligibleIndicator == false || GlobalVars.ValidateDevice.currentPlanEligibleIndicator == false">
          <gotodialog next="CareESNSwap_Conflicts_Main#ES1905_CheckAccountIssue_DS"/>
        </elseif>
        <elseif cond="GlobalVars.ValidateDevice.imeiIsEligibleForActivation == true || GlobalVars.isByod  == true">
          <if cond="(GlobalVars.ValidateDevice.simType == 'ESIM' || GlobalVars.deviceChangeTargetSIMChosen == 'ESIM')">
            <action next="ES2215_ReserveESIM_DB_DA"/>
            <else>
              <action next="ES1505_SubmitSwapTransition_DM"/>
            </else>
          </if>
        </elseif>
        <else>
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
          <action next="ES1701_CheckIssue_JDA"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="ES1505_SubmitSwapTransition_DM" type="DIGT">
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="usingOldSIMForSwap" value="GlobalVars.usingOldSIMForSwap == undefined ? false : GlobalVars.usingOldSIMForSwap" type="String"/>
      <session-mapping key="switchLinesSuccess" value="GlobalVars.switchLinesSuccess" type="String"/>
      <session-mapping key="esnChangedPlan" value="GlobalVars.esnChangedPlan" type="String"/>
      <session-mapping key="acceptedRemoveFeatures" value="GlobalVars.acceptedRemoveFeatures" type="String"/>
      <session-mapping key="BYODNotInInventory" value="GlobalVars.ValidateDevice.BYODNotInInventory" type="String"/>
      <session-mapping key="audioMessageKey" value="metro_insurance_product_message" type="String"/>
      <session-mapping key="isByod" value="GlobalVars.isByod != undefined ? GlobalVars.isByod : false" type="String"/>
      <session-mapping key="simType" value="GlobalVars.ValidateDevice.simType" type="String"/>
      <session-mapping key="deviceChangeTargetSIMChosen" value="GlobalVars.deviceChangeTargetSIMChosen" type="String"/>
      <success>
        <action label="repeat">
          <action next="ES1510_SubmitChangeDevice_DB_DA"/>
        </action>
        <action label="default" next="ES1510_SubmitChangeDevice_DB_DA"/>
      </success>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES1505_out_01" cond="esnChangedPlan == 'true' || esnChangedPlan == true || acceptedRemoveFeatures == true">
                <prompt-segments>
                  <audiofile text="I'm going to try and swap your phone now, with your current plan If that doesn't work, we'll set you up with a new plan " src="ES1505_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_ini_03" cond="!(esnChangedPlan == 'true' || esnChangedPlan == true || acceptedRemoveFeatures == true)">
                <prompt-segments>
                  <audiofile src="ES1505_ini_03.wav" text="We're almost done!"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_ini_04" cond="(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')">
                <prompt-segments>
                  <audiofile text="I d also like to mention our device protection program It s available for brand new phones only It provides protection against loss and theft, accidental damage, including liquid damage and malfunctions due to mechanical or electrical breakdown after manufacturer's warranty expires It provides next business-day shipping, when available, at no additional cost If you re interested, you ll have 7 days to add it from the date of the new purchase of your device Just go to Metro by T dash mobile dot com, and click My Account " src="ES1505_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_ini_06" cond="(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')">
                <prompt-segments>
                  <audiofile src="ES1505_ini_06.wav" text="This call will *drop* when I transfer your service to your new phone"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false" cond="(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage_ES1505"/>
              </prompt>
              <prompt id="ES1505_ini_01" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'))     &amp;&amp;      (aniMatch == 'true' || aniMatch == true) &amp;&amp; (switchLinesSuccess == false)">
                <prompt-segments>
                  <audiofile text="This call will *drop* when I transfer your service to your new phone Don't worry, that means it's working! " src="ES1505_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_ini_02" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'))     &amp;&amp; (aniMatch == 'true' || aniMatch == true) &amp;&amp; (switchLinesSuccess == false) &amp;&amp; (usingOldSIMForSwap == 'true' || usingOldSIMForSwap == true)">
                <prompt-segments>
                  <audiofile text="When the call drops, take your SIM card out and pop it into your new phone Then, restart your new phone and it should be working within a few hours " src="ES1505_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_out_02" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'))     &amp;&amp; (aniMatch == 'true' || aniMatch == true) &amp;&amp; (switchLinesSuccess == false) &amp;&amp; (usingOldSIMForSwap == 'false' || usingOldSIMForSwap == false)">
                <prompt-segments>
                  <audiofile text="When the call drops, restart your new phone and it should be working within a few hours " src="ES1505_out_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'))">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'))">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage_ES1505"/>
              </prompt>
              <prompt id="ES1505_ini_05" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')) &amp;&amp; (isByod == false)">
                <prompt-segments>
                  <audiofile text="Hold tight while I set up your phone " src="ES1505_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_out_03" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')) &amp;&amp; (isByod == true)">
                <prompt-segments>
                  <audiofile text="One moment " src="ES1505_out_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES1505_out_01" cond="esnChangedPlan == 'true' || esnChangedPlan == true || acceptedRemoveFeatures == true">
                <prompt-segments>
                  <audiofile text="I'm going to try and swap your phone now, with your current plan If that doesn't work, we'll set you up with a new plan " src="ES1505_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_ini_03" cond="!(esnChangedPlan == 'true' || esnChangedPlan == true || acceptedRemoveFeatures == true)">
                <prompt-segments>
                  <audiofile src="ES1505_ini_03.wav" text="We're almost done!"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_ini_04" cond="(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')">
                <prompt-segments>
                  <audiofile text="I d also like to mention our device protection program It s available for brand new phones only It provides protection against loss and theft, accidental damage, including liquid damage and malfunctions due to mechanical or electrical breakdown after manufacturer's warranty expires It provides next business-day shipping, when available, at no additional cost If you re interested, you ll have 7 days to add it from the date of the new purchase of your device Just go to Metro by T dash mobile dot com, and click My Account " src="ES1505_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_ini_06" cond="(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')">
                <prompt-segments>
                  <audiofile src="ES1505_ini_06.wav" text="This call will *drop* when I transfer your service to your new phone"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false" cond="(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage_ES1505"/>
              </prompt>
              <prompt id="ES1505_ini_01" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'))     &amp;&amp;      (aniMatch == 'true' || aniMatch == true) &amp;&amp; (switchLinesSuccess == false)">
                <prompt-segments>
                  <audiofile text="This call will *drop* when I transfer your service to your new phone Don't worry, that means it's working! " src="ES1505_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_ini_02" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'))     &amp;&amp; (aniMatch == 'true' || aniMatch == true) &amp;&amp; (switchLinesSuccess == false) &amp;&amp; (usingOldSIMForSwap == 'true' || usingOldSIMForSwap == true)">
                <prompt-segments>
                  <audiofile text="When the call drops, take your SIM card out and pop it into your new phone Then, restart your new phone and it should be working within a few hours " src="ES1505_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_out_02" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'))     &amp;&amp; (aniMatch == 'true' || aniMatch == true) &amp;&amp; (switchLinesSuccess == false) &amp;&amp; (usingOldSIMForSwap == 'false' || usingOldSIMForSwap == false)">
                <prompt-segments>
                  <audiofile text="When the call drops, restart your new phone and it should be working within a few hours " src="ES1505_out_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'))">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="audioMessageKey" bargein="false" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'))">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptPlayAudioMessage_ES1505"/>
              </prompt>
              <prompt id="ES1505_ini_05" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')) &amp;&amp; (isByod == false)">
                <prompt-segments>
                  <audiofile text="Hold tight while I set up your phone " src="ES1505_ini_05.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1505_out_03" cond="(!(simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM')) &amp;&amp; (isByod == true)">
                <prompt-segments>
                  <audiofile text="One moment " src="ES1505_out_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="ES1505_SubmitSwapTransition_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="500ms" timeout="100ms"/>
      </collection_configuration>
      <global_configuration>
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" interdigittimeout="100ms" termtimeout="100ms" timeout="500ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="NEVER" highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="100ms" completetimeout="0ms" maxspeechtimeout="100ms" timeout="100ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="ES1510_SubmitChangeDevice_DB_DA">
      <session-mapping key="trn" value="GlobalVars.trn" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="iccid" value="" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="isByod" value="GlobalVars.isByod" type="String"/>
      <session-mapping key="deviceType" value="GlobalVars.deviceType" type="String"/>
      <session-mapping key="removeFeatures" value="GlobalVars.removeFeatures != undefined ? GlobalVars.removeFeatures : ''" type="String"/>
      <session-mapping key="newRatePlan" value="GlobalVars.newRatePlan != undefined ? GlobalVars.newRatePlan : ''" type="String"/>
      <session-mapping key="removeFuturePromoSoc" value="(GlobalVars.removeFuturePromoSoc != undefined)  ? GlobalVars.removeFuturePromoSoc : ''" type="String"/>
      <session-mapping key="removeRegularPromoSoc" value="(GlobalVars.removeRegularPromoSoc != undefined)  ? GlobalVars.removeRegularPromoSoc : ''" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="isOrderApproved" value="GlobalVars.isOrderApproved" type="String"/>
      <session-mapping key="orderId" value="GlobalVars.orderId" type="String"/>
      <session-mapping key="useTwoStep" value="true" type="Boolean"/>
      <session-mapping key="accessToken" value="GlobalVars.ValidateTempCode == undefined ? '' : GlobalVars.ValidateTempCode.accessToken" type="String"/>
      <if cond="trn == null || trn.length !=15">
        <session-mapping key="trn" expr="''"/>
      </if>
      <if cond="GlobalVars.deviceChangeTargetSIMChosen == 'ESIM' || GlobalVars.ValidateDevice.simType == 'ESIM'">
        <session-mapping key="iccid" expr="GlobalVars.ValidateDevice.eid"/>
        <else>
          <session-mapping key="iccid" expr="GlobalVars.iccidSerialNumber"/>
        </else>
      </if>
      <data-access id="SubmitChangeDevice" classname="com.nuance.metro.dataaccess.SubmitChangeDevice">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="trn"/>
          <input-variable name="mdn"/>
          <input-variable name="imei"/>
          <input-variable name="iccid"/>
          <input-variable name="isByod"/>
          <input-variable name="deviceType"/>
          <input-variable name="removeFeatures"/>
          <input-variable name="newRatePlan"/>
          <input-variable name="JWTToken"/>
          <input-variable name="isOrderApproved"/>
          <input-variable name="accessToken"/>
          <input-variable name="orderId"/>
          <input-variable name="useTwoStep"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="deviceChangedInd"/>
          <output-variable name="dueImmediatlyAmount"/>
          <output-variable name="orderId"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.SubmitChangeDevice" expr="SubmitChangeDevice"/>
        <if cond="SubmitChangeDevice.status == 'Success' &amp;&amp; SubmitChangeDevice.dueImmediatlyAmount &gt; 0">
          <session-mapping key="GlobalVars.orderId" expr="GlobalVars.SubmitChangeDevice.orderId"/>
          <gotodialog next="CareESNSwap_Conflicts_Main#ES2005_ApproveNewPayment_DM"/>
          <elseif cond="SubmitChangeDevice.status == 'Success' &amp;&amp; SubmitChangeDevice.deviceChangedInd != true">
            <if cond="GlobalVars.SubmitChangeDeviceCalled != true">
              <session-mapping key="GlobalVars.SubmitChangeDeviceCalled" value="true" type="Boolean"/>
              <session-mapping key="GlobalVars.orderId" expr="GlobalVars.SubmitChangeDevice.orderId"/>
              <session-mapping key="GlobalVars.isOrderApproved" value="true" type="Boolean"/>
              <action next="ES1510_SubmitChangeDevice_DB_DA"/>
              <else>
                <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
              </else>
            </if>
          </elseif>
          <elseif cond="SubmitChangeDevice.status == 'Success' &amp;&amp; SubmitChangeDevice.deviceChangedInd == true">
            <session-mapping key="esnChangeVars.eventTypeGMT" expr="getEventTime()"/>
            <session-mapping key="esnChangeVars.status" expr="'success'"/>
            <session-mapping key="esnChangeVars.eventType" expr="'esn_change'"/>
            <action next="ES1515_PlaySwapSuccess_PP"/>
          </elseif>
          <else>
            <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'133'"/>
            <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <play-state id="ES1515_PlaySwapSuccess_PP">
      <session-mapping key="simType" value="GlobalVars.ValidateDevice.simType" type="String"/>
      <session-mapping key="usingOldSIMForSwap" value="GlobalVars.usingOldSIMForSwap == undefined ? false : GlobalVars.usingOldSIMForSwap" type="String"/>
      <session-mapping key="deviceChangeTargetSIMChosen" value="GlobalVars.deviceChangeTargetSIMChosen" type="String"/>
      <audio>
        <if cond="usingOldSIMForSwap == true || simType == 'PSIM' || deviceChangeTargetSIMChosen == 'PSIM'">
          <prompt id="ES1515_out_01">
            <prompt-segments>
              <audiofile text="You've successfully switched your device Activation can take up to ten minutes to processif you have any problems or eSIM does not download, please contact Metro by T-Mobile customer service" src="ES1515_out_01.wav"/>
            </prompt-segments>
          </prompt>
        </if>
      </audio>
      <session-mapping key="GlobalVars.SubmitChangeDeviceCalled" value="false" type="Boolean"/>
      <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'135'"/>
      <session-mapping key="esnChangeVars.eventTypeGMT" expr="getEventTime()"/>
      <session-mapping key="esnChangeVars.status" expr="'success'"/>
      <session-mapping key="esnChangeVars.eventType" expr="'esn_change'"/>
      <action next="getReturnLink()"/>
    </play-state>

    <decision-state id="ES1603_CheckCompatibility_DS">
      <if cond="(GlobalVars.byodCompatibility == 'Fully Compatible')">
        <action next="ES1605_RegisterBYOD_SD"/>
        <else>
          <action next="ES1604_CompatibilityIssues_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="ES1604_CompatibilityIssues_SD">
      <gotodialog next="CompatibilityIssues_Dialog"/>
      <action next="ES1604_CompatibilityIssues_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES1604_CompatibilityIssues_SD_return_CS">
      <if cond="(GlobalVars.compatibilityTransfer == true)">
        <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
        <else>
          <action next="ES1605_RegisterBYOD_SD"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="ES1605_RegisterBYOD_SD">
      <gotodialog next="RegisterBYOD_Main_Dialog"/>
      <action next="ES1605_RegisterBYOD_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES1605_RegisterBYOD_SD_return_CS">
      <if cond="deviceType == undefined">
        <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
        <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
        <else>
          <session-mapping key="GlobalVars.BYODRegistered" value="true" type="Boolean"/>
          <action next="ES1610_BYODInfo_SD"/>
        </else>
      </if>
    </custom-state>

    <subdialog-state id="ES1610_BYODInfo_SD">
      <gotodialog next="BYODInfo_Main_Dialog"/>
      <action next="ES1610_BYODInfo_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES1610_BYODInfo_SD_return_CS">
      <action next="ES2110_FirstValidateDeviceAndPlan_DB_DA"/>
    </custom-state>

    <decision-state id="ES1701_CheckIssue_DS">
      <session-mapping key="esnChangeVars.eventTypeGMT" expr="getEventTime()"/>
      <session-mapping key="esnChangeVars.status" expr="'failure'"/>
      <session-mapping key="esnChangeVars.eventType" expr="'esn_change'"/>
      <if cond="GlobalVars.ValidateDevice.imeiEquipmentActive == true">
        <action next="ES1705_ESNActive_PP"/>
        <elseif cond="GlobalVars.ValidateDevice.simExpired == true">
          <action next="ES1715_SIMExpired_PP"/>
        </elseif>
        <elseif cond="GlobalVars.ValidateDevice.imeiEquipmentInNegativeList == true">
          <action next="ES1722_PhoneLostOrStolen_PP"/>
        </elseif>
        <elseif cond="GlobalVars.ValidateDevice.iccidEquipmentInNegativeList == true">
          <action next="ES1723_SIMLostOrStolen_PP"/>
        </elseif>
        <else>
          <action next="ES1720_DeviceInvalid_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="ES1705_ESNActive_PP">
      <audio>
        <prompt id="ES1705_out_01">
          <prompt-segments>
            <audiofile text="It looks like the phone you want to use is already active on an account " src="ES1705_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
    </play-state>

    <play-state id="ES1715_SIMExpired_PP">
      <audio>
        <prompt id="ES1715_out_01">
          <prompt-segments>
            <audiofile text="It looks like there s an issue with your SIM card You ll need to get help at one of our stores or authorized dealers " src="ES1715_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="enteringFrom" expr="'ES1715_SIMExpired_PP'"/>
      <action next="ES1725_FindStore_DM"/>
    </play-state>

    <play-state id="ES1720_DeviceInvalid_PP">
      <audio>
        <prompt id="ES1720_out_01">
          <prompt-segments>
            <audiofile src="ES1720_out_01.wav" text="It looks like there s an issue with your phone, so I won t be able to add it to an account You ll have to take it to a Metro  store or authorized dealer to discuss available options"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="enteringFrom" expr="'ES1720_DeviceInvalid_PP'"/>
      <action next="ES1725_FindStore_DM"/>
    </play-state>

    <play-state id="ES1722_PhoneLostOrStolen_PP">
      <audio>
        <prompt id="ES1722_out_01">
          <prompt-segments>
            <audiofile text="Because your phone is reported as lost or stolen, our agents won't be able to switch your phone You'll need to contact the carrier with whom the phone was previously registered to resolve this " src="ES1722_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="ES1735_Goodbye_SD"/>
    </play-state>

    <play-state id="ES1723_SIMLostOrStolen_PP">
      <audio>
        <prompt id="ES1723_out_01">
          <prompt-segments>
            <audiofile text="Because this SIM card is reported as lost or stolen, I won't be able to switch your phone here in the automated system So you'll need to get help in person at a Metro store " src="ES1723_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="ES1725_FindStore_DM"/>
    </play-state>

    <dm-state id="ES1725_FindStore_DM" type="CUST">
      <session-mapping key="simExpired" value="GlobalVars.ValidateDevice.simExpired" type="String"/>
      <success>
        <action label="inquire-store_location">
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'payment'"/>
          <action next="ES1730_StoreLocator_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="ES1725_operator_counter" expr="ES1725_operator_counter+1"/>
          <if cond="ES1725_operator_counter &lt; 2">
            <action next="ES1725_FindStore_DM"/>
            <else>
              <audio>
                <prompt id="ES1725_out_01">
                  <prompt-segments>
                    <audiofile text="I m sorry Our agents would not be able to help you To find a map of our locations, please visit us at metrobyt-mobilecom" src="ES1725_out_01.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="ES1735_Goodbye_SD"/>
            </else>
          </if>
        </action>
        <action label="repeat">
          <if cond="enteringFrom == 'ES1715_SIMExpired_PP'">
            <action next="ES1715_SIMExpired_PP"/>
            <elseif cond="enteringFrom == 'ES1720_DeviceInvalid_PP'">
              <action next="ES1720_DeviceInvalid_PP"/>
            </elseif>
            <else>
              <action next="ES1725_FindStore_DM"/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="ES1725_operator_counter != 1">
                <prompt id="ES1725_ini_01">
                  <prompt-segments>
                    <audiofile src="ES1725_ini_01.wav" text="To look for Metro stores right now, say  find a store  You can also find a map of our locations near you at metrobyt-mobilecom If you re done, you can just hang up"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(ES1725_operator_counter == 1)">
                <prompt id="ES1725_rin_01" cond="simExpired == 'true' || simExpired == true">
                  <prompt-segments>
                    <audiofile src="ES1725_rin_01.wav" text="I m sorry There s a problem with your SIM card, so our agents would not be able to work with it over the phone You would need to buy a new one at a Metro store or authorized dealer You can say  find a store , or go to metrobyt-mobilecom for a map of our locations If you re done, you can just hang up"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1725_rin_02" cond="!(simExpired == 'true' || simExpired == true)">
                  <prompt-segments>
                    <audiofile src="ES1725_rin_02.wav" text="I m sorry Because of an issue with your phone, our agents would not be able to work with it over the phone You would need to get help *in person* at a Metro store or authorized dealer You can say  find a store , or go to metrobyt-mobilecom for a map of our locations If you re done, you can just hang up"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="ES1725_operator_counter != 1">
                <prompt id="ES1725_ini_01">
                  <prompt-segments>
                    <audiofile src="ES1725_ini_01.wav" text="To look for Metro stores right now, say  find a store  You can also find a map of our locations near you at metrobyt-mobilecom If you re done, you can just hang up"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="(ES1725_operator_counter == 1)">
                <prompt id="ES1725_rin_01" cond="simExpired == 'true' || simExpired == true">
                  <prompt-segments>
                    <audiofile src="ES1725_rin_01.wav" text="I m sorry There s a problem with your SIM card, so our agents would not be able to work with it over the phone You would need to buy a new one at a Metro store or authorized dealer You can say  find a store , or go to metrobyt-mobilecom for a map of our locations If you re done, you can just hang up"/>
                  </prompt-segments>
                </prompt>
                <prompt id="ES1725_rin_02" cond="!(simExpired == 'true' || simExpired == true)">
                  <prompt-segments>
                    <audiofile src="ES1725_rin_02.wav" text="I m sorry Because of an issue with your phone, our agents would not be able to work with it over the phone You would need to get help *in person* at a Metro store or authorized dealer You can say  find a store , or go to metrobyt-mobilecom for a map of our locations If you re done, you can just hang up"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES1725_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say find a store, or if you re done, just hang up " src="ES1725_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1725_nm2_01">
                <prompt-segments>
                  <audiofile text="To find out where you can get help, you can find a map of our corporate stores at metrobyt-mobilecom" src="ES1725_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1725_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say find a store, or if you re done, just hang up " src="ES1725_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="ES1725_nm2_01">
                <prompt-segments>
                  <audiofile text="To find out where you can get help, you can find a map of our corporate stores at metrobyt-mobilecom" src="ES1725_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="ES1725_FindStore_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES1725_FindStore_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="ES1730_StoreLocator_SD">
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="ES1730_StoreLocator_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES1730_StoreLocator_SD_return_CS">
      <action next="ES1735_Goodbye_SD"/>
    </custom-state>

    <subdialog-state id="ES1735_Goodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="ES1735_Goodbye_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES1735_Goodbye_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <decision-state id="ES1814_CheckNeedLogin_DS">
      <if cond="(GlobalVars.loggedIn == true || GlobalVars.loggedIn == 'true')">
        <if cond="GlobalVars.GetAccountDetails &amp;&amp; (GlobalVars.GetAccountDetails.hasPhoneInsurance == true || GlobalVars.GetAccountDetails.hasPhoneInsurance == 'true')">
          <action next="ES1822_PlayInsuranceTransition_PP"/>
        </if>
        <elseif cond="(GlobalVars.loggedIn == false || GlobalVars.loggedIn == 'false') &amp;&amp; (GlobalVars.GetAccountDetails == undefined || GlobalVars.GetAccountDetails == null)">
          <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <action next="ES1830_CheckFutureDatedRequest_JDA"/>
        </elseif>
        <else>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <action next="ES1815_Login_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="ES1815_Login_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="ES1815_Login_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES1815_Login_SD_return_CS">
      <action next="ES1820_PlayLoginTransition_PP"/>
    </custom-state>

    <play-state id="ES1820_PlayLoginTransition_PP">
      <audio>
        <prompt id="ES1020_out_01">
          <prompt-segments>
            <audiofile text="Thanks" src="ES1020_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="GlobalVars.GetAccountDetails &amp;&amp; (GlobalVars.GetAccountDetails.hasPhoneInsurance == true || GlobalVars.GetAccountDetails.hasPhoneInsurance == 'true' )">
        <action next="ES1822_PlayInsuranceTransition_PP"/>
        <else>
          <action next="ES1830_CheckFutureDatedRequest_JDA"/>
        </else>
      </if>
    </play-state>

    <play-state id="ES1822_PlayInsuranceTransition_PP">
      <audio>
        <prompt id="ES1818_out_01">
          <prompt-segments>
            <audiofile text="I see you have insurance on your device, so we will need assistance to switch your phoneOur agents will take it from here!" src="ES1818_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.callType" expr="'transfer_esn_insurance'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
    </play-state>

    <subdialog-state id="ES1825_TwoFactorAuth_SD">
      <gotodialog next="TwoFactorAuth_Main_Dialog"/>
      <action next="ES1825_TwoFactorAuth_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES1825_TwoFactorAuth_SD_return_CS">
      <if cond="GlobalVars.twoFactorAuthOutcome == 'success'">
        <action next="ES1830_CheckFutureDatedRequest_JDA"/>
        <elseif cond="GlobalVars.twoFactorAuthOutcome == 'no_phone'">
          <action next="ES1735_Goodbye_SD"/>
        </elseif>
        <else>
          <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
        </else>
      </if>
    </custom-state>

    <decision-state id="ES1830_CheckFutureDatedRequest_DS">
      <if cond="((GlobalVars.GetAccountDetails ) &amp;&amp; (GlobalVars.GetAccountDetails.customerInBannedMarket == true || GlobalVars.GetAccountDetails.customerInBannedMarket == 'true') &amp;&amp; (GlobalVars.GetAccountDetails.accountFutureRequestInd == true || GlobalVars.GetAccountDetails.accountFutureRequestInd == 'true'))">
        <action next="ES1835_FutureDateRequestTransition_PP"/>
        <else>
          <gotodialog next="CareESNSwap_Main#ES1205_IMEITransitionSkipSBI_DM"/>
        </else>
      </if>
    </decision-state>

    <play-state id="ES1835_FutureDateRequestTransition_PP">
      <audio>
        <prompt id="ES1835_out_01">
          <prompt-segments>
            <audiofile text="I see you have an upcoming change on your account, so I'll need to transfer you to an agent for help with switching your phone" src="ES1835_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <gotodialog next="CareESNSwap_Conflicts_Main#ES1935_Transfer_SD"/>
    </play-state>

    <play-state id="ES2105_FirstValidateDeviceTransition_PP">
      <audio>
        <prompt id="ES2105_out_01">
          <prompt-segments>
            <audiofile src="ES2105_out_01.wav" text="Okay, let me work on that"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="ES2110_FirstValidateDeviceAndPlan_DB_DA"/>
    </play-state>

    <data-access-state id="ES2110_FirstValidateDeviceAndPlan_DB_DA">
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="ignoreDsDeviceProperty" value="GlobalVars.GetBCSParameters.device_change_esim_disable" type="String"/>
      <data-access id="ValidateDevice" classname="com.nuance.metro.dataaccess.ValidateDevice">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="imei"/>
          <input-variable name="mdn"/>
          <input-variable name="JWTToken"/>
          <input-variable name="sessionId"/>
          <input-variable name="iccid"/>
          <input-variable name="ignoreDsDeviceProperty"/>
        </inputs>
        <outputs>
          <output-variable name="imeiEquipmentActive"/>
          <output-variable name="iccidEquipmentActive"/>
          <output-variable name="imeiIsEligibleForActivation"/>
          <output-variable name="iccidIsEligibleForActivation"/>
          <output-variable name="imeiAllowSwap"/>
          <output-variable name="iccidAllowSwap"/>
          <output-variable name="imeiEquipmentInNegativeList"/>
          <output-variable name="iccidEquipmentInNegativeList"/>
          <output-variable name="imeiInvalidDevice"/>
          <output-variable name="iccidInvalidDevice"/>
          <output-variable name="simExpired"/>
          <output-variable name="BYODNotInInventory"/>
          <output-variable name="imeiSerialNumber"/>
          <output-variable name="iccidSerialNumber"/>
          <output-variable name="imeiItemId"/>
          <output-variable name="iccidItemId"/>
          <output-variable name="imeiHasBeenActivePrev"/>
          <output-variable name="iccidHasBeenActivePrev"/>
          <output-variable name="imeiAllowInsurance"/>
          <output-variable name="iccidAllowInsurance"/>
          <output-variable name="networkType"/>
          <output-variable name="imeiList"/>
          <output-variable name="isDualSimDevice"/>
          <output-variable name="itemIdDesc"/>
          <output-variable name="eid"/>
          <output-variable name="deviceType"/>
          <output-variable name="actualSupportedImei"/>
          <output-variable name="simType"/>
          <output-variable name="operatingSystem"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="ValidateDevice.status == 'Success'">
          <session-mapping key="GlobalVars.ValidateDevice" expr="ValidateDevice"/>
          <session-mapping key="GlobalVars.ValidateDevice.BYODNotInInventory" expr="ValidateDevice.BYODNotInInventory"/>
          <session-mapping key="GlobalVars.byodCompatibility" expr="ValidateDevice.byodCompatibility"/>
          <session-mapping key="GlobalVars.imeiList" expr="ValidateDevice.imeiList"/>
          <session-mapping key="GlobalVars.isDualSimDevice" expr="ValidateDevice.isDualSimDevice"/>
          <session-mapping key="GlobalVars.itemIdDesc" expr="ValidateDevice.itemIdDesc"/>
          <session-mapping key="GlobalVars.operatingSystem" expr="ValidateDevice.operatingSystem"/>
          <if cond="GlobalVars.ValidateDevice.BYODNotInInventory == true">
            <session-mapping key="GlobalVars.isByod" value="true" type="Boolean"/>
          </if>
          <session-mapping key="ActivationTable.NETWORKTYPE" expr="'G'"/>
          <session-mapping key="ActivationTable.DEVICEID" expr="ValidateDevice.imeiItemId"/>
          <session-mapping key="ActivationTable.ESN" expr="ValidateDevice.imeiSerialNumber"/>
          <if cond="GlobalVars.ValidateDevice.deviceType == 'IOT' || GlobalVars.ValidateDevice.deviceType == 'WEARABLE' || GlobalVars.ValidateDevice.deviceType == 'TABLET'">
            <action next="ES2225_Transfer_SD"/>
          </if>
          <action next="ES2115_CheckIMEIStatus_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail.ValidateDevice'"/>
            <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'133'"/>
            <action next="ES2225_Transfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="ES2115_CheckIMEIStatus_DS">
      <if cond="GlobalVars.ValidateDevice.imeiEquipmentInNegativeList  == true">
        <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
        <action next="ES1701_CheckIssue_JDA"/>
        <elseif cond="GlobalVars.ValidateDevice.imeiEquipmentActive == true">
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
          <action next="ES1701_CheckIssue_JDA"/>
        </elseif>
        <elseif cond="(GlobalVars.ValidateDevice.isDualSimDevice == true) &amp;&amp; (GlobalVars.ValidateDevice.imeiList == null || GlobalVars.ValidateDevice.imeiList.length &lt; 2) ">
          <action next="ES2210_EIDOrIMEI2NotReturned_PP"/>
        </elseif>
        <elseif cond="GlobalVars.ValidateDevice.BYODNotInInventory == true &amp;&amp; GlobalVars.BYODRegistered == false">
          <action next="ES1603_CheckCompatibility_JDA"/>
        </elseif>
        <elseif cond="GlobalVars.ValidateDevice.simType == 'ESIM' &amp;&amp; (GlobalVars.ValidateDevice.eid == undefined || GlobalVars.ValidateDevice.eid == '' || GlobalVars.ValidateDevice.eid == null || GlobalVars.ValidateDevice.eid == 'null')">
          <action next="ES2210_EIDOrIMEI2NotReturned_PP"/>
        </elseif>
        <elseif cond="GlobalVars.ValidateDevice.simType == 'ESIM' || GlobalVars.ValidateDevice.simType == 'PESIM'">
          <action next="ES2117_GetAccountAccessRestrictions_DB_DA"/>
        </elseif>
        <else>
          <action next="ES2117_GetAccountAccessRestrictions_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="ES2117_GetAccountAccessRestrictions_DB_DA">
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <data-access id="GetAccountAccessRestrictions" classname="com.nuance.metro.dataaccess.GetAccountAccessRestrictions">
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="accountRestrictionsList"/>
          <output-variable name="simSwapRestricted"/>
          <output-variable name="unblockingNotAllowed"/>
          <output-variable name="portOutRestricted"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.GetAccountAccessRestrictions" expr="GetAccountAccessRestrictions"/>
        <if cond="GlobalVars.GetAccountAccessRestrictions &amp;&amp; GlobalVars.GetAccountAccessRestrictions.status &amp;&amp; GlobalVars.GetAccountAccessRestrictions.status.toUpperCase() == 'SUCCESS'">
          <session-mapping key="GlobalVars.activityCode" expr="GlobalVars.GetAccountAccessRestrictions.activityCode"/>
          <if cond="GlobalVars.ValidateDevice.simType == 'ESIM' || GlobalVars.ValidateDevice.simType == 'PESIM'">
            <action next="ES2120_CheckESIMActivationDisabled_JDA"/>
            <else>
              <action next="ES2125_CheckSourceAndTargetDevice_JDA"/>
            </else>
          </if>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'133'"/>
            <action next="ES2225_Transfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="ES2120_CheckESIMActivationDisabled_DS">
      <if cond="GlobalVars.GetBCSParameters.device_change_esim_disable  == 'true'">
        <if cond="GlobalVars.ValidateDevice.simType == 'ESIM'">
          <action next="ES2225_Transfer_SD"/>
          <else>
            <if cond="GlobalVars.GetAccountDetails.currentSIMType == 'ESIM'">
              <if cond="GlobalVars.activityCode == 'BSS'">
                <session-mapping key="GlobalVars.fromESNSwapDialog" value="true" type="Boolean"/>
                <gotodialog next="CareESNSwap_Main#ES1007_SimSwappedBlocked_DM"/>
                <else>
                  <gotodialog next="CareESNSwap_Main#ES1305_ICCIDTransitionSkipSBI_DM"/>
                </else>
              </if>
              <else>
                <gotodialog next="CareESNSwap_Main#ES1005_KeepingSameSIMCard_DM"/>
              </else>
            </if>
          </else>
        </if>
        <else>
          <action next="ES2125_CheckSourceAndTargetDevice_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="ES2125_CheckSourceAndTargetDevice_DS">
      <if cond="GlobalVars.ValidateDevice.simType == 'ESIM'">
        <if cond="GlobalVars.GetAccountDetails.currentSIMType  == 'ESIM'">
          <action next="ES2130_AskContinueWithESIMYN_DM"/>
          <else>
            <action next="ES2135_AskContinueWithESIM_DM"/>
          </else>
        </if>
        <elseif cond="GlobalVars.ValidateDevice.simType == 'PESIM'">
          <if cond="GlobalVars.GetAccountDetails.currentSIMType  == 'ESIM'">
            <action next="ES2145_ChooseESIMOrNewPSIM_DM"/>
            <else>
              <action next="ES2150_ChooseESIMOrNewPSIMOrOldPSIM_DM"/>
            </else>
          </if>
        </elseif>
        <else>
          <if cond="GlobalVars.GetAccountDetails.currentSIMType  == 'ESIM'">
            <action next="ES2140_AskContinueWithPSIMYN_DM"/>
            <else>
              <gotodialog next="CareESNSwap_Main#ES1005_KeepingSameSIMCard_DM"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <dm-state id="ES2130_AskContinueWithESIMYN_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="ES2130_out_01">
              <prompt-segments>
                <audiofile src="ES2130_out_01.wav" text="Great"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.deviceChangeTargetSIMChosen" expr="'ESIM'"/>
          <if cond="GlobalVars.activityCode == 'BSS'">
            <session-mapping key="GlobalVars.fromESNSwapDialog" value="true" type="Boolean"/>
            <gotodialog next="CareESNSwap_Main#ES1007_SimSwappedBlocked_DM"/>
            <else>
              <action next="ES2205_AskWiFiConnected_DM"/>
            </else>
          </if>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.cancelESNSwap" value="true" type="Boolean"/>
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
          <audio>
            <prompt id="ES2130_out_02">
              <prompt-segments>
                <audiofile src="ES2130_out_02.wav" text="No problem, I won t change anything"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES2130_ini_01">
                <prompt-segments>
                  <audiofile src="ES2130_ini_01.wav" text="Just so you know, you won t be able to use your current eSIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2130_ini_02">
                <prompt-segments>
                  <audiofile src="ES2130_ini_02.wav" text="Do you want to continue with your new phone s eSIM?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="" text="" id="ES2130_AskContinueWithESIMYN_DM_help"/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES2130_ini_01">
                <prompt-segments>
                  <audiofile src="ES2130_ini_01.wav" text="Just so you know, you won t be able to use your current eSIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2130_ini_02">
                <prompt-segments>
                  <audiofile src="ES2130_ini_02.wav" text="Do you want to continue with your new phone s eSIM?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES2130_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2130_nm1_01.wav" text="Do you want to continue with your new phone s eSIM?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2130_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2130_nm2_01.wav" text="To continue switching your phone using your new phone s eSIM, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2130_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2130_nm2_01.wav" text="To continue switching your phone using your new phone s eSIM, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2130_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2130_nm1_01.wav" text="Do you want to continue with your new phone s eSIM?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2130_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2130_nm2_01.wav" text="To continue switching your phone using your new phone s eSIM, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2130_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2130_nm2_01.wav" text="To continue switching your phone using your new phone s eSIM, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="ES2130_ini_01">
                <prompt-segments>
                  <audiofile src="ES2130_ini_01.wav" text="Just so you know, you won t be able to use your current eSIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2130_ini_02">
                <prompt-segments>
                  <audiofile src="ES2130_ini_02.wav" text="Do you want to continue with your new phone s eSIM?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="ES2130_AskContinueWithESIMYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES2130_AskContinueWithESIMYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="ES2135_AskContinueWithESIM_DM" type="CUST">
      <success>
        <action label="continue">
          <audio>
            <prompt id="ES2135_out_01">
              <prompt-segments>
                <audiofile src="ES2135_out_01.wav" text="Great"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.deviceChangeTargetSIMChosen" expr="'ESIM'"/>
          <session-mapping key="ES2135_counter" expr="0"/>
          <if cond="GlobalVars.activityCode == 'BSS'">
            <session-mapping key="GlobalVars.fromESNSwapDialog" value="true" type="Boolean"/>
            <gotodialog next="CareESNSwap_Main#ES1007_SimSwappedBlocked_DM"/>
            <else>
              <action next="ES2205_AskWiFiConnected_DM"/>
            </else>
          </if>
        </action>
        <action label="cancel">
          <audio>
            <prompt id="ES2135_out_02">
              <prompt-segments>
                <audiofile src="ES2135_out_02.wav" text="No problem, I won t change anything"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.cancelESNSwap" value="true" type="Boolean"/>
          <session-mapping key="ES2135_counter" expr="0"/>
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="more_info">
          <audio>
            <prompt id="ES2135_out_03">
              <prompt-segments>
                <audiofile src="ES2135_out_03.wav" text="Sure Here s some more information"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="ES2135_out_04">
              <prompt-segments>
                <audiofile src="ES2135_out_04.wav" text="Embedded SIM, eSIM, is just a SIM card gone digital It is built into your phone, so you won t lose or damage it With an eSIM you can simply get rid of the physical SIM card To activate it I ll just use your account information, no need to insert a physical SIM"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="ES2135_out_05">
              <prompt-segments>
                <audiofile src="ES2135_out_05.wav" text="So, which would you like? Say  continue with new eSIM  or  cancel "/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="ES2135_counter" expr="ES2135_counter+1"/>
          <action next="ES2135_AskContinueWithESIM_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES2135_ini_01" cond="ES2135_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2135_ini_01.wav" text="I see the phone you want to switch to only has an eSIM, which means that you won t be able to use your current SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="ES2135_counter == 0">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2135_ini_02" cond="ES2135_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2135_ini_02.wav" text="Now, say  continue with new eSIM ,  cancel , or  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES2135_ini_01" cond="ES2135_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2135_ini_01.wav" text="I see the phone you want to switch to only has an eSIM, which means that you won t be able to use your current SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="ES2135_counter == 0">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2135_ini_02" cond="ES2135_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2135_ini_02.wav" text="Now, say  continue with new eSIM ,  cancel , or  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES2135_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2135_nm1_01.wav" text="Say  continue with new eSIM ,  cancel , or  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2135_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2135_nm2_01.wav" text="Say  continue with new eSIM  or press 1,  cancel  or press 2, or  more information  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2135_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2135_nm2_01.wav" text="Say  continue with new eSIM  or press 1,  cancel  or press 2, or  more information  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2135_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2135_nm1_01.wav" text="Say  continue with new eSIM ,  cancel , or  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2135_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2135_nm2_01.wav" text="Say  continue with new eSIM  or press 1,  cancel  or press 2, or  more information  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2135_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2135_nm2_01.wav" text="Say  continue with new eSIM  or press 1,  cancel  or press 2, or  more information  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="ES2135_ini_01" cond="ES2135_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2135_ini_01.wav" text="I see the phone you want to switch to only has an eSIM, which means that you won t be able to use your current SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="ES2135_counter == 0">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2135_ini_02" cond="ES2135_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2135_ini_02.wav" text="Now, say  continue with new eSIM ,  cancel , or  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="ES2135_AskContinueWithESIM_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES2135_AskContinueWithESIM_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="ES2135_AskContinueWithESIM_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="ES2140_AskContinueWithPSIMYN_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="ES2140_out_01">
              <prompt-segments>
                <audiofile src="ES2140_out_01.wav" text="Great"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.deviceChangeTargetSIMChosen" expr="'PSIM'"/>
          <if cond="GlobalVars.activityCode == 'BSS'">
            <session-mapping key="GlobalVars.fromESNSwapDialog" value="true" type="Boolean"/>
            <gotodialog next="CareESNSwap_Main#ES1007_SimSwappedBlocked_DM"/>
            <else>
              <gotodialog next="CareESNSwap_Main#ES1305_ICCIDTransitionSkipSBI_DM"/>
            </else>
          </if>
        </action>
        <action label="false">
          <audio>
            <prompt id="ES2140_out_02">
              <prompt-segments>
                <audiofile src="ES2140_out_02.wav" text="No problem, I won t change anything"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.cancelESNSwap" value="true" type="Boolean"/>
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES2140_ini_01">
                <prompt-segments>
                  <audiofile src="ES2140_ini_01.wav" text="I see the phone you want to switch to only has a physical SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2140_ini_02">
                <prompt-segments>
                  <audiofile src="ES2140_ini_02.wav" text="Do you want to continue switching your phone with a new physical SIM?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES2140_ini_01">
                <prompt-segments>
                  <audiofile src="ES2140_ini_01.wav" text="I see the phone you want to switch to only has a physical SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2140_ini_02">
                <prompt-segments>
                  <audiofile src="ES2140_ini_02.wav" text="Do you want to continue switching your phone with a new physical SIM?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES2140_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2140_nm1_01.wav" text="Do you want to continue switching your phone with a new physical SIM?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2140_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2140_nm2_01.wav" text="To continue switching your phone using a new physical SIM card, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2140_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2140_nm2_01.wav" text="To continue switching your phone using a new physical SIM card, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2140_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2140_nm1_01.wav" text="Do you want to continue switching your phone with a new physical SIM?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2140_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2140_nm2_01.wav" text="To continue switching your phone using a new physical SIM card, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2140_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2140_nm2_01.wav" text="To continue switching your phone using a new physical SIM card, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="ES2140_ini_01">
                <prompt-segments>
                  <audiofile src="ES2140_ini_01.wav" text="I see the phone you want to switch to only has a physical SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2140_ini_02">
                <prompt-segments>
                  <audiofile src="ES2140_ini_02.wav" text="Do you want to continue switching your phone with a new physical SIM?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="ES2140_AskContinueWithPSIMYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES2140_AskContinueWithPSIMYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="ES2145_ChooseESIMOrNewPSIM_DM" type="CUST">
      <success>
        <action label="ESIM">
          <audio>
            <prompt id="ES2145_out_01">
              <prompt-segments>
                <audiofile src="ES2145_out_01.wav" text="Great"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="ES2145_counter" expr="0"/>
          <session-mapping key="GlobalVars.deviceChangeTargetSIMChosen" expr="'ESIM'"/>
          <if cond="GlobalVars.activityCode == 'BSS'">
            <session-mapping key="GlobalVars.fromESNSwapDialog" value="true" type="Boolean"/>
            <gotodialog next="CareESNSwap_Main#ES1007_SimSwappedBlocked_DM"/>
            <else>
              <action next="ES2205_AskWiFiConnected_DM"/>
            </else>
          </if>
        </action>
        <action label="PSIM">
          <audio>
            <prompt id="ES2145_out_02">
              <prompt-segments>
                <audiofile src="ES2145_out_02.wav" text="Great"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="ES2145_counter" expr="0"/>
          <session-mapping key="GlobalVars.deviceChangeTargetSIMChosen" expr="'PSIM'"/>
          <if cond="GlobalVars.activityCode == 'BSS'">
            <session-mapping key="GlobalVars.fromESNSwapDialog" value="true" type="Boolean"/>
            <gotodialog next="CareESNSwap_Main#ES1007_SimSwappedBlocked_DM"/>
            <else>
              <gotodialog next="CareESNSwap_Main#ES1305_ICCIDTransitionSkipSBI_DM"/>
            </else>
          </if>
        </action>
        <action label="more_info">
          <audio>
            <prompt id="ES2145_out_03">
              <prompt-segments>
                <audiofile src="ES2145_out_03.wav" text="Sure Here s some more information"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="ES2145_out_04">
              <prompt-segments>
                <audiofile src="ES2145_out_04.wav" text="For the phone you re switching to, you can choose between using a new physical SIM, or use an embedded SIM Embedded SIM, eSIM, is just a SIM card gone digital It is built into your phone, so you won t lose or damage it With an eSIM you can simply get rid of the physical SIM card To activate it I ll just use your account information, no need to insert a physical SIM"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="ES2145_out_05">
              <prompt-segments>
                <audiofile src="ES2145_out_05.wav" text="So, which would you like to use? Say   eSIM  or  new physical SIM "/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="ES2145_counter" expr="ES2145_counter+1"/>
          <action next="ES2145_ChooseESIMOrNewPSIM_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES2145_ini_01" cond="ES2145_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2145_ini_01.wav" text="I see the phone you want to switch to has an eSIM in addition to a physical SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="ES2145_counter == 0">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2145_ini_02" cond="ES2145_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2145_ini_02.wav" text="Which one would you like to use say  eSIM , or  new physical SIM  You can also say  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES2145_ini_01" cond="ES2145_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2145_ini_01.wav" text="I see the phone you want to switch to has an eSIM in addition to a physical SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="ES2145_counter == 0">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2145_ini_02" cond="ES2145_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2145_ini_02.wav" text="Which one would you like to use say  eSIM , or  new physical SIM  You can also say  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES2145_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2145_nm1_01.wav" text="Which one would you like to use say  eSIM , or  new physical SIM  You can also say  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2145_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2145_nm2_01.wav" text="Say  eSIM  or press 1, or  new physical SIM  or press 2 You can also say  more information  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2145_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2145_nm2_01.wav" text="Say  eSIM  or press 1, or  new physical SIM  or press 2 You can also say  more information  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2145_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2145_nm1_01.wav" text="Which one would you like to use say  eSIM , or  new physical SIM  You can also say  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2145_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2145_nm2_01.wav" text="Say  eSIM  or press 1, or  new physical SIM  or press 2 You can also say  more information  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2145_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2145_nm2_01.wav" text="Say  eSIM  or press 1, or  new physical SIM  or press 2 You can also say  more information  or press 3"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="ES2145_out_03">
                <prompt-segments>
                  <audiofile src="ES2145_out_03.wav" text="Sure Here s some more information"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2145_out_04">
                <prompt-segments>
                  <audiofile src="ES2145_out_04.wav" text="For the phone you re switching to, you can choose between using a new physical SIM, or use an embedded SIM Embedded SIM, eSIM, is just a SIM card gone digital It is built into your phone, so you won t lose or damage it With an eSIM you can simply get rid of the physical SIM card To activate it I ll just use your account information, no need to insert a physical SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2145_out_05">
                <prompt-segments>
                  <audiofile src="ES2145_out_05.wav" text="So, which would you like to use? Say   eSIM  or  new physical SIM "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="ES2145_ChooseESIMOrNewPSIM_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES2145_ChooseESIMOrNewPSIM_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="ES2145_ChooseESIMOrNewPSIM_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="ES2150_ChooseESIMOrNewPSIMOrOldPSIM_DM" type="CUST">
      <success>
        <action label="ESIM">
          <session-mapping key="GlobalVars.deviceChangeTargetSIMChosen" expr="'ESIM'"/>
          <if cond="GlobalVars.activityCode == 'BSS'">
            <session-mapping key="GlobalVars.fromESNSwapDialog" value="true" type="Boolean"/>
            <gotodialog next="CareESNSwap_Main#ES1007_SimSwappedBlocked_DM"/>
            <else>
              <audio>
                <prompt id="ES2150_out_01">
                  <prompt-segments>
                    <audiofile src="ES2150_out_01.wav" text="Great"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="ES2150_counter" expr="0"/>
              <action next="ES2205_AskWiFiConnected_DM"/>
            </else>
          </if>
        </action>
        <action label="new_PSIM">
          <if cond="GlobalVars.activityCode == 'BSS'">
            <audio>
              <prompt id="ES2150_out_07">
                <prompt-segments>
                  <audiofile src="ES2150_out_07.wav" text="Got it"/>
                </prompt-segments>
              </prompt>
            </audio>
            <session-mapping key="GlobalVars.fromESNSwapDialog" value="true" type="Boolean"/>
            <gotodialog next="CareESNSwap_Main#ES1007_SimSwappedBlocked_DM"/>
            <else>
              <audio>
                <prompt id="ES2150_out_02">
                  <prompt-segments>
                    <audiofile src="ES2150_out_02.wav" text="Great"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="GlobalVars.deviceChangeTargetSIMChosen" expr="'PSIM'"/>
              <audio>
                <prompt id="silence_250ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="ES2150_counter" expr="0"/>
              <gotodialog next="CareESNSwap_Main#ES1305_ICCIDTransitionSkipSBI_DM"/>
            </else>
          </if>
        </action>
        <action label="current_SIM">
          <audio>
            <prompt id="ES2150_out_03">
              <prompt-segments>
                <audiofile src="ES2150_out_03.wav" text="Great"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.deviceChangeTargetSIMChosen" expr="'PSIM'"/>
          <session-mapping key="GlobalVars.usingOldSIMForSwap" value="true" type="Boolean"/>
          <session-mapping key="ES2150_counter" expr="0"/>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="ES1405_ValidateDeviceTransition_PP"/>
        </action>
        <action label="more_info">
          <audio>
            <prompt id="ES2150_out_04">
              <prompt-segments>
                <audiofile src="ES2150_out_04.wav" text="Sure Here s some more information"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="ES2150_out_05">
              <prompt-segments>
                <audiofile src="ES2150_out_05.wav" text="For the phone you re switching to, you can choose to keep your current physical SIM, use a new physical SIM, or use an embedded SIM Embedded SIM, eSIM, is just a SIM card gone digital It is built into your phone, so you won t lose or damage it With an eSIM you can simply get rid of the physical SIM card To activate it I ll just use your account information, no need to insert a physical SIM"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="ES2150_out_06">
              <prompt-segments>
                <audiofile src="ES2150_out_06.wav" text="So, which would you like to use? Say   eSIM ,  new physical SIM , or  current SIM "/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="ES2150_counter" expr="ES2150_counter+1"/>
          <action next="ES2150_ChooseESIMOrNewPSIMOrOldPSIM_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES2150_ini_01" cond="ES2150_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2150_ini_01.wav" text="I see the phone you want to switch to has an eSIM in addition to a physical SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="ES2150_counter == 0">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2150_ini_02" cond="ES2150_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2150_ini_02.wav" text="Which one would you like to use say  eSIM ,  new physical SIM  or  current SIM  You can also say  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES2150_ini_01" cond="ES2150_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2150_ini_01.wav" text="I see the phone you want to switch to has an eSIM in addition to a physical SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="ES2150_counter == 0">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2150_ini_02" cond="ES2150_counter == 0">
                <prompt-segments>
                  <audiofile src="ES2150_ini_02.wav" text="Which one would you like to use say  eSIM ,  new physical SIM  or  current SIM  You can also say  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES2150_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2150_nm1_01.wav" text="Which one would you like to use say  eSIM ,  new physical SIM  or  current SIM  You can also say  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2150_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2150_nm2_01.wav" text="Say  eSIM  or press 1,  new physical SIM  or press 2, or  current SIM  or press 3 You can also say  more information  or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2150_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2150_nm2_01.wav" text="Say  eSIM  or press 1,  new physical SIM  or press 2, or  current SIM  or press 3 You can also say  more information  or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2150_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2150_nm1_01.wav" text="Which one would you like to use say  eSIM ,  new physical SIM  or  current SIM  You can also say  more information "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2150_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2150_nm2_01.wav" text="Say  eSIM  or press 1,  new physical SIM  or press 2, or  current SIM  or press 3 You can also say  more information  or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2150_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2150_nm2_01.wav" text="Say  eSIM  or press 1,  new physical SIM  or press 2, or  current SIM  or press 3 You can also say  more information  or press 4"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="ES2150_out_04">
                <prompt-segments>
                  <audiofile src="ES2150_out_04.wav" text="Sure Here s some more information"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2150_out_05">
                <prompt-segments>
                  <audiofile src="ES2150_out_05.wav" text="For the phone you re switching to, you can choose to keep your current physical SIM, use a new physical SIM, or use an embedded SIM Embedded SIM, eSIM, is just a SIM card gone digital It is built into your phone, so you won t lose or damage it With an eSIM you can simply get rid of the physical SIM card To activate it I ll just use your account information, no need to insert a physical SIM"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2150_out_06">
                <prompt-segments>
                  <audiofile src="ES2150_out_06.wav" text="So, which would you like to use? Say   eSIM ,  new physical SIM , or  current SIM "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="ES2150_ChooseESIMOrNewPSIMOrOldPSIM_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES2150_ChooseESIMOrNewPSIMOrOldPSIM_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="3000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="ES2150_ChooseESIMOrNewPSIMOrOldPSIM_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="ES2205_AskWiFiConnected_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="ES2205_out_01">
              <prompt-segments>
                <audiofile src="ES2205_out_01.wav" text="Okay"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="ES1410_ValidateDeviceAndPlan_DB_DA"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="ES2205_out_02">
              <prompt-segments>
                <audiofile src="ES2205_out_02.wav" text="Alright"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="ES2225_Transfer_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES2205_ini_01">
                <prompt-segments>
                  <audiofile src="ES2205_ini_01.wav" text="After this call you will need a WiFi connection to complete your swap Will you be able to connect to WiFi from your new phone?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES2205_ini_01">
                <prompt-segments>
                  <audiofile src="ES2205_ini_01.wav" text="After this call you will need a WiFi connection to complete your swap Will you be able to connect to WiFi from your new phone?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES2205_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2205_nm1_01.wav" text="Can you connect your new phone to WiFi?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2205_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2205_nm2_01.wav" text="If you have WIFi connection, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2205_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2205_nm2_01.wav" text="If you have WIFi connection, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2205_nm1_01">
                <prompt-segments>
                  <audiofile src="ES2205_nm1_01.wav" text="Can you connect your new phone to WiFi?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2205_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2205_nm2_01.wav" text="If you have WIFi connection, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2205_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2205_nm2_01.wav" text="If you have WIFi connection, say  yes  or press 1 Otherwise, say  no  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="ES2205_ini_01">
                <prompt-segments>
                  <audiofile src="ES2205_ini_01.wav" text="After this call you will need a WiFi connection to complete your swap Will you be able to connect to WiFi from your new phone?"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="ES2205_AskWiFiConnected_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES2205_AskWiFiConnected_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="ES2210_EIDOrIMEI2NotReturned_PP">
      <session-mapping key="eid" value="GlobalVars.ValidateDevice.eid" type="String"/>
      <audio>
        <prompt id="ES2210_out_01" cond="(eid == undefined || eid == '' || eid == null || eid == 'null')">
          <prompt-segments>
            <audiofile src="ES2210_out_01.wav" text="Sorry, it seems that I cannot activate an eSIM at this time"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms" cond="(eid == undefined || eid == '' || eid == null || eid == 'null')">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ES2210_out_02">
          <prompt-segments>
            <audiofile src="ES2210_out_02.wav" text="You ll need to talk to an agent to switch your phone"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="ES2225_Transfer_SD"/>
    </play-state>

    <data-access-state id="ES2215_ReserveESIM_DB_DA">
      <session-mapping key="simNumber" value="" type="String"/>
      <session-mapping key="transactionType" value="SIMCHANGE" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="primaryIMEI" value="GlobalVars.imeiSerialNumber" type="String"/>
      <if cond="GlobalVars.deviceChangeTargetSIMChosen == 'ESIM' || GlobalVars.ValidateDevice.simType == 'ESIM'">
        <session-mapping key="simNumber" expr="GlobalVars.ValidateDevice.eid"/>
        <else>
          <session-mapping key="simNumber" expr="GlobalVars.iccidSerialNumber"/>
        </else>
      </if>
      <data-access id="AddOrUpdateEsim" classname="com.nuance.metro.dataaccess.AddOrUpdateEsim">
        <inputs>
          <input-variable name="simNumber"/>
          <input-variable name="transactionType"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="AddOrUpdateEsim.status == 'Success'">
          <action next="ES2220_ESIMActivationSMS_SD"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <action next="ES2225_Transfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <subdialog-state id="ES2220_ESIMActivationSMS_SD">
      <gotodialog next="ActivationSMS_Dialog"/>
      <action next="ES2220_ESIMActivationSMS_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES2220_ESIMActivationSMS_SD_return_CS">
      <action next="ES1505_SubmitSwapTransition_DM"/>
    </custom-state>

    <subdialog-state id="ES2225_Transfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="ES2225_Transfer_SD_return"/>
    </subdialog-state>
  </dialog>
  