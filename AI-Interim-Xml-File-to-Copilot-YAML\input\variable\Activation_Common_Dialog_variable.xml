<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="trn" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="iccid" value="empty" type="String"/>
  <session-mapping key="ignoreDsDeviceProperty" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="simNumber" value="empty" type="String"/>
  <session-mapping key="transactionType" value="empty" type="String"/>
  <session-mapping key="GetBCSParameters.activations_enable_prepaid_methods" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.activationEntryPoint" value="care" type="string"/>
  <session-mapping key="GlobalVars.BYODNotInInventory" value="true" type="boolean"/>
  <session-mapping key="imeiFailChecksum" value="true" type="boolean"/>
  <session-mapping key="interpretation.dm_root" value="instructions" type="string"/>
  <session-mapping key="GlobalVars.imeiFailChecksum" value="true" type="boolean"/>
  <session-mapping key="iccidFailChecksum" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.iccidFailChecksum" value="true" type="boolean"/>
  <session-mapping key="GetDeviceStatus.status" value="Success" type="string"/>
  <session-mapping key="GlobalVars.iccidEquipmentActive" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.simExpired" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.iccidEquipmentInNegativeList" value="true" type="string"/>
  <session-mapping key="GlobalVars.iccidIsEligibleForActivation" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.iccidInvalidDevice" value="true" type="boolean"/>
  <session-mapping key="GetDeviceStatus.isDualSimDevice" value="true" type="boolean"/>
  <session-mapping key="GetDeviceStatus.actualSupportedImei" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.activationType" value="eSIM" type="string"/>
  <session-mapping key="GetDeviceStatus.iccidNotInInventory" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.activationResult" value="transfer" type="string"/>
  <session-mapping key="activations_enable_address_collection" value="true" type="boolean"/>
  <session-mapping key="GetDeviceStatus.deviceType" value="IOT" type="string"/>
  <session-mapping key="GetBCSParameters.activations_esim_disable" value="true" type="boolean"/>
  <session-mapping key="GetDeviceStatus.simType" value="ESIM" type="string"/>
  <session-mapping key="GetDeviceStatus.imeiList" value="null" type="string"/>
  <session-mapping key="imeiList.length" value="2" type="string"/>
  <session-mapping key="BYODRegistered" value="false" type="string"/>
  <session-mapping key="GlobalVars.imeiEquipmentActive" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.imeiEquipmentInNegativeList" value="true" type="boolean"/>
  <session-mapping key="GetDeviceStatus.eid" value="undefined" type="string"/>
  <session-mapping key="simType" value="ESIM" type="string"/>
  <session-mapping key="AddOrUpdateEsim.status" value="Success" type="string"/>
</session-mappings>
