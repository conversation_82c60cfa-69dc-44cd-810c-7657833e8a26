memorize below yaml format
- kind: SendActivity
  id: sendActivity_REPLACE_THIS
  activity:
    text:
      - Custom state. Please implement.
    speak:
      - Custom state. Please implement.
- kind: GotoAction
  id: TestValue
  actionId: pm46030_CheckPaymentMethods_DS

or 
- kind: SendActivity
  id: sendActivity_REPLACE_THIS
  activity:
    text:
      - Custom state. Please implement.
    speak:
      - Custom state. Please implement.
- kind: BeginDialog
  id: hl0040_NLCS_SD
  dialog: topic.InvokeCS_dvxml

prefix = topic	

also use these instruction while conversion 
1) if input has "action" tag then kind should be "GotoAction" and generate below format.
- kind: SendActivity
  id: sendActivity_REPLACE_THIS
  activity:
    text:
      - Custom state. Please implement.
    speak:
      - Custom state. Please implement.
- kind: GotoAction
  id: TestValue
  actionId: pm46030_CheckPaymentMethods_DS

where "actionId" should be "next" value.
2) if input has "view" tag then kind should be "BeginDialog" and generate this format.
- kind: SendActivity
  id: sendActivity_REPLACE_THIS
  activity:
    text:
      - Custom state. Please implement.
    speak:
      - Custom state. Please implement.
- kind: BeginDialog
  id: hl0040_NLCS_SD
  dialog: topic.InvokeCS_dvxml

where "dialog" should be "name" value of view tag without "/" and convert '.' into '_' and add prefix in the starting like dialog: topic.InvokeCS_dvxml.
like InvokeCS.dvxml to InvokeCS_dvxml
3) replace "id" value with "custom-state" tag id.
4) if there is no data like below in the input then just create below yaml for it.
input : 
    <custom-state id="EndApplication_CS">
      <exit/>
      <event>
        <exit/>
      </event>
    </custom-state>

Output :
- kind: SendActivity
  id: sendActivity_REPLACE_THIS
  activity:
    text:
      - Custom state. Please implement.
    speak:
      - Custom state. Please implement.
- kind: EndConversation
  id: EndApplication_CS

5) if there is "gotodialog" in input then take its "next" value and put it in "dialog" with topic. prefix like below or example#4.
input : 
<custom-state id="TransferCaller_CS">
      <gotodialog next="Transfer_Dialog" />
</custom-state>

Output :
- kind: SendActivity
  id: sendActivity_REPLACE_THIS
  activity:
    text:
      - Custom state. Please implement.
    speak:
      - Custom state. Please implement.
- kind: BeginDialog
  id: TransferCaller_CS
  dialog: topic.Transfer_Dialog

6) if there is any logic or if/else in the input, then simply create the  "SendActivity" yaml with "Custom state. Please implement." text and the next part as per the above instructions.
6) do not create yaml for other things like script, if/else and so on. 
7) indentation for next yaml should be same as first one or as memorized yaml
8) do not provide duplicate yaml.. generate only for provided inputs.

### Example 1:
**Input XML:**
```xml
<custom-state id="TestValue">
<script className="com.nuance.humana.scripts.ChooseFirstAccount"/>
<action next="pm46030_CheckPaymentMethods_DS"> </action>
</custom-state>
```

**Output yaml:**
```yaml
- kind: SendActivity
  id: sendActivity_REPLACE_THIS
  activity:
    text:
      - Custom state. Please implement.
    speak:
      - Custom state. Please implement.
- kind: GotoAction
  id: TestValue
  actionId: pm46030_CheckPaymentMethods_DS
```
### Example 2:
**Input XML:**
```xml
<custom-state id="hl0040_NLCS_SD">
<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
<script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
<script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
<session-mapping key="authenticationRequired" value="true"/>
<session-mapping key="formID" value="Invoke_NLCS_SD"/>
<view name="/InvokeCS.dvxml"/>
```

**Output yaml:**
```yaml
- kind: SendActivity
  id: sendActivity_REPLACE_THIS
  activity:
    text:
      - Custom state. Please implement.
    speak:
      - Custom state. Please implement.
  - kind: BeginDialog
    id: hl0040_NLCS_SD
    dialog: topic.InvokeCS_dvxml
```
### Example 3:
**Input XML:**
```xml
    <custom-state id="EndApplication_CS">
      <exit/>
      <event>
        <exit/>
      </event>
    </custom-state>
```

**Output yaml:**
```yaml
- kind: SendActivity
  id: sendActivity_REPLACE_THIS
  activity:
    text:
      - Custom state. Please implement.
    speak:
      - Custom state. Please implement.
- kind: EndConversation
  id: EndApplication_CS
```

### Example 4:
**Input XML:**
```xml
<custom-state id="TransferCaller_CS">
      <gotodialog next="Transfer_Dialog" />
</custom-state>
```
**Output yaml:**
```yaml
- kind: SendActivity
  id: sendActivity_REPLACE_THIS
  activity:
    text:
      - Custom state. Please implement.
    speak:
      - Custom state. Please implement.
- kind: BeginDialog
  id: TransferCaller_CS
  dialog: topic.Transfer_Dialog
```


### Example 5:
**Input XML:**
```xml

<custom-state id="MM1060_GoToLogin_SD_return_CS">
      <if cond="GlobalVars.callType == 'my_account'">
        <gotodialog next="MainMenu_Main" />
        <else>
          <gotodialog next="MainMenu_Routing_Dialog" />
        </else>
      </if>
    </custom-state>
```
**Output yaml:**
```yaml
- kind: SendActivity
  id: sendActivity_REPLACE_THIS
  activity:
    text:
      - Custom state. Please implement.
    speak:
      - Custom state. Please implement.
- kind: BeginDialog
  id: MM1060_GoToLogin_SD_return_CS
  dialog: topic.MainMenu_Routing_Dialog
```
