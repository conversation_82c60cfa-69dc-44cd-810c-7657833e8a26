<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="TechnicalSupport_Dialog">
    <decision-state id="TS1001_CheckIntent_DS">
      <session-mapping key="heardHotspotExceededInfo " value="false" type="Boolean"/>
      <session-mapping key="heardDataExceededInfo " value="false" type="Boolean"/>
      <if cond="GlobalVars.tag == 'troubleshooting-hotspot'">
        <action next="TS1105_CheckHaveDataUsage_JDA"/>
        <else>
          <action next="TS1005_AskSupportType_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="TS1005_AskSupportType_DM" type="CUST">
      <success>
        <action label="mobile">
          <session-mapping key="techSupportType" expr="'mobiledata'"/>
          <action next="TS1105_CheckHaveDataUsage_JDA"/>
        </action>
        <action label="hotspot">
          <session-mapping key="techSupportType" expr="'hotspot'"/>
          <action next="TS1105_CheckHaveDataUsage_JDA"/>
        </action>
        <action label="something-else_troubleshooting">
          <session-mapping key="techSupportType" expr="'other'"/>
          <action next="TS1010_TechSupportTransfer_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TS1005_ini_01">
                <prompt-segments>
                  <audiofile text="Which do you need help with? Say mobile data, hotspot, or something else" src="TS1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TS1005_ini_01">
                <prompt-segments>
                  <audiofile text="Which do you need help with? Say mobile data, hotspot, or something else" src="TS1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TS1005_ini_01">
                <prompt-segments>
                  <audiofile text="Which do you need help with? Say mobile data, hotspot, or something else" src="TS1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say mobile data or press 1, or hotspot or press 2 For all other help, say something else or press 3" src="TS1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say mobile data or press 1, or hotspot or press 2 For all other help, say something else or press 3" src="TS1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1005_ini_01">
                <prompt-segments>
                  <audiofile text="Which do you need help with? Say mobile data, hotspot, or something else" src="TS1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say mobile data or press 1, or hotspot or press 2 For all other help, say something else or press 3" src="TS1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say mobile data or press 1, or hotspot or press 2 For all other help, say something else or press 3" src="TS1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TS1005_ini_01">
                <prompt-segments>
                  <audiofile text="Which do you need help with? Say mobile data, hotspot, or something else" src="TS1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="TS1005_AskSupportType_DM.grxml" count="1"/>
          <dtmfgrammars filename="TS1005_AskSupportType_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="TS1010_TechSupportTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
    </subdialog-state>
    <decision-state id="TS1105_CheckHaveDataUsage_DS">
      <session-mapping key="dataUsage_dataFound" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataFound:false" type="String"/>
      <session-mapping key="isLoggedIn" value="GlobalVars.loggedIn" type="String"/>
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <if cond="dataUsage_dataFound == true">
        <action next="TS1115_CheckSupportType_JDA"/>
        <elseif cond="(aniMatch == 'true' || aniMatch == true) || (isLoggedIn == true || isLoggedIn == 'true')">
          <action next="TS1110_GetDataUsage_DB_DA"/>
        </elseif>
        <else>
          <action next="TS1115_CheckSupportType_JDA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="TS1110_GetDataUsage_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.mdn:''" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.payDate:''" type="String"/>
      <session-mapping key="isBARTET" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.isBARTET:false" type="String"/>
      <session-mapping key="billCycleLength" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.billCycleLength:0" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="isHint" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.parentDeviceType == 'INT')? true : false" type="String"/>
      <session-mapping key="isSuspended" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" type="String"/>
      <session-mapping key="getTopUpHistory" value="isHint == true ? false : true" type="String"/>
      <data-access id="GetDataUsage" classname="com.nuance.metro.dataaccess.GetDataUsage">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="getTopUpHistory"/>
          <input-variable name="payDate"/>
          <input-variable name="sessionId"/>
          <input-variable name="isSuspended"/>
          <input-variable name="isBARTET"/>
        </inputs>
        <outputs>
          <output-variable name="dataFound"/>
          <output-variable name="dataUsed"/>
          <output-variable name="dataCap"/>
          <output-variable name="isUnlimited"/>
          <output-variable name="dataPercentage"/>
          <output-variable name="dataUsage_dataFound"/>
          <output-variable name="dataUsage_dataUsed"/>
          <output-variable name="dataUsage_dataCap"/>
          <output-variable name="dataUsage_dataUsed_KB"/>
          <output-variable name="dataUsage_dataCap_KB"/>
          <output-variable name="dataUsage_isUnlimited"/>
          <output-variable name="dataUsage_dataPercentage"/>
          <output-variable name="topUpHotSpot_dataFound"/>
          <output-variable name="topUpHotSpot_dataUsed"/>
          <output-variable name="topUpHotSpot_dataCap"/>
          <output-variable name="topUpHotSpot_dataUsed_KB"/>
          <output-variable name="topUpHotSpot_dataCap_KB"/>
          <output-variable name="topUpHotSpot_isUnlimited"/>
          <output-variable name="topUpHotSpot_dataPercentage"/>
          <output-variable name="topUp_feature1_code"/>
          <output-variable name="topUp_feature1_featureName_en"/>
          <output-variable name="topUp_feature1_featureName_es"/>
          <output-variable name="topUp_feature1_description_en"/>
          <output-variable name="topUp_feature1_description_es"/>
          <output-variable name="topUp_feature1_price"/>
          <output-variable name="numPaidTopUps"/>
          <output-variable name="numGoodWillTopUps "/>
          <output-variable name="featureDetailsPromptURL_en"/>
          <output-variable name="featureDetailsPromptURL_es "/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetDataUsage.status == 'Success'">
          <session-mapping key="GlobalVars.GetDataUsageInfo" expr="GetDataUsage"/>
          <session-mapping key="willExceedDataLimit" expr="GetDataUsage.willExceedDataLimit"/>
          <session-mapping key="numPaidTopUps" expr="GetDataUsage.numPaidTopUps"/>
          <session-mapping key="numGoodWillTopUps" expr="GetDataUsage.numGoodWillTopUps"/>
          <session-mapping key="topUpHotSpot_dataFound" expr="GetDataUsage.topUpHotSpot_dataFound"/>
        </if>
        <action next="TS1115_CheckSupportType_JDA"/>
      </action>
      </data-access-state>

    <decision-state id="TS1115_CheckSupportType_DS">
      <if cond="techSupportType == 'mobiledata'">
        <action next="TS1205_CheckDataExceeded_JDA"/>
        <else>
          <action next="TS1305_CheckHotspotExceeded_JDA"/>
        </else>
      </if>
    </decision-state>

    <play-state id="TS1210_PlayMobileTips_PP">
      <session-mapping key="dataUsage_dataFound" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataFound:false" type="String"/>
      <audio>
        <prompt id="TS1210_out_01">
          <prompt-segments>
            <audiofile text="Heres a few easy things to check  " src="TS1210_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1210_out_02" cond="dataUsage_dataFound == false">
          <prompt-segments>
            <audiofile text="First, make sure that you have high speed data left If youre all out until your next payment, you can get a top up to stay at top speed! " src="TS1210_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms" cond="dataUsage_dataFound == false">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1210_out_03" cond="dataUsage_dataFound == false">
          <prompt-segments>
            <audiofile text="Next" src="TS1210_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1210_out_04" cond="dataUsage_dataFound == true">
          <prompt-segments>
            <audiofile text="First" src="TS1210_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1210_out_05">
          <prompt-segments>
            <audiofile text="Check our coverage map at metro by t dash mobile dot com You might be somewhere where we dont have great data coverage yet! " src="TS1210_out_05.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1210_out_06">
          <prompt-segments>
            <audiofile text="Then, look in your phone settings and make sure you re not in Airplane Mode, and that you re NOT on wifi If you re having trouble connecting to wifi, you ll need to get help from your internet provider " src="TS1210_out_06.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1210_out_07">
          <prompt-segments>
            <audiofile text="And last, you can fix most connection problems by turning your phone off for a few minutes, and turning it on again It really works! " src="TS1210_out_07.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="TS1215_MobileTipsWrapMenu_DM"/>
    </play-state>

    <decision-state id="TS1205_CheckDataExceeded_DS">
      <session-mapping key="dataUsage_dataFound" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataFound:false" type="String"/>
      <session-mapping key="isUnlimited" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.isUnlimited:false" type="String"/>
      <session-mapping key="dataUsage_dataUsed" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataUsed:0" type="String"/>
      <session-mapping key="numPaidTopUps" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.numPaidTopUps:0" type="String"/>
      <session-mapping key="numGoodWillTopUps" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.numGoodWillTopUps:0" type="String"/>
      <session-mapping key="dataUsage_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataPercentage:0" type="String"/>
      <session-mapping key="topUpHotSpot_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataPercentage:0" type="String"/>
      <session-mapping key="topUpHotSpot_dataFound" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataFound:false" type="String"/>
      <if cond="dataUsage_dataFound == true">
        <if cond="isUnlimited == true">
          <if cond="dataUsage_dataUsed &gt;= 35">
            <action next="TS1220_PlayDataUsageExceeded_PP"/>
            <else>
              <action next="TS1210_PlayMobileTips_PP"/>
            </else>
          </if>
          <elseif cond="isUnlimited == false &amp;&amp; dataUsage_dataPercentage &gt;= 100 ">
            <if cond=" topUpHotSpot_dataFound==true &amp;&amp; (numPaidTopUps != 0 || numGoodWillTopUps!=0)">
              <if cond="topUpHotSpot_dataPercentage &gt;= 100 ">
                <action next="TS1220_PlayDataUsageExceeded_PP"/>
                <else>
                  <action next="TS1210_PlayMobileTips_PP"/>
                </else>
              </if>
              <else>
                <action next="TS1220_PlayDataUsageExceeded_PP"/>
              </else>
            </if>
          </elseif>
          <else>
            <action next="TS1210_PlayMobileTips_PP"/>
          </else>
        </if>
        <else>
          <action next="TS1210_PlayMobileTips_PP"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="TS1215_MobileTipsWrapMenu_DM" type="CUST">
      <success>
        <action label="repeat">
          <action next="TS1210_PlayMobileTips_PP"/>
        </action>
        <action label="tried">
          <audio>
            <prompt id="TS1215_out_01">
              <prompt-segments>
                <audiofile text="Okay, our tech support team will take it from here!" src="TS1215_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="TS1010_TechSupportTransfer_SD"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TS1215_ini_01">
                <prompt-segments>
                  <audiofile text="Say repeat that, or I ve already tried If you re all set, you can just hang up " src="TS1215_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TS1215_ini_01">
                <prompt-segments>
                  <audiofile text="Say repeat that, or I ve already tried If you re all set, you can just hang up " src="TS1215_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1215_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those tips again, say repeat or press 1 Or say I ve already tried or press 2 " src="TS1215_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1215_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those tips again, say repeat or press 1 Or say I ve already tried or press 2 " src="TS1215_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1215_ini_01">
                <prompt-segments>
                  <audiofile text="Say repeat that, or I ve already tried If you re all set, you can just hang up " src="TS1215_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1215_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those tips again, say repeat or press 1 Or say I ve already tried or press 2 " src="TS1215_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1215_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those tips again, say repeat or press 1 Or say I ve already tried or press 2 " src="TS1215_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TS1215_ini_01">
                <prompt-segments>
                  <audiofile text="Say repeat that, or I ve already tried If you re all set, you can just hang up " src="TS1215_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="TS1215_MobileTipsWrapMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="TS1215_MobileTipsWrapMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="TS1220_PlayDataUsageExceeded_PP">
      <session-mapping key="isUnlimited" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.isUnlimited:false" type="String"/>
      <session-mapping key="numPaidTopUps" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.numPaidTopUps:0" type="String"/>
      <session-mapping key="numGoodWillTopUps" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.numGoodWillTopUps:0" type="String"/>
      <session-mapping key="topupEligibility" value="GlobalVars.GetAccountDetails.topupEligibility" type="String"/>
      <audio>
        <prompt id="TS1220_out_01" cond="isUnlimited == true">
          <prompt-segments>
            <audiofile text="It looks like you ve used over 35 gigabytes of high-speed data this month, so your mobile data will be slower until your next payment date " src="TS1220_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1220_out_02" cond="isUnlimited != true &amp;&amp; (numPaidTopUps != 0 || numGoodWillTopUps !=0)">
          <prompt-segments>
            <audiofile text="I see you re out of high-speed data, and you ve used all of your top-up data So your mobile connection will be slower until your next payment date, or until you add another top-up " src="TS1220_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1220_out_03" cond="isUnlimited != true &amp;&amp; (!(numPaidTopUps != 0 || numGoodWillTopUps !=0)) &amp;&amp; (topupEligibility == 'ELIGIBLE')">
          <prompt-segments>
            <audiofile text="I see you're out of high-speed data So your mobile connection will be slower until your next payment date, or until you add a top-up " src="TS1220_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1220_out_04" cond="isUnlimited != true &amp;&amp; (!(numPaidTopUps != 0 || numGoodWillTopUps !=0)) &amp;&amp; !(topupEligibility == 'ELIGIBLE')">
          <prompt-segments>
            <audiofile text="I see you re out of high-speed data So your mobile connection will be slower until your next payment date " src="TS1220_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="heardDataExceededInfo" value="true" type="Boolean"/>
      <action next="TS1225_DataUsageExceededWrapMenu_DM"/>
    </play-state>

    <dm-state id="TS1225_DataUsageExceededWrapMenu_DM" type="CUST">
      <session-mapping key="isUnlimited" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.isUnlimited:false" type="String"/>
      <session-mapping key="topupEligibility" value="GlobalVars.GetAccountDetails.topupEligibility" type="String"/>
      <session-mapping key="collection_grammar1" value="TS1225_DataUsageExceededWrapMenu_DM.jsp" type="String"/>
      <session-mapping key="collection_dtmfgrammar1" value="TS1225_DataUsageExceededWrapMenu_DM_dtmf.jsp" type="String"/>
      <session-mapping key="allowedResponses" value="" type="String"/>
      <session-mapping key="allowedResponsesDtmf" value="" type="String"/>
      <success>
        <action label="buy-data_topup">
          <audio>
            <prompt id="TS1225_out_01">
              <prompt-segments>
                <audiofile text="You got it! " src="TS1225_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.tag" expr="'buy-data_topup'"/>
          <action next="TS1405_GetPIN_SD"/>
        </action>
        <action label="tips">
          <audio>
            <prompt id="TS1225_out_02">
              <prompt-segments>
                <audiofile text="Sure thing! " src="TS1225_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="TS1210_PlayMobileTips_PP"/>
        </action>
        <action label="change-plan">
          <audio>
            <prompt id="TS1225_out_03">
              <prompt-segments>
                <audiofile text="You got it! " src="TS1225_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
          <action next="TS1505_GetPIN_SD"/>
        </action>
        <action label="repeat">
          <action next="TS1220_PlayDataUsageExceeded_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TS1225_ini_01" cond="isUnlimited == true">
                <prompt-segments>
                  <audiofile text="If you think the problem is something else, say 'more tips' If you're done, you can simply hang up" src="TS1225_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_ini_02" cond="isUnlimited != true &amp;&amp; topupEligibility == 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="For more data, say 'get a top-up', or 'change plan' If you think the problem is something else, say 'more tips' If you're done, you can simply hang up" src="TS1225_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_ini_03" cond="isUnlimited != true &amp;&amp; topupEligibility != 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="I can offer you plans with more data, or if you think the problem is something else, I can give you some more tips So, say 'change plan' or 'more tips'  If you're done, you can simply hang up" src="TS1225_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TS1225_ini_01" cond="isUnlimited == true">
                <prompt-segments>
                  <audiofile text="If you think the problem is something else, say 'more tips' If you're done, you can simply hang up" src="TS1225_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_ini_02" cond="isUnlimited != true &amp;&amp; topupEligibility == 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="For more data, say 'get a top-up', or 'change plan' If you think the problem is something else, say 'more tips' If you're done, you can simply hang up" src="TS1225_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_ini_03" cond="isUnlimited != true &amp;&amp; topupEligibility != 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="I can offer you plans with more data, or if you think the problem is something else, I can give you some more tips So, say 'change plan' or 'more tips'  If you're done, you can simply hang up" src="TS1225_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_01" cond="isUnlimited == true">
                <prompt-segments>
                  <audiofile text="If your problem is NOT from a slow connection, say 'more tips' or press 1 If you're done, you can just hang up" src="TS1225_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_02" cond="isUnlimited != true &amp;&amp; topupEligibility == 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="If your connection is too slow and you want more data, say 'get a top-up' or press 1, or 'change plan' or press 2 If your issue is something else, say 'more tips' or press 3 If you're done for now, you can just hang up" src="TS1225_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_03" cond="isUnlimited != true &amp;&amp; topupEligibility != 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="If your connection is too slow and you want more data, say 'change plan' or press 1 If your issue is something else, say 'more tips' or press 2 If you're done for now, you can just hang up" src="TS1225_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_01" cond="isUnlimited == true">
                <prompt-segments>
                  <audiofile text="If your problem is NOT from a slow connection, say 'more tips' or press 1 If you're done, you can just hang up" src="TS1225_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_02" cond="isUnlimited != true &amp;&amp; topupEligibility == 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="If your connection is too slow and you want more data, say 'get a top-up' or press 1, or 'change plan' or press 2 If your issue is something else, say 'more tips' or press 3 If you're done for now, you can just hang up" src="TS1225_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_03" cond="isUnlimited != true &amp;&amp; topupEligibility != 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="If your connection is too slow and you want more data, say 'change plan' or press 1 If your issue is something else, say 'more tips' or press 2 If you're done for now, you can just hang up" src="TS1225_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_ini_01" cond="isUnlimited == true">
                <prompt-segments>
                  <audiofile text="If you think the problem is something else, say 'more tips' If you're done, you can simply hang up" src="TS1225_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_ini_02" cond="isUnlimited != true &amp;&amp; topupEligibility == 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="For more data, say 'get a top-up', or 'change plan' If you think the problem is something else, say 'more tips' If you're done, you can simply hang up" src="TS1225_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_ini_03" cond="isUnlimited != true &amp;&amp; topupEligibility != 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="I can offer you plans with more data, or if you think the problem is something else, I can give you some more tips So, say 'change plan' or 'more tips'  If you're done, you can simply hang up" src="TS1225_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_01" cond="isUnlimited == true">
                <prompt-segments>
                  <audiofile text="If your problem is NOT from a slow connection, say 'more tips' or press 1 If you're done, you can just hang up" src="TS1225_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_02" cond="isUnlimited != true &amp;&amp; topupEligibility == 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="If your connection is too slow and you want more data, say 'get a top-up' or press 1, or 'change plan' or press 2 If your issue is something else, say 'more tips' or press 3 If you're done for now, you can just hang up" src="TS1225_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_03" cond="isUnlimited != true &amp;&amp; topupEligibility != 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="If your connection is too slow and you want more data, say 'change plan' or press 1 If your issue is something else, say 'more tips' or press 2 If you're done for now, you can just hang up" src="TS1225_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_01" cond="isUnlimited == true">
                <prompt-segments>
                  <audiofile text="If your problem is NOT from a slow connection, say 'more tips' or press 1 If you're done, you can just hang up" src="TS1225_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_02" cond="isUnlimited != true &amp;&amp; topupEligibility == 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="If your connection is too slow and you want more data, say 'get a top-up' or press 1, or 'change plan' or press 2 If your issue is something else, say 'more tips' or press 3 If you're done for now, you can just hang up" src="TS1225_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_nm2_03" cond="isUnlimited != true &amp;&amp; topupEligibility != 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="If your connection is too slow and you want more data, say 'change plan' or press 1 If your issue is something else, say 'more tips' or press 2 If you're done for now, you can just hang up" src="TS1225_nm2_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TS1225_ini_01" cond="isUnlimited == true">
                <prompt-segments>
                  <audiofile text="If you think the problem is something else, say 'more tips' If you're done, you can simply hang up" src="TS1225_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_ini_02" cond="isUnlimited != true &amp;&amp; topupEligibility == 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="For more data, say 'get a top-up', or 'change plan' If you think the problem is something else, say 'more tips' If you're done, you can simply hang up" src="TS1225_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1225_ini_03" cond="isUnlimited != true &amp;&amp; topupEligibility != 'ELIGIBLE'">
                <prompt-segments>
                  <audiofile text="I can offer you plans with more data, or if you think the problem is something else, I can give you some more tips So, say 'change plan' or 'more tips'  If you're done, you can simply hang up" src="TS1225_ini_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="TS1225_DataUsageExceededWrapMenu_DM.jsp" count="1"/>
          <dtmfgrammars filename="TS1225_DataUsageExceededWrapMenu_DM_dtmf.jsp" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="TS1305_CheckHotspotExceeded_DS">
      <session-mapping key="isUnlimited" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.isUnlimited:false" type="String"/>
      <session-mapping key="topUpHotSpot_dataFound" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataFound:false" type="String"/>
      <session-mapping key="topUpHotSpot_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataPercentage:0" type="String"/>
      <session-mapping key="dataUsage_dataPercentage" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_dataPercentage:0" type="String"/>
      <session-mapping key="numPaidTopUps" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.numPaidTopUps:0" type="String"/>
      <session-mapping key="numGoodWillTopUps" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.numGoodWillTopUps:0" type="String"/>
      <if cond="topUpHotSpot_dataFound == true">
        <if cond="isUnlimited == true">
          <if cond="topUpHotSpot_dataPercentage &gt;= 100">
            <action next="TS1320_PlayHotspotExceeded_PP"/>
            <else>
              <action next="TS1310_PlayHotspotTips_PP"/>
            </else>
          </if>
          <elseif cond="isUnlimited == false &amp;&amp; dataUsage_dataPercentage &gt;= 100 ">
            <if cond="numPaidTopUps != 0 || numGoodWillTopUps!=0">
              <if cond="topUpHotSpot_dataPercentage &gt;= 100 ">
                <action next="TS1320_PlayHotspotExceeded_PP"/>
                <else>
                  <action next="TS1310_PlayHotspotTips_PP"/>
                </else>
              </if>
              <else>
                <action next="TS1320_PlayHotspotExceeded_PP"/>
              </else>
            </if>
          </elseif>
          <else>
            <action next="TS1310_PlayHotspotTips_PP"/>
          </else>
        </if>
        <else>
          <action next="TS1310_PlayHotspotTips_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="TS1310_PlayHotspotTips_PP">
      <session-mapping key="topUpHotSpot_dataFound" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUpHotSpot_dataFound:false" type="String"/>
      <audio>
        <prompt id="TS1310_out_01" cond="heardHotspotExceededInfo == true">
          <prompt-segments>
            <audiofile text="Just in case, here's a few more things to check " src="TS1310_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1310_out_02" cond="heardHotspotExceededInfo != true">
          <prompt-segments>
            <audiofile text="Here's a few easy things to check " src="TS1310_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1310_out_03" cond="topUpHotSpot_dataFound == false">
          <prompt-segments>
            <audiofile text="First, check your account to make sure that your plan supports mobile hotspot, and that you still have hotspot data left If you are all out until your next payment, you can get a top-up to keep tethering! " src="TS1310_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms" cond="topUpHotSpot_dataFound == false">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1310_out_04" cond="topUpHotSpot_dataFound == false">
          <prompt-segments>
            <audiofile text="Next " src="TS1310_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1310_out_05" cond="topUpHotSpot_dataFound == true">
          <prompt-segments>
            <audiofile text="First" src="TS1310_out_05.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1310_out_06">
          <prompt-segments>
            <audiofile text="Look in your phone settings and make sure you actually have Hotspot data enabled on your device " src="TS1310_out_06.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1310_out_07">
          <prompt-segments>
            <audiofile text="Then, check our coverage map at metro by t dash mobile dot com You might be somewhere where we dont have great data coverage yet! " src="TS1310_out_07.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1310_out_08">
          <prompt-segments>
            <audiofile text="And last, you can fix most connection problems by turning your phone off for a few minutes, and turning it on again It really works! " src="TS1310_out_08.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="TS1315_HotspotTipsWrapMenu_DM"/>
    </play-state>

    <dm-state id="TS1315_HotspotTipsWrapMenu_DM" type="CUST">
      <success>
        <action label="repeat">
          <action next="TS1310_PlayHotspotTips_PP"/>
        </action>
        <action label="tried">
          <audio>
            <prompt id="TS1315_out_01">
              <prompt-segments>
                <audiofile text="Okay, our tech support team will take it from here" src="TS1315_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="TS1010_TechSupportTransfer_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TS1315_ini_01">
                <prompt-segments>
                  <audiofile text="Say repeat that, or I have already tried If you are all set, you can just hang up " src="TS1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TS1315_ini_01">
                <prompt-segments>
                  <audiofile text="Say repeat that, or I have already tried If you are all set, you can just hang up " src="TS1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1315_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those tips again, say repeat or press 1 Or say I have already tried or press 2 " src="TS1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1315_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those tips again, say repeat or press 1 Or say I have already tried or press 2 " src="TS1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1315_ini_01">
                <prompt-segments>
                  <audiofile text="Say repeat that, or I have already tried If you are all set, you can just hang up " src="TS1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1315_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those tips again, say repeat or press 1 Or say I have already tried or press 2 " src="TS1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1315_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those tips again, say repeat or press 1 Or say I have already tried or press 2 " src="TS1315_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TS1315_ini_01">
                <prompt-segments>
                  <audiofile text="Say repeat that, or I have already tried If you are all set, you can just hang up " src="TS1315_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="TS1315_HotspotTipsWrapMenu_DM.grxml" count="1"/>
          <dtmfgrammars filename="TS1315_HotspotTipsWrapMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="TS1320_PlayHotspotExceeded_PP">
      <session-mapping key="numPaidTopUps" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.numPaidTopUps:0" type="String"/>
      <session-mapping key="numGoodWillTopUps" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.numGoodWillTopUps:0" type="String"/>
      <session-mapping key="topupEligibility" value="GlobalVars.GetAccountDetails.topupEligibility" type="String"/>
      <audio>
        <prompt id="TS1320_out_01" cond="(numPaidTopUps != 0 || numGoodWillTopUps !=0)">
          <prompt-segments>
            <audiofile text="Looks like you are out of hotspot data So it wont work until your next payment date, or until you add another top-up " src="TS1320_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1320_out_02" cond="(!(numPaidTopUps != 0 || numGoodWillTopUps !=0))&amp;&amp;(topupEligibility == 'ELIGIBLE')">
          <prompt-segments>
            <audiofile text="Looks like you are out of hotspot data So it wont work until your next payment date, or until you add a top-up " src="TS1320_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="TS1320_out_03" cond="(!(numPaidTopUps != 0 || numGoodWillTopUps !=0))&amp;&amp;!(topupEligibility == 'ELIGIBLE')">
          <prompt-segments>
            <audiofile text="Looks like you are out of hotspot data So it wont work until your next payment date" src="TS1320_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="heardHotspotExceededInfo" value="true" type="Boolean"/>
      <action next="TS1325_CheckTopupEligible_JDA"/>
    </play-state>

    <decision-state id="TS1325_CheckTopupEligible_DS">
      <session-mapping key="topupEligibility" value="GlobalVars.GetAccountDetails.topupEligibility" type="String"/>
      <if cond="topupEligibility == 'ELIGIBLE'">
        <action next="TS1330_OfferTopupYN_DM"/>
        <else>
          <action next="TS1310_PlayHotspotTips_PP"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="TS1330_OfferTopupYN_DM" type="CUST">
      <success>
        <action label="yes">
          <audio>
            <prompt id="TS1330_out_01">
              <prompt-segments>
                <audiofile text="You got it! " src="TS1330_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.tag" expr="'buy-data_topup'"/>
          <action next="TS1405_GetPIN_SD"/>
        </action>
        <action label="no">
          <audio>
            <prompt id="TS1330_out_02">
              <prompt-segments>
                <audiofile text="No problem" src="TS1330_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.tag" expr="undefined"/>
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="tips">
          <audio>
            <prompt id="TS1330_out_03">
              <prompt-segments>
                <audiofile text="Sure tips" src="TS1330_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="TS1310_PlayHotspotTips_PP"/>
        </action>
        <action label="repeat">
          <action next="TS1320_PlayHotspotExceeded_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TS1330_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to add one now? Or say more tips " src="TS1330_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TS1330_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to add a top-up now, to get your hotspot connection back? You can also say more tips " src="TS1330_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1330_nm2_01">
                <prompt-segments>
                  <audiofile text="To add a top-up, say yes or press 1 Otherwise, say no or press 2, or more tips - 3 If you are done for now, you can just hang up " src="TS1330_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1330_nm2_01">
                <prompt-segments>
                  <audiofile text="To add a top-up, say yes or press 1 Otherwise, say no or press 2, or more tips - 3 If you are done for now, you can just hang up " src="TS1330_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1330_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to add a top-up now, to get your hotspot connection back? You can also say more tips " src="TS1330_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1330_nm2_01">
                <prompt-segments>
                  <audiofile text="To add a top-up, say yes or press 1 Otherwise, say no or press 2, or more tips - 3 If you are done for now, you can just hang up " src="TS1330_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="TS1330_nm2_01">
                <prompt-segments>
                  <audiofile text="To add a top-up, say yes or press 1 Otherwise, say no or press 2, or more tips - 3 If you are done for now, you can just hang up " src="TS1330_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TS1330_ini_01">
                <prompt-segments>
                  <audiofile text="Would you like to add one now? Or say more tips " src="TS1330_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="TS1330_OfferTopupYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="TS1330_OfferTopupYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="TS1405_GetPIN_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="TS1405_GetPIN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="TS1405_GetPIN_SD_return_CS">
      <action next="TS1410_DataTopup_SD"/>
    </custom-state>

    <subdialog-state id="TS1410_DataTopup_SD">
      <gotodialog next="DataTopUp_Main_Dialog"/>
      <action next="TS1410_DataTopup_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="TS1410_DataTopup_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <subdialog-state id="TS1505_GetPIN_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="TS1505_GetPIN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="TS1505_GetPIN_SD_return_CS">
      <action next="TS1510_ChangePlan_SD"/>
    </custom-state>

    <subdialog-state id="TS1510_ChangePlan_SD">
      <gotodialog next="RatePlan_Main_Dialog"/>
      <action next="TS1510_ChangePlan_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="TS1510_ChangePlan_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  