<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="DSG_Main_Dialog">
    <decision-state id="DS3000_CheckDID_DS">
      <session-mapping key="GlobalVars.SaidActivateAtLogin" value="false" type="Boolean"/>
      <if cond="GlobalVars.enteredFrom == 'DSG'">
        <action next="DS3005_AskLanguage_DM"/>
        <else>
          <action next="DS3001_GoToMainMenu_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="DS3001_GoToMainMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
      <action next="DS3001_GoToMainMenu_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DS3001_GoToMainMenu_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <dm-state id="DS3005_AskLanguage_DM" type="CUST">
      <session-mapping key="GlobalVars.DS3005Pressed" expr="''"/>
      <success>
        <action label="Spanish">
          <session-mapping key="GlobalVars.DS3005Pressed" expr="'9'"/>
          <session-mapping key="language" expr="'es-US'"/>
          <gotodialog next="DSG_Main#DS3007_CheckBCSSuccess_DS"/>
        </action>
        <action label="default" next="DS3007_CheckBCSSuccess_JDA"/>
        <action label="default" next="DS3007_CheckBCSSuccess_JDA"/>
      </success>
      <catch>
        <session-mapping key="language" expr="'en-US'"/>
        <session-mapping key="GlobalVars.DS3005Pressed" expr="'retry'"/>
        <action next="DS3007_CheckBCSSuccess_JDA"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550" inputmodes="dtmf">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DS3005_ini_01">
                <prompt-segments>
                  <audiofile text="You've reached Metro's DSG! Para continuar en espanol, marque el nueve" src="DS3005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DS3005_ini_01">
                <prompt-segments>
                  <audiofile text="You've reached Metro's DSG! Para continuar en espanol, marque el nueve" src="DS3005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="DS3005_AskLanguage_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms" inputmodes="dtmf"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms" inputmodes="dtmf"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550" inputmodes="dtmf">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms" inputmodes="dtmf"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="DS3007_CheckBCSSuccess_DS">
      <if cond="GlobalVars.isBCSSuccess">
        <if cond="GlobalVars.DS3005Pressed == 'retry'">
          <action next="DS3010_DealerOrCustomer_DM"/>
          <elseif cond="GlobalVars.DS3005Pressed == '9'">
            <action next="DS3010_DealerOrCustomer_DM"/>
          </elseif>
        </if>
      </if>
      <action next="DS3020_DSGAppCustomer_XR_CS"/>
    </decision-state>

    <dm-state id="DS3010_DealerOrCustomer_DM" type="CUST">
      <success>
        <session-mapping key="GlobalVars.playedCCPA" value="true" type="Boolean"/>
        <action label="star" next="DS3015_PlayCustomerMessage_PP"/>
        <action label="default" next="DS3011_DSGOptions_DM">
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'DSGapp_Dealer_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'DSGapp_Dealer_Spanish'"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
        <action label="repeat" next="DS3005_AskLanguage_DM"/>
      </success>
      <catch>
        <audio>
          <prompt id="DS3010_maxretries4_01">
            <prompt-segments>
              <audiofile text="Please enter your dealer code now If you are a Metro PCS Customer, press star " src="DS3010_maxretries4_01.wav"/>
            </prompt-segments>
          </prompt>
        </audio>
        <session-mapping key="GlobalVars.playGoodbyeMessage" value="true" type="Boolean"/>
        <action next="DS3090_GoToGoodBye_SD"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550" inputmodes="dtmf">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DS3010_ini_01">
                <prompt-segments>
                  <audiofile text="You've reached Metro's DSG  As part of this call, and subsequent interactions from this call, we may collect information about you to improve service To learn more, go to metro by T dash Mobile dot com slash privacy If you are a Metro customer, please press Star If you're a Metro Authorized Dealer, please enter your dealer code now " src="DS3010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DS3010_ini_01">
                <prompt-segments>
                  <audiofile text="You've reached Metro's DSG  As part of this call, and subsequent interactions from this call, we may collect information about you to improve service To learn more, go to metro by T dash Mobile dot com slash privacy If you are a Metro customer, please press Star If you're a Metro Authorized Dealer, please enter your dealer code now " src="DS3010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DS3010_ni1_01">
                <prompt-segments>
                  <audiofile src="DS3010_ni1_01.wav" text="Please enter your dealer code now If you are a Metro  Customer, press star"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="DS3010_nm1_01">
                <prompt-segments>
                  <audiofile text="I couldn't find that dealer code Try entering it again " src="DS3010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="DS3010_DealerOrCustomer_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms" inputmodes="dtmf"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms" inputmodes="dtmf"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550" inputmodes="dtmf">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms" inputmodes="dtmf"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="DS3011_DSGOptions_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <action label="request-extension_dsg" next="DS4001_CheckContext_JDA">
          <session-mapping key="callType" expr="'dsgExtension'"/>
        </action>
        <action label="edge_pw" next="DS3025_PlayDealerMessages_PP">
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'DSGapp_EDGEPW_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'DSGapp_EDGEPW_Spanish'"/>
            </else>
          </if>
        </action>
        <action label="access" next="DS3025_PlayDealerMessages_PP">
          <if cond="language == 'en-US'">
            <session-mapping key="TransferTag" expr="'DSGapp_AccessSupport_English'"/>
            <else>
              <session-mapping key="TransferTag" expr="'DSGapp_AccessSupport_Spanish'"/>
            </else>
          </if>
        </action>
        <action label="default" next="DS3025_PlayDealerMessages_PP"/>
        <action label="operator"/>
        <action label="repeat" next="DS3005_AskLanguage_DM"/>
      </success>
      <catch>
        <action next="DS3025_PlayDealerMessages_PP"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DS3011_ini_01">
                <prompt-segments>
                  <audiofile src="DS3011_ini_01.wav" text="For payment extensions, press 1 To reset an EDGE password, press 2 For Dealer Access Support, press 3 For anything else, hold the line"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DS3011_ini_01">
                <prompt-segments>
                  <audiofile src="DS3011_ini_01.wav" text="For payment extensions, press 1 To reset an EDGE password, press 2 For Dealer Access Support, press 3 For anything else, hold the line"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DS3011_ini_01">
                <prompt-segments>
                  <audiofile src="DS3011_ini_01.wav" text="For payment extensions, press 1 To reset an EDGE password, press 2 For Dealer Access Support, press 3 For anything else, hold the line"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <dtmfgrammars filename="DS3011_DSGOptions_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="DS3015_PlayCustomerMessage_PP">
      <audio>
        <prompt id="DS3015_out_01">
          <prompt-segments>
            <audiofile text="Just a moment" src="DS3015_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="DS3020_DSGAppCustomer_XR_CS"/>
    </play-state>

    <custom-state id="DS3020_DSGAppCustomer_XR_CS">
      <action next="DS3095_GoToInitialHandling_SD"/>
    </custom-state>

    <custom-state id="DS3020_DSGAppCustomer_XRReturn_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <play-state id="DS3025_PlayDealerMessages_PP">
      <if cond="TransferTag == 'DSGapp_EDGEPW_English' || TransferTag == 'DSGapp_EDGEPW_Spanish' || TransferTag == 'DSGapp_AccessSupport_English' || TransferTag == 'DSGapp_AccessSupport_Spanish' || TransferTag == 'DSGapp_CancelLine_English' || TransferTag == 'DSGapp_CancelLine_Spanish'">
        <audio>
          <prompt id="DS3025_out_02">
            <prompt-segments>
              <audiofile src="DS3025_out_02.wav" text="One moment while I transfer your account"/>
            </prompt-segments>
          </prompt>
        </audio>
        <else>
          <audio>
            <prompt id="DS3025_out_01">
              <prompt-segments>
                <audiofile text="ust as a reminder, dealers should be performing all dealer support transactions like Activations, Reactivations, ESN Changes and Rate Plan Changes at their dealer location before calling the DSG If the customer is not able to receive an OTP to any line on the account, please verify that customer ID matches account info and contact Dealer Access Support The customer must also be able to provide their PIN to make any changes to the account Just a moment while I transfer your account to the Dealer Support Group" src="DS3025_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </else>
      </if>
      <session-mapping key="GlobalVars.playTransferMessage" value="false" type="Boolean"/>
      <action next="DS3035_GoToCallTransfer_SD"/>
    </play-state>

    <subdialog-state id="DS3035_GoToCallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="DS3035_GoToCallTransfer_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="DS3035_GoToCallTransfer_SDReturn_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <subdialog-state id="DS3090_GoToGoodBye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="DS3090_GoToGoodBye_SD_return"/>
    </subdialog-state>
    <subdialog-state id="DS3095_GoToInitialHandling_SD">
      <gotodialog next="InitialHandling_Main_Dialog"/>
      <action next="DS3095_GoToInitialHandling_TerminalSD_return_CS"/>
    </subdialog-state>
    <custom-state id="DS3095_GoToInitialHandling_TerminalSD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <decision-state id="DS4001_CheckContext_DS">
      <action next="DS4005_PlayCredentialsInfo_PP"/>
    </decision-state>

    <play-state id="DS4005_PlayCredentialsInfo_PP">
      <session-mapping key="accountPinToggleOn" value="GlobalVars.GetBCSParameters.accountPinToggleOn" type="String"/>
      <audio>
        <if cond="accountPinToggleOn == true">
          <prompt id="DS4005_out_01">
            <prompt-segments>
              <audiofile text="Before we can apply an extension, you'll need to provide the customer's 10-digit phone number, and 6-to-15-digit account PIN We will also collect a one time security code" src="DS4005_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="DS4005_out_02">
              <prompt-segments>
                <audiofile text="Before we can apply an extension, you'll need to provide the customer's 10-digit phone number, and 8-digit account PIN we will also collect a one time security code" src="DS4005_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.needMDN" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.callType" expr="'dsgExtension'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <action next="DS4010_LoginMDN_SD"/>
    </play-state>

    <subdialog-state id="DS4010_LoginMDN_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="DS4010_LoginMDN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DS4010_LoginMDN_SD_return_CS">
      <action next="DS4015_CheckAccountSettings_JDA"/>
    </custom-state>

    <decision-state id="DS4015_CheckAccountSettings_DS">
      <if cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'active')">
        <action next="DS4030_AccountIssue_PP"/>
        <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; (GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ8' || GlobalVars.GetAccountDetails.securityQuestionCode == 'SQ9'))">
          <session-mapping key="GlobalVars.cti_AuthStatus" expr="getctiAuthStatus(GlobalVars.GetCTIParameters.ctiAuthStatus, 'not_authenticated_high_security_account')"/>
          <action next="DS4030_AccountIssue_PP"/>
        </elseif>
        <elseif cond="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.eligibleForUnhotlineCheck == false)">
          <action next="DS4030_AccountIssue_PP"/>
        </elseif>
        <else>
          <action next="DS4020_CheckUnhotlineEligibility_DB_DA"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="DS4020_CheckUnhotlineEligibility_DB_DA">
      <session-mapping key="accountNumber" value="GlobalVars.GetAccountDetails.accountNumber" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="CheckUnhotlineEligibility" classname="com.nuance.metro.dataaccess.CheckUnhotlineEligibility">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="accountNumber" mask="true"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="isEligible"/>
          <output-variable name="numberOfCases"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.CheckUnhotlineEligibility" expr="CheckUnhotlineEligibility"/>
        <if cond="(CheckUnhotlineEligibility.isEligible == 'true' || CheckUnhotlineEligibility.isEligible == true)">
          <session-mapping key="GlobalVars.extensionAllowed" value="true" type="Boolean"/>
          <action next="DS4025_AccountEligible_PP"/>
          <else>
            <action next="DS4030_AccountIssue_PP"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <play-state id="DS4025_AccountEligible_PP">
      <audio>
        <prompt id="DS4025_out_01">
          <prompt-segments>
            <audiofile text="The account *is* eligible for an extension " src="DS4025_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.securityRequired" value="false" type="Boolean"/>
      <action next="DS4105_LoginPIN_SD"/>
    </play-state>

    <play-state id="DS4030_AccountIssue_PP">
      <session-mapping key="highSecurity" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.highSecurity:false" type="String"/>
      <session-mapping key="accountStatus" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.accountStatus:''" type="String"/>
      <session-mapping key="isEligibleForUnhotlineCheck" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.eligibleForUnhotlineCheck:false" type="String"/>
      <audio>
        <if cond="(highSecurity == true)">
          <prompt id="DS4030_out_01">
            <prompt-segments>
              <audiofile text="I'll take you to an agent for next steps One moment " src="DS4030_out_01.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="DS4030_out_02" cond="(accountStatus == 'active')">
              <prompt-segments>
                <audiofile text="This account is *not* currently suspended " src="DS4030_out_02.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="DS4030_out_03" cond="(accountStatus != 'active')">
              <prompt-segments>
                <audiofile text="This account is *not* eligible for an extension at this time " src="DS4030_out_03.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="DS4030_out_04">
              <prompt-segments>
                <audiofile text="I'll take you to an agent to discuss the options One moment " src="DS4030_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <session-mapping key="GlobalVars.playTransferMessage" value="false" type="Boolean"/>
      <action next="DS3035_GoToCallTransfer_SD"/>
    </play-state>

    <subdialog-state id="DS4105_LoginPIN_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="DS4105_LoginPIN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DS4105_LoginPIN_SD_return_CS">
      <session-mapping key="GlobalVars.extensionEntryPoint" expr="'DSG'"/>
      <action next="DS4110_ApplyExtension_SD"/>
    </custom-state>

    <subdialog-state id="DS4110_ApplyExtension_SD">
      <gotodialog next="ApplyExtension_Main_Dialog"/>
      <action next="DS4110_ApplyExtension_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DS4110_ApplyExtension_SD_return_CS">
      <if cond="(GlobalVars.extensionSuccess == true)">
        <action next="DS3090_GoToGoodBye_SD"/>
        <else>
          <action next="DS4115_ExtensionFailure_PP"/>
        </else>
      </if>
    </custom-state>

    <play-state id="DS4115_ExtensionFailure_PP">
      <audio>
        <prompt id="DS4115_out_01">
          <prompt-segments>
            <audiofile text="Please advise the customer that they can get an extension without speaking with an agent, or make their payment, simply by calling 611 They can also pay online at metrobyt-mobilecom, or with the myMetro app on their phone " src="DS4115_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DS4115_out_02">
          <prompt-segments>
            <audiofile src="DS4115_out_02.wav" text="Thank you for calling Metro s DSG Goodbye"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.playGoodbyeMessage" value="false" type="Boolean"/>
      <action next="DS3090_GoToGoodBye_SD"/>
    </play-state>

    <play-state id="NR5010_PlayTransferPrompt_PP">
      <audio>
        <prompt id="NR5010_out_01">
          <prompt-segments>
            <audiofile text="Just a second I ll transfer you " src="NR5010_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.playTransferMessage" value="false" type="Boolean"/>
      <action next="NR5025_GoToCallTransfer_SD"/>
    </play-state>

    <subdialog-state id="NR5025_GoToCallTransfer_SD">
      <session-mapping key="TransferTag" expr="'NRH_Transfer'"/>
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="NR5025_GoToCallTransfer_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="NR5025_GoToCallTransfer_SDReturn_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <subdialog-state id="NR5030_GoToInitialHandling_SD">
      <gotodialog next="InitialHandling_Main_Dialog"/>
      <action next="NR5030_GoToInitialHandling_TerminalSD_return_CS"/>
    </subdialog-state>
    <custom-state id="NR5030_GoToInitialHandling_TerminalSD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

  </dialog>
  