<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="banId" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="clearCache" value="empty" type="String"/>
  <session-mapping key="multiLineDetails" value="empty" type="String"/>
  <session-mapping key="accountStatus" value="active" type="string"/>
  <session-mapping key="GlobalVars.accountFutureRequestInd" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.tag" value="change-payment_date" type="string"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="suspended" type="string"/>
  <session-mapping key="GetAccountDetails.eligibleForBillCycleReset" value="true" type="string"/>
  <session-mapping key="GlobalVars.callType" value="make_pmt" type="string"/>
  <session-mapping key="currentDate" value="31" type="integer"/>
  <session-mapping key="loggedIn" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="boolean"/>
  <session-mapping key="callType" value="make_pmt" type="string"/>
  <session-mapping key="GlobalVars.securityRequired" value="true" type="boolean"/>
  <session-mapping key="BillCycleReset.status" value="Success" type="string"/>
  <session-mapping key="GetSubscriberDetails.status" value="Success" type="string"/>
  <session-mapping key="newbillCycle" value="29" type="integer"/>
</session-mappings>
