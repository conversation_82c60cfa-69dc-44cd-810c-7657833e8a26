<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="ValidateCardOptions.cardStatus" value="amount_under_min" type="string"/>
  <session-mapping key="GlobalVars.authorizationFailureHandling" value="reconfirmDetails" type="string"/>
  <session-mapping key="GlobalVars.tryOtherCardReason" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.callType" value="activate" type="string"/>
  <session-mapping key="GetPaymentOptions.hasEWallet" value="true" type="string"/>
  <session-mapping key="GetPaymentOptions.isWalletPopulated" value="true" type="string"/>
  <session-mapping key="GlobalVars.abandonEWalletAuth" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.guestPayment" value="false" type="boolean"/>
  <session-mapping key="paymentAmount" value="35" type="integer"/>
  <session-mapping key="GlobalVars.preferredPaymentMethod" value="credit" type="string"/>
  <session-mapping key="needResidualPayment" value="true" type="boolean"/>
  <session-mapping key="fromAnotherPaymentMenu" value="false" type="boolean"/>
  <session-mapping key="offerPrepaid" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.activationResult" value="goodbye" type="string"/>
  <session-mapping key="GlobalVars.payingWithEWallet" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.authFailure" value="true" type="boolean"/>
  <session-mapping key="payingWithEWallet" value="true" type="boolean"/>
  <session-mapping key="walletConvenienceFee" value="0" type="string"/>
  <session-mapping key="cardConvenienceFee" value="0" type="string"/>
  <session-mapping key="acceptedBCR" value="true" type="boolean"/>
  <session-mapping key="paymentErrorCount" value="0" type="integer"/>
  <session-mapping key="callType" value="resetbillcycle" type="string"/>
  <session-mapping key="GlobalVars.payingWithPrepaid" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.needResidualPayment" value="true" type="boolean"/>
</session-mappings>
