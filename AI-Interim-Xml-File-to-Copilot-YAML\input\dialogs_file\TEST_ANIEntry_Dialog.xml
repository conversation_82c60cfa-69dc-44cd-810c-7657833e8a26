<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="TEST_ANIEntry_Dialog">
    <custom-state id="Initial_Form_CS">
      <action next="TEST_ANIEntry_DM"/>
    </custom-state>

    <dm-state id="TEST_ANIEntry_DM" type="CUST">
      <success>
        <action label="228">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="228Part">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="99">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="DSG">
          <return namelist="dnis ani"/>
        </action>
        <action label="RCC">
          <return namelist="dnis ani"/>
        </action>
        <action label="NRH">
          <return namelist="dnis ani"/>
        </action>
        <action label="611">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="888">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="289">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="FiServ">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="TestCallMin">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="490">
          <return namelist="dnis ani"/>
        </action>
        <action label="491">
          <return namelist="dnis ani"/>
        </action>
        <action label="611_228_Entry_English">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="611_228_Entry_Spanish">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="888_data_collection">
          <return namelist="dnis ani"/>
        </action>
        <action label="Star_99_English">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="Star_99_Spanish">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="611_GSM">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="611_Data_Collection">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="OMNI">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="WNP">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="PWR">
          <return namelist="dnis ani"/>
        </action>
        <action label="PC">
          <return namelist="dnis ani"/>
        </action>
        <action label="VR">
          <action next="TEST_ANIEntry_DM"/>
        </action>
        <action label="default">
          <assign name="ani" expr="TEST_ANIEntry_DM.returnvalue"/>
          <assign name="GlobalVars.trn" expr="TEST_ANIEntry_DM.returnvalue"/>
          <return namelist="dnis ani"/>
        </action>
      </success>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="" text="" id="TestAniPrompt1"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <dtmfgrammars filename="TEST_ANIEntry_DM_dtmf.grxml" count="1"/>
          <dtmfgrammars filename="TEST_ANISingleEntry_DM_dtmf.grxml" count="2"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="2000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxrepeats="0" maxnomatches="0" maxnoinputs="0"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="TestAniPrompt1"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="TestAniPrompt1">
                <prompt-segments>
                  <audiofile text="Please enter the TRN to use for testing Or enter a 2 digit code for the application you want 0 1 for 6 1 1 app 0 2 for Star 2 2 8 0 3 for 8 8 8 Care 0 4 for Star 99 Care 0 5 for D S G app 0 6 for N R H app 0 7 for R C C app 0 8 for Star 228 partial 0 9 for Star 289 1 0 for Fi Serv 1 1 for Test Call Minutes 1 3 for 6 1 1 and 2 2 8 Entry English 1 4 for 6 1 1 and 2 2 8 Entry Spanish 1 5 for 8 8 8 data collection 1 6 for Star 9 9 English 1 7 for Star 9 9 Spanish 2 0 for 6 1 1 GSM 2 1 for 611 Data Collection 2 2     for OMNI 9 0 for 4 9 0 9 1 for 4 9 1 26 for VR" src="TestAniPrompt1.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  