import xml.etree.ElementTree as ET
import os


def xml_to_files(input_xml_path, output_directory):
    # Ensure the output directory exists
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)

    # Load and parse the XML data from the file
    tree = ET.parse(input_xml_path)
    root = tree.getroot()

    # Dictionary to hold content for each tag name
    tag_files_content = {}

    # Process each child element in the XML root
    for element in root:
        tag_name = element.tag

        # Initialize the content for this tag if not already
        if tag_name not in tag_files_content:
            tag_files_content[tag_name] = ""

        # Convert the element to a string and add it to the corresponding section
        element_string = ET.tostring(element, encoding='unicode')
        tag_files_content[tag_name] += element_string + "\n"

    # Write the content to separate files based on the tag name
    for tag, content in tag_files_content.items():
        filename = os.path.join(output_directory, f"{tag}.xml")
        with open(filename, 'w') as file:
            file.write(f"<root>\n{content}</root>")  # Wrap content in a root tag for valid XML
        print(f"File '{filename}' created successfully.")

    # Define the input file and output directory
input_file = "input/dialogs_file/hl00_CareSharedHL_SD_Dialog.xml"
output_dir = "input/dialogs_file/rerun"
xml_to_files(input_file, output_dir)
