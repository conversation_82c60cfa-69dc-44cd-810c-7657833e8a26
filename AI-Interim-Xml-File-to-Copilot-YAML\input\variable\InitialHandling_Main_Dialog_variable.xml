<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="clearCache" value="empty" type="String"/>
  <session-mapping key="multiLineDetails" value="empty" type="String"/>
  <session-mapping key="min" value="empty" type="String"/>
  <session-mapping key="lang" value="empty" type="String"/>
  <session-mapping key="campaignId" value="empty" type="String"/>
  <session-mapping key="GlobalVars.isBCSSuccess" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.returnToMainMenu" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.operatorRequestMidFlowNLU" value="true" type="string"/>
  <session-mapping key="GlobalVars.switchLinesSuccess" value="true" type="string"/>
  <session-mapping key="GlobalVars.enteredFrom" value="490" type="string"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="GetSubscriberDetails.accountStatus" value="active" type="string"/>
  <session-mapping key="haveMDN" value="true" type="boolean"/>
  <session-mapping key="serviceDialed" value="99" type="string"/>
  <session-mapping key="GetAccountDetails.accountRestrictIndicator" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.aniMatch" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="suspended" type="string"/>
  <session-mapping key="haveLangbasedTFN" value="true" type="boolean"/>
  <session-mapping key="QueryLanguage.status" value="Success" type="string"/>
  <session-mapping key="QueryLanguage.lang" value="unknown" type="string"/>
  <session-mapping key="GlobalVars.GetAccountDetails" value="undefined" type="string"/>
  <session-mapping key="playedCCPA" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.sbpStateCode" value="null" type="string"/>
  <session-mapping key="GlobalVars.serviceDialed" value="611" type="string"/>
  <session-mapping key="isGreetByNameEnabled" value="true" type="boolean"/>
  <session-mapping key="lang" value="en-US" type="string"/>
  <session-mapping key="dnis" value="OMNI" type="string"/>
  <session-mapping key="lastCustomerIntent" value="delaypayment" type="string"/>
  <session-mapping key="greetingPromptURL" value="null" type="string"/>
  <session-mapping key="greetingTranscript" value="null" type="string"/>
  <session-mapping key="hasDefaultAccountValues" value="true" type="boolean"/>
  <session-mapping key="accountStatus" value="active" type="string"/>
  <session-mapping key="care_enable_acct_dets_reminder_SMS" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.broadcastMessageCallingDialog" value="IH" type="string"/>
  <session-mapping key="GlobalVars.careInitialBroadcastMessagePlayed" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.outageByColo" value="true" type="boolean"/>
  <session-mapping key="GetBCSParameters.care_upfront_sms_offer_broadcast_message" value="Custom" type="string"/>
  <session-mapping key="campaignId" value="null" type="string"/>
  <session-mapping key="broadcastMessageAction" value="null" type="string"/>
  <session-mapping key="GetBCSParameters.care_upfrontInfo_broadcastMessage" value="Custom" type="string"/>
  <session-mapping key="broadcastMessage" value="Custom" type="string"/>
  <session-mapping key="GlobalVars.lastCustomerIntent" value="undefined" type="string"/>
</session-mappings>
