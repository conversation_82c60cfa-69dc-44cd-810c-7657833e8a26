memorize below yaml format
    - kind: Question
      id: hl0150_VSS_PreMenu_DM
      displayName: hl0150_VSS_PreMenu_DM
      interruptionPolicy:
        allowInterruption: true

      unrecognizedPrompt:
        speak:
          - |
            {Switch(
                true,
                
                Global.memberIDEntryAttempts = 1 &&
                (Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService"),
                "Please say or enter their member ID again",
            
                Global.memberIDEntryAttempts = 1,
                "Please say or enter their Humana member ID again",
            
                Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService",
                "Thanks, now please say or enter the member ID",
            
                "Thanks, now please say or enter the Humana member ID"
            )}
      
      alwaysPrompt: true
      variable: Global.hl0150_VSS_PreMenu_DM_reco
      prompt:
        speak:
          - |
            {Switch(
                true,
                Global.collectionCounter = 0 && Global.callComesFromAgent = true,
                "So... after the tone please say: 'At Riyad Bank my voice is my password.'",
                
                Global.collectionCounter = 0 && Global.callComesFromAgent = false,
                "So ... after the tone please say: 'At Riyad Bank my voice is my password'. (alternative: Great! To create your voice print, I'm going to ask you to repeat a phrase a few times so the system can analyze your voice. So, after the tone please say 'With Riyad Bank my voice is my password#)",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "noinput" && Global.collectionNoInputCounter = 1,
                "After the tone please say: 'At Riyad Bank my voice is my password'",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "noinput" && Global.collectionNoInputCounter = 2,
                "Sorry, I didn't hear that. After the tone, please repeat the following phrase: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "nomatch" && Global.collectionNoMatchCounter = 1,
                "Let's try that again. After the tone, please say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "nomatch" && Global.collectionNoMatchCounter = 2,
                "Sorry, it's important you repeat \*exactly\* the same phrase. After the tone, say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.repromptAfterTrainError = true && Global.collectionCounter < 4,
                "I'm having some trouble getting that. Let's try again. After the tone say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.collectionCounter = 1,
                "Again, after the tone say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.collectionCounter = 2,
                "And again, after the tone: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.collectionCounter = 3,
                "Almost there ... after the tone: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false,
                "And last time: At Riyad Bank my voice is my password.",
                
                "tone" 
            )}

      entity: StringPrebuiltEntity  
      voiceInputSettings:
        silenceDetectionTimeoutInMilliseconds: 7000
        repeatCountOnSilence: 2
        inputTimeoutResponse:
          speak:
            - |
              {Switch(
                true,
                
                Global.memberIDEntryAttempts = 1 &&
                (Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService"),
                "Please say or enter their member ID again",
            
                Global.memberIDEntryAttempts = 1,
                "Please say or enter their Humana member ID again",
            
                Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService",
                "Thanks, now please say or enter the member ID",
            
                "Thanks, now please say or enter the Humana member ID"
            )}

        defaultValueMissingAction: Escalate

    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.collectionNiOrNm_Temp
      value: undefined

    - kind: GotoAction
      id: goto_REPLACE_THIS
      actionId: ezbEjq  


prefix = topic
also use these instruction while conversion 
1) kind should always be "kind: Question"  
2) replace first "id" and "displayName" value with "dm-state" id.
3) check all "nomatchprompts" tags and fetch text from "audiofile" tag and put it under "unrecognizedPrompt" speak, one by one.
4) check "initialprompt" tag, if there is any if/else tag then convert it into powerfx like below and put it under "prompt" speak.
input : 
<initialprompt count="1">
						<audio>
							<if cond="collectionCounter == 0 &amp;&amp; callComesFromAgent == true">
								<prompt id="rb0310_ini_12_part2" bargein="false">
									<prompt-segments>
										<audiofile text="So... after the tone please say: 'At Riyad Bank my voice is my password'." src="rb0310_ini_12_part2.wav" />
									</prompt-segments>
								</prompt>
								<elseif cond="collectionCounter == 0 &amp;&amp; callComesFromAgent == false">
									<prompt id="rb0310_ini_13" bargein="false">
										<prompt-segments>
											<audiofile text="So ... after the tone please say: 'At Riyad Bank my voice is my password'. (alternative: Great! To create your voice print, I'm going to ask you to repeat a phrase a few times so the system can analyze your voice.  So, after the tone please say 'With Riyad Bank my voice is my password#)" src="rb0310_ini_13.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'noinput' &amp;&amp; collectionNoInputCounter == 1">
									<prompt id="rb0310_ini_02" bargein="false">
										<prompt-segments>
											<audiofile text="After the tone please say: 'At Riyad Bank my voice is my password'" src="rb0310_ini_02.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'noinput' &amp;&amp; collectionNoInputCounter == 2">
									<prompt id="rb0310_ini_03" bargein="false">
										<prompt-segments>
											<audiofile text="Sorry, I didn't hear that.  After the tone, please repeat the following phrase: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_03.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'nomatch' &amp;&amp; collectionNoMatchCounter == 1">
									<prompt id="rb0310_ini_04" bargein="false">
										<prompt-segments>
											<audiofile text="Let's try that again.  After the tone, please say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_04.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'nomatch' &amp;&amp; collectionNoMatchCounter == 2">
									<prompt id="rb0310_ini_05" bargein="false">
										<prompt-segments>
											<audiofile text="Sorry, it's important you repeat *exactly* the same phrase.  After the tone, say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_05.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; repromptAfterTrainError == true &amp;&amp; collectionCounter &lt; 4">
									<prompt id="rb0310_ini_06" bargein="false">
										<prompt-segments>
											<audiofile text="I'm having some trouble getting that.  Let's try again.  After the tone say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_06.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 1">
									<prompt id="rb0310_ini_07" bargein="false">
										<prompt-segments>
											<audiofile text="Again, after the tone say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_07.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 2">
									<prompt id="rb0310_ini_08" bargein="false">
										<prompt-segments>
											<audiofile text="And again, after the tone: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_08.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 3">
									<prompt id="rb0310_ini_09" bargein="false">
										<prompt-segments>
											<audiofile text="Almost there ... after the tone: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_09.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false">
									<prompt id="rb0310_ini_10" bargein="false">
										<prompt-segments>
											<audiofile text="And last time: At Riyad Bank my voice is my password." src="rb0310_ini_10.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
							</if>
							<prompt id="rb0310_ini_11" bargein="false">
								<prompt-segments>
									<audiofile text="tone" src="rb0310_ini_11.wav" />
								</prompt-segments>
							</prompt>
						</audio>
            </initialprompt>
output:
  prompt:
    speak:
      - |
        {Switch(
                true,
                Global.collectionCounter = 0 && Global.callComesFromAgent = true,
                "So... after the tone please say: 'At Riyad Bank my voice is my password.'",
                
                Global.collectionCounter = 0 && Global.callComesFromAgent = false,
                "So ... after the tone please say: 'At Riyad Bank my voice is my password'. (alternative: Great! To create your voice print, I'm going to ask you to repeat a phrase a few times so the system can analyze your voice. So, after the tone please say 'With Riyad Bank my voice is my password#)",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "noinput" && Global.collectionNoInputCounter = 1,
                "After the tone please say: 'At Riyad Bank my voice is my password'",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "noinput" && Global.collectionNoInputCounter = 2,
                "Sorry, I didn't hear that. After the tone, please repeat the following phrase: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "nomatch" && Global.collectionNoMatchCounter = 1,
                "Let's try that again. After the tone, please say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "nomatch" && Global.collectionNoMatchCounter = 2,
                "Sorry, it's important you repeat \*exactly\* the same phrase. After the tone, say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.repromptAfterTrainError = true && Global.collectionCounter < 4,
                "I'm having some trouble getting that. Let's try again. After the tone say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.collectionCounter = 1,
                "Again, after the tone say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.collectionCounter = 2,
                "And again, after the tone: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.collectionCounter = 3,
                "Almost there ... after the tone: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false,
                "And last time: At Riyad Bank my voice is my password.",
                
                "tone" // Default case
            )}


use switch case for each condition in initial prompt. if there is no if/else then use example#3 to convert it.
if there are multiple audiofile tag within one if or else than use reference if example#4  and generate it like below.

input: 
          <initialprompt count="1">
            <audio>
              <if cond="collectionCounter == 0 &amp;&amp; enrollmentLanguage == 'en'">
                <prompt id="rb0520_ini_03">
                  <prompt-segments>
                    <audiofile text="'At Riyad Bank my voice is my password'" src="rb0520_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="rb0310_ini_11">
                  <prompt-segments>
                    <audiofile text="tone" src="rb0310_ini_11.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_1000">
                  <prompt-segments>
                    <audiofile text="Silence 1000 milliseconds" src="silence_1000.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="rb0520_ini_08">
                  <prompt-segments>
                    <audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="collectionCounter == 0 &amp;&amp; enrollmentLanguage == 'ar'">
                  <prompt id="rb0520_ini_04">
                    <prompt-segments>
                      <audiofile text="Arabic Passphrase" src="rb0520_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0310_ini_11">
                    <prompt-segments>
                      <audiofile text="tone" src="rb0310_ini_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000">
                    <prompt-segments>
                      <audiofile text="Silence 1000 milliseconds" src="silence_1000.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0520_ini_08">
                    <prompt-segments>
                      <audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="collectionCounter != 0 &amp;&amp; enrollmentLanguage == 'en'">
                  <prompt id="rb0520_ini_05">
                    <prompt-segments>
                      <audiofile text="One more time please " src="rb0520_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0520_ini_06">
                    <prompt-segments>
                      <audiofile text="'At Riyad Bank my voice is my password'" src="rb0520_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0310_ini_11">
                    <prompt-segments>
                      <audiofile text="tone" src="rb0310_ini_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000">
                    <prompt-segments>
                      <audiofile text="Silence 1000 milliseconds" src="silence_1000.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0520_ini_09">
                    <prompt-segments>
                      <audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="collectionCounter != 0 &amp;&amp; enrollmentLanguage == 'ar'">
                  <prompt id="rb0520_ini_05">
                    <prompt-segments>
                      <audiofile text="One more time please " src="rb0520_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0520_ini_07">
                    <prompt-segments>
                      <audiofile text="Arabic Passphrase" src="rb0520_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0310_ini_11">
                    <prompt-segments>
                      <audiofile text="tone" src="rb0310_ini_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000">
                    <prompt-segments>
                      <audiofile text="Silence 1000 milliseconds" src="silence_1000.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0520_ini_09">
                    <prompt-segments>
                      <audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
            </audio>
          </initialprompt>
output: 
prompt:
        speak:
  - |
    {
      Switch(
        true,

        // Case 1: collectionCounter = 0 && enrollmentLanguage = "en"
        Global.collectionCounter = 0 && Global.enrollmentLanguage = "en",
        Concatenate(
          "At Riyad Bank my voice is my password",
          "tone",
          "Silence 1000 milliseconds",
          "or key in your PIN, followed by the hash key"
        ),

        // Case 2: collectionCounter = 0 && enrollmentLanguage = "ar"
        Global.collectionCounter = 0 && Global.enrollmentLanguage = "ar",
        Concatenate(
          "Arabic Passphrase",
          "tone",
          "Silence 1000 milliseconds",
          "or key in your PIN, followed by the hash key"
        ),

        // Case 3: collectionCounter <> 0 && enrollmentLanguage = "en"
        Global.collectionCounter <> 0 && Global.enrollmentLanguage = "en",
        Concatenate(
          "One more time please: ...",
          "At Riyad Bank my voice is my password",
          "tone",
          "Silence 1000 milliseconds",
          "or key in your PIN, followed by the hash key"
        ),

        // Case 4: collectionCounter <> 0 && enrollmentLanguage = "ar"
        Global.collectionCounter <> 0 && Global.enrollmentLanguage = "ar",
        Concatenate(
          "One more time please: ...",
          "Arabic Passphrase",
          "tone",
          "Silence 1000 milliseconds",
          "or key in your PIN, followed by the hash key"
        ),

        // Default Case
        Concatenate("Invalid state or language.")
      )
    }


5) check all "noinputprompts", if there is any if/else tag then convert it into powerfx like below and put it under "inputTimeoutResponse" speak.
input: 
<noinputprompts count="1">
                <audio>
                    <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                        <prompt id="au17420_ni1_02">
                            <prompt-segments>
                                <audiofile src="au17420_ni1_02.wav" text="Sorry, *what's* the member ID?" />
                            </prompt-segments>
                        </prompt>
                        <else>
                            <prompt id="au17420_ni1_01">
                                <prompt-segments>
                                    <audiofile src="au17420_ni1_01.wav" text="Sorry, *what's* the Humana ID?" />
                                </prompt-segments>
                            </prompt>
                        </else>
                    </if>
                </audio>
            </noinputprompts>
            <noinputprompts count="2">
                <audio>
                    <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                        <prompt id="au17420_ni2_02">
                            <prompt-segments>
                                <audiofile src="au17420_ni2_02.wav" text="Say or enter the member ID" />
                            </prompt-segments>
                        </prompt>
                        <else>
                            <prompt id="au17420_ni2_01">
                                <prompt-segments>
                                    <audiofile src="au17420_ni2_01.wav" text="Say or enter the member's Humana ID" />
                                </prompt-segments>
                            </prompt>
                        </else>
                    </if>
                </audio>
            </noinputprompts>
output:
    inputTimeoutResponse:
      speak:
        - |
          {Switch(
                true,
                Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService",
                "Sorry, \*what's\* the member ID?",
            
                true,
                "Sorry, \*what's\* the Humana ID?"
            )
            }
        - |
          {Switch(
                true,
                Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService",
                "Say or enter the member ID",
            
                true,
                "Say or enter the member's Humana ID"
            )
            }

use switch case for each condition in noinputprompts prompt. if there is no if/else then use example#1 to convert it.

6) if there is no if/else in noinput tag then create it like below. just take the text from "audiofile" tag, if there are more than one audio tag in one nomatch tag then combine both the text and put it under speak of "inputTimeoutResponse".
input:
<noinputprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0022_EndeavorAirMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0022_EndeavorAirMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0022_EndeavorAirMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0022_EndeavorAirMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
output: 
      inputTimeoutResponse:
        speak:
          - "<audio src=\"AUDIO_LOCATION/gl_nm1_01.wav\">Im sorry, I didnt get that.</audio>"
          - "<audio src=\"AUDIO_LOCATION/dlhd0022_EndeavorAirMenu_DM_ini_02.wav\">For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8.</audio>"
          - "<audio src=\"AUDIO_LOCATION/gl_nm2_01.wav\">I still didnt understand.</audio>"
          - "<audio src=\"AUDIO_LOCATION/dlhd0022_EndeavorAirMenu_DM_ini_02.wav\">For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8.</audio>"

7) check all "nomatchprompts", if there is any if/else tag then convert it into powerfx like below and put it under "unrecognizedPrompt" speak.
input: 
<nomatchprompts count="1">
                <audio>
                    <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                        <prompt id="au17420_nm1_02">
                            <prompt-segments>
                                <audiofile src="au17420_nm1_02.wav" text="Sorry, *what's* the member ID?" />
                            </prompt-segments>
                        </prompt>
                        <else>
                            <prompt id="au17420_nm1_01">
                                <prompt-segments>
                                    <audiofile src="au17420_nm1_01.wav" text="Sorry, *what's* the Humana ID?" />
                                </prompt-segments>
                            </prompt>
                        </else>
                    </if>
                </audio>
            </nomatchprompts>
            <nomatchprompts count="2">
                <audio>
                    <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                        <prompt id="au17420_nm2_02">
                            <prompt-segments>
                                <audiofile src="au17420_nm2_02.wav" text="Say or enter the member ID" />
                            </prompt-segments>
                        </prompt>
                        <else>
                            <prompt id="au17420_nm2_01">
                                <prompt-segments>
                                    <audiofile src="au17420_nm2_01.wav" text="Say or enter the member's Humana ID" />
                                </prompt-segments>
                            </prompt>
                        </else>
                    </if>
                </audio>
            </nomatchprompts>
output:
    unrecognizedPrompt:
      speak:
        - |
          {Switch(
                true,
                Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService",
                "Sorry, \*what's\* the member ID?",
            
                true,
                "Sorry, \*what's\* the Humana ID?"
            )
            }
        - |
          {Switch(
                true,
                Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService",
                "Say or enter the member ID",
            
                true,
                "Say or enter the member's Humana ID"
            )
            }

use switch case for each condition in nomatchprompts prompt. if there is no if/else then use example#1 to convert it.
8) if there is no if/else in nomatch or noinput tag then create it like below. just take the text from "audiofile" tag, if there are more than one audio tag in one nomatch tag then combine both the text and put it under speak of "unrecognizedPrompt".
input:
<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0022_EndeavorAirMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0022_EndeavorAirMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0022_EndeavorAirMenu_DM_ini_02">
								<prompt-segments>
									<audiofile text="For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8." src="tcc/dlhd0022_EndeavorAirMenu_DM_ini_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
output: 
      unrecognizedPrompt:
        speak:
          - "<audio src=\"AUDIO_LOCATION/gl_nm1_01.wav\">Im sorry, I didnt get that.</audio>"
          - "<audio src=\"AUDIO_LOCATION/dlhd0022_EndeavorAirMenu_DM_ini_02.wav\">For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8.</audio>"
          - "<audio src=\"AUDIO_LOCATION/gl_nm2_01.wav\">I still didnt understand.</audio>"
          - "<audio src=\"AUDIO_LOCATION/dlhd0022_EndeavorAirMenu_DM_ini_02.wav\">For password issues or PIN resets, press 0. If this is a risk to Flight or multiple users are experiencing an outage, press 1. For issues with software or computer hardware, press 2. For Corporate-owned *mobile* devices, press 3. For Financial Systems, press 4. For issues with Office 365, press 7. Or, for any other issue, press 8.</audio>"
          
9) check the <success> tag, if there are mulitple label and its value is yes and no like below then assign  "BooleanPrebuiltEntity" to "entity".
input: 
<success>
        <action label="yes" next="SendSMS_DA" audio="sv8030_ConfirmSMS_DM_yes">
          <session-mapping key="IVR_SMSsent" value="true"/>
          <script className="com.nuance.ps.telefonica.scripts.sv8030Assignment"/>
        </action>
        <action label="no" next="ReturnFromSendSMS_CS" audio="sv8030_out_02">
          <script className="com.nuance.ps.telefonica.scripts.sv8030Assignment"/>
          <script className="com.nuance.ps.telefonica.scripts.ReportSmsPost">
            <param name="STATUS" value="2"/>
          </script>
        </action>
      </success>
output : 
entity: BooleanPrebuiltEntity

if <success> tag have multiple labels and its not yes/no or true/false. if its like below example then convert entity only like below
input:
<success>
        <action label="sim" next="hl0010_WhatsNext_DS">
            <session-mapping key="comingFrom" value="VSS_PreMenu" />
        </action>
        <action label="usage">
            <session-mapping key="comingFrom" value="VSS_PreMenu" />
            <session-mapping key="transferTo" value="AgentCare" />
            <if cond="authenticated == 1 || (ivrCLI == ivrIdValue &amp;&amp; ivrIdType == 'MSISDN')">
                <action next="hl0120_UsageInfo_SD" />
                <else>
                    <action next="hl0010_WhatsNext_DS" audio="hl0150_out_01">
                        <session-mapping key="hangupRequired" value="true" />
                    </action>
                </else>
            </if>
        </action>
    </success>
output: 
  entity:
          kind: EmbeddedEntity
          definition:
            kind: ClosedListEntity
            items:
              - id: sim
                displayName: sim

              - id: usage
                displayName: usage
- kind: ConditionGroup
      id: conditionGroup_415d92
      conditions:
        - id: conditionItem_f88bda
          condition: =If(Global.Var_hl0150_VSS_PreMenu_DM = "sim", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_05f758
              variable: Global.comingFrom
              value: VSS_PreMenu

            - kind: GotoAction
              id: begin_a1992e
              actionId: hl0010_WhatsNext_JDA

      elseActions:
        - kind: ConditionGroup
          id: conditionGroup_1f69c8
          conditions:
            - id: conditionItem_1b0e3e
              condition: =If(Global.Var_hl0150_VSS_PreMenu_DM = "usage", true, false)
              actions:
                - kind: SetVariable
                  id: setVariable_ce4111
                  variable: Global.comingFrom
                  value: VSS_PreMenu

                - kind: SetVariable
                  id: setVariable_f6a9ea
                  variable: Global.transferTo
                  value: AgentCare

                - kind: ConditionGroup
                  id: conditionGroup_374335
                  conditions:
                    - id: conditionItem_3fa7c2
                      condition: =If(Or(Global.authenticated = 1 , Global.ivrCLI = "ivrIdValue"),And ( Global.ivrIdType = "MSISDN"), true, false)
                      actions:
                        - kind: GotoAction
                          id: begin_aa0ecd
                          actionId: hl0120_UsageInfo_SD

                  elseActions:
                    - kind: SetVariable
                      id: setVariable_d741ad
                      variable: Global.hangupRequired
                      value: true

when there is label under action tag then replace that label with the value of dm-state tag "id" with prefix Global.Var_ like Global.Var_hl0150_VSS_PreMenu_DM.
input: 
<action label="sim" next="hl0010_WhatsNext_DS">
output: 
like condition: =If(Global.Var_hl0150_VSS_PreMenu_DM = "sim", true, false)

10) if there are multiple label then take example#5 as a reference to generate yaml.
11) if there are multiple lable then create below code after "defaultValueMissingAction" and before starting of "ConditionGroup" like below. where kind should be "SetVariable"  and variable should be "dm-state" tag id value with Global.Var_ prefix and value should be "dm-state" tag id value with _reco postfix and put it under =Text().. like =Text(Global.dlhd0012_ACSMenu_DM_reco). so the full format should be like below

    - kind: SetVariable
      id: setvariable_REPLACE_THIS
      variable: Global.Var_dlhd0012_ACSMenu_DM
      value: =Text(Global.dlhd0012_ACSMenu_DM_reco)

6) if there is any "session-mapping" tag within input, convert it into below format. 
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.hangupRequired
      value: true
7) if there is special character # while session-mapping . put it under quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: '#'
8) if there is any condition in the value of session-mapping then put it under double quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: "GlobalVars.GetTagData.tagVariables != undefined ? GlobalVars.GetTagData.tagVariables.needMDN : 'false'"
9) replace "SetVariable" id with "setVariable_REPLACE_THIS" .
10) if  there is any "if" condition under "<success>" tag in input then convert it in yml format like below and do not use double equals like "=="
- kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
		  condition: |
        =If(Global.callerLanguage = "ar", true, false)
11) if there is "!=" sign in any condition then change it to "<>" sign.
12) if there is "OR" sign in condition then change it to "||" sign.
13) if there is "AND" sign in condition then change it to "&&" sign.
14) if there is and "AND" or "OR" sign in the condition then add "=" in front of first Global variable only not for other variables. like below:
  condition: |
    =If(And(Global.vpEnrollStatus = "MORE_AUDIO_REQUIRED" , Global.collectionCounter < 5), Or  (Global.enrollCounter < 4), true, false)
15) if there is and "AND" or "OR" sign in the condition then add "Global" in front of each variable if its missing. Like:
Global.myMode = "dtmf" || Global.myMode = "DTMF"
16) if the next value's postfix is  _DS or _DM or _PP or _DA or _DB of "action" tag in "<success>" tag then only generate below format. "actionId" should be next value.
 - kind: GotoAction
   id: goto_REPLACE_THIS
   actionId: rb0410_CleanUpNeeded_DS

replace "GotoAction" id with "goto_REPLACE_THIS".

17) when the next value of "action" tag in  "<success>" tag is _Dialog then generate below format. "dialog" should be next value with prefix.
  - kind: BeginDialog
    id: begin_REPLACE_THIS
    dialog: topic.vptransfer_Dialog

Replace "BeginDialog" id with "begin_REPLACE_THIS".

Example Input:
<action label="retry" next="rb0310_Enrollment_DM">
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0310_Enrollment_DM

Output should be generated based on the next value alone, without considering the label.

18) follow the same above next pattern in "elseActions".
19) Replace "ConditionGroup" id with "conditionGroup_REPLACE_THIS".
20) Replace "conditions" first id with "conditionItem_REPLACE_THIS".
21) replace "variable" value after "alwaysPrompt" with value with "dm-state" id with _reco postfix like below.
variable: Global.rb0310_Enrollment_DM_reco
21) dialog should be the "next" with prefix value.
22) don't do anything for <event> tag. if there is ant <event> tag in the input then don't create any yaml for it.

23) if nomatch/noinput tags are not present. doin't create yaml for it.
24) if there is any condition with empty string like below. convert that single quotes to double quotes.
<if cond="hangupRequired == 'false' &amp;&amp; goToSMS == 'false' &amp;&amp; goToOtherService == 'false' &amp;&amp; (serviceTarget == 'undefined' || serviceTarget == '' || serviceTarget == '#')">

output:

condition: |-
  =If(AND(Global.hangupRequired = false , goToSMS == 'false', Global.goToOtherService = false , Or(Global.serviceTarget = "undefined" , Global.serviceTarget = "" , Global.serviceTarget = "#")), true, false)

use above pattern to make condition and don't use && and || operator in any condition
25) If there is any integer value in condition, just remove the quotes from them and make it integer like below example:
input:
<if cond="authenticated == '0' &amp;&amp; authenticationRequired == 'true'">
output:
condition: |-
  =If(And(Global.authenticated == 0 , Global.authenticationRequired == true),true, false)

26) if there are more than 2 "label" under "success" tag without if/else condition like example 3 then compare that label value with Global.label and create if else condition for each label like example 3 output, follow this instruction just for success tag do the other thing as it is for other input/output.Also if there is any if/else condition within any label, make that condition as other if/else condition. 
27) if there is any prompt between the success tag then take a example#7 for your reference to generate it.
28) indentation for next yaml should be same as first one or as memorized yaml


### Example 1:
**Input XML:**
```xml
<dm-state id="rb0310_Enrollment_DM">
    <var name="collectionCounter" path="collectionCounter" scope="session" isNamelist="true" />
    <var name="collectionNoInputCounter" path="collectionNoInputCounter" scope="session" isNamelist="true" />
    <var name="collectionNoMatchCounter" path="collectionNoMatchCounter" scope="session" isNamelist="true" />
    <var name="isComingFrom_rb0330" path="isComingFrom_rb0330" scope="session" isNamelist="true" />
    <var name="collectionNiOrNm_Temp" path="collectionNiOrNm_Temp" scope="session" isNamelist="true" />
    <var name="repromptAfterTrainError" path="repromptAfterTrainError" scope="session" isNamelist="true" />
    <var name="callComesFromAgent" path="callComesFromAgent" scope="session" isNamelist="true" />
    <success>
        <action next="rb0320_SaveEnrollUtterance_DA">
            <session-mapping key="collectionNiOrNm_Temp" value="undefined" type="String" />
            <session-mapping key="collectionNoInputCounter" value="0" type="Integer" />
            <session-mapping key="collectionNoMatchCounter" value="0" type="Integer" />
            <session-mapping key="repromptAfterTrainError" value="false" type="Boolean" />
            <script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
                <param name="param1" value="collectionCounter" />
            </script>
            <session-mapping key="isComingFrom_rb0330" value="false" type="Boolean" />
            <var name="callerLanguage" path="callerLanguage" scope="session" />
            <if cond="callerLanguage == 'ar'">
                <session-mapping key="enrollmentLanguage" value="ar" type="String" />
                <elseif cond="callerLanguage == 'en'">
                    <session-mapping key="enrollmentLanguage" value="en" type="String" />
                </elseif>
            </if>
        </action>
    </success>
    <event name="event.nuance.dialog.ndm.maxnomatches">
        <action next="rb0330_CheckEnrollPassphrase_DS">
            <session-mapping key="collectionNiOrNm_Temp" value="nomatch" type="String" />
            <session-mapping key="repromptAfterTrainError" value="false" type="Boolean" />
            <script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
                <param name="param1" value="collectionCounter" />
                <param name="param2" value="collectionNoMatchCounter" />
                <param name="param3" value="collectionMaxErrorCounter" />
            </script>
            <var name="callerLanguage" path="callerLanguage" scope="session" />
            <if cond="callerLanguage == 'ar'">
                <session-mapping key="enrollmentLanguage" value="ar" type="String" />
                <elseif cond="callerLanguage == 'en'">
                    <session-mapping key="enrollmentLanguage" value="en" type="String" />
                </elseif>
            </if>
        </action>
    </event>
    <event name="event.nuance.dialog.ndm.maxnoinputs">
        <action next="rb0330_CheckEnrollPassphrase_DS">
            <session-mapping key="collectionNiOrNm_Temp" value="noinput" type="String" />
            <session-mapping key="repromptAfterTrainError" value="false" type="Boolean" />
            <script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
                <param name="param1" value="collectionCounter" />
                <param name="param2" value="collectionNoInputCounter" />
                <param name="param3" value="collectionMaxErrorCounter" />
            </script>
            <var name="callerLanguage" path="callerLanguage" scope="session" />
            <if cond="callerLanguage == 'ar'">
                <session-mapping key="enrollmentLanguage" value="ar" type="String" />
                <elseif cond="callerLanguage == 'en'">
                    <session-mapping key="enrollmentLanguage" value="en" type="String" />
                </elseif>
            </if>
        </action>
    </event>
    <collection_configuration inputmodes="voice">
        <threshold_configuration maxnomatches="0" maxnoinputs="0" />
        <vxml_properties inputmodes="voice">
            <property_map>
                <key>recordutterance</key>
                <value>true</value>
            </property_map>
        </vxml_properties>
        <prompt_configuration>
            <initialprompt count="1">
						<audio>
							<if cond="collectionCounter == 0 &amp;&amp; callComesFromAgent == true">
								<prompt id="rb0310_ini_12_part2" bargein="false">
									<prompt-segments>
										<audiofile text="So... after the tone please say: 'At Riyad Bank my voice is my password'." src="rb0310_ini_12_part2.wav" />
									</prompt-segments>
								</prompt>
								<elseif cond="collectionCounter == 0 &amp;&amp; callComesFromAgent == false">
									<prompt id="rb0310_ini_13" bargein="false">
										<prompt-segments>
											<audiofile text="So ... after the tone please say: 'At Riyad Bank my voice is my password'. (alternative: Great! To create your voice print, I'm going to ask you to repeat a phrase a few times so the system can analyze your voice.  So, after the tone please say 'With Riyad Bank my voice is my password#)" src="rb0310_ini_13.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'noinput' &amp;&amp; collectionNoInputCounter == 1">
									<prompt id="rb0310_ini_02" bargein="false">
										<prompt-segments>
											<audiofile text="After the tone please say: 'At Riyad Bank my voice is my password'" src="rb0310_ini_02.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'noinput' &amp;&amp; collectionNoInputCounter == 2">
									<prompt id="rb0310_ini_03" bargein="false">
										<prompt-segments>
											<audiofile text="Sorry, I didn't hear that.  After the tone, please repeat the following phrase: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_03.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'nomatch' &amp;&amp; collectionNoMatchCounter == 1">
									<prompt id="rb0310_ini_04" bargein="false">
										<prompt-segments>
											<audiofile text="Let's try that again.  After the tone, please say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_04.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == true &amp;&amp; collectionNiOrNm_Temp == 'nomatch' &amp;&amp; collectionNoMatchCounter == 2">
									<prompt id="rb0310_ini_05" bargein="false">
										<prompt-segments>
											<audiofile text="Sorry, it's important you repeat *exactly* the same phrase.  After the tone, say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_05.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; repromptAfterTrainError == true &amp;&amp; collectionCounter &lt; 4">
									<prompt id="rb0310_ini_06" bargein="false">
										<prompt-segments>
											<audiofile text="I'm having some trouble getting that.  Let's try again.  After the tone say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_06.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 1">
									<prompt id="rb0310_ini_07" bargein="false">
										<prompt-segments>
											<audiofile text="Again, after the tone say: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_07.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 2">
									<prompt id="rb0310_ini_08" bargein="false">
										<prompt-segments>
											<audiofile text="And again, after the tone: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_08.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false &amp;&amp; collectionCounter == 3">
									<prompt id="rb0310_ini_09" bargein="false">
										<prompt-segments>
											<audiofile text="Almost there ... after the tone: 'At Riyad Bank my voice is my password.'" src="rb0310_ini_09.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
								<elseif cond="isComingFrom_rb0330 == false">
									<prompt id="rb0310_ini_10" bargein="false">
										<prompt-segments>
											<audiofile text="And last time: At Riyad Bank my voice is my password." src="rb0310_ini_10.wav" />
										</prompt-segments>
									</prompt>
								</elseif>
							</if>
							<prompt id="rb0310_ini_11" bargein="false">
								<prompt-segments>
									<audiofile text="tone" src="rb0310_ini_11.wav" />
								</prompt-segments>
							</prompt>
						</audio>
            </initialprompt>
            <repeatprompts>
                <audio>
                    <prompt id="sv3025_ini_01">
                        <prompt-segments>
                            <audiofile text="Möchten Sie die Informationen noch einmal hören?" src="sv3025_ini_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </repeatprompts>
            <noinputprompts>
                <audio>
                    <prompt id="sv8030_retry1_01">
                        <prompt-segments>
                            <audiofile src="sv8030_retry1_01.wav" text="Noinput1"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </noinputprompts>
            <noinputprompts>
                <audio>
                    <prompt id="gl_ni2_01">
                        <prompt-segments>
                            <audiofile text="Noinput2" src="gl_ni2_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </noinputprompts>
            <noinputprompts>
                <audio>
                    <prompt id="gl_ni3_01">
                        <prompt-segments>
                            <audiofile text="Noinput3" src="gl_ni3_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </noinputprompts>
            <nomatchprompts>
                <audio>
                    <prompt id="gl_nm1_01">
                        <prompt-segments>
                            <audiofile text="Nomatch1" src="gl_nm1_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </nomatchprompts>
            <nomatchprompts>
                <audio>
                    <prompt id="gl_nm2_01">
                        <prompt-segments>
                            <audiofile text="Nomatch2" src="gl_nm2_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </nomatchprompts>
            <nomatchprompts>
                <audio>
                    <prompt id="gl_nm3_01">
                        <prompt-segments>
                            <audiofile text="Nomatch3" src="gl_nm3_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </nomatchprompts>
            <helpprompts count="1" filename="none" text="none" id="" />
            <helpprompts count="2" filename="none" text="none" id="" />
            <repeatprompts count="1" filename="none" text="none" id="" />
            <repeatprompts count="2" filename="none" text="none" id="" />
            <nomatchprefixes count="1" filename="none" text="none" id="" />
            <nomatchprefixes count="2" filename="none" text="none" id="" />
            <nomatchprefixes count="3" filename="none" text="none" id="" />
            <noinputprefixes count="1" filename="none" text="none" id="" />
            <noinputprefixes count="2" filename="none" text="none" id="" />
            <noinputprefixes count="3" filename="none" text="none" id="" />
            <noinputprompts count="1" bargein="false" filename="none" text="none" id="" />
            <nomatchprompts count="1" bargein="false" filename="none" text="none" id="" />
            <notoconfirmprefixes count="2" filename="none" text="none" id="" />
            <notoconfirmprefixes count="3" filename="none" text="none" id="" />
            <notoconfirmprefixes count="1" bargein="false" filename="none" text="none" id="" />
            <notoconfirmprompts count="1" bargein="false" filename="none" text="none" id="" />
            <notoconfirmprefixes count="2" bargein="false" filename="none" text="none" id="" />
            <notoconfirmprompts count="2" bargein="false" filename="none" text="none" id="" />
        </prompt_configuration>
    </collection_configuration>
    <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="1" maxnoinputs="1" />
        <vxml_properties bargein="false" inputmodes="voice" />
        <failureprompt count="1" filename="none" text="none" id="" />
        <commandconfirmationprompts count="1" bargein="false" filename="none" text="none" id="" />
        <successprompts count="1" filename="none" text="none" id="" />
        <successprompts count="2" filename="none" text="none" id="" />
        <successprompts count="3" filename="none" text="none" id="" />
        <successcorrectedprompt count="1" filename="none" text="none" id="" />
    </global_configuration>
</dm-state>
```

**Output yaml:**
```yaml
- kind: Question
  id: rb0310_Enrollment_DM
  displayName: rb0310_Enrollment_DM
  interruptionPolicy:
    allowInterruption: true

  unrecognizedPrompt:
    speak:
      - nomatch 1
      - nomatch 2

  alwaysPrompt: true
  variable: Global.rb0310_Enrollment_DM_reco
  prompt:
    speak:
      - |
        {Switch(
                true,
                Global.collectionCounter = 0 && Global.callComesFromAgent = true,
                "So... after the tone please say: 'At Riyad Bank my voice is my password.'",
                
                Global.collectionCounter = 0 && Global.callComesFromAgent = false,
                "So ... after the tone please say: 'At Riyad Bank my voice is my password'. (alternative: Great! To create your voice print, I'm going to ask you to repeat a phrase a few times so the system can analyze your voice. So, after the tone please say 'With Riyad Bank my voice is my password#)",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "noinput" && Global.collectionNoInputCounter = 1,
                "After the tone please say: 'At Riyad Bank my voice is my password'",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "noinput" && Global.collectionNoInputCounter = 2,
                "Sorry, I didn't hear that. After the tone, please repeat the following phrase: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "nomatch" && Global.collectionNoMatchCounter = 1,
                "Let's try that again. After the tone, please say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = true && Global.collectionNiOrNm_Temp = "nomatch" && Global.collectionNoMatchCounter = 2,
                "Sorry, it's important you repeat \*exactly\* the same phrase. After the tone, say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.repromptAfterTrainError = true && Global.collectionCounter < 4,
                "I'm having some trouble getting that. Let's try again. After the tone say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.collectionCounter = 1,
                "Again, after the tone say: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.collectionCounter = 2,
                "And again, after the tone: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false && Global.collectionCounter = 3,
                "Almost there ... after the tone: 'At Riyad Bank my voice is my password.'",
                
                Global.isComingFrom_rb0330 = false,
                "And last time: At Riyad Bank my voice is my password.",
                
                "tone" // Default case
            )}

  entity: BooleanPrebuiltEntity
  voiceInputSettings:
    silenceDetectionTimeoutInMilliseconds: 7000
    repeatCountOnSilence: 2
    inputTimeoutResponse:
      speak:
        - Noinput 1
        - Noinput2

    defaultValueMissingAction: Escalate

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.collectionNiOrNm_Temp
  value: undefined

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.collectionNoInputCounter
  value: 0

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.collectionNoMatchCounter
  value: 0

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.repromptAfterTrainError
  value: false

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.isComingFrom_rb0330
  value: false

- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(Global.callerLanguage = "ar", true, false)
      actions:
        - kind: SetVariable
          id: setVariable_REPLACE_THIS
          variable: Global.enrollmentLanguage
          value: ar

  elseActions:
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Global.callerLanguage = "en")
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.enrollmentLanguage
              value: en

- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0330_CheckEnrollPassphrase_DS
```


### Example 2:
**Input XML:**
```xml
<dm-state id="au17420_GetMemberIDFromAgent_DM">
    <properties name="swirec.secure_context" value="1" />
    <properties name="switts.secure_context" value="1" />
    <success>
        <action label="default">
            <session-mapping key="sessionData.IDAuthData.dobInputMode" expr="au17420_GetMemberIDFromAgent_DM.state.getCollection().getResult().getInputMode()" />
            <session-mapping key="sessionData.IDAuthData.memberIDProvided" path="returnvalue" scope="request" />
            <if cond="au17420_GetMemberIDFromAgent_DM.state.getConfirmed() == true">
                <session-mapping key="sessionData.IDAuthData.memberIDConfirmed" value="true" type="Boolean" />
            </if>
            <if cond="sessionData.IDAuthData.memberIDEntryAttempts &gt; 0">
                <action label="for single memberIdEntryAttempts" next="au17440_AuthenticateMemberFromAgent_DB_DA" />
                <else>
                    <session-mapping value="memberID" key="session.memberData.memberIDType" />
                    <script className="com.nuance.humana.scripts.MemberIDManipulation" />
                    <action label="member id" next="au17430_GetDOBFromAgent_DM" />
                </else>
            </if>
        </action>
    </success>
    <collection_configuration confirmationmode="Never" highconfidencelevel="0.021" inputmodes="voice">
        <threshold_configuration maxinvalidanswers="2" maxturns="6" maxnoinputs="3" maxnomatches="3" maxrepeats="2" maxhelps="2" />
        <grammar_configuration commandgrammar="none" dtmfcommandgrammar="none" supportsadaptivegrammar="false" adaptivewordlist="none" adaptivegrammar="none">
            <grammars filename="au17420_GetMemberIDFromAgent_DM.gram" count="1" />
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" timeout="5000ms" incompletetimeout="1500ms" maxspeechtimeout="10000ms" termtimeout="10ms" interdigittimeout="2000ms" inputmodes="voice" />
        <prompt_configuration>
            <initialprompt count="1">
                <audio>
                    <if type="expression" cond="sessionData.IDAuthData.memberIDEntryAttempts == 1">
                        <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                            <prompt id="au17420_ini_03">
                                <prompt-segments>
                                    <audiofile src="au17420_ini_03.wav" text="Please say or enter their member ID again" />
                                </prompt-segments>
                            </prompt>
                            <else>
                                <prompt id="au17420_ini_01">
                                    <prompt-segments>
                                        <audiofile src="au17420_ini_01.wav" text="Please say or enter their Humana member ID again" />
                                    </prompt-segments>
                                </prompt>
                            </else>
                        </if>
                        <else>
                            <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                                <prompt id="au17420_ini_04">
                                    <prompt-segments>
                                        <audiofile src="au17420_ini_04.wav" text="Thanks, now please say or enter the member ID" />
                                    </prompt-segments>
                                </prompt>
                                <else>
                                    <prompt id="au17420_ini_02">
                                        <prompt-segments>
                                            <audiofile src="au17420_ini_02.wav" text="Thanks, now please say or enter the Humana member ID" />
                                        </prompt-segments>
                                    </prompt>
                                </else>
                            </if>
                        </else>
                    </if>
                </audio>
            </initialprompt>
            <repeatprompts count="1">
                <audio>
                    <if type="expression" cond="sessionData.IDAuthData.memberIDEntryAttempts == 1">
                        <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                            <prompt id="au17420_ini_03">
                                <prompt-segments>
                                    <audiofile src="au17420_ini_03.wav" text="Please say or enter their member ID again" />
                                </prompt-segments>
                            </prompt>
                            <else>
                                <prompt id="au17420_ini_01">
                                    <prompt-segments>
                                        <audiofile src="au17420_ini_01.wav" text="Please say or enter their Humana member ID again" />
                                    </prompt-segments>
                                </prompt>
                            </else>
                        </if>
                        <else>
                            <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                                <prompt id="au17420_ini_04">
                                    <prompt-segments>
                                        <audiofile src="au17420_ini_04.wav" text="Thanks, now please say or enter the member ID" />
                                    </prompt-segments>
                                </prompt>
                                <else>
                                    <prompt id="au17420_ini_02">
                                        <prompt-segments>
                                            <audiofile src="au17420_ini_02.wav" text="Thanks, now please say or enter the Humana member ID" />
                                        </prompt-segments>
                                    </prompt>
                                </else>
                            </if>
                        </else>
                    </if>
                </audio>
            </repeatprompts>
            <nomatchprompts count="1">
                <audio>
                    <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                        <prompt id="au17420_nm1_02">
                            <prompt-segments>
                                <audiofile src="au17420_nm1_02.wav" text="Sorry, *what's* the member ID?" />
                            </prompt-segments>
                        </prompt>
                        <else>
                            <prompt id="au17420_nm1_01">
                                <prompt-segments>
                                    <audiofile src="au17420_nm1_01.wav" text="Sorry, *what's* the Humana ID?" />
                                </prompt-segments>
                            </prompt>
                        </else>
                    </if>
                </audio>
            </nomatchprompts>
            <nomatchprompts count="2">
                <audio>
                    <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                        <prompt id="au17420_nm2_02">
                            <prompt-segments>
                                <audiofile src="au17420_nm2_02.wav" text="Say or enter the member ID" />
                            </prompt-segments>
                        </prompt>
                        <else>
                            <prompt id="au17420_nm2_01">
                                <prompt-segments>
                                    <audiofile src="au17420_nm2_01.wav" text="Say or enter the member's Humana ID" />
                                </prompt-segments>
                            </prompt>
                        </else>
                    </if>
                </audio>
            </nomatchprompts>
            <nomatchprompts count="3" />
            <noinputprompts count="1">
                <audio>
                    <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                        <prompt id="au17420_ni1_02">
                            <prompt-segments>
                                <audiofile src="au17420_ni1_02.wav" text="Sorry, *what's* the member ID?" />
                            </prompt-segments>
                        </prompt>
                        <else>
                            <prompt id="au17420_ni1_01">
                                <prompt-segments>
                                    <audiofile src="au17420_ni1_01.wav" text="Sorry, *what's* the Humana ID?" />
                                </prompt-segments>
                            </prompt>
                        </else>
                    </if>
                </audio>
            </noinputprompts>
            <noinputprompts count="2">
                <audio>
                    <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
                        <prompt id="au17420_ni2_02">
                            <prompt-segments>
                                <audiofile src="au17420_ni2_02.wav" text="Say or enter the member ID" />
                            </prompt-segments>
                        </prompt>
                        <else>
                            <prompt id="au17420_ni2_01">
                                <prompt-segments>
                                    <audiofile src="au17420_ni2_01.wav" text="Say or enter the member's Humana ID" />
                                </prompt-segments>
                            </prompt>
                        </else>
                    </if>
                </audio>
            </noinputprompts>
            <noinputprompts count="3" />
        </prompt_configuration>
    </collection_configuration>
    <global_configuration confirmationmode="Never">
        <failureprompt count="1" />
        <successprompts count="1" />
        <successprompts count="2" />
        <successprompts count="3" />
        <successcorrectedprompt count="1" />
    </global_configuration>
</dm-state>
```
**Output yaml:**
```yaml

- kind: Question
  id: au17420_GetMemberIDFromAgent_DM
  displayName: au17420_GetMemberIDFromAgent_DM
  interruptionPolicy:
    allowInterruption: true

  unrecognizedPrompt:
    speak:
      - |
        {Switch(
                true,
                Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService",
                "Sorry, \*what's\* the member ID?",
            
                true,
                "Sorry, \*what's\* the Humana ID?"
            )
            }
        - |
          {Switch(
                true,
                Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService",
                "Say or enter the member ID",
            
                true,
                "Say or enter the member's Humana ID"
            )
            }


  alwaysPrompt: true
  variable: Global.au17420_GetMemberIDFromAgent_DM_reco
  prompt:
    speak:
      - |
        {Switch(
                true,
                
                Global.memberIDEntryAttempts = 1 &&
                (Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService"),
                "Please say or enter their member ID again",
            
                Global.memberIDEntryAttempts = 1,
                "Please say or enter their Humana member ID again",
            
                Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService",
                "Thanks, now please say or enter the member ID",
            
                "Thanks, now please say or enter the Humana member ID"
            )}

  entity: StringPrebuiltEntity
  voiceInputSettings:
    silenceDetectionTimeoutInMilliseconds: 7000
    repeatCountOnSilence: 2
    inputTimeoutResponse:
      speak:
        - |
          {Switch(
                true,
                Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService",
                "Sorry, \*what's\* the member ID?",
            
                true,
                "Sorry, \*what's\* the Humana ID?"
            )
            }
        - |
          {Switch(
                true,
                Global.dnisType = "CarePlusMember" || Global.dnisType = "CarePlusSelfService",
                "Say or enter the member ID",
            
                true,
                "Say or enter the member's Humana ID"
            )
            }

    defaultValueMissingAction: Escalate

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.sessionData.IDAuthData.dobInputMode
  value: au17420_GetMemberIDFromAgent_DM.state.getCollection().getResult().getInputMode()

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.sessionData.IDAuthData.memberIDProvided
  value: returnvalue

- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(au17420_GetMemberIDFromAgent_DM.state.getConfirmed() = true, true, false)
      actions:
        - kind: SetVariable
          id: setVariable_REPLACE_THIS
          variable: Global.sessionData.IDAuthData.memberIDConfirmed
          value: true

- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(Global.sessionData.IDAuthData.memberIDEntryAttempts > 0, true, false)
      actions:
        - kind: GotoAction
          id: goto_REPLACE_THIS
          actionId: au17440_AuthenticateMemberFromAgent_DB_DA

  elseActions:
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.session.memberData.memberIDType
      value: memberID

    - kind: GotoAction
      id: goto_REPLACE_THIS
      actionId: au17430_GetDOBFromAgent_DM
```

### Example 3:
**Input XML:**
```xml
<dm-state id="hl0150_VSS_PreMenu_DM">
    <script className="com.nuance.ps.telefonica.scripts.KPIAddNode" />
    <success>
        <action label="sim" next="hl0010_WhatsNext_DS">
            <session-mapping key="comingFrom" value="VSS_PreMenu" />
        </action>
        <action label="usage">
            <session-mapping key="comingFrom" value="VSS_PreMenu" />
            <session-mapping key="transferTo" value="AgentCare" />
            <if cond="authenticated == 1 || (ivrCLI == ivrIdValue &amp;&amp; ivrIdType == 'MSISDN')">
                <action next="hl0120_UsageInfo_SD" />
                <else>
                    <action next="hl0010_WhatsNext_DS" audio="hl0150_out_01">
                        <session-mapping key="hangupRequired" value="true" />
                    </action>
                </else>
            </if>
        </action>
    </success>
    <event name="event.nuance.dialog.ndm.maxnomatches">
        <action next="hl0010_WhatsNext_DS">
            <script className="com.nuance.ps.telefonica.scripts.SetReturnValues" />
            <session-mapping key="comingFrom" value="VSS_PreMenu" />
        </action>
    </event>
    <event name="event.nuance.dialog.ndm.maxnoinputs">
        <action next="hl0010_WhatsNext_DS">
            <script className="com.nuance.ps.telefonica.scripts.SetReturnValues" />
            <session-mapping key="comingFrom" value="VSS_PreMenu" />
        </action>
    </event>
    <event name="error_CS" next="hl0020_TransferToAgent_PP">
        <session-mapping key="goToAgent" value="true" />
        <session-mapping key="comingFrom" value="VSS_PreMenu" />
    </event>
    <collection_configuration confirmationmode="Never" highconfidencelevel="0.021" inputmodes="voice">
        <threshold_configuration maxinvalidanswers="2" maxturns="6" maxnoinputs="3" maxnomatches="3" maxrepeats="2" maxhelps="2" />
        <grammar_configuration commandgrammar="none" dtmfcommandgrammar="none" supportsadaptivegrammar="false" adaptivewordlist="none" adaptivegrammar="none">
            <grammars filename="hl0150_VSS_PreMenu_DM.gram" count="1" />
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" timeout="5000ms" incompletetimeout="1500ms" maxspeechtimeout="10000ms" termtimeout="10ms" interdigittimeout="2000ms" inputmodes="voice" />
        <prompt_configuration>
            <initialprompt count="1">
                <audio>
                    <prompt id="hl0150_ini_01">
                        <prompt-segments>
                            <audiofile src="hl0150_ini_01.wav" text="Worum geht es bei Ihrem Anruf? Um eine Verbrauchsinformation - oder um Ihre SIM-Karte?" />
                        </prompt-segments>
                    </prompt>
                </audio>
            </initialprompt>
            <repeatprompts count="1">
                <audio>
                    <prompt id="hl0150_ini_01">
                        <prompt-segments>
                            <audiofile src="hl0150_ini_01.wav" text="Worum geht es bei Ihrem Anruf? Um eine Verbrauchsinformation - oder um Ihre SIM-Karte?" />
                        </prompt-segments>
                    </prompt>
                </audio>
            </repeatprompts>
            <repeatprompts count="2" />
            <nomatchprefixes count="1" />
            <nomatchprefixes count="2" />
            <nomatchprefixes count="3" />
            <noinputprefixes count="1" />
            <noinputprefixes count="2" />
            <noinputprefixes count="3" />
            <noinputprompts count="1">
                <audio>
                    <prompt id="hl0150_retry1_01">
                        <prompt-segments>
                            <audiofile src="hl0150_retry1_01.wav" text="F�r Fragen zu Ihrem Verbrauch sagen Sie bitte - 'Verbrauchsinfo' und bei Anliegen rund um Ihre SIM-Karte sagen Sie - 'SIM'" />
                        </prompt-segments>
                    </prompt>
                </audio>
            </noinputprompts>
            <noinputprompts count="2">
                <audio>
                    <prompt id="gl_ni2_01">
                        <prompt-segments>
                            <audiofile text="Ich konnte Sie erneut nicht h�ren" src="gl_ni2_01.wav" />
                        </prompt-segments>
                    </prompt>
                    <prompt id="hl0150_retry2_01">
                        <prompt-segments>
                            <audiofile src="hl0150_retry2_01.wav" text="Bitte w�hlen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM" />
                        </prompt-segments>
                    </prompt>
                </audio>
            </noinputprompts>
            <noinputprompts count="3">
                <audio>
                    <prompt id="gl_ni3_01">
                        <prompt-segments>
                            <audiofile text="Ich konnte Sie erneut nicht h�ren" src="gl_ni3_01.wav" />
                        </prompt-segments>
                    </prompt>
                    <prompt id="hl0150_retry2_01">
                        <prompt-segments>
                            <audiofile src="hl0150_retry2_01.wav" text="Bitte w�hlen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM" />
                        </prompt-segments>
                    </prompt>
                </audio>
            </noinputprompts>
            <nomatchprompts count="1">
                <audio>
                    <prompt id="gl_nm1_01">
                        <prompt-segments>
                            <audiofile text="Entschuldigung" src="gl_nm1_01.wav" />
                        </prompt-segments>
                    </prompt>
                    <prompt id="hl0150_retry1_01">
                        <prompt-segments>
                            <audiofile src="hl0150_retry1_01.wav" text="F�r Fragen zu Ihrem Verbrauch sagen Sie bitte - 'Verbrauchsinfo' und bei Anliegen rund um Ihre SIM-Karte sagen Sie - 'SIM'" />
                        </prompt-segments>
                    </prompt>
                </audio>
            </nomatchprompts>
            <nomatchprompts count="2">
                <audio>
                    <prompt id="gl_nm2_01">
                        <prompt-segments>
                            <audiofile text="Ich konnte Sie erneut nicht verstehen" src="gl_nm2_01.wav" />
                        </prompt-segments>
                    </prompt>
                    <prompt id="hl0150_retry2_01">
                        <prompt-segments>
                            <audiofile src="hl0150_retry2_01.wav" text="Bitte w�hlen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM" />
                        </prompt-segments>
                    </prompt>
                </audio>
            </nomatchprompts>
            <nomatchprompts count="3">
                <audio>
                    <prompt id="gl_nm3_01">
                        <prompt-segments>
                            <audiofile text="Ich konnte Sie erneut nicht verstehen" src="gl_nm3_01.wav" />
                        </prompt-segments>
                    </prompt>
                    <prompt id="hl0150_retry2_01">
                        <prompt-segments>
                            <audiofile src="hl0150_retry2_01.wav" text="Bitte w�hlen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM" />
                        </prompt-segments>
                    </prompt>
                </audio>
            </nomatchprompts>
            <notoconfirmprefixes count="2" />
            <notoconfirmprefixes count="3" />
            <notoconfirmprefixes count="1" />
            <notoconfirmprompts count="1" />
        </prompt_configuration>
    </collection_configuration>
    <global_configuration confirmationmode="Never">
        <failureprompt count="1" />
        <successprompts count="1" />
        <successprompts count="2" />
        <successprompts count="3" />
        <successcorrectedprompt count="1" />
    </global_configuration>
</dm-state>
</root>
```

**Output yaml:**
```yaml
    - kind: Question
      id: hl0150_VSS_PreMenu_DM
      displayName: hl0150_VSS_PreMenu_DM
      interruptionPolicy:
        allowInterruption: true

      unrecognizedPrompt:
        speak:
          - "<audio src=\"AUDIO_LOCATION/gl_nm1_01.wav\">Entschuldigung</audio>"
          - "<audio src=\"AUDIO_LOCATION/hl0150_retry1_01.wav\">F�r Fragen zu Ihrem Verbrauch sagen Sie bitte - 'Verbrauchsinfo' und bei Anliegen rund um Ihre SIM-Karte sagen Sie - 'SIM'</audio>"
          - "<audio src=\"AUDIO_LOCATION/gl_nm2_01.wav\">Ich konnte Sie erneut nicht verstehen</audio>"
          - "<audio src=\"AUDIO_LOCATION/hl0150_retry2_01.wav\">Bitte w�hlen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM</audio>"

      alwaysPrompt: true
      variable: Global.hl0150_VSS_PreMenu_DM_reco
      prompt:
        speak:
          - "<audio src=\"AUDIO_LOCATION/hl0150_ini_01.wav\">Worum geht es bei Ihrem Anruf? Um eine Verbrauchsinformation - oder um Ihre SIM-Karte?</audio>"

      entity:
        kind: EmbeddedEntity
        definition:
          kind: ClosedListEntity
          items:
            - id: sim
              displayName: sim

            - id: usage
              displayName: usage
      voiceInputSettings:
        silenceDetectionTimeoutInMilliseconds: 7000
        repeatCountOnSilence: 2
        inputTimeoutResponse:
          speak:
            - "<audio src=\"AUDIO_LOCATION/hl0150_retry1_01.wav\">F�r Fragen zu Ihrem Verbrauch sagen Sie bitte - 'Verbrauchsinfo' und bei Anliegen rund um Ihre SIM-Karte sagen Sie - 'SIM'</audio>"
            - "<audio src=\"AUDIO_LOCATION/gl_ni2_01.wav\">Ich konnte Sie erneut nicht h�ren</audio>"
            - "<audio src=\"AUDIO_LOCATION/hl0150_retry2_01.wav\">Bitte w�hlen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM</audio>"

        defaultValueMissingAction: Escalate

    - kind: SetVariable
      id: set_REPLACE_THIS
      variable: Global.Var_hl0150_VSS_PreMenu_DM
      value: =Text(Global.hl0150_VSS_PreMenu_DM_reco)

    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Global.Var_hl0150_VSS_PreMenu_DM = "sim", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.comingFrom
              value: VSS_PreMenu
            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: hl0010_WhatsNext_DS

      elseActions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: |
                =If(Global.Var_hl0150_VSS_PreMenu_DM = "usage", true, false)
              actions:
                - kind: SetVariable
                  id: setVariable_usDhbi
                  variable: Global.comingFrom
                  value: VSS_PreMenu

                - kind: SetVariable
                  id: setVariable_REPLACE_THIS
                  variable: Global.transferTo
                  value: AgentCare

                - kind: ConditionGroup
                  id: conditionGroup_REPLACE_THIS
                  conditions:
                    - id: conditionItem_REPLACE_THIS
                      condition: |
                        =If(Or(Global.authenticated = 1 , Global.ivrCLI = "ivrIdValue"),And ( Global.ivrIdType = "MSISDN"), true, false)
                      actions:
                        - kind: BeginDialog
                          id: begin_REPLACE_THIS
                          dialog: topic.hl0120_UsageInfo_SD

                  elseActions:
                    - kind: SetVariable
                      id: setVariable_REPLACE_THIS
                      variable: Global.hangupRequired
                      value: true
                    - kind: GotoAction
                      id: xfCP5e
                      actionId: hl0010_WhatsNext_DS
```

### Example 4:
**Input XML:**
```xml
<dm-state id="rb0520_CollectVerificationPhrase_DM" className="com.nuance.riyadhbank.vocalpassword.stateengine.DMVocalPassword">
      <var name="collection_grammar1" path="passPhraseGrammar" scope="session" isNamelist="true"/>
      <var name="collection_grammar" value="rb0520_CollectVerificationPhrase_DM_dtmf.grxml" scope="session" isNamelist="true"/>
      <var name="collectionCounter" path="collectionCounter" scope="session" isNamelist="true"/>
      <var name="enrollmentLanguage" path="enrollmentLanguage" scope="session" isNamelist="true"/>
      <var name="promptCounter" path="promptCounter" scope="session" isNamelist="true"/>
      <success>
        <action label="*" next="rb0520_CollectVerificationPhrase_DM_nomatch_JDA">
    			</action>
        <action label="default" next="rb0530_SavePassPhrase_DA">
          <session-mapping key="collectionNoInputCounter" value="0" type="Integer"/>
          <session-mapping key="collectionNoMatchCounter" value="0" type="Integer"/>
          <session-mapping key="myMode" path="rb0520_CollectVerificationPhrase_DM.collection.inputmode" scope="request"/>
          <if cond="myMode == 'dtmf' || myMode == 'DTMF'">
            <session-mapping key="result" value="success" type="String"/>
            <session-mapping key="collectedPin" path="rb0520_CollectVerificationPhrase_DM.returnvalue" scope="request" type="String"/>
            <session-mapping key="pinEntered" value="true" scope="session"/>
            <action next="rb0550_EndSession_DA"/>
          </if>
          <script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
            <param name="param1" value="collectionCounter"/>
          </script>
        </action>
      </success>
      <event name="event.nuance.dialog.ndm.maxnomatches">
        <action next="rb0520_CollectVerificationPhrase_DM_nomatch_JDA"/>
      </event>
      <event name="event.nuance.dialog.ndm.maxnoinputs">
        <action next="rb0520_CollectVerificationPhrase_DM_noinput_JDA"/>
      </event>
      <collection_configuration inputmodes="voice">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <vxml_properties inputmodes="voice">
          <property_map>
            <key>recordutterance</key>
            <value>true</value>
          </property_map>
        </vxml_properties>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="collectionCounter == 0 &amp;&amp; enrollmentLanguage == 'en'">
                <prompt id="rb0520_ini_03">
                  <prompt-segments>
                    <audiofile text="'At Riyad Bank my voice is my password'" src="rb0520_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="rb0310_ini_11">
                  <prompt-segments>
                    <audiofile text="tone" src="rb0310_ini_11.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_1000">
                  <prompt-segments>
                    <audiofile text="Silence 1000 milliseconds" src="silence_1000.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="rb0520_ini_08">
                  <prompt-segments>
                    <audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="collectionCounter == 0 &amp;&amp; enrollmentLanguage == 'ar'">
                  <prompt id="rb0520_ini_04">
                    <prompt-segments>
                      <audiofile text="Arabic Passphrase" src="rb0520_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0310_ini_11">
                    <prompt-segments>
                      <audiofile text="tone" src="rb0310_ini_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000">
                    <prompt-segments>
                      <audiofile text="Silence 1000 milliseconds" src="silence_1000.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0520_ini_08">
                    <prompt-segments>
                      <audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="collectionCounter != 0 &amp;&amp; enrollmentLanguage == 'en'">
                  <prompt id="rb0520_ini_05">
                    <prompt-segments>
                      <audiofile text="One more time please " src="rb0520_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0520_ini_06">
                    <prompt-segments>
                      <audiofile text="'At Riyad Bank my voice is my password'" src="rb0520_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0310_ini_11">
                    <prompt-segments>
                      <audiofile text="tone" src="rb0310_ini_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000">
                    <prompt-segments>
                      <audiofile text="Silence 1000 milliseconds" src="silence_1000.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0520_ini_09">
                    <prompt-segments>
                      <audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="collectionCounter != 0 &amp;&amp; enrollmentLanguage == 'ar'">
                  <prompt id="rb0520_ini_05">
                    <prompt-segments>
                      <audiofile text="One more time please " src="rb0520_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0520_ini_07">
                    <prompt-segments>
                      <audiofile text="Arabic Passphrase" src="rb0520_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0310_ini_11">
                    <prompt-segments>
                      <audiofile text="tone" src="rb0310_ini_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_1000">
                    <prompt-segments>
                      <audiofile text="Silence 1000 milliseconds" src="silence_1000.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="rb0520_ini_09">
                    <prompt-segments>
                      <audiofile text="or key in your PIN, followed by the hash key" src="rb0520_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprompts count="2"/>
        </prompt_configuration>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <vxml_properties bargein="true" inputmodes="voice" termtimeout="0ms"/>
        <failureprompt count="1"/>
        <commandconfirmationprompts count="1" bargein="false" filename="none" text="none" id=""/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
      <confirmation_configuration>
        <threshold_configuration maxnomatches="1" maxnoinputs="1" maxnegativeconfirmations="1"/>
        <prompt_configuration>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_noinput_confirm1_01"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nomatch_confirm1_01"/>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
      </confirmation_configuration>
    </dm-state>
```

**Output yaml:**
```yaml
- kind: Question
      id: rb0520_CollectVerificationPhrase_DM
      displayName: rb0520_CollectVerificationPhrase_DM
      interruptionPolicy:
        allowInterruption: true

      alwaysPrompt: true
      variable: Global.rb0520_CollectVerificationPhrase_DM_reco
      prompt:
        speak:
  - |
    {
      Switch(
        true,

        // Case 1: collectionCounter = 0 && enrollmentLanguage = "en"
        Global.collectionCounter = 0 && Global.enrollmentLanguage = "en",
        Concatenate(
          "At Riyad Bank my voice is my password",
          "tone",
          "Silence 1000 milliseconds",
          "or key in your PIN, followed by the hash key"
        ),

        // Case 2: collectionCounter = 0 && enrollmentLanguage = "ar"
        Global.collectionCounter = 0 && Global.enrollmentLanguage = "ar",
        Concatenate(
          "Arabic Passphrase",
          "tone",
          "Silence 1000 milliseconds",
          "or key in your PIN, followed by the hash key"
        ),

        // Case 3: collectionCounter <> 0 && enrollmentLanguage = "en"
        Global.collectionCounter <> 0 && Global.enrollmentLanguage = "en",
        Concatenate(
          "One more time please: ...",
          "At Riyad Bank my voice is my password",
          "tone",
          "Silence 1000 milliseconds",
          "or key in your PIN, followed by the hash key"
        ),

        // Case 4: collectionCounter <> 0 && enrollmentLanguage = "ar"
        Global.collectionCounter <> 0 && Global.enrollmentLanguage = "ar",
        Concatenate(
          "One more time please: ...",
          "Arabic Passphrase",
          "tone",
          "Silence 1000 milliseconds",
          "or key in your PIN, followed by the hash key"
        ),

        // Default Case
        Concatenate("Invalid state or language.")
      )
    }


      entity: BooleanPrebuiltEntity
      voiceInputSettings:
        silenceDetectionTimeoutInMilliseconds: 7000
        repeatCountOnSilence: 2
        inputTimeoutResponse:
        defaultValueMissingAction: Escalate

    - kind: SetVariable
      id: setVariable_b2162c
      variable: Global.collectionNoInputCounter
      value: 0

    - kind: SetVariable
      id: setVariable_a0ca3c
      variable: Global.collectionNoMatchCounter
      value: 0

    - kind: SetVariable
      id: setVariable_2c8f30
      variable: Global.myMode
      value: rb0520_CollectVerificationPhrase_DM.collection.inputmode

    - kind: ConditionGroup
      id: conditionGroup_b0ddde
      conditions:
        - id: conditionItem_b578dc
          condition: =If(Global.myMode = "dtmf" || Global.myMode = "DTMF", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_f4bf04
              variable: Global.result
              value: success

            - kind: SetVariable
              id: setVariable_e8c6fd
              variable: Global.collectedPin
              value: rb0520_CollectVerificationPhrase_DM.returnvalue

            - kind: SetVariable
              id: setVariable_dd0622
              variable: Global.pinEntered
              value: true

            - kind: GotoAction
              id: goto_2f9e5a
              actionId: rb0550_EndSession_DA

    - kind: GotoAction
      id: goto_7625b2
      actionId: rb0520_CollectVerificationPhrase_DM_nomatch_JDA

    - kind: GotoAction
      id: goto_67f303
      actionId: rb0520_CollectVerificationPhrase_DM_noinput_JDA

```

### Example 5:
**Input XML:**
```xml
<dm-state id="dlhd0010_GenericMenu_DM" type="CUST">
      <success>
        <action label="ACS" next="dlhd0011_DetermineCustomMenu_JDA">
          <session-mapping key="customMenu" expr="acs"/>
        </action>
        <action label="TechOps" next="dlhd0011_DetermineCustomMenu_JDA">
          <session-mapping key="customMenu" expr="techOps"/>
        </action>
        <action label="Reservations" next="dlhd0011_DetermineCustomMenu_JDA">
          <session-mapping key="customMenu" expr="reservations"/>
        </action>
        <action label="OfficeSupport" next="dlhd0011_DetermineCustomMenu_JDA">
          <session-mapping key="customMenu" expr="officeSupport"/>
        </action>
        <action label="FltOps" next="dlhd0011_DetermineCustomMenu_JDA">
          <session-mapping key="customMenu" expr="fltOps"/>
        </action>
        <action label="IntlSupport" next="dlhd0011_DetermineCustomMenu_JDA">
          <session-mapping key="customMenu" expr="intlSupport"/>
        </action>
        <action label="MLTSupport" next="dlhd0011_DetermineCustomMenu_JDA">
          <session-mapping key="customMenu" expr="mltSupport"/>
        </action>
        <action label="EndeavorAir" next="dlhd0011_DetermineCustomMenu_JDA">
          <session-mapping key="customMenu" expr="endeavorAir"/>
        </action>
        <action label="RegionalElite" next="dlhd0011_DetermineCustomMenu_JDA">
          <session-mapping key="customMenu" expr="regionalElite"/>
        </action>
        <action label="Air4" next="dlhd0011_DetermineCustomMenu_JDA">
          <session-mapping key="customMenu" expr="air4"/>
        </action>
      </success>
      <collection_configuration>
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <dtmfgrammars filename="dlhd0010_GenericMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties termtimeout="500" interdigittimeout="500"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 MLT Support, 7 Endeavor Air support, press 8 Or for Air4 support, press 9" src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 Or, for Regional Elite, press 7" src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 MLT Support, 7 Endeavor Air support, press 8 Or for Air4 support, press 9" src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 Or, for Regional Elite, press 7" src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_ni1_01">
                <prompt-segments>
                  <audiofile text="I couldnt tell whether you pressed anything" src="tcc/gl_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 MLT Support, 7 Endeavor Air support, press 8 Or for Air4 support, press 9" src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 Or, for Regional Elite, press 7" src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="Im sorry, I still didnt hear anything" src="tcc/gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 MLT Support, 7 Endeavor Air support, press 8 Or for Air4 support, press 9" src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 Or, for Regional Elite, press 7" src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="Im sorry, I didnt get that" src="tcc/gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 MLT Support, 7 Endeavor Air support, press 8 Or for Air4 support, press 9" src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 Or, for Regional Elite, press 7" src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I still didnt understand" src="tcc/gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01" cond="elite == false">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 MLT Support, 7 Endeavor Air support, press 8 Or for Air4 support, press 9" src="tcc/dlhd0010_GenericMenu_DM_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0010_GenericMenu_DM_ini_01_elite" cond="elite == true">
                <prompt-segments>
                  <audiofile text="For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 Or, for Regional Elite, press 7" src="tcc/dlhd0010_GenericMenu_DM_ini_01_elite.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
    </dm-state>

```

**Output yaml:**
```yaml
    - kind: Question
      id: dlhd0010_GenericMenu_DM
      displayName: dlhd0010_GenericMenu_DM
      interruptionPolicy:
        allowInterruption: true

      unrecognizedPrompt:
        speak:
          - I couldnt tell whether you pressed anything
          - "{Switch(         true,         Global.elite = false,         \"For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 MLT Support, 7 Endeavor Air support, press 8 Or for Air4 support, press 9\",              Global.elite = true,         \"For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 Or, for Regional Elite, press 7\"     )}"
          - Im sorry, I still didnt hear anything
          - "{Switch(         true,         Global.elite = false,         \"For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 MLT Support, 7 Endeavor Air support, press 8 Or for Air4 support, press 9\",              Global.elite = true,         \"For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 Or, for Regional Elite, press 7\"     )}"

      alwaysPrompt: true
      variable: Global.dlhd0010_GenericMenu_DM_reco
      prompt:
        speak:
          - "{Switch(         true,         Global.elite = false,         \"For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 MLT Support, 7 Endeavor Air support, press 8 Or for Air4 support, press 9\",              Global.elite = true,         \"For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 Or, for Regional Elite, press 7\"     )}"

      entity:
        kind: EmbeddedEntity
        definition:
          kind: ClosedListEntity
          items:
            - id: ACS
              displayName: ACS

            - id: TechOps
              displayName: TechOps

            - id: Reservations
              displayName: Reservations

            - id: OfficeSupport
              displayName: OfficeSupport

            - id: FltOps
              displayName: FltOps

            - id: IntlSupport
              displayName: IntlSupport

            - id: MLTSupport
              displayName: MLTSupport

            - id: EndeavorAir
              displayName: EndeavorAir

            - id: RegionalElite
              displayName: RegionalElite

            - id: Air4
              displayName: Air4

      voiceInputSettings:
        silenceDetectionTimeoutInMilliseconds: 7000
        repeatCountOnSilence: 2
        inputTimeoutResponse:
          speak:
            - I couldnt tell whether you pressed anything
            - "{Switch(         true,         Global.elite = false,         \"For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 MLT Support, 7 Endeavor Air support, press 8 Or for Air4 support, press 9\",              Global.elite = true,         \"For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 Or, for Regional Elite, press 7\"     )}"
            - Im sorry, I still didnt hear anything
            - "{Switch(         true,         Global.elite = false,         \"For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 MLT Support, 7 Endeavor Air support, press 8 Or for Air4 support, press 9\",              Global.elite = true,         \"For Airport Operations or Cargo support, press 1 for Tech Ops support, press 2 Reservations support, 3 Office and campus support, 4 Inflight or Flight operations, 5 International support, 6 Or, for Regional Elite, press 7\"     )}"

        defaultValueMissingAction: Escalate

    
    - kind: SetVariable
      id: set_REPLACE_THIS
      variable: Global.Var_dlhd0010_GenericMenu_DM
      value: =Text(Global.dlhd0010_GenericMenu_DM_reco)

    - kind: ConditionGroup
      id: conditionGroup_d0c9b2
      conditions:
        - id: conditionItem_4a83ff
          condition: =If(Global.Var_dlhd0010_GenericMenu_DM = "ACS", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_d1adac
              variable: Global.customMenu
              value: acs

            - kind: GotoAction
              id: goto_95e6af
              actionId: dlhd0011_DetermineCustomMenu_JDA

        - id: conditionItem_h9rYKH
          condition: =If(Global.Var_dlhd0010_GenericMenu_DM = "TechOps", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_k4fex5
              variable: Global.customMenu
              value: techOps

        - id: conditionItem_KIYKhv
          condition: =If(Global.Var_dlhd0010_GenericMenu_DM = "Reservations", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_2qXWTr
              variable: Global.customMenu
              value: reservations

        - id: conditionItem_nrFNLW
          condition: =If(Global.Var_dlhd0010_GenericMenu_DM = "OfficeSupport", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_sZkWme
              variable: Global.customMenu
              value: officeSupport

        - id: conditionItem_KIYKhdf
          condition: =If(Global.Var_dlhd0010_GenericMenu_DM = "FltOps", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_2qXWgf
              variable: Global.customMenu
              value: fltOps

        - id: conditionItem_nrFNfd
          condition: =If(Global.Var_dlhd0010_GenericMenu_DM = "IntlSupport", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_sZkWser
              variable: Global.customMenu
              value: intlSupport

        - id: conditionItem_1kHIkj
          condition: =If(Global.Var_dlhd0010_GenericMenu_DM = "Air4", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_iCEBaT
              variable: Global.customMenu
              value: Air4

        - id: conditionItem_nrFN343
          condition: =If(Global.Var_dlhd0010_GenericMenu_DM = "MLTSupport", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_sZkWssdf
              variable: Global.customMenu
              value: mltSupport

        - id: conditionItem_1kHdf
          condition: =If(Global.Var_dlhd0010_GenericMenu_DM = "EndeavorAir", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_isd
              variable: Global.customMenu
              value: endeavorAir

        - id: conditionItem_nrFh43
          condition: =If(Global.Var_dlhd0010_GenericMenu_DM = "RegionalElite", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_sZkWsdfdf
              variable: Global.customMenu
              value: regionalElite

        - id: conditionItem_1kHIkj
          condition: =If(Global.Var_dlhd0010_GenericMenu_DM = "Air4", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_iCEBaT
              variable: Global.customMenu
              value: Air4
 ```
 

### Example 6:
**Input XML:**
```xml
<dm-state id="dlhd0012_ACSMenu_DM" type="CUST">
      <session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
      <success>
        <action label="1" next="EnrollAnnounceResult_PP">
          <session-mapping key="menuSelection" expr="'ACS_1'"/>
        </action>
        <action label="2" next="EnrollAnnounceResult_PP">
          <session-mapping key="menuSelection" expr="'ACS_2'"/>
        </action>
        <action label="3" next="EnrollAnnounceResult_PP">
          <session-mapping key="menuSelection" expr="'ACS_3'"/>
        </action>
        <action label="4" next="EnrollAnnounceResult_PP">
          <session-mapping key="menuSelection" expr="'ACS_4'"/>
        </action>
        <action label="5" next="EnrollAnnounceResult_PP">
          <session-mapping key="menuSelection" expr="'ACS_5'"/>
        </action>
        <action label="7" next="EnrollAnnounceResult_PP">
          <session-mapping key="menuSelection" expr="'ACS_7'"/>
        </action>
        <action label="8" next="EnrollAnnounceResult_PP">
          <session-mapping key="menuSelection" expr="'ACS_8'"/>
        </action>
      </success>
      <collection_configuration>
        <threshold_configuration maxnomatches="2" maxnoinputs="2"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <dtmfgrammars filename="dlhd0012_ACSMenu_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties termtimeout="500" interdigittimeout="500"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="dlhd0012_ACSMenu_DM_ini_02">
                <prompt-segments>
                  <audiofile text="For password issues, press 0 For Common Use Airport locations, press 1 For ACS ticketing and procedural questions, press 2 For Corporate-owned *mobile* devices, press 3 SAP and Ariba, press 4 For Financial Systems, press 5 For issues with Office 365, press 7 Or, for any other issue, press 8" src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="dlhd0012_ACSMenu_DM_ini_02">
                <prompt-segments>
                  <audiofile text="For password issues, press 0 For Common Use Airport locations, press 1 For ACS ticketing and procedural questions, press 2 For Corporate-owned *mobile* devices, press 3 SAP and Ariba, press 4 For Financial Systems, press 5 For issues with Office 365, press 7 Or, for any other issue, press 8" src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_ni1_01">
                <prompt-segments>
                  <audiofile text="I couldnt tell whether you pressed anything" src="tcc/gl_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0012_ACSMenu_DM_ini_02">
                <prompt-segments>
                  <audiofile text="For password issues, press 0 For Common Use Airport locations, press 1 For ACS ticketing and procedural questions, press 2 For Corporate-owned *mobile* devices, press 3 SAP and Ariba, press 4 For Financial Systems, press 5 For issues with Office 365, press 7 Or, for any other issue, press 8" src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="Im sorry, I still didnt hear anything" src="tcc/gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0012_ACSMenu_DM_ini_02">
                <prompt-segments>
                  <audiofile text="For password issues, press 0 For Common Use Airport locations, press 1 For ACS ticketing and procedural questions, press 2 For Corporate-owned *mobile* devices, press 3 SAP and Ariba, press 4 For Financial Systems, press 5 For issues with Office 365, press 7 Or, for any other issue, press 8" src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="Im sorry, I didnt get that" src="tcc/gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0012_ACSMenu_DM_ini_02">
                <prompt-segments>
                  <audiofile text="For password issues, press 0 For Common Use Airport locations, press 1 For ACS ticketing and procedural questions, press 2 For Corporate-owned *mobile* devices, press 3 SAP and Ariba, press 4 For Financial Systems, press 5 For issues with Office 365, press 7 Or, for any other issue, press 8" src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I still didnt understand" src="tcc/gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="dlhd0012_ACSMenu_DM_ini_02">
                <prompt-segments>
                  <audiofile text="For password issues, press 0 For Common Use Airport locations, press 1 For ACS ticketing and procedural questions, press 2 For Corporate-owned *mobile* devices, press 3 SAP and Ariba, press 4 For Financial Systems, press 5 For issues with Office 365, press 7 Or, for any other issue, press 8" src="tcc/dlhd0012_ACSMenu_DM_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
      </global_configuration>
    </dm-state>

```

**Output yaml:**
```yaml
- kind: DialogComponent
    displayName: SimpleDM_Dialog
    parentBotId: 4a1f5002-3044-ef11-8409-6045bd0303b3
    schemaName: topic.SimpleDM_Dialog
    dialog:
      beginDialog:
        kind: OnRecognizedIntent
        id: main
        actions:
          - kind: Question
            id: dlhd0012_ACSMenu_DM
            displayName: dlhd0012_ACSMenu_DM
            interruptionPolicy:
              allowInterruption: true
            unrecognizedPrompt:
              speak:
                - "<audio src=\"AUDIO_LOCATION/gl_nm1_01.wav\">Im sorry, I didnt get that</audio>"
                - "<audio src=\"AUDIO_LOCATION/dlhd0012_ACSMenu_DM_ini_02.wav\">For password issues, press 0 For Common Use Airport locations, press 1 For ACS ticketing and procedural questions, press 2 For Corporate-owned *mobile* devices, press 3 SAP and Ariba, press 4 For Financial Systems, press 5 For issues with Office 365, press 7 Or, for any other issue, press 8</audio>"
                
                - "<audio src=\"AUDIO_LOCATION/gl_nm2_01.wav\">I still didnt understand</audio>"
                - "<audio src=\"AUDIO_LOCATION/dlhd0012_ACSMenu_DM_ini_02.wav\">For password issues, press 0 For Common Use Airport locations, press 1 For ACS ticketing and procedural questions, press 2 For Corporate-owned *mobile* devices, press 3 SAP and Ariba, press 4 For Financial Systems, press 5 For issues with Office 365, press 7 Or, for any other issue, press 8</audio>"
            alwaysPrompt: true
            variable: Global.dlhd0012_ACSMenu_DM_reco
            prompt:
              speak:
                - "<audio src=\"AUDIO_LOCATION/dlhd0012_ACSMenu_DM_ini_02.wav\">For password issues, press 0 For Common Use Airport locations, press 1 For ACS ticketing and procedural questions, press 2 For Corporate-owned *mobile* devices, press 3 SAP and Ariba, press 4 For Financial Systems, press 5 For issues with Office 365, press 7 Or, for any other issue, press 8</audio>"
            entity:
              kind: EmbeddedEntity
              definition:
                kind: ClosedListEntity
                items:
                  - id: 1
                    displayName: 1
                  - id: 2
                    displayName: 2
                  - id: 3
                    displayName: 3
                  - id: 4
                    displayName: 4
                  - id: 5
                    displayName: 5
                  - id: 7
                    displayName: 7
                  - id: 8
                    displayName: 8
            voiceInputSettings:
              silenceDetectionTimeoutInMilliseconds: 7000
              repeatCountOnSilence: 2
              inputTimeoutResponse:
                speak:
                  - "<audio src=\"AUDIO_LOCATION/gl_ni1_01.wav\">Im sorry, I didnt get that</audio>"
                - "<audio src=\"AUDIO_LOCATION/dlhd0012_ACSMenu_DM_ini_02.wav\">For password issues, press 0 For Common Use Airport locations, press 1 For ACS ticketing and procedural questions, press 2 For Corporate-owned *mobile* devices, press 3 SAP and Ariba, press 4 For Financial Systems, press 5 For issues with Office 365, press 7 Or, for any other issue, press 8</audio>"
                
                - "<audio src=\"AUDIO_LOCATION/gl_ni2_01.wav\">I still didnt understand</audio>"
                - "<audio src=\"AUDIO_LOCATION/dlhd0012_ACSMenu_DM_ini_02.wav\">For password issues, press 0 For Common Use Airport locations, press 1 For ACS ticketing and procedural questions, press 2 For Corporate-owned *mobile* devices, press 3 SAP and Ariba, press 4 For Financial Systems, press 5 For issues with Office 365, press 7 Or, for any other issue, press 8</audio>"
              defaultValueMissingAction: Escalate
          - kind: SetVariable
            id: set_4e93b5
            variable: Global.Var_dlhd0012_ACSMenu_DM
            value: =Text(Global.dlhd0012_ACSMenu_DM_reco)
          - kind: ConditionGroup
            id: conditionGroup_ba12d4
            conditions:
              - id: conditionItem_dbe869
                condition: |-
                  =If(Global.Var_dlhd0012_ACSMenu_DM = "1", true, false)
                actions:
                  - kind: SetVariable
                    id: setVariable_db2d05
                    variable: Global.menuSelection
                    value: ACS_1
                  - kind: GotoAction
                    id: goto_69c4c1
                    actionId: EnrollAnnounceResult_PP
            elseActions:
              - kind: ConditionGroup
                id: conditionGroup_4291ee
                conditions:
                  - id: conditionItem_9ab083
                    condition: |-
                      =If(Global.Var_dlhd0012_ACSMenu_DM = "2", true, false)
                    actions:
                      - kind: SetVariable
                        id: setVariable_689e06
                        variable: Global.menuSelection
                        value: ACS_2
                      - kind: GotoAction
                        id: goto_768313
                        actionId: EnrollAnnounceResult_PP
                elseActions:
                  - kind: ConditionGroup
                    id: conditionGroup_a5b9e8
                    conditions:
                      - id: conditionItem_f37db2
                        condition: |-
                          =If(Global.Var_dlhd0012_ACSMenu_DM = "3", true, false)
                        actions:
                          - kind: SetVariable
                            id: setVariable_efa0e8
                            variable: Global.menuSelection
                            value: ACS_3
                          - kind: GotoAction
                            id: goto_b1910e
                            actionId: EnrollAnnounceResult_PP
                    elseActions:
                      - kind: ConditionGroup
                        id: conditionGroup_a92c4b
                        conditions:
                          - id: conditionItem_add189
                            condition: |-
                              =If(Global.Var_dlhd0012_ACSMenu_DM = "4", true, false)
                            actions:
                              - kind: SetVariable
                                id: setVariable_2e25a7
                                variable: Global.menuSelection
                                value: ACS_4
                              - kind: GotoAction
                                id: goto_384149
                                actionId: EnrollAnnounceResult_PP
                        elseActions:
                          - kind: ConditionGroup
                            id: conditionGroup_055770
                            conditions:
                              - id: conditionItem_8605e9
                                condition: |-
                                  =If(Global.Var_dlhd0012_ACSMenu_DM = "5", true, false)
                                actions:
                                  - kind: SetVariable
                                    id: setVariable_f33f14
                                    variable: Global.menuSelection
                                    value: ACS_5
                                  - kind: GotoAction
                                    id: goto_ad23fa
                                    actionId: EnrollAnnounceResult_PP
                            elseActions:
                              - kind: ConditionGroup
                                id: conditionGroup_6f2c72
                                conditions:
                                  - id: conditionItem_0de96a
                                    condition: |-
                                      =If(Global.Var_dlhd0012_ACSMenu_DM = "7", true, false)
                                    actions:
                                      - kind: SetVariable
                                        id: setVariable_4e0ed1
                                        variable: Global.menuSelection
                                        value: ACS_7
                                      - kind: GotoAction
                                        id: goto_0d445c
                                        actionId: EnrollAnnounceResult_PP
                                elseActions:
                                  - kind: ConditionGroup
                                    id: conditionGroup_4c667b
                                    conditions:
                                      - id: conditionItem_a96180
                                        condition: |-
                                          =If(Global.Var_dlhd0012_ACSMenu_DM = "8", true, false)
                                        actions:
                                          - kind: SetVariable
                                            id: setVariable_ab388d
                                            variable: Global.menuSelection
                                            value: ACS_8
                                          - kind: GotoAction
                                            id: goto_ca28da
                                            actionId: EnrollAnnounceResult_PP
 ```

 
### Example 7:
**Input XML:**
```xml
<dm-state id="dlhd0004_ConfirmPPRNumber_DM" type="CUST">
			<session-mapping key="collection_dtmfparallelgrammar1" value="" type="String"/>
			<success>
				<action label="yes" next="GenericMenu.dvxml">
					<session-mapping key="customMenu" expr="generic"/>
					<session-mapping key="pprNumber" expr="''"/>
				</action>
				<action label="no" next="dummy">
					<if cond="PPRAttemptCount &lt; 2">
						<audio>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_out_02">
								<prompt-segments>
									<audiofile text="Okay. Lets try again." src="tcc/dlhd0004_ConfirmPPRNumber_DM_out_02.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
						<audio>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_03">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
						<action next="dlhd0003_GetPPRNumber_DM"/>
						<else>
							<audio>
								<prompt id="dlhd0004_ConfirmPPRNumber_DM_out_03">
									<prompt-segments>
										<audiofile text="Lets try this a different way." src="tcc/dlhd0004_ConfirmPPRNumber_DM_out_03.wav"/>
									</prompt-segments>
								</prompt>
							</audio>
							<audio>
								<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_04">
									<prompt-segments>
										<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_04.wav"/>
									</prompt-segments>
								</prompt>
							</audio>
							<session-mapping key="customMenu" expr="generic"/>
							<gotodialog next="GenericMenu"/>
						</else>
					</if>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<audio>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_ni3_01">
						<prompt-segments>
							<audiofile text="I still didnt get that, but lets continue." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ni3_01.wav"/>
						</prompt-segments>
					</prompt>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_06">
						<prompt-segments>
							<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_06.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="customMenu" expr="generic"/>
				<gotodialog next="GenericMenu"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<audio>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_nm3_01">
						<prompt-segments>
							<audiofile text="I still didnt get that, but lets continue." src="tcc/dlhd0004_ConfirmPPRNumber_DM_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_05">
						<prompt-segments>
							<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_05.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="customMenu" expr="generic"/>
				<gotodialog next="GenericMenu"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxrepeats">
				<audio>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_nm3_01">
						<prompt-segments>
							<audiofile text="I still didnt get that, but lets continue." src="tcc/dlhd0004_ConfirmPPRNumber_DM_nm3_01.wav"/>
						</prompt-segments>
					</prompt>
					<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_05">
						<prompt-segments>
							<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_05.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<session-mapping key="customMenu" expr="generic"/>
				<gotodialog next="GenericMenu"/>
			</event>
			<event name="event.nuance.dialog.ndm.internalerror">
				<audio>
					<prompt id="gl_technicalerror1_01">
						<prompt-segments>
							<audiofile text="Im sorry, but were experiencing technical difficulties.  Please call back later.  Goodbye!" src="tcc/gl_technicalerror1_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<audio>
					<prompt id="gl_maxerror5_01">
						<prompt-segments>
							<audiofile text="Sorry were having trouble.  Please call back later.  Goodbye!" src="tcc/gl_maxerror5_01.wav"/>
						</prompt-segments>
					</prompt>
				</audio>
				<gotodialog next="Exit"/>
			</event>
			<collection_configuration>
				<threshold_configuration maxnomatches="2" maxnoinputs="2"/>
				<grammar_configuration supportsadaptivegrammar="false">
					<dtmfgrammars filename="dlhd0004_ConfirmPPRNumber_DM_dtmf.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties termtimeout="500" interdigittimeout="500"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ini_01">
								<prompt-segments>
									<audiofile text="You entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ini_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_01">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ini_03">
								<prompt-segments>
									<audiofile text="If thats right, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ini_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<helpprompts count="1" filename="none" text="none" id=""/>
					<helpprompts count="2" filename="none" text="none" id=""/>
					<repeatprompts count="1">
						<audio>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="To continue, I need to confirm your PPR number.  I think you entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_02">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_02.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_03">
								<prompt-segments>
									<audiofile text="If thats correct, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="gl_ni1_01">
								<prompt-segments>
									<audiofile text="I couldnt tell whether you pressed anything." src="tcc/gl_ni1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="To continue, I need to confirm your PPR number.  I think you entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_02">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_02.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_03">
								<prompt-segments>
									<audiofile text="If thats correct, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Im sorry, I still didnt hear anything." src="tcc/gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="To continue, I need to confirm your PPR number.  I think you entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_02">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_02.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_03">
								<prompt-segments>
									<audiofile text="If thats correct, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Im sorry, I didnt get that." src="tcc/gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="To continue, I need to confirm your PPR number.  I think you entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_02">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_02.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_03">
								<prompt-segments>
									<audiofile text="If thats correct, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="I still didnt understand." src="tcc/gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_01">
								<prompt-segments>
									<audiofile text="To continue, I need to confirm your PPR number.  I think you entered" src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt type="number" expr="pprNumber">
								<param name="speakAs" value="digits"/>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_sil_02">
								<prompt-segments>
									<audiofile text="..." src="tcc/dlhd0004_ConfirmPPRNumber_DM_sil_02.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="dlhd0004_ConfirmPPRNumber_DM_ree_03">
								<prompt-segments>
									<audiofile text="If thats correct, press 1. Otherwise, press 2." src="tcc/dlhd0004_ConfirmPPRNumber_DM_ree_03.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="5" maxnomatches="2" maxnoinputs="2"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>

 ```
**Output yaml:**
```yaml
     - kind: Question
      id: dlhd0004_ConfirmPPRNumber_DM
      displayName: dlhd0004_ConfirmPPRNumber_DM
      interruptionPolicy:
        allowInterruption: true

      unrecognizedPrompt:
        speak:
          - Im sorry, I didnt get that
          - I still didnt understand

      alwaysPrompt: true
      variable: Global.dlhd0004_ConfirmPPRNumber_DM_reco
      prompt:
        speak:
          - You entered {Global.pprNumber} If thats right, press 1 Otherwise, press 2

      entity: BooleanPrebuiltEntity
      voiceInputSettings:
        silenceDetectionTimeoutInMilliseconds: 7000
        repeatCountOnSilence: 2
        inputTimeoutResponse:
          speak:
            - I couldnt tell whether you pressed anything
            - To continue, I need to confirm your PPR number  I think you entered {Global.pprNumber} If thats correct, press 1 Otherwise, press 2
            - Im sorry, I still didnt hear anything
            - To continue, I need to confirm your PPR number  I think you entered {Global.pprNumber} If thats correct, press 1 Otherwise, press 2

        defaultValueMissingAction: Escalate

    - kind: SetVariable
      id: set_9d837f
      variable: Global.Var_dlhd0004_ConfirmPPRNumber_DM
      value: =Text(Global.dlhd0004_ConfirmPPRNumber_DM_reco)

    - kind: ConditionGroup
      id: conditionGroup_f313f2
      conditions:
        - id: conditionItem_6ad3ba
          condition: =If(Global.Var_dlhd0004_ConfirmPPRNumber_DM = "yes", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_688ba2
              variable: Global.customMenu
              value: generic

            - kind: SetVariable
              id: setVariable_c1de28
              variable: Global.pprNumber
              value: 1

            - kind: BeginDialog
              id: begin_ed92c8
              dialog: crd93_voice_delta43.topic.GenericMenu_Dialog

      elseActions:
        - kind: ConditionGroup
          id: conditionGroup_9ccd04
          conditions:
            - id: conditionItem_e8f090
              condition: =If(Global.Var_dlhd0004_ConfirmPPRNumber_DM = "no", true, false)
              actions:
                - kind: ConditionGroup
                  id: conditionGroup_066c22
                  conditions:
                    - id: conditionItem_69dcca
                      condition: =If(Global.PPRAttemptCount < 2, true, false)
                      actions:
                        - kind: SendActivity
                          id: sendActivity_QtOuAd
                          activity:
                            speak:
                              - "<audio src=\"AUDIO_LOCATION/dlhd0004_ConfirmPPRNumber_DM_out_02.wav\">Okay Lets try again</audio>"
                              - "<audio src=\"AUDIO_LOCATION/dlhd0004_ConfirmPPRNumber_DM_sil_03.wav\">...</audio>"

                        - kind: GotoAction
                          id: goto_d2391c
                          actionId: dlhd0003_GetPPRNumber_DM

                  elseActions:
                    - kind: SendActivity
                      id: sendActivity_4gkNOe
                      activity:
                        speak:
                          - "<audio src=\"AUDIO_LOCATION/dlhd0004_ConfirmPPRNumber_DM_out_03.wav\">Lets try this a different way</audio>"
                          - "<audio src=\"AUDIO_LOCATION/dlhd0004_ConfirmPPRNumber_DM_sil_04.wav\">...</audio>"

                    - kind: SetVariable
                      id: setVariable_914808
                      variable: Global.customMenu
                      value: generic

                    - kind: BeginDialog
                      id: begin_892e41
                      dialog: crd93_voice_delta43.topic.GenericMenu_Dialog

```