<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="SubmitChangeOffer_Dialog">
    <decision-state id="SO1001_CheckContext_DS">
      <session-mapping key="GlobalVars.orderId" expr=""/>
      <if cond="((GlobalVars.newRatePlan != undefined) &amp;&amp;(GlobalVars.newRatePlan != ''))">
        <session-mapping key="newPlanVars.eventTypeGMT" expr="getEventTime()"/>
        <session-mapping key="newPlanVars.status" expr="'incomplete'"/>
        <session-mapping key="newPlanVars.eventType" expr="'newRatePlan'"/>
      </if>
      <if cond="((GlobalVars.addFeatures != undefined) &amp;&amp;(GlobalVars.addFeatures != ''))">
        <session-mapping key="addFeatureVars.eventTypeGMT" expr="getEventTime()"/>
        <session-mapping key="addFeatureVars.status" expr="'incomplete'"/>
        <session-mapping key="addFeatureVars.eventType" expr="'FeatureAddFE'"/>
      </if>
      <session-mapping key="GlobalVars.autoCorrectApproved" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.ratePlanChangeDBErrorCounter" expr="0"/>
      <action next="SO1005_SubmitChangeOffer_DB_DA"/>
    </decision-state>

    <data-access-state id="SO1005_SubmitChangeOffer_DB_DA">
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="currentRatePlan" value="GlobalVars.GetAccountDetails.ratePlan" type="String"/>
      <session-mapping key="newRatePlan" value="GlobalVars.newRatePlan" type="String"/>
      <session-mapping key="autoCorrectApproved" value="GlobalVars.autoCorrectApproved" type="String"/>
      <session-mapping key="addFeatures" value="GlobalVars.addFeatures" type="String"/>
      <session-mapping key="removeFeatures" value="GlobalVars.removeFeatures" type="String"/>
      <session-mapping key="addFuturePromo" value="" type="String"/>
      <session-mapping key="addRegularPromo" value="" type="String"/>
      <session-mapping key="removeFuturePromoSoc" value="GlobalVars.removeFuturePromoSoc" type="String"/>
      <session-mapping key="removeRegularPromoSoc" value="GlobalVars.removeRegularPromoSoc" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="futureDate" value="GlobalVars.futureDate" type="String"/>
      <session-mapping key="isUpgrade" value="false" type="Boolean"/>
      <session-mapping key="orderId" value="GlobalVars.orderId" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="clearCache" value="true" type="Boolean"/>
      <data-access id="SubmitChangeOffer_Dialog" classname="com.nuance.metro.dataaccess.SubmitChangeOffer">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="mdn"/>
          <input-variable name="JWTToken"/>
          <input-variable name="currentRatePlan"/>
          <input-variable name="newRatePlan"/>
          <input-variable name="autoCorrectApproved"/>
          <input-variable name="futureDate"/>
          <input-variable name="isUpgrade"/>
          <input-variable name="removeFeatures"/>
          <input-variable name="addFuturePromo"/>
          <input-variable name="addRegularPromo"/>
          <input-variable name="removeFuturePromoSoc"/>
          <input-variable name="removeRegularPromoSoc"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="isAutoCorrectRequired"/>
          <output-variable name="dueImmediatlyAmount"/>
          <output-variable name="GetSubscriberDetails.pin" mask="true"/>
          <output-variable name="GetSubscriberDetails.securityAnswer" mask="true"/>
          <output-variable name="GetSubscriberDetails.accountNumber" mask="true"/>
          <output-variable name="GetSubscriberDetails.zipCode" mask="true"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="SubmitChangeOffer.status == 'Success'">
          <session-mapping key="GlobalVars.SubmitChangeOffer" expr="SubmitChangeOffer_Dialog"/>
          <session-mapping key="GlobalVars.isAutoCorrectRequired" expr="SubmitChangeOffer.isAutoCorrectRequired"/>
          <session-mapping key="GlobalVars.accountUpdateFailed" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.orderId" expr="SubmitChangeOffer.orderId"/>
          <if cond="SubmitChangeOffer.triedToUpdateSubscriberDetails == true || SubmitChangeOffer.triedToUpdateSubscriberDetails == 'true' ">
            <if cond="SubmitChangeOffer.GetSubscriberDetails &amp;&amp; SubmitChangeOffer.GetSubscriberDetails.status &amp;&amp; SubmitChangeOffer.GetSubscriberDetails.status.toUpperCase() == 'SUCCESS' &amp;&amp; SubmitChangeOffer.GetSubscriberDetails.validAccount">
              <session-mapping key="GlobalVars.GetAccountDetails" expr="SubmitChangeOffer.GetSubscriberDetails"/>
              <session-mapping key="GlobalVars.marketID" expr="SubmitChangeOffer.GetSubscriberDetails.marketID"/>
              <session-mapping key="GlobalVars.accountUpdateFailed" value="false" type="Boolean"/>
              <else>
                <session-mapping key="GlobalVars.accountUpdateFailed" value="true" type="Boolean"/>
              </else>
            </if>
          </if>
          <action next="SO1010_IsAutoCorrectNeeded_JDA"/>
          <else>
            <if cond="((newRatePlan != undefined) &amp;&amp;(newRatePlan != ''))">
              <session-mapping key="newPlanVars.eventTypeGMT" expr="getEventTime()"/>
              <session-mapping key="newPlanVars.status" expr="'failure'"/>
              <session-mapping key="newPlanVars.eventType" expr="'newRatePlan'"/>
              <if cond="(GlobalVars.GetBCSParameters.care_enable_rateplan_special_error_handling == 'true'&amp;&amp; GlobalVars.care_rateplan_special_error_planlist != null &amp;&amp; GlobalVars.care_rateplan_special_error_planlist != '' &amp;&amp; GlobalVars.care_rateplan_special_error_planlist != undefined) &amp;&amp; (GlobalVars.care_rateplan_special_error_planlist.toLowerCase().indexOf(newRatePlan.toLowerCase()) != -1)">
                <if cond="GlobalVars.ratePlanChangeDBErrorCounter == 0">
                  <session-mapping key="GlobalVars.ratePlanChangeDBErrorCounter" expr="GlobalVars.ratePlanChangeDBErrorCounter + 1"/>
                  <action next="SO1005_SubmitChangeOffer_DB_DA"/>
                  <else>
                    <action next="SO1305_PlaySpecialErrorPrompt_PP"/>
                  </else>
                </if>
              </if>
            </if>
            <if cond="((addFeatures != undefined) &amp;&amp;(addFeatures != ''))">
              <session-mapping key="addFeatureVars.eventTypeGMT" expr="getEventTime()"/>
              <session-mapping key="addFeatureVars.status" expr="'failure'"/>
              <session-mapping key="addFeatureVars.eventType" expr="'FeatureAddFE'"/>
            </if>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
      </data-access-state>

    <decision-state id="SO1010_IsAutoCorrectNeeded_DS">
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.SubmitChangeOffer.dueImmediatlyAmount" type="String"/>
      <session-mapping key="isAutoCorrectRequired" value="GlobalVars.isAutoCorrectRequired" type="String"/>
      <if cond="isAutoCorrectRequired == true">
        <action next="SO1105_IsAutoCorrectEnabled_JDA"/>
        <else>
          <if cond="(GlobalVars.autoCorrectApproved == false)">
            <action next="SO1401_CheckUpdates_JDA"/>
            <elseif cond="((GlobalVars.newRatePlan != undefined) &amp;&amp;(GlobalVars.newRatePlan != ''))">
              <session-mapping key="newPlanVars.eventTypeGMT" expr="getEventTime()"/>
              <session-mapping key="newPlanVars.status" expr="'success'"/>
              <session-mapping key="newPlanVars.eventType" expr="'newRatePlan'"/>
              <session-mapping key="GlobalVars.newPlanPrice" expr="GlobalVars.GetAccountDetails.ratePlanPrice"/>
            </elseif>
          </if>
          <if cond="((GlobalVars.addFeatures != undefined) &amp;&amp;(GlobalVars.addFeatures != ''))">
            <session-mapping key="addFeatureVars.eventTypeGMT" expr="getEventTime()"/>
            <session-mapping key="addFeatureVars.status" expr="'success'"/>
            <session-mapping key="addFeatureVars.eventType" expr="'FeatureAddFE'"/>
          </if>
          <action next="SO1205_CheckIfPlanChange_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="SO1105_IsAutoCorrectEnabled_DS">
      <if cond="GlobalVars.care_enable_autocorrect_offer == true || GlobalVars.care_enable_autocorrect_offer == 'true'">
        <action next="SO1401_CheckUpdates_JDA"/>
        <else>
          <action next="SO1125_MetricsAutoCorrectDisabled_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="SO1125_MetricsAutoCorrectDisabled_DS">
      <action next="SO1130_TransferAutoCorrect_SD"/>
    </decision-state>

    <subdialog-state id="SO1130_TransferAutoCorrect_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
    </subdialog-state>
    <decision-state id="SO1205_CheckIfPlanChange_DS">
      <if cond="((GlobalVars.newRatePlan == undefined) || (GlobalVars.newRatePlan == ''))">
        <session-mapping key="GlobalVars.addFeatures" expr="undefined"/>
        <action next="getReturnLink()"/>
        <else>
          <session-mapping key="GlobalVars.newRatePlan" expr="undefined"/>
          <action next="SO1210_CheckPriceDifference_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="SO1210_CheckPriceDifference_DS">
      <if cond="(GlobalVars.newPlanPrice &lt; GlobalVars.oldPlanPrice)">
        <action next="SO1215_MetricsPlanDowngrade_JDA"/>
        <else>
          <action next="SO1220_MetricsPlanUpgrade_JDA"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="SO1215_MetricsPlanDowngrade_DS">
      <action next="getReturnLink()"/>
    </decision-state>

    <decision-state id="SO1220_MetricsPlanUpgrade_DS">
      <action next="getReturnLink()"/>
    </decision-state>

    <play-state id="SO1305_PlaySpecialErrorPrompt_PP">
      <session-mapping key="broadcastMessageKey" value="CARE_RSE" type="String"/>
      <session-mapping key="broadcastMessage" value="GlobalVars.GetBCSParameters.care_rateplan_special_error_broadcast_message" type="String"/>
      <audio>
        <prompt type="custom" expr="broadcastMessageKey" cond="broadcastMessage == 'Custom'" bargein="false">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayBroadcastMessage"/>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SO1310_CheckSpecialErrorAction_JDA"/>
    </play-state>

    <decision-state id="SO1310_CheckSpecialErrorAction_DS">
      <session-mapping key="broadcastMessageAction" value="GlobalVars.GetBCSParameters.care_rateplanSpecialError_broadcastMessageAction" type="String"/>
      <if cond="broadcastMessageAction != null &amp;&amp; broadcastMessageAction != ''">
        <session-mapping key="broadcastMessageAction" expr="broadcastMessageAction.toLowerCase()"/>
      </if>
      <if cond="broadcastMessageAction == 'disconnect'">
        <action next="SO1320_PlanChangeSpecialErrorEnd_SD"/>
        <elseif cond="broadcastMessageAction == 'transfer'">
          <action next="SO1315_PlanChangeSpecialErrorTransfer_SD"/>
        </elseif>
        <else>
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.returnToMainMenu" value="true" type="Boolean"/>
          <action next="SO1325_PlanChangeSpecialErrorRestart_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="SO1315_PlanChangeSpecialErrorTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
    </subdialog-state>
    <subdialog-state id="SO1320_PlanChangeSpecialErrorEnd_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </subdialog-state>
    <subdialog-state id="SO1325_PlanChangeSpecialErrorRestart_SD">
      <gotodialog next="InitialHandling_Main_Dialog"/>
    </subdialog-state>
    <decision-state id="SO1401_CheckUpdates_DS">
      <if cond="(GlobalVars.SubmitChangeOffer.dueImmediatlyAmount == 0) &amp;&amp; (GlobalVars.SubmitChangeOffer.priorDueImmediatelyAmt == 0) &amp;&amp; (GlobalVars.SubmitChangeOffer.amountDue == GlobalVars.GetAccountDetails.balance) &amp;&amp; (GlobalVars.SubmitChangeOffer.isAutoCorrectRequired == false)">
        <action next="SO1411_SetUpdatesVars_JDA"/>
        <else>
          <action next="SO1404_PlayUpdatesTransition_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="SO1404_PlayUpdatesTransition_PP">
      <audio>
        <prompt id="SO1404_out_01">
          <prompt-segments>
            <audiofile text="Before I update your account" src="SO1404_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SO1405_PlayUpdates_PP"/>
    </play-state>

    <play-state id="SO1405_PlayUpdates_PP">
      <session-mapping key="priorDueImmediatelyAmt" value="GlobalVars.SubmitChangeOffer.priorDueImmediatelyAmt" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.SubmitChangeOffer.dueImmediatlyAmount" type="String"/>
      <session-mapping key="amountDue" value="GlobalVars.SubmitChangeOffer.amountDue" type="String"/>
      <session-mapping key="anniversaryDate" value="GlobalVars.GetAccountDetails.payDate" type="String"/>
      <session-mapping key="accountStatus" value="(GlobalVars.GetAccountDetails)?GlobalVars.GetAccountDetails.accountStatus :''" type="String"/>
      <session-mapping key="isAutoCorrectRequired" value="GlobalVars.SubmitChangeOffer.isAutoCorrectRequired" type="String"/>
      <audio>
        <prompt id="SO1405_PlayUpdates_PP_audio"/>
      </audio>
      <action next="SO1410_ApproveUpdates_DM"/>
    </play-state>

    <dm-state id="SO1410_ApproveUpdates_DM" type="CUST">
      <success>
        <action label="approve">
          <audio>
            <prompt id="SO1410_out_03">
              <prompt-segments>
                <audiofile text="Great, I'll make the change" src="SO1410_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="SO1411_SetUpdatesVars_JDA"/>
        </action>
        <action label="repeat">
          <audio>
            <prompt id="SO1410_out_04">
              <prompt-segments>
                <audiofile text="Sure" src="SO1410_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="SO1405_PlayUpdates_PP"/>
        </action>
        <action label="cancel">
          <audio>
            <prompt id="SO1410_out_05">
              <prompt-segments>
                <audiofile text="No problem, I won't change anything" src="SO1410_out_05.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="SO1415_CancelPendingOrder_DB_DA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SO1410_ini_04">
                <prompt-segments>
                  <audiofile text="Say 'approve', 'repeat' or 'cancel'  " src="SO1410_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SO1410_ini_04">
                <prompt-segments>
                  <audiofile text="Say 'approve', 'repeat' or 'cancel'  " src="SO1410_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SO1410_ini_04">
                <prompt-segments>
                  <audiofile text="Say 'approve', 'repeat' or 'cancel'  " src="SO1410_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SO1410_nm2_02">
                <prompt-segments>
                  <audiofile text="To continue with this change, say 'approve' or press 1 Or say 'repeat' or press 2, or 'cancel' or press 3" src="SO1410_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SO1410_nm2_02">
                <prompt-segments>
                  <audiofile text="To continue with this change, say 'approve' or press 1 Or say 'repeat' or press 2, or 'cancel' or press 3" src="SO1410_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SO1410_ini_04">
                <prompt-segments>
                  <audiofile text="Say 'approve', 'repeat' or 'cancel'  " src="SO1410_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SO1410_nm2_02">
                <prompt-segments>
                  <audiofile text="To continue with this change, say 'approve' or press 1 Or say 'repeat' or press 2, or 'cancel' or press 3" src="SO1410_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SO1410_nm2_02">
                <prompt-segments>
                  <audiofile text="To continue with this change, say 'approve' or press 1 Or say 'repeat' or press 2, or 'cancel' or press 3" src="SO1410_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SO1410_ini_04">
                <prompt-segments>
                  <audiofile text="Say 'approve', 'repeat' or 'cancel'  " src="SO1410_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="SO1410_ApproveUpdates_DM.grxml" count="1"/>
          <dtmfgrammars filename="SO1410_ApproveUpdates_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY" dmoutputkeys="eventLogs|failurereason|returnvalue|returncode|nbestresults|confidencescore|returnkeys">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'approve'">
                <prompt id="SO1410_cnf_ini_03">
                  <prompt-segments>
                    <audiofile text="You approve this change" src="SO1410_cnf_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'cancel'">
                <prompt id="SO1410_cnf_ini_04">
                  <prompt-segments>
                    <audiofile text="Cancel this change" src="SO1410_cnf_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="SO1410_ApproveUpdates_DM_confirmation_nomatch"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="SO1410_ApproveUpdates_DM_confirmation_nomatch"/>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="SO1411_SetUpdatesVars_DS">
      <session-mapping key="GlobalVars.autoCorrectApproved" value="true" type="Boolean"/>
      <action next="SO1005_SubmitChangeOffer_DB_DA"/>
    </decision-state>

    <data-access-state id="SO1415_CancelPendingOrder_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="orderId" value="GlobalVars.orderId" type="String"/>
      <data-access id="CancelPendingOrder" classname="com.nuance.metro.dataaccess.CancelPendingOrder">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="mdn"/>
          <input-variable name="orderId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="orderCancelStatus"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.CancelPendingOrder" expr="CancelPendingOrder"/>
        <if cond="GlobalVars.CancelPendingOrder &amp;&amp; GlobalVars.CancelPendingOrder.status &amp;&amp; GlobalVars.CancelPendingOrder.status.toUpperCase() == 'SUCCESS'">
          <session-mapping key="GlobalVars.orderCancelStatus" expr="GlobalVars.CancelPendingOrder.orderCancelStatus"/>
          <if cond="GlobalVars.orderCancelStatus.toUpperCase() == 'ORDER CANCELED'">
            <session-mapping key="GlobalVars.cancelPlanChange" value="true" type="Boolean"/>
            <action next="getReturnLink()"/>
            <else>
              <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
              <gotodialog next="CallTransfer_Main_Dialog"/>
            </else>
          </if>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

  </dialog>
  