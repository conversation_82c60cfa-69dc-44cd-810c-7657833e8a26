import os
import subprocess
from step0_input_cleanup import process_xml


# run_all_in_one.py
local_run = True

if __name__ == "__main__":
    # List of script filenames you want to run sequentially
    scripts = [
        "step1_separate_dialogs.py",
        "step2_generate_topic_template.py",
        "step3_variable_extraction.py",
        "step4_callAgentDecision.py",
        "step5_generate_bot_yaml.py",
        "step6_post_processing.py"
    ]
    input_xml_file = 'input/updated-state-engine-Metro_V2.xml'
    if os.path.exists('agent.log'):
         os.remove('agent.log')
    process_xml(input_xml_file)
    # Run each script one by one
    for script in scripts:
        if os.path.exists(script):
            #print(f"local_run value: {post_processing.local_run}")
            print(f"Running {script}...")
            result = subprocess.run(["python", script], capture_output=True, text=True)
            print(result.stdout)
            if result.returncode != 0:
                print(f"Error occurred while running {script}:")
                print(result.stderr)
            print(f"Finished running {script}\n")
        else:
            print(f"Script {script} does not exist.")
