import xml.etree.ElementTree as ET
import re, os
from xml.dom import minidom
from util import read_filenames_in_folder, delete_existing_files, merge_variable_files
from collections import OrderedDict

# Function to parse an XML file and extract session-mappings
def parse_xml(file_path):
    tree = ET.parse(file_path)
    root = tree.getroot()
    mappings = OrderedDict()

    for session_mapping in root.findall('.//session-mapping'):
        key = session_mapping.get('key') or "undefined"  # Handle None by providing default
        value = session_mapping.get('value') or "undefined"  # Handle None by providing default
        type_ = session_mapping.get('type') or "undefined"  # Handle None by providing default

        # Add to mappings, overwriting if key already exists (removes duplicates)
        mappings[key] = {'value': value, 'type': type_}

    return mappings, root

# Function to merge session-mappings and remove duplicates
def merge_mappings(*args):
    combined_mappings = OrderedDict()

    for mapping in args:
        for key, attributes in mapping.items():
            combined_mappings[key] = attributes  # Overwrite duplicates by default

    return combined_mappings


# Function to extract variables from the <inputs> and <outputs> tags under <data-access-state>
def extract_variables_from_data_access_state(inputs, outputs):
    tree = ET.parse(inputs)
    root = tree.getroot()
    mappings = OrderedDict()
    # Find all data-access-state elements
    for data_access_state in root.findall(".//data-access-state"):
        # Process inputs
        inputs = data_access_state.find(".//inputs")
        if inputs is not None:
            for input_var in inputs.findall("input-variable"):
                key = input_var.get("name") or "undefined"
                # Add to mappings, overwriting if key already exists (removes duplicates)
                mappings[key] = {'value': "empty", 'type': "String"}

        # # Process outputs
        # outputs = data_access_state.find(".//outputs")
        # if outputs is not None:
        #     for output_var in outputs.findall("output-variable"):
        #         key = output_var.get("name") or "undefined"
        #         mappings[key] = {'value': "empty", 'type': "String"}

    return mappings


def determine_type(value):
        # Check if the value is a boolean (true/false), ignoring quotes
    if value.strip("'").lower() == 'true':
        return 'boolean'
    elif value.strip("'").lower() == 'false':
        return 'boolean'
    
    # Check if the value is enclosed in single quotes (string type)
    if value.startswith("'") and value.endswith("'"):
        return 'string'
    
    # Try to determine if the value is an integer
    try:
        int(value)
        return 'integer'
    except ValueError:
        pass
    
    # Try to determine if the value is a float
    try:
        float(value)
        return 'float'
    except ValueError:
        pass
    
    # If none of the above, return unknown
    return 'string'


# Function to extract conditions from the XML and save to a new XML file
def extract_conditions(input_file, output_file, combined_mappings):
    tree = ET.parse(input_file)
    root = tree.getroot()
    output_root = ET.Element("conditions")
    output_root = ET.Element("session-mappings")

    # Add static session-mapping entries
    # changes hardcoded value of username and password
    static_mappings = [
        {"key": "label", "value": "abc", "type": "String"},
        {"key": "TRUE", "value": "true", "type": "Boolean"},
        {"key": "event", "value": "abc", "type": "String"},
        {"key": "project_name", "value": "project_name", "type": "String"},
        {"key": "language", "value": "en-US", "type": "String"},
        {"key": "username", "value": "admin", "type": "String"},
        {"key": "password", "value": "admin123", "type": "String"}
    ]
    
    # Add the static mappings to the XML structure
    for static_mapping in static_mappings:
        session_mapping = ET.SubElement(output_root, "session-mapping")
        session_mapping.set("key", static_mapping['key'])
        session_mapping.set("value", static_mapping['value'])
        session_mapping.set("type", static_mapping['type'])

    # Add existing mappings from combined_mappings into the new XML file
    for key, attributes in combined_mappings.items():
        session_mapping = ET.SubElement(output_root, "session-mapping")
        session_mapping.set("key", key)
        session_mapping.set("value", attributes['value'])
        session_mapping.set("type", attributes['type'])

    # Dictionary to hold extracted variables and their values
    variables_dict = {}

    def process_condition(element, parent_element):
        # Extract the text inside the condition tag
        #cond_text = element.text or ""
        cond_text = element.get("cond")
        # Match variable-value pairs using regex
        if cond_text:
            matches = re.findall(r"((?:\w+\.)?\w+)\s*[!=><]+\s*('.*?'|\S+)(?=\s*[\)&\|]|\s*$)", cond_text)
            for variable, value in matches:
                #variable, value = match
                # Add the variable to the dictionary if it's not already present
                if variable not in variables_dict:
                    if variable.startswith("'") and variable.endswith("'"):
                       value1 = variable
                       variable = value
                       value = value1
                       #print(f'Opp condition : {variable, value}')
                    
                    variables_dict[variable] = value
                    # Determine the type of the value
                    value_type = determine_type(value)
                            # If the type is string, keep the quotes, otherwise remove them
                    #if value_type == 'string':
                    xml_value = value.strip("'")  # Remove quotes for XML
                    xml_value2 = xml_value.strip(")")
                    variable_value = variable.strip(")")
                    # else:
                    #     xml_value = value.strip("'")  # Remove quotes for booleans and numbers too
                    #     xml_value2 = xml_value.strip(")")

                    #print(f'{variable} : {value} :{value_type}')
                    # Create a new XML element with the specified format
                    session_mapping = ET.SubElement(parent_element, "session-mapping")
                    session_mapping.set("key", variable_value)
                    session_mapping.set("value", xml_value2)
                    session_mapping.set("type", value_type)

    # Traverse the XML to find 'if', 'elseif', and 'else' tags
    for element in root.iter():
        if element.tag in ["if", "elseif", "else", "prompt"]:
            process_condition(element, output_root)

    # Convert the ElementTree to a string
    rough_string = ET.tostring(output_root, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    pretty_xml_as_string = reparsed.toprettyxml(indent="  ")

    # Write the prettified XML to the output file
    with open(output_file, 'w') as f:
        f.write(pretty_xml_as_string)


def process_file(input_file, output_file):
    
    # Step 2: Extract variables from <data-access-state> tags
    combined_mappings = extract_variables_from_data_access_state(input_file, output_file)
    
    # Step 3: Write extracted conditions to output file, including the variables from data-access-state
    extract_conditions(input_file, output_file, combined_mappings)

# ONE GO RUN
input_file = 'input/dialogs_file/'  
variable_file = 'input/variable/' 

delete_existing_files(variable_file)
files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(input_file)]
for file in files:
 process_file(input_file+file+'.xml', variable_file+file+'_variable.xml')

# Add this at the end of the script
if __name__ == "__main__":
    input_file = 'input/dialogs_file/'  
    variable_file = 'input/variable/' 
    merged_variable_file = 'input/variable/merged_variables.xml'

    delete_existing_files(variable_file)
    files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(input_file)]
    for file in files:
        process_file(input_file+file+'.xml', variable_file+file+'_variable.xml')

    # Merge all variable files
    merge_variable_files(variable_file, merged_variable_file)
    print(f"Merged variable file created: {merged_variable_file}")
