<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Initialization_Dialog">
    <custom-state id="toTestPage_CS">
      <session-mapping key="subAni" expr="result.ani"/>
      <session-mapping key="isTesting" value="true" type="Boolean"/>
      <action next="StartApplication_CS"/>
    </custom-state>

    <custom-state id="StartApplication_CS">
      <session-mapping key="mdnChangeVars" expr="initializeMonitoringObject(appsessionID, ani)"/>
      <session-mapping key="paymentVars" expr="initializeMonitoringObject(appsessionID, ani)"/>
      <session-mapping key="esnChangeVars" expr="initializeMonitoringObject(appsessionID, ani)"/>
      <session-mapping key="ctiAttachVars" expr="initializeMonitoringObject(appsessionID, ani)"/>
      <session-mapping key="ctiTransferVars" expr="initializeMonitoringObject(appsessionID, ani)"/>
      <session-mapping key="newPlanVars" expr="initializeMonitoringObject(appsessionID, ani)"/>
      <session-mapping key="addFeatureVars" expr="initializeMonitoringObject(appsessionID, ani)"/>
      <session-mapping key="activationVars" expr="initializeMonitoringObject(appsessionID, ani)"/>
      <session-mapping key="GlobalVars.trn" expr="ani"/>
      <session-mapping key="GlobalVars.providerId" expr="'providerId'"/>
      <session-mapping key="GlobalVars.sessionId" expr="appsessionID"/>
      <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.unCoopMaxRequest" expr="4"/>
      <session-mapping key="GlobalVars.coopMaxRequest" expr="4"/>
      <session-mapping key="GlobalVars.customerRequestedOperatorInCustomerScreen" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.FirstTimeDH1060" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.FirstTimeRP1040" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.FirstTimeRP1415" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.FirstTimeRP0520" value="true" type="Boolean"/>
      <session-mapping key="ActivationTable.ANI" expr="ani"/>
      <session-mapping key="ActivationTable.DID" expr="dnis"/>
      <session-mapping key="ActivationTable.SESSION_ID" expr="appsessionID"/>
      <session-mapping key="ActivationTable.ACTIVATION_MODE" expr="'0'"/>
      <session-mapping key="ActivationTable.ACCOUNT_NUM" expr="''"/>
      <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="''"/>
      <session-mapping key="ActivationTable.ACTIVATION_TYPE" expr="''"/>
      <session-mapping key="ActivationTable.ACTIVATION_STARTED" expr="''"/>
      <session-mapping key="ActivationTable.ESN" expr="''"/>
      <session-mapping key="ActivationTable.MDN" expr="''"/>
      <session-mapping key="ActivationTable.RATE_PLAN" expr="''"/>
      <session-mapping key="ActivationTable.TRF_SWITCH_ON" expr="'F'"/>
      <session-mapping key="ActivationTable.CITY" expr="''"/>
      <session-mapping key="ActivationTable.STATE" expr="''"/>
      <session-mapping key="ActivationTable.ZIP" expr="''"/>
      <session-mapping key="ActivationTable.BILLABLE_DATE" expr="''"/>
      <session-mapping key="ActivationTable.OLD_PHONE_TYPE" expr="'0'"/>
      <session-mapping key="ActivationTable.ERROR_TEXT" expr="''"/>
      <session-mapping key="PaymentTable.ANI" expr="ani"/>
      <session-mapping key="PaymentTable.DID" expr="dnis"/>
      <session-mapping key="PaymentTable.SESSION_ID" expr="appsessionID"/>
      <session-mapping key="PaymentTable.ACTIVATION_MODE" expr="'0'"/>
      <session-mapping key="PaymentTable.ACCOUNT_NUM" expr="''"/>
      <session-mapping key="PaymentTable.ACTIVATION_STATUS" expr="''"/>
      <session-mapping key="PaymentTable.ACTIVATION_TYPE" expr="''"/>
      <session-mapping key="PaymentTable.ACTIVATION_STARTED" expr="''"/>
      <session-mapping key="PaymentTable.ESN" expr="''"/>
      <session-mapping key="PaymentTable.MDN" expr="''"/>
      <session-mapping key="PaymentTable.RATE_PLAN" expr="''"/>
      <session-mapping key="PaymentTable.TRF_SWITCH_ON" expr="'F'"/>
      <session-mapping key="PaymentTable.CITY" expr="''"/>
      <session-mapping key="PaymentTable.STATE" expr="''"/>
      <session-mapping key="PaymentTable.ZIP" expr="''"/>
      <session-mapping key="PaymentTable.BILLABLE_DATE" expr="''"/>
      <session-mapping key="PaymentTable.OLD_PHONE_TYPE" expr="'0'"/>
      <session-mapping key="PaymentTable.ERROR_TEXT" expr="''"/>
      <session-mapping key="PaymentTable.CARD_TYPE" expr="''"/>
      <session-mapping key="PaymentTable.ACTIVATION_ENDED_GMT" expr="''"/>
      <session-mapping key="PaymentTable.TRANSACTION_ID" expr="''"/>
      <session-mapping key="PaymentTable.TRANSACTION_AMOUNT" expr="''"/>
      <session-mapping key="GlobalVars.XR1020_visit_count" expr="0"/>
      <session-mapping key="GlobalVars.operatorPaymentsReqCount" expr="0"/>
      <session-mapping key="GlobalVars.VS1135initialEntry" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.mp2025_reentry" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.acceptPayByPhone" expr="undefined"/>
      <session-mapping key="GlobalVars.otherGhostCaller" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.metroCardFail" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.acceptBCR" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.playFailureGoodbye" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.maxNoInput" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.eventCapture" expr="''"/>
      <session-mapping key="GlobalVars.clearCache" value="false" type="Boolean"/>
      <gotodialog next="Init_Main_Dialog"/>
    </custom-state>

  </dialog>
  