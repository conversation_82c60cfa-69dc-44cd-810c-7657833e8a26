<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="LookupNPANXX_Main_Dialog">
    <data-access-state id="LN1000_LookupNPANXX_DB_DA">
      <session-mapping key="zipCode" value="GlobalVars.zipCode" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <data-access id="GetNPANXX" classname="com.nuance.metro.dataaccess.GetNPANXX">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="zipCode" mask="true"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="availableNpaList"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.GetNPANXX" expr="GetNPANXX"/>
        <action next="getReturnLink()"/>
      </action>
    </data-access-state>

  </dialog>
  