<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="SelectProtectDialog_Dialog">
    <decision-state id="SE1000_SetVariables_DS">
      <session-mapping key="GlobalVars.simSwapRestricted" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.portOutRestricted " value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.unblockingNotAllowed " value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.providedOTPforSP " value="false" type="Boolean"/>
      <if cond="GlobalVars.tag == 'cancel-service_transfer'">
        <action next="SE1001_CheckAuth_JDA"/>
        <else>
          <action next="SE1002_SelectProtectStart_PP"/>
        </else>
      </if>
    </decision-state>

    <decision-state id="SE1001_CheckAuth_DS">
      <if cond="GlobalVars.loggedIn == true || GlobalVars.loggedIn == 'true'">
        <action next="SE2115_GetAccountAccessRestrictionsList_DB_DA"/>
        <else>
          <action next="SE1005_CallLogin_SD"/>
        </else>
      </if>
    </decision-state>

    <play-state id="SE1002_SelectProtectStart_PP">
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <if cond="tag == 'unlock-sim'">
        <audio>
          <prompt id="SE1002_out_02">
            <prompt-segments>
              <audiofile text="Let me check  your account settings for sim changes to see if they are restricted  This look up will also check the phone number transfer setting" src="SE1002_out_02.wav"/>
            </prompt-segments>
          </prompt>
        </audio>
        <else>
          <audio>
            <prompt id="SE1002_out_01">
              <prompt-segments>
                <audiofile text="The select and protect program allows you to restrict your phone from sim changes *and* help prevent your mobile number from being transferred to a different carrier Let's check your current account settings" src="SE1002_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </else>
      </if>
      <action next="SE1001_CheckAuth_JDA"/>
    </play-state>

    <subdialog-state id="SE1005_CallLogin_SD">
      <gotodialog next="Login_Security_Dialog"/>
      <action next="SE1005_CallLogin_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SE1005_CallLogin_SD_return_CS">
      <action next="SE2115_GetAccountAccessRestrictionsList_DB_DA"/>
    </custom-state>

    <data-access-state id="SE2115_GetAccountAccessRestrictionsList_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.trn" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <data-access id="GetAccountAccessRestrictionsList" classname="com.nuance.metro.dataaccess.GetAccountAccessRestrictionsList">
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="accountRestrictionsList"/>
          <output-variable name="simSwapRestricted"/>
          <output-variable name="unblockingNotAllowed"/>
          <output-variable name="portOutRestricted"/>
          <output-variable name="accountRestrictionsList[n].expirationDate" mask="true"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetAccountAccessRestrictionsList.status &amp;&amp; GetAccountAccessRestrictionsList.status.toUpperCase() == 'SUCCESS'">
          <session-mapping key="GlobalVars.GetAccountAccessRestrictionsList" expr="GetAccountAccessRestrictionsList"/>
          <session-mapping key="GlobalVars.simSwapRestricted" expr="GetAccountAccessRestrictionsList.simSwapRestricted"/>
          <session-mapping key="GlobalVars.unblockingNotAllowed" expr="GetAccountAccessRestrictionsList.unblockingNotAllowed"/>
          <session-mapping key="GlobalVars.portOutRestricted" expr="GetAccountAccessRestrictionsList.portOutRestricted"/>
          <session-mapping key="GlobalVars.gotoCallingDialog" expr="GetAccountAccessRestrictionsList.gotoCallingDialog"/>
          <session-mapping key="GlobalVars.gotoSE2120" expr="GetAccountAccessRestrictionsList.gotoSE2120"/>
          <if cond="GlobalVars.restrictionsUpdateSuccessfull == true &amp;&amp; GlobalVars.fromESNSwapDialog == true">
            <action next="getReturnLink()"/>
            <elseif cond="tag == 'cancel-service_transfer' &amp;&amp; GlobalVars.simSwapRestricted == true">
              <action next="SE2175_CallTransfer_SD"/>
            </elseif>
            <elseif cond="(GlobalVars.simSwapRestricted == true)">
              <if cond="GlobalVars.tag == 'cancel-service_transfer'">
                <action next="SE2156_PlayUnblockInfo_DM"/>
                <elseif cond="(GlobalVars.unblockingNotAllowed == true) &amp;&amp; (GlobalVars.fromESNSwapDialog == true)">
                  <action next="SE2170_ChangeNeedsAssistance_PP"/>
                </elseif>
                <else>
                  <action next="SE2116_CheckIfChangesMade_JDA"/>
                </else>
              </if>
            </elseif>
            <elseif cond="GlobalVars.portOutRestricted == true">
              <action next="SE2116_CheckIfChangesMade_JDA"/>
            </elseif>
            <else>
              <action next="SE2116_CheckIfChangesMade_JDA"/>
            </else>
          </if>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="SE2116_CheckIfChangesMade_DS">
      <if cond="GlobalVars.hitRestrictActivitiesSuccess == true">
        <action next="SE2125_PlayUpdatedSettings_PP"/>
        <else>
          <action next="SE2120_PlayCurrentSettings_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="SE2125_PlayUpdatedSettings_PP">
      <session-mapping key="portOutRestricted" value="GlobalVars.portOutRestricted" type="String"/>
      <session-mapping key="simSwapRestricted" value="GlobalVars.simSwapRestricted" type="String"/>
      <session-mapping key="isOnFamilyPlan" value="GlobalVars.GetAccountDetails.isOnFamilyPlan" type="String"/>
      <session-mapping key="twoFactorAuthOutcome" value="GlobalVars.twoFactorAuthOutcome" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.SE2125_PlayUpdatedSettings_PP"/>
          <param name="portOutRestricted" value="portOutRestricted" scope="request"/>
          <param name="simSwapRestricted" value="simSwapRestricted" scope="request"/>
          <param name="isOnFamilyPlan" value="isOnFamilyPlan" scope="request"/>
          <param name="twoFactorAuthOutcome" value="twoFactorAuthOutcome" scope="request"/>
          <param name="tag" value="tag" scope="request"/>
        </prompt>
      </audio>
      <if cond="GlobalVars.changedBothSP == true">
        <action next="getReturnLink()"/>
        <else>
          <action next="SE2130_AskMakeChanges_DM"/>
        </else>
      </if>
    </play-state>

    <play-state id="SE2120_PlayCurrentSettings_PP">
      <session-mapping key="portOutRestricted" value="GlobalVars.portOutRestricted" type="String"/>
      <session-mapping key="simSwapRestricted" value="GlobalVars.simSwapRestricted" type="String"/>
      <session-mapping key="visited_SE2120" value="GlobalVars.visited_SE2120 != undefined ? GlobalVars.visited_SE2120 : false" type="String"/>
      <session-mapping key="repeat_at_SE2130" value="GlobalVars.repeat_at_SE2130 != undefined ? GlobalVars.repeat_at_SE2130 : false" type="String"/>
      <session-mapping key="isOnFamilyPlan" value="GlobalVars.GetAccountDetails.isOnFamilyPlan" type="String"/>
      <session-mapping key="twoFactorAuthOutcome" value="GlobalVars.twoFactorAuthOutcome" type="String"/>
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.SP2120_PlayCurrentSettings_PP"/>
          <param name="portOutRestricted" value="portOutRestricted" scope="request"/>
          <param name="simSwapRestricted" value="simSwapRestricted" scope="request"/>
          <param name="visited_SE2120" value="visited_SE2120" scope="request"/>
          <param name="repeat_at_SE2130" value="repeat_at_SE2130" scope="request"/>
          <param name="isOnFamilyPlan" value="isOnFamilyPlan" scope="request"/>
          <param name="twoFactorAuthOutcome" value="twoFactorAuthOutcome" scope="request"/>
          <param name="tag" value="tag" scope="request"/>
        </prompt>
      </audio>
      <if cond="GlobalVars.visited_SE2120 != true &amp;&amp; GlobalVars.tag == 'cancel-service_transfer' &amp;&amp; GlobalVars.portOutRestricted == true">
        <session-mapping key="GlobalVars.activityCode" expr="'BPO'"/>
        <action next="SE2156_PlayUnblockInfo_DM"/>
        <elseif cond="GlobalVars.visited_SE2120 != true &amp;&amp; simSwapRestricted == true &amp;&amp; portOutRestricted == true">
          <action next="SE2156_PlayUnblockInfo_DM"/>
        </elseif>
      </if>
      <session-mapping key="GlobalVars.visited_SE2120" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.repeat_at_SE2130" value="false" type="Boolean"/>
      <if cond="isOnFamilyPlan == true">
        <action next="SE2130_AskMakeChanges_DM"/>
        <else>
          <action next="SE2130_AskMakeChanges_DM"/>
        </else>
      </if>
    </play-state>

    <dm-state id="SE2130_AskMakeChanges_DM" type="CUST">
      <session-mapping key="simSwapRestricted" value="GlobalVars.simSwapRestricted != undefined ? GlobalVars.simSwapRestricted : false" type="String"/>
      <session-mapping key="portOutRestricted" value="GlobalVars.portOutRestricted != undefined ? GlobalVars.portOutRestricted : false" type="String"/>
      <success>
        <session-mapping key="GlobalVars.visited_SE2130" value="true" type="Boolean"/>
        <action label="yes">
          <if cond="simSwapRestricted == false &amp;&amp; portOutRestricted == false">
            <action next="SE2140_WhatToChangeBothAllowed_DM"/>
            <elseif cond="simSwapRestricted == true &amp;&amp; portOutRestricted == true">
              <action next="SE2156_PlayUnblockInfo_DM"/>
            </elseif>
            <elseif cond="simSwapRestricted == false &amp;&amp; portOutRestricted == true">
              <action next="SE2150_WhatToChangeMix2_DM"/>
            </elseif>
            <else>
              <action next="SE2145_WhatToChangeMix1_DM"/>
            </else>
          </if>
        </action>
        <action label="no">
          <audio>
            <prompt id="SE2130_out_01">
              <prompt-segments>
                <audiofile text="Alright" src="SE2130_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="repeat">
          <session-mapping key="GlobalVars.repeat_at_SE2130" value="true" type="Boolean"/>
          <action next="SE2120_PlayCurrentSettings_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SE2130_ini_01" cond="visited_SE2130 == false">
                <prompt-segments>
                  <audiofile text="Would you like to make changes here?" src="SE2130_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2130_ini_02" cond="visited_SE2130 == true">
                <prompt-segments>
                  <audiofile text="These settings apply to all lines on your account" src="SE2130_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SE2130_ni1_01">
                <prompt-segments>
                  <audiofile text="To hear your account restrictions again say 'repeat that'  Otherwise, would you like to make changes?" src="SE2130_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2130_nm2_01">
                <prompt-segments>
                  <audiofile text="To make changes to your current account restrictions say 'yes' or press 1  or say 'no' or press 2  To hear your current restriction again say 'repeat that' or press 3" src="SE2130_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2130_nm2_01">
                <prompt-segments>
                  <audiofile text="To make changes to your current account restrictions say 'yes' or press 1  or say 'no' or press 2  To hear your current restriction again say 'repeat that' or press 3" src="SE2130_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2130_nm1_01">
                <prompt-segments>
                  <audiofile text="These settings apply to all lines on your account" src="SE2130_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2130_nm2_01">
                <prompt-segments>
                  <audiofile text="To make changes to your current account restrictions say 'yes' or press 1  or say 'no' or press 2  To hear your current restriction again say 'repeat that' or press 3" src="SE2130_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2130_nm2_01">
                <prompt-segments>
                  <audiofile text="To make changes to your current account restrictions say 'yes' or press 1  or say 'no' or press 2  To hear your current restriction again say 'repeat that' or press 3" src="SE2130_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SE2130_ini_01" cond="visited_SE2130 == false">
                <prompt-segments>
                  <audiofile text="Would you like to make changes here?" src="SE2130_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2130_ini_02" cond="visited_SE2130 == true">
                <prompt-segments>
                  <audiofile text="These settings apply to all lines on your account" src="SE2130_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="SE2130_AskMakeChanges_DM.grxml" count="1"/>
          <dtmfgrammars filename="SE2130_AskMakeChanges_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="SE2140_WhatToChangeBothAllowed_DM" type="CUST">
      <success>
        <action label="sim_changes">
          <session-mapping key="GlobalVars.activityCode" expr="'BSS'"/>
          <session-mapping key="GlobalVars.blockingInd" expr="'Y'"/>
          <action next="SE2155_PlayUpdateTransition_PP"/>
        </action>
        <action label="port_outs">
          <session-mapping key="GlobalVars.activityCode" expr="'BPO'"/>
          <session-mapping key="GlobalVars.blockingInd" expr="'Y'"/>
          <action next="SE2155_PlayUpdateTransition_PP"/>
        </action>
        <action label="both_of_them">
          <session-mapping key="GlobalVars.activityCode" expr="'BSS'"/>
          <session-mapping key="GlobalVars.blockingInd" expr="'Y'"/>
          <session-mapping key="GlobalVars.changedBothSP" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.changedBoth" value="true" type="Boolean"/>
          <action next="SE2142_SetVariablesForBoth_JDA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SE2140_ini_01">
                <prompt-segments>
                  <audiofile text="Which action would you like to restrict? say 'mobile number transfers, 'sim changes' or 'both of them'" src="SE2140_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="SE2140_WhatToChangeBothAllowed_DM_initial"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SE2140_ini_01">
                <prompt-segments>
                  <audiofile text="Which action would you like to restrict? say 'mobile number transfers, 'sim changes' or 'both of them'" src="SE2140_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SE2140_nm1_01">
                <prompt-segments>
                  <audiofile text="Which action would you like to retrict? say 'mobile number transfers', 'sim changes' or 'both of them'" src="SE2140_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2140_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'mobile number transfer' or press 1  'sim changes' or press 2'both of them' or press 3" src="SE2140_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2140_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'mobile number transfer' or press 1  'sim changes' or press 2'both of them' or press 3" src="SE2140_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2140_nm1_01">
                <prompt-segments>
                  <audiofile text="Which action would you like to retrict? say 'mobile number transfers', 'sim changes' or 'both of them'" src="SE2140_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2140_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'mobile number transfer' or press 1  'sim changes' or press 2'both of them' or press 3" src="SE2140_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2140_nm2_01">
                <prompt-segments>
                  <audiofile text="You can say 'mobile number transfer' or press 1  'sim changes' or press 2'both of them' or press 3" src="SE2140_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SE2140_ini_01">
                <prompt-segments>
                  <audiofile text="Which action would you like to restrict? say 'mobile number transfers, 'sim changes' or 'both of them'" src="SE2140_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SE2140_WhatToChangeBothAllowed_DM.grxml" count="1"/>
          <dtmfgrammars filename="SE2140_WhatToChangeBothAllowed_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="SE2142_SetVariablesForBoth_DS">
      <if cond="GlobalVars.unblockingNotAllowed == true">
        <action next="SE2170_ChangeNeedsAssistance_PP"/>
        <else>
          <action next="SE2155_PlayUpdateTransition_PP"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="SE2145_WhatToChangeMix1_DM" type="CUST">
      <session-mapping key="unblockingNotAllowed" value="GlobalVars.unblockingNotAllowed != undefined ? GlobalVars.unblockingNotAllowed : false" type="String"/>
      <success>
        <action label="allow_sim_changes">
          <session-mapping key="GlobalVars.tag" expr="'unlock-sim'"/>
          <session-mapping key="GlobalVars.activityCode" expr="'BSS'"/>
          <session-mapping key="GlobalVars.blockingInd" expr="'N'"/>
          <action next="SE2156_PlayUnblockInfo_DM"/>
        </action>
        <action label="block_port_outs">
          <session-mapping key="GlobalVars.activityCode" expr="'BPO'"/>
          <session-mapping key="GlobalVars.blockingInd" expr="'Y'"/>
          <action next="SE2155_PlayUpdateTransition_PP"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SE2145_ini_01">
                <prompt-segments>
                  <audiofile text="Your choices are 'allow sim changes' or 'restrict mobile number transfers' Which would you like?" src="SE2145_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SE2145_ini_01">
                <prompt-segments>
                  <audiofile text="Your choices are 'allow sim changes' or 'restrict mobile number transfers' Which would you like?" src="SE2145_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SE2145_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'allow sim changes' or 'restrict mobile number transfers'" src="SE2145_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2145_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'allow sim changes' or press 1 'restrict mobile number transfers' or press 2" src="SE2145_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2145_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'allow sim changes' or press 1 'restrict mobile number transfers' or press 2" src="SE2145_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2145_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'allow sim changes' or 'restrict mobile number transfers'" src="SE2145_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2145_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'allow sim changes' or press 1 'restrict mobile number transfers' or press 2" src="SE2145_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2145_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'allow sim changes' or press 1 'restrict mobile number transfers' or press 2" src="SE2145_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SE2145_ini_01">
                <prompt-segments>
                  <audiofile text="Your choices are 'allow sim changes' or 'restrict mobile number transfers' Which would you like?" src="SE2145_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SE2145_WhatToChangeMix1_DM.grxml" count="1"/>
          <dtmfgrammars filename="SE2145_WhatToChangeMix1_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="SE2150_WhatToChangeMix2_DM" type="CUST">
      <success>
        <action label="block_sim_changes">
          <session-mapping key="GlobalVars.activityCode" expr="'BSS'"/>
          <session-mapping key="GlobalVars.blockingInd" expr="'Y'"/>
          <action next="SE2155_PlayUpdateTransition_PP"/>
        </action>
        <action label="allow_port_outs">
          <session-mapping key="GlobalVars.activityCode" expr="'BPO'"/>
          <session-mapping key="GlobalVars.blockingInd" expr="'N'"/>
          <action next="SE2156_PlayUnblockInfo_DM"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="SE2150_ini_01">
                <prompt-segments>
                  <audiofile text="Your choices are 'restrict sim changes' or 'allow mobile number transfers' Which would you like?" src="SE2150_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="SE2150_ini_01">
                <prompt-segments>
                  <audiofile text="Your choices are 'restrict sim changes' or 'allow mobile number transfers' Which would you like?" src="SE2150_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SE2150_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'restrict sim changes' or 'allow mobile number transfers'" src="SE2150_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2150_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'restrict sim changes' or press 1 'allow mobile number transfers' or press 2" src="SE2150_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2150_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'restrict sim changes' or press 1 'allow mobile number transfers' or press 2" src="SE2150_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2150_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'restrict sim changes' or 'allow mobile number transfers'" src="SE2150_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2150_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'restrict sim changes' or press 1 'allow mobile number transfers' or press 2" src="SE2150_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2150_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'restrict sim changes' or press 1 'allow mobile number transfers' or press 2" src="SE2150_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="SE2150_ini_01">
                <prompt-segments>
                  <audiofile text="Your choices are 'restrict sim changes' or 'allow mobile number transfers' Which would you like?" src="SE2150_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="SE2150_WhatToChangeMix2_DM.grxml" count="1"/>
          <dtmfgrammars filename="SE2150_WhatToChangeMix2_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="SE2165_RestrictAccountActivities_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.trn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="activityCode" value="GlobalVars.activityCode" type="String"/>
      <session-mapping key="blockingInd" value="GlobalVars.blockingInd" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <data-access id="RestrictAccountActivities" classname="com.nuance.metro.dataaccess.RestrictAccountActivities">
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="sessionId"/>
          <input-variable name="activityCode"/>
          <input-variable name="blockingInd"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="RestrictAccountActivities.status &amp;&amp; RestrictAccountActivities.status.toUpperCase() == 'SUCCESS'">
          <session-mapping key="GlobalVars.hitRestrictActivitiesSuccess" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.RestrictAccountActivities" expr="RestrictAccountActivities"/>
          <if cond="GlobalVars.changedBothSP == true">
            <action next="SE2166_CheckChangeBoth_JDA"/>
            <else>
              <if cond="GlobalVars.fromESNSwapDialog == true">
                <session-mapping key="GlobalVars.restrictionsUpdateSuccessfull" value="true" type="Boolean"/>
              </if>
              <action next="SE2115_GetAccountAccessRestrictionsList_DB_DA"/>
            </else>
          </if>
          <else>
            <action next="SE2175_CallTransfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <play-state id="SE2155_PlayUpdateTransition_PP">
      <audio>
        <prompt id="SE2155_out_01">
          <prompt-segments>
            <audiofile text="Alright" src="SE2155_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="(GlobalVars.loggedIn == true)">
        <action next="SE2165_RestrictAccountActivities_DB_DA"/>
        <else>
          <action next="SE1005_CallLogin_SD"/>
        </else>
      </if>
    </play-state>

    <dm-state id="SE2156_PlayUnblockInfo_DM" type="CUST">
      <session-mapping key="tag" value="GlobalVars.tag" type="String"/>
      <session-mapping key="saidOperatorSE2156" value="GlobalVars.saidOperatorSE2156" type="String"/>
      <session-mapping key="saidRepeatSE2156" value="GlobalVars.saidRepeatSE2156" type="String"/>
      <session-mapping key="GlobalVars.operatorReqCount_SE2156" expr="GlobalVars.operatorReqCount_SE2156 != undefined ? GlobalVars.operatorReqCount_SE2156 : 0"/>
      <success>
        <action label="send_text">
          <audio>
            <prompt id="SE2156_out_01">
              <prompt-segments>
                <audiofile text="Ok, will do " src="SE2156_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.campaignId" expr="GlobalVars.GetBCSParameters.unblock_info_campaign_id"/>
          <action next="SE2180_SendUnblockingText_DB_DA"/>
        </action>
        <action label="repeat">
          <session-mapping key="GlobalVars.saidRepeatSE2156" value="true" type="Boolean"/>
          <action next="SE2156_PlayUnblockInfo_DM"/>
        </action>
        <action label="main-menu">
          <action next="getReturnLink()"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.operatorReqCount_SE2156" expr="GlobalVars.operatorReqCount_SE2156+1"/>
          <session-mapping key="GlobalVars.saidRepeatSE2156" value="false" type="Boolean"/>
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="visited_SE2156_operator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperatorSE2156" value="true" type="Boolean"/>
          <if cond="GlobalVars.operatorReqCount_SE2156 == 1">
            <audio>
              <prompt id="SE2156_Operator_01">
                <prompt-segments>
                  <audiofile text="To keep your account secure, only you can make adjustments to your account restrictions online  Customer care is unable to adjust your account security preferences   You can quickly change these settings on metry by t dash moblecom  Sign in and navigate to  My Account and select Profile From there select, Privacy and Notification and then choose 'account protection' to adjust your account security settings as needed   To hear that again say, Repeat that To receive a text with this information, say, 'send me a text'  To do something else, say 'main menu " src="SE2156_Operator_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="SE2156_PlayUnblockInfo_DM"/>
            <else>
              <action next="SE2175_CallTransfer_SD"/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="visited_SE2156_operator == false || saidRepeatSE2156 == true">
                <prompt id="SE2156_ini_01" cond="tag == 'cancel-service_transfer'">
                  <prompt-segments>
                    <audiofile text="To keep your account secure, your current settings prevent the transfer of your number to a different carrier  However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed" src="SE2156_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SE2156_ini_04" cond="tag == 'unlock-sim'">
                  <prompt-segments>
                    <audiofile text="To keep your account secure, your current settings prevent a SIM change  However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed" src="SE2156_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SE2156_ini_02" cond="!(tag == 'cancel-service_transfer' || tag == 'unlock-sim')">
                  <prompt-segments>
                    <audiofile text="To keep your account secure, your current settings prevent the transfer of your number to a different carrier and prevent a SIM chagne   However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed" src="SE2156_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SE2156_ini_03">
                  <prompt-segments>
                    <audiofile text="To hear that again say, Repeat that To receive a text with this information, say, 'send me a text'  To do something else, say 'main menu" src="SE2156_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="SE2156_PlayUnblockInfo_DM_initial"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="visited_SE2156_operator == false || saidRepeatSE2156 == true">
                <prompt id="SE2156_ini_01" cond="tag == 'cancel-service_transfer'">
                  <prompt-segments>
                    <audiofile text="To keep your account secure, your current settings prevent the transfer of your number to a different carrier  However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed" src="SE2156_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SE2156_ini_04" cond="tag == 'unlock-sim'">
                  <prompt-segments>
                    <audiofile text="To keep your account secure, your current settings prevent a SIM change  However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed" src="SE2156_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SE2156_ini_02" cond="!(tag == 'cancel-service_transfer' || tag == 'unlock-sim')">
                  <prompt-segments>
                    <audiofile text="To keep your account secure, your current settings prevent the transfer of your number to a different carrier and prevent a SIM chagne   However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed" src="SE2156_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SE2156_ini_03">
                  <prompt-segments>
                    <audiofile text="To hear that again say, Repeat that To receive a text with this information, say, 'send me a text'  To do something else, say 'main menu" src="SE2156_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="SE2156_nm1_01">
                <prompt-segments>
                  <audiofile text="Say Repeat that'  or If you like to receive a text with this information, say, 'send me a text'  You can also say main menu  If you're finished, feel free to hang up " src="SE2156_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2156_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say Repeat that or press 1  'send me a text' or press 2 Main menu or press 3   If you're all set, feel free to hang up " src="SE2156_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2156_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say Repeat that or press 1  'send me a text' or press 2 Main menu or press 3   If you're all set, feel free to hang up " src="SE2156_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2156_nm1_01">
                <prompt-segments>
                  <audiofile text="Say Repeat that'  or If you like to receive a text with this information, say, 'send me a text'  You can also say main menu  If you're finished, feel free to hang up " src="SE2156_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2156_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say Repeat that or press 1  'send me a text' or press 2 Main menu or press 3   If you're all set, feel free to hang up " src="SE2156_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="SE2156_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say Repeat that or press 1  'send me a text' or press 2 Main menu or press 3   If you're all set, feel free to hang up " src="SE2156_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="visited_SE2156_operator == false || saidRepeatSE2156 == true">
                <prompt id="SE2156_ini_01" cond="tag == 'cancel-service_transfer'">
                  <prompt-segments>
                    <audiofile text="To keep your account secure, your current settings prevent the transfer of your number to a different carrier  However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed" src="SE2156_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SE2156_ini_04" cond="tag == 'unlock-sim'">
                  <prompt-segments>
                    <audiofile text="To keep your account secure, your current settings prevent a SIM change  However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed" src="SE2156_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SE2156_ini_02" cond="!(tag == 'cancel-service_transfer' || tag == 'unlock-sim')">
                  <prompt-segments>
                    <audiofile text="To keep your account secure, your current settings prevent the transfer of your number to a different carrier and prevent a SIM chagne   However, you can quickly change these settings online  Sign into your account , go to My Account and select Profile From there select, Privacy and Notification where you can adjust your account security settings as needed" src="SE2156_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="SE2156_ini_03">
                  <prompt-segments>
                    <audiofile text="To hear that again say, Repeat that To receive a text with this information, say, 'send me a text'  To do something else, say 'main menu" src="SE2156_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
          <grammars filename="SE2156_PlayUnblockInfo_DM.grxml" count="1"/>
          <dtmfgrammars filename="SE2156_PlayUnblockInfo_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="14000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="14000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="Is that right?" src="gl_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_06">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_06.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="SE2170_ChangeNeedsAssistance_PP">
      <audio>
        <prompt id="SE2170_out_01">
          <prompt-segments>
            <audiofile text="Ok removing the sim swap restriction will require assistance" src="SE2170_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="SE2175_CallTransfer_SD"/>
    </play-state>

    <subdialog-state id="SE2175_CallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="SE2175_CallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="SE2175_CallTransfer_SD_return_CS">
      <gotodialog next="Exit_Dialog"/>
    </custom-state>

    <data-access-state id="SE2180_SendUnblockingText_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="campaignId" value="GlobalVars.campaignId" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <data-access id="SendTextMessage" classname="com.nuance.metro.dataaccess.SendTextMessage">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="mdn"/>
          <input-variable name="campaignId"/>
          <input-variable name="languageCode"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <action next="getReturnLink()"/>
      </action>
    </data-access-state>

    <decision-state id="SE2166_CheckChangeBoth_DS">
      <session-mapping key="GlobalVars.secondTimeSE2166" expr="GlobalVars.secondTimeSE2166 != undefined ? GlobalVars.secondTimeSE2166 : false"/>
      <if cond="GlobalVars.secondTimeSE2166 == false">
        <session-mapping key="GlobalVars.activityCode" expr="'BPO'"/>
        <session-mapping key="GlobalVars.secondTimeSE2166" value="true" type="Boolean"/>
        <action next="SE2165_RestrictAccountActivities_DB_DA"/>
        <else>
          <action next="SE2115_GetAccountAccessRestrictionsList_DB_DA"/>
        </else>
      </if>
    </decision-state>

  </dialog>
  