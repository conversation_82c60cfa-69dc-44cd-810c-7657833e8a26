<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="streetAddress" value="empty" type="String"/>
  <session-mapping key="addressType" value="empty" type="String"/>
  <session-mapping key="city" value="empty" type="String"/>
  <session-mapping key="stateCode" value="empty" type="String"/>
  <session-mapping key="zipCode" value="empty" type="String"/>
  <session-mapping key="securityPinCode" value="empty" type="String"/>
  <session-mapping key="isAddressApproved" value="empty" type="String"/>
  <session-mapping key="isOrderApproved" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="portInMdn" value="empty" type="String"/>
  <session-mapping key="portInproviderId" value="empty" type="String"/>
  <session-mapping key="offerCode" value="empty" type="String"/>
  <session-mapping key="pricePlanCode" value="empty" type="String"/>
  <session-mapping key="priceSheetCode" value="empty" type="String"/>
  <session-mapping key="sim" value="empty" type="String"/>
  <session-mapping key="iccid" value="empty" type="String"/>
  <session-mapping key="submarket" value="empty" type="String"/>
  <session-mapping key="areaCode" value="empty" type="String"/>
  <session-mapping key="monitoringFullString" value="empty" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="waiveConvFee" value="empty" type="String"/>
  <session-mapping key="paymentAmount" value="empty" type="String"/>
  <session-mapping key="targetMdn" value="empty" type="String"/>
  <session-mapping key="preferredPaymentMethod" value="empty" type="String"/>
  <session-mapping key="cardNumber" value="empty" type="String"/>
  <session-mapping key="expirationDate" value="empty" type="String"/>
  <session-mapping key="billingZip" value="empty" type="String"/>
  <session-mapping key="creditCardSecurityCode" value="empty" type="String"/>
  <session-mapping key="amountDue" value="empty" type="String"/>
  <session-mapping key="voucherNumber" value="empty" type="String"/>
  <session-mapping key="GlobalVars.activationResult" value="goodbye" type="string"/>
  <session-mapping key="activationEntryPoint" value="228" type="string"/>
  <session-mapping key="activationType" value="eSIM" type="string"/>
  <session-mapping key="GlobalVars.npi_flag" value="true" type="boolean"/>
  <session-mapping key="ActivateSubscriberOnNewAccount_AC1705.status" value="Success" type="string"/>
  <session-mapping key="AuthorizeByAnonymous_AC1707.paymentStatus" value="SUCCESS" type="string"/>
  <session-mapping key="GlobalVars.reChargeStatus" value="Y" type="string"/>
  <session-mapping key="numberPINPayments" value="0" type="integer"/>
  <session-mapping key="GlobalVars.activationStatus" value="Success" type="string"/>
  <session-mapping key="GlobalVars.paymentStatus" value="SUCCESS" type="string"/>
  <session-mapping key="GlobalVars.preferredPaymentMethod" value="credit" type="string"/>
  <session-mapping key="GlobalVars.isAuthorized" value="true))|" type="string"/>
  <session-mapping key="GlobalVars.payingWithPrepaid" value="true" type="string"/>
  <session-mapping key="GlobalVars.bankCardNumber" value="undefined" type="string"/>
  <session-mapping key="accountPinToggleOn" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.acceptedAPNSMS" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.acceptedAcctDetsSMS" value="true" type="boolean"/>
</session-mappings>
