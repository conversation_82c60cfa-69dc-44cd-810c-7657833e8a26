import yaml
import os
import shutil,logging, ruamel.yaml
from indentation import indent_yaml, indent_yaml_step5
from util import update_yaml_file, read_filenames_in_folder, fix_DA_input_format

logging.basicConfig(filename='agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')

first_hit = True

# Function to load YAML from a file
def load_yaml(file_path):
    try:
        with open(file_path, 'r') as file:
            return yaml.safe_load(file)
    except Exception as e:
        print(f"Failed to load YAML file {file_path}: {e}")
        raise

# Function to append additional component to the skeleton YAML
def append_component_to_yaml(skeleton, component):
    global first_hit
    if 'components' in skeleton:
        skeleton['components'].extend(component)
    else:
        skeleton['components'] = component
    return skeleton

def combine_yaml_files(skeleton_yaml_path, additional_component_yaml_path, output_yaml_path):
    try:
        # Load the skeleton and additional component YAML
        skeleton_yaml = load_yaml(skeleton_yaml_path)
        additional_component_yaml = load_yaml(additional_component_yaml_path)

        # Ensure additional component is a list
        if not isinstance(additional_component_yaml, list):
            additional_component_yaml = [additional_component_yaml]

        # Append the additional component to the skeleton
        updated_yaml = append_component_to_yaml(skeleton_yaml, additional_component_yaml)

        # Save the updated YAML to a new file
        with open(output_yaml_path, 'w') as file:
            yaml.dump(updated_yaml, file, default_flow_style=False, sort_keys=False)

    except Exception as e:
        print(f"Failed to combine YAML files {skeleton_yaml_path} and {additional_component_yaml_path}: {e}")
        raise

def handle_failed_file(file_name):
    input_folder = 'input/dialogs_file/'
    rerun_folder = 'input/dialogs_file/rerun/'
    xml_file_name = os.path.splitext(file_name.replace('_topic', ''))[0] + '.xml'
    xml_file_path = os.path.join(input_folder, xml_file_name)
    rerun_file_path = os.path.join(rerun_folder, xml_file_name)

    if os.path.exists(xml_file_path):
        print(f"Moving {xml_file_name} to rerun folder.")
        shutil.move(xml_file_path, rerun_file_path)
    else:
        print(f"Corresponding XML file {xml_file_name} not found in {input_folder}.")


# Function to process the YAML file and print replacements
def modify_redirection(file_path):
    yaml = ruamel.yaml.YAML()
    yaml.preserve_quotes = True

    with open(file_path, 'r') as file:
        data = yaml.load(file)

    # Traverse through the data to find 'kind: BeginDialog' or 'GotoAction' and update them
    def traverse_and_update(node):
        if isinstance(node, dict):
            # Replace BeginDialog with GotoAction if necessary
            if node.get('kind') == 'BeginDialog' and node.get('dialog'):
                dialog_value = node['dialog']
                if any(dialog_value.endswith(suffix) for suffix in ['_DM', '_DS', '_DA', '_DB', '_PP', '_CS', '_SD','_JDA']): 
                    action_id = dialog_value.split('.')[-1]
                    node['kind'] = 'GotoAction'
                    node['actionId'] = action_id
                    del node['dialog']
                    
            # Replace GotoAction with EndDialog if actionId is 'return'
            elif node.get('kind') == 'GotoAction' and node.get('actionId') == 'return':
                node['kind'] = 'EndDialog'
                del node['actionId']

            elif node.get('kind') == 'GotoAction' and node.get('actionId'):
                actionId_value = node['actionId']
                if any(actionId_value.endswith(suffix) for suffix in ['_Dialog','_dvxml']): 
                    dialog_value = 'topic.'+actionId_value
                    node['kind'] = 'BeginDialog'
                    node['dialog'] = dialog_value
                    del node['actionId']

            for key, value in node.items():
                traverse_and_update(value)

        elif isinstance(node, list):
            for item in node:
                traverse_and_update(item)

    traverse_and_update(data)
    # Save the updated data back to the YAML file
    with open(file_path, 'w') as file:
        yaml.dump(data, file)

# Function to process the YAML file and print replacements
def modify_redirection_for_custome_state(file_path):
    yaml = ruamel.yaml.YAML()
    yaml.preserve_quotes = True
    custom_output_dir = 'input/custom_file/'
    with open(file_path, 'r') as file:
        data = yaml.load(file)

    # Traverse through the data to find 'kind: BeginDialog' or 'GotoAction' and update them
    def traverse_and_update(node):
        if isinstance(node, dict):
            files = [os.path.splitext(f)[0] for f in read_filenames_in_folder(custom_output_dir)]
            for file in files:
                # Replace BeginDialog with GotoAction if necessary
                if node.get('kind') == 'GotoAction' and 'actionId' in node:
                    action_id_value = node['actionId']
                    if action_id_value == file: 
                        dialog_value = 'topic.'+action_id_value
                        node['kind'] = 'BeginDialog'
                        node['dialog'] = dialog_value
                        del node['actionId']
                    
            for key, value in node.items():
                traverse_and_update(value)

        elif isinstance(node, list):
            for item in node:
                traverse_and_update(item)

    traverse_and_update(data)
    try:
        with open(file_path, 'w') as file:
            yaml.dump(data, file)
    except Exception as e:
        print(f"Error writing file: {e}")


folder_path = 'output/topic_yaml/'

print("step5_generate_bot_yaml: Combining consolidated YAML files with the skeleton YAML...")

files = read_filenames_in_folder(folder_path)
for file in files:
    try:
        if first_hit:
            combine_yaml_files('YAML-template/botContentIVR.yml', folder_path+file, 'output/final_output.yml')
            first_hit = False
            print('first_hit')
        else:
            combine_yaml_files('output/final_output.yml', folder_path+file, 'output/final_output.yml')
    except Exception as e:
        print(f"Failed to process file {file}: {e}")
        handle_failed_file(file)


try:
    #   replacing _REPLACE_THIS for unique values
    updated_yaml_content = update_yaml_file('output/final_output.yml')
    with open('output/final_output.yml', 'w') as file:
        file.write(updated_yaml_content)
    modify_redirection('output/final_output.yml')
    modify_redirection_for_custome_state('output/final_output.yml')
    fix_DA_input_format('output/final_output.yml')
    indent_yaml_step5('output/final_output.yml', 'output/bot_yaml/final.yml')
    print("step5_generate_bot_yaml: Consolidated YAML files created.")
    logging.info("step5_generate_bot_yaml completed")

except Exception as e:
    print(f"Failed to finalize the YAML file: {e}")