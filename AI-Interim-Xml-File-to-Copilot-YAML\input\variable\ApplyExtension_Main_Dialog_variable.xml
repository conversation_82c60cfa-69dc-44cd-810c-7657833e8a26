<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="accountNumber" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="GlobalVars.accountFutureRequestInd" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.callType" value="dsgExtension" type="string"/>
  <session-mapping key="GlobalVars.tag" value="request-extension" type="string"/>
  <session-mapping key="GlobalVars.extensionAllowed" value="true" type="string"/>
  <session-mapping key="GlobalVars.extensionEntryPoint" value="failed_payment" type="string"/>
  <session-mapping key="tag" value="request-extension" type="string"/>
  <session-mapping key="extensionAllowed" value="true" type="boolean"/>
  <session-mapping key="numberOfCases" value="0" type="integer"/>
  <session-mapping key="AX1705_operator_counter" value="3" type="integer"/>
  <session-mapping key="extensionEntryPoint" value="payment_amount" type="string"/>
  <session-mapping key="care_nlu_enabled" value="true" type="boolean"/>
  <session-mapping key="language" value="en-US" type="string"/>
  <session-mapping key="CreateUnhotlineCase.status" value="success" type="string"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="suspended" type="string"/>
  <session-mapping key="CheckUnhotlineEligibility.isEligible" value="true" type="boolean"/>
</session-mappings>
