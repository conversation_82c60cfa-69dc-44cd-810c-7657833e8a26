<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="features" value="empty" type="String"/>
  <session-mapping key="multilineList" value="empty" type="String"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="boolean"/>
  <session-mapping key="GetAutoPayMessages.messageCode" value="9003" type="string"/>
  <session-mapping key="CalculatePaidFeatures.status" value="Success" type="string"/>
  <session-mapping key="autoEligPlan" value="true" type="boolean"/>
  <session-mapping key="isSupported" value="true" type="string"/>
  <session-mapping key="numPaidFeatures" value="10" type="integer"/>
  <session-mapping key="GlobalVars.accountFutureRequestInd" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.isOnFamilyPlan" value="true" type="boolean"/>
  <session-mapping key="TransformMultilineInfo.status" value="Success" type="string"/>
  <session-mapping key="TransformMultilineInfo.numberLines" value="5" type="integer"/>
  <session-mapping key="GlobalVars.isCareTransfer" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.arBalance" value="undefined" type="string"/>
  <session-mapping key="messageCode" value="9002" type="string"/>
  <session-mapping key="isSuspended" value="true" type="boolean"/>
  <session-mapping key="remainingDays" value="0" type="string"/>
  <session-mapping key="isAutopayEnrolled" value="true" type="boolean"/>
  <session-mapping key="isAutopayEligPlanExists" value="true" type="boolean"/>
  <session-mapping key="hasValidMessageCode" value="true" type="string"/>
  <session-mapping key="linesPlayedCounter" value="(numberLines-1" type="string"/>
  <session-mapping key="linePaidFeatures" value="false" type="boolean"/>
  <session-mapping key="tag" value="payment-help" type="string"/>
  <session-mapping key="isOnFamilyPlan" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.tag" value="request-extension" type="string"/>
</session-mappings>
