<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="cheaperOnly" value="empty" type="String"/>
  <session-mapping key="JWTToken" value="empty" type="String"/>
  <session-mapping key="futureChangeOffer" value="empty" type="String"/>
  <session-mapping key="restrictionType" value="empty" type="String"/>
  <session-mapping key="ratePlanPrice" value="empty" type="String"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="suspended" type="string"/>
  <session-mapping key="supportedPlanIndicator" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.callType" value="change_plan" type="string"/>
  <session-mapping key="GlobalVars.accountFutureRequestInd" value="true" type="string"/>
  <session-mapping key="GetAccountDetails.isOnFamilyPlan" value="true" type="string"/>
  <session-mapping key="GlobalVars.heardCurrentPlan" value="true" type="string"/>
  <session-mapping key="GlobalVars.subscriberFuturePricePlanInd" value="true" type="string"/>
  <session-mapping key="GetFutureTransactionDetails.status" value="Success" type="string"/>
  <session-mapping key="GlobalVars.futurePlan" value="undefined" type="string"/>
  <session-mapping key="futurePlanPromptURL" value="undefined" type="string"/>
  <session-mapping key="futurePlanPromptTTS" value="undefined" type="string"/>
  <session-mapping key="tag" value="home-internet" type="string"/>
  <session-mapping key="callType" value="home_internet" type="string"/>
  <session-mapping key="ratePlanAction" value="tell_me_more" type="string"/>
  <session-mapping key="isPlanDetails" value="true" type="boolean"/>
  <session-mapping key="isOnFamilyPlan" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.tag" value="home-internet_active" type="string"/>
  <session-mapping key="GlobalVars.ratePlanAction" value="undefined" type="string"/>
  <session-mapping key="GetAvailableRatePlanOffers.status" value="Success" type="string"/>
  <session-mapping key="CalculatePlanOptions_MW.status" value="Success" type="string"/>
  <session-mapping key="GlobalVars.restrictionType" value="NoChangeAllowed" type="string"/>
  <session-mapping key="immediate" value="lastresult" type="string"/>
  <session-mapping key="futuredate" value="lastresult" type="string"/>
  <session-mapping key="accountStatus" value="active" type="string"/>
  <session-mapping key="ratePlans.length" value="0" type="string"/>
  <session-mapping key="GlobalVars.planChangeTimeOption" value="immediate" type="string"/>
  <session-mapping key="planChangeTimeOption" value="future" type="string"/>
  <session-mapping key="isUpgrade" value="true" type="boolean"/>
  <session-mapping key="isNewPlanPriceLessThanCurrentPlanPrice" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.declineChangeCounter" value="2" type="string"/>
  <session-mapping key="interpretation.dm_root" value="change_plan" type="string"/>
</session-mappings>
