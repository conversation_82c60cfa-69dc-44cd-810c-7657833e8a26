<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="DeviceHandling_Routing_Dialog">
    <subdialog-state id="DH1070_LoginCollectPIN_SD">
      <gotodialog next="Login_Main_Dialog"/>
      <action next="DH1070_LoginCollectPIN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DH1070_LoginCollectPIN_SD_return_CS">
      <action next="DH1210_CheckCallType_JDA"/>
    </custom-state>

    <subdialog-state id="DH1071_LoginCollectMDN_SD">
      <session-mapping key="GlobalVars.shortSecurityCodePrompt" value="true" type="Boolean"/>
      <gotodialog next="Login_Main_Dialog"/>
      <action next="DH1071_LoginCollectMDN_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DH1071_LoginCollectMDN_SD_return_CS">
      <if cond="ActivationTable.ACTIVATION_TYPE != '5'">
        <session-mapping key="GlobalVars.billableTask" value="true" type="Boolean"/>
        <session-mapping key="ActivationTable.ACTIVATION_TYPE" expr="'5'"/>
        <session-mapping key="ActivationTable.ACTIVATION_STARTED" expr="getGMTTime()"/>
        <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'47'"/>
        <session-mapping key="ActivationTable.ERROR_TEXT" expr="'FAILURE OR HANG UP'"/>
        <session-mapping key="mdnChangeVars.eventTypeGMT" expr="getEventTime()"/>
        <session-mapping key="mdnChangeVars.status" expr="'incomplete'"/>
        <session-mapping key="mdnChangeVars.eventType" expr="'mdn_change'"/>
      </if>
      <session-mapping key="GlobalVars.securityRequired" value="true" type="Boolean"/>
      <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
      <session-mapping key="GlobalVars.paymentInfoRequired" value="true" type="Boolean"/>
      <gotodialog next="DeviceHandling_Main#DH1040_CheckPaymentInfoRequired_DS"/>
    </custom-state>

    <decision-state id="DH1075_CheckNLUMenuConfig_DS">
      <session-mapping key="care_nlu_enabled" value="GlobalVars.GetBCSParameters.care_nlu_enabled" type="String"/>
      <if cond="((care_nlu_enabled == true || care_nlu_enabled == 'true') &amp;&amp; (language == 'en-US'))">
        <action next="getReturnLink()"/>
        <else>
          <action next="DH1080_MainMenu_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="DH1080_MainMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
      <action next="DeviceHandling_TerminalSDReturn_CS"/>
    </subdialog-state>
    <subdialog-state id="DH1085_GoToLostPhone_SD">
      <gotodialog next="LostPhone_Main_Dialog"/>
      <action next="DH1085_GoToLostPhone_TerminalSDReturn_CS"/>
    </subdialog-state>
    <custom-state id="DH1085_GoToLostPhone_TerminalSDReturn_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="DH1210_CheckCallType_DS">
      <if cond="GlobalVars.callType == 'mdn_change'">
        <action next="DH1230_MDNChangeModule_SD"/>
        <elseif cond="GlobalVars.callType == 'lost_phone'">
          <action next="DH1220_LostPhone_SD"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'add_line'">
          <action next="DH1295_AddLineWrap_DM"/>
        </elseif>
        <else>
          <action next="DH1235_DeviceCarrierYN_DM"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="DH1220_LostPhone_SD">
      <gotodialog next="LostPhone_Main_Dialog"/>
      <action next="DeviceHandling_TerminalSDReturn_CS"/>
    </subdialog-state>
    <subdialog-state id="DH1230_MDNChangeModule_SD">
      <gotodialog next="MDNChange_Main_Dialog"/>
      <action next="DH1230_MDNChangeModule_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="DH1230_MDNChangeModule_SDReturn_CS">
      <session-mapping key="firstTimeVisitedDH1230" value="GlobalVars.firstTimeVisitedDH1230 != undefined ? GlobalVars.firstTimeVisitedDH1230 : false" type="String"/>
      <if cond="firstTimeVisitedDH1230 == false">
        <gotodialog next="DeviceHandling_Main#DH1065_ReadyToProceed_PP"/>
        <else>
          <session-mapping key="GlobalVars.billableTaskStarted" expr="undefined"/>
          <session-mapping key="GlobalVars.billableTaskSuccess" expr="undefined"/>
          <session-mapping key="GlobalVars.billableTask" expr="undefined"/>
          <action next="DH1270_PaymentOptions_SD"/>
        </else>
      </if>
    </custom-state>

    <dm-state id="DH1235_DeviceCarrierYN_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="isSwitchPhone" value="GlobalVars.callType == 'switch_phone' ? true : false" type="String"/>
      <session-mapping key="stateVar" value="" type="String"/>
      <success>
        <session-mapping key="enteringFrom" expr="''"/>
        <action label="yes">
          <action next="DH1238_BYODDevice_DM"/>
        </action>
        <action label="no">
          <if cond="callType == 'add_line'">
            <action next="DH1240_OtherDeviceHandling_PP"/>
            <else>
              <audio>
                <prompt id="DH1235_out_01">
                  <prompt-segments>
                    <audiofile text="Okay" src="DH1235_out_01.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <session-mapping key="esnChangeVars.eventTypeGMT" expr="getEventTime()"/>
              <session-mapping key="esnChangeVars.status" expr="'incomplete'"/>
              <session-mapping key="esnChangeVars.eventType" expr="'esn_change'"/>
              <session-mapping key="ActivationTable.ACTIVATION_TYPE" expr="'11'"/>
              <session-mapping key="ActivationTable.ACTIVATION_STARTED" expr="getGMTTime()"/>
              <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'130'"/>
              <action next="DH1301_CheckContext_JDA"/>
            </else>
          </if>
        </action>
        <action label="dont_know">
          <audio>
            <prompt id="DH1235_out_02">
              <prompt-segments>
                <audiofile text="Alright, then please check with your old wireless carrier, and ask them to unlock the phone for you " src="DH1235_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="DH1238_BYODDevice_DM"/>
        </action>
        <action label="operator"/>
        <action label="more_info">
          <audio>
            <prompt id="DH1235_more_info_01">
              <prompt-segments>
                <audiofile text="More information " src="DH1235_more_info_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="DH1235_more_info_02">
              <prompt-segments>
                <audiofile src="DH1235_more_info_02.wav" text="If you got your new phone from Metro , or directly from the phone manufacturer, you re fine"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="DH1235_more_info_03">
              <prompt-segments>
                <audiofile text="But if you got it from a different wireless carrier, that carrier will have it locked So it can only be active on their network If you didn't ask them to unlock it when you closed your account, it s probably still locked, and we can t switch to it right away " src="DH1235_more_info_03.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="DH1235_more_info_04">
              <prompt-segments>
                <audiofile text="So do you think your new phone is locked? Say yes, no or I m not sure " src="DH1235_more_info_04.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="enteringFrom" expr="'DH1235_DeviceCarrierYN_DM'"/>
          <action next="DH1235_DeviceCarrierYN_DM"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="enteringFrom != 'DH1235_DeviceCarrierYN_DM'">
                <prompt id="DH1235_ini_01" cond="callType == 'add_line'">
                  <prompt-segments>
                    <audiofile text="Is that phone from another wireless carrier?" src="DH1235_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="DH1235_ini_02" cond="callType != 'add_line'">
                  <prompt-segments>
                    <audiofile text="Is the phone you want to switch to locked by another wireless carrier? You can also say more information " src="DH1235_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="enteringFrom != 'DH1235_DeviceCarrierYN_DM'">
                <prompt id="DH1235_ini_01" cond="callType == 'add_line'">
                  <prompt-segments>
                    <audiofile text="Is that phone from another wireless carrier?" src="DH1235_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="DH1235_ini_02" cond="callType != 'add_line'">
                  <prompt-segments>
                    <audiofile text="Is the phone you want to switch to locked by another wireless carrier? You can also say more information " src="DH1235_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DH1235_ini_01" cond="callType == 'add_line'">
                <prompt-segments>
                  <audiofile text="Is that phone from another wireless carrier?" src="DH1235_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1235_ini_02" cond="callType != 'add_line'">
                <prompt-segments>
                  <audiofile text="Is the phone you want to switch to locked by another wireless carrier? You can also say more information " src="DH1235_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1235_nm2_01" cond="callType == 'add_line'">
                <prompt-segments>
                  <audiofile text="If the device you would like to use on the new line is from another wireless carrier, say yes or press 1 Otherwise, say no or press 2" src="DH1235_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1235_nm2_02" cond="callType != 'add_line'">
                <prompt-segments>
                  <audiofile text="If the device you would like to switch to might be locked by another wireless carrier, say yes or press 1 Otherwise, say no or press 2, or more information or press star " src="DH1235_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1235_nm2_01" cond="callType == 'add_line'">
                <prompt-segments>
                  <audiofile text="If the device you would like to use on the new line is from another wireless carrier, say yes or press 1 Otherwise, say no or press 2" src="DH1235_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1235_nm2_02" cond="callType != 'add_line'">
                <prompt-segments>
                  <audiofile text="If the device you would like to switch to might be locked by another wireless carrier, say yes or press 1 Otherwise, say no or press 2, or more information or press star " src="DH1235_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1235_ini_01" cond="callType == 'add_line'">
                <prompt-segments>
                  <audiofile text="Is that phone from another wireless carrier?" src="DH1235_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1235_ini_02" cond="callType != 'add_line'">
                <prompt-segments>
                  <audiofile text="Is the phone you want to switch to locked by another wireless carrier? You can also say more information " src="DH1235_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1235_nm2_01" cond="callType == 'add_line'">
                <prompt-segments>
                  <audiofile text="If the device you would like to use on the new line is from another wireless carrier, say yes or press 1 Otherwise, say no or press 2" src="DH1235_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1235_nm2_02" cond="callType != 'add_line'">
                <prompt-segments>
                  <audiofile text="If the device you would like to switch to might be locked by another wireless carrier, say yes or press 1 Otherwise, say no or press 2, or more information or press star " src="DH1235_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1235_nm2_01" cond="callType == 'add_line'">
                <prompt-segments>
                  <audiofile text="If the device you would like to use on the new line is from another wireless carrier, say yes or press 1 Otherwise, say no or press 2" src="DH1235_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1235_nm2_02" cond="callType != 'add_line'">
                <prompt-segments>
                  <audiofile text="If the device you would like to switch to might be locked by another wireless carrier, say yes or press 1 Otherwise, say no or press 2, or more information or press star " src="DH1235_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="enteringFrom != 'DH1235_DeviceCarrierYN_DM'">
                <prompt id="DH1235_ini_01" cond="callType == 'add_line'">
                  <prompt-segments>
                    <audiofile text="Is that phone from another wireless carrier?" src="DH1235_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="DH1235_ini_02" cond="callType != 'add_line'">
                  <prompt-segments>
                    <audiofile text="Is the phone you want to switch to locked by another wireless carrier? You can also say more information " src="DH1235_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="DH1235_DeviceCarrierYN_DM.jsp" count="1">
            <param name="stateVar" value="stateVarVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="DH1235_DeviceCarrierYN_DM_dtmf.jsp" count="1">
            <param name="stateVar" value="stateVarVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="dont_know == 'lastresult'">
                <prompt id="DH1235_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You re not sure if you phone is locked " src="DH1235_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="main_menu == 'lastresult'">
                <prompt id="DH1235_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You re not sure if you phone is locked " src="DH1235_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="main_menu == 'lastresult'">
                <prompt id="DH1235_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You re not sure if you phone is locked " src="DH1235_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="DH1238_BYODDevice_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <success>
        <action label="inquire-store_location">
          <session-mapping key="GlobalVars.callType" expr="'activate_byod'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="DH1285_GoToStoreLocator_SD"/>
        </action>
        <action label="something_else">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="DH1075_CheckNLUMenuConfig_JDA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DH1238_ini_02" cond="callType == 'switch_phone'">
                <prompt-segments>
                  <audiofile src="DH1238_ini_02.wav" text="To switch to a non-Metro device, you ll need to bring it to a Metro store or authorized dealer  The device must be unlocked and not active on another account  At the store, you ll need to buy a Metro SIM card  Also, you ll be asked to provide your phone number and account PIN"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1238_ini_03" cond="callType != 'switch_phone'">
                <prompt-segments>
                  <audiofile src="DH1238_ini_03.wav" text="To add a non-Metro device to your account, you ll need to bring it to a Metro store or authorized dealer  The device must be unlocked and not active on another account  At the store, you ll need to buy a Metro SIM card  Also, you ll be asked to provide your phone number and account PIN"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1238_ini_05">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that,' 'help me find a MetroPCS store,' or 'I need something else'  If you're finished, you can just hang up" src="DH1238_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DH1238_ini_02" cond="callType == 'switch_phone'">
                <prompt-segments>
                  <audiofile src="DH1238_ini_02.wav" text="To switch to a non-Metro device, you ll need to bring it to a Metro store or authorized dealer  The device must be unlocked and not active on another account  At the store, you ll need to buy a Metro SIM card  Also, you ll be asked to provide your phone number and account PIN"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1238_ini_03" cond="callType != 'switch_phone'">
                <prompt-segments>
                  <audiofile src="DH1238_ini_03.wav" text="To add a non-Metro device to your account, you ll need to bring it to a Metro store or authorized dealer  The device must be unlocked and not active on another account  At the store, you ll need to buy a Metro SIM card  Also, you ll be asked to provide your phone number and account PIN"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1238_ini_05">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that,' 'help me find a MetroPCS store,' or 'I need something else'  If you're finished, you can just hang up" src="DH1238_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DH1238_nm1_01">
                <prompt-segments>
                  <audiofile text="To hear those instructions again, say 'repeat that  You can also say 'find a store,' or 'I need something else'  If you're finished, you can just hang up" src="DH1238_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1238_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those instructions again, say 'repeat that,' or press star  You can also say 'find a store' or press 1; or 'I need something else,' or press 2  If you're finished, you can just hang up" src="DH1238_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1238_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those instructions again, say 'repeat that,' or press star  You can also say 'find a store' or press 1; or 'I need something else,' or press 2  If you're finished, you can just hang up" src="DH1238_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1238_nm1_01">
                <prompt-segments>
                  <audiofile text="To hear those instructions again, say 'repeat that  You can also say 'find a store,' or 'I need something else'  If you're finished, you can just hang up" src="DH1238_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1238_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those instructions again, say 'repeat that,' or press star  You can also say 'find a store' or press 1; or 'I need something else,' or press 2  If you're finished, you can just hang up" src="DH1238_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1238_nm2_01">
                <prompt-segments>
                  <audiofile text="To hear those instructions again, say 'repeat that,' or press star  You can also say 'find a store' or press 1; or 'I need something else,' or press 2  If you're finished, you can just hang up" src="DH1238_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DH1238_ini_02" cond="callType == 'switch_phone'">
                <prompt-segments>
                  <audiofile src="DH1238_ini_02.wav" text="To switch to a non-Metro device, you ll need to bring it to a Metro store or authorized dealer  The device must be unlocked and not active on another account  At the store, you ll need to buy a Metro SIM card  Also, you ll be asked to provide your phone number and account PIN"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1238_ini_03" cond="callType != 'switch_phone'">
                <prompt-segments>
                  <audiofile src="DH1238_ini_03.wav" text="To add a non-Metro device to your account, you ll need to bring it to a Metro store or authorized dealer  The device must be unlocked and not active on another account  At the store, you ll need to buy a Metro SIM card  Also, you ll be asked to provide your phone number and account PIN"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1238_ini_05">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that,' 'help me find a MetroPCS store,' or 'I need something else'  If you're finished, you can just hang up" src="DH1238_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="DH1238_BYODDevice_DM.grxml" count="1"/>
          <dtmfgrammars filename="DH1238_BYODDevice_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="DH_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="DH1240_OtherDeviceHandling_PP">
      <audio>
        <prompt id="DH1240_out_04">
          <prompt-segments>
            <audiofile src="DH1240_out_04.wav" text="I can get you to someone who can add a Metro device to your account You'll need to provide your phone number and account PIN And be aware that you can't add a device that's still active on another account If you're ready to add the device to your account now, just stay on the line Otherwise, give us call later"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
      <action next="DH1290_CallTransfer_SD"/>
    </play-state>

    <subdialog-state id="DH1270_PaymentOptions_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="DeviceHandling_TerminalSDReturn_CS"/>
    </subdialog-state>
    <subdialog-state id="DH1285_GoToStoreLocator_SD">
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="DH1285_GoToStoreLocator_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DH1285_GoToStoreLocator_SD_return_CS">
      <action next="DH1315_Goodbye_SD"/>
    </custom-state>

    <subdialog-state id="DH1290_CallTransfer_SD">
      <session-mapping key="GlobalVars.transferFrom" expr="'DeviceHandling_XR'"/>
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="DeviceHandling_TerminalSDReturn_CS"/>
    </subdialog-state>
    <dm-state id="DH1295_AddLineWrap_DM" type="CUST">
      <success>
        <action label="main_menu">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.tag" expr="undefined"/>
          <action next="DH1075_CheckNLUMenuConfig_JDA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DH1295_ini_01">
                <prompt-segments>
                  <audiofile text="If your account is suspended you will need to make a payment before you can add a line Otherwise adding a line is easy Just visit metro by tmobilecom and go to the 'my account' option If you are not suspended  there will be an option in the middle of the screen to add a line It will guide you through the process If that's all you needed you can simply hang up If you need help with something else say 'main menu'" src="DH1295_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DH1295_ini_01">
                <prompt-segments>
                  <audiofile text="If your account is suspended you will need to make a payment before you can add a line Otherwise adding a line is easy Just visit metro by tmobilecom and go to the 'my account' option If you are not suspended  there will be an option in the middle of the screen to add a line It will guide you through the process If that's all you needed you can simply hang up If you need help with something else say 'main menu'" src="DH1295_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DH1295_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' Or ff you need something else say 'main menu'" src="DH1295_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1295_nm1_02">
                <prompt-segments>
                  <audiofile text="If you need something else say 'main menu' or press 1" src="DH1295_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1295_nm1_02">
                <prompt-segments>
                  <audiofile text="If you need something else say 'main menu' or press 1" src="DH1295_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1295_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say 'repeat that' Or ff you need something else say 'main menu'" src="DH1295_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1295_nm1_02">
                <prompt-segments>
                  <audiofile text="If you need something else say 'main menu' or press 1" src="DH1295_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1295_nm1_02">
                <prompt-segments>
                  <audiofile text="If you need something else say 'main menu' or press 1" src="DH1295_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="DH1295_AddLineWrap_DM.grxml" count="1"/>
          <dtmfgrammars filename="DH1295_AddLineWrap_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="IF_NECESSARY" highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <custom-state id="DeviceHandling_TerminalSDReturn_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="DH1301_CheckContext_DS">
      <if cond="language == 'en-US'">
        <session-mapping key="TransferTag" expr="'Activations_Support_English'"/>
        <else>
          <session-mapping key="TransferTag" expr="'Activations_Support_Spanish'"/>
        </else>
      </if>
      <if cond="GlobalVars.GetAccountDetails.accountStatus == 'active'">
        <action next="DH1305_ESNSwap_SD"/>
        <else>
          <action next="DH1320_AskPayNowYN_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="DH1320_AskPayNowYN_DM" type="YSNO">
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentsEntryPoint" expr="'careESN'"/>
          <session-mapping key="GlobalVars.paymentAmount" expr="GlobalVars.GetAccountDetails.dueImmediatelyAmount"/>
          <action next="DH1325_MakePayment_SD"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <audio>
            <prompt id="DH1320_out_01">
              <prompt-segments>
                <audiofile text="Okay, you can pay any time online or in the myMetro app" src="DH1320_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_750ms">
              <prompt-segments>
                <audiofile text="test" src="silence_750ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DH1320_ini_01">
                <prompt-segments>
                  <audiofile text="Now, because your account is suspended, we'll need to take your payment before we can swap your phone Do you want to pay right now? " src="DH1320_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DH1320_ini_01">
                <prompt-segments>
                  <audiofile text="Now, because your account is suspended, we'll need to take your payment before we can swap your phone Do you want to pay right now? " src="DH1320_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DH1320_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to make a payment now, so we can continue with your swap? " src="DH1320_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1320_nm2_01">
                <prompt-segments>
                  <audiofile text="Do you want to make a payment now, so we can continue with your swap? " src="DH1320_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1320_nm2_01">
                <prompt-segments>
                  <audiofile text="Do you want to make a payment now, so we can continue with your swap? " src="DH1320_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1320_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you want to make a payment now, so we can continue with your swap? " src="DH1320_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1320_nm2_01">
                <prompt-segments>
                  <audiofile text="Do you want to make a payment now, so we can continue with your swap? " src="DH1320_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1320_nm2_01">
                <prompt-segments>
                  <audiofile text="Do you want to make a payment now, so we can continue with your swap? " src="DH1320_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="DH1320_AskPayNowYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="DH1320_AskPayNowYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="IF_NECESSARY" highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DH1320_AskPayNowYN_DM_confirmation_initial"/>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="DH1320_AskPayNowYN_DM_cnf_nomatch_1"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="DH1320_AskPayNowYN_DM_cnf_nomatch_1"/>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="DH1325_MakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="DH1325_MakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DH1325_MakePayment_SD_return_CS">
      <if cond="language == 'en-US'">
        <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
        <else>
          <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
        </else>
      </if>
      <action next="DH1330_PostPaymentTransition_PP"/>
    </custom-state>

    <play-state id="DH1330_PostPaymentTransition_PP">
      <audio>
        <prompt id="DH1330_out_01">
          <prompt-segments>
            <audiofile text="Okay, now back to switching your phone" src="DH1330_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
      <action next="DH1305_ESNSwap_SD"/>
    </play-state>

    <subdialog-state id="DH1305_ESNSwap_SD">
      <gotodialog next="CareESNSwap_Main_Dialog"/>
      <action next="DH1305_ESNSwap_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DH1305_ESNSwap_SD_return_CS">
      <if cond="language == 'en-US'">
        <session-mapping key="TransferTag" expr="'Customer_Support_English'"/>
        <else>
          <session-mapping key="TransferTag" expr="'Customer_Support_Spanish'"/>
        </else>
      </if>
      <if cond="GlobalVars.cancelESNSwap == true">
        <session-mapping key="GlobalVars.cancelESNSwap" value="false" type="Boolean"/>
        <session-mapping key="GlobalVars.callType" expr="undefined"/>
        <session-mapping key="GlobalVars.tag" expr="'undefined'"/>
        <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        <action next="getReturnLink()"/>
        <else>
          <action next="DH1310_ESNSuccessWrap_DM"/>
        </else>
      </if>
    </custom-state>

    <dm-state id="DH1310_ESNSuccessWrap_DM" type="CUST">
      <session-mapping key="simType" value="GlobalVars.ValidateDevice.simType" type="String"/>
      <session-mapping key="deviceChangeTargetSIMChosen" value="GlobalVars.deviceChangeTargetSIMChosen" type="String"/>
      <success>
        <action label="repeat">
          <audio>
            <prompt id="DH1310_out_01">
              <prompt-segments>
                <audiofile text="Sure" src="DH1310_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="DH1310_ESNSuccessWrap_DM"/>
        </action>
        <action label="main_menu">
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.tag" expr="undefined"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'">
                <prompt id="DH1310_ini_03">
                  <prompt-segments>
                    <audiofile text="Remember to go online at metro by t-mobile dot com slash esim on your new phone to complete the remaining steps to activate your eSIM After you do that,it should be working normally within a few hours If it's still not working after that, call 888-8METRO8 from a *different* phone, and select 'Troubleshooting tips' " src="DH1310_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DH1310_ini_01">
                    <prompt-segments>
                      <audiofile text="Restart your new phone now, and it should be working normally within a few hours If it's still not working after that, call 888-8METRO8 from a *different* phone, and select 'Troubleshooting tips' " src="DH1310_ini_01.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="DH1310_ini_02">
                <prompt-segments>
                  <audiofile text="Say repeat that or main menu If you re done, you can simply hang up " src="DH1310_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'">
                <prompt id="DH1310_nm1_02">
                  <prompt-segments>
                    <audiofile text="Remember to go to metro by t-mobile dot com slash esim to activate your eSIM To hear all the information again, say 'repeat' You can also say 'main menu', or if you're done you can simply hang up " src="DH1310_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DH1310_nm1_01">
                    <prompt-segments>
                      <audiofile text="We re all set, and your phone is switched To hear all the information again, say  repeat  You can also say main menu or if you re done you can simply hang up " src="DH1310_nm1_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1310_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say repeat that or press 1, or say main menu or press 2 If you re done, you can simply hang up " src="DH1310_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1310_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say repeat that or press 1, or say main menu or press 2 If you re done, you can simply hang up " src="DH1310_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="simType == 'ESIM' || deviceChangeTargetSIMChosen == 'ESIM'">
                <prompt id="DH1310_nm1_02">
                  <prompt-segments>
                    <audiofile text="Remember to go to metro by t-mobile dot com slash esim to activate your eSIM To hear all the information again, say 'repeat' You can also say 'main menu', or if you're done you can simply hang up " src="DH1310_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DH1310_nm1_01">
                    <prompt-segments>
                      <audiofile text="We re all set, and your phone is switched To hear all the information again, say  repeat  You can also say main menu or if you re done you can simply hang up " src="DH1310_nm1_01.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1310_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say repeat that or press 1, or say main menu or press 2 If you re done, you can simply hang up " src="DH1310_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1310_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say repeat that or press 1, or say main menu or press 2 If you re done, you can simply hang up " src="DH1310_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="DH1310_ESNSuccessWrap_DM.grxml" count="1"/>
          <dtmfgrammars filename="DH1310_ESNSuccessWrap_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="IF_NECESSARY" highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="main_menu == 'lastresult'">
                <prompt id="DH1310_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Main menu" src="DH1310_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="main_menu == 'lastresult'">
                <prompt id="DH1310_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Main menu" src="DH1310_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="main_menu == 'lastresult'">
                <prompt id="DH1310_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Main menu" src="DH1310_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="DH1315_Goodbye_SD">
      <gotodialog next="Goodbye_Main_Dialog"/>
      <action next="DH1315_Goodbye_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DH1315_Goodbye_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <decision-state id="DH1405_CheckContext_DS">
      <if cond="language == 'en-US'">
        <session-mapping key="TransferTag" expr="'Activations_Support_English'"/>
        <else>
          <session-mapping key="TransferTag" expr="'Activations_Support_Spanish'"/>
        </else>
      </if>
      <if cond="(GlobalVars.GetBCSParameters.webActivationsFlag  == 'true') ||(GlobalVars.GetBCSParameters.webActivationsFlag  == true)">
        <action next="DH1403_WebActivationRedirect_DM"/>
        <else>
          <action next="DH1410_AskReadyActivateYN_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="DH1403_WebActivationRedirect_DM" type="CUST">
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DH1403_ini_01">
                <prompt-segments>
                  <audiofile text="You can activate your Metro by T-Mobile phone with our easy-to-use online tool at metro by T-Mobile dot com slash activate Again, that's metro by T-Mobile dot com slash activate pause Feel free to hang up now to start your activation pause Or stay on the line if you need assistance" src="DH1403_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="DH1403_WebActivationRedirect_DM_initial"/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DH1403_ini_01">
                <prompt-segments>
                  <audiofile text="You can activate your Metro by T-Mobile phone with our easy-to-use online tool at metro by T-Mobile dot com slash activate Again, that's metro by T-Mobile dot com slash activate pause Feel free to hang up now to start your activation pause Or stay on the line if you need assistance" src="DH1403_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1"/>
          <noinputprompts count="2"/>
          <noinputprompts count="3"/>
          <nomatchprompts count="1"/>
          <nomatchprompts count="2"/>
          <nomatchprompts count="3"/>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
    </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="IF_NECESSARY" highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1"/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
      <success>
        <action label="operator"/>
      </success>
    </dm-state>

    <dm-state id="DH1410_AskReadyActivateYN_DM" type="CUST">
      <success>
        <action label="yes">
          <audio>
            <prompt id="DH1410_out_01">
              <prompt-segments>
                <audiofile text="Great! " src="DH1410_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="DH1415_AcceptTermsYN_DM"/>
        </action>
        <action label="no">
          <audio>
            <prompt id="DH1410_out_02">
              <prompt-segments>
                <audiofile text="No problem When you're ready, you can set up an account at metro by t dash mobile dot com " src="DH1410_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="DH1315_Goodbye_SD"/>
        </action>
        <action label="repeat">
          <audio>
            <prompt id="DH1410_out_03">
              <prompt-segments>
                <audiofile text="Sure" src="DH1410_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="DH1410_AskReadyActivateYN_DM"/>
        </action>
        <action label="inquire-store_location">
          <session-mapping key="GlobalVars.storeLocatorReason" expr="'sign_up'"/>
          <action next="DH1285_GoToStoreLocator_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DH1410_ini_03">
                <prompt-segments>
                  <audiofile text="To open an account today, you'll need the phone you wanna activate and a Metro SIM card, or a phone with an embedded SIM, also known as eSIM If you plan to activate an eSIM, make sure you'll have WiFi connection If you got the phone from another carrier, you'll also need to make sure it's NOT still active on another account, and that the other carrier's unlocked it And last, you'll need to pay your first month's charges before we can activate your account" src="DH1410_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_750ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_750ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1410_ini_02">
                <prompt-segments>
                  <audiofile text="Are you ready to go ahead now? Or say 'repeat' or 'find a store' " src="DH1410_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" filename="none" text="none" id=""/>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DH1410_nm1_01">
                <prompt-segments>
                  <audiofile text="Are you ready to open an account? To hear about everything we need again, say 'repeat'" src="DH1410_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1410_nm2_01">
                <prompt-segments>
                  <audiofile text="Are you ready to open an account? Say 'yes' or press 1 or 'no' or press 2 To hear about everything we need again, say 'repeat' or press 3" src="DH1410_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1410_nm2_01">
                <prompt-segments>
                  <audiofile text="Are you ready to open an account? Say 'yes' or press 1 or 'no' or press 2 To hear about everything we need again, say 'repeat' or press 3" src="DH1410_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1410_nm1_01">
                <prompt-segments>
                  <audiofile text="Are you ready to open an account? To hear about everything we need again, say 'repeat'" src="DH1410_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1410_nm2_01">
                <prompt-segments>
                  <audiofile text="Are you ready to open an account? Say 'yes' or press 1 or 'no' or press 2 To hear about everything we need again, say 'repeat' or press 3" src="DH1410_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1410_nm2_01">
                <prompt-segments>
                  <audiofile text="Are you ready to open an account? Say 'yes' or press 1 or 'no' or press 2 To hear about everything we need again, say 'repeat' or press 3" src="DH1410_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1"/>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
          <grammars filename="DH1410_AskReadyActivateYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="DH1410_AskReadyActivateYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration confirmationmode="IF_NECESSARY" highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="store == 'lastresult'">
                <prompt id="DH1410_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Find a store " src="DH1410_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="store == 'lastresult'">
                <prompt id="DH1410_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Find a store " src="DH1410_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="store == 'lastresult'">
                <prompt id="DH1410_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Find a store " src="DH1410_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="DH1415_AcceptTermsYN_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="DH1415_out_01">
              <prompt-segments>
                <audiofile text="Thanks" src="DH1415_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.activationEntryPoint" expr="'care'"/>
          <session-mapping key="GlobalVars.callType" expr="'activate'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="DH1420_Activation_SD"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="DH1415_out_02">
              <prompt-segments>
                <audiofile text="No problem You can read them and then set up an account at metro by t dash mobile dot com " src="DH1415_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="DH1315_Goodbye_SD"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DH1415_ini_01">
                <prompt-segments>
                  <audiofile text="First, do you accept our terms and conditions of service? They're on metro by T dash mobile dot com slash terms" src="DH1415_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="DH1415_AcceptTermsYN_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DH1415_ini_01">
                <prompt-segments>
                  <audiofile text="First, do you accept our terms and conditions of service? They're on metro by T dash mobile dot com slash terms" src="DH1415_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DH1415_nm1_01">
                <prompt-segments>
                  <audiofile text="You can read our terms and conditions of service on metro by T dash mobile dot com slash terms Do you accept them? " src="DH1415_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1415_nm2_01">
                <prompt-segments>
                  <audiofile text="If you accept our terms and conditions of service, say 'yes' or press 1 Otherwise, say 'no' or press 2  " src="DH1415_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1415_nm2_01">
                <prompt-segments>
                  <audiofile text="If you accept our terms and conditions of service, say 'yes' or press 1 Otherwise, say 'no' or press 2  " src="DH1415_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1415_nm1_01">
                <prompt-segments>
                  <audiofile text="You can read our terms and conditions of service on metro by T dash mobile dot com slash terms Do you accept them? " src="DH1415_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1415_nm2_01">
                <prompt-segments>
                  <audiofile text="If you accept our terms and conditions of service, say 'yes' or press 1 Otherwise, say 'no' or press 2  " src="DH1415_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DH1415_nm2_01">
                <prompt-segments>
                  <audiofile text="If you accept our terms and conditions of service, say 'yes' or press 1 Otherwise, say 'no' or press 2  " src="DH1415_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DH1415_ini_01">
                <prompt-segments>
                  <audiofile text="First, do you accept our terms and conditions of service? They're on metro by T dash mobile dot com slash terms" src="DH1415_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="DH1415_AcceptTermsYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="DH1415_AcceptTermsYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="false" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="DH1420_Activation_SD">
      <gotodialog next="Activation_Common_Dialog"/>
      <action next="DH1420_Activation_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DH1420_Activation_SD_return_CS">
      <if cond="(GlobalVars.activationResult == 'goodbye') || (GlobalVars.activationResult == 'complete')">
        <gotodialog next="Goodbye_Main_Dialog"/>
        <else>
          <session-mapping key="isMDE" value="false" type="Boolean"/>
          <gotodialog next="CallTransfer_Main_Dialog"/>
        </else>
      </if>
    </custom-state>

  </dialog>
  