"""
Simple script to run Step 4 with fixed indentation - Windows compatible
"""
import xml.etree.ElementTree as ET
import os
from llm_response import convertXmltoYaml
import logging
from util import read_filenames_in_folder, delete_existing_files,log_failed_file,remove_null_from_action
from fixed_indentation import fixed_topic_yaml_indentation
import llm_response

# Configure logging
logging.basicConfig(filename='agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')

def default_agent(content, topic_template, prompt_path):
    logging.info('step4_callAgentDecision:default_agent')
    logging.info(f'Input: {content}')
    convertXmltoYaml(content, prompt_path, topic_template)

def dvxml_agent(content, topic_template, prompt_path):
    logging.info('step4_callAgentDecision:dvxml_agent')
    logging.info(f'Input: {content}')
    convertXmltoYaml(content, prompt_path, topic_template)

def main():
    # Configure paths
    input_folder = './input/dialogs_file/'
    input_custom_folder = './input/custom_file/'
    input_dvxml_folder = './input/dvxml_file/'
    topic_template_path = './YAML-template/'
    output_yaml_path = './output/unformatted_topic_yaml/'
    final_topic_yaml = './output/topic_yaml/'
    
    # Prompt paths
    custom_state_prompt_path = 'prompts/few_shot_CPS_CS_topic.md'
    dvxml_prompt_path = 'prompts/few_shot_CPS_DVXML.md'
    
    print("Starting YAML processing with FIXED indentation function...")
    
    # Clear existing files
    delete_existing_files(output_yaml_path)
    delete_existing_files(final_topic_yaml)
    
    # Process dialog files
    print("\nProcessing dialog files...")
    dialog_files = read_filenames_in_folder(input_folder)
    success_count = 0
    
    for file in dialog_files:
        try:
            topic_template = topic_template_path + file + '.yml'
            default_agent(input_folder + file + '.xml', topic_template, 'prompts/few_shot_CPS_CS_topic.md')
            fixed_topic_yaml_indentation(output_yaml_path + file + '.yml', final_topic_yaml + file + '_topic.yml')
            
            fileName = final_topic_yaml + file + '_topic.yml'
            remove_null_from_action(fileName)
            success_count += 1
            print(f"SUCCESS: {file}")
            
        except Exception as e:
            logging.error(f'Failed to process dialog file {file}: {e}')
            log_failed_file(file)
            print(f"FAILED: {file}: {e}")
    
    print(f"\nDialog files: {success_count}/{len(dialog_files)} successful")
    
    # Process custom files
    print("\nProcessing custom files...")
    custom_files = read_filenames_in_folder(input_custom_folder)
    custom_success = 0
    
    for file in custom_files:
        try:
            topic_template = topic_template_path + file + '.yml'
            default_agent(input_custom_folder + file + '.xml', topic_template, custom_state_prompt_path)
            fixed_topic_yaml_indentation(output_yaml_path + file + '.yml', final_topic_yaml + file + '_topic.yml')
            
            fileName = final_topic_yaml + file + '_topic.yml'
            remove_null_from_action(fileName)
            custom_success += 1
            print(f"SUCCESS: {file}")
            
        except Exception as e:
            logging.error(f'Failed to process custom file {file}: {e}')
            log_failed_file(file)
            print(f"FAILED: {file}: {e}")
    
    print(f"\nCustom files: {custom_success}/{len(custom_files)} successful")
    
    # Process variable file
    print("\nProcessing variable file...")
    try:
        unformatted_variable_path = './output/unformatted_topic_yaml/All_Variable.yml'
        fixed_topic_yaml_indentation(unformatted_variable_path, final_topic_yaml + 'All_Variable.yml')
        print("SUCCESS: All_Variable.yml")
    except Exception as e:
        print(f"FAILED: All_Variable.yml: {e}")
    
    # Process DVXML files
    print("\nProcessing DVXML files...")
    dvxml_files = read_filenames_in_folder(input_dvxml_folder)
    dvxml_success = 0
    
    for file in dvxml_files:
        try:
            dvxml_topic_template = topic_template_path + file + '.yml'
            dvxml_agent(input_dvxml_folder + file + '.xml', dvxml_topic_template, dvxml_prompt_path)
            fixed_topic_yaml_indentation(output_yaml_path + file + '.yml', final_topic_yaml + file + '_topic.yml')
            
            fileName = final_topic_yaml + file + '_topic.yml'
            remove_null_from_action(fileName)
            dvxml_success += 1
            print(f"SUCCESS: {file}")
            
        except Exception as e:
            logging.error(f'Failed to process dvxml file {file}: {e}')
            log_failed_file(file)
            print(f"FAILED: {file}: {e}")
    
    print(f"\nDVXML files: {dvxml_success}/{len(dvxml_files)} successful")
    
    # Final summary
    total_files = len(dialog_files) + len(custom_files) + len(dvxml_files) + 1
    total_success = success_count + custom_success + dvxml_success + 1
    
    print(f"\nPROCESSING COMPLETE!")
    print(f"Total Success: {total_success}/{total_files} files")
    print(f"Output directory: {final_topic_yaml}")
    
    # Count final files
    final_files = read_filenames_in_folder(final_topic_yaml)
    print(f"Final YAML files created: {len(final_files)}")

if __name__ == "__main__":
    main()
