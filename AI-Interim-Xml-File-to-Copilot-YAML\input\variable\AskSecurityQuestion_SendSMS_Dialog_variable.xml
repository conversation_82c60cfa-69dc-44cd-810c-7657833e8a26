<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="DID" value="empty" type="String"/>
  <session-mapping key="smsType" value="empty" type="String"/>
  <session-mapping key="task" value="empty" type="String"/>
  <session-mapping key="smsParamValue" value="empty" type="String"/>
  <session-mapping key="GlobalVars.callType" value="pair_sim" type="string"/>
  <session-mapping key="GlobalVars.aniMatch" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.callerZipCode" value="GlobalVars.GetAccountDetails.zipCode" type="string"/>
  <session-mapping key="GlobalVars.smsZipCodeWrong" value="false" type="boolean"/>
  <session-mapping key="GlobalVars.saidOperatorAQ1205" value="false" type="boolean"/>
  <session-mapping key="aniMatch" value="true" type="boolean"/>
  <session-mapping key="askSQEntry" value="EWallet" type="string"/>
  <session-mapping key="saidOperator" value="true" type="boolean"/>
  <session-mapping key="returnFromWait" value="true" type="boolean"/>
  <session-mapping key="careAlterHandContainment" value="true" type="boolean"/>
  <session-mapping key="callType" value="esn_swap" type="string"/>
  <session-mapping key="GlobalVars.askSQEntry" value="EWallet" type="string"/>
  <session-mapping key="SendSMS.status" value="Success" type="string"/>
  <session-mapping key="smsWaitTimeout" value="true" type="boolean"/>
</session-mappings>
