<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="trn" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="did" value="empty" type="String"/>
  <session-mapping key="zipCode" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="IsServiceAvailable.status" value="Success" type="string"/>
  <session-mapping key="GlobalVars.serviceAvailable" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.noServiceInZipCount" value="2" type="integer"/>
  <session-mapping key="GlobalVars.deviceType" value="undefined" type="string"/>
  <session-mapping key="GetAvailableRatePlans.status" value="Success" type="string"/>
  <session-mapping key="ratePlans.length" value="0" type="integer"/>
  <session-mapping key="GlobalVars.activationResult" value="transfer" type="string"/>
</session-mappings>
