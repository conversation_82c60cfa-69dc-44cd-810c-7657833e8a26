<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="paymentType" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="JWTToken" value="empty" type="String"/>
  <session-mapping key="sessionID" value="empty" type="String"/>
  <session-mapping key="paymentAmount" value="empty" type="String"/>
  <session-mapping key="paymentMethod" value="empty" type="String"/>
  <session-mapping key="targetMdn" value="empty" type="String"/>
  <session-mapping key="accountNumber" value="empty" type="String"/>
  <session-mapping key="waiveConvFee" value="empty" type="String"/>
  <session-mapping key="cardNumber" value="empty" type="String"/>
  <session-mapping key="expirationDate" value="empty" type="String"/>
  <session-mapping key="preferredCardMethod" value="empty" type="String"/>
  <session-mapping key="billingZip" value="empty" type="String"/>
  <session-mapping key="securityCode" value="empty" type="String"/>
  <session-mapping key="creditCardSecurityCode" value="empty" type="String"/>
  <session-mapping key="nameOnCard" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="serialNumber" value="empty" type="String"/>
  <session-mapping key="clearCache" value="empty" type="String"/>
  <session-mapping key="voucherNumber" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="multiLineDetails" value="empty" type="String"/>
  <session-mapping key="GlobalVars.suspendedRatePlanChange" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.dataTopUpPayment" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.acceptedBCR" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.paymentsEntryPoint" value="careESN" type="string"/>
  <session-mapping key="payments_enable_prepaid_methods" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.authorizationFailureHandling" value="systemError" type="string"/>
  <session-mapping key="GlobalVars.payingWithPrepaid" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.GetPaymentOptions" value="undefined" type="string"/>
  <session-mapping key="GetPaymentOptions.status" value="BLACKLISTED" type="string"/>
  <session-mapping key="GetPaymentOptions.isWalletPopulated" value="true" type="string"/>
  <session-mapping key="GlobalVars.GetConvenienceFee" value="undefined" type="string"/>
  <session-mapping key="GlobalVars.callType" value="mdn_change" type="string"/>
  <session-mapping key="GlobalVars.completedOnePaymentOnCall" value="true|" type="string"/>
  <session-mapping key="GlobalVars.guestPayment" value="true" type="boolean"/>
  <session-mapping key="isAutopayEnabled" value="true" type="string"/>
  <session-mapping key="GetAccountDetails.accountStatus" value="suspended" type="string"/>
  <session-mapping key="GetPaymentOptions.madePaymentInLast24Hours" value="true" type="string"/>
  <session-mapping key="GlobalVars.heardRecentPaymentInfoCare" value="true" type="string"/>
  <session-mapping key="acceptedBCR" value="true" type="boolean"/>
  <session-mapping key="totalDueAmount" value="0" type="string"/>
  <session-mapping key="payDate" value="today" type="string"/>
  <session-mapping key="madePaymentInLast24Hours" value="true" type="boolean"/>
  <session-mapping key="GetAccountDetails.isAutopayEnabled" value="true" type="boolean"/>
  <session-mapping key="balanceAnddueImmediatelyAmount" value="0" type="integer"/>
  <session-mapping key="GetAccountDetails.eligibleForBillCycleReset" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.heardBCRTerms" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.authFailure" value="true" type="string"/>
  <session-mapping key="GlobalVars.cardStatus" value="amount_under_min" type="string"/>
  <session-mapping key="paymentErrorCount" value="1" type="integer"/>
  <session-mapping key="payingWithPrepaid" value="true" type="boolean"/>
  <session-mapping key="ProcessPayment.status" value="PENDING" type="string"/>
  <session-mapping key="GlobalVars.preferredPaymentMethod" value="credit" type="string"/>
  <session-mapping key="GlobalVars.isAuthorized" value="true" type="string"/>
  <session-mapping key="GlobalVars.reChargeStatus" value="Y" type="string"/>
  <session-mapping key="preferredPaymentMethod" value="credit" type="string"/>
  <session-mapping key="isSuspended" value="true" type="boolean"/>
  <session-mapping key="transactionId" value="undefined" type="string"/>
  <session-mapping key="callType" value="make_pmt_auto_pay" type="string"/>
  <session-mapping key="guestPayment" value="true" type="boolean"/>
  <session-mapping key="dueImmediatelyAmount" value="0" type="integer"/>
  <session-mapping key="numDaysBtwPayAndCurrentDate" value="7" type="string"/>
  <session-mapping key="isAutopayEligPlanExists" value="true" type="boolean"/>
  <session-mapping key="payingWithEWallet" value="false" type="boolean"/>
  <session-mapping key="amountDue" value="0" type="integer"/>
  <session-mapping key="GlobalVars.imeiEquipmentActive" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.iccidEquipmentActive" value="false" type="string"/>
  <session-mapping key="GlobalVars.GetAccountDetails" value="null" type="string"/>
  <session-mapping key="GetAccountDetails.ratePlan" value="null" type="string"/>
  <session-mapping key="GlobalVars.paymentFailure" value="true" type="boolean"/>
  <session-mapping key="AnonymousRechargeByVoucher.reChargeStatus" value="Y" type="string"/>
  <session-mapping key="isWalletPopulated" value="true" type="boolean"/>
</session-mappings>
