<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="BillCycleReset_Main_Dialog">
    <decision-state id="BR1001_CheckContext_DS">
      <action next="BR1005_OfferBCRYN_DM"/>
    </decision-state>

    <dm-state id="BR1005_OfferBCRYN_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="FromBR1005" value="GlobalVars.FromBR1005" type="String"/>
      <session-mapping key="imeiEquipmentActive" value="GlobalVars.imeiEquipmentActive" type="String"/>
      <session-mapping key="iccidEquipmentActive" value="GlobalVars.iccidEquipmentActive" type="String"/>
      <success>
        <action label="yes">
          <audio>
            <prompt id="BR1005_out_01">
              <prompt-segments>
                <audiofile text="Great" src="BR1005_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.acceptBCR" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="no">
          <audio>
            <prompt id="BR1005_out_02">
              <prompt-segments>
                <audiofile text="No problem" src="BR1005_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="more_info">
          <session-mapping key="GlobalVars.FromBR1005" value="false" type="Boolean"/>
          <action next="BR1005_OfferBCRYN_DM_PP"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="BR1005_ini_01" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="If you're ready to pay today, we can reset your due date So you won't have to pay for the days when your account was suspended" src="BR1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="callType == 'esn_swap'">
                <prompt id="BR1005_ini_02" cond="FromBR1005 != true">
                  <prompt-segments>
                    <audiofile text="After that, the agent can help you switch your phone" src="BR1005_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="imeiEquipmentActive == true &amp;&amp; iccidEquipmentActive == false">
                  <prompt id="BR1005_ini_03" cond="FromBR1005 != true">
                    <prompt-segments>
                      <audiofile text="After that, the agent can set up your phone" src="BR1005_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="BR1005_ini_04" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="Would you like to talk to someone about that?" src="BR1005_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1005_ini_05" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="You can also say 'more info'" src="BR1005_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <repeatprompts count="1">
            <audio>
              <prompt id="BR1005_ini_01" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="If you're ready to pay today, we can reset your due date So you won't have to pay for the days when your account was suspended" src="BR1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="callType == 'esn_swap'">
                <prompt id="BR1005_ini_02" cond="FromBR1005 != true">
                  <prompt-segments>
                    <audiofile text="After that, the agent can help you switch your phone" src="BR1005_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="imeiEquipmentActive == true &amp;&amp; iccidEquipmentActive == false">
                  <prompt id="BR1005_ini_03" cond="FromBR1005 != true">
                    <prompt-segments>
                      <audiofile text="After that, the agent can set up your phone" src="BR1005_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="BR1005_ini_04" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="Would you like to talk to someone about that?" src="BR1005_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1005_ini_05" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="You can also say 'more info'" src="BR1005_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="BR1005_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to reset your due date to start *today*? You can also say 'more info'" src="BR1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="BR1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to reset your due date, say 'yes' or press 1 Otherwise, say 'no' or press 2 You can also say 'more info' or press 3" src="BR1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="BR1005_nm3_01">
                <prompt-segments>
                  <audiofile text="If you'd like to reset your due date, press 1 If not, press 2 For more information, press 3" src="BR1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1005_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to reset your due date to start *today*? You can also say 'more info'" src="BR1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to reset your due date, say 'yes' or press 1 Otherwise, say 'no' or press 2 You can also say 'more info' or press 3" src="BR1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1005_nm3_01">
                <prompt-segments>
                  <audiofile text="If you'd like to reset your due date, press 1 If not, press 2 For more information, press 3" src="BR1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="BR1005_ini_01" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="If you're ready to pay today, we can reset your due date So you won't have to pay for the days when your account was suspended" src="BR1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="callType == 'esn_swap'">
                <prompt id="BR1005_ini_02" cond="FromBR1005 != true">
                  <prompt-segments>
                    <audiofile text="After that, the agent can help you switch your phone" src="BR1005_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="imeiEquipmentActive == true &amp;&amp; iccidEquipmentActive == false">
                  <prompt id="BR1005_ini_03" cond="FromBR1005 != true">
                    <prompt-segments>
                      <audiofile text="After that, the agent can set up your phone" src="BR1005_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt id="BR1005_ini_04" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="Would you like to talk to someone about that?" src="BR1005_ini_04.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="BR1005_ini_05" cond="FromBR1005 != true">
                <prompt-segments>
                  <audiofile text="You can also say 'more info'" src="BR1005_ini_05.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="BR1005_OfferBCRYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="BR1005_OfferBCRYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="BR1005_OfferBCRYN_DM_PP">
      <session-mapping key="FromBR1005" value="GlobalVars.FromBR1005" type="String"/>
      <audio>
        <if cond="FromBR1005 != true">
          <prompt type="custom">
            <param name="className" value="com.nuance.metro.audio.custom.PlayMoreInfo"/>
          </prompt>
        </if>
      </audio>
      <session-mapping key="GlobalVars.FromBR1005" value="true" type="Boolean"/>
      <action next="BR1005_OfferBCRYN_DM"/>
    </play-state>

  </dialog>
  