<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="PreTransferSecurity_Main_Dialog">
    <decision-state id="XS1001_CheckBCSSecurityConfig_DS">
      <session-mapping key="securityCheckEnabled" value="GlobalVars.GetBCSParameters.pretransfer_security_information_check_enabled" type="String"/>
      <if cond="(securityCheckEnabled == false) || (securityCheckEnabled == 'false')">
        <session-mapping key="GlobalVars.securityCheckPassed" value="true" type="Boolean"/>
        <action next="getReturnLink()"/>
        <else>
          <action next="XS1005_AskKnowDetailsYN_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="XS1005_AskKnowDetailsYN_DM" type="YSNO">
      <session-mapping key="saidOperator" value="GlobalVars.saidOperatorXS1005?GlobalVars.saidOperatorXS1005:false" type="String"/>
      <session-mapping key="securityQuestionCode" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityQuestionCode:''" type="String"/>
      <success>
        <action label="true">
          <session-mapping key="GlobalVars.securityCheckPassed" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperatorXS1005" value="false" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="false">
          <session-mapping key="GlobalVars.saidOperatorXS1005" value="false" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator">
          <if cond="GlobalVars.saidOperatorXS1005 == false">
            <session-mapping key="GlobalVars.saidOperatorXS1005" value="true" type="Boolean"/>
            <action next="XS1005_AskKnowDetailsYN_DM"/>
            <else>
              <action next="getReturnLink()"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="XS1005_rin_01" cond="saidOperator == true">
                <prompt-segments>
                  <audiofile text="In order to talk to an agent, you'll need either your 8-digit security code, or the answer to your security question Do you have one of those ready" src="XS1005_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_01" cond="saidOperator != true &amp;&amp; securityQuestionCode == 'Q13'">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* - your childhood nickname If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_02" cond="saidOperator != true &amp;&amp; securityQuestionCode == 'Q10'">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* - the name of your first pet If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_03" cond="saidOperator != true &amp;&amp; securityQuestionCode == 'Q12'">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* - the street you grew up on If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_04" cond="saidOperator != true &amp;&amp; (securityQuestionCode != 'Q12' &amp;&amp; securityQuestionCode != 'Q10' &amp;&amp; securityQuestionCode != 'Q13') ">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="XS1005_AskKnowDetailsYN_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="XS1005_rin_01" cond="saidOperator == true">
                <prompt-segments>
                  <audiofile text="In order to talk to an agent, you'll need either your 8-digit security code, or the answer to your security question Do you have one of those ready" src="XS1005_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_01" cond="saidOperator != true &amp;&amp; securityQuestionCode == 'Q13'">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* - your childhood nickname If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_02" cond="saidOperator != true &amp;&amp; securityQuestionCode == 'Q10'">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* - the name of your first pet If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_03" cond="saidOperator != true &amp;&amp; securityQuestionCode == 'Q12'">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* - the street you grew up on If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_04" cond="saidOperator != true &amp;&amp; (securityQuestionCode != 'Q12' &amp;&amp; securityQuestionCode != 'Q10' &amp;&amp; securityQuestionCode != 'Q13') ">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="XS1005_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you know your 8-digit security code, or the answer to your security question?" src="XS1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="XS1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have your 8-digit security code, or the answer to your security question ready to give the agent,  say 'yes' or press 1 Otherwise, say 'no' or press 2" src="XS1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="XS1005_nm3_01">
                <prompt-segments>
                  <audiofile text="If you know your security code or the answer to your security question, press 1 If you don't have either, press 2" src="XS1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_nm1_01">
                <prompt-segments>
                  <audiofile text="Do you know your 8-digit security code, or the answer to your security question?" src="XS1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_nm2_01">
                <prompt-segments>
                  <audiofile text="If you have your 8-digit security code, or the answer to your security question ready to give the agent,  say 'yes' or press 1 Otherwise, say 'no' or press 2" src="XS1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_nm3_01">
                <prompt-segments>
                  <audiofile text="If you know your security code or the answer to your security question, press 1 If you don't have either, press 2" src="XS1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="XS1005_rin_01" cond="saidOperator == true">
                <prompt-segments>
                  <audiofile text="In order to talk to an agent, you'll need either your 8-digit security code, or the answer to your security question Do you have one of those ready" src="XS1005_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_01" cond="saidOperator != true &amp;&amp; securityQuestionCode == 'Q13'">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* - your childhood nickname If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_02" cond="saidOperator != true &amp;&amp; securityQuestionCode == 'Q10'">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* - the name of your first pet If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_03" cond="saidOperator != true &amp;&amp; securityQuestionCode == 'Q12'">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* - the street you grew up on If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_03.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="XS1005_ini_04" cond="saidOperator != true &amp;&amp; (securityQuestionCode != 'Q12' &amp;&amp; securityQuestionCode != 'Q10' &amp;&amp; securityQuestionCode != 'Q13') ">
                <prompt-segments>
                  <audiofile text="Alright Metro cares about your security! So when you get to an agent, they'll need your 8-digit security *code*, or the answer to your security *question* If you have one of those ready to give the agent, say 'yes' Otherwise, say 'no'" src="XS1005_ini_04.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="XS1005_AskKnowDetailsYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="XS1005_AskKnowDetailsYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  