<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Goodbye_Main_Dialog">
    <play-state id="GB1005_PlayGoodbyeMessage_PP">
      <session-mapping key="otherGhostCaller" value="GlobalVars.otherGhostCaller" type="String"/>
      <session-mapping key="playGoodbyeMessage" value="GlobalVars.playGoodbyeMessage" type="String"/>
      <session-mapping key="skipGoodbye" value="GlobalVars.skipGoodbye" type="String"/>
      <session-mapping key="playFailureGoodbye" value="GlobalVars.playFailureGoodbye" type="String"/>
      <session-mapping key="securityQuestionCode" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.securityQuestionCode:''" type="String"/>
      <session-mapping key="maxRequests" value="GlobalVars.maxRequests" type="String"/>
      <session-mapping key="accountStatus" value="(GlobalVars.GetAccountDetails)?GlobalVars.GetAccountDetails.accountStatus :''" type="String"/>
      <session-mapping key="care_allow_suspended_transfers" value="GlobalVars.GetBCSParameters.care_allow_suspended_transfers == 'true'" type="String"/>
      <audio>
        <if cond="(skipGoodbye == true)">
          <prompt id="silence_100ms">
            <prompt-segments>
              <audiofile text="test" src="silence_100ms.wav"/>
            </prompt-segments>
          </prompt>
          <elseif cond="(operatorReqCount &gt; maxRequests) &amp;&amp; (accountStatus == 'suspended') &amp;&amp; (care_allow_suspended_transfers == false)">
            <prompt id="GB1005_out_03">
              <prompt-segments>
                <audiofile text="Since this account is suspended due to non-payment, I'm not able to transfer you to an agent  You'll need to make a payment, or setup a payment extension if eligible  You can do that here in the automated phone system, in the myMetro app, or online at metrobyt-mobilecom  Thanks for calling  Goodbye " src="GB1005_out_03.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="securityQuestionCode == 'SQ8'">
            <prompt id="GB1005_out_02">
              <prompt-segments>
                <audiofile text="It looks like this account has a security warning So you will need to get help in person at a Metro store There is map of our locations at metrobyt-mobilecom Sorry for the inconvenience! Thanks for calling Metro, by T-Mobile Goodbye! " src="GB1005_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="playFailureGoodbye == true">
            <prompt id="GB1005_out_04">
              <prompt-segments>
                <audiofile text="Thanks for calling Metro, by T-Mobile! Goodbye " src="GB1005_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="isMDE == true || otherGhostCaller == true">
            <prompt id="GB1005_out_01">
              <prompt-segments>
                <audiofile text="Alright, please try again later, or check out our website at metrobyt-mobilecom  Thank you for calling Metro PCS!  As always, we appreciate your business! Goodbye" src="GB1005_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <else>
            <prompt id="GB1005_ini_01" cond="playGoodbyeMessage == true">
              <prompt-segments>
                <audiofile text="Thanks for calling Metro, by T-Mobile!  As always, we appreciate your business! Goodbye " src="GB1005_ini_01.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
      </audio>
      <action next="GB1010_EndCall_CT_CS"/>
    </play-state>

    <custom-state id="GB1010_EndCall_CT_CS">
      <gotodialog next="Goodbye_Dialog"/>
    </custom-state>

  </dialog>
  