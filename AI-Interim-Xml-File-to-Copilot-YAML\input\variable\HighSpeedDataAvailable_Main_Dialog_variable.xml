<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="JWTToken" value="empty" type="String"/>
  <session-mapping key="ratePlan" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="GetAccountDetails.hasHighSpeedData" value="FromRatePlan" type="string"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="string"/>
  <session-mapping key="GlobalVars.aniMatch" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.tag" value="hear-data_usage" type="string"/>
  <session-mapping key="GlobalVars.callType" value="dataUsage" type="string"/>
  <session-mapping key="GetAvailableFeatureOffers.status" value="Success" type="string"/>
  <session-mapping key="topupEligibility" value="NOT-ELIGIBLE" type="string"/>
  <session-mapping key="eligibleForDataAddons" value="false" type="boolean"/>
  <session-mapping key="isDataUnlimited" value="true" type="string"/>
  <session-mapping key="supportedPlanIndicator" value="false" type="boolean"/>
  <session-mapping key="tag" value="buy-data_topup" type="string"/>
  <session-mapping key="value.dm_root" value="true" type="boolean"/>
</session-mappings>
