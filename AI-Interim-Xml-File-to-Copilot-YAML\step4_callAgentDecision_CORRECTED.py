import xml.etree.ElementTree as ET
import os
from llm_response import convertXmltoYaml
import logging
from util import read_filenames_in_folder, delete_existing_files, log_failed_file, remove_null_from_action
from fixed_indentation_clean import fixed_topic_yaml_indentation
import llm_response
from ruamel.yaml import YAML

# Configure logging
logging.basicConfig(filename='agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')

def main():
    # Reset the global variable for each run
    import llm_response
    llm_response.is_first_time = True
    
    # Configure paths
    input_folder = './input/dialogs_file/'
    input_custom_folder = './input/custom_file/'
    input_dvxml_folder = './input/dvxml_file/'
    topic_template_path = './YAML-template/'
    output_yaml_path = './output/unformatted_topic_yaml/'
    final_topic_yaml = './output/topic_yaml/'
    
    # Prompt paths
    custom_state_prompt_path = 'prompts/few_shot_CPS_CS_topic.md'
    dvxml_prompt_path = 'prompts/few_shot_CPS_DVXML.md'
    
    print("Starting YAML processing with FIXED indentation function...")
    print(f"📂 Input directory: {input_folder}")
    print(f"📂 Output directory: {final_topic_yaml}")
    
    # Ensure output directories exist
    os.makedirs(output_yaml_path, exist_ok=True)
    os.makedirs(final_topic_yaml, exist_ok=True)
    
    # Delete existing files to start fresh
    delete_existing_files(output_yaml_path)
    delete_existing_files(final_topic_yaml)
      # Process dialog files
    print("\n📁 Processing dialog files...")
    dialog_files = read_filenames_in_folder(input_folder)
    success_count = 0
    
    # Map XML element types to their corresponding prompt files
    tag_prompt_map = {
        "data-access-state": "prompts/few_shot_CPS_DA.md",
        "decision-state": "prompts/few_shot_CPS_DS.md", 
        "play-state": "prompts/few_shot_CPS_PP.md",
        "dm-state": "prompts/few_shot_CPS_DM.md",
        "subdialog-state": "prompts/few_shot_CPS_SD.md",
        "custom-state": "prompts/few_shot_CPS_CS.md"
    }
    
    for file in dialog_files:
        try:
            # Use the generic topic template instead of looking for specific ones
            topic_template = topic_template_path + 'Topic_template_v1.yml'
            
            # Remove .xml extension from filename to get the base name
            base_filename = file.replace('.xml', '') if file.endswith('.xml') else file
            
            print(f"   Processing: {file}")
            
            # Read the XML content
            with open(input_folder + file, 'r', encoding='utf-8') as f:
                xml_content = f.read()
            
            # Parse XML to determine the appropriate prompt file
            import xml.etree.ElementTree as ET
            try:
                root = ET.fromstring(xml_content)
                # Find the first child element to determine the dialog type
                prompt_file = None
                for element in root:
                    if element.tag in tag_prompt_map:
                        prompt_file = tag_prompt_map[element.tag]
                        break
                
                # If no matching tag found, use a default prompt
                if prompt_file is None:
                    prompt_file = "prompts/few_shot_CPS_DM.md"  # Default to DM prompt
                    print(f"   ⚠️  No specific prompt found for {file}, using default DM prompt")
                
            except ET.ParseError as e:
                print(f"   ⚠️  XML parsing error for {file}: {e}, using default DM prompt")
                prompt_file = "prompts/few_shot_CPS_DM.md"
            
            # Convert XML to YAML using the determined prompt file
            result = convertXmltoYaml(xml_content, prompt_file, topic_template)
            
            if result and result.strip():
                # Save unformatted YAML
                unformatted_file = output_yaml_path + base_filename + '.yml'
                with open(unformatted_file, 'w', encoding='utf-8') as f:
                    f.write(result)
                
                # Apply indentation fixes
                final_file = final_topic_yaml + base_filename + '_topic.yml'
                fixed_topic_yaml_indentation(unformatted_file, final_file)
                
                success_count += 1
                print(f"   ✅ Successfully processed: {base_filename}")
            else:
                print(f"   ❌ Empty result for: {file}")
                log_failed_file(file)
                
        except Exception as e:
            print(f"   ❌ Error processing {file}: {str(e)}")
            log_failed_file(file)
    
    print(f"\n📊 Dialog files: {success_count}/{len(dialog_files)} successful")
    
    # Process custom files
    print("\n📁 Processing custom files...")
    custom_files = read_filenames_in_folder(input_custom_folder)
    custom_success_count = 0
    
    for file in custom_files:
        try:
            # Remove .xml if present (since read_filenames_in_folder returns full filenames)
            base_filename = file.replace('.xml', '') if file.endswith('.xml') else file
            xml_file_path = input_custom_folder + base_filename + '.xml'
            
            topic_template = topic_template_path + 'Topic_template_v1.yml'
            
            print(f"   Processing: {base_filename}")
            
            # Check if XML file exists
            if not os.path.exists(xml_file_path):
                print(f"   ❌ File not found: {xml_file_path}")
                continue
                
            with open(xml_file_path, 'r', encoding='utf-8') as f:
                xml_content = f.read()
            
            result = convertXmltoYaml(xml_content, custom_state_prompt_path, topic_template)
            
            if result and result.strip():
                unformatted_file = output_yaml_path + base_filename + '.yml'
                with open(unformatted_file, 'w', encoding='utf-8') as f:
                    f.write(result)
                
                final_file = final_topic_yaml + base_filename + '_topic.yml'
                fixed_topic_yaml_indentation(unformatted_file, final_file)
                
                custom_success_count += 1
                print(f"   ✅ Successfully processed: {base_filename}")
            else:
                print(f"   ❌ Empty result for: {base_filename}")
                log_failed_file(base_filename)
                
        except Exception as e:
            print(f"   ❌ Error processing {base_filename}: {str(e)}")
            log_failed_file(base_filename)
    
    print(f"\n📊 Custom files: {custom_success_count}/{len(custom_files)} successful")
    
    # Process variable files (All_Variable.yml)
    print("\n📁 Processing variable files...")
    try:
        unformatted_variable_path = output_yaml_path + 'All_Variable.yml'
        if os.path.exists(unformatted_variable_path):
            fixed_topic_yaml_indentation(unformatted_variable_path, final_topic_yaml + 'All_Variable.yml')
            print("   ✅ Variable file processed successfully")
        else:
            print("   ℹ️ No variable file found to process")
    except Exception as e:
        print(f"   ❌ Error processing variable file: {str(e)}")
    
    # Process DVXML files
    print("\n📁 Processing DVXML files...")
    dvxml_files = read_filenames_in_folder(input_dvxml_folder)
    dvxml_success_count = 0
    
    for file in dvxml_files:
        try:
            # Remove .xml if present
            base_filename = file.replace('.xml', '') if file.endswith('.xml') else file
            xml_file_path = input_dvxml_folder + base_filename + '.xml'
            
            dvxml_topic_template = topic_template_path + 'Topic_template_v1.yml'
            
            print(f"   Processing: {base_filename}")
            
            # Check if XML file exists
            if not os.path.exists(xml_file_path):
                print(f"   ❌ File not found: {xml_file_path}")
                continue
            
            with open(xml_file_path, 'r', encoding='utf-8') as f:
                xml_content = f.read()
            
            result = convertXmltoYaml(xml_content, dvxml_prompt_path, dvxml_topic_template)
            
            if result and result.strip():
                unformatted_file = output_yaml_path + base_filename + '.yml'
                with open(unformatted_file, 'w', encoding='utf-8') as f:
                    f.write(result)
                
                final_file = final_topic_yaml + base_filename + '_topic.yml'
                fixed_topic_yaml_indentation(unformatted_file, final_file)
                
                dvxml_success_count += 1
                print(f"   ✅ Successfully processed: {base_filename}")
            else:
                print(f"   ❌ Empty result for: {base_filename}")
                log_failed_file(base_filename)
                
        except Exception as e:
            print(f"   ❌ Error processing {base_filename}: {str(e)}")
            log_failed_file(base_filename)
    
    print(f"\n📊 DVXML files: {dvxml_success_count}/{len(dvxml_files)} successful")
    
    # Final summary
    total_processed = len(dialog_files) + len(custom_files) + len(dvxml_files)
    total_successful = success_count + custom_success_count + dvxml_success_count
    
    print(f"\n🎯 FINAL SUMMARY:")
    print(f"   Total files processed: {total_successful}/{total_processed}")
    print(f"   Dialog files: {success_count}/{len(dialog_files)}")
    print(f"   Custom files: {custom_success_count}/{len(custom_files)}")
    print(f"   DVXML files: {dvxml_success_count}/{len(dvxml_files)}")
    print(f"📂 Output directory: {final_topic_yaml}")
    
    # Show final output files
    if os.path.exists(final_topic_yaml):
        final_files = read_filenames_in_folder(final_topic_yaml)
        print(f"📄 Generated {len(final_files)} YAML files")
        for yaml_file in final_files[:5]:  # Show first 5 files
            print(f"   - {yaml_file}")
        if len(final_files) > 5:
            print(f"   ... and {len(final_files) - 5} more files")
    
    print("\n✨ Processing complete!")

if __name__ == "__main__":
    main()
