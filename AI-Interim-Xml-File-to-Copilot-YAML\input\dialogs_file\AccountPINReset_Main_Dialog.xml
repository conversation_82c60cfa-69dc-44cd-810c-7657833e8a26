<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="AccountPINReset_Main_Dialog">
    <dm-state id="AR1005_OfferSMSLinkForAcctPinUpdate_DM" type="CUST">
      <session-mapping key="saidOperatorArAt1005" value="GlobalVars.saidOperatorArAt1005" type="String"/>
      <session-mapping key="saidRepeatAtAR1005" value="GlobalVars.saidRepeatAtAR1005 != undefined ? GlobalVars.saidRepeatAtAR1005 : false" type="String"/>
      <session-mapping key="GlobalVars.operatorReqCount_AR1005" expr="GlobalVars.operatorReqCount_AR1005 != undefined ? GlobalVars.operatorReqCount_AR1005 : 0"/>
      <success>
        <action label="repeat">
          <session-mapping key="GlobalVars.saidRepeatAtAR1005" value="true" type="Boolean"/>
          <action next="AR1005_OfferSMSLinkForAcctPinUpdate_DM"/>
        </action>
        <action label="main-menu">
          <session-mapping key="GlobalVars.needMDN" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.callType" expr="undefined"/>
          <session-mapping key="GlobalVars.tag" expr="undefined"/>
          <session-mapping key="GlobalVars.nluReEntryAfterSelfService" value="true" type="Boolean"/>
          <if cond="(GlobalVars.enteredFrom == 'PWR') &amp;&amp; (GlobalVars.GetAccountDetails == undefined)">
            <action next="getReturnLink()"/>
          </if>
          <if cond="(GlobalVars.GetBCSParameters.care_nlu_enabled == 'true') &amp;&amp; (language == 'en-US')">
            <action next="AR1015_NLUMainMenu_SD"/>
            <else>
              <action next="AR1010_MainMenu_SD"/>
            </else>
          </if>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidRepeatAtAR1005" value="false" type="Boolean"/>
          <session-mapping key="GlobalVars.operatorReqCount_AR1005" expr="GlobalVars.operatorReqCount_AR1005+1"/>
          <session-mapping key="callerSaidOperator" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.saidOperatorArAt1005" value="true" type="Boolean"/>
          <if cond="GlobalVars.operatorReqCount_AR1005 == 1">
            <audio>
              <prompt id="AR1005_Operator_01">
                <prompt-segments>
                  <audiofile text="To keep your account secure, only you can make adjustments to your account online Customer care is unable to reset your account PIN You can reset your account PIN online at metro by t dash mobile dot com Simply sign into your account , go to 'My Account' and select 'Profile' From there select, 'PIN/Passcode'To hear that again say,'Repeat that' To do something else, say 'main menu'" src="AR1005_Operator_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="AR1005_OfferSMSLinkForAcctPinUpdate_DM"/>
            <else>
              <session-mapping key="GlobalVars.operatorReqCount_AR1005" expr="undefined"/>
              <session-mapping key="GlobalVars.saidOperatorArAt1005" expr="undefined"/>
              <submit expr="operatorSubmitFunction('AccountPINReset_Main.dvxml','AR1005_OfferSMSLinkForAcctPinUpdate_DM',AR1005_OfferSMSLinkForAcctPinUpdate_DM.confidencescore)" namelist="language library version "/>
            </else>
          </if>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AR1005_ini_01" cond="saidOperatorArAt1005 != true || saidRepeatAtAR1005 == true">
                <prompt-segments>
                  <audiofile text="To keep your account secure, only you can make adjustments to your account online  Customer care is unable to reset your account PINYou can reset your account PIN online at metro by t dash mobile dot comSimply sign into your account , go to 'My Account' and select 'Profile'From there select,'PIN/Passcode'To hear that again say, 'Repeat that'  To do something else, say 'main menu'" src="AR1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AR1005_OfferSMSLinkForAcctPinUpdate_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" bargein="true" filename="" text="" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AR1005_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say repeat that' or To receive a text with the link to our website say 'send me  text' You can also say Main menu" src="AR1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AR1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'send me a text' or press 1  Main Menu or press 2" src="AR1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AR1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'send me a text' or press 1  Main Menu or press 2" src="AR1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AR1005_nm1_01">
                <prompt-segments>
                  <audiofile text="You can say repeat that' or To receive a text with the link to our website say 'send me  text' You can also say Main menu" src="AR1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AR1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'send me a text' or press 1  Main Menu or press 2" src="AR1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AR1005_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say 'send me a text' or press 1  Main Menu or press 2" src="AR1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AR1005_ini_01" cond="saidOperatorArAt1005 != true || saidRepeatAtAR1005 == true">
                <prompt-segments>
                  <audiofile text="To keep your account secure, only you can make adjustments to your account online  Customer care is unable to reset your account PINYou can reset your account PIN online at metro by t dash mobile dot comSimply sign into your account , go to 'My Account' and select 'Profile'From there select,'PIN/Passcode'To hear that again say, 'Repeat that'  To do something else, say 'main menu'" src="AR1005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_OperatorOnly.grxml" dtmfcommandgrammar="GlobalCommands_OperatorOnly_dtmf.grxml">
          <grammars filename="AR1005_OfferSMSLinkForAcctPinUpdate_DM.grxml" count="1"/>
          <dtmfgrammars filename="AR1005_OfferSMSLinkForAcctPinUpdate_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false"/>
        <prompt_configuration>
          <initialprompt count="1"/>
          <reentryprompt count="1" bargein="true" filename="" text="" id=""/>
          <noinputprompts count="1"/>
          <nomatchprompts count="1"/>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="AR1010_MainMenu_SD">
      <gotodialog next="MainMenu_Main_Dialog"/>
      <action next="AccountPINReset_TerminalSDReturn"/>
    </subdialog-state>
    <subdialog-state id="AR1015_NLUMainMenu_SD">
      <gotodialog next="NLUMainMenu_Start_Dialog"/>
      <action next="AR1015_NLUMainMenu_SDReturn_CS"/>
    </subdialog-state>
    <custom-state id="AR1015_NLUMainMenu_SDReturn_CS">
      <gotodialog next="NLURouting_Start_Dialog"/>
    </custom-state>

  </dialog>
  