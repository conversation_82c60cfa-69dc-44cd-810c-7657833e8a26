import lxml.etree as ET
import xml.dom.minidom as minidom
import re
import os

def remove_colons_in_audiofile_text(root):
    # Iterate through all audiofile elements
    for audiofile in root.iter('audiofile'):
        text_value = audiofile.get('text')
        if text_value:
            # Remove colons and periods from the text attribute
            cleaned_text = text_value.replace(':', '').replace('.', '')
            audiofile.set('text', cleaned_text)

    #print("Removed colons and periods from audiofile text attributes.")
    return root

def update_ids_in_xml(root):
    # Mapping of tag to required suffix
    suffix_mapping = {
        'play-state': '_PP',
        'data-access-state': '_DA',
        'dm-state': '_DM',
        'decision-state': '_DS',
        'custom-state': '_CS',
        'dialog': '_Dialog'
    }

    # Function to update ID based on tag
    def update_id(element, suffix):
        old_id = element.get('id')
        if old_id and not old_id.endswith(suffix):
            new_id = old_id + suffix
            element.set('id', new_id)
            return old_id, new_id
        return None, None

    # Dictionary to store the old and new ID mapping
    id_mapping = {}

    # Iterate over each element in the tree and update IDs
    for element in root.iter():
        for tag, suffix in suffix_mapping.items():
            if element.tag == tag:
                old_id, new_id = update_id(element, suffix)
                if old_id and new_id:
                    id_mapping[old_id] = new_id
                    #print(f'Updated ID: {old_id} -> {new_id}')

    # Update references to the old IDs throughout the XML
    def replace_references(element, id_mapping):
        for attr, value in element.attrib.items():
            if value in id_mapping:
                #print(f'Updated reference: {attr}="{value}" -> {attr}="{id_mapping[value]}"')
                element.set(attr, id_mapping[value])

    for element in root.iter():
        replace_references(element, id_mapping)

    #print("Updated IDs in XML.")
    return root

## THIS METHOD WILL ADD DA NODE ABOVE ALL THE DS THOSE ARE CALLING JAVA
def add_data_access_state(root):
    # Create a list to store decision states to modify
    decision_states_to_modify = []
    # Iterate through decision states
    for decision_state in root.findall(".//decision-state"):
        # Check if the decision state contains direct if/else conditions
        if decision_state.find("if") is not None or decision_state.find("elseif") is not None or decision_state.find("else") is not None:
            continue

        # Check for the <action> elements within the decision state
        actions = decision_state.findall("action")
        all_actions_match = True
        for action in actions:
            label = action.get("label")
            next_action = action.get("next")
            # Use regex to match label and next (with any value)
            if not (label and next_action and re.match(r".*", label) and re.match(r".*", next_action)):
                all_actions_match = False
                break

        # Check if the decision state contains a <script> tag
        if decision_state.find("script") is not None:
            all_actions_match = True

        # If all <action> elements within this decision state match the pattern or contains a <script> tag, add this decision state to the list
        if all_actions_match:
            decision_states_to_modify.append(decision_state)

    # Insert the new data access state as a sibling before each relevant decision state
    for decision_state in decision_states_to_modify:
        # Get the ID of the decision state
        decision_state_id = decision_state.get('id')
        if decision_state_id:
            # Construct the new ID for data access state by appending '_JDA' to the decision state ID
            new_data_access_state_id = f"{decision_state_id[:-3]}_JDA"  # Remove the '_DS' suffix and add '_JDA'
            # Define the new data access state element with the constructed ID
            new_state = ET.Element("data-access-state", id=new_data_access_state_id)
            data_access = ET.SubElement(new_state, "data-access", id="WhatsNext")
            outputs = ET.SubElement(data_access, "outputs")
            ET.SubElement(outputs, "output-variable", name="label", type="String")
            ET.SubElement(new_state, "action", next=decision_state_id)
            
            # Find the parent of the decision state manually
            parent = None
            for potential_parent in root.findall(".//"):
                if decision_state in list(potential_parent):
                    parent = potential_parent
                    break
            if parent is not None:
                index = list(parent).index(decision_state)
                parent.insert(index, new_state)
    return root

def add_empty_lines_between_states(xml_string):
    # Define the regular expression pattern to match the closing tags of the specified state elements
    pattern = r"(</(data-access-state|decision-state|dm-state|custom-state|play-state)>)"
    # Substitute each matched closing tag with the tag followed by a newline for spacing
    modified_xml_string = re.sub(pattern, r"\1\n", xml_string)
    #print("Added empty lines between state elements.")
    return modified_xml_string


## This method will changes the trasition to JDA(created in above method) node from DS
def update_transition_for_DS(xml_file_path):
    # Parse the XML file
    tree = ET.parse(xml_file_path)
    root = tree.getroot()

    # Traverse all action elements and update 'next' attributes
    for action in root.findall(".//action"):
        next_attr = action.get('next')
        if next_attr and next_attr.endswith('_DS'):
            action.set('next', next_attr[:-3] + '_JDA')  # Replace _DS with _JDA
    
        # Check for 'data-access-state' elements and replace values
    for data_access_state in root.findall('.//data-access-state'):
        id_value = data_access_state.get('id', '')
        if id_value.endswith('_JDA'):
            # Find the next 'action' element
            action = data_access_state.find('action')
            if action is not None:
                next_value = action.get('next', '')
                # Replace '_JDA' with '_DS' in the 'next' attribute
                if '_JDA' in next_value:
                    new_next_value = next_value.replace('_JDA', '_DS')
                    action.set('next', new_next_value)
    # Write the updated XML back to file
    tree.write(xml_file_path)


def conditional_PP_modification(xml_file_path):

    tree = ET.parse(xml_file_path)
    root = tree.getroot()

    # Find all <play-state> and <initialprompt> tags
    for play_state_tag in root.findall(".//play-state") + root.findall(".//initialprompt")+ root.findall(".//noinputprompts")+ root.findall(".//nomatchprompts") +root.findall(".//audio"):
        # Within each <play-state> and <initialprompt>, find all <if> tags
        for if_tag in play_state_tag.findall(".//if") + play_state_tag.findall(".//elseif") + play_state_tag.findall(".//else"):
            conditions = []
            condition_tags_to_remove = []

            # Check for <condition> tags where classname contains "StringCompare"
            for condition in list(if_tag):  # Use list(if_tag) to get direct children only
                classname = condition.get("classname", "")
                if "StringCompare" in classname:
                    param_compare = condition.find("param[@name='compare']").get("value")
                    param_value = condition.find("param[@name='value']").get("value")

                    # Determine operator
                    operator = condition.find("param[@name='operator']")
                    operator_value = "!=" if operator is not None and operator.get("value") == "NOT" else "=="
                    condition_str = f"{param_value} {operator_value} '{param_compare}'"
                    conditions.append(condition_str)

                    # Mark this condition for removal after processing
                    condition_tags_to_remove.append(condition)

            # Create the combined condition string if any conditions exist
            if conditions:
                if_condition = " && ".join(conditions)
                if_tag.set("cond", if_condition)

                # Remove type="java" attribute if present
                if "type" in if_tag.attrib:
                    del if_tag.attrib["type"]

                # Remove all processed <condition> tags
                for condition in condition_tags_to_remove:
                    if_tag.remove(condition)
    # Write the updated XML back to file
    tree.write(xml_file_path)

#remove event tag from input xml
def remove_event_tags(xml_file):
    tree = ET.parse(xml_file)
    root = tree.getroot()
    # Find all event tags within the XML and remove them, except within custom-state tags
    for event in root.findall('.//event'):
        parent = event.getparent()
        if parent is not None and parent.tag != 'custom-state':
            parent.remove(event)

    # Save the modified XML back to the file
    tree.write(xml_file, pretty_print=True, xml_declaration=True, encoding="utf-8")

def transform_action_elements(root):
    """
    Transform only action elements that have next attribute containing .dvxml
    into a nested structure with gotodialog element.
    Other action elements remain unchanged.
    """
    # Iterate through all action elements in the XML
    for action in root.iter('action'):
        # Check if both label and next attributes exist
        if 'label' in action.attrib and 'next' in action.attrib:
            next_value = action.get('next')
            
            # Only transform if next contains .dvxml
            if '.dvxml' in next_value:
                # Remove the next attribute
                action.attrib.pop('next')
                
                # Create new gotodialog element with proper indentation
                #next_value = next_value.replace('.dvxml', '')
                dialog_element = ET.SubElement(action, 'gotodialog')
                dialog_element.set('next', next_value)
                remove_gotodialog_with_dvxml(root)

                
                # Ensure proper indentation
                dialog_element.tail = '\n        '
                
                # If this action has other children, preserve their order and adjust their indentation
                for child in action:
                    if child != dialog_element:
                        child.tail = '\n        '
                
                # Set the last child's tail to include proper closing indentation
                if len(action) > 0:
                    last_child = list(action)[-1]
                    last_child.tail = '\n    '

    return root

def remove_gotodialog_with_dvxml(root):
    """
    Check all gotodialog tags and remove '.dvxml' from the next attribute if it contains '.dvxml'.
    """
    for gotodialog in root.findall(".//gotodialog"):
        next_value = gotodialog.get('next')
        if next_value and '.dvxml' in next_value:
            new_next_value = next_value.replace('.dvxml', '')
            gotodialog.set('next', new_next_value)
    return root

# to move DM command tag under success tag
def move_command_actions_to_success_in_dm_state(root):
 
    # Find all <dm-state> tags
    dm_state_tags = root.findall('.//dm-state')

    for dm_state in dm_state_tags:
        # Find the <success> tag within the <dm-state>
        success_tag = dm_state.find('.//success')
        if success_tag is None:
            success_tag = ET.SubElement(dm_state, 'success')

        # Find all <command> tags within the <dm-state>
        command_tags = dm_state.findall('.//command')

        for command_tag in command_tags:
            # Move all <action> tags from <command> to <success>
            for action in command_tag.findall('action'):
                success_tag.append(action)
            
            # Remove the <command> tag after processing its actions
            parent = command_tag.getparent()
            if parent is not None:
                parent.remove(command_tag)

    return root

def process_xml(input_xml_file):
    # Parse the XML file
    tree = ET.parse(input_xml_file)
    root = tree.getroot()
    output_xml_file = 'input/cleaned/updated-state-engine.xml'

    if os.path.exists(output_xml_file):
        os.remove(output_xml_file)
        print(f"Deleted existing file: {output_xml_file}")
    else:
        print(f"No existing file found: {output_xml_file}")

    # Step 0: Transform action elements (new step)
    root = transform_action_elements(root)

    # Step 1: Remove colons and periods in audiofile text attributes
    root = remove_colons_in_audiofile_text(root)

    # Step 2: Update IDs based on tag-suffix rules
    root = update_ids_in_xml(root)

    # Step 3: Add data access state before specific decision-state elements
    root = add_data_access_state(root)

    # Step 4: Move command actions to success in DM state
    root = move_command_actions_to_success_in_dm_state(root)

    # Step 5: Pretty-print the modified XML
    xml_str = ET.tostring(root, encoding="utf-8")
    pretty_xml_str = minidom.parseString(xml_str).toprettyxml(indent="  ")
    pretty_xml_str = "\n".join([line for line in pretty_xml_str.split("\n") if line.strip()])

    # Step 6: Add empty lines between specified state elements
    pretty_xml_str = add_empty_lines_between_states(pretty_xml_str)

    # Step 7: Save the modified XML to a file
    os.makedirs(os.path.dirname(output_xml_file), exist_ok=True)
    with open(output_xml_file, "w", encoding="utf-8") as f:
        f.write(pretty_xml_str)

    update_transition_for_DS(output_xml_file)
    conditional_PP_modification(output_xml_file)
    remove_event_tags(output_xml_file)
    #print(f"Updated XML file saved to: {output_xml_file}")


# # Input and output file paths
input_xml_file = 'input/updated-state-engine.xml'

# # # Run the function to process the XML
if os.path.exists('agent.log'):
    os.remove('agent.log')
process_xml(input_xml_file)
