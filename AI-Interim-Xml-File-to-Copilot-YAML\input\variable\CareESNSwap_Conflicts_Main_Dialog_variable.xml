<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="mdn" value="empty" type="String"/>
  <session-mapping key="imei" value="empty" type="String"/>
  <session-mapping key="sessionId" value="empty" type="String"/>
  <session-mapping key="languageCode" value="empty" type="String"/>
  <session-mapping key="cheaperOnly" value="empty" type="String"/>
  <session-mapping key="JWTToken" value="empty" type="String"/>
  <session-mapping key="futureChangeOffer" value="empty" type="String"/>
  <session-mapping key="features" value="empty" type="String"/>
  <session-mapping key="currentRatePlan" value="empty" type="String"/>
  <session-mapping key="currentFeatures" value="empty" type="String"/>
  <session-mapping key="featuresThirdParty" value="empty" type="String"/>
  <session-mapping key="ValidateDevice.currentAddOnEligibleIndicator" value="false" type="boolean"/>
  <session-mapping key="ValidateDevice.currentPlanEligibleIndicator" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.isOnFamilyPlan" value="true" type="boolean"/>
  <session-mapping key="GetBCSParameters.care_enable_multiline_esnplanchange" value="true" type="boolean"/>
  <session-mapping key="isOnFamilyPlan" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.cancelESNSwap" value="true" type="boolean"/>
  <session-mapping key="GetAvailableRatePlanOffers.status" value="Success" type="string"/>
  <session-mapping key="CalculateFeatureConflicts.status" value="Success" type="string"/>
  <session-mapping key="esnSwapIncompatibleFeatures" value="" type="string"/>
  <session-mapping key="interpretation.dm_root" value="continue" type="string"/>
  <session-mapping key="aniMatch" value="true" type="boolean"/>
  <session-mapping key="switchLinesSuccess" value="false" type="boolean"/>
  <session-mapping key="esnChangedPlan" value="true" type="boolean"/>
</session-mappings>
