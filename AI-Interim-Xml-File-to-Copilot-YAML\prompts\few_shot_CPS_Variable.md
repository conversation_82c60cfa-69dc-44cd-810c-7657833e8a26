memorize below yaml format

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.collectionNiOrNm_Temp
  value: undefined
	  

also use these instruction while conversion 
1) kind should always be "SetVariable"  
2) for every "session-mapping" tag within input, convert it into below format.
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.hangupRequired
      value: true
3) if there is special character # while session-mapping . put it under quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: '#'
4) if there is any condition in the value of session-mapping then put it under double quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: "GlobalVars.GetTagData.tagVariables != undefined ? GlobalVars.GetTagData.tagVariables.needMDN : 'false'"
5) Replace "SetVariable" id  with "setVariable_REPLACE_THIS".
6) indentation for next yaml should be same as first one or as memorized yaml.
7) do not provide duplicate yaml.. generate only for provided inputs.

### Example 1:
**Input XML:**
```xml
<conditions>
  <session-mapping key="callerLanguage" value="ar" type="String"/>
  <session-mapping key="collectionCounter" value="0" type="Integer"/>
  <session-mapping key="callComesFromAgent" value="true" type="Boolean"/>
</conditions>
```

**Output yaml:**
```yaml
- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.callerLanguage
  value: ar
- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.collectionCounter
  value: 0
- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.callComesFromAgent
  value: true
```
