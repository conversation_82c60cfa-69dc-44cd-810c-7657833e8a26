<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="CareESNSwap_Conflicts_Main_Dialog">
    <decision-state id="ES1905_CheckAccountIssue_DS">
      <if cond="GlobalVars.ValidateDevice.currentAddOnEligibleIndicator == false &amp;&amp; GlobalVars.ValidateDevice.currentPlanEligibleIndicator == true">
        <action next="ES1918_GetAvailableRatePlanOffers_DB_DA"/>
        <else>
          <if cond="GlobalVars.isOnFamilyPlan == true">
            <action next="ES1907_CheckMultilineAcctsConfig_JDA"/>
            <else>
              <action next="ES1910_PlayPlanConflict_PP"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="ES1906_PlayAccountIssues_PP">
      <audio>
        <prompt id="ES1906_out_01">
          <prompt-segments>
            <audiofile text="Actually, your current plan or add-on services are NOT gonna work with your new phone Our agents will take it from here! " src="ES1906_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="ES1935_Transfer_SD"/>
    </play-state>

    <decision-state id="ES1907_CheckMultilineAcctsConfig_DS">
      <if cond="GlobalVars.GetBCSParameters.care_enable_multiline_esnplanchange == true || GlobalVars.GetBCSParameters.care_enable_multiline_esnplanchange == 'true'">
        <action next="ES1910_PlayPlanConflict_PP"/>
        <else>
          <action next="ES1906_PlayAccountIssues_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="ES1910_PlayPlanConflict_PP">
      <session-mapping key="isOnFamilyPlan" value="GlobalVars.GetAccountDetails.isOnFamilyPlan" type="String"/>
      <audio>
        <prompt id="ES1910_out_01">
          <prompt-segments>
            <audiofile src="ES1910_out_01.wav" text="Actually, it looks like your current plan s NOT gonna work with your new phone, so let s pick a new one!"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="ES1910_out_02" cond="isOnFamilyPlan == true">
          <prompt-segments>
            <audiofile src="ES1910_out_02.wav" text="Keep in mind that other lines on your account may be impacted as well At this time, I can't present your plan change options with multi-line pricing included But after you select one of our base plan options, I'll be able to calculate your new total monthly price, inclusive of all lines, before you confirm the change "/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms" cond="isOnFamilyPlan == true">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="ES1915_ESNPlanChange_SD"/>
    </play-state>

    <subdialog-state id="ES1915_ESNPlanChange_SD">
      <gotodialog next="CareESNSwapChangePlan_Main"/>
      <action next="ES1915_ESNPlanChange_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="ES1915_ESNPlanChange_SD_return_CS">
      <if cond="GlobalVars.cancelESNSwap == true">
        <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
        <action next="getReturnLink()"/>
        <else>
          <session-mapping key="GlobalVars.esnChangedPlan" value="true" type="Boolean"/>
          <gotodialog next="CareESNSwap_Main_Cont#ES1505_SubmitSwapTransition_DM"/>
        </else>
      </if>
    </custom-state>

    <data-access-state id="ES1918_GetAvailableRatePlanOffers_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails.mdn" type="String"/>
      <session-mapping key="sessionId" value="GlobalVars.sessionId" type="String"/>
      <session-mapping key="imei" value="GlobalVars.imeiSerialNumber" type="String"/>
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <session-mapping key="currentRatePlan" value="GlobalVars.GetAccountDetails.ratePlan" type="String"/>
      <session-mapping key="futureChangeOffer" value="false" type="Boolean"/>
      <data-access id="GetAvailableRatePlanOffers" classname="com.nuance.metro.dataaccess.GetAvailableRatePlanOffers">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="imei"/>
          <input-variable name="sessionId"/>
          <input-variable name="languageCode"/>
          <input-variable name="cheaperOnly"/>
          <input-variable name="JWTToken"/>
          <input-variable name="futureChangeOffer"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="ratePlans"/>
          <output-variable name="futureDate"/>
          <output-variable name="restrictionType"/>
          <output-variable name="existingPlanPricePrimaryRate"/>
          <output-variable name="RatePlanGrammarURL"/>
          <output-variable name="RatePlanDTMFGrammarURL"/>
          <output-variable name="RatePlanUnambiguousGrammarURL"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="(GetAvailableRatePlanOffers &amp;&amp; GetAvailableRatePlanOffers.status == 'Success')">
          <session-mapping key="GlobalVars.ratePlans" expr="GetAvailableRatePlanOffers.ratePlans"/>
          <session-mapping key="GlobalVars.GetAvailableRatePlans" expr="GetAvailableRatePlanOffers"/>
          <session-mapping key="GlobalVars.futureDate" expr="GetAvailableRatePlanOffers.futureDate"/>
          <session-mapping key="GlobalVars.restrictionType" expr="GetAvailableRatePlanOffers.restrictionType"/>
          <session-mapping key="GlobalVars.RatePlanGrammarURL" expr="GetAvailableRatePlanOffers.RatePlanGrammarURL"/>
          <session-mapping key="GlobalVars.RatePlanDTMFGrammarURL" expr="GetAvailableRatePlanOffers.RatePlanDTMFGrammarURL"/>
          <session-mapping key="GlobalVars.RatePlanUnambiguousGrammarURL" expr="GetAvailableRatePlanOffers.RatePlanUnambiguousGrammarURL"/>
          <action next="ES1919_CalculateFeatureConflicts_DB_DA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <data-access-state id="ES1919_CalculateFeatureConflicts_DB_DA">
      <data-access id="CalculateFeatureConflicts" classname="com.nuance.metro.dataaccess.CalculateFeaturesConflict">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="features"/>
          <input-variable name="currentRatePlan"/>
          <input-variable name="currentFeatures"/>
          <input-variable name="featuresThirdParty"/>
        </inputs>
        <outputs>
          <output-variable name="esnSwapIncompatibleFeatures"/>
          <output-variable name="featureMedialPromptURL"/>
          <output-variable name="featureFinalPromptTTS"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="CalculateFeatureConflicts.status == 'Success'">
          <session-mapping key="GlobalVars.CalculateFeatureConflicts" expr="CalculateFeatureConflicts"/>
          <session-mapping key="GlobalVars.esnSwapIncompatibleFeatures" expr="GlobalVars.CalculateFeatureConflicts.esnSwapIncompatibleFeaturesSocs"/>
          <session-mapping key="GlobalVars.esnSwapIncompatibleFeaturesObj" expr="GlobalVars.CalculateFeatureConflicts.esnSwapIncompatibleFeatures"/>
          <action next="ES1920_PlayFeatureConflict_PP"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <action next="ES1935_Transfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <play-state id="ES1920_PlayFeatureConflict_PP">
      <session-mapping key="esnSwapIncompatibleFeatures" value="GlobalVars.esnSwapIncompatibleFeatures" type="String"/>
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.ES1920_PlayFeatureConflict"/>
        </prompt>
      </audio>
      <if cond="esnSwapIncompatibleFeatures == ''">
        <action next="ES1935_Transfer_SD"/>
        <else>
          <action next="ES1925_AskContinueCancelFeature_DM"/>
        </else>
      </if>
    </play-state>

    <dm-state id="ES1925_AskContinueCancelFeature_DM" type="CUST">
      <success>
        <action label="continue">
          <audio>
            <prompt id="ES1925_out_01">
              <prompt-segments>
                <audiofile src="ES1925_out_01.wav" text="Okay"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.removeFeatures" expr="GlobalVars.esnSwapIncompatibleFeatures"/>
          <session-mapping key="GlobalVars.acceptedRemoveFeatures" value="true" type="Boolean"/>
          <gotodialog next="CareESNSwap_Main_Cont#ES1505_SubmitSwapTransition_DM"/>
        </action>
        <action label="cancel">
          <audio>
            <prompt id="ES1925_out_02">
              <prompt-segments>
                <audiofile src="ES1925_out_02.wav" text="No problem, I won t change anything"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.cancelESNSwap" value="true" type="Boolean"/>
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES1925_ini_01">
                <prompt-segments>
                  <audiofile src="ES1925_ini_01.wav" text="Say  continue , or  cancel "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="ES1925_AskContinueCancelFeature_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES1925_ini_01">
                <prompt-segments>
                  <audiofile src="ES1925_ini_01.wav" text="Say  continue , or  cancel "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES1925_nm2_01">
                <prompt-segments>
                  <audiofile src="ES1925_nm2_01.wav" text="If you want to switch your phone and remove the features that won t work anymore, say  continue  or press 1 Otherwise, say  cancel  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1925_nm2_01">
                <prompt-segments>
                  <audiofile src="ES1925_nm2_01.wav" text="If you want to switch your phone and remove the features that won t work anymore, say  continue  or press 1 Otherwise, say  cancel  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1925_nm2_01">
                <prompt-segments>
                  <audiofile src="ES1925_nm2_01.wav" text="If you want to switch your phone and remove the features that won t work anymore, say  continue  or press 1 Otherwise, say  cancel  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1925_nm1_01">
                <prompt-segments>
                  <audiofile src="ES1925_nm1_01.wav" text="Please say  continue  or  cancel "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1925_nm2_01">
                <prompt-segments>
                  <audiofile src="ES1925_nm2_01.wav" text="If you want to switch your phone and remove the features that won t work anymore, say  continue  or press 1 Otherwise, say  cancel  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES1925_nm2_01">
                <prompt-segments>
                  <audiofile src="ES1925_nm2_01.wav" text="If you want to switch your phone and remove the features that won t work anymore, say  continue  or press 1 Otherwise, say  cancel  or press 2"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="ES1925_ini_01">
                <prompt-segments>
                  <audiofile src="ES1925_ini_01.wav" text="Say  continue , or  cancel "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="ES1925_AskContinueCancelFeature_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES1925_AskContinueCancelFeature_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'continue'">
                <prompt id="ES1925_cnf_ini_01">
                  <prompt-segments>
                    <audiofile src="ES1925_cnf_ini_01.wav" text="You want to switch your phone and remove the features that won t work"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="lastresult.interpretation.dm_root == 'cancel'">
                <prompt id="ES1925_cnf_ini_02">
                  <prompt-segments>
                    <audiofile src="ES1925_cnf_ini_02.wav" text="You want to cancel this phone switch"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="ES1925_AskContinueCancelFeature_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="ES1935_Transfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="ES1935_Transfer_SD_return"/>
    </subdialog-state>
    <dm-state id="ES2005_ApproveNewPayment_DM" type="CUST">
      <session-mapping key="aniMatch" value="GlobalVars.aniMatch" type="String"/>
      <session-mapping key="switchLinesSuccess" value="GlobalVars.switchLinesSuccess" type="String"/>
      <session-mapping key="esnChangedPlan" value="GlobalVars.esnChangedPlan" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.SubmitChangeDevice.dueImmediatlyAmount" type="String"/>
      <success>
        <action label="continue">
          <audio>
            <prompt id="ES2005_out_01">
              <prompt-segments>
                <audiofile src="ES2005_out_01.wav" text="Great!"/>
              </prompt-segments>
            </prompt>
          </audio>
          <if cond="aniMatch == true &amp;&amp; switchLinesSuccess == false">
            <audio>
              <prompt id="ES2005_out_02">
                <prompt-segments>
                  <audiofile src="ES2005_out_02.wav" text="Again, this call will drop when I transfer your service to your new phone Nothing to worry about! Here we go"/>
                </prompt-segments>
              </prompt>
            </audio>
          </if>
          <session-mapping key="GlobalVars.isOrderApproved" value="true" type="Boolean"/>
          <gotodialog next="CareESNSwap_Main_Cont#ES1510_SubmitChangeDevice_DB"/>
        </action>
        <action label="cancel">
          <audio>
            <prompt id="ES2005_out_03">
              <prompt-segments>
                <audiofile src="ES2005_out_03.wav" text="No problem, I won t change anything"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.cancelESNSwap" value="true" type="Boolean"/>
          <session-mapping key="ActivationTable.ACTIVATION_STATUS" expr="'134'"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="ES2005_ini_01" cond="esnChangedPlan == true">
                <prompt-segments>
                  <audiofile src="ES2005_ini_01.wav" text="If we switch your phone and change your plan, your payment due now will be"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_02" cond="esnChangedPlan == false">
                <prompt-segments>
                  <audiofile src="ES2005_ini_02.wav" text="If we switch your phone and update your account, your payment due now will be"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param name="runtime" value="ecmascript"/>
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_03">
                <prompt-segments>
                  <audiofile src="ES2005_ini_03.wav" text="By saying  continue , you agree that you ll need to pay right away to avoid a service interruption"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_04">
                <prompt-segments>
                  <audiofile src="ES2005_ini_04.wav" text="Should I go ahead? Say  continue  or  cancel "/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="" text="" id="ES2005_ApproveNewPayment_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="ES2005_ini_01" cond="esnChangedPlan == true">
                <prompt-segments>
                  <audiofile src="ES2005_ini_01.wav" text="If we switch your phone and change your plan, your payment due now will be"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_02" cond="esnChangedPlan == false">
                <prompt-segments>
                  <audiofile src="ES2005_ini_02.wav" text="If we switch your phone and update your account, your payment due now will be"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param name="runtime" value="ecmascript"/>
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_03">
                <prompt-segments>
                  <audiofile src="ES2005_ini_03.wav" text="By saying  continue , you agree that you ll need to pay right away to avoid a service interruption"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_04">
                <prompt-segments>
                  <audiofile src="ES2005_ini_04.wav" text="Should I go ahead? Say  continue  or  cancel "/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="ES2005_ini_01" cond="esnChangedPlan == true">
                <prompt-segments>
                  <audiofile src="ES2005_ini_01.wav" text="If we switch your phone and change your plan, your payment due now will be"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_02" cond="esnChangedPlan == false">
                <prompt-segments>
                  <audiofile src="ES2005_ini_02.wav" text="If we switch your phone and update your account, your payment due now will be"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param name="runtime" value="ecmascript"/>
                <param value="m" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_03">
                <prompt-segments>
                  <audiofile src="ES2005_ini_03.wav" text="By saying  continue , you agree that you ll need to pay right away to avoid a service interruption"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_04">
                <prompt-segments>
                  <audiofile src="ES2005_ini_04.wav" text="Should I go ahead? Say  continue  or  cancel "/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2005_nm2_01.wav" text="If you re OK with your payment due now, say  continue  and I ll switch your phone Otherwise, say  cancel  and I won t make *any* changes to your account or phone"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2005_nm2_01.wav" text="If you re OK with your payment due now, say  continue  and I ll switch your phone Otherwise, say  cancel  and I won t make *any* changes to your account or phone"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3"/>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_01" cond="esnChangedPlan == true">
                <prompt-segments>
                  <audiofile src="ES2005_ini_01.wav" text="If we switch your phone and change your plan, your payment due now will be"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_02" cond="esnChangedPlan == false">
                <prompt-segments>
                  <audiofile src="ES2005_ini_02.wav" text="If we switch your phone and update your account, your payment due now will be"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param name="runtime" value="ecmascript"/>
                <param value="m" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_03">
                <prompt-segments>
                  <audiofile src="ES2005_ini_03.wav" text="By saying  continue , you agree that you ll need to pay right away to avoid a service interruption"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_04">
                <prompt-segments>
                  <audiofile src="ES2005_ini_04.wav" text="Should I go ahead? Say  continue  or  cancel "/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2005_nm2_01.wav" text="If you re OK with your payment due now, say  continue  and I ll switch your phone Otherwise, say  cancel  and I won t make *any* changes to your account or phone"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_nm2_01">
                <prompt-segments>
                  <audiofile src="ES2005_nm2_01.wav" text="If you re OK with your payment due now, say  continue  and I ll switch your phone Otherwise, say  cancel  and I won t make *any* changes to your account or phone"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="ES2005_ini_01" cond="esnChangedPlan == true">
                <prompt-segments>
                  <audiofile src="ES2005_ini_01.wav" text="If we switch your phone and change your plan, your payment due now will be"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_02" cond="esnChangedPlan == false">
                <prompt-segments>
                  <audiofile src="ES2005_ini_02.wav" text="If we switch your phone and update your account, your payment due now will be"/>
                </prompt-segments>
              </prompt>
              <prompt type="currency" expr="dueImmediatelyAmount">
                <param name="runtime" value="ecmascript"/>
                <param value="f" name="intonation"/>
                <param value="false" name="playZeroCents"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_03">
                <prompt-segments>
                  <audiofile src="ES2005_ini_03.wav" text="By saying  continue , you agree that you ll need to pay right away to avoid a service interruption"/>
                </prompt-segments>
              </prompt>
              <prompt id="ES2005_ini_04">
                <prompt-segments>
                  <audiofile src="ES2005_ini_04.wav" text="Should I go ahead? Say  continue  or  cancel "/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="ES2005_ApproveNewPayment_DM.grxml" count="1"/>
          <dtmfgrammars filename="ES2005_ApproveNewPayment_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false" commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="ES2005_ApproveNewPayment_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
              </prompt>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  