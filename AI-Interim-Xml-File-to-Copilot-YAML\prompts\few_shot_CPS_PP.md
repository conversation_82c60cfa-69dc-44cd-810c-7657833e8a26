memorize below yaml format
- kind: SendActivity
  id: Play_Welcome_PP
  displayName: Play_Welcome_PP
  activity:
    text:
      - Thank you for using our service. Have a great day!
    speak:
      - "<audio src=\"AUDIO_LOCATION/SoundHelix-Song-1000.mp3\"> Thank you for using our service. Have a great day! </audio>"
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: Play_name_PP
      
and this as well
      
    - kind: ConditionGroup
      id: hl0020_TransferToAgent_PP
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: =If(Global.forceCallerToHangup = true, true, false)
          actions:
            - kind: SendActivity
              id: sendActivity_REPLACE_THIS
              activity:
                text:
                  - Vielen dank f&#252;r ihren Anruf.
                speak:
                  - "<audio src=\"AUDIO_LOCATION/hl0020_out_01.wav\">Vielen dank f&#252;r ihren Anruf. </audio>"

        - id: conditionItem_REPLACE_THIS
          condition: =If(And(Global.authenticated = 0,  Global.ivrOriginalEntry = "VSS"),true, false)
          actions:
            - kind: SendActivity
              id: sendActivity_REPLACE_THIS
              activity:
                text:
                  - Wir verbinden Sie jetzt mit der Kundenhotline.
                speak:
                  - "<audio src=\"AUDIO_LOCATION/hl0020_out_03.wav\"> Wir verbinden Sie jetzt mit der Kundenhotline </audio>"

        - id: conditionItem_REPLACE_THIS
          condition: =If(And(Global.applicationTag = "kuendigungMobil",Global.winback = "N", Global.comingFrom <> "SendSMS_DA"), true,false)
          actions:
            - kind: SendActivity
              id: sendActivity_REPLACE_THIS
              activity:
                text:
                  - Heute kann es leider ein wenig länger dauern Falls Sie Ihren Vertrag kündigen möchten, können Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen
                speak:
                  - "<audio src=\"AUDIO_LOCATION/hl0020_out_02.wav\">  Heute kann es leider ein wenig länger dauern Falls Sie Ihren Vertrag kündigen möchten, können Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen</audio>"

      elseActions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: =If(And(Global.applicationTag = "kuendigungInfoMobil",Global.winback = "N",Global.comingFrom <> "SendSMS_DA"), true,false)
              actions:
                - kind: SendActivity
                  id: sendActivity_REPLACE_THIS
                  activity:
                    text:
                      - Heute kann es leider ein wenig länger dauern Falls Sie Ihren Vertrag kündigen möchten, können Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen
                    speak:
                      - "<audio src=\"AUDIO_LOCATION/hl0020_out_02.wav\">  Heute kann es leider ein wenig länger dauern Falls Sie Ihren Vertrag kündigen möchten, können Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen </audio>"

    - kind: GotoAction
      id: goto_REPLACE_THIS
      actionId: CustomerAgentQueue_CS

    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: = If(And(Or(Global.applicationtag = "kuendigungMobil",Global.applicationtag = "kuendigungInfoMobil"),Or(Global.ivrCliType = "MSISDN",And(Global.ivrCliType <> "MSISDN",Global.ivrIdType = "MSISDN", Global.authenticated = "1" )), Global.winback <> "Y", Global.CorbaAccessible,Global.comingFrom <> "SendSMS"),true, false)
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.goToSMS
              value: true

            - kind: BeginDialog
              id: begin_REPLACE_THIS
              dialog: topic.SendSMS_SD_Dialog

      elseActions:
        - kind: GotoAction
          id: goto_REPLACE_THIS
          actionId: hl0030_CallTerminate_PP

prefix = topic 

also use these instruction while conversion 
1) If there is no if/else condition in input then use below instruction
* kind should always be "SendActivity"  
* replace "id" and "displayName" value with "play-state" tag id.
* replace "speak" value with "audio" tag and src should be wav file name only without any path with "AUDIO_LOCATION/"  prefix in below format and then add audiofile tag's "text" field value between the audio tag and in "text" as well like below example. 
<prompt-segments>
            <audiofile src="tcc/sv8040_out_01.wav" text="hello, how are you" />
        </prompt-segments>
output:
        text:
          - hello, how are you
        speak:
          - "<audio src=\"AUDIO_LOCATION/sv8040_out_01.mp3\">hello, how are you </audio>"

* If the "next" value of "action" tag ends with _DM, _DS, _DA, _DB or _PP, output this format:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0410_CleanUpNeeded_DS

actionId should be next id.
Replace "GotoAction" id with "goto_REPLACE_THIS".

Input:
<action next="rb0520_CollectVerificationPhrase_DM" />
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0520_CollectVerificationPhrase_DM

* If the next value ends with _Dialog, output this format:
- kind: BeginDialog
  id: begin_REPLACE_THIS
  dialog: topic.enroll_Dialog

"dialog" should be next value with prefix.
Replace "BeginDialog" id with "begin_REPLACE_THIS".

Example Input:
<action label="retry" next="rb0310_Enrollment_DM">
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0310_Enrollment_DM

Output should be generated based on the next value alone, without considering the label.
* "actionId" of "GotoAction" should be "next" id of "action" tag from input.
* if there is no "text" and only prompt id in the input. then generate it in this format and take example#4 for your reference.
    speak:
      - "<audio src=\"AUDIO_LOCATION/VP1030_ini_01.wav\"></audio>"
* if there is <if type="java"> in any input, just create below yaml like example 3
- kind: SendActivity
  id: sv3020_UsageInfo_PP
  activity:
    text:
      - custom text from java class
    speak:
      - custom text from java class

- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: sv3025_HearUsageInfoAgain_DM

  actionId should be next value.
* if there is any "session-mapping" tag within input, convert it into below format and generate some random alphanumeric value and replace id with it. Do not generate same id everytime.
actions:
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.hangupRequired
      value: true
* if there is special character # while session-mapping . put it under quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: '#'
* if there is any condition in the value of session-mapping then put it under double quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: "GlobalVars.GetTagData.tagVariables != undefined ? GlobalVars.GetTagData.tagVariables.needMDN : 'false'"
*  Replace "SetVariable" id with "setVariable_REPLACE_THIS".


2) if there is any if else condition then generate yaml like example#4 and also follow below instruction as well.
* kind should always be "ConditionGroup".
* replace first "id" and "displayName" value with "play-state" id.
* if there is "!=" sign in any condition then change it to "<>" sign.
* if there is "OR" sign in condition then change it to "||" sign.
* if there is "AND" sign in condition then change it to "&&" sign.
* if there is and "AND" or "OR" sign in the condition then add "=" in front of first Global variable only not for other variables. like below:
  condition: |
    =If(And(Global.vpEnrollStatus = "MORE_AUDIO_REQUIRED" , Global.collectionCounter < 5), Or( Global.enrollCounter < 4), true, false)
* if there is and "AND" or "OR" sign in the condition then add "Global" in front of each variable if its missing. Like:
Global.myMode = "dtmf" || Global.myMode = "DTMF"
if there is any "session-mapping" tag within input, convert it into below format and generate some random alphanumeric value and replace id with it. Do not generate same id everytime.
actions:
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.hangupRequired
      value: true
* if there is special character # while session-mapping . put it under quotes like below.
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.serviceTarget
      value: '#'
*  Replace "SetVariable" id with "setVariable_REPLACE_THIS".
* If the "next" value of "action" tag ends with _DM, _DS, _DA, _DB, _JDA or _PP, _SD, output this format:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: hl0070_SendSMS_SD
 
do not add _Dialog in the end of actionId value.

actionId should be next id.
Replace "GotoAction" id with "goto_REPLACE_THIS".

Input:
<action next="rb0520_CollectVerificationPhrase_DM" />
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0520_CollectVerificationPhrase_DM

*  Replace "ConditionGroup" id with "conditionGroup_REPLACE_THIS".
* Replace "conditions" first id with "conditionItem_REPLACE_THIS".
* follow the same above next pattern in "elseActions".
* do not create yaml for "script" tag.
* "LogCustomTelemetryEvent" id should be the "decision-state" id.
*  if there is complex condition and having more more "&&" and "||" operator then use the powerfx and use AND and OR and convert it into powerfx like below format.
example : 
input:
cond="('kuendigungMobil'==applicationtag || 'kuendigungInfoMobil'==applicationtag) &amp;&amp; ('MSISDN' == ivrCliType || ( 'MSISDN' != ivrCliType &amp;&amp; 'MSISDN' == ivrIdType &amp;&amp; '1' == authenticated ))&amp;&amp; 'Y' != winback &amp;&amp; CorbaAccessible  &amp;&amp; 'SendSMS' != comingFrom"
output:
condition: = If(And(Or(Global.applicationtag = "kuendigungMobil",Global.applicationtag = "kuendigungInfoMobil"),Or(Global.ivrCliType = "MSISDN",And(Global.ivrCliType <> "MSISDN",Global.ivrIdType = "MSISDN", Global.authenticated = "1" )), Global.winback <> "Y", Global.CorbaAccessible,Global.comingFrom <> "SendSMS"),true, false)
* do not create any yaml for "<event>" tag. like below.
<event name="error" next="hl0020_TransferToAgent_PP">
      <session-mapping key="goToAgent" value="true" />
</event>

* each  "elseActions" should be indented with its "conditions" like below examples.
- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(Global.returnCode = "0", true, false)
      actions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: | 
                =If(Global.sessionData.employerGroupData.isAgentOfRecord(), true, false)
              actions:
                - kind: ConditionGroup
                  id: conditionGroup_REPLACE_THIS
                  conditions:
                    - id: conditionItem_REPLACE_THIS
                      condition: |
                        =If(Global.sessionData.isAuthenticated(), true, false)
                      actions:
                        - kind: GotoAction
                          id: goto_REPLACE_THIS
                          actionId: au17320_CheckIntentReqMemLevelAuth_DS
                  elseActions:
                    - kind: ConditionGroup
                      id: conditionGroup_REPLACE_THIS
                      conditions:
                        - id: conditionItem_REPLACE_THIS
                          condition: |
                            =If(Global.sessionData.IDAuthData.billingProfileIDEntryAttempts = 0, true, false)
                          actions:
                            - kind: GotoAction
                              id: goto_REPLACE_THIS
                              actionId: au17300_ConfirmBillingID_DM
                      elseActions:
                        - kind: GotoAction
                          id: goto_REPLACE_THIS
                          actionId: ma01100_InitTransfer_DS
          elseActions:
            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: ma01100_InitTransfer_DS
  elseActions:
    - kind: GotoAction
      id: goto_REPLACE_THIS
      actionId: ma01100_InitTransfer_DS
* if there is any condition with empty string like below. convert that single quotes to double quotes.
<if cond="hangupRequired == 'false' &amp;&amp; goToSMS == 'false' &amp;&amp; goToOtherService == 'false' &amp;&amp; (serviceTarget == 'undefined' || serviceTarget == '' || serviceTarget == '#')">
output:

condition: |-
  =If(AND(Global.hangupRequired = false , goToSMS == 'false', Global.goToOtherService = false , Or(Global.serviceTarget = "undefined" , Global.serviceTarget = "" , Global.serviceTarget = "#")), true, false)

 * If there is any integer value in condition, just remove the quotes from them and make it integer like below example:
input:
<if cond="authenticated == '0' &amp;&amp; authenticationRequired == 'true'">
output:
condition: |-
  =If(And(Global.authenticated == 0 , Global.authenticationRequired == true),true, false) 

13) indentation for next yaml should be same as first one or as memorized yaml
14) do not provide duplicate yaml.. generate only for provided inputs.

### Example 1:
**Input XML:**
```xml
<play-state id="Play_Welcome_PP">
<script className="com.nuance.ps.telefonica.scripts.KPIAddNode" />
			<audio>
				<prompt id="play_welcome_01">
					<prompt-segments>
						<audiofile src="play_welcome_01.wav" text="Hello, Welcome to our AI Generated Voice App." />
					</prompt-segments>
				</prompt>
			</audio>
			<action next="Play_name_PP">
				<session-mapping key="IVR_SMSsent" value="true" />
			</action>
</play-state>

```

**Output yaml:**
```yaml
- kind: SendActivity
  id: Play_Welcome_PP
  displayName: Play_Welcome_PP
  activity:
    text:
      - Hello, Welcome to our AI Generated Voice App.
    speak:
      - "<audio src=\"AUDIO_LOCATION/play_welcome_01.wav\">Hello, Welcome to our AI Generated Voice App.</audio>"

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.IVR_SMSsent
  value: true

- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: Play_name_PP
```

### Example 2:
**Input XML:**
```xml
<play-state id="PlayForPune_PP">
			<audio>
				<if type="java">
					<prompt id="pune_pp">
						<prompt-segments>
							<audiofile src="pune_pp.wav" text="You have chosen Pune" />
						</prompt-segments>
					</prompt>
				</if>
			</audio>
			<action label="call_end_SD" next="EndofConversation" />
</play-state>
```

**Output yaml:**
```yaml
- kind: SendActivity
  id: PlayForPune_PP
  displayName: PlayForPune_PP
  activity:
    text: 
      - You have chosen Pune.
    speak:
      - "<audio src=\"AUDIO_LOCATION/pune_pp.wav\">You have chosen Pune.</audio>"

- kind: BeginDialog
  id: begin_REPLACE_THIS
  dialog: topic.EndofConversation
```
### Example 3:
**Input XML:**
```xml
    <play-state id="sv3020_UsageInfo_PP">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode" />
			<audio><prompt type="custom" bargein="false">
  			<param name="className" value="com.nuance.ps.telefonica.prompts.UsageInfoPrompt" /></prompt></audio><action label="default" next="sv3025_HearUsageInfoAgain_DM">
				<script className="com.nuance.ps.telefonica.scripts.ReportMobileUsageInfo">
					<param name="STATUS" value="1" />
				</script>
        	</action>
		</play-state>
```  
**Output yaml:**
```yaml
- kind: SendActivity
  id: sv3020_UsageInfo_PP
  displayName: sv3020_UsageInfo_PP
  activity:
    text:
      - custom text from java class
    speak:
      - custom text from java class

- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: sv3025_HearUsageInfoAgain_DM

```
### Example 4:
**Input XML:**
```xml

  <play-state id="hl0020_TransferToAgent_PP">
      <script className="com.nuance.ps.telefonica.scripts.KPIAddNode" />
      <script className="com.nuance.ps.telefonica.scripts.SetRoutingValues" />
      <audio>
        <if cond="forceCallerToHangup == 'true'">
          <prompt id="silence_3500ms">
            <prompt-segments>
              <audiofile src="silence_3500ms.wav" text="" />
            </prompt-segments>
          </prompt>
          <prompt id="hl0020_out_01">
            <prompt-segments>
              <audiofile src="hl0020_out_01.wav" text="Vielen dank f&#252;r ihren Anruf" />
            </prompt-segments>
          </prompt>
          <prompt id="silence_6000ms">
            <prompt-segments>
              <audiofile src="silence_6000ms.wav" text="" />
            </prompt-segments>
          </prompt>
        </if>
        <if cond="authenticated == '0' &amp;&amp; ivrOriginalEntry == 'VSS'">
          <prompt id="hl0020_out_03">
            <prompt-segments>
              <audiofile src="hl0020_out_03.wav" text="Wir verbinden Sie jetzt mit der Kundenhotline" />
            </prompt-segments>
          </prompt>
        </if>
        <if cond="applicationtag == 'kuendigungMobil' &amp;&amp; winback == 'N' &amp;&amp; comingFrom != 'SendSMS_DA'">
          <prompt id="hl0020_out_02">
            <prompt-segments>
              <audiofile src="hl0020_out_02.wav" text="Heute kann es leider ein wenig l&#228;nger dauern Falls Sie Ihren Vertrag k&#252;ndigen m&#246;chten, k&#246;nnen Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen" />
            </prompt-segments>
          </prompt>
          <elseif cond="applicationtag == 'kuendigungInfoMobil' &amp;&amp; winback == 'N' &amp;&amp; comingFrom != 'SendSMS_DA'">
            <prompt id="hl0020_out_02">
              <prompt-segments>
                <audiofile src="hl0020_out_02.wav" text="Heute kann es leider ein wenig l&#228;nger dauern Falls Sie Ihren Vertrag k&#252;ndigen m&#246;chten, k&#246;nnen Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen" />
              </prompt-segments>
            </prompt>
          </elseif>
        </if>
      </audio>
      <action next="CustomerAgentQueue_CS">
        <if cond="('kuendigungMobil'==applicationtag || 'kuendigungInfoMobil'==applicationtag)                 &amp;&amp; ('MSISDN' == ivrCliType || ( 'MSISDN' != ivrCliType &amp;&amp; 'MSISDN' == ivrIdType &amp;&amp; '1' == authenticated ))                 &amp;&amp; 'Y' != winback                &amp;&amp; CorbaAccessible                &amp;&amp; 'SendSMS' != comingFrom">
          <session-mapping key="goToSMS" value="true" />
          <action next="hl0070_SendSMS_SD" />
          <else>
            <action next="CustomerAgentQueue_CS" />
          </else>
        </if>
      </action>
    </play-state>

```  
**Output yaml:**
```yaml

    - kind: ConditionGroup
      id: hl0020_TransferToAgent_PP
      displayName: hl0020_TransferToAgent_PP
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: =If(Global.forceCallerToHangup = true, true, false)
          actions:
            - kind: SendActivity
              id: sendActivity_REPLACE_THIS
              activity:
                text:
                  - Vielen dank f&#252;r ihren Anruf 
                speak:
                  - "<audio src=\"AUDIO_LOCATION/hl0020_out_01.wav\"> Vielen dank f&#252;r ihren Anruf </audio>"


        - id: conditionItem_REPLACE_THIS
          condition: =If(And(Global.authenticated = 0,  Global.ivrOriginalEntry = "VSS"),true, false)
          actions:
            - kind: SendActivity
              id: sendActivity_REPLACE_THIS
              activity:
                text:
                  - Wir verbinden Sie jetzt mit der Kundenhotline
                speak:
                  - "<audio src=\"AUDIO_LOCATION/hl0020_out_03.wav\"> Wir verbinden Sie jetzt mit der Kundenhotline </audio>"


        - id: conditionItem_REPLACE_THIS
          condition: =If(And(Global.applicationTag = "kuendigungMobil",Global.winback = "N", Global.comingFrom <> "SendSMS_DA"), true,false)
          actions:
            - kind: SendActivity
              id: sendActivity_REPLACE_THIS
              activity:
                text:
                  - Heute kann es leider ein wenig länger dauern Falls Sie Ihren Vertrag kündigen möchten, können Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen
                speak:
                  - "<audio src=\"AUDIO_LOCATION/hl0020_out_02.wav\">  Heute kann es leider ein wenig länger dauern Falls Sie Ihren Vertrag kündigen möchten, können Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen</audio>"

      elseActions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: =If(And(Global.applicationTag = "kuendigungInfoMobil",Global.winback = "N",Global.comingFrom <> "SendSMS_DA"), true,false)
              actions:
                - kind: SendActivity
                  id: sendActivity_REPLACE_THIS
                  activity:
                    text:
                      - Heute kann es leider ein wenig länger dauern Falls Sie Ihren Vertrag kündigen möchten, können Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen
                    speak:
                      - "<audio src=\"AUDIO_LOCATION/hl0020_out_02.wav\">  Heute kann es leider ein wenig länger dauern Falls Sie Ihren Vertrag kündigen möchten, können Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2de/goto/kuendigen Ich wiederhole o2de/goto/kuendigen </audio>"

    - kind: GotoAction
      id: goto_REPLACE_THIS
      actionId: CustomerAgentQueue_CS

    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: = If(And(Or(Global.applicationtag = "kuendigungMobil",Global.applicationtag = "kuendigungInfoMobil"),Or(Global.ivrCliType = "MSISDN",And(Global.ivrCliType <> "MSISDN",Global.ivrIdType = "MSISDN", Global.authenticated = "1" )), Global.winback <> "Y", Global.CorbaAccessible,Global.comingFrom <> "SendSMS"),true, false)
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.goToSMS
              value: true

            - kind: BeginDialog
              id: begin_REPLACE_THIS
              dialog: topic.SendSMS_SD_Dialog

      elseActions:
        - kind: GotoAction
          id: goto_REPLACE_THIS
          actionId: hl0030_CallTerminate_PP
```  
### Example 2:
**Input XML:**
```xml
<play-state id="VP1020_PlayResetPasswordSuccess_PP">
			<audio>
				<prompt id="VP1020_ini_01" />
				<prompt id="VP1020_ini_02" />
				<prompt id="silence_1000ms" />
			</audio>
			<action next="VP1035_GoToAnythingElse_SD" />
		</play-state>

```  
**Output yaml:**
```yaml

- kind: SendActivity
  id: VP1020_PlayResetPasswordSuccess_PP
  displayName: VP1020_PlayResetPasswordSuccess_PP
  activity:
    speak:
      - "<audio src=\"AUDIO_LOCATION/VP1020_ini_01.wav\"></audio>"
      - "<audio src=\"AUDIO_LOCATION/VP1020_ini_02.wav\"></audio>"
      - "<audio src=\"AUDIO_LOCATION/silence_1000ms.wav\"></audio>"
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: VP1035_GoToAnythingElse_SD