<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="DataTopUp_Main_Dialog">
    <decision-state id="DT1001_CheckContext_DS">
      <session-mapping key="GlobalVars.paymentAmount" expr="0.00"/>
      <if cond="(GlobalVars.GetDataUsageInfo == undefined || GlobalVars.GetDataUsageInfo == '')">
        <action next="DT1002_GetDataUsage_DB_DA"/>
        <else>
          <action next="DT1005_DataTopUpTerms_PP"/>
        </else>
      </if>
    </decision-state>

    <data-access-state id="DT1002_GetDataUsage_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.mdn:''" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.payDate:''" type="String"/>
      <session-mapping key="billCycleLength" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.billCycleLength:0" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="isBARTET" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.isBARTET:false" type="String"/>
      <session-mapping key="isHint" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.parentDeviceType == 'INT')? true : false" type="String"/>
      <session-mapping key="isSuspended" value="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false" type="String"/>
      <session-mapping key="getTopUpHistory" value="isHint == true ? false : true" type="String"/>
      <data-access id="GetDataUsage" classname="com.nuance.metro.dataaccess.GetDataUsage">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="getTopUpHistory"/>
          <input-variable name="payDate"/>
          <input-variable name="sessionId"/>
          <input-variable name="isSuspended"/>
          <input-variable name="isBARTET"/>
        </inputs>
        <outputs>
          <output-variable name="dataFound"/>
          <output-variable name="dataUsed"/>
          <output-variable name="dataCap"/>
          <output-variable name="isUnlimited"/>
          <output-variable name="dataPercentage"/>
          <output-variable name="dataUsage_dataFound"/>
          <output-variable name="dataUsage_dataUsed"/>
          <output-variable name="dataUsage_dataCap"/>
          <output-variable name="dataUsage_dataUsed_KB"/>
          <output-variable name="dataUsage_dataCap_KB"/>
          <output-variable name="dataUsage_isUnlimited"/>
          <output-variable name="dataUsage_dataPercentage"/>
          <output-variable name="topUpHotSpot_dataFound"/>
          <output-variable name="topUpHotSpot_dataUsed"/>
          <output-variable name="topUpHotSpot_dataCap"/>
          <output-variable name="topUpHotSpot_dataUsed_KB"/>
          <output-variable name="topUpHotSpot_dataCap_KB"/>
          <output-variable name="topUpHotSpot_isUnlimited"/>
          <output-variable name="topUpHotSpot_dataPercentage"/>
          <output-variable name="topUp_feature1_code"/>
          <output-variable name="topUp_feature1_featureName_en"/>
          <output-variable name="topUp_feature1_featureName_es"/>
          <output-variable name="topUp_feature1_description_en"/>
          <output-variable name="topUp_feature1_description_es"/>
          <output-variable name="topUp_feature1_price"/>
          <output-variable name="numPaidTopUps"/>
          <output-variable name="numGoodWillTopUps "/>
          <output-variable name="featureDetailsPromptURL_en"/>
          <output-variable name="featureDetailsPromptURL_es "/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetDataUsage.status == 'Success'">
          <session-mapping key="GlobalVars.GetDataUsageInfo" expr="GetDataUsage"/>
          <session-mapping key="dataUsage_isUnlimited" expr="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.dataUsage_isUnlimited:false"/>
          <session-mapping key="remainingDays" expr="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.remainingDays:0"/>
          <action next="DT1005_DataTopUpTerms_PP"/>
        </if>
        <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
        <gotodialog next="CallTransfer_Main_Dialog"/>
      </action>
      </data-access-state>

    <play-state id="DT1005_DataTopUpTerms_PP">
      <session-mapping key="topUp_feature1_code" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUp_feature1_code:''" type="String"/>
      <session-mapping key="featureDetailsPromptURL_en" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.featureDetailsPromptURL_en:''" type="String"/>
      <session-mapping key="featureDetailsPromptURL_es" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.featureDetailsPromptURL_es:''" type="String"/>
      <session-mapping key="topUp_feature1_description_en" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUp_feature1_description_en:''" type="String"/>
      <session-mapping key="topUp_feature1_description_es" value="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUp_feature1_description_es:''" type="String"/>
      <if cond="topUp_feature1_code == null || topUp_feature1_code == ''">
        <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
        <action next="DT1035_Transfer_SD"/>
      </if>
      <audio>
        <prompt type="custom">
          <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.CustomPlayTopUpFeature"/>
          <param name="featureDetailsPromptURL_en" value="featureDetailsPromptURL_en" scope="request"/>
          <param name="featureDetailsPromptURL_es" value="featureDetailsPromptURL_es" scope="request"/>
          <param name="topUp_feature1_description_en" value="topUp_feature1_description_en" scope="request"/>
          <param name="topUp_feature1_description_es" value="topUp_feature1_description_es" scope="request"/>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DT1005_out_01">
          <prompt-segments>
            <audiofile text="You'll need to make your payment right away so your service isn't suspended " src="DT1005_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_250ms">
          <prompt-segments>
            <audiofile text="test" src="silence_250ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="DT1005_out_02">
          <prompt-segments>
            <audiofile text="And remember that the top-up will expire on your next due date That means any data you have left over at the end will be lost" src="DT1005_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <if cond="(remainingDays &lt;= 7)">
          <prompt id="DT1005_out_03">
            <prompt-segments>
              <audiofile text="I see your due date is coming up So make sure you use all the data before then!" src="DT1005_out_03.wav"/>
            </prompt-segments>
          </prompt>
          <else>
            <prompt id="DT1005_out_04">
              <prompt-segments>
                <audiofile text="So make sure you use all the data before your next due date!" src="DT1005_out_04.wav"/>
              </prompt-segments>
            </prompt>
          </else>
        </if>
        <if cond="(dataUsage_isUnlimited == false)">
          <prompt id="silence_500ms">
            <prompt-segments>
              <audiofile text="test" src="silence_500ms.wav"/>
            </prompt-segments>
          </prompt>
          <prompt id="DT1005_out_05">
            <prompt-segments>
              <audiofile text="Also, don't forget to check out our other montly plans for even more included data! " src="DT1005_out_05.wav"/>
            </prompt-segments>
          </prompt>
        </if>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="DT1010_AcceptTermsYN_DM"/>
    </play-state>

    <dm-state id="DT1010_AcceptTermsYN_DM" type="CUST">
      <session-mapping key="collection_grammar1" value="'DT1010_AcceptTermsYN_DM.jsp?stateVar='+dataUsage_isUnlimited" type="String"/>
      <session-mapping key="collection_dtmfgrammar1" value="'DT1010_AcceptTermsYN_DM_dtmf.jsp?stateVar='+dataUsage_isUnlimited" type="String"/>
      <success>
        <action label="yes" next="DT1015_AddTopUpData_DB_DA">
          <session-mapping key="GlobalVars.featureCode" expr="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUp_feature1_code:''"/>
        </action>
        <action label="repeat" next="DT1005_DataTopUpTerms_PP"/>
        <action label="changeplan" next="return">
          <session-mapping key="GlobalVars.callType" expr="'change_plan'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
        </action>
        <action label="no" next="DT1026_CheckFromNLU_JDA"/>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="(dataUsage_isUnlimited == true)">
                <prompt id="DT1010_ini_01">
                  <prompt-segments>
                    <audiofile text="Are you ready to go ahead? You can also say 'repeat that' " src="DT1010_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DT1010_ini_02">
                    <prompt-segments>
                      <audiofile text="Are you ready to go ahead? You can also say 'repeat that', or 'change my plan' " src="DT1010_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <helpprompts count="1" bargein="true" filename="" text="" id="DT1010_AcceptTermsYN_DM_initial"/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="(dataUsage_isUnlimited == true)">
                <prompt id="DT1010_ini_01">
                  <prompt-segments>
                    <audiofile text="Are you ready to go ahead? You can also say 'repeat that' " src="DT1010_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DT1010_ini_02">
                    <prompt-segments>
                      <audiofile text="Are you ready to go ahead? You can also say 'repeat that', or 'change my plan' " src="DT1010_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(dataUsage_isUnlimited == true)">
                <prompt id="DT1010_ini_01">
                  <prompt-segments>
                    <audiofile text="Are you ready to go ahead? You can also say 'repeat that' " src="DT1010_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DT1010_ini_02">
                    <prompt-segments>
                      <audiofile text="Are you ready to go ahead? You can also say 'repeat that', or 'change my plan' " src="DT1010_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(dataUsage_isUnlimited == true)">
                <prompt id="DT1010_nm2_01">
                  <prompt-segments>
                    <audiofile text="Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 If you don't want to do anything just now, press 3 " src="DT1010_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DT1010_nm2_02">
                    <prompt-segments>
                      <audiofile text="Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 To check out or other data plans, say 'change my plan' or press 3 If you don't want to do anything just now, press 4 " src="DT1010_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(dataUsage_isUnlimited == true)">
                <prompt id="DT1010_nm2_01">
                  <prompt-segments>
                    <audiofile text="Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 If you don't want to do anything just now, press 3 " src="DT1010_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DT1010_nm2_02">
                    <prompt-segments>
                      <audiofile text="Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 To check out or other data plans, say 'change my plan' or press 3 If you don't want to do anything just now, press 4 " src="DT1010_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(dataUsage_isUnlimited == true)">
                <prompt id="DT1010_ini_01">
                  <prompt-segments>
                    <audiofile text="Are you ready to go ahead? You can also say 'repeat that' " src="DT1010_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DT1010_ini_02">
                    <prompt-segments>
                      <audiofile text="Are you ready to go ahead? You can also say 'repeat that', or 'change my plan' " src="DT1010_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(dataUsage_isUnlimited == true)">
                <prompt id="DT1010_nm2_01">
                  <prompt-segments>
                    <audiofile text="Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 If you don't want to do anything just now, press 3 " src="DT1010_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DT1010_nm2_02">
                    <prompt-segments>
                      <audiofile text="Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 To check out or other data plans, say 'change my plan' or press 3 If you don't want to do anything just now, press 4 " src="DT1010_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="(dataUsage_isUnlimited == true)">
                <prompt id="DT1010_nm2_01">
                  <prompt-segments>
                    <audiofile text="Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 If you don't want to do anything just now, press 3 " src="DT1010_nm2_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DT1010_nm2_02">
                    <prompt-segments>
                      <audiofile text="Are you ready to add a top-up? To continue, say 'yes' or press 1 To hear the information about top-ups again, say 'repeat' or press 2 To check out or other data plans, say 'change my plan' or press 3 If you don't want to do anything just now, press 4 " src="DT1010_nm2_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="(dataUsage_isUnlimited == true)">
                <prompt id="DT1010_ini_01">
                  <prompt-segments>
                    <audiofile text="Are you ready to go ahead? You can also say 'repeat that' " src="DT1010_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="DT1010_ini_02">
                    <prompt-segments>
                      <audiofile text="Are you ready to go ahead? You can also say 'repeat that', or 'change my plan' " src="DT1010_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoRepeat.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="DT1010_AcceptTermsYN_DM.jsp" count="1"/>
          <dtmfgrammars filename="DT1010_AcceptTermsYN_DM_dtmf.jsp" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="DT1010_AcceptTermsYN_DM_confirmation_initial"/>
          <repeatprompts count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                  <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                </prompt>
              </if>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_09">
                    <prompt-segments>
                      <audiofile text="Is that correct?" src="gl_cnf_ini_09.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_10">
                    <prompt-segments>
                      <audiofile text="Is that right?" src="gl_cnf_ini_10.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="DT1015_AddTopUpData_DB_DA">
      <session-mapping key="mdn" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.mdn:''" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="featureCode" value="GlobalVars.featureCode" type="String"/>
      <session-mapping key="JWTToken" value="GlobalVars.Authenticate.JWTToken" type="String"/>
      <data-access id="AddTopUpData" classname="com.nuance.metro.dataaccess.AddTopUpData">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="mdn"/>
          <input-variable name="featureCode"/>
          <input-variable name="pin" mask="true"/>
          <input-variable name="sessionId"/>
          <input-variable name="JWTToken"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="AddTopUpData &amp;&amp; AddTopUpData.status == 'Success'">
          <action next="DT1020_PayNowYN_DM"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <gotodialog next="CallTransfer_Main_Dialog"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <dm-state id="DT1020_PayNowYN_DM" type="CUST">
      <success>
        <action label="yes">
          <session-mapping key="GlobalVars.callType" expr="'make_pmt'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.acceptPayByPhone" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.dataTopUpPayment" value="true" type="Boolean"/>
          <session-mapping key="GlobalVars.paymentAmount" expr="GlobalVars.GetDataUsageInfo?GlobalVars.GetDataUsageInfo.topUp_feature1_price:0.0"/>
          <action next="DT1025_MakePayment_SD"/>
        </action>
        <action label="no">
          <action next="DT1026_CheckFromNLU_JDA"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="DT1020_ini_01">
                <prompt-segments>
                  <audiofile text="Alright! I added your top-up! " src="DT1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DT1020_ini_02">
                <prompt-segments>
                  <audiofile text="Are you ready to pay now? " src="DT1020_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <helpprompts count="1" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="DT1020_ini_01">
                <prompt-segments>
                  <audiofile text="Alright! I added your top-up! " src="DT1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DT1020_ini_02">
                <prompt-segments>
                  <audiofile text="Are you ready to pay now? " src="DT1020_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="DT1020_ni1_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay over the phone *now*? " src="DT1020_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_ni2_01">
                <prompt-segments>
                  <audiofile text="I didn't hear you " src="gl_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DT1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to pay by phone now, say 'yes' or press 1 Otherwise, say 'no' or press 2 " src="DT1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_ni3_01">
                <prompt-segments>
                  <audiofile text="I still didn't hear" src="gl_ni3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DT1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to pay by phone now, say 'yes' or press 1 Otherwise, say 'no' or press 2 " src="DT1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DT1020_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like to pay over the phone *now*? " src="DT1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DT1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to pay by phone now, say 'yes' or press 1 Otherwise, say 'no' or press 2 " src="DT1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DT1020_nm2_01">
                <prompt-segments>
                  <audiofile text="If you'd like to pay by phone now, say 'yes' or press 1 Otherwise, say 'no' or press 2 " src="DT1020_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="DT1020_ini_01">
                <prompt-segments>
                  <audiofile text="Alright! I added your top-up! " src="DT1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="DT1020_ini_02">
                <prompt-segments>
                  <audiofile text="Are you ready to pay now? " src="DT1020_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands.grxml" dtmfcommandgrammar="GlobalCommands_dtmf.grxml">
          <grammars filename="DT1020_PayNowYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="DT1020_PayNowYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <failureprompt count="1"/>
        <successprompts count="1">
          <audio>
            <if cond="(collect.value.dm_root == 'no')">
              <prompt id="DT1020_out_01">
                <prompt-segments>
                  <audiofile text="Okay You can also pay online, or with the myMetro app Don't forget to do it right away, or your service will be suspended " src="DT1020_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
            </if>
          </audio>
        </successprompts>
        <successprompts count="2">
          <audio>
            <if cond="(collect.value.dm_root == 'no')">
              <prompt id="DT1020_out_01">
                <prompt-segments>
                  <audiofile text="Okay You can also pay online, or with the myMetro app Don't forget to do it right away, or your service will be suspended " src="DT1020_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
            </if>
          </audio>
        </successprompts>
        <successprompts count="3">
          <audio>
            <if cond="(collect.value.dm_root == 'no')">
              <prompt id="DT1020_out_01">
                <prompt-segments>
                  <audiofile text="Okay You can also pay online, or with the myMetro app Don't forget to do it right away, or your service will be suspended " src="DT1020_out_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_1000ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_1000ms.wav"/>
                </prompt-segments>
              </prompt>
            </if>
          </audio>
        </successprompts>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.500" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.500">
        <grammar_configuration>
    </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="DT1025_MakePayment_SD">
      <gotodialog next="MakePayment_Main_Dialog"/>
      <action next="DT1025_MakePayment_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DT1025_MakePayment_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <decision-state id="DT1026_CheckFromNLU_DS">
      <if cond="(GlobalVars.tag == 'buy-data_topup' &amp;&amp; GlobalVars.GetBCSParameters.care_nlu_enabled == 'true' &amp;&amp; language == 'en-US')">
        <action next="getReturnLink()"/>
        <else>
          <action next="DT1030_AnythingElse_SD"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="DT1030_AnythingElse_SD">
      <gotodialog next="AnythingElse_Main_Dialog"/>
      <action next="DT1030_AnythingElse_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DT1030_AnythingElse_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

    <subdialog-state id="DT1035_Transfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
      <action next="DT1035_Transfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="DT1035_Transfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

  </dialog>
  