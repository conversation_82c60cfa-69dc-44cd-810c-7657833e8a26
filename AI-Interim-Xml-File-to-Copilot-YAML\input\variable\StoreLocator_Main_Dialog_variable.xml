<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="location" value="empty" type="String"/>
  <session-mapping key="filterBy" value="empty" type="String"/>
  <session-mapping key="storeLocReason" value="unknown" type="string"/>
  <session-mapping key="storeLocatorReason" value="invalidEquipment" type="string"/>
  <session-mapping key="firstSearch" value="true" type="boolean"/>
  <session-mapping key="searchCount" value="1" type="integer"/>
  <session-mapping key="GlobalVars.storeLocatorReason" value="invalidequipment" type="string"/>
  <session-mapping key="locations.length" value="0" type="integer"/>
  <session-mapping key="length" value="1" type="integer"/>
  <session-mapping key="playResultsPreamble" value="true" type="string"/>
  <session-mapping key="curStoreIndex" value="0" type="integer"/>
  <session-mapping key="5" value="4" type="string"/>
  <session-mapping key="isPreviousAllowed" value="true" type="boolean"/>
  <session-mapping key="isNextAllowed" value="true" type="boolean"/>
  <session-mapping key="isMoreAllowed" value="true" type="boolean"/>
  <session-mapping key="isFirstRepeatCnt" value="false" type="boolean"/>
  <session-mapping key="searchStatus" value="start" type="string"/>
  <session-mapping key="heardNavigationInstr" value="true" type="boolean"/>
  <session-mapping key="isRepeat" value="false" type="boolean"/>
  <session-mapping key="isPrevious" value="false" type="boolean"/>
  <session-mapping key="cust_store_type" value="MetroSt" type="string"/>
</session-mappings>
