import os
import xml.etree.ElementTree as ET
import logging
from lxml import etree as ET
from util import delete_existing_files

logging.basicConfig(filename='agent.log', level=logging.INFO, format='%(asctime)s - %(message)s')

## This method creating dialog files from project xml 
def separate_dialog_files(input_file, output_dir):

   # Parse the XML file
    try:
        tree = ET.parse(input_file)
        root = tree.getroot()
    except ET.ParseError as e:
        logging.error(f"Error parsing XML file: {e}")
        return

    # Iterate through each dialog in the XML file
    dialogs_found = False
    for dialog in root.findall('.//dialog'):
        dialogs_found = True

        # Get the dialog ID to use as the filename
        dialog_id = dialog.get('id')
        if dialog_id is None:
            logging.info("Dialog ID not found, skipping dialog.")
            continue

        file_name = f"{dialog_id}.xml"

        # Create an ElementTree for the single dialog
        dialog_tree = ET.ElementTree(dialog)

        # Write the dialog to its own file
        output_path = os.path.join(output_dir, file_name)
        try:
            dialog_tree.write(output_path, encoding='utf-8', xml_declaration=True)
            logging.info(f"Created file: {output_path}")
        except Exception as e:
            logging.error(f"Error writing file {output_path}: {e}")

    if not dialogs_found:
        logging.info("No dialogs found in the XML file.")
    else:
        logging.info("Separate files created for each dialog.")

## This method is creating custom-state files from project xml 
def separate_custom_files(input_file, output_dir):

    # Parse the XML file
    try:
        tree = ET.parse(input_file)
        root = tree.getroot()
    except ET.XMLSyntaxError  as e:
        logging.error(f"Error parsing XML file: {e}")
        return
    
    #calling extract dvxml view method
    extract_dvxml_views(root)

    # Iterate through each custom_state in the XML file
    custom_state_found = False
    for custom_state in root.xpath('.//custom-state[not(ancestor::dialog)]'):
        custom_state_found = True

        # Get the custom_state ID to use as the filename
        custom_state_id = custom_state.get('id')
        if custom_state_id is None:
            logging.info("custom-state ID not found, skipping custom_state.")
            continue

        file_name = f"{custom_state_id}.xml"

        # Create an ElementTree for the single custom_state
        custom_state_tree = ET.ElementTree(custom_state)

        # Write the custom_state to its own file
        output_path = os.path.join(output_dir, file_name)
        try:
            custom_state_tree.write(output_path, encoding='utf-8', xml_declaration=True)
            logging.info(f"Created file: {output_path}")
        except Exception as e:
            logging.error(f"Error writing file {output_path}: {e}")

    if not custom_state_found:
        logging.info("No custom_state found in the XML file.")
    else:
        logging.info("Separate files created for each custom_state.")

## This method is creating dvxml files from project xml 
def extract_dvxml_views(root):

    # Collect all "view" elements with ".dvxml" in the name
    for view in root.xpath("//view[contains(@name, '.dvxml')]"):
        name = view.get('name')
        if name and name.endswith(".dvxml"):
            # Prepare the filename
            file_name = name.strip('/').replace('.dvxml', '_dvxml.xml')

            # Replace 'view' tag with 'dvxml'
            view.tag = 'dvxml'
            # Replace the 'name' attribute with 'id'
            #view.attrib['id'] = view.attrib.pop('name').lstrip('/')
            view.attrib['id'] = name.strip('/').replace('.dvxml', '_dvxml')

            # Remove all namespaces from the element
            for elem in view.iter():
                elem.tag = ET.QName(elem).localname
                elem.attrib.pop('xmlns:xsi', None)  # Ensure xsi namespace is removed if present

            # Create a new XML structure for the view
            view_tree = ET.ElementTree(view)

            # Write the view tag to a separate XML file
            view_tree.write(os.path.join('input/dvxml_file/', file_name),pretty_print=True, xml_declaration=True, encoding='utf-8')



# Paths for input, output and markdown files
input_xml_path = 'input/cleaned/updated-state-engine.xml'
dialog_output_dir = 'input/dialogs_file/'
custom_output_dir = 'input/custom_file/'
prompts_folder_path = 'prompts/'
updated_prompts_folder_path = 'prompts/updated/'


delete_existing_files(dialog_output_dir)
delete_existing_files(custom_output_dir)
delete_existing_files('input/dvxml_file/')
separate_dialog_files(input_xml_path, dialog_output_dir)
separate_custom_files(input_xml_path,custom_output_dir)


# Updating prompt scripts with the new XML file names
# markdown_files = [file for file in os.listdir(prompts_folder_path) if file.endswith('.md')]
# for markdown_file in markdown_files:
#     markdown_file_path = os.path.join(prompts_folder_path, markdown_file)
#     #updated_file = update_script_markdown_with_folder(dialog_output_dir, markdown_file_path, updated_prompts_folder_path)
#     #print(f"Updated script saved to: {updated_file}")

## This method is updating prompt files with dialogs names
# def update_script_markdown_with_folder(folder_path, markdown_file_path, updated_folder_path):
#     # Ensure the updated folder exists
#     os.makedirs(updated_folder_path, exist_ok=True)

#     # Get list of XML file names without extension
#     xml_file_names = [os.path.splitext(file)[0] for file in os.listdir(folder_path) if file.endswith('.xml')]
    
#     # Create a comma-separated string of file names
#     user_input = ", ".join(xml_file_names)
    
#     # Read the original markdown file
#     with open(markdown_file_path, 'r') as file:
#         script_content = file.read()

#     # Replace the placeholder with the XML file names
#     updated_content = script_content.replace("{user_input}", user_input)
    
#     # Save the updated script to the updated folder
#     updated_file_name = os.path.basename(markdown_file_path).replace(".md", "_updated.md")
#     updated_file_path = os.path.join(updated_folder_path, updated_file_name)
#     with open(updated_file_path, 'w') as updated_file:
#         updated_file.write(updated_content)

#     return updated_file_path