<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="ErrorHandlingPayment_Dialog">
    <decision-state id="EH1001_CheckContext_DS">
      <action next="EH1005_FindStoreYN_DM"/>
    </decision-state>

    <dm-state id="EH1005_FindStoreYN_DM" type="CUST">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <session-mapping key="collection_grammar1" value="EH1005_FindStoreYN_DM.grxml" type="String"/>
      <session-mapping key="tryOtherCardReason" value="GlobalVars.tryOtherCardReason" type="String"/>
      <session-mapping key="preferredPaymentMethod" value="GlobalVars.preferredPaymentMethod" type="String"/>
      <session-mapping key="findCardTimeout" value="GlobalVars.findCardTimeout" type="String"/>
      <success>
        <action label="true" next="EH1010_GoTo_StoreLocator_SD">
          <if cond="GlobalVars.callType == 'activate'">
            <session-mapping key="GlobalVars.storeLocatorReason" expr="'sign up'"/>
            <else>
              <session-mapping key="GlobalVars.storeLocatorReason" expr="'payment'"/>
            </else>
          </if>
        </action>
        <action label="false" next="EH1015_CallUsBack_PP"/>
        <action label="yes" next="EH1010_GoTo_StoreLocator_SD">
          <if cond="GlobalVars.callType == 'activate'">
            <session-mapping key="GlobalVars.storeLocatorReason" expr="'sign up'"/>
            <else>
              <session-mapping key="GlobalVars.storeLocatorReason" expr="'payment'"/>
            </else>
          </if>
        </action>
        <action label="no" next="EH1015_CallUsBack_PP"/>
        <action label="continue">
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="callType == 'activate'">
                <prompt id="EH1005_ini_01">
                  <prompt-segments>
                    <audiofile text="Unfortunately I won't be able to activate your account without a payment on this call" src="EH1005_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="EH1005_ini_02">
                  <prompt-segments>
                    <audiofile text="You can open an account by paying in cash at a Metro  Store or Authorized dealer Would you like me to find the closest one to you now?" src="EH1005_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="silence_500ms" cond="preferredPaymentMethod == 'metro' &amp;&amp; tryOtherCardReason == 'pin_invalid'">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="EH1005_ini_04">
                    <prompt-segments>
                      <audiofile text="You can also pay in person at a Metro Location Would you like me to find the closest one to you?" src="EH1005_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="EH1005_FindStoreYN_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <if cond="callType == 'activate'">
                <prompt id="EH1005_ini_01">
                  <prompt-segments>
                    <audiofile text="Unfortunately I won't be able to activate your account without a payment on this call" src="EH1005_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="EH1005_ini_02">
                  <prompt-segments>
                    <audiofile text="You can open an account by paying in cash at a Metro  Store or Authorized dealer Would you like me to find the closest one to you now?" src="EH1005_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="silence_500ms" cond="preferredPaymentMethod == 'metro' &amp;&amp; tryOtherCardReason == 'pin_invalid'">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="EH1005_ini_04">
                    <prompt-segments>
                      <audiofile text="You can also pay in person at a Metro Location Would you like me to find the closest one to you?" src="EH1005_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="EH1005_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like me to find the closest Metro location?" src="EH1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="EH1005_nm2_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="You can open an account by paying in cash at a Metro  location If you d like to look for the closest one to you now, say  yes  or press 1 Otherwise, say  no  or press 2" src="EH1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EH1005_nm2_02" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="You can pay in cash at a Metro  location If you d like to look for the closest one to you now, say  yes  or press 1 Otherwise, say  no  or press 2" src="EH1005_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="EH1005_nm3_01">
                <prompt-segments>
                  <audiofile text="If you want to find the closest Metro location now, press 1 Otherwise, press 2" src="EH1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EH1005_nm1_01">
                <prompt-segments>
                  <audiofile text="Would you like me to find the closest Metro location?" src="EH1005_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EH1005_nm2_01" cond="callType == 'activate'">
                <prompt-segments>
                  <audiofile text="You can open an account by paying in cash at a Metro  location If you d like to look for the closest one to you now, say  yes  or press 1 Otherwise, say  no  or press 2" src="EH1005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EH1005_nm2_02" cond="callType != 'activate'">
                <prompt-segments>
                  <audiofile text="You can pay in cash at a Metro  location If you d like to look for the closest one to you now, say  yes  or press 1 Otherwise, say  no  or press 2" src="EH1005_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="EH1005_nm3_01">
                <prompt-segments>
                  <audiofile text="If you want to find the closest Metro location now, press 1 Otherwise, press 2" src="EH1005_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="callType == 'activate'">
                <prompt id="EH1005_ini_01">
                  <prompt-segments>
                    <audiofile text="Unfortunately I won't be able to activate your account without a payment on this call" src="EH1005_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="EH1005_ini_02">
                  <prompt-segments>
                    <audiofile text="You can open an account by paying in cash at a Metro  Store or Authorized dealer Would you like me to find the closest one to you now?" src="EH1005_ini_02.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="silence_500ms" cond="preferredPaymentMethod == 'metro' &amp;&amp; tryOtherCardReason == 'pin_invalid'">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="EH1005_ini_04">
                    <prompt-segments>
                      <audiofile text="You can also pay in person at a Metro Location Would you like me to find the closest one to you?" src="EH1005_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="EH1005_FindStoreYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="EH1005_FindStoreYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="EH1010_GoTo_StoreLocator_SD">
      <gotodialog next="StoreLocator_Main_Dialog"/>
      <action next="EH1010_GoTo_StoreLocator_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="EH1010_GoTo_StoreLocator_SD_return_CS">
      <if cond="GlobalVars.callType == 'activate'">
        <session-mapping key="GlobalVars.activationResult" expr="'goodbye'"/>
        <action next="getReturnLink()"/>
        <else>
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
          <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </custom-state>

    <play-state id="EH1015_CallUsBack_PP">
      <audio>
        <prompt id="EH1015_out_01">
          <prompt-segments>
            <audiofile text="Alright You can call us back any time you're ready!" src="EH1015_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="GlobalVars.callType == 'activate'">
        <session-mapping key="GlobalVars.activationResult" expr="'goodbye'"/>
        <action next="getReturnLink()"/>
        <else>
          <session-mapping key="GlobalVars.tryOtherCardReason" expr="undefined"/>
          <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </play-state>

  </dialog>
  