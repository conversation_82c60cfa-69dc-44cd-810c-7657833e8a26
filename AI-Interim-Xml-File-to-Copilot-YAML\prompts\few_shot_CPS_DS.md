memorize below yaml format

- kind: LogCustomTelemetryEvent
  id: rb0310_Enroll_DS

- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(Global.userName = "abc", true, false)
      actions:
        - kind: GotoAction
          id: goto_REPLACE_THIS
          actionId: rb0340_Enroll_DA
  elseActions:
    - kind: GotoAction
      id: goto_REPLACE_THIS
      actionId: PlayWelcomeService_PP
      

prefix = topic	

also use these instruction while conversion 
1) kind should always be "ConditionGroup"
2) replace first "id" value with "decision-state" id.
3) replace conditions "id" value with some alpha numeric value with conditionItem_ prefix.
4) pick "if" condition from input and convert it in yml format like below and do not use double equals like "=="
=Topic.ivrOriginalEntry = "HOTLINE"
5) if there is "!=" sign in any condition then change it to "<>" sign.
6) if there is "OR" sign in condition then change it to "||" sign.
7) if there is "AND" sign in condition then change it to "&&" sign.
8) if there is and "AND" or "OR" sign in the condition then add "=" in front of first Global variable only not for other variables. like below:
  condition: |
    =If(And(Global.vpEnrollStatus = "MORE_AUDIO_REQUIRED" , Global.collectionCounter < 5), Or( Global.enrollCounter < 4), true, false)
9) if there is and "AND" or "OR" sign in the condition then add "Global" in front of each variable if its missing. Like:
Global.myMode = "dtmf" || Global.myMode = "DTMF"
10) if there is any "session-mapping" tag within input, convert it into below format and generate some random alphanumeric value and replace id with it. Do not generate same id everytime.
actions:
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.hangupRequired
      value: true
11) if there is special character # while session-mapping . put it under quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: '#'
12) if there is any condition in the value of session-mapping then put it under double quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: "GlobalVars.GetTagData.tagVariables != undefined ? GlobalVars.GetTagData.tagVariables.needMDN : 'false'"
13)  Replace "SetVariable" id with "setVariable_REPLACE_THIS".
14) If the "next" value of "action" tag ends with _DM, _DS, _DA, _DB or _PP, output this format:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0410_CleanUpNeeded_DS

actionId should be next id.
Replace "GotoAction" id with "goto_REPLACE_THIS".

Input:
<action next="rb0520_CollectVerificationPhrase_DM" />
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0520_CollectVerificationPhrase_DM

15) If the next value ends with _Dialog, output this format:
- kind: BeginDialog
  id: begin_REPLACE_THIS
  dialog: topic.enroll_Dialog

"dialog" should be next value with prefix.
Replace "BeginDialog" id with "begin_REPLACE_THIS".

Example Input:
<action label="retry" next="rb0310_Enrollment_DM">
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0310_Enrollment_DM

Output should be generated based on the next value alone, without considering the label.

16) Replace "ConditionGroup" id with "conditionGroup_REPLACE_THIS".
17) Replace "conditions" first id with "conditionItem_REPLACE_THIS".
18) follow the same above next pattern in "elseActions".
19) do not create yaml for "script" tag.
20) "LogCustomTelemetryEvent" id should be the "decision-state" id.
21) if there is "OR" sign in condition then change it to "||" sign.
22) if there is "AND" sign in condition then change it to "&&" sign.
23) if there is complex condition and having more more "&&" and "||" operator then use the powerfx and use AND and OR and convert it into powerfx like below format.
example : 
input:
cond="sessionData.dnisType == 'SpecialtyPharmacy' &amp;&amp; (sessionData.callerIntent.contains('BOMM')==false &amp;&amp; sessionData.nluTagData.destination.contains('BOMM')==false &amp;&amp; sessionData.nluTagData.destination.contains('Bomm')==false)"
output:
=If(And(
    Global.sessionData.dnisType = "SpecialtyPharmacy",
    Not(Text.Contains(Global.sessionData.callerIntent, "BOMM")),
    Not(Text.Contains(Global.sessionData.nluTagData.destination, "BOMM")),
    Not(Text.Contains(Global.sessionData.nluTagData.destination, "Bomm"))
), true, false)

24) do not create any yaml for "<event>" tag. like below.
<event name="error" next="hl0020_TransferToAgent_PP">
      <session-mapping key="goToAgent" value="true" />
</event>

25) each  "elseActions" should be indented with its "conditions" like below examples.
- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(Global.returnCode = "0", true, false)
      actions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: | 
                =If(Global.sessionData.employerGroupData.isAgentOfRecord(), true, false)
              actions:
                - kind: ConditionGroup
                  id: conditionGroup_REPLACE_THIS
                  conditions:
                    - id: conditionItem_REPLACE_THIS
                      condition: |
                        =If(Global.sessionData.isAuthenticated(), true, false)
                      actions:
                        - kind: GotoAction
                          id: goto_REPLACE_THIS
                          actionId: au17320_CheckIntentReqMemLevelAuth_DS
                  elseActions:
                    - kind: ConditionGroup
                      id: conditionGroup_REPLACE_THIS
                      conditions:
                        - id: conditionItem_REPLACE_THIS
                          condition: |
                            =If(Global.sessionData.IDAuthData.billingProfileIDEntryAttempts = 0, true, false)
                          actions:
                            - kind: GotoAction
                              id: goto_REPLACE_THIS
                              actionId: au17300_ConfirmBillingID_DM
                      elseActions:
                        - kind: GotoAction
                          id: goto_REPLACE_THIS
                          actionId: ma01100_InitTransfer_DS
          elseActions:
            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: ma01100_InitTransfer_DS
  elseActions:
    - kind: GotoAction
      id: goto_REPLACE_THIS
      actionId: ma01100_InitTransfer_DS
26) if there is no if/else condition like example 6 or more than 2 label without if/else condition then compare that label value with Global.label and create if else condition for each label like example 6 output.
27) if there is no label in "action tag" then make it like below.
Example
input:
<action next="hl0010_WhatsNext_DS">
output:
condition: |-
    =If(Global.TRUE = true, true, false)

28) if there is any condition with empty string like below. convert that single quotes to double quotes.
<if cond="hangupRequired == 'false' &amp;&amp; goToSMS == 'false' &amp;&amp; goToOtherService == 'false' &amp;&amp; (serviceTarget == 'undefined' || serviceTarget == '' || serviceTarget == '#')">

output:

condition: |-
  =If(AND(Global.hangupRequired = false , goToSMS == 'false', Global.goToOtherService = false , Or(Global.serviceTarget = "undefined" , Global.serviceTarget = "" , Global.serviceTarget = "#")), true, false)

use above pattern to make condition and don't use && and || operator in any condition
29) If there is any integer value in condition, just remove the quotes from them and make it integer like below example:
input:
<if cond="authenticated == '0' &amp;&amp; authenticationRequired == 'true'">
output:
condition: |-
  =If(And(Global.authenticated == 0 , Global.authenticationRequired == true),true, false)

30) indentation should be same as memorized yaml
31) use below examples for your reference

### Example 1:
**Input XML:**
```xml
<decision-state id="rb0330_CheckEnrollPassphrase_DS">
			<if cond="collectionMaxErrorCounter  &gt; 2">
				<action label="cleanup" next="rb0410_CleanUpNeeded_DS">
				
				<session-mapping key="result" value="failure" type="String" />	
				<session-mapping key="reason" value="maxCollections" type="String" />	
				</action>
			<elseif cond="collectionNiOrNm_Temp == 'undefined'">
				<action label="enroll" next="rb0340_Enroll_DA" />
			</elseif>
			<else>
				<action label="retry" next="EndofConversation">
					<session-mapping key="isComingFrom_rb0330" value="true" type="Boolean" />
				</action>
			</else>
			</if>
</decision-state>
          
```

**Output yaml:**
```yaml
- kind: LogCustomTelemetryEvent
  id: rb0330_CheckEnrollPassphrase_DS

- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(Global.collectionMaxErrorCounter > 2, true, false)
      actions:
        - kind: SetVariable
          id: setVariable_REPLACE_THIS
          variable: Global.result
          value: failure
        - kind: SetVariable
          id: setVariable_REPLACE_THIS
          variable: Global.reason
          value: maxCollections
        - kind: GotoAction
          id: goto_REPLACE_THIS
          actionId: rb0410_CleanUpNeeded_DS

  elseActions:
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Global.collectionNiOrNm_Temp = "undefined", true, false)
          action:
            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: rb0340_Enroll_DA
      
      elseActions:
        - kind: SetVariable
          id: setVariable_REPLACE_THIS
          variable: Global.isComingFrom_rb0330
          value: true
        - kind: BeginDialog
          id: begin_REPLACE_THIS
          dialog: topic.EndofConversation

```

### Example 2:
**Input XML:**
```xml
<decision-state id="gs00530_IsReentry_DS">
            <if cond="sessionData.nluTagData.topicTag == 'card' &amp;&amp; sessionData.idCardData.isIdCardsComplete() == true">
                <action label="reentry ID cards" next="ma01100_InitTransfer_DS" />
    		<elseif cond="sessionData.nluTagData.topicTag == 'benefits' &amp;&amp; sessionData.benefitsData.isEligBenefitsComplete() == true">
                <action label="reentry Benefits" next="ma01100_InitTransfer_DS" />
            </elseif>
    		<elseif cond="sessionData.nluTagData.topicTag == 'billing' &amp;&amp; sessionData.billingData.isBillingComplete() == true">
                <action label="reentry Billing" next="ma01100_InitTransfer_DS" />
            </elseif>
    		<elseif cond="sessionData.nluTagData.topicTag == 'rx' &amp;&amp; sessionData.nluTagData.subtopicTag == 'refill' &amp;&amp; sessionData.memberData.rxData.isRxRefillComplete() == true">
                <action label="reentry Refill" next="ma01100_InitTransfer_DS" />
            </elseif>
            <elseif cond="sessionData.nluTagData.topicTag == 'claims' &amp;&amp; sessionData.claimsData.isClaimsComplete() == true">
                <action label="reentry claims" next="ma01100_InitTransfer_DS" />
            </elseif>
            <elseif cond="sessionData.nluTagData.topicTag == 'rx' &amp;&amp; sessionData.rxBillingData.isRxPaymentComplete() == true">
                <action label="rxPaymentAllSet" next="ma01100_InitTransfer_DS" />
            </elseif>
            <else>
            	<action label="first entry" next="gs00902_SkipProgramIdCheck_DS" />
            </else>
            </if>
        </decision-state>
```
**Output yaml:**
```yaml
- kind: LogCustomTelemetryEvent
  id: gs00530_IsReentry_DS
- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(And(Global.sessionData.nluTagData.topicTag = "card",Global.sessionData.idCardData.isIdCardsComplete() = true), true, false)
      actions:
        - kind: GotoAction
          id: goto_REPLACE_THIS
          actionId: ma01100_InitTransfer_DS
  elseActions:
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(And(Global.sessionData.nluTagData.topicTag = "benefits", Global.sessionData.benefitsData.isEligBenefitsComplete() = true), true, false)
          actions:
            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: ma01100_InitTransfer_DS
      elseActions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: |
                =If(And(Global.sessionData.nluTagData.topicTag = "billing", Global.sessionData.billingData.isBillingComplete() = true), true, false)
              actions:
                - kind: GotoAction
                  id: goto_REPLACE_THIS
                  actionId: ma01100_InitTransfer_DS
          elseActions:
            - kind: ConditionGroup
              id: conditionGroup_REPLACE_THIS
              conditions:
                - id: conditionItem_REPLACE_THIS
                  condition: |
                    =If(And(Global.sessionData.nluTagData.topicTag = "rx" , Global.sessionData.nluTagData.subtopicTag = "refill" , Global.sessionData.memberData.rxData.isRxRefillComplete() = true), true, false)
                  actions:
                    - kind: GotoAction
                      id: goto_REPLACE_THIS
                      actionId: ma01100_InitTransfer_DS
              elseActions:
                - kind: ConditionGroup
                  id: conditionGroup_REPLACE_THIS
                  conditions:
                    - id: conditionItem_REPLACE_THIS
                      condition: |
                        =If(And(Global.sessionData.nluTagData.topicTag = "claims" , Global.sessionData.claimsData.isClaimsComplete() = true), true, false)
                      actions:
                        - kind: GotoAction
                          id: goto_REPLACE_THIS
                          actionId: ma01100_InitTransfer_DS
                  elseActions:
                    - kind: ConditionGroup
                      id: conditionGroup_REPLACE_THIS
                      conditions:
                        - id: conditionItem_REPLACE_THIS
                          condition: |
                            =If(And(Global.sessionData.nluTagData.topicTag = "rx" , Global.sessionData.rxBillingData.isRxPaymentComplete() = true), true, false)
                          actions:
                            - kind: GotoAction
                              id: goto_REPLACE_THIS
                              actionId: ma01100_InitTransfer_DS
                      elseActions:
                        - kind: BeginDialog
                          id: begin_REPLACE_THIS
                          dialog: topic.gs00902_SkipProgramIdCheck_DS
```

### Example 3:
**Input XML:**
```xml
<decision-state id="cf13540_CheckMemberContractInfo_DS">
              <if cond="true">
                   <session-mapping key="sessionData.transferData.calledCoverageService" value="true" type="Boolean" />
              </if>
              <if cond="returnCode == '0'">
                   <if cond="sessionData.ingressData.isDnisPolicyTypeMismatchTransfer() == true &amp;&amp; sessionData.nluTagData.functionTag != 'programs-otc'">
                        <session-mapping value="Y" key="sessionData.authStatus" />
                        <action label="DnisPolicyTypeMismatchTransfer" next="ma01100_InitTransfer_DS" />
                   <else>
                        <if cond="(sessionData.nluTagData.subtopicTag == 'benefits-hospice' || sessionData.nluTagData.subtopicTag == 'benefits-healthy_foods_card'||sessionData.nluTagData.subtopicTag == 'benefits-healthy_options')|| (sessionData.nluTagData.functionTag == 'contact_order_disambig' || sessionData.nluTagData.functionTag == 'inquire-card_activation' || sessionData.nluTagData.functionTag == 'inquire-order_disambig'||                          sessionData.nluTagData.functionTag == 'inquire-order_status_cancelled_disambig' || sessionData.nluTagData.functionTag == 'inquire-order_status_disambig' || sessionData.nluTagData.functionTag == 'place-order_disambig'|| sessionData.nluTagData.functionTag == 'programs-otc' || sessionData.nluTagDatafunctionTag == 'report-problem_order_disambig') || (sessionData.nluTagData.initialIntent == 'order-otc')">
                             <if cond="sessionData.memberData.selectedPolicy.contractNumber == null || sessionData.memberData.selectedPolicy.contractNumber == '' || sessionData.memberData.selectedPolicy.pbpId == null || sessionData.memberData.selectedPolicy.pbpId == ''|| sessionData.memberData.selectedPolicy.segmentId == null || sessionData.memberData.selectedPolicy.segmentId == ''">
                                  <action label="success skip veteran and program identification" next="cf13610_CheckHspDnis_DS" />
                             <else>
                                  <action label="success check program identification" next="cf13570_GetProgramIdentification_DB_DA" />
                             </else>
                             </if>
                        <else>
                        	<if cond="sessionData.dnisType == 'HBH'">
                        		<action label="Humana Behavioral Health" next="cf13545_LookupBehavioralHealth_DB_DA">
                        			<session-mapping key="sessionData.memberData.includeMemberPartners" value="true" type="Boolean" />
                        		</action>
                        	<else>
                        		<action label="Non Behavioral Health" next="cf13610_CheckHspDnis_DS" />
                        	</else>
                        	</if>
                        </else>
                        </if>
                   </else>
                   </if>
              <else>
                   <session-mapping value="cf13540_CheckMemberContractInfo_DS" key="sessionData.erroredAt" />
                   <action label="Failure" next="ma01100_InitTransfer_DS" />
              </else>
              </if>
         </decision-state>
```
**Output yaml:**
```yaml
- kind: LogCustomTelemetryEvent
  id: cf13540_CheckMemberContractInfo_DS
- kind: ConditionGroup
  id: conditionGroup_T2bIlr
  conditions:
    - id: conditionItem_QDcFzP
      condition: |
        =If(Global.true = true, true, false)
      actions:
        - kind: SetVariable
          id: setVariable_gJiUXr
          variable: Global.sessionData.transferData.calledCoverageService
          value: true

    - id: conditionItem_F2EX7Y
      condition: |
        =If(Global.action = "sfdsd", true, false)
      actions:
        - kind: ConditionGroup
          id: conditionGroup_yo3hf1
          conditions:
            - id: conditionItem_LBr8HL
              condition: |
                =If(Global.returnCode = "0", true, false)
              actions:
                - kind: SetVariable
                  id: setVariable_DVQEe4
                  variable: Global.authStatus
                  value: Y

                - kind: GotoAction
                  id: MX4lWU
                  actionId: ma01100_InitTransfer_DS

          elseActions:
            - kind: ConditionGroup
              id: conditionGroup_RvAvzs
              conditions:
                - id: conditionItem_EIsjzq
                  condition: |
                    =If(Or(Global.sessionData.nluTagData.subtopicTag = "benefits-hospice" , Global.sessionData.nluTagData.subtopicTag = "benefits-healthy_foods_card" , Global.sessionData.nluTagData.subtopicTag = "benefits-healthy_options" , Global.sessionData.nluTagData.functionTag = "contact_order_disambig" , Global.sessionData.nluTagData.functionTag = "inquire-card_activation" , Global.sessionData.nluTagData.functionTag = "inquire-order_disambig" , Global.sessionData.nluTagData.functionTag = "inquire-order_status_cancelled_disambig" , Global.sessionData.nluTagData.functionTag = "inquire-order_status_disambig" , Global.sessionData.nluTagData.functionTag = "place-order_disambig" , Global.sessionData.nluTagData.functionTag = "programs-otc" , Global.sessionData.nluTagData.functionTag = "report-problem_order_disambig" , Global.sessionData.nluTagData.initialIntent = "order-otc"), true, false)
                  actions:
                    - kind: ConditionGroup
                      id: conditionGroup_YRgGPm
                      conditions:
                        - id: conditionItem_hxpMYh
                          condition: |
                            =If(Or(Global.sessionData.memberData.selectedPolicy.contractNumber = null , Global.sessionData.memberData.selectedPolicy.contractNumber = "" , Global.sessionData.memberData.selectedPolicy.pbpId = null , Global.sessionData.memberData.selectedPolicy.pbpId = "" , Global.sessionData.memberData.selectedPolicy.segmentId = null , Global.sessionData.memberData.selectedPolicy.segmentId = ""),true, false)
                          actions:
                            - kind: GotoAction
                              id: xUqz9j
                              actionId: cf13610_CheckHspDnis_DS

                      elseActions:
                        - kind: GotoAction
                          id: goto_REPLACE_THIS
                          actionId: cf13570_GetProgramIdentification_DB_DA
              elseActions:
                - kind: ConditionGroup
                  id: conditionGroup_wvS85F
                  conditions:
                    - id: conditionItem_ePi299
                      condition: |
                        =If(Global.sessionData.dnisType = "HBH",true, false)
                      actions:
                        - kind: SetVariable
                          id: setVariable_wxLciI
                          variable: Global.sessionData.memberData.includeMemberPartners
                          value: true

                        - kind: GotoAction
                          id: goto_REPLACE_THIS
                          actionId: cf13545_LookupBehavioralHealth_DB_DA

                  elseActions:
                    - kind: GotoAction
                      id: goto_REPLACE_THIS
                      actionId: cf13610_CheckHspDnis_DS

  elseActions:
      - kind: SetVariable
        id: setVariable_REPLACE_THIS
        variable: Global.sessionData.erroredAt
        value: cf13540_CheckMemberContractInfo_DS
      - kind: GotoAction
        id: goto_REPLACE_THIS
        actionId: ma01100_InitTransfer_DS
        
```

### Example 4:
**Input XML:**
```xml
<decision-state id="au17050_LookupAgentResult_DS">
  <if cond="returnCode == '0'">
    <if cond="sessionData.agentData.agentIDType == 'SAN'">
      <session-mapping key="sessionData.agentData.agentID" path="sessionData.IDAuthData.agentIDEntered" type="String" />
    </if>
    <action label="Found" next="au17100_GetGroupIDFromAgent_DM" />
    <elseif cond="returnCode == '-1'">
      <if cond="sessionData.IDAuthData.agentIDEntryAttempts == 0 &amp;&amp; sessionData.IDAuthData.isAgentIDConfirmed()== false">
              <action label="Notfound" next="au17060_ConfirmTaxIDOrSAN_DM">
                  <session-mapping value="+1" key="sessionData.IDAuthData.agentIDEntryAttempts" type="Integer" />
              </action>
            <else>
                <action label="AgentIdEntryAttemptsnonzero" next="ma01100_InitTransfer_DS" />
            </else>  
        </if>
    </elseif> 
    <else>
        <action label="Transfer" next="ma01100_InitTransfer_DS">
            <session-mapping key="sessionData.erroredAt" value="au17050_LookupAgentResult_DS" type="String" />
        </action>
    </else>  
  </if>
</decision-state>
```
**Output yaml:**
```yaml

- kind: LogCustomTelemetryEvent
  id: au17050_LookupAgentResult_DS

- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(Global.returnCode = "0",true, false)
      actions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: |
                =If(Global.sessionData.agentData.agentIDType = "SAN",true, false)
              actions:
                - kind: SetVariable
                  id: setVariable_REPLACE_THIS
                  variable: Global.sessionData.agentData.agentID
                  value: sessionData.IDAuthData.agentIDEntered
        - kind: GotoAction
          id: goto_REPLACE_THIS
          actionId: au17100_GetGroupIDFromAgent_DM

  elseActions:
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Global.returnCode = "-1",true, false)
          actions:
            - kind: ConditionGroup
              id: conditionGroup_REPLACE_THIS
              conditions:
                - id: conditionItem_REPLACE_THIS
                  condition: |
                    =If(And(Global.sessionData.IDAuthData.agentIDEntryAttempts = 0 , Global.sessionData.IDAuthData.isAgentIDConfirmed() = false),true, false)
                  actions:
                    - kind: SetVariable
                      id: setVariable_REPLACE_THIS
                      variable: Global.sessionData.IDAuthData.agentIDEntryAttempts
                      value: +1
                    - kind: GotoAction
                      id: goto_REPLACE_THIS
                      actionId: au17060_ConfirmTaxIDOrSAN_DM
              elseActions:
                - kind: GotoAction
                  id: goto_REPLACE_THIS
                  actionId: ma01100_InitTransfer_DS

      elseActions:
        - kind: SetVariable
          id: setVariable_REPLACE_THIS
          variable: Global.sessionData.erroredAt
          value: au17050_LookupAgentResult_DS
        - kind: GotoAction
          id: goto_REPLACE_THIS
          actionId: ma01100_InitTransfer_DS
```

### Example 5:
**Input XML:**
```xml
<decision-state id="ic11170_CheckAction_DS">
  <if cond="sessionData.nluTagData.destinationType == 'DISAMBIG' || sessionData.nluTagData.destinationType == 'disambig'">
    <if cond="sessionData.dnisType == 'SpecialtyPharmacy' &amp;&amp; (sessionData.callerIntent.contains('BOMM')==false &amp;&amp; sessionData.nluTagData.destination.contains('BOMM')==false &amp;&amp; sessionData.nluTagData.destination.contains('Bomm')==false)">
      <action label="Transfer - HSP - Disambig" next="ma00900_EndCall_SD">
        <session-mapping key="hasConfirmed" value="false" type="Boolean" />
      </action>
      <else>
        <action label="Disambig" next="ic11150_NLUdisambig_DM">
          <session-mapping key="hasConfirmed" value="false" type="Boolean" />
        </action>
      </else>
    </if>
    <elseif cond="sessionData.nluTagData.destinationType == 'RULE' || sessionData.nluTagData.destinationType == 'rule'">
      <action label="Route to a rule" next="CleanUpPreviousTagInfo" />
    </elseif>
    <elseif cond="sessionData.nluTagData.destinationType == 'reprompt'">
      <if cond="nluAttemptCounter &lt; 2">
        <action label="Route to Main Menu" next="ic11100_MainMenu_DM" />
        <else>
          <action label="Route to rep or functionType is none or blank" next="return" />
        </else>
      </if>
    </elseif>
    <else>
      <action label="return" next="return" />
    </else>
  </if>
</decision-state>
```

**Output yaml:**
```yaml

- kind: LogCustomTelemetryEvent
  id: ic11170_CheckAction_DS
- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(Or(Global.sessionData.nluTagData.destinationType = "DISAMBIG" , Global.sessionData.nluTagData.destinationType = "disambig"), true, false)
      actions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: |
                =If(And(Global.sessionData.dnisType = "SpecialtyPharmacy" , Global.sessionData.callerIntent.contains('BOMM') = false , Global.sessionData.nluTagData.destination.contains('BOMM') = false , Global.sessionData.nluTagData.destination.contains('Bomm') = false), true, false)
              actions:
                - kind: SetVariable
                  id: setVariable_REPLACE_THIS
                  variable: Global.hasConfirmed
                  value: false
                - kind: BeginDialog
                  id: begin_REPLACE_THIS
                  dialog: topic.ma00900_EndCall_SD
          elseActions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.hasConfirmed
              value: false
            - kind: BeginDialog
              id: begin_REPLACE_THIS
              dialog: topic.ic11150_NLUdisambig_DM
  elseActions:
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Or(Global.sessionData.nluTagData.destinationType = "RULE" , Global.sessionData.nluTagData.destinationType = "rule"), true, false)
          actions:
            - kind: BeginDialog
              id: begin_REPLACE_THIS
              dialog: topic.CleanUpPreviousTagInfo
      elseActions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: |
                =If(Global.sessionData.nluTagData.destinationType = "reprompt", true, false)
              actions:
                - kind: ConditionGroup
                  id: conditionGroup_REPLACE_THIS
                  conditions:
                    - id: conditionItem_REPLACE_THIS
                      condition: =Global.nluAttemptCounter < 2
                      actions:
                        - kind: BeginDialog
                          id: begin_REPLACE_THIS
                          dialog: topic.ic11100_MainMenu_DM
                  elseActions:
                    - kind: BeginDialog
                      id: begin_REPLACE_THIS
                      dialog: topic.return
          elseActions:
            - kind: BeginDialog
              id: begin_REPLACE_THIS
              dialog: topic.return
```
### Example 6:
**Input XML:**
```xml
<decision-state id="hl0010_WhatsNext_DS">
            <action label="goToAgent" next="hl0020_TransferToAgent_PP" />
            <action label="hangupRequired" next="hl0030_CallTerminate_PP" />
			<action label="callsteering" next="hl0040_NLCS_SD" />
			<action label="simcard" next="hl0060_Sim_SD" />
			<action label="authenticate" next="hl0090_Authentication_SD" />
			<action label="k2" next="hl0080_K2_SD" />
			<action label="webinfo" next="hl0045_WebInfo_PP" />
			<action label="hungup" next="Hangup">
			    <session-mapping key="hungup" value="true" />
			    <session-mapping key="returncode" value="HANGUP" />
			</action>									
			<action label="default" next="Return" />
        </decision-state>
```

**Output yaml:**
```yaml
    - kind: LogCustomTelemetryEvent
      id: KVaRM3

    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Global.label = "View topic", true, false)
          actions:
            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: hl0020_TransferToAgent_PP

      elseActions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: |
                =If(Global.label = "hangupRequired", true, false)
              actions:
                - kind: GotoAction
                  id: goto_REPLACE_THIS
                  actionId: hl0030_CallTerminate_PP

          elseActions:
            - kind: ConditionGroup
              id: conditionGroup_REPLACE_THIS
              conditions:
                - id: conditionItem_REPLACE_THIS
                  condition: |
                    =If(Global.label = "callsteering", true, false)
                  actions:
                    - kind: GotoAction
                      id: goto_REPLACE_THIS
                      actionId: hl0040_NLCS_SD

              elseActions:
                - kind: ConditionGroup
                  id: conditionGroup_REPLACE_THIS
                  conditions:
                    - id: conditionItem_REPLACE_THIS
                      condition: |
                        =If(Global.label = "simcard", true, false)
                      actions:
                        - kind: GotoAction
                          id: goto_REPLACE_THIS
                          actionId: hl0060_Sim_SD

                  elseActions:
                    - kind: ConditionGroup
                      id: conditionGroup_REPLACE_THIS
                      conditions:
                        - id: conditionItem_REPLACE_THIS
                          condition: |
                            =If(Global.label = "authenticate", true, false)
                          actions:
                            - kind: GotoAction
                              id: goto_REPLACE_THIS
                              actionId: hl0090_Authentication_SD

                      elseActions:
                        - kind: ConditionGroup
                          id: conditionGroup_REPLACE_THIS
                          conditions:
                            - id: conditionItem_REPLACE_THIS
                              condition: |
                                =If(Global.label = "k2", true, false)
                              actions:
                                - kind: GotoAction
                                  id: goto_REPLACE_THIS
                                  actionId: hl0080_K2_SD

                          elseActions:
                            - kind: ConditionGroup
                              id: conditionGroup_REPLACE_THIS
                              conditions:
                                - id: conditionItem_REPLACE_THIS
                                  condition: |
                                    =If(Global.label = "webinfo", true, false)
                                  actions:
                                    - kind: GotoAction
                                      id: goto_REPLACE_THIS
                                      actionId: hl0045_WebInfo_PP
                              elseActions:
                                - kind: ConditionGroup
                                  id: conditionGroup_REPLACE_THIS
                                  conditions:
                                    - id: conditionItem_REPLACE_THIS
                                      condition: |
                                        =If(Global.label = "hungup", true, false)
                                      actions:
                                        - kind: SetVariable
                                          id: setVariable_REPLACE_THIS
                                          variable: Global.hungup
                                          value: true
                                      actions:
                                        - kind: SetVariable
                                          id: setVariable_REPLACE_THIS
                                          variable: Global.returncode
                                          value: HUNGUP   
                              
                                        - kind: GotoAction
                                          id: goto_REPLACE_THIS
                                          actionId: Hangup

                                  elseActions:
                                    - kind: EndDialog
                                      id: Return

```
### Example 7:
**Input XML:**
```xml
<decision-state id="ReturnFromSim_DS">
      <session-mapping key="comingFrom" value="SimCard" />
      <script className="com.nuance.ps.telefonica.scripts.UpdateFromSim" />
      <script className="com.nuance.ps.telefonica.scripts.CalllogUpdate" />
      <script className="com.nuance.ps.telefonica.scripts.KPIUpdate" />
      <script className="com.nuance.ps.telefonica.scripts.ModuleExit" />
      <action label="default" next="hl0010_WhatsNext_JDA">
        <if cond="authenticated == '0' &amp;&amp; authenticationRequired == 'true'">
          <action label="reauthentication" next="hl0002_lookup_init_CDP_DB_DA" />
          <else>
            <session-mapping key="serviceTarget" value="undefined" />
            <action label="routing" next="hl0010_WhatsNext_JDA" />
          </else>
        </if>
      </action>
      <action label="hangup_CS" next="Hangup_DA">
        <session-mapping key="hungup" value="true" />
        <session-mapping key="returncode" value="HANGUP" />
      </action>
      <event name="connection.disconnect.hangup" next="Hangup_DA">
        <session-mapping key="hungup" value="true" />
        <session-mapping key="returncode" value="HANGUP" />
      </event>
      <event name="error_CS" next="hl0020_TransferToAgent_PP">
        <session-mapping key="goToAgent" value="true" />
      </event>
    </decision-state>
```
**Output yaml:**
```yaml
- kind: LogCustomTelemetryEvent
  id: ReturnFromSim_DS

- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |-
        =If(Global.TRUE = true, true, false)
      actions:
        - kind: SetVariable
          id: setVariable_REPLACE_THIS
          variable: Global.comingFrom
          value: SimCard
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: |-
                =If(And(Global.authenticated = 0, Global.authenticationRequired = true), true, false)
              actions:
                - kind: GotoAction
                  id: goto_REPLACE_THIS
                  actionId: hl0002_lookup_init_CDP_DB_DA
          elseActions:
              - kind: SetVariable
                id: setVariable_REPLACE_THIS
                variable: Global.serviceTarget
                value: undefined
              - kind: GotoAction
                id: goto_REPLACE_THIS
                actionId: hl0010_WhatsNext_JDA

  elseActions:
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |-
            =If(Global.label = "hangup_CS", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.hungup
              value: true
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.returncode
              value: HANGUP
            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: Hangup_DA
```

### Example 8:
**Input XML:**
```xml
    <decision-state id="XR0005_CheckEntryMethod_DS">
      <session-mapping key="GlobalVars.isSuspended" expr="(GlobalVars.GetAccountDetails &amp;&amp; GlobalVars.GetAccountDetails.accountStatus == 'suspended')? true : false"/>
      <if cond="TransferTag == 'New_Customer_Sales_En' || TransferTag == 'New_Customer_Sales_Es'">
        <action next="XR0025_PlayTransferPrompt_PP"/>
      </if>
      <if cond="GlobalVars.acctLocked == true">
        <action next="XR0023_GoToBroadcastMessages_SD"/>
      </if>
      <if cond="GlobalVars.tag == 'request-representative_sales'">
        <session-mapping key="TransferTag" expr="'sales'"/>
      </if>
      <if cond="GlobalVars.transferReason == 'dbfail'">
        <session-mapping key="GlobalVars.cti_TransferReason" expr="getctiTransferReason(GlobalVars.GetCTIParameters.ctiTransferReason, 'xferred_backend_failure')"/>
      </if>
      <if cond="GlobalVars.tag == 'purchase-phone' || GlobalVars.tag == 'purchase-accessory'">
        <if cond="language == 'en-US'">
          <session-mapping key="TransferTag" expr="'purchasing_support_english'"/>
          <else>
            <session-mapping key="TransferTag" expr="'purchasing_support_spanish'"/>
          </else>
        </if>
        <elseif cond="GlobalVars.tag == 'T-MobileMoney'">
          <session-mapping key="TransferTag" expr="'tmobile_money_all'"/>
        </elseif>
        <elseif cond="GlobalVars.tag == 'home-internet'">
          <session-mapping key="TransferTag" expr="'home_internet_all'"/>
        </elseif>
      </if>
      <if cond="GlobalVars.fromMyMetroCustomerSupport == true">
        <action next="XR0025_PlayTransferPrompt_PP"/>
        <elseif cond="GlobalVars.enteredFrom == 'RCC'">
          <action next="XR0030_GetTransferDestination_DB_DA"/>
        </elseif>
        <elseif cond="callerSaidOperator == true">
          <session-mapping key="callerSaidOperator" value="false" type="Boolean"/>
          <gotodialog next="CallTransfer_OperatorRequestHandling"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'auto_pay'">
          <action next="XR0006_MetricsAutopayXfer_JDA"/>
        </elseif>
        <elseif cond="GlobalVars.callType == 'ThirdPartyFeaturesXfer' || GlobalVars.callType == 'transfer' || GlobalVars.troubleshootingTransfer == true || GlobalVars.dataCollectionTransfer == true">
          <gotodialog next="CallTransfer_Main_Dialog"/>
        </elseif>
        <elseif cond="GlobalVars.saidSomethingElse == true">
          <gotodialog next="CallTransfer_OperatorRequestHandling"/>
        </elseif>
        <elseif cond="(isMDE == true || GlobalVars.otherGhostCaller == true) &amp;&amp; !(GlobalVars.tag == 'vague-agent_destination')">
          <session-mapping key="isMDE" value="false" type="Boolean"/>
          <gotodialog next="CallTransfer_MDEHandling"/>
        </elseif>
        <elseif cond="GlobalVars.skipBroadcastMessage == true">
          <action next="XR0025_PlayTransferPrompt_PP"/>
        </elseif>
        <elseif cond="GlobalVars.playTransferMessage == false &amp;&amp; GlobalVars.twoFactorAuthOutcome == 'not_attempted'">
          <action next="XR0030_GetTransferDestination_DB_DA"/>
        </elseif>
        <else>
          <action next="XR0023_GoToBroadcastMessages_SD"/>
        </else>
      </if>
    </decision-state>

```
**Output yaml:**
```yaml
    - kind: LogCustomTelemetryEvent
      id: XR0005_CheckEntryMethod_DS
    
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |-
            =If(Global.TRUE = true, true, false)
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.GlobalVars.isSuspended
              value: "(GlobalVars.GetAccountDetails && GlobalVars.GetAccountDetails.accountStatus == 'suspended') ? true : false"
    
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Or(Global.TransferTag = "New_Customer_Sales_En", Global.TransferTag = "New_Customer_Sales_Es"), true, false)
          actions:
            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: XR0025_PlayTransferPrompt_PP
    
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Global.GlobalVars.acctLocked = true, true, false)
          actions:
            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: XR0023_GoToBroadcastMessages_SD
    
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Global.GlobalVars.tag = "request-representative_sales", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.TransferTag
              value: sales
    
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Global.GlobalVars.transferReason = "dbfail", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.GlobalVars.cti_TransferReason
              value: getctiTransferReason(GlobalVars.GetCTIParameters.ctiTransferReason, 'xferred_backend_failure')
    
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Or(Global.GlobalVars.tag = "purchase-phone", Global.GlobalVars.tag = "purchase-accessory"), true, false)
          actions:
            - kind: ConditionGroup
              id: conditionGroup_REPLACE_THIS
              conditions:
                - id: conditionItem_REPLACE_THIS
                  condition: |
                    =If(Global.language = "en-US", true, false)
                  actions:
                    - kind: SetVariable
                      id: setVariable_REPLACE_THIS
                      variable: Global.TransferTag
    
              elseActions:
                - kind: SetVariable
                  id: setVariable_REPLACE_THIS
                  variable: Global.TransferTag
                  value: purchasing_support_spanish
    
      elseActions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: |
                =If(Global.GlobalVars.tag = "T-MobileMoney", true, false)
              actions:
                - kind: SetVariable
                  id: setVariable_REPLACE_THIS
                  variable: Global.TransferTag
                  value: tmobile_money_all
    
          elseActions:
            - kind: ConditionGroup
              id: conditionGroup_REPLACE_THIS
              conditions:
                - id: conditionItem_REPLACE_THIS
                  condition: |
                    =If(Global.GlobalVars.tag = "home-internet", true, false)
                  actions:
                    - kind: SetVariable
                      id: setVariable_REPLACE_THIS
                      variable: Global.TransferTag
                      value: home_internet_all
    
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Global.GlobalVars.fromMyMetroCustomerSupport = true, true, false)
          actions:
            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: XR0025_PlayTransferPrompt_PP
    
      elseActions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: |
                =If(Global.GlobalVars.enteredFrom = "RCC", true, false)
              actions:
                - kind: GotoAction
                  id: goto_REPLACE_THIS
                  actionId: XR0030_GetTransferDestination_DB_DA
    
          elseActions:
            - kind: ConditionGroup
              id: conditionGroup_REPLACE_THIS
              conditions:
                - id: conditionItem_REPLACE_THIS
                  condition: |
                    =If(Global.callerSaidOperator = true, true, false)
                  actions:
                    - kind: SetVariable
                      id: setVariable_REPLACE_THIS
                      variable: Global.callerSaidOperator
                      value: false
    
                    - kind: BeginDialog
                      id: begin_REPLACE_THIS
                      dialog: topic.CallTransfer_OperatorRequestHandling
    
              elseActions:
                - kind: ConditionGroup
                  id: conditionGroup_REPLACE_THIS
                  conditions:
                    - id: conditionItem_REPLACE_THIS
                      condition: |
                        =If(Global.GlobalVars.callType = "auto_pay", true, false)
                      actions:
                        - kind: GotoAction
                          id: goto_REPLACE_THIS
                          actionId: XR0006_MetricsAutopayXfer_JDA
    
                  elseActions:
                    - kind: ConditionGroup
                      id: conditionGroup_REPLACE_THIS
                      conditions:
                        - id: conditionItem_IZ6ICz
                          condition: |
                            =If(Or(Global.GlobalVars.callType = "ThirdPartyFeaturesXfer", Global.GlobalVars.callType = "transfer", Global.GlobalVars.troubleshootingTransfer = true, Global.GlobalVars.dataCollectionTransfer = true), true, false)
                          actions:
                            - kind: BeginDialog
                              id: begin_REPLACE_THIS
                              dialog: topic.CallTransfer_Main_Dialog
                             
                      elseActions:
                        - kind: ConditionGroup
                          id: conditionGroup_REPLACE_THIS
                          conditions:
                            - id: conditionItem_REPLACE_THIS
                              condition: |
                                  =If(Global.GlobalVars.saidSomethingElse = true, true, false)
                              actions:
                                - kind: BeginDialog
                                  id: begin_REPLACE_THIS
                                  dialog: topic.CallTransfer_OperatorRequestHandling
    
                          elseActions:
                            - kind: ConditionGroup
                              id: conditionGroup_REPLACE_THIS
                              conditions:
                                - id: conditionItem_REPLACE_THIS
                                  condition: |
                                      =If(And(Or(Global.isMDE = true, Global.GlobalVars.otherGhostCaller = true), Not(Global.GlobalVars.tag = "vague-agent_destination")), true, false)
                                  actions:
                                    - kind: SetVariable
                                      id: setVariable_REPLACE_THIS
                                      variable: Global.isMDE
                                      value: false
                                    - kind: BeginDialog
                                      id: begin_REPLACE_THIS
                                      dialog: topic.CallTransfer_MDEHandling
    
                              elseActions:
                                - kind: ConditionGroup
                                  id: conditionGroup_REPLACE_THIS
                                  conditions:
                                    - id: conditionItem_REPLACE_THIS
                                      condition: |
                                        =If(Global.GlobalVars.skipBroadcastMessage = true, true, false)
                                      actions:
                                        - kind: GotoAction
                                          id: goto_REPLACE_THIS
                                          actionId: XR0025_PlayTransferPrompt_PP
    
                                  elseActions:
                                    - kind: ConditionGroup
                                      id: conditionGroup_REPLACE_THIS
                                      conditions:
                                        - id: conditionItem_REPLACE_THIS
                                          condition: |
                                              =If(And(Global.GlobalVars.playTransferMessage = false, Global.GlobalVars.twoFactorAuthOutcome = "not_attempted"), true, false)
                                          actions:
                                            - kind: GotoAction
                                              id: goto_REPLACE_THIS
                                              actionId: XR0030_GetTransferDestination_DB_DA
    
    
                                      elseActions:
                                        - kind: GotoAction
                                          id: goto_REPLACE_THIS
                                          actionId: XR0006_MetricsAutopayXfer_DS