<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="AdressCapture_Main_Dialog">
    <custom-state id="AD1000_AddressOSDM_EC_CS">
      <session-mapping key="collected_zipcode" value="GlobalVars.zipCode" type="String"/>
      <session-mapping key="zipcodegrammardirectory" value="/addr_grams/'+language+'/grammars/zipcodes/" type="String"/>
      <if cond="AddressCollection.returncode == 'COMMAND' &amp;&amp; AddressCollection.returnvalue == 'operator'">
        <action next="operatorSubmitFunction('AdressCapture_Main.dvxml','AD1000_AddressOSDM_EC',0)"/>
      </if>
      <if cond="AddressCollection.returncode == 'RECORD'">
        <session-mapping key="GlobalVars.addressFileName" expr="AddressCollection.returnvalue"/>
        <action next="SubmitAddressUtterance_Form_CS"/>
      </if>
      <session-mapping key="GlobalVars.streetAddress" expr="AD1000_AddressOSDM_EC.ADDRESS2NDLINE"/>
      <session-mapping key="GlobalVars.city" expr="AD1000_AddressOSDM_EC.CITY"/>
      <session-mapping key="GlobalVars.state" expr="AD1000_AddressOSDM_EC.STATEABBR"/>
      <session-mapping key="ActivationTable.CITY" expr="GlobalVars.city"/>
      <session-mapping key="ActivationTable.STATE" expr="GlobalVars.state"/>
      <session-mapping key="GlobalVars.addressreturnCode" expr="AddressCollection.returncode"/>
      <action next="Address_Return_CS"/>
      <catch/>
    </custom-state>

    <custom-state id="SubmitAddressUtterance_Form_CS">
      <session-mapping key="utterance" value="GlobalVars.addressFileName" type="String"/>
      <session-mapping key="spelledUtterance" value="" type="String"/>
      <session-mapping key="slot" value="wholeAddress" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="GlobalVars.isRecording" value="true" type="Boolean"/>
      <data-access id="SubmitUtteranceToTranscription"/>
      <if cond="SubmitUtteranceToTranscription.status == 'Success'">
        <action next="Address_Return_CS"/>
        <else>
          <action next="Address_Return_CS"/>
          <action next="Transfer.dvxml"/>
        </else>
      </if>
    </custom-state>

    <custom-state id="Address_Return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  