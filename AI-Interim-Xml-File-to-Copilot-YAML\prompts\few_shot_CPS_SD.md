memorize below yaml format

    - kind: BeginDialog
      id: hl0070_SendSMS_SD
      dialog: topic.SendSMS_SD_Dialog

    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.comingFrom
      value: SendSMS

    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(And(Global.hangupRequired = false , Global.goToOtherService = false, Or (Global.serviceTarget = "undefined" , Global.serviceTarget = '' , Global.serviceTarget = "#")), true, false)
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.forceCallerToHangup
              value: true
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.goToAgent
              value: true
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.goToSMS
              value: false

            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: hl0010_WhatsNext_DS

      elseActions:
        - kind: SetVariable
          id: setVariable_REPLACE_THIS
          variable: Global.goToSMS
          value: false

        - kind: GotoAction
          id: goto_REPLACE_THIS
          actionId: hl0010_WhatsNext_DS

    - kind: BeginDialog
      id: FBEyTy
      dialog: topic.PlayendcallService_PP

prefix = topic	

also use these instruction while conversion 
1) kind should always be "BeginDialog"  
2) replace "id" value with "subdialog-state" tag id.
3) "dialog" should be "gotodialog" tag next value with prefix.like below
dialog: topic.hu205_Benefits_Proactive
4)  if there is any "session-mapping" tag within input, convert it into below format. 
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.hangupRequired
      value: true
5) if there is special character # while session-mapping . put it under quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: '#'
6) if there is any condition in the value of session-mapping then put it under double quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: "GlobalVars.GetTagData.tagVariables != undefined ? GlobalVars.GetTagData.tagVariables.needMDN : 'false'"
6) replace "SetVariable" id with "setVariable_REPLACE_THIS" .
7) if there is any if/else condition then follow below instruction as well.
8) pick "if" condition from input and convert it in yml format like below and do not use double equals like "==".
=Topic.ivrOriginalEntry = "HOTLINE"
9) if there is "!=" sign in any condition then change it to "<>" sign.
10) if there is "OR" sign in condition then change it to "||" sign.
11) if there is "AND" sign in condition then change it to "&&" sign.
12) if there is and "AND" or "OR" sign in the condition then add "=" in front of first Global variable only not for other variables. like below:
  condition: |
    =If(And(Global.vpEnrollStatus = "MORE_AUDIO_REQUIRED" , Global.collectionCounter < 5), Or(Global.enrollCounter < 4), true, false)
13) if there is and "AND" or "OR" sign in the condition then add "Global" in front of each variable if its missing. Like:
Global.myMode = "dtmf" || Global.myMode = "DTMF"
14) If the "next" value of "action" tag ends with _DM, _DS, _DA, _DB or _PP, output this format:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0410_CleanUpNeeded_DS

actionId should be next id.
Replace "GotoAction" id with "goto_REPLACE_THIS".

Input:
<action next="rb0520_CollectVerificationPhrase_DM" />
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0520_CollectVerificationPhrase_DM

15) If the next value ends with _Dialog, output this format:
- kind: BeginDialog
  id: begin_REPLACE_THIS
  dialog: topic.enroll_Dialog

"dialog" should be next value with prefix.
Replace "BeginDialog" id with "begin_REPLACE_THIS".

Example Input:
<action label="retry" next="rb0310_Enrollment_DM">
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0310_Enrollment_DM

Output should be generated based on the next value alone, without considering the label.

16) Replace "ConditionGroup" id with "conditionGroup_REPLACE_THIS".
17) Replace "conditions" first id with "conditionItem_REPLACE_THIS".
18) follow the same above next pattern in "elseActions".
19) do not create yaml for "script" tag.
20) if there is "()" sign in the any conditions then remove it.
21) if there is any condition like below update it in a proper format.
 example:
 input:
 <if cond="hungup == 'true'">
 output:
 condition: |
   =If(Global.hungup = true, true, false)

22) if there is any condition with empty string like below. convert that single quotes to double quotes.
<if cond="hangupRequired == 'false' &amp;&amp; goToSMS == 'false' &amp;&amp; goToOtherService == 'false' &amp;&amp; (serviceTarget == 'undefined' || serviceTarget == '' || serviceTarget == '#')">

output:

condition: |-
  =If(AND(Global.hangupRequired = false , goToSMS == 'false', Global.goToOtherService = false , Or(Global.serviceTarget = "undefined" , Global.serviceTarget = "" , Global.serviceTarget = "#")), true, false)

use above pattern to make condition and don't use && and || operator in any condition
23) If there is any integer value in condition, just remove the quotes from them and make it integer like below example:
input:
<if cond="authenticated == '0' &amp;&amp; authenticationRequired == 'true'">
output:
condition: |-
  =If(And(Global.authenticated == 0 , Global.authenticationRequired == true),true, false)

24) indentation for next yaml should be same as first one or as memorized yaml
25) do not provide duplicate yaml.. generate only for provided inputs.

### Example 1:
**Input XML:**
```xml
<subdialog-state id="hl0070_SendSMS_SD">
<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
<gotodialog next="SendSMS_SD"/>
<action next="hl0010_WhatsNext_DS">
<session-mapping key="comingFrom" value="SendSMS"/>
<if cond="hangupRequired == 'false' && goToOtherService == 'false' && (serviceTarget == 'undefined' || serviceTarget == '' || serviceTarget == '#')">
<session-mapping key="forceCallerToHangup" value="true"/>
<session-mapping key="goToAgent" value="true"/>
<session-mapping key="goToSMS" value="false"/>
<action next="hl0010_WhatsNext_DS"/>
<else>
<session-mapping key="goToSMS" value="false"/>
<action next="hl0010_WhatsNext_DS"/>
</else>
</if>
</action>
</subdialog-state>

```

**Output yaml:**
```yaml
    - kind: BeginDialog
      id: hl0070_SendSMS_SD
      dialog: topic.SendSMS_SD_Dialog

    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.comingFrom
      value: SendSMS

    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(And(Global.hangupRequired = false , Global.goToOtherService = false, Or(Global.serviceTarget = "undefined" , Global.serviceTarget = '' , Global.serviceTarget = "#")), true, false)
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.forceCallerToHangup
              value: true
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.goToAgent
              value: true
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.goToSMS
              value: false

            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: hl0010_WhatsNext_DS

      elseActions:
        - kind: SetVariable
          id: setVariable_REPLACE_THIS
          variable: Global.goToSMS
          value: false

        - kind: GotoAction
          id: goto_REPLACE_THIS
          actionId: hl0010_WhatsNext_DS

```
