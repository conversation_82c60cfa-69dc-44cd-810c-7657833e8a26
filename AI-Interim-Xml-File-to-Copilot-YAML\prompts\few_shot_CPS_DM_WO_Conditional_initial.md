memorize below yaml format
    - kind: Question
      id: question_REPLACE_THIS
      interruptionPolicy:
        allowInterruption: true

      unrecognizedPrompt:
        speak:
          - nomatch1
          - nomatch2
      
      alwaysPrompt: true
      variable: Global.Var_REPLACE_THIS
      prompt:
        speak:
          - "So... after the tone please say: 'At Riyad Bank my voice is my password'"

      entity: PersonNamePrebuiltEntity
      voiceInputSettings:
        silenceDetectionTimeoutInMilliseconds: 7000
        repeatCountOnSilence: 2
        inputTimeoutResponse:
          speak:
            - Noinput1
            - Noinput2

        defaultValueMissingAction: Escalate

    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.collectionNiOrNm_Temp
      value: undefined

    - kind: GotoAction
      id: goto_REPLACE_THIS
      actionId: ezbEjq  


prefix = topic
also use these instruction while conversion 
1) kind should always be "kind: Question"  
2) replace first "id" value with "dm-state" id.
3) check all "nomatchprompts" tags and fetch text from "audiofile" tag and put it under "unrecognizedPrompt" speak, one by one.
4) check "initialprompt" tag and pick text from audiofile tag and put it under "prompt" speak.
5) check all "noinputprompts" tags and fetch text from "audiofile" tag and put it under "inputTimeoutResponse" speak, one by one.
6) if there is any "session-mapping" tag within input, convert it into below format. 
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.hangupRequired
      value: true
7) if there is special character # while session-mapping . put it under quotes like below.
    - kind: SetVariable
      id: setVariable_75ef3a
      variable: Global.serviceTarget
      value: '#'
7) replace "SetVariable" id with "setVariable_REPLACE_THIS" .
8) if  there is any "if" condition under "<success>" tag in input then convert it in yml format like below and do not use double equals like "=="
- kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
		  condition: |
        =If(Global.callerLanguage = "ar", true, false)
9) if there is "!=" sign in any condition then change it to "<>" sign.
10) if there is "OR" sign in condition then change it to "||" sign.
11) if there is "AND" sign in condition then change it to "&&" sign.
12) if there is and "AND" or "OR" sign in the condition then add "=" in front of first Global variable only not for other variables. like below:
  condition: |
    =If(And(Global.vpEnrollStatus = "MORE_AUDIO_REQUIRED" , Global.collectionCounter < 5), Or  (Global.enrollCounter < 4), true, false)
13) if there is and "AND" or "OR" sign in the condition then add "Global" in front of each variable if its missing. Like:
Global.myMode = "dtmf" || Global.myMode = "DTMF"
14) if the next value's postfix is  _DS or _DM or _PP or _DA or _DB of "action" tag in "<success>" tag then only generate below format. "actionId" should be next value.
 - kind: GotoAction
   id: goto_REPLACE_THIS
   actionId: rb0410_CleanUpNeeded_DS

replace "GotoAction" id with "goto_REPLACE_THIS".

15) when the next value of "action" tag in  "<success>" tag is _Dialog then generate below format. "dialog" should be next value with prefix.
  - kind: BeginDialog
    id: begin_REPLACE_THIS
    dialog: topic.vptransfer_Dialog

Replace "BeginDialog" id with "begin_REPLACE_THIS".

Example Input:
<action label="retry" next="rb0310_Enrollment_DM">
Example Output:
- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0310_Enrollment_DM

Output should be generated based on the next value alone, without considering the label.

16) follow the same above next pattern in "elseActions".
17) Replace "ConditionGroup" id with "conditionGroup_REPLACE_THIS".
18) Replace "conditions" first id with "conditionItem_REPLACE_THIS".
19) replace "variable" value after "alwaysPrompt" with Global.Var_REPLACE_THIS.
20) dialog should be the "next" with prefix value.
21) do not create any yaml for "<event>" tag. like below.
<event name="error" next="hl0020_TransferToAgent_PP">
      <session-mapping key="goToAgent" value="true" />
</event>

22) if nomatch/noinput tags are not present. doin't create yaml for it.
23) if there is any condition with empty string like below. convert that single quotes to double quotes.
<if cond="hangupRequired == 'false' &amp;&amp; goToSMS == 'false' &amp;&amp; goToOtherService == 'false' &amp;&amp; (serviceTarget == 'undefined' || serviceTarget == '' || serviceTarget == '#')">

output:

condition: |-
  =If(AND(Global.hangupRequired = false , goToSMS == 'false', Global.goToOtherService = false , Or(Global.serviceTarget = "undefined" , Global.serviceTarget = "" , Global.serviceTarget = "#")), true, false)

use above pattern to make condition and don't use && and || operator in any condition
24) If there is any integer value in condition, just remove the quotes from them and make it integer like below example:
input:
<if cond="authenticated == '0' &amp;&amp; authenticationRequired == 'true'">
output:
condition: |-
  =If(And(Global.authenticated == 0 , Global.authenticationRequired == true),true, false)

25) if there are more than 2 "label" under "success" tag without if/else condition like example 3 then compare that label value with Global.label and create if else condition for each label like example 3 output, follow this instruction just for success tag do the other thing as it is for other input/output.Also if there is any if/else condition within any label, make that condition as other if/else condition. 
26) indentation for next yaml should be same as first one or as memorized yaml


### Example 1:
**Input XML:**
```xml
<dm-state id="rb0310_Enrollment_DM">
    <var name="collectionCounter" path="collectionCounter" scope="session" isNamelist="true" />
    <var name="collectionNoInputCounter" path="collectionNoInputCounter" scope="session" isNamelist="true" />
    <var name="collectionNoMatchCounter" path="collectionNoMatchCounter" scope="session" isNamelist="true" />
    <var name="isComingFrom_rb0330" path="isComingFrom_rb0330" scope="session" isNamelist="true" />
    <var name="collectionNiOrNm_Temp" path="collectionNiOrNm_Temp" scope="session" isNamelist="true" />
    <var name="repromptAfterTrainError" path="repromptAfterTrainError" scope="session" isNamelist="true" />
    <var name="callComesFromAgent" path="callComesFromAgent" scope="session" isNamelist="true" />
    <success>
        <action next="rb0320_SaveEnrollUtterance_DA">
            <session-mapping key="collectionNiOrNm_Temp" value="undefined" type="String" />
            <session-mapping key="collectionNoInputCounter" value="0" type="Integer" />
            <session-mapping key="collectionNoMatchCounter" value="0" type="Integer" />
            <session-mapping key="repromptAfterTrainError" value="false" type="Boolean" />
            <script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
                <param name="param1" value="collectionCounter" />
            </script>
            <session-mapping key="isComingFrom_rb0330" value="false" type="Boolean" />
            <var name="callerLanguage" path="callerLanguage" scope="session" />
            <if cond="callerLanguage == 'ar'">
                <session-mapping key="enrollmentLanguage" value="ar" type="String" />
                <elseif cond="callerLanguage == 'en'">
                    <session-mapping key="enrollmentLanguage" value="en" type="String" />
                </elseif>
            </if>
        </action>
    </success>
    <event name="event.nuance.dialog.ndm.maxnomatches">
        <action next="rb0330_CheckEnrollPassphrase_DS">
            <session-mapping key="collectionNiOrNm_Temp" value="nomatch" type="String" />
            <session-mapping key="repromptAfterTrainError" value="false" type="Boolean" />
            <script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
                <param name="param1" value="collectionCounter" />
                <param name="param2" value="collectionNoMatchCounter" />
                <param name="param3" value="collectionMaxErrorCounter" />
            </script>
            <var name="callerLanguage" path="callerLanguage" scope="session" />
            <if cond="callerLanguage == 'ar'">
                <session-mapping key="enrollmentLanguage" value="ar" type="String" />
                <elseif cond="callerLanguage == 'en'">
                    <session-mapping key="enrollmentLanguage" value="en" type="String" />
                </elseif>
            </if>
        </action>
    </event>
    <event name="event.nuance.dialog.ndm.maxnoinputs">
        <action next="rb0330_CheckEnrollPassphrase_DS">
            <session-mapping key="collectionNiOrNm_Temp" value="noinput" type="String" />
            <session-mapping key="repromptAfterTrainError" value="false" type="Boolean" />
            <script className="com.nuance.riyadhbank.vocalpassword.script.IncrementCountersFromSession">
                <param name="param1" value="collectionCounter" />
                <param name="param2" value="collectionNoInputCounter" />
                <param name="param3" value="collectionMaxErrorCounter" />
            </script>
            <var name="callerLanguage" path="callerLanguage" scope="session" />
            <if cond="callerLanguage == 'ar'">
                <session-mapping key="enrollmentLanguage" value="ar" type="String" />
                <elseif cond="callerLanguage == 'en'">
                    <session-mapping key="enrollmentLanguage" value="en" type="String" />
                </elseif>
            </if>
        </action>
    </event>
    <collection_configuration inputmodes="voice">
        <threshold_configuration maxnomatches="0" maxnoinputs="0" />
        <vxml_properties inputmodes="voice">
            <property_map>
                <key>recordutterance</key>
                <value>true</value>
            </property_map>
        </vxml_properties>
        <prompt_configuration>
            <initialprompt>
                <audio>
                    <prompt id="rb0310_ini_12_part2" bargein="false">
                        <prompt-segments>
                            <audiofile text="So... after the tone please say 'At Riyad Bank my voice is my password'" src="rb0310_ini_12_part2.wav" />
                        </prompt-segments>
                    </prompt>
                </audio>
            </initialprompt>
            <repeatprompts>
                <audio>
                    <prompt id="sv3025_ini_01">
                        <prompt-segments>
                            <audiofile text="Möchten Sie die Informationen noch einmal hören?" src="sv3025_ini_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </repeatprompts>
            <noinputprompts>
                <audio>
                    <prompt id="sv8030_retry1_01">
                        <prompt-segments>
                            <audiofile src="sv8030_retry1_01.wav" text="Noinput1"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </noinputprompts>
            <noinputprompts>
                <audio>
                    <prompt id="gl_ni2_01">
                        <prompt-segments>
                            <audiofile text="Noinput2" src="gl_ni2_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </noinputprompts>
            <noinputprompts>
                <audio>
                    <prompt id="gl_ni3_01">
                        <prompt-segments>
                            <audiofile text="Noinput3" src="gl_ni3_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </noinputprompts>
            <nomatchprompts>
                <audio>
                    <prompt id="gl_nm1_01">
                        <prompt-segments>
                            <audiofile text="Nomatch1" src="gl_nm1_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </nomatchprompts>
            <nomatchprompts>
                <audio>
                    <prompt id="gl_nm2_01">
                        <prompt-segments>
                            <audiofile text="Nomatch2" src="gl_nm2_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </nomatchprompts>
            <nomatchprompts>
                <audio>
                    <prompt id="gl_nm3_01">
                        <prompt-segments>
                            <audiofile text="Nomatch3" src="gl_nm3_01.wav"/>
                        </prompt-segments>
                    </prompt>
                </audio>
            </nomatchprompts>
            <helpprompts count="1" filename="none" text="none" id="" />
            <helpprompts count="2" filename="none" text="none" id="" />
            <repeatprompts count="1" filename="none" text="none" id="" />
            <repeatprompts count="2" filename="none" text="none" id="" />
            <nomatchprefixes count="1" filename="none" text="none" id="" />
            <nomatchprefixes count="2" filename="none" text="none" id="" />
            <nomatchprefixes count="3" filename="none" text="none" id="" />
            <noinputprefixes count="1" filename="none" text="none" id="" />
            <noinputprefixes count="2" filename="none" text="none" id="" />
            <noinputprefixes count="3" filename="none" text="none" id="" />
            <noinputprompts count="1" bargein="false" filename="none" text="none" id="" />
            <nomatchprompts count="1" bargein="false" filename="none" text="none" id="" />
            <notoconfirmprefixes count="2" filename="none" text="none" id="" />
            <notoconfirmprefixes count="3" filename="none" text="none" id="" />
            <notoconfirmprefixes count="1" bargein="false" filename="none" text="none" id="" />
            <notoconfirmprompts count="1" bargein="false" filename="none" text="none" id="" />
            <notoconfirmprefixes count="2" bargein="false" filename="none" text="none" id="" />
            <notoconfirmprompts count="2" bargein="false" filename="none" text="none" id="" />
        </prompt_configuration>
    </collection_configuration>
    <global_configuration confirmationmode="Never">
        <threshold_configuration maxnomatches="1" maxnoinputs="1" />
        <vxml_properties bargein="false" inputmodes="voice" />
        <failureprompt count="1" filename="none" text="none" id="" />
        <commandconfirmationprompts count="1" bargein="false" filename="none" text="none" id="" />
        <successprompts count="1" filename="none" text="none" id="" />
        <successprompts count="2" filename="none" text="none" id="" />
        <successprompts count="3" filename="none" text="none" id="" />
        <successcorrectedprompt count="1" filename="none" text="none" id="" />
    </global_configuration>
</dm-state>
```

**Output yaml:**
```yaml
- kind: Question
  id: question_REPLACE_THIS
  interruptionPolicy:
    allowInterruption: true

  unrecognizedPrompt:
    speak:
      - nomatch 1
      - nomatch 2

  alwaysPrompt: true
  variable: Global.Var_REPLACE_THIS
  prompt:
    speak:
      - "So... after the tone please say: 'At Riyad Bank my voice is my password'"

  entity: PersonNamePrebuiltEntity
  voiceInputSettings:
    silenceDetectionTimeoutInMilliseconds: 7000
    repeatCountOnSilence: 2
    inputTimeoutResponse:
      speak:
        - Noinput 1
        - Noinput2

    defaultValueMissingAction: Escalate

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.collectionNiOrNm_Temp
  value: undefined

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.collectionNoInputCounter
  value: 0

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.collectionNoMatchCounter
  value: 0

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.repromptAfterTrainError
  value: false

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.isComingFrom_rb0330
  value: false

- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(Global.callerLanguage = "ar", true, false)
      actions:
        - kind: SetVariable
          id: setVariable_REPLACE_THIS
          variable: Global.enrollmentLanguage
          value: ar

  elseActions:
    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Global.callerLanguage = "en")
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.enrollmentLanguage
              value: en

- kind: GotoAction
  id: goto_REPLACE_THIS
  actionId: rb0330_CheckEnrollPassphrase_DS
```


### Example 2:
**Input XML:**
```xml
<dm-state id="au17420_GetMemberIDFromAgent_DM">
        	<properties name="swirec.secure_context" value="1" />
	     	<properties name="switts.secure_context" value="1" />
            <success>
                <action label="default">
                	<session-mapping key="sessionData.IDAuthData.dobInputMode" expr="au17420_GetMemberIDFromAgent_DM.state.getCollection().getResult().getInputMode()" />
                    <session-mapping key="sessionData.IDAuthData.memberIDProvided" path="returnvalue" scope="request" />
                     <if cond="au17420_GetMemberIDFromAgent_DM.state.getConfirmed() == true">
                        <session-mapping key="sessionData.IDAuthData.memberIDConfirmed" value="true" type="Boolean" />
                     </if>
                    <if cond="sessionData.IDAuthData.memberIDEntryAttempts &gt; 0">
                        <action label="for single memberIdEntryAttempts" next="au17440_AuthenticateMemberFromAgent_DB_DA" />
                        <else>
                            <session-mapping value="memberID" key="session.memberData.memberIDType" />
                            <script className="com.nuance.humana.scripts.MemberIDManipulation" />
                            <action label="member id" next="au17430_GetDOBFromAgent_DM" />
                        </else>
                    </if>
                </action>
            </success>
        <collection_configuration confirmationmode="Never" highconfidencelevel="0.021" inputmodes="voice">
        <threshold_configuration maxinvalidanswers="2" maxturns="6" maxnoinputs="3" maxnomatches="3" maxrepeats="2" maxhelps="2" />
        <grammar_configuration commandgrammar="none" dtmfcommandgrammar="none" supportsadaptivegrammar="false" adaptivewordlist="none" adaptivegrammar="none">
            <grammars filename="au17420_GetMemberIDFromAgent_DM.gram" count="1" />
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" timeout="5000ms" incompletetimeout="1500ms" maxspeechtimeout="10000ms" termtimeout="10ms" interdigittimeout="2000ms" inputmodes="voice" />
        <prompt_configuration>
            <initialprompt count="1"><audio><if type="expression" cond="sessionData.IDAuthData.memberIDEntryAttempts == 1">
        	<if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
        		<prompt id="au17420_ini_03"><prompt-segments>
            <audiofile src="au17420_ini_03.wav" text="Please say or enter their member ID again" />
        </prompt-segments>
    </prompt><else>
        		<prompt id="au17420_ini_01"><prompt-segments>
            <audiofile src="au17420_ini_01.wav" text="Please say or enter their Humana member ID again" />
        </prompt-segments>
    </prompt></else></if><else>
           <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
        		<prompt id="au17420_ini_04"><prompt-segments>
            <audiofile src="au17420_ini_04.wav" text="Thanks, now please say or enter the member ID" />
        </prompt-segments>
    </prompt><else>
        		<prompt id="au17420_ini_02"><prompt-segments>
            <audiofile src="au17420_ini_02.wav" text="Thanks, now please say or enter the Humana member ID" />
        </prompt-segments>
    </prompt></else></if></else></if></audio></initialprompt><repeatprompts count="1"><audio><if type="expression" cond="sessionData.IDAuthData.memberIDEntryAttempts == 1">
        	<if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
        		<prompt id="au17420_ini_03"><prompt-segments>
            <audiofile src="au17420_ini_03.wav" text="Please say or enter their member ID again" />
        </prompt-segments>
    </prompt><else>
        		<prompt id="au17420_ini_01"><prompt-segments>
            <audiofile src="au17420_ini_01.wav" text="Please say or enter their Humana member ID again" />
        </prompt-segments>
    </prompt></else></if><else>
           <if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
        		<prompt id="au17420_ini_04"><prompt-segments>
            <audiofile src="au17420_ini_04.wav" text="Thanks, now please say or enter the member ID" />
        </prompt-segments>
    </prompt><else>
        		<prompt id="au17420_ini_02"><prompt-segments>
            <audiofile src="au17420_ini_02.wav" text="Thanks, now please say or enter the Humana member ID" />
        </prompt-segments>
    </prompt></else></if></else></if></audio></repeatprompts><nomatchprompts count="1"><audio><if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
       		<prompt id="au17420_nm1_02"><prompt-segments>
            <audiofile src="au17420_nm1_02.wav" text="Sorry, *what's* the member ID?" />
        </prompt-segments>
    </prompt><else>
       		<prompt id="au17420_nm1_01"><prompt-segments>
            <audiofile src="au17420_nm1_01.wav" text="Sorry, *what's* the Humana ID?" />
        </prompt-segments>
    </prompt></else></if></audio></nomatchprompts><nomatchprompts count="2"><audio><if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
       		<prompt id="au17420_nm2_02"><prompt-segments>
            <audiofile src="au17420_nm2_02.wav" text="Say or enter the member ID" />
        </prompt-segments>
    </prompt><else>
       		<prompt id="au17420_nm2_01"><prompt-segments>
            <audiofile src="au17420_nm2_01.wav" text="Say or enter the member's Humana ID" />
        </prompt-segments>
    </prompt></else></if></audio></nomatchprompts><nomatchprompts count="3" /><noinputprompts count="1"><audio><if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
       		<prompt id="au17420_ni1_02"><prompt-segments>
            <audiofile src="au17420_ni1_02.wav" text="Sorry, *what's* the member ID?" />
        </prompt-segments>
    </prompt><else>
       		<prompt id="au17420_ni1_01"><prompt-segments>
            <audiofile src="au17420_ni1_01.wav" text="Sorry, *what's* the Humana ID?" />
        </prompt-segments>
    </prompt></else></if></audio></noinputprompts><noinputprompts count="2"><audio><if type="expression" cond="sessionData.dnisType == 'CarePlusMember' || sessionData.dnisType == 'CarePlusSelfService'">
       		<prompt id="au17420_ni2_02"><prompt-segments>
            <audiofile src="au17420_ni2_02.wav" text="Say or enter the member ID" />
        </prompt-segments>
    </prompt><else>
       		<prompt id="au17420_ni2_01"><prompt-segments>
            <audiofile src="au17420_ni2_01.wav" text="Say or enter the member's Humana ID" />
        </prompt-segments>
    </prompt></else></if></audio></noinputprompts><noinputprompts count="3" /></prompt_configuration>
    </collection_configuration>
    <global_configuration confirmationmode="Never">
        <failureprompt count="1" /><successprompts count="1" /><successprompts count="2" /><successprompts count="3" /><successcorrectedprompt count="1" /></global_configuration>
</dm-state>
```
**Output yaml:**
```yaml

- kind: Question
  id: au17420_GetMemberIDFromAgent_DM
  interruptionPolicy:
    allowInterruption: true

  unrecognizedPrompt:
    speak:
      - "Sorry, *what's* the member ID?"
      - "Say or enter the member ID"

  alwaysPrompt: true
  variable: Global.Var_REPLACE_THIS
  prompt:
    speak:
      - "Thanks, now please say or enter the member ID"

  entity: PersonNamePrebuiltEntity
  voiceInputSettings:
    silenceDetectionTimeoutInMilliseconds: 7000
    repeatCountOnSilence: 2
    inputTimeoutResponse:
      speak:
        - "Sorry, *what's* the member ID?"
        - "Say or enter the member ID"

    defaultValueMissingAction: Escalate

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.sessionData.IDAuthData.dobInputMode
  value: au17420_GetMemberIDFromAgent_DM.state.getCollection().getResult().getInputMode()

- kind: SetVariable
  id: setVariable_REPLACE_THIS
  variable: Global.sessionData.IDAuthData.memberIDProvided
  value: returnvalue

- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(au17420_GetMemberIDFromAgent_DM.state.getConfirmed() = true, true, false)
      actions:
        - kind: SetVariable
          id: setVariable_REPLACE_THIS
          variable: Global.sessionData.IDAuthData.memberIDConfirmed
          value: true

- kind: ConditionGroup
  id: conditionGroup_REPLACE_THIS
  conditions:
    - id: conditionItem_REPLACE_THIS
      condition: |
        =If(Global.sessionData.IDAuthData.memberIDEntryAttempts > 0, true, false)
      actions:
        - kind: GotoAction
          id: goto_REPLACE_THIS
          actionId: au17440_AuthenticateMemberFromAgent_DB_DA

  elseActions:
    - kind: SetVariable
      id: setVariable_REPLACE_THIS
      variable: Global.session.memberData.memberIDType
      value: memberID

    - kind: GotoAction
      id: goto_REPLACE_THIS
      actionId: au17430_GetDOBFromAgent_DM
```

### Example 2:
**Input XML:**
```xml
<dm-state id="hl0150_VSS_PreMenu_DM"><script className="com.nuance.ps.telefonica.scripts.KPIAddNode" />
            <success>
                <action label="sim" next="hl0010_WhatsNext_DS">
		            <session-mapping key="comingFrom" value="VSS_PreMenu" />
				</action>
				
                <action label="usage">
                	<session-mapping key="comingFrom" value="VSS_PreMenu" />
                	<session-mapping key="transferTo" value="AgentCare" />
                	<if cond="authenticated == 1 || (ivrCLI == ivrIdValue &amp;&amp; ivrIdType == 'MSISDN')">
                	    <action next="hl0120_UsageInfo_SD" />
                	<else>
                	    <action next="hl0010_WhatsNext_DS" audio="hl0150_out_01">
                	        <session-mapping key="hangupRequired" value="true" />
						</action>
                	</else>
                	</if>
                </action>
			</success>
            
			<event name="event.nuance.dialog.ndm.maxnomatches">
			    <action next="hl0010_WhatsNext_DS">
				    <script className="com.nuance.ps.telefonica.scripts.SetReturnValues" />
				    <session-mapping key="comingFrom" value="VSS_PreMenu" />
			    </action>
			</event>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
			    <action next="hl0010_WhatsNext_DS">
				    <script className="com.nuance.ps.telefonica.scripts.SetReturnValues" />
				    <session-mapping key="comingFrom" value="VSS_PreMenu" />
			    </action>
			</event>
        	<event name="error_CS" next="hl0020_TransferToAgent_PP">
        	    <session-mapping key="goToAgent" value="true" />
        	    <session-mapping key="comingFrom" value="VSS_PreMenu" />
        	</event>
        <collection_configuration confirmationmode="Never" highconfidencelevel="0.021" inputmodes="voice">
		<threshold_configuration maxinvalidanswers="2" maxturns="6" maxnoinputs="3" maxnomatches="3" maxrepeats="2" maxhelps="2" />
		<grammar_configuration commandgrammar="none" dtmfcommandgrammar="none" supportsadaptivegrammar="false" adaptivewordlist="none" adaptivegrammar="none">					
			<grammars filename="hl0150_VSS_PreMenu_DM.gram" count="1" />
		</grammar_configuration>
		<vxml_properties confidencelevel="0.450" timeout="5000ms" incompletetimeout="1500ms" maxspeechtimeout="10000ms" termtimeout="10ms" interdigittimeout="2000ms" inputmodes="voice" />
		<prompt_configuration>
            <initialprompt count="1"><audio><prompt id="hl0150_ini_01"><prompt-segments>
            <audiofile src="hl0150_ini_01.wav" text="Worum geht es bei Ihrem Anruf? Um eine Verbrauchsinformation - oder um Ihre SIM-Karte?" />
        </prompt-segments>
    </prompt></audio></initialprompt><repeatprompts count="1"><audio><prompt id="hl0150_ini_01"><prompt-segments>
            <audiofile src="hl0150_ini_01.wav" text="Worum geht es bei Ihrem Anruf? Um eine Verbrauchsinformation - oder um Ihre SIM-Karte?" />
        </prompt-segments>
    </prompt></audio></repeatprompts><repeatprompts count="2" /><nomatchprefixes count="1" /><nomatchprefixes count="2" /><nomatchprefixes count="3" /><noinputprefixes count="1" /><noinputprefixes count="2" /><noinputprefixes count="3" /><noinputprompts count="1"><audio><prompt id="hl0150_retry1_01"><prompt-segments>
            <audiofile src="hl0150_retry1_01.wav" text="F�r Fragen zu Ihrem Verbrauch sagen Sie bitte - 'Verbrauchsinfo' und bei Anliegen rund um Ihre SIM-Karte sagen Sie - 'SIM'" />
        </prompt-segments>
    </prompt></audio></noinputprompts><noinputprompts count="2"><audio><prompt id="gl_ni2_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht h�ren" src="gl_ni2_01.wav" />
        </prompt-segments>
    </prompt><prompt id="hl0150_retry2_01"><prompt-segments>
            <audiofile src="hl0150_retry2_01.wav" text="Bitte w�hlen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM" />
        </prompt-segments>
    </prompt></audio></noinputprompts><noinputprompts count="3"><audio><prompt id="gl_ni3_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht h�ren" src="gl_ni3_01.wav" />
        </prompt-segments>
    </prompt><prompt id="hl0150_retry2_01"><prompt-segments>
            <audiofile src="hl0150_retry2_01.wav" text="Bitte w�hlen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM" />
        </prompt-segments>
    </prompt></audio></noinputprompts><nomatchprompts count="1"><audio><prompt id="gl_nm1_01"><prompt-segments>
            <audiofile text="Entschuldigung" src="gl_nm1_01.wav" />
        </prompt-segments>
    </prompt><prompt id="hl0150_retry1_01"><prompt-segments>
            <audiofile src="hl0150_retry1_01.wav" text="F�r Fragen zu Ihrem Verbrauch sagen Sie bitte - 'Verbrauchsinfo' und bei Anliegen rund um Ihre SIM-Karte sagen Sie - 'SIM'" />
        </prompt-segments>
    </prompt></audio></nomatchprompts><nomatchprompts count="2"><audio><prompt id="gl_nm2_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht verstehen" src="gl_nm2_01.wav" />
        </prompt-segments>
    </prompt><prompt id="hl0150_retry2_01"><prompt-segments>
            <audiofile src="hl0150_retry2_01.wav" text="Bitte w�hlen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM" />
        </prompt-segments>
    </prompt></audio></nomatchprompts><nomatchprompts count="3"><audio><prompt id="gl_nm3_01"><prompt-segments>
            <audiofile text="Ich konnte Sie erneut nicht verstehen" src="gl_nm3_01.wav" />
        </prompt-segments>
    </prompt><prompt id="hl0150_retry2_01"><prompt-segments>
            <audiofile src="hl0150_retry2_01.wav" text="Bitte w�hlen Sie aus folgenden Alternativen Verbrauchsinfos - oder - SIM" />
        </prompt-segments>
    </prompt></audio></nomatchprompts><notoconfirmprefixes count="2" /><notoconfirmprefixes count="3" /><notoconfirmprefixes count="1" /><notoconfirmprompts count="1" /></prompt_configuration>
	</collection_configuration>
    <global_configuration confirmationmode="Never">
        <failureprompt count="1" /><successprompts count="1" /><successprompts count="2" /><successprompts count="3" /><successcorrectedprompt count="1" /></global_configuration>
</dm-state>
</root>
```

**Output yaml:**
```yaml
    - kind: Question
      id: hl0150_VSS_PreMenu_DM
      interruptionPolicy:
        allowInterruption: true

      unrecognizedPrompt:
        speak:
          - Entschuldigung
          - Ich konnte Sie erneut nicht verstehen

      alwaysPrompt: true
      variable: Global.Var_REPLACE_THIS
      prompt:
        speak:
          - Worum geht es bei Ihrem Anruf? Um eine Verbrauchsinformation - oder um Ihre SIM-Karte?

      entity: PersonNamePrebuiltEntity
      voiceInputSettings:
        silenceDetectionTimeoutInMilliseconds: 7000
        repeatCountOnSilence: 2
        inputTimeoutResponse:
          speak:
            - Ich konnte Sie erneut nicht h�ren
            - F�r Fragen zu Ihrem Verbrauch sagen Sie bitte - 'Verbrauchsinfo' und bei Anliegen rund um Ihre SIM-Karte sagen Sie - 'SIM'

        defaultValueMissingAction: Escalate

    - kind: ConditionGroup
      id: conditionGroup_REPLACE_THIS
      conditions:
        - id: conditionItem_REPLACE_THIS
          condition: |
            =If(Global.label = "sim", true, false)
          actions:
            - kind: SetVariable
              id: setVariable_REPLACE_THIS
              variable: Global.comingFrom
              value: VSS_PreMenu
            - kind: GotoAction
              id: goto_REPLACE_THIS
              actionId: hl0010_WhatsNext_DS

      elseActions:
        - kind: ConditionGroup
          id: conditionGroup_REPLACE_THIS
          conditions:
            - id: conditionItem_REPLACE_THIS
              condition: |
                =If(Global.label = "usage", true, false)
              actions:
                - kind: SetVariable
                  id: setVariable_usDhbi
                  variable: Global.comingFrom
                  value: VSS_PreMenu

                - kind: SetVariable
                  id: setVariable_REPLACE_THIS
                  variable: Global.transferTo
                  value: AgentCare

                - kind: ConditionGroup
                  id: conditionGroup_REPLACE_THIS
                  conditions:
                    - id: conditionItem_REPLACE_THIS
                      condition: |
                        =If(Or(Global.authenticated = 1 , Global.ivrCLI = "ivrIdValue"),And ( Global.ivrIdType = "MSISDN"), true, false)
                      actions:
                        - kind: BeginDialog
                          id: begin_REPLACE_THIS
                          dialog: topic.hl0120_UsageInfo_SD

                  elseActions:
                    - kind: SetVariable
                      id: setVariable_REPLACE_THIS
                      variable: Global.hangupRequired
                      value: true
                    - kind: GotoAction
                      id: xfCP5e
                      actionId: hl0010_WhatsNext_DS
```