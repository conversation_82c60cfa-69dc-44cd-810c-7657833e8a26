<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="MDNChange_Main_Dialog">
    <decision-state id="MC0010_CheckContext_DS">
      <session-mapping key="careMdnChangeBroadcastMessagePlayed" value="GlobalVars.careMdnChangeBroadcastMessagePlayed != undefined ? GlobalVars.careMdnChangeBroadcastMessagePlayed : false" type="String"/>
      <if cond="(careMdnChangeBroadcastMessagePlayed == false)">
        <action next="MC0990_GoToBroadcastMessages_SD"/>
        <else>
          <action next="MC1000_MDNChangeIntro_PP"/>
        </else>
      </if>
    </decision-state>

    <subdialog-state id="MC0990_GoToBroadcastMessages_SD">
      <session-mapping key="GlobalVars.broadcastMessageKey" expr="'MC'"/>
      <gotodialog next="BroadcastMessages_Dialog"/>
      <action next="MC0990_GoToBroadcastMessages_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MC0990_GoToBroadcastMessages_SD_return_CS">
      <action next="getReturnLink()"/>
    </custom-state>

    <play-state id="MC1000_MDNChangeIntro_PP">
      <audio>
        <prompt id="MC1000_out_01">
          <prompt-segments>
            <audiofile text="Now let me see which area codes are available for the zip code we have on file for you" src="MC1000_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="MC1010_LookupNPANXX_DB_DA"/>
    </play-state>

    <data-access-state id="MC1010_LookupNPANXX_DB_DA">
      <session-mapping key="ani" value="GlobalVars.trn" type="String"/>
      <session-mapping key="did" value="dnis" type="String"/>
      <session-mapping key="marketId" value="GlobalVars.marketID" type="String"/>
      <session-mapping key="zipCode" value="GlobalVars.zipCode" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="networkType" value="GlobalVars.networkType" type="String"/>
      <data-access id="GetNPANXX" classname="com.nuance.metro.dataaccess.GetNPANXX">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="zipCode" mask="true"/>
          <input-variable name="sessionId"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
          <output-variable name="availableNpaList"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="GetNPANXX.status == 'Success'">
          <session-mapping key="GlobalVars.GetNPANXX" expr="GetNPANXX"/>
          <session-mapping key="GlobalVars.oldNum" expr="GlobalVars.mdn"/>
          <action next="MC1020_CheckAreaCodesAvailable_JDA"/>
          <else>
            <session-mapping key="GlobalVars.transferReason" expr="'dbfail'"/>
            <session-mapping key="GlobalVars.playTransferMessage" value="true" type="Boolean"/>
            <action next="MC1090_CallTransfer_SD"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <decision-state id="MC1020_CheckAreaCodesAvailable_DS">
      <if cond="typeof(GlobalVars.GetNPANXX.availableNpaList) != 'undefined' &amp;&amp; GlobalVars.GetNPANXX.availableNpaList.length == 1">
        <session-mapping key="GlobalVars.NPA" expr="GlobalVars.GetNPANXX.availableNpaList[0].npa"/>
        <session-mapping key="GlobalVars.NXX" expr="GlobalVars.GetNPANXX.availableNpaList[0].nxx"/>
        <action next="MC1030_ConfirmSingleAreaCodeYN_DM"/>
        <elseif cond="typeof(GlobalVars.GetNPANXX.availableNpaList) != 'undefined' &amp;&amp; GlobalVars.GetNPANXX.availableNpaList.length &gt; 1">
          <action next="MC1050_SelectAreaCode_DM"/>
        </elseif>
        <else>
          <action next="MC1090_CallTransfer_SD"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="MC1030_ConfirmSingleAreaCodeYN_DM" type="YSNO">
      <session-mapping key="areaCode" value="GlobalVars.NPA" type="String"/>
      <success>
        <action label="true" next="MC1070_MDNChangeMsg_PP"/>
        <action label="false" next="MC1090_CallTransfer_SD">
          <audio>
            <prompt id="MC1050_out_01">
              <prompt-segments>
                <audiofile text="That s fine" src="MC1050_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MC1050_ini_01">
                <prompt-segments>
                  <audiofile text="It looks as though, there's one area code I can offer you - it's " src="MC1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="number" expr="areaCode">
                <param value="digits" name="speakAs"/>
                <param value="true" name="PlayZeroUnits"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1050_ini_02">
                <prompt-segments>
                  <audiofile text="Can I go ahead and use that one?" src="MC1050_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="MC1030_ConfirmSingleAreaCodeYN_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MC1050_ini_01">
                <prompt-segments>
                  <audiofile text="It looks as though, there's one area code I can offer you - it's " src="MC1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="number" expr="areaCode">
                <param value="digits" name="speakAs"/>
                <param value="true" name="PlayZeroUnits"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1050_ini_02">
                <prompt-segments>
                  <audiofile text="Can I go ahead and use that one?" src="MC1050_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1050_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   Is that the area code you d like for your new phone number?" src="MC1050_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1050_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like that area code for your new phone number say  yes  or press 1  If you want a different area code, say  no  or press 2" src="MC1050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1050_nm3_01">
                <prompt-segments>
                  <audiofile text="If you like that area code for your new phone number say  yes  or press 1  If you want a different area code, say  no  or press 2" src="MC1050_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1050_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say  yes  or  no   Is that the area code you d like for your new phone number?" src="MC1050_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1050_nm2_01">
                <prompt-segments>
                  <audiofile text="If you d like that area code for your new phone number say  yes  or press 1  If you want a different area code, say  no  or press 2" src="MC1050_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1050_nm3_01">
                <prompt-segments>
                  <audiofile text="If you like that area code for your new phone number say  yes  or press 1  If you want a different area code, say  no  or press 2" src="MC1050_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MC1050_ini_01">
                <prompt-segments>
                  <audiofile text="It looks as though, there's one area code I can offer you - it's " src="MC1050_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt type="number" expr="areaCode">
                <param value="digits" name="speakAs"/>
                <param value="true" name="PlayZeroUnits"/>
                <param value="m" name="intonation"/>
              </prompt>
              <prompt id="silence_250ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_250ms.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1050_ini_02">
                <prompt-segments>
                  <audiofile text="Can I go ahead and use that one?" src="MC1050_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="MC1030_ConfirmSingleAreaCodeYN_DM.grxml" count="1"/>
          <dtmfgrammars filename="MC1030_ConfirmSingleAreaCodeYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="MC1040_ListAreaCodes_PP">
      <session-mapping key="availableNpaList" value="GlobalVars.areaCodeList" type="String"/>
      <session-mapping key="areaCodesToPlay" value="GlobalVars.areaCodesToPlay" type="String"/>
      <audio>
        <prompt type="custom" expr="areaCodesToPlay">
          <param name="className" value="com.nuance.metro.audio.custom.PlayAvailableAreaCodesMDNChange"/>
          <param name="variableName" value="areaCodesToPlay"/>
          <param name="variableScope" value="request"/>
        </prompt>
      </audio>
      <session-mapping key="comingFromMC1040" value="true" type="Boolean"/>
      <action next="MC1050_SelectAreaCode_DM"/>
    </play-state>

    <dm-state id="MC1050_SelectAreaCode_DM" type="CUST">
      <session-mapping key="areaCode" value="GlobalVars.GetAccountDetails.mdn.substring(0,3)" type="String"/>
      <session-mapping key="AllowedAreaCodes" value="GlobalVars.areaCodeList" type="String"/>
      <session-mapping key="AllowedAreaCodesSize" value="GlobalVars.areaCodeList.length" type="String"/>
      <session-mapping key="listSize" value="GlobalVars.GetNPANXX.availableNpaList.length" type="String"/>
      <session-mapping key="AreaCodeAnNPA" value="GlobalVars.AreaCodeAnNPA" type="String"/>
      <session-mapping key="keepCurrentOption" value="GlobalVars.keepCurrentOption" type="String"/>
      <session-mapping key="randomKey" value="0" type="String"/>
      <session-mapping key="availableNpaList" value="GlobalVars.areaCodeList" type="String"/>
      <session-mapping key="areaCodesToPlay" value="GlobalVars.areaCodesToPlay" type="String"/>
      <session-mapping key="currentOptionMatchingNXX" value="" type="String"/>
      <success>
        <action label="list_them" next="MC1040_ListAreaCodes_PP">
          <audio>
            <prompt id="MC1050_out_02">
              <prompt-segments>
                <audiofile text="When you hear the one you want, just say it" src="MC1050_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="wantList" value="true" type="Boolean"/>
        </action>
        <action label="keep_current" next="MC1070_MDNChangeMsg_PP">
          <session-mapping key="GlobalVars.NXX" expr="currentOptionMatchingNXX"/>
        </action>
        <action label="random" next="MC1070_MDNChangeMsg_PP">
          <session-mapping key="randomKey" expr="Math.floor(Math.random() * listSize)"/>
          <session-mapping key="GlobalVars.NPA" expr="GlobalVars.GetNPANXX.availableNpaList[randomKey].npa"/>
          <session-mapping key="GlobalVars.NXX" expr="GlobalVars.GetNPANXX.availableNpaList[randomKey].nxx"/>
        </action>
        <action label="area-code_none">
          <action next="MC1090_CallTransfer_SD"/>
        </action>
        <action label="default" next="MC1070_MDNChangeMsg_PP">
          <session-mapping key="GlobalVars.NPA" expr="MC1050_SelectAreaCode_DM.returnvalue"/>
          <session-mapping key="comingFromMC1040" value="false" type="Boolean"/>
        </action>
        <action label="operator"/>
        <action label="repeat">
          <session-mapping key="repeatAtMC1050" value="true" type="Boolean"/>
          <action next="MC1040_ListAreaCodes_PP"/>
        </action>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="AreaCodeAnNPA == true &amp;&amp; comingFromMC1040 == false">
                <prompt id="MC1050_ini_04" cond="listSize ==2">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code' or say" src="MC1050_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_05" cond="listSize == 3 || listSize == 4">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code', or, when you hear the one you want, just say it" src="MC1050_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_06" cond="listSize &gt; 5 || listSize == 5">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code'" src="MC1050_ini_06.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_07" cond="listSize == 5">
                  <prompt-segments>
                    <audiofile text="plus there're 4 others available" src="MC1050_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_08" cond="listSize == 6">
                  <prompt-segments>
                    <audiofile text="plus there're 5 others available" src="MC1050_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_09" cond="listSize == 7">
                  <prompt-segments>
                    <audiofile text="plus there're 6 others available" src="MC1050_ini_09.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_10" cond="listSize == 8">
                  <prompt-segments>
                    <audiofile text="plus there're 7 others available" src="MC1050_ini_10.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_11" cond="listSize == 9">
                  <prompt-segments>
                    <audiofile text="plus there're 8 others available" src="MC1050_ini_11.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_12" cond="listSize == 10">
                  <prompt-segments>
                    <audiofile text="plus there're 9 others available" src="MC1050_ini_12.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_13" cond="listSize == 11">
                  <prompt-segments>
                    <audiofile text="plus there're 10 others available" src="MC1050_ini_13.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_14" cond="listSize &gt; 11">
                  <prompt-segments>
                    <audiofile text="plus there're more than 10 others available" src="MC1050_ini_14.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_22" cond="listSize &gt; 5 || listSize == 5">
                  <prompt-segments>
                    <audiofile text="So if you'd rather get a new one,  say 'choose for me' or 'list them all'" src="MC1050_ini_22.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="AreaCodeAnNPA == false &amp;&amp; comingFromMC1040 == false">
                  <prompt id="MC1050_ini_15" cond="listSize == 5">
                    <prompt-segments>
                      <audiofile text="There're 5 area codes available" src="MC1050_ini_15.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_16" cond="listSize == 6">
                    <prompt-segments>
                      <audiofile text="There're 6 area codes available" src="MC1050_ini_16.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_17" cond="listSize == 7">
                    <prompt-segments>
                      <audiofile text="There're 7 area codes available" src="MC1050_ini_17.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_18" cond="listSize == 8">
                    <prompt-segments>
                      <audiofile text="There're 8 area codes available" src="MC1050_ini_18.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_19" cond="listSize == 9">
                    <prompt-segments>
                      <audiofile text="There're 9 area codes available" src="MC1050_ini_19.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_20" cond="listSize == 10">
                    <prompt-segments>
                      <audiofile text="There're 10 area codes available" src="MC1050_ini_20.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_21" cond="listSize &gt; 10">
                    <prompt-segments>
                      <audiofile text="There're more than 10 area codes available" src="MC1050_ini_21.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_23" cond="listSize &gt; 5 || listSize == 5">
                    <prompt-segments>
                      <audiofile text="You can say 'choose for me' or 'list them all'" src="MC1050_ini_23.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_24" cond="listSize &lt; 5">
                    <prompt-segments>
                      <audiofile text="When you hear the area code you want, just say it" src="MC1050_ini_24.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <if cond="listSize &lt; 5 &amp;&amp; comingFromMC1040 == false">
                <prompt type="custom" expr="areaCodesToPlay">
                  <param name="className" value="com.nuance.metro.audio.custom.PlayAvailableAreaCodesMDNChange"/>
                  <param name="variableName" value="areaCodesToPlay"/>
                  <param name="variableScope" value="request"/>
                </prompt>
                <prompt id="silence_250ms" cond="AllowedAreaCodesSize == 2 &amp;&amp; AreaCodeAnNPA == true">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_27">
                  <prompt-segments>
                    <audiofile text="You can also say 'choose for me', or to hear those again, say 'repeat'" src="MC1050_ini_27.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="MC1050_SelectAreaCode_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <if cond="AreaCodeAnNPA == true">
                <prompt id="MC1050_nm1_02" cond="listSize ==2">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code', or say or enter " src="MC1050_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm1_03" cond="listSize == 3 || listSize == 4">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code', or, when you hear the area code you want, just say it back to me or enter it" src="MC1050_nm1_03.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm1_04" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="When you hear the new area code you want, just say it back to me or enter it" src="MC1050_nm1_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm1_06" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="You can also say 'keep my area code', or 'choose for me' Here are the available area codes again " src="MC1050_nm1_06.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm1_07" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code', or, if you'd like me to pick an area code at random for you, say 'choose for me' To hear all the available area codes, say 'list them all'" src="MC1050_nm1_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MC1050_nm1_08" cond="listSize &lt; 5">
                    <prompt-segments>
                      <audiofile text="When you hear the area code you want, just say it back to me or enter it" src="MC1050_nm1_08.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm1_09" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="When you hear the area code you want, just say it back to me or enter it" src="MC1050_nm1_09.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm1_11" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="You can also say 'choose for me' Here are the available area codes again" src="MC1050_nm1_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm1_12" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                    <prompt-segments>
                      <audiofile text="If you'd like me to pick an area code at random for you, say 'choose for me' To hear all the available area codes, say 'list them all'" src="MC1050_nm1_12.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="areaCodesToPlay" cond="!(listSize &gt;= 5 &amp;&amp; wantList == false)">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAvailableAreaCodesMDNChange"/>
                <param name="variableName" value="areaCodesToPlay"/>
                <param name="variableScope" value="request"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <if cond="AreaCodeAnNPA == true">
                <prompt id="MC1050_nm2_02" cond="listSize ==2">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as just one other If you'd like to keep the area code you have now, say 'keep my area code' Otherwise, say or enter " src="MC1050_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_03" cond="listSize == 3 || listSize == 4 ">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as a few others If you want to keep the area code you have now, say 'keep my area code' Otherwise, when you hear the area code you want, just say it back to me, like 5-1-4, or enter it on your keypad" src="MC1050_nm2_03.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_04" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="When you hear the new area code you want, please say it back to me, like 5-1-4, or enter it on your keypad" src="MC1050_nm2_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_06" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="You can also say 'keep my area code' or 'choose for me' Here are the available area codes again " src="MC1050_nm2_06.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_07" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as several others If you want your new phone number to start with your current area code, say 'keep my area code' If you'd like me to pick a *new* area code at random for you, say 'choose for me', or, to hear all the available area codes, say 'list them all' " src="MC1050_nm2_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="AreaCodeAnNPA == false">
                  <prompt id="MC1050_nm2_08" cond="listSize &lt; 5">
                    <prompt-segments>
                      <audiofile text="I will list the area codes available to you When you hear the one you want, just say it back to me, like 5-1-4 or enter it on your keypad " src="MC1050_nm2_08.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_09" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="I will list the area codes available to you When you hear the one you want, just say it back to me, like 5-1-4 or enter it on your keypad " src="MC1050_nm2_09.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_11" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="You can also say 'choose for me' Here are the available area codes again " src="MC1050_nm2_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_12" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                    <prompt-segments>
                      <audiofile text="I can either give you a new area code at random, or you can choose one yourself If you'd like me to pick one for you, say 'choose for me' If you want to hear the list of all the area codes available to you, say 'list them all' " src="MC1050_nm2_12.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt type="custom" expr="areaCodesToPlay" cond="!(listSize &gt;= 5 &amp;&amp; wantList == false)">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAvailableAreaCodesMDNChange"/>
                <param name="variableName" value="areaCodesToPlay"/>
                <param name="variableScope" value="request"/>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <if cond="AreaCodeAnNPA == true">
                <prompt id="MC1050_nm2_02" cond="listSize ==2">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as just one other If you'd like to keep the area code you have now, say 'keep my area code' Otherwise, say or enter " src="MC1050_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_03" cond="listSize == 3 || listSize == 4 ">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as a few others If you want to keep the area code you have now, say 'keep my area code' Otherwise, when you hear the area code you want, just say it back to me, like 5-1-4, or enter it on your keypad" src="MC1050_nm2_03.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_04" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="When you hear the new area code you want, please say it back to me, like 5-1-4, or enter it on your keypad" src="MC1050_nm2_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_06" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="You can also say 'keep my area code' or 'choose for me' Here are the available area codes again " src="MC1050_nm2_06.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_07" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as several others If you want your new phone number to start with your current area code, say 'keep my area code' If you'd like me to pick a *new* area code at random for you, say 'choose for me', or, to hear all the available area codes, say 'list them all' " src="MC1050_nm2_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="AreaCodeAnNPA == false">
                  <prompt id="MC1050_nm2_08" cond="listSize &lt; 5">
                    <prompt-segments>
                      <audiofile text="I will list the area codes available to you When you hear the one you want, just say it back to me, like 5-1-4 or enter it on your keypad " src="MC1050_nm2_08.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_09" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="I will list the area codes available to you When you hear the one you want, just say it back to me, like 5-1-4 or enter it on your keypad " src="MC1050_nm2_09.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_11" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="You can also say 'choose for me' Here are the available area codes again " src="MC1050_nm2_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_12" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                    <prompt-segments>
                      <audiofile text="I can either give you a new area code at random, or you can choose one yourself If you'd like me to pick one for you, say 'choose for me' If you want to hear the list of all the area codes available to you, say 'list them all' " src="MC1050_nm2_12.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt type="custom" expr="areaCodesToPlay" cond="!(listSize &gt;= 5 &amp;&amp; wantList == false)">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAvailableAreaCodesMDNChange"/>
                <param name="variableName" value="areaCodesToPlay"/>
                <param name="variableScope" value="request"/>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="AreaCodeAnNPA == true">
                <prompt id="MC1050_nm1_02" cond="listSize ==2">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code', or say or enter " src="MC1050_nm1_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm1_03" cond="listSize == 3 || listSize == 4">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code', or, when you hear the area code you want, just say it back to me or enter it" src="MC1050_nm1_03.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm1_04" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="When you hear the new area code you want, just say it back to me or enter it" src="MC1050_nm1_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm1_06" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="You can also say 'keep my area code', or 'choose for me' Here are the available area codes again " src="MC1050_nm1_06.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm1_07" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code', or, if you'd like me to pick an area code at random for you, say 'choose for me' To hear all the available area codes, say 'list them all'" src="MC1050_nm1_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="MC1050_nm1_08" cond="listSize &lt; 5">
                    <prompt-segments>
                      <audiofile text="When you hear the area code you want, just say it back to me or enter it" src="MC1050_nm1_08.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm1_09" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="When you hear the area code you want, just say it back to me or enter it" src="MC1050_nm1_09.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm1_11" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="You can also say 'choose for me' Here are the available area codes again" src="MC1050_nm1_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm1_12" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                    <prompt-segments>
                      <audiofile text="If you'd like me to pick an area code at random for you, say 'choose for me' To hear all the available area codes, say 'list them all'" src="MC1050_nm1_12.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt type="custom" expr="areaCodesToPlay" cond="!(listSize &gt;= 5 &amp;&amp; wantList == false)">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAvailableAreaCodesMDNChange"/>
                <param name="variableName" value="areaCodesToPlay"/>
                <param name="variableScope" value="request"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="AreaCodeAnNPA == true">
                <prompt id="MC1050_nm2_02" cond="listSize ==2">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as just one other If you'd like to keep the area code you have now, say 'keep my area code' Otherwise, say or enter " src="MC1050_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_03" cond="listSize == 3 || listSize == 4 ">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as a few others If you want to keep the area code you have now, say 'keep my area code' Otherwise, when you hear the area code you want, just say it back to me, like 5-1-4, or enter it on your keypad" src="MC1050_nm2_03.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_04" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="When you hear the new area code you want, please say it back to me, like 5-1-4, or enter it on your keypad" src="MC1050_nm2_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_06" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="You can also say 'keep my area code' or 'choose for me' Here are the available area codes again " src="MC1050_nm2_06.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_07" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as several others If you want your new phone number to start with your current area code, say 'keep my area code' If you'd like me to pick a *new* area code at random for you, say 'choose for me', or, to hear all the available area codes, say 'list them all' " src="MC1050_nm2_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="AreaCodeAnNPA == false">
                  <prompt id="MC1050_nm2_08" cond="listSize &lt; 5">
                    <prompt-segments>
                      <audiofile text="I will list the area codes available to you When you hear the one you want, just say it back to me, like 5-1-4 or enter it on your keypad " src="MC1050_nm2_08.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_09" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="I will list the area codes available to you When you hear the one you want, just say it back to me, like 5-1-4 or enter it on your keypad " src="MC1050_nm2_09.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_11" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="You can also say 'choose for me' Here are the available area codes again " src="MC1050_nm2_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_12" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                    <prompt-segments>
                      <audiofile text="I can either give you a new area code at random, or you can choose one yourself If you'd like me to pick one for you, say 'choose for me' If you want to hear the list of all the area codes available to you, say 'list them all' " src="MC1050_nm2_12.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt type="custom" expr="areaCodesToPlay" cond="!(listSize &gt;= 5 &amp;&amp; wantList == false)">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAvailableAreaCodesMDNChange"/>
                <param name="variableName" value="areaCodesToPlay"/>
                <param name="variableScope" value="request"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <if cond="AreaCodeAnNPA == true">
                <prompt id="MC1050_nm2_02" cond="listSize ==2">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as just one other If you'd like to keep the area code you have now, say 'keep my area code' Otherwise, say or enter " src="MC1050_nm2_02.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_03" cond="listSize == 3 || listSize == 4 ">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as a few others If you want to keep the area code you have now, say 'keep my area code' Otherwise, when you hear the area code you want, just say it back to me, like 5-1-4, or enter it on your keypad" src="MC1050_nm2_03.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_04" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="When you hear the new area code you want, please say it back to me, like 5-1-4, or enter it on your keypad" src="MC1050_nm2_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_06" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                  <prompt-segments>
                    <audiofile text="You can also say 'keep my area code' or 'choose for me' Here are the available area codes again " src="MC1050_nm2_06.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_nm2_07" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                  <prompt-segments>
                    <audiofile text="Your current area code is available, as well as several others If you want your new phone number to start with your current area code, say 'keep my area code' If you'd like me to pick a *new* area code at random for you, say 'choose for me', or, to hear all the available area codes, say 'list them all' " src="MC1050_nm2_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="AreaCodeAnNPA == false">
                  <prompt id="MC1050_nm2_08" cond="listSize &lt; 5">
                    <prompt-segments>
                      <audiofile text="I will list the area codes available to you When you hear the one you want, just say it back to me, like 5-1-4 or enter it on your keypad " src="MC1050_nm2_08.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_09" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="I will list the area codes available to you When you hear the one you want, just say it back to me, like 5-1-4 or enter it on your keypad " src="MC1050_nm2_09.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="silence_500ms" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="test" src="silence_500ms.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_11" cond="listSize &gt;= 5 &amp;&amp; wantList == true">
                    <prompt-segments>
                      <audiofile text="You can also say 'choose for me' Here are the available area codes again " src="MC1050_nm2_11.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_nm2_12" cond="listSize &gt;= 5 &amp;&amp; wantList == false">
                    <prompt-segments>
                      <audiofile text="I can either give you a new area code at random, or you can choose one yourself If you'd like me to pick one for you, say 'choose for me' If you want to hear the list of all the area codes available to you, say 'list them all' " src="MC1050_nm2_12.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <prompt type="custom" expr="areaCodesToPlay" cond="!(listSize &gt;= 5 &amp;&amp; wantList == false)">
                <param name="className" value="com.nuance.metro.audio.custom.PlayAvailableAreaCodesMDNChange"/>
                <param name="variableName" value="areaCodesToPlay"/>
                <param name="variableScope" value="request"/>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <if cond="AreaCodeAnNPA == true &amp;&amp; comingFromMC1040 == false">
                <prompt id="MC1050_ini_04" cond="listSize ==2">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code' or say" src="MC1050_ini_04.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_05" cond="listSize == 3 || listSize == 4">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code', or, when you hear the one you want, just say it" src="MC1050_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_06" cond="listSize &gt; 5 || listSize == 5">
                  <prompt-segments>
                    <audiofile text="You can say 'keep my area code'" src="MC1050_ini_06.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_07" cond="listSize == 5">
                  <prompt-segments>
                    <audiofile text="plus there're 4 others available" src="MC1050_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_08" cond="listSize == 6">
                  <prompt-segments>
                    <audiofile text="plus there're 5 others available" src="MC1050_ini_08.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_09" cond="listSize == 7">
                  <prompt-segments>
                    <audiofile text="plus there're 6 others available" src="MC1050_ini_09.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_10" cond="listSize == 8">
                  <prompt-segments>
                    <audiofile text="plus there're 7 others available" src="MC1050_ini_10.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_11" cond="listSize == 9">
                  <prompt-segments>
                    <audiofile text="plus there're 8 others available" src="MC1050_ini_11.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_12" cond="listSize == 10">
                  <prompt-segments>
                    <audiofile text="plus there're 9 others available" src="MC1050_ini_12.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_13" cond="listSize == 11">
                  <prompt-segments>
                    <audiofile text="plus there're 10 others available" src="MC1050_ini_13.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_14" cond="listSize &gt; 11">
                  <prompt-segments>
                    <audiofile text="plus there're more than 10 others available" src="MC1050_ini_14.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_22" cond="listSize &gt; 5 || listSize == 5">
                  <prompt-segments>
                    <audiofile text="So if you'd rather get a new one,  say 'choose for me' or 'list them all'" src="MC1050_ini_22.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="AreaCodeAnNPA == false &amp;&amp; comingFromMC1040 == false">
                  <prompt id="MC1050_ini_15" cond="listSize == 5">
                    <prompt-segments>
                      <audiofile text="There're 5 area codes available" src="MC1050_ini_15.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_16" cond="listSize == 6">
                    <prompt-segments>
                      <audiofile text="There're 6 area codes available" src="MC1050_ini_16.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_17" cond="listSize == 7">
                    <prompt-segments>
                      <audiofile text="There're 7 area codes available" src="MC1050_ini_17.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_18" cond="listSize == 8">
                    <prompt-segments>
                      <audiofile text="There're 8 area codes available" src="MC1050_ini_18.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_19" cond="listSize == 9">
                    <prompt-segments>
                      <audiofile text="There're 9 area codes available" src="MC1050_ini_19.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_20" cond="listSize == 10">
                    <prompt-segments>
                      <audiofile text="There're 10 area codes available" src="MC1050_ini_20.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_21" cond="listSize &gt; 10">
                    <prompt-segments>
                      <audiofile text="There're more than 10 area codes available" src="MC1050_ini_21.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_23" cond="listSize &gt; 5 || listSize == 5">
                    <prompt-segments>
                      <audiofile text="You can say 'choose for me' or 'list them all'" src="MC1050_ini_23.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="MC1050_ini_24" cond="listSize &lt; 5">
                    <prompt-segments>
                      <audiofile text="When you hear the area code you want, just say it" src="MC1050_ini_24.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <if cond="listSize &lt; 5 &amp;&amp; comingFromMC1040 == false">
                <prompt type="custom" expr="areaCodesToPlay">
                  <param name="className" value="com.nuance.metro.audio.custom.PlayAvailableAreaCodesMDNChange"/>
                  <param name="variableName" value="areaCodesToPlay"/>
                  <param name="variableScope" value="request"/>
                </prompt>
                <prompt id="silence_250ms" cond="AllowedAreaCodesSize == 2 &amp;&amp; AreaCodeAnNPA == true">
                  <prompt-segments>
                    <audiofile text="test" src="silence_250ms.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_27">
                  <prompt-segments>
                    <audiofile text="You can also say 'choose for me', or to hear those again, say 'repeat'" src="MC1050_ini_27.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <if cond="comingFromMC1040 == true">
                <prompt id="MC1050_ini_27" cond="listSize &lt; 5 &amp;&amp; AreaCodeAnNPA == true">
                  <prompt-segments>
                    <audiofile text="You can also say 'choose for me', or to hear those again, say 'repeat'" src="MC1050_ini_27.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_25" cond="listSize &gt;= 5 &amp;&amp; AreaCodeAnNPA == true">
                  <prompt-segments>
                    <audiofile text="You can say keep my area code, choose for me, or to hear those again, say repeat" src="MC1050_ini_25.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="MC1050_ini_28" cond="AreaCodeAnNPA == false">
                  <prompt-segments>
                    <audiofile text="You can say 'choose for me', or to hear those again, say 'repeat' " src="MC1050_ini_28.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="MC1050_SelectAreaCode_DM.jsp" count="1">
            <param name="AllowedAreaCodes" value="AllowedAreaCodesVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="MC1050_SelectAreaCode_DM_dtmf.jsp" count="1">
            <param name="AllowedAreaCodes" value="AllowedAreaCodesVXMLVar"/>
          </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="1000ms" completetimeout="0ms" maxspeechtimeout="12000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt id="MC1050_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="You chose" src="MC1050_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reentryprompt count="1" bargein="true" filename="" text="" id="MC1050_SelectAreaCode_DM_confirmation_initial"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt id="MC1050_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="You chose" src="MC1050_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="gl_cnf_nm1_02">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfMeaningIsDigitString"/>
                <prompt id="MC1050_cnf_ini_05">
                  <prompt-segments>
                    <audiofile text="You chose" src="MC1050_cnf_ini_05.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt type="digits" expr="lastresult" path="interpretation.dm_root"/>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfConfirmationStringDefined"/>
                  <prompt type="custom" expr="lastresult" path="interpretation.dm_confirm_string">
                    <param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.HDDMEcmascriptDynamicPromptIDRendererImpl"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="tts" expr="lastresult" path="interpretation.MEANING"/>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <play-state id="MC1070_MDNChangeMsg_PP">
      <session-mapping key="paymentInfoRequired" value="GlobalVars.paymentInfoRequired" type="String"/>
      <session-mapping key="networkType" value="GlobalVars.networkType" type="String"/>
      <audio>
        <prompt id="MC1070_out_02" cond="paymentInfoRequired == true">
          <prompt-segments>
            <audiofile text="I'm ready to change your number" src="MC1070_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="MC1070_out_01" cond="paymentInfoRequired == true">
          <prompt-segments>
            <audiofile text="Please note, charges *may* apply for changing your phone number" src="MC1070_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_1000ms">
          <prompt-segments>
            <audiofile text="test" src="silence_1000ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="MC1080_Dial686Repeat_DM"/>
    </play-state>

    <dm-state id="MC1080_Dial686Repeat_DM" type="YSNO">
      <success>
        <action label="true" next="MC1080_Dial686Repeat_DM"/>
        <action label="false">
          <audio>
            <prompt id="MC1080_out_01">
              <prompt-segments>
                <audiofile text="Alright Thank you for calling Metro, by T-Mobile" src="MC1080_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <gotodialog next="MDNChange_Submit_Dialog"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="MC1080_ini_01">
                <prompt-segments>
                  <audiofile text="Also, this call may now get disconnected as we update our network to use your new number, but please don't hang up until then You'll need to restart your phone, and in a few minutes you should be able to get your new phone number simply by dialing pound 6-8-6 pound from your phone Again, that's pound 6-8-6 pound" src="MC1080_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1080_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="MC1080_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="MC1080_Dial686Repeat_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1">
            <audio>
              <prompt id="MC1080_rin_01">
                <prompt-segments>
                  <audiofile text="This call may now get disconnected as we update our network to use your new number, but please don't hang up until then You'll need to restart your phone, and in a few minutes you should be able to get your new phone number simply by dialing pound 6-8-6 pound from your phone Again, that's pound 6-8-6 pound" src="MC1080_rin_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1080_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="MC1080_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </repeatprompts>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="MC1080_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say yes or no  Would you like to hear that information again?" src="MC1080_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="MC1080_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say yes or press one, or no or press two  Do you want to hear that information again?" src="MC1080_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="MC1080_nm3_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say yes or press one  To continue, say no or press two" src="MC1080_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1080_nm1_01">
                <prompt-segments>
                  <audiofile text="Please say yes or no  Would you like to hear that information again?" src="MC1080_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1080_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say yes or press one, or no or press two  Do you want to hear that information again?" src="MC1080_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1080_nm3_01">
                <prompt-segments>
                  <audiofile text="To hear that information again, say yes or press one  To continue, say no or press two" src="MC1080_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1"/>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="MC1080_ini_01">
                <prompt-segments>
                  <audiofile text="Also, this call may now get disconnected as we update our network to use your new number, but please don't hang up until then You'll need to restart your phone, and in a few minutes you should be able to get your new phone number simply by dialing pound 6-8-6 pound from your phone Again, that's pound 6-8-6 pound" src="MC1080_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="MC1080_ini_02">
                <prompt-segments>
                  <audiofile text="Would you like to hear that again?" src="MC1080_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="MC1080_Dial686Repeat_DM.grxml" count="1"/>
          <dtmfgrammars filename="MC1080_Dial686Repeat_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="MC1090_CallTransfer_SD">
      <if cond="GlobalVars.isCareTransfer == 'true'">
        <gotodialog next="CallTransfer_Main_Dialog"/>
        <else>
          <gotodialog next="Transfer_Main"/>
        </else>
      </if>
      <action next="MC1090_CallTransfer_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="MC1090_CallTransfer_SD_return_CS">
      <gotodialog next="Goodbye_Main_Dialog"/>
    </custom-state>

  </dialog>
  