<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="PaymentAmount_Main_Dialog">
    <decision-state id="PA1001_CheckContext_DS">
      <if cond="GlobalVars.cardStatus == 'amount_under_min' || GlobalVars.cardStatus == 'amount_over_max' || GlobalVars.heardRecentPaymentInfoCare == true || GlobalVars.skipBalance == true || GlobalVars.authorizationFailureHandling == 'sameAmountDeclined'">
        <session-mapping key="GlobalVars.skipBalance" value="false" type="Boolean"/>
        <action next="PA1015_GetAmount_DM"/>
        <elseif cond="(GlobalVars.guestPayment == true || (GlobalVars.fromAnotherPaymentMenu == true &amp;&amp; (parseFloat(GlobalVars.paymentAmount) == 0)))">
          <action next="PA1015_GetAmount_DM"/>
        </elseif>
        <else>
          <session-mapping key="GlobalVars.repeatPA1005" value="false" type="Boolean"/>
          <action next="PA1005_PlayCharges_PP"/>
        </else>
      </if>
    </decision-state>

    <play-state id="PA1005_PlayCharges_PP">
      <session-mapping key="balance" value="(GlobalVars.GetAccountDetails.realBalance) ? GlobalVars.GetAccountDetails.realBalance : GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="paymentsEntryPoint" value="GlobalVars.paymentsEntryPoint" type="String"/>
      <session-mapping key="payDate" value="GlobalVars.GetAccountDetails.payDateDay" type="String"/>
      <session-mapping key="suspendedRatePlanChange" value="GlobalVars.suspendedRatePlanChange" type="String"/>
      <session-mapping key="paymentsEntryPoint" value="GlobalVars.paymentsEntryPoint?GlobalVars.paymentsEntryPoint:''" type="String"/>
      <session-mapping key="remainingDays" value="GlobalVars.GetAccountDetails?GlobalVars.GetAccountDetails.numDaysBtwPayAndCurrentDate:0" type="String"/>
      <session-mapping key="arBalance" value="GlobalVars.GetAccountDetails.arBalance" type="String"/>
      <session-mapping key="arBalanceCredit" value="(Math.abs(arBalance)).toString()" type="String"/>
      <session-mapping key="fromAnotherPaymentMenu" value="GlobalVars.fromAnotherPaymentMenu != undefined ? GlobalVars.fromAnotherPaymentMenu : false" type="String"/>
      <audio>
        <if cond="((suspendedRatePlanChange == true || suspendedRatePlanChange == 'true') || (paymentsEntryPoint == 'careESN') || (fromAnotherPaymentMenu == true) )">
          <prompt id="PA1005_out_09">
            <prompt-segments>
              <audiofile text="Alright" src="PA1005_out_09.wav"/>
            </prompt-segments>
          </prompt>
          <prompt type="currency" expr="dueImmediatelyAmount" cond="(parseFloat(dueImmediatelyAmount) &gt; 0)">
            <param value="false" name="playZeroCents"/>
            <param value="f" name="intonation"/>
          </prompt>
          <elseif cond="parseFloat(balance) &gt; 0">
            <prompt id="PA1005_out_01" cond="parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; paymentsEntryPoint == 'careVoiceStore'">
              <prompt-segments>
                <audiofile text="Your payment due now is" src="PA1005_out_01.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="PA1005_out_02" cond="parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; paymentsEntryPoint != 'careVoiceStore'">
              <prompt-segments>
                <audiofile text="To keep your account active, your payment due now is" src="PA1005_out_02.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="dueImmediatelyAmount" cond="parseFloat(dueImmediatelyAmount) &gt; 0">
              <param value="false" name="playZeroCents"/>
              <param value="f" name="intonation"/>
            </prompt>
            <prompt id="silence_500ms" cond="parseFloat(dueImmediatelyAmount) &gt; 0">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="PA1005_out_03" cond="parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text="Your next payment of" src="PA1005_out_03.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="balance" cond="parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <param value="false" name="playZeroCents"/>
              <param value="f" name="intonation"/>
            </prompt>
            <prompt id="due_1st" cond="payDate == '1' &amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the first" src="due_1st.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_2nd" cond="payDate == '2' &amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 2nd" src="due_2nd.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_3rd" cond="payDate == '3' &amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 3rd" src="due_3rd.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_4th" cond="payDate == '4' &amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 4th" src="due_4th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_5th" cond="payDate == '5' &amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 5th" src="due_5th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_6th" cond="payDate == '6' &amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 6th" src="due_6th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_7th" cond="payDate == '7' &amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 7th" src="due_7th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_8th" cond="payDate == '8' &amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 8th" src="due_8th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_9th" cond="payDate == '9' &amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 9th" src="due_9th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_10th" cond="payDate == '10 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 10th" src="due_10th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_11th" cond="payDate == '11 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 11th" src="due_11th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_12th" cond="payDate == '12 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 12th" src="due_12th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_13th" cond="payDate == '13 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 13th" src="due_13th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_14th" cond="payDate == '14 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 14th" src="due_14th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_15th" cond="payDate == '15 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 15th" src="due_15th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_16th" cond="payDate == '16 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 16th" src="due_16th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_17th" cond="payDate == '17 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 17th" src="due_17th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_18th" cond="payDate == '18 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 18th" src="due_18th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_19th" cond="payDate == '19 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 19th" src="due_19th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_20th" cond="payDate == '20 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 20th" src="due_20th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_21st" cond="payDate == '21 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 21st" src="due_21st.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_22nd" cond="payDate == '22 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 22nd" src="due_22nd.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_23rd" cond="payDate == '23 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 23rd" src="due_23rd.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_24th" cond="payDate == '24 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 24th" src="due_24th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_25th" cond="payDate == '25 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 25th" src="due_25th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_26th" cond="payDate == '26 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 26th" src="due_26th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_27th" cond="payDate == '27 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 27th" src="due_27th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_28th" cond="payDate == '28 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 28th" src="due_28th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_29th" cond="payDate == '29 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 29th" src="due_29th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_30th" cond="payDate == '30 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 30th" src="due_30th.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="due_31st" cond="payDate == '31 '&amp;&amp; parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; remainingDays &lt;= 10">
              <prompt-segments>
                <audiofile text=" is due on the 31st" src="due_31st.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms" cond="parseFloat(dueImmediatelyAmount) &gt; 0">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="PA1005_out_04" cond="!(parseFloat(dueImmediatelyAmount) &gt; 0)">
              <prompt-segments>
                <audiofile text="Your current balance is" src="PA1005_out_04.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="balance" cond="!(parseFloat(dueImmediatelyAmount) &gt; 0)">
              <param value="false" name="playZeroCents"/>
              <param value="f" name="intonation"/>
            </prompt>
            <prompt id="silence_250ms" expr="balance" cond="!(parseFloat(dueImmediatelyAmount) &gt; 0)">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="parseFloat(balance) == 0">
            <prompt id="PA1005_out_05" cond="parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; paymentsEntryPoint == 'careVoiceStore'">
              <prompt-segments>
                <audiofile text="Your payment due now is" src="PA1005_out_05.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="PA1005_out_06" cond="parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; paymentsEntryPoint != 'careVoiceStore'">
              <prompt-segments>
                <audiofile text="To keep your account active, your payment due now is" src="PA1005_out_06.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="dueImmediatelyAmount" cond="parseFloat(dueImmediatelyAmount) &gt; 0">
              <param value="false" name="playZeroCents"/>
              <param value="f" name="intonation"/>
            </prompt>
            <prompt id="silence_250ms" cond="parseFloat(dueImmediatelyAmount) &gt; 0">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="PA1005_out_07" cond="!(parseFloat(dueImmediatelyAmount) &gt; 0)">
              <prompt-segments>
                <audiofile text="I see you account's all paid up" src="PA1005_out_07.wav"/>
              </prompt-segments>
            </prompt>
            <prompt id="silence_500ms" cond="!(parseFloat(dueImmediatelyAmount) &gt; 0)">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
          <elseif cond="parseFloat(arBalance) &lt; 0">
            <prompt id="PA1005_out_08">
              <prompt-segments>
                <audiofile text="I see your account's all paid up, and you have a credit of" src="PA1005_out_08.wav"/>
              </prompt-segments>
            </prompt>
            <prompt type="currency" expr="arBalanceCredit">
              <param value="false" name="playZeroCents"/>
              <param value="f" name="intonation"/>
            </prompt>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </elseif>
        </if>
      </audio>
      <if cond="(parseFloat(dueImmediatelyAmount) != 0)&amp;&amp;(suspendedRatePlanChange == true || GlobalVars.paymentsEntryPoint == 'careESN' || GlobalVars.fromAnotherPaymentMenu == true)">
        <if cond="(GlobalVars.fromAnotherPaymentMenu == true)">
          <action next="PA1006_CheckOfferPrepaid_JDA"/>
          <else>
            <action next="getReturnLink()"/>
          </else>
        </if>
        <elseif cond="parseFloat(balance) &gt; 0">
          <if cond="(((parseFloat(dueImmediatelyAmount) &gt; 0) &amp;&amp; (remainingDays &gt; 10)) &amp;&amp; (GlobalVars.payingWithPrepaid != true))">
            <action next="PA1006_CheckOfferPrepaid_JDA"/>
            <elseif cond="(parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; GlobalVars.payingWithPrepaid == true)">
              <session-mapping key="GlobalVars.repeatPA1005" value="true" type="Boolean"/>
              <action next="getReturnLink()"/>
            </elseif>
            <elseif cond="(parseFloat(dueImmediatelyAmount) &gt; 0 &amp;&amp; GlobalVars.payingWithPrepaid != true)">
              <session-mapping key="GlobalVars.repeatPA1005" value="true" type="Boolean"/>
              <action next="PA1015_GetAmount_DM"/>
            </elseif>
            <else>
              <action next="PA1006_CheckOfferPrepaid_JDA"/>
            </else>
          </if>
        </elseif>
        <elseif cond="parseFloat(balance) == 0">
          <if cond="parseFloat(dueImmediatelyAmount) &gt; 0">
            <action next="PA1006_CheckOfferPrepaid_JDA"/>
            <else>
              <if cond="GlobalVars.payingWithPrepaid == true">
                <session-mapping key="GlobalVars.repeatPA1005" value="true" type="Boolean"/>
                <action next="getReturnLink()"/>
                <else>
                  <session-mapping key="GlobalVars.repeatPA1005" value="true" type="Boolean"/>
                  <action next="PA1015_GetAmount_DM"/>
                </else>
              </if>
            </else>
          </if>
        </elseif>
        <elseif cond="parseFloat(arBalance) &lt; 0">
          <if cond="GlobalVars.payingWithPrepaid == true">
            <session-mapping key="GlobalVars.repeatPA1005" value="true" type="Boolean"/>
            <action next="getReturnLink()"/>
            <else>
              <session-mapping key="GlobalVars.repeatPA1005" value="true" type="Boolean"/>
              <action next="PA1015_GetAmount_DM"/>
            </else>
          </if>
        </elseif>
      </if>
    </play-state>

    <decision-state id="PA1006_CheckOfferPrepaid_DS">
      <if cond="GlobalVars.payingWithPrepaid == true">
        <action next="getReturnLink()"/>
        <elseif cond="GlobalVars.offerPrepaid == true">
          <action next="PA2005_PaySameAmountPrepaid_DM"/>
        </elseif>
        <else>
          <action next="PA1010_PaySameAmountYN_DM"/>
        </else>
      </if>
    </decision-state>

    <dm-state id="PA1010_PaySameAmountYN_DM" type="CUST">
      <session-mapping key="balance" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="extensionAllowed" value="GlobalVars.extensionAllowed != undefined ? GlobalVars.extensionAllowed : false" type="String"/>
      <success>
        <action label="yes">
          <if cond="parseFloat(dueImmediatelyAmount) &gt; 3">
            <session-mapping key="GlobalVars.paymentAmount" expr="dueImmediatelyAmount"/>
            <else>
              <session-mapping key="GlobalVars.paymentAmount" expr="balance"/>
            </else>
          </if>
          <action next="getReturnLink()"/>
        </action>
        <action label="no" next="PA1015_GetAmount_DM"/>
        <action label="request-extension">
          <audio>
            <prompt id="PA1010_out_01">
              <prompt-segments>
                <audiofile text="Sure!" src="PA1010_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'extension'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.extensionEntryPoint" expr="'payment_amount'"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator">
          <session-mapping key="GlobalVars.saidOperator" value="true" type="Boolean"/>
        </action>
        <action label="repeat" next="PA1005_PlayCharges_PP"/>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PA1010_ini_01">
                <prompt-segments>
                  <audiofile text="Is that the amount you want to pay" src="PA1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PA1010_nm1_02" cond="parseFloat(dueImmediatelyAmount) &gt; 3">
                <prompt-segments>
                  <audiofile text="Do you want to make your payment due now? " src="PA1010_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1010_nm1_01" cond="!(parseFloat(dueImmediatelyAmount) &gt; 3)">
                <prompt-segments>
                  <audiofile text="Do you want to pay your full balance?" src="PA1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="PA1010_nm2_02" cond="parseFloat(dueImmediatelyAmount) &gt; 3">
                <prompt-segments>
                  <audiofile text="If you want to pay your amount due now, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="PA1010_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1010_nm2_01" cond="!(parseFloat(dueImmediatelyAmount) &gt; 3)">
                <prompt-segments>
                  <audiofile text="If you want to pay your full balance, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="PA1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="PA1010_nm3_02" cond="parseFloat(dueImmediatelyAmount) &gt; 3">
                <prompt-segments>
                  <audiofile text="To pay your amount due now, press 1 To pay a different amount, press 2" src="PA1010_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1010_nm3_01" cond="!(parseFloat(dueImmediatelyAmount) &gt; 3)">
                <prompt-segments>
                  <audiofile text="To pay your full balance, press 1 To pay a different amount, press 2" src="PA1010_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1010_nm1_02" cond="parseFloat(dueImmediatelyAmount) &gt; 3">
                <prompt-segments>
                  <audiofile text="Do you want to make your payment due now? " src="PA1010_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1010_nm1_01" cond="!(parseFloat(dueImmediatelyAmount) &gt; 3)">
                <prompt-segments>
                  <audiofile text="Do you want to pay your full balance?" src="PA1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1010_nm2_02" cond="parseFloat(dueImmediatelyAmount) &gt; 3">
                <prompt-segments>
                  <audiofile text="If you want to pay your amount due now, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="PA1010_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1010_nm2_01" cond="!(parseFloat(dueImmediatelyAmount) &gt; 3)">
                <prompt-segments>
                  <audiofile text="If you want to pay your full balance, say 'yes' or press 1 Otherwise, say 'no' or press 2" src="PA1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1010_nm3_02" cond="parseFloat(dueImmediatelyAmount) &gt; 3">
                <prompt-segments>
                  <audiofile text="To pay your amount due now, press 1 To pay a different amount, press 2" src="PA1010_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1010_nm3_01" cond="!(parseFloat(dueImmediatelyAmount) &gt; 3)">
                <prompt-segments>
                  <audiofile text="To pay your full balance, press 1 To pay a different amount, press 2" src="PA1010_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PA1010_ini_01">
                <prompt-segments>
                  <audiofile text="Is that the amount you want to pay" src="PA1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml">
          <grammars filename="PA1010_PaySameAmountYN_DM.jsp" count="1">
            <param name="extensionAllowed" value="extensionAllowedVXMLVar"/>
          </grammars>
          <dtmfgrammars filename="PA1010_PaySameAmountYN_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="Never">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="PA1010_cnf_ini_01">
                <prompt-segments>
                  <audiofile text="You want a payment extension " src="PA1010_cnf_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="extension == 'lastresult'">
                <prompt id="PA1010_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You want a payment extension " src="PA1010_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="extension == 'lastresult'">
                <prompt id="PA1010_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You want a payment extension " src="PA1010_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <dm-state id="PA1015_GetAmount_DM" type="CURR">
      <session-mapping key="paymentAmount" value="GlobalVars.paymentAmount != undefined ? GlobalVars.paymentAmount : 0.0" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="maxLimit" value="(GlobalVars.ValidateCardOptions)?GlobalVars.ValidateCardOptions.maxLimit : ''" type="String"/>
      <session-mapping key="minLimit" value="(GlobalVars.ValidateCardOptions)?GlobalVars.ValidateCardOptions.minLimit : ''" type="String"/>
      <session-mapping key="extensionAllowed" value="GlobalVars.extensionAllowed != undefined ? GlobalVars.extensionAllowed : false" type="String"/>
      <session-mapping key="authorizationFailureHandling" value="GlobalVars.authorizationFailureHandling" type="String"/>
      <session-mapping key="offerPrepaid" value="GlobalVars.offerPrepaid" type="String"/>
      <session-mapping key="collection_grammar1" value="" type="String"/>
      <session-mapping key="collection_dtmfgrammar1" value="" type="String"/>
      <success>
        <action label="prepaid">
          <session-mapping key="GlobalVars.payingWithPrepaid" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="switch_account">
          <audio>
            <prompt id="PA1015_out_01">
              <prompt-segments>
                <audiofile text="Sure To manage a diffrent account, you can call 611 from the phone you want to work with If you don t have it, you ll need to call from a landline, and dial 888 8METRO8 Again, that s 888-8-METRO8, from a landline Then, you ll just enter the number you want to work with " src="PA1015_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_750ms">
              <prompt-segments>
                <audiofile text="test" src="silence_750ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="request-extension">
          <audio>
            <prompt id="PA1015_out_02">
              <prompt-segments>
                <audiofile text="Sure!" src="PA1015_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_250ms">
              <prompt-segments>
                <audiofile text="test" src="silence_250ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'extension'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.extensionEntryPoint" expr="'payment_amount'"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="default">
          <session-mapping key="GlobalVars.paymentAmount" expr="PA1015_GetAmount_DM.returnvalue"/>
          <session-mapping key="GlobalVars.repeatPA1005" value="false" type="Boolean"/>
          <action next="PA1020_CheckDueImmediately_JDA"/>
        </action>
        <action label="operator"/>
        <action label="repeat">
          <if cond="GlobalVars.repeatPA1005 == true">
            <action next="PA1005_PlayCharges_PP"/>
            <else>
              <action next="PA1015_GetAmount_DM"/>
            </else>
          </if>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if cond="parseFloat(paymentAmount) == 0.00 || authorizationFailureHandling == 'sameAmountDeclined'">
                <prompt id="PA1015_ini_06">
                  <prompt-segments>
                    <audiofile text="How much would you like to pay today" src="PA1015_ini_06.wav"/>
                  </prompt-segments>
                </prompt>
                <prompt id="PA1015_ini_03" cond="offerPrepaid == true">
                  <prompt-segments>
                    <audiofile text="Or say use a  Payment PIN " src="PA1015_ini_03.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="parseFloat(paymentAmount) &lt; parseFloat(minLimit)">
                  <prompt id="PA1015_ini_02">
                    <prompt-segments>
                      <audiofile text="I'm sorry, the amount you entered is too small for your type of card Please choose an amount of at least" src="PA1015_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="currency" expr="minLimit">
                    <param name="runtime" value="ecmascript"/>
                    <param value="f" name="intonation"/>
                    <param value="false" name="playZeroCents"/>
                  </prompt>
                </elseif>
                <elseif cond="parseFloat(paymentAmount) &gt; parseFloat(maxLimit)">
                  <prompt id="PA1015_ini_04">
                    <prompt-segments>
                      <audiofile text="I'm sorry, the amount you entered is too much for your type of card Please choose an amount under " src="PA1015_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt type="currency" expr="maxLimit">
                    <param name="runtime" value="ecmascript"/>
                    <param value="f" name="intonation"/>
                    <param value="false" name="playZeroCents"/>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PA1015_ini_05">
                    <prompt-segments>
                      <audiofile text="How much would you like to pay today? " src="PA1015_ini_05.wav"/>
                    </prompt-segments>
                  </prompt>
                  <prompt id="PA1015_ini_07" cond="offerPrepaid == true">
                    <prompt-segments>
                      <audiofile text="Or say 'use a  Payment PIN'" src="PA1015_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="PA1015_GetAmount_DM_initial"/>
          <helpprompts count="1" bargein="true" filename="none" text="" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PA1015_nm1_01">
                <prompt-segments>
                  <audiofile text="How much would you like to pay today" src="PA1015_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1015_nm1_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Or say use a  Payment PIN" src="PA1015_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="PA1015_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say the amount you want to pay or enter on your keypad, followed by the sign For example if you want to pay 25 dollars, enter 2500" src="PA1015_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1015_nm2_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="If you'll be redeeming a prepaid  Payment PIN today, press star and we'll take care of that first " src="PA1015_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="PA1015_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter the amount you want to pay, in dollars and cents, followed by the # sign If you want to pay 57 dollars, enter it like 5700" src="PA1015_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1015_nm3_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="To redeem a  Payment PIN, press star " src="PA1015_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1015_nm1_01">
                <prompt-segments>
                  <audiofile text="How much would you like to pay today" src="PA1015_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1015_nm1_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Or say use a  Payment PIN" src="PA1015_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1015_nm2_01">
                <prompt-segments>
                  <audiofile text="Please say the amount you want to pay or enter on your keypad, followed by the sign For example if you want to pay 25 dollars, enter 2500" src="PA1015_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1015_nm2_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="If you'll be redeeming a prepaid  Payment PIN today, press star and we'll take care of that first " src="PA1015_nm2_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1015_nm3_01">
                <prompt-segments>
                  <audiofile text="Please enter the amount you want to pay, in dollars and cents, followed by the # sign If you want to pay 57 dollars, enter it like 5700" src="PA1015_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1015_nm3_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="To redeem a  Payment PIN, press star " src="PA1015_nm3_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PA1015_nm1_01">
                <prompt-segments>
                  <audiofile text="How much would you like to pay today" src="PA1015_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA1015_nm1_02" cond="offerPrepaid == true">
                <prompt-segments>
                  <audiofile text="Or say use a  Payment PIN" src="PA1015_nm1_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommandsNoRepeat_dtmf.grxml">
          <grammars filename="PA1015_GetAmount_DM.jsp" count="1">
      </grammars>
          <dtmfgrammars filename="PA1015_GetAmount_DM_dtmf.jsp" count="1">
      </dtmfgrammars>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="3000ms" completetimeout="0ms" maxspeechtimeout="8000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="2000ms" timeout="9000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="switch_account == 'lastresult'">
                <prompt id="PA1015_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You want to work with a different account  " src="PA1015_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="request-extension == 'lastresult'">
                  <prompt id="PA1015_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="You want a payment extension " src="PA1015_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="prepaid == 'lastresult'">
                  <prompt id="PA1015_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="A  Payment PIN " src="PA1015_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="currency" expr="lastresult" path="interpretation.dm_root">
                    <param value="f" name="intonation"/>
                    <param value="false" name="playZeroCents"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="switch_account == 'lastresult'">
                <prompt id="PA1015_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You want to work with a different account  " src="PA1015_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="extension == 'lastresult'">
                  <prompt id="PA1015_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="You want a payment extension " src="PA1015_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="prepaid == 'lastresult'">
                  <prompt id="PA1015_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="A  Payment PIN " src="PA1015_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="currency" expr="lastresult" path="interpretation.dm_root">
                    <param value="f" name="intonation"/>
                    <param value="false" name="playZeroCents"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="switch_account == 'lastresult'">
                <prompt id="PA1015_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="You want to work with a different account  " src="PA1015_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="extension == 'lastresult'">
                  <prompt id="PA1015_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="You want a payment extension " src="PA1015_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="prepaid == 'lastresult'">
                  <prompt id="PA1015_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="A  Payment PIN " src="PA1015_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt type="currency" expr="lastresult" path="interpretation.dm_root">
                    <param value="f" name="intonation"/>
                    <param value="false" name="playZeroCents"/>
                  </prompt>
                  <prompt id="silence_250ms">
                    <prompt-segments>
                      <audiofile text="test" src="silence_250ms.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <decision-state id="PA1020_CheckDueImmediately_DS">
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <if cond="GlobalVars.guestPayment == true">
        <action next="getReturnLink()"/>
        <elseif cond="((parseFloat(GlobalVars.paymentAmount) &lt; parseFloat(dueImmediatelyAmount)) &amp;&amp; (GlobalVars.GetAccountDetails.accountStatus == 'suspended') &amp;&amp; (GlobalVars.serviceDialed == '99'))">
          <action next="PA1025_Call611ForOptions_PP"/>
        </elseif>
        <elseif cond="(GlobalVars.ValidateCardOptions &amp;&amp; (parseFloat(GlobalVars.paymentAmount)) &lt;  (parseFloat(GlobalVars.ValidateCardOptions.minLimit)))|| (GlobalVars.ValidateCardOptions &amp;&amp; (parseFloat(GlobalVars.paymentAmount)) &gt;  (parseFloat(GlobalVars.ValidateCardOptions.maxLimit)))">
          <action next="PA1015_GetAmount_DM"/>
        </elseif>
        <else>
          <action next="getReturnLink()"/>
        </else>
      </if>
    </decision-state>

    <play-state id="PA1025_Call611ForOptions_PP">
      <session-mapping key="callType" value="GlobalVars.callType" type="String"/>
      <audio>
        <prompt id="PA1025_out_01">
          <prompt-segments>
            <audiofile text="Unfortunately, we won t be able to turn your service back on without a full payment" src="PA1025_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="PA1025_out_02">
          <prompt-segments>
            <audiofile text="To see if there are other options for you, please hang up here, and dial 611 from your Metro phone or call 888-8METRO8 that s 888-8-METRO-8 " src="PA1025_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_750ms">
          <prompt-segments>
            <audiofile text="test" src="silence_750ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <session-mapping key="GlobalVars.callType" expr="'goodbye'"/>
      <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
      <action next="getReturnLink()"/>
    </play-state>

    <dm-state id="PA2005_PaySameAmountPrepaid_DM" type="CUST">
      <session-mapping key="fromAnotherPaymentMenu" value="GlobalVars.fromAnotherPaymentMenu" type="String"/>
      <session-mapping key="extensionAllowed" value="GlobalVars.extensionAllowed != undefined ? GlobalVars.extensionAllowed : false" type="String"/>
      <session-mapping key="dueImmediatelyAmount" value="GlobalVars.GetAccountDetails.dueImmediatelyAmount" type="String"/>
      <session-mapping key="amountDue" value="GlobalVars.GetAccountDetails.balance" type="String"/>
      <session-mapping key="collection_grammar1" value="PA2005_PaySameAmountPrepaid_DM.grxml" type="String"/>
      <if cond="extensionAllowed == false">
        <session-mapping key="collection_grammar1" expr="collection_grammar1 + '?SWI_vars.disallow=request-extension'"/>
        <else>
          <session-mapping key="collection_grammar1" expr="collection_grammar1"/>
        </else>
      </if>
      <success>
        <action label="card">
          <if cond="(parseFloat(dueImmediatelyAmount) &gt; 3) ">
            <session-mapping key="GlobalVars.paymentAmount" expr="dueImmediatelyAmount"/>
            <else>
              <session-mapping key="GlobalVars.paymentAmount" expr="amountDue"/>
            </else>
          </if>
          <session-mapping key="GlobalVars.offerPrepaid" value="false" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="prepaid">
          <session-mapping key="GlobalVars.payingWithPrepaid" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="change">
          <audio>
            <prompt id="PA2005_out_01">
              <prompt-segments>
                <audiofile text="Okay " src="PA2005_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="PA1015_GetAmount_DM"/>
        </action>
        <action label="request-extension">
          <audio>
            <prompt id="PA2005_out_02">
              <prompt-segments>
                <audiofile text="Sure!" src="PA2005_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.callType" expr="'extension'"/>
          <session-mapping key="GlobalVars.cti_Intent" expr="getctiIntent(GlobalVars.GetCTIParameters.ctiIntent,GlobalVars.callType, 'callType')"/>
          <session-mapping key="GlobalVars.extensionEntryPoint" expr="'payment_amount'"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="repeat">
          <action next="PA1005_PlayCharges_PP"/>
        </action>
      </success>
      <catch/>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="3" maxnoinputs="3"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="PA2005_ini_01" cond="fromAnotherPaymentMenu == true">
                <prompt-segments>
                  <audiofile text="How would you like to pay? Say 'bank card', ' Payment PIN' or, 'change the amount' " src="PA2005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA2005_ini_02" cond="fromAnotherPaymentMenu != true">
                <prompt-segments>
                  <audiofile text="First, how would you like to pay? Say 'bank card', ' Payment PIN' Or say 'change the amount' " src="PA2005_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="false" filename="none" text="none" id=""/>
          <helpprompts count="1" bargein="true" filename="none" text="none" id=""/>
          <helpprompts count="2" filename="none" text="none" id=""/>
          <repeatprompts count="1"/>
          <repeatprompts count="2"/>
          <repeatprompts count="3"/>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="PA2005_ini_01" cond="fromAnotherPaymentMenu == true">
                <prompt-segments>
                  <audiofile text="How would you like to pay? Say 'bank card', ' Payment PIN' or, 'change the amount' " src="PA2005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA2005_ini_02" cond="fromAnotherPaymentMenu != true">
                <prompt-segments>
                  <audiofile text="First, how would you like to pay? Say 'bank card', ' Payment PIN' Or say 'change the amount' " src="PA2005_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="PA2005_nm2_01">
                <prompt-segments>
                  <audiofile text="How would you like to pay? Say 'bank card' or press 1, or ' Payment PIN' or press 2 To change the amount you wanna pay, say 'change amount or press 3 " src="PA2005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="PA2005_nm2_01">
                <prompt-segments>
                  <audiofile text="How would you like to pay? Say 'bank card' or press 1, or ' Payment PIN' or press 2 To change the amount you wanna pay, say 'change amount or press 3 " src="PA2005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA2005_ini_01" cond="fromAnotherPaymentMenu == true">
                <prompt-segments>
                  <audiofile text="How would you like to pay? Say 'bank card', ' Payment PIN' or, 'change the amount' " src="PA2005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA2005_ini_02" cond="fromAnotherPaymentMenu != true">
                <prompt-segments>
                  <audiofile text="First, how would you like to pay? Say 'bank card', ' Payment PIN' Or say 'change the amount' " src="PA2005_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="gl_nm2_01">
                <prompt-segments>
                  <audiofile text="I *still* didn't get that " src="gl_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA2005_nm2_01">
                <prompt-segments>
                  <audiofile text="How would you like to pay? Say 'bank card' or press 1, or ' Payment PIN' or press 2 To change the amount you wanna pay, say 'change amount or press 3 " src="PA2005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="gl_nm3_01">
                <prompt-segments>
                  <audiofile text="Let's try one more time " src="gl_nm3_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA2005_nm2_01">
                <prompt-segments>
                  <audiofile text="How would you like to pay? Say 'bank card' or press 1, or ' Payment PIN' or press 2 To change the amount you wanna pay, say 'change amount or press 3 " src="PA2005_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="PA2005_ini_01" cond="fromAnotherPaymentMenu == true">
                <prompt-segments>
                  <audiofile text="How would you like to pay? Say 'bank card', ' Payment PIN' or, 'change the amount' " src="PA2005_ini_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="PA2005_ini_02" cond="fromAnotherPaymentMenu != true">
                <prompt-segments>
                  <audiofile text="First, how would you like to pay? Say 'bank card', ' Payment PIN' Or say 'change the amount' " src="PA2005_ini_02.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommandsNoOperator.grxml" dtmfcommandgrammar="GlobalCommandsNoOperator_dtmf.grxml">
          <grammars filename="PA2005_PaySameAmountPrepaid_DM.grxml" count="1"/>
          <dtmfgrammars filename="PA2005_PaySameAmountPrepaid_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="4" maxnomatches="4" maxnoinputs="4"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
          <dtmfgrammars filename="confirmation_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="card == 'lastresult'">
                <prompt id="PA2005_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="A bank card" src="PA2005_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="prepaid == 'lastresult'">
                  <prompt id="PA2005_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="A  Payment PIN" src="PA2005_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="change == 'lastresult'">
                  <prompt id="PA2005_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Pay a different amount " src="PA2005_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="request-extension == 'lastresult'">
                  <prompt id="PA2005_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="You want a payment extension" src="PA2005_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
              </if>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="gl_cnf_ini_07">
                    <prompt-segments>
                      <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <noinputprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="card == 'lastresult'">
                <prompt id="PA2005_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="A bank card" src="PA2005_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="prepaid == 'lastresult'">
                  <prompt id="PA2005_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="A  Payment PIN" src="PA2005_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="change == 'lastresult'">
                  <prompt id="PA2005_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Pay a different amount " src="PA2005_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PA2005_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="You want a payment extension" src="PA2005_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_cnf_nm1_01">
                <prompt-segments>
                  <audiofile text="Just say  yes  or  no " src="gl_cnf_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="3"/>
                </condition>
                <prompt id="gl_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="Okay," src="gl_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="3"/>
                  </condition>
                  <prompt id="gl_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="Alright," src="gl_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="gl_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Sure," src="gl_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <if cond="card == 'lastresult'">
                <prompt id="PA2005_cnf_ini_01">
                  <prompt-segments>
                    <audiofile text="A bank card" src="PA2005_cnf_ini_01.wav"/>
                  </prompt-segments>
                </prompt>
                <elseif cond="prepaid == 'lastresult'">
                  <prompt id="PA2005_cnf_ini_02">
                    <prompt-segments>
                      <audiofile text="A  Payment PIN" src="PA2005_cnf_ini_02.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif cond="change == 'lastresult'">
                  <prompt id="PA2005_cnf_ini_03">
                    <prompt-segments>
                      <audiofile text="Pay a different amount " src="PA2005_cnf_ini_03.wav"/>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="PA2005_cnf_ini_04">
                    <prompt-segments>
                      <audiofile text="You want a payment extension" src="PA2005_cnf_ini_04.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
              <prompt id="silence_500ms">
                <prompt-segments>
                  <audiofile text="test" src="silence_500ms.wav"/>
                </prompt-segments>
              </prompt>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="2"/>
                </condition>
                <prompt id="gl_cnf_ini_07">
                  <prompt-segments>
                    <audiofile text="Right?" src="gl_cnf_ini_07.wav"/>
                  </prompt-segments>
                </prompt>
                <else>
                  <prompt id="gl_cnf_ini_08">
                    <prompt-segments>
                      <audiofile text="Did I get that right?" src="gl_cnf_ini_08.wav"/>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </nomatchprompts>
        </prompt_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

  </dialog>
  