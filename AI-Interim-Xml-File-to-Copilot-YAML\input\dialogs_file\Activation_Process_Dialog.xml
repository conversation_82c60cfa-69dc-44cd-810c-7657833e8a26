<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Activation_Process_Dialog">
    <subdialog-state id="AC2130_AccountInfoReadback_SD">
      <gotodialog next="AcctInfoReadback_Main_Dialog"/>
      <action next="AC2130_AccountInfoReadback_SD_return_CS"/>
    </subdialog-state>
    <custom-state id="AC2130_AccountInfoReadback_SD_return_CS">
      <if cond="GlobalVars.activationResult != 'transfer'">
        <session-mapping key="GlobalVars.activationResult" expr="'complete'"/>
      </if>
      <action next="getReturnLink()"/>
    </custom-state>

  </dialog>
  