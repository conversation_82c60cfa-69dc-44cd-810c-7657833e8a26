<?xml version="1.0" ?>
<session-mappings>
  <session-mapping key="label" value="abc" type="String"/>
  <session-mapping key="TRUE" value="true" type="Boolean"/>
  <session-mapping key="event" value="abc" type="String"/>
  <session-mapping key="project_name" value="project_name" type="String"/>
  <session-mapping key="language" value="en-US" type="String"/>
  <session-mapping key="username" value="admin" type="String"/>
  <session-mapping key="password" value="admin123" type="String"/>
  <session-mapping key="accountStatus" value="active" type="string"/>
  <session-mapping key="GlobalVars.extensionAllowed" value="true" type="string"/>
  <session-mapping key="GetAccountDetails.eligibleForBillCycleReset" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.tag" value="vague-billing" type="string"/>
  <session-mapping key="tag" value="vague-billing" type="string"/>
  <session-mapping key="GlobalVars.callType" value="extension" type="string"/>
  <session-mapping key="features.length" value="0" type="integer"/>
  <session-mapping key="isOnFamilyPlan" value="true" type="boolean"/>
  <session-mapping key="GlobalVars.loggedIn" value="true" type="boolean"/>
  <session-mapping key="operatorReqCount" value="maxRequests" type="string"/>
  <session-mapping key="playedOperator" value="false" type="boolean"/>
</session-mappings>
