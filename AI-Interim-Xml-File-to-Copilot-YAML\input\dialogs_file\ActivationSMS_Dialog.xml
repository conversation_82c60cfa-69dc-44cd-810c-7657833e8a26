<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="ActivationSMS_Dialog">
    <decision-state id="AS1001_CheckContext_DS">
      <if cond="GlobalVars.acceptedAcctDetsSMS == true">
        <session-mapping key="GlobalVars.acct_dets_campaign_id" expr="GlobalVars.GetBCSParameters.acct_dets_campaign_id"/>
        <action next="AS1105_SendAcctDetsSMS_DB_DA"/>
        <else>
          <if cond="GlobalVars.activationType == 'eSIM' || GlobalVars.deviceChangeTargetSIMChosen == 'ESIM'">
            <action next="AS1015_PlayActivationExtraStep_PP"/>
            <else>
              <action next="AS1005_PlaySMSPreamble_PP"/>
            </else>
          </if>
        </else>
      </if>
    </decision-state>

    <play-state id="AS1005_PlaySMSPreamble_PP">
      <session-mapping key="haveActivationAddress" value="GlobalVars.haveActivationAddress != undefined ? GlobalVars.haveActivationAddress : false" type="String"/>
      <session-mapping key="activationType" value="GlobalVars.activationType" type="String"/>
      <audio>
        <prompt id="AS1005_out_03" cond="activationType == 'eSIM' &amp;&amp; haveActivationAddress == true">
          <prompt-segments>
            <audiofile text="You also need to fill out the *rest* of your account information, like your name and your security question That you can do that at metrobyt-mobilecom, under My Account" src="AS1005_out_03.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AS1005_out_04" cond="activationType == 'eSIM' &amp;&amp; haveActivationAddress == false">
          <prompt-segments>
            <audiofile text="You also need to fill out the *rest* of your account information, like your name, address, and your security question That you can do that at metrobyt-mobilecom, under My Account" src="AS1005_out_04.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AS1005_out_01" cond="activationType != 'eSIM' &amp;&amp; haveActivationAddress == true">
          <prompt-segments>
            <audiofile text="By the way, when we're done here you'll need to fill out the *rest* of your account information, like your name and your security question You can do that at metrobyt-mobilecom, under My Account" src="AS1005_out_01.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="AS1005_out_02" cond="activationType != 'eSIM' &amp;&amp; haveActivationAddress == false">
          <prompt-segments>
            <audiofile text="By the way, when we're done here you'll need to fill out the *rest* of your account information, like your name, address, and your security question You can do that at metrobyt-mobilecom, under My Account " src="AS1005_out_02.wav"/>
          </prompt-segments>
        </prompt>
        <prompt id="silence_500ms">
          <prompt-segments>
            <audiofile text="test" src="silence_500ms.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <action next="AS1010_OfferAcctSMS_DM"/>
    </play-state>

    <dm-state id="AS1010_OfferAcctSMS_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="AS1010_out_01">
              <prompt-segments>
                <audiofile text="Great" src="AS1010_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.acceptedAcctDetsSMS" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
        </action>
        <action label="false">
          <audio>
            <prompt id="AS1010_out_02">
              <prompt-segments>
                <audiofile text="No problem Don't forget to to go to metrobyt-mobilecom, and log in under My Account " src="AS1010_out_02.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <audio>
            <prompt id="silence_500ms">
              <prompt-segments>
                <audiofile text="test" src="silence_500ms.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="getReturnLink()"/>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.550">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AS1010_ini_01">
                <prompt-segments>
                  <audiofile text="Can I text you a link, when your phone's activated? " src="AS1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AS1010_OfferAcctSMS_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="AS1010_OfferAcctSMS_DM_initial"/>
          <helpprompts count="2" bargein="true" filename="" text="" id="AS1010_OfferAcctSMS_DM_initial"/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AS1010_OfferAcctSMS_DM_reinvoke"/>
            </audio>
          </repeatprompts>
          <repeatprompts count="2">
            <audio>
              <prompt id="AS1010_OfferAcctSMS_DM_reinvoke"/>
            </audio>
          </repeatprompts>
          <repeatprompts count="3">
            <audio>
              <prompt id="AS1010_OfferAcctSMS_DM_reinvoke"/>
            </audio>
          </repeatprompts>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AS1010_ni1_01">
                <prompt-segments>
                  <audiofile text="Should I text you the link to finish filling out your account details? Say 'yes' or press 1, or 'no' or press 2" src="AS1010_ni1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AS1010_ni2_01">
                <prompt-segments>
                  <audiofile text="That's OK, let's move on " src="AS1010_ni2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AS1010_OfferAcctSMS_DM_noinput_3"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AS1010_nm1_01">
                <prompt-segments>
                  <audiofile text="Should I text you the link to finish filling out your account details? Say 'yes' or press 1, or 'no' or press 2" src="AS1010_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="AS1010_nm2_01">
                <prompt-segments>
                  <audiofile text="That's OK, let's move on" src="AS1010_nm2_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="AS1010_OfferAcctSMS_DM_nomatch_3"/>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AS1010_ini_01">
                <prompt-segments>
                  <audiofile text="Can I text you a link, when your phone's activated? " src="AS1010_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="AS1010_OfferAcctSMS_DM.grxml" count="1"/>
          <dtmfgrammars filename="AS1010_OfferAcctSMS_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="AS1105_SendAcctDetsSMS_DB_DA">
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="campaignId" value="GlobalVars.GetBCSParameters.acct_dets_campaign_id" type="String"/>
      <data-access id="SendTextMessage" classname="com.nuance.metro.dataaccess.SendTextMessage">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="mdn"/>
          <input-variable name="campaignId"/>
          <input-variable name="languageCode"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <if cond="SendTextMessage.status == 'Success'">
          <session-mapping key="GlobalVars.SendTextMessage" expr="SendTextMessage"/>
          <session-mapping key="GlobalVars.acctDetsSMSSuccess" value="true" type="Boolean"/>
          <action next="getReturnLink()"/>
          <else>
            <action next="getReturnLink()"/>
          </else>
        </if>
      </action>
    </data-access-state>

    <play-state id="AS1015_PlayActivationExtraStep_PP">
      <audio>
        <prompt id="AS1015_out_01">
          <prompt-segments>
            <audiofile text="By the way, when we're done here, you'll need to go to metrobyt-mobile dot com slash esim and follow the steps to complete your eSIM activation Before you do, make sure to have a WiFi connection Please know your phone will not be connected to our network until you've taken the steps at metrobyt-mobile dot com slash esim" src="AS1015_out_01.wav"/>
          </prompt-segments>
        </prompt>
      </audio>
      <if cond="(GlobalVars.GetAccountDetails) &amp;&amp; (GlobalVars.GetAccountDetails.accountStatus == 'active')">
        <action next="AS1020_OffereSIMSMS_DM"/>
        <else>
          <action next="AS1005_PlaySMSPreamble_PP"/>
        </else>
      </if>
    </play-state>

    <dm-state id="AS1020_OffereSIMSMS_DM" type="YSNO">
      <success>
        <action label="true">
          <audio>
            <prompt id="AS1020_out_01">
              <prompt-segments>
                <audiofile text="Great" src="AS1020_out_01.wav"/>
              </prompt-segments>
            </prompt>
          </audio>
          <session-mapping key="GlobalVars.campaign_id" expr="'ESIM_ACT'"/>
          <action next="AS1110_SendCompActSMS_DB_DA"/>
        </action>
        <action label="false">
          <if cond="GlobalVars.deviceChangeTargetSIMChosen == 'ESIM'">
            <audio>
              <prompt id="AS1020_out_03">
                <prompt-segments>
                  <audiofile text="Alright" src="AS1020_out_03.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
            <action next="getReturnLink()"/>
            <else>
              <audio>
                <prompt id="AS1020_out_02">
                  <prompt-segments>
                    <audiofile text="No problem Don't forget to to go to metrobyt-mobilecom, and complete your activation " src="AS1020_out_02.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <audio>
                <prompt id="silence_500ms">
                  <prompt-segments>
                    <audiofile text="test" src="silence_500ms.wav"/>
                  </prompt-segments>
                </prompt>
              </audio>
              <action next="AS1005_PlaySMSPreamble_PP"/>
            </else>
          </if>
        </action>
        <action label="operator"/>
      </success>
      <catch>
        <session-mapping key="GlobalVars.eventCapture" expr="_event"/>
      </catch>
      <collection_configuration highconfidencelevel="0.650">
        <threshold_configuration maxnomatches="1" maxnoinputs="1"/>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <prompt id="AS1020_ini_01">
                <prompt-segments>
                  <audiofile text="Can I text you this link to this phone you're calling from? " src="AS1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </initialprompt>
          <reinvokeprompt count="1" bargein="true" filename="" text="" id="AS1020_OffereSIMSMS_DM_reinvoke"/>
          <helpprompts count="1" bargein="true" filename="" text="" id="AS1020_OffereSIMSMS_DM_initial"/>
          <helpprompts count="2" bargein="true" filename="" text="" id="AS1020_OffereSIMSMS_DM_initial"/>
          <repeatprompts count="1">
            <audio>
              <prompt id="AS1020_OffereSIMSMS_DM_reinvoke"/>
            </audio>
          </repeatprompts>
          <repeatprompts count="2">
            <audio>
              <prompt id="AS1020_OffereSIMSMS_DM_reinvoke"/>
            </audio>
          </repeatprompts>
          <repeatprompts count="3">
            <audio>
              <prompt id="AS1020_OffereSIMSMS_DM_reinvoke"/>
            </audio>
          </repeatprompts>
          <repeatprompts count="4"/>
          <nomatchprefixes count="1"/>
          <nomatchprefixes count="2"/>
          <nomatchprefixes count="3"/>
          <noinputprefixes count="1"/>
          <noinputprefixes count="2"/>
          <noinputprefixes count="3"/>
          <noinputprompts count="1">
            <audio>
              <prompt id="AS1020_nm1_01">
                <prompt-segments>
                  <audiofile text="Should I text you the link to complete your eSIM activation? Say 'yes' or press 1, or 'no' or press 2 " src="AS1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </noinputprompts>
          <noinputprompts count="2">
            <audio>
              <prompt id="AS1020_OffereSIMSMS_DM_noinput_2"/>
            </audio>
          </noinputprompts>
          <noinputprompts count="3">
            <audio>
              <prompt id="AS1020_OffereSIMSMS_DM_noinput_3"/>
            </audio>
          </noinputprompts>
          <nomatchprompts count="1">
            <audio>
              <prompt id="gl_nm1_01">
                <prompt-segments>
                  <audiofile text="I didn't get that " src="gl_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
              <prompt id="AS1020_nm1_01">
                <prompt-segments>
                  <audiofile text="Should I text you the link to complete your eSIM activation? Say 'yes' or press 1, or 'no' or press 2 " src="AS1020_nm1_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="2">
            <audio>
              <prompt id="AS1020_OffereSIMSMS_DM_nomatch_2"/>
            </audio>
          </nomatchprompts>
          <nomatchprompts count="3">
            <audio>
              <prompt id="AS1020_OffereSIMSMS_DM_nomatch_3"/>
            </audio>
          </nomatchprompts>
          <notoconfirmprefixes count="2"/>
          <notoconfirmprefixes count="3"/>
          <notoconfirmprefixes count="1">
            <audio>
              <prompt id="gl_cnf_out_01">
                <prompt-segments>
                  <audiofile text="My Mistake" src="gl_cnf_out_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprefixes>
          <notoconfirmprompts count="1">
            <audio>
              <prompt id="AS1020_ini_01">
                <prompt-segments>
                  <audiofile text="Can I text you this link to this phone you're calling from? " src="AS1020_ini_01.wav"/>
                </prompt-segments>
              </prompt>
            </audio>
          </notoconfirmprompts>
        </prompt_configuration>
        <grammar_configuration commandgrammar="GlobalCommands_common.grxml" dtmfcommandgrammar="GlobalCommands_common_dtmf.grxml">
          <grammars filename="AS1020_OffereSIMSMS_DM.grxml" count="1"/>
          <dtmfgrammars filename="AS1020_OffereSIMSMS_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.500" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="IF_NECESSARY">
        <threshold_configuration maxrepeats="3" maxnomatches="3" maxnoinputs="3"/>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="500ms" termtimeout="300ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration>
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.150" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <data-access-state id="AS1110_SendCompActSMS_DB_DA">
      <session-mapping key="languageCode" value="language == 'en-US' ? 'en' : 'es'" type="String"/>
      <session-mapping key="mdn" value="GlobalVars.mdn" type="String"/>
      <session-mapping key="sessionId" value="appsessionID" type="String"/>
      <session-mapping key="campaignId" value="ESIM_ACT" type="String"/>
      <data-access id="SendTextMessage" classname="com.nuance.metro.dataaccess.SendTextMessage">
        <fetchAudio>fetch_audio_30s</fetchAudio>
        <inputs>
          <input-variable name="sessionId"/>
          <input-variable name="mdn"/>
          <input-variable name="campaignId"/>
          <input-variable name="languageCode"/>
        </inputs>
        <outputs>
          <output-variable name="status"/>
        </outputs>
      </data-access>
      <action label="default">
        <session-mapping key="GlobalVars.SendTextMessage" expr="SendTextMessage"/>
        <action next="AS1005_PlaySMSPreamble_PP"/>
      </action>
    </data-access-state>

  </dialog>
  