import os
import yaml
from ruamel.yaml import <PERSON><PERSON><PERSON>

def fixed_topic_yaml_indentation(input_file, output_file):
    """
    Properly indent YAML files by moving content after line 10 under the actions key
    """
    try:
        # Check if input file exists and has content
        if not os.path.exists(input_file):
            print(f"Input file does not exist: {input_file}")
            return False
        
        with open(input_file, 'r', encoding='utf-8') as infile:
            content = infile.read()
        
        if not content.strip():
            print(f"Input file is empty: {input_file}")
            # Create empty output file
            with open(output_file, 'w', encoding='utf-8') as outfile:
                outfile.write('')
            return False
        
        # First, try to validate the YAML syntax
        try:
            yaml_obj = YAML()
            yaml_obj.load(content)
            print("YAML is valid.")
        except Exception as yaml_error:
            print(f"YAML validation error in {input_file}: {yaml_error}")
            # Try to fix common YAML syntax issues
            content = fix_yaml_syntax_issues(content)
        
        lines = content.splitlines(keepends=True)
        
        if len(lines) == 0:
            # Handle empty files
            with open(output_file, 'w', encoding='utf-8') as outfile:
                outfile.write('')
            return False
        
        # Find the line with "actions: null" or "actions:" and replace it
        actions_line_index = -1
        for i, line in enumerate(lines):
            if 'actions: null' in line:
                lines[i] = line.replace('actions: null', 'actions:')
                actions_line_index = i
                break
            elif line.strip().endswith('actions:') or 'actions: _REPLACE_THIS' in line:
                lines[i] = line.replace('actions: _REPLACE_THIS', 'actions:').replace('actions:', 'actions:')
                actions_line_index = i
                break
        
        # If no "actions:" found, write file as-is
        if actions_line_index == -1:
            with open(output_file, 'w', encoding='utf-8') as outfile:
                outfile.writelines(lines)
            return True
        
        # Calculate the base indentation for the actions content
        actions_line = lines[actions_line_index]
        base_indent = len(actions_line) - len(actions_line.lstrip())
        new_indent = ' ' * (base_indent + 2)
        
        # Write the output file
        with open(output_file, 'w', encoding='utf-8') as outfile:
            # Write everything up to and including the actions line
            for i in range(actions_line_index + 1):
                outfile.write(lines[i])
            
            # Write the remaining lines with proper indentation under actions
            for i in range(actions_line_index + 1, len(lines)):
                line = lines[i]
                if line.strip():  # Only process non-empty lines
                    # Remove any existing indentation and add the new base indentation
                    content_line = line.lstrip()
                    if content_line.startswith('- '):
                        # This is a list item, use the calculated indentation
                        outfile.write(new_indent + content_line)
                    else:
                        # This is a continuation, add 2 more spaces
                        outfile.write(new_indent + '  ' + content_line)
                else:
                    # Empty line, write as-is
                    outfile.write(line)
        
        print(f"Successfully processed: {input_file} -> {output_file}")
        return True
                    
    except Exception as e:
        print(f"Error processing {input_file}: {e}")
        # If there's an error, try to copy the input to output
        try:
            with open(input_file, 'r', encoding='utf-8') as infile:
                content = infile.read()
            with open(output_file, 'w', encoding='utf-8') as outfile:
                outfile.write(content)
            print(f"Copied input to output due to processing error")
            return True
        except:
            # If even copying fails, create an empty file
            with open(output_file, 'w', encoding='utf-8') as outfile:
                outfile.write('')
            print(f"Created empty output file due to errors")
            return False

def fix_yaml_syntax_issues(content):
    """
    Fix common YAML syntax issues
    """
    lines = content.split('\n')
    fixed_lines = []
    
    for line in lines:
        # Fix "mapping values are not allowed here" by checking for proper indentation
        if ':' in line and not line.strip().startswith('#'):
            # Check if the line has proper structure
            parts = line.split(':', 1)
            if len(parts) == 2:
                key = parts[0].strip()
                value = parts[1].strip()
                indent = len(line) - len(line.lstrip())
                
                # Reconstruct the line with proper formatting
                if value:
                    fixed_line = ' ' * indent + f"{key}: {value}"
                else:
                    fixed_line = ' ' * indent + f"{key}:"
                fixed_lines.append(fixed_line)
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)
                        # This is a list item, use the calculated indentation
                        outfile.write(new_indent + content)
                    else:
                        # This is a continuation, add 2 more spaces
                        outfile.write(new_indent + '  ' + content)
                else:
                    # Empty line, write as-is
                    outfile.write(line)
                    
    except Exception as e:
        print(f"Error processing {input_file}: {e}")
        # If there's an error, try to copy the input to output
        try:
            with open(input_file, 'r', encoding='utf-8') as infile:
                content = infile.read()
            with open(output_file, 'w', encoding='utf-8') as outfile:
                outfile.write(content)
        except:
            # If even copying fails, create an empty file
            with open(output_file, 'w', encoding='utf-8') as outfile:
                outfile.write('')
        raise

if __name__ == "__main__":
    # Test the function
    test_input = "c:/Users/<USER>/Documents/Projects/NDF-MCS/ndf-to-mcs/AI-Interim-Xml-File-to-Copilot-YAML/output/unformatted_topic_yaml/MainMenu_Main_Dialog.yml"
    test_output = "c:/Users/<USER>/Documents/Projects/NDF-MCS/ndf-to-mcs/AI-Interim-Xml-File-to-Copilot-YAML/test_fixed_output.yml"
    
    try:
        fixed_topic_yaml_indentation(test_input, test_output)
        print("SUCCESS: Fixed indentation function completed")
        
        # Check if output file exists and has content
        if os.path.exists(test_output):
            with open(test_output, 'r') as f:
                content = f.read()
            print(f"Output file size: {len(content)} characters")
            print("First 500 characters:")
            print(content[:500])
        else:
            print("ERROR: Output file was not created")
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
