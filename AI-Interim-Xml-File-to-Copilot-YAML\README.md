# IVR Conversion Project

This project is designed to convert IVR (Interactive Voice Response) systems from an XML-based format to a YAML-based format. It includes several Python scripts that work together to process input XML files, generate YAML templates, and produce a final bot YAML configuration.

## Scripts Overview

1. `run_all_in_one.py`: The main script that orchestrates the execution of all other scripts in the correct order.

2. `step0_input_cleanup.py`: Cleans up the input XML files by removing colons in audiofile text and updating IDs, etc.

3. `step1_separate_dialogs.py`: Separates dialog files from the main XML file and extracts DVXML views.

4. `step2_generate_topic_template.py`: Creates topic-level YAML templates based on the separated dialog files.

5. `step3_variable_extraction.py`: Extracts variables from all the conditions and dataaccess from the XML files.

6. `step4_callAgentDecision.py`: Processes different types of states (e.g., data access, decision, play) and generates corresponding YAML content.

7. `step5_generate_bot_yaml.py`: Combines all generated YAML files into a final bot YAML configuration.

8. `step6_post_processing.py` : Do some post processing action like adding audio_location and so on.

9. `indentation.py`: Handles YAML formatting and indentation.

10. `llm_response.py`: Interacts with a language model (LLM) to generate YAML content from XML input.

11. `merge_topic.py`: Concatenates YAML files.

12. `util.py`: Contains utility functions used across multiple scripts.

13. `app.py` : use to run this app through reuqest url.

14. `app.properties` : audio file location configuration.

## Dependencies

- Python 3.x
- Required Python packages:
  - yaml
  - ruamel.yaml
  - xml.etree.ElementTree
  - lxml
  - openai (for Azure OpenAI integration)
  - dotenv

## Usage

1. Ensure all dependencies are installed.
2. Place your input XML files in the appropriate input directories.
3. Set up environment variables for Azure OpenAI if required.
4. Run the `run_all_in_one.py` script:
   ```
   python run_all_in_one.py
   ```

5. The final bot YAML configuration will be generated in the `output/bot_yaml/` directory.

## File Structure

- `input/`: Contains input XML files
  - `dialogs_file/`: Separated dialog XML files
  - `custom_file/`: Custom state XML files
  - `dvxml_file/`: DVXML files
- `output/`: Contains generated YAML files
  - `topic_yaml/`: Individual topic YAML files
  - `bot_yaml/`: Final bot YAML configuration
- `YAML-template/`: Contains YAML templates
- `prompts/`: Contains prompt files for the LLM

## Error Handling

- Failed file processing is logged, and the corresponding XML files are moved to a `rerun` folder for later processing.
- The script includes retry mechanisms for certain operations.

## Logging

- The script logs information and errors to `agent.log`.

## Note

This project relies on specific XML structures and YAML templates. Ensure your input files conform to the expected format for successful conversion.
