<?xml version='1.0' encoding='UTF-8'?>
<dialog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="CollectData_Dialog">
    <decision-state id="DC1001_CheckContext_DS">
      <session-mapping key="GlobalVars.dataCollectionTransfer" value="true" type="Boolean"/>
      <action next="DC0030_AskIntent_DM"/>
    </decision-state>

    <dm-state id="DC0030_AskIntent_DM" type="CUST">
      <success>
        <action label="dtmf_entry">
          <session-mapping key="GlobalVars.playTransferMessage " value="false" type="Boolean"/>
          <audio>
            <prompt id="DC0030_nm1_01">
              <prompt-segments>
                <audiofile text="One moment while I transfer you" src="DC0030_nm1_01.wav">
            </audiofile>
              </prompt-segments>
            </prompt>
          </audio>
          <action next="DC0050_CallTransfer_SD"/>
        </action>
        <action label="operator">
          <session-mapping key="agentCounter" expr="agentCounter + 1"/>
          <if cond="agentCounter == 1">
            <session-mapping key="IntentMSG" expr="'operator_request'"/>
            <action next="DC0030_AskIntent_DM"/>
            <else>
              <session-mapping key="GlobalVars.playTransferMessage " value="false" type="Boolean"/>
              <if cond="agentCounter &lt; 1">
                <audio>
                  <prompt id="DC0030_out_01">
                    <prompt-segments>
                      <audiofile text="Thanks, we'll use your response to improve our services for the future" src="DC0030_out_01.wav">
            </audiofile>
                    </prompt-segments>
                  </prompt>
                </audio>
                <action next="DC0050_CallTransfer_SD"/>
                <else>
                  <audio>
                    <prompt id="DC0030_out_02">
                      <prompt-segments>
                        <audiofile text="I didn't quite get that One moment while I transfer you" src="DC0030_out_02.wav">
            </audiofile>
                      </prompt-segments>
                    </prompt>
                  </audio>
                  <action next="DC0050_CallTransfer_SD"/>
                </else>
              </if>
            </else>
          </if>
        </action>
      </success>
      <collection_configuration highconfidencelevel="0.600">
        <threshold_configuration maxnomatches="0" maxnoinputs="0" maxturns="2">
        </threshold_configuration>
        <prompt_configuration>
          <initialprompt count="1">
            <audio>
              <if type="java">
                <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                  <param name="numToMatch" value="1"/>
                  <param name="startNum" value="1"/>
                  <param name="endNum" value="4"/>
                </condition>
                <prompt id="DC0030_ini_01_few_calling_about" cond="IntentMSG == 'Default'">
                  <prompt-segments>
                    <audiofile text="What ll it be today? Tell me in a few words" src="DC0030_ini_01_few_calling_about.wav">
            </audiofile>
                  </prompt-segments>
                </prompt>
                <prompt id="DC0030_re_07_right_rep" cond="IntentMSG == 'operator_request'">
                  <prompt-segments>
                    <audiofile text="To get you to the right representative, please tell me the reason for your call" src="DC0030_re_07_right_rep.wav">
            </audiofile>
                  </prompt-segments>
                </prompt>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="2"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="DC0030_ini_02_calling_about" cond="IntentMSG == 'Default'">
                    <prompt-segments>
                      <audiofile text="What can I do for you? Tell me in a couple words" src="DC0030_ini_02_calling_about.wav">
            </audiofile>
                    </prompt-segments>
                  </prompt>
                  <prompt id="DC0030_re_08_right_here" cond="IntentMSG == 'operator_request'">
                    <prompt-segments>
                      <audiofile text="I can help you right here, so please, tell me the reason for your call" src="DC0030_re_08_right_here.wav">
            </audiofile>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <elseif>
                  <condition classname="com.nuance.metro.sentence.conditionals.IfRandom">
                    <param name="numToMatch" value="3"/>
                    <param name="startNum" value="1"/>
                    <param name="endNum" value="4"/>
                  </condition>
                  <prompt id="DC0030_ini_04_few_help" cond="IntentMSG == 'Default'">
                    <prompt-segments>
                      <audiofile text="Let s get started! In a few words, what can I help you with?" src="DC0030_ini_04_few_help.wav">
            </audiofile>
                    </prompt-segments>
                  </prompt>
                  <prompt id="DC0030_re_09_help_better" cond="IntentMSG == 'operator_request'">
                    <prompt-segments>
                      <audiofile text="In order to help you better, please tell me the reason for your call" src="DC0030_re_09_help_better.wav">
            </audiofile>
                    </prompt-segments>
                  </prompt>
                </elseif>
                <else>
                  <prompt id="DC0030_ini_05_help" cond="IntentMSG == 'Default'">
                    <prompt-segments>
                      <audiofile text="How can I make your day? Tell me in your own words" src="DC0030_ini_05_help.wav">
            </audiofile>
                    </prompt-segments>
                  </prompt>
                  <prompt id="DC0030_re_10_understand" cond="IntentMSG == 'operator_request'">
                    <prompt-segments>
                      <audiofile text="I understand you'd like to speak with someone, but first please tell me the reason for your call" src="DC0030_re_10_understand.wav">
            </audiofile>
                    </prompt-segments>
                  </prompt>
                </else>
              </if>
            </audio>
          </initialprompt>
          <reinvokeprompt text="" id="DC0030_AskIntent_DM_reinvoke" filename="" count="1" bargein="true"/>
        </prompt_configuration>
        <grammar_configuration>
          <grammars filename="DC0030_AskIntent_DM.grxml" count="1"/>
          <dtmfgrammars filename="DC0030_AskIntent_DM_dtmf.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.450" incompletetimeout="2500ms" completetimeout="0ms" maxspeechtimeout="15000ms" timeout="7000ms"/>
      </collection_configuration>
      <global_configuration confirmationmode="NEVER">
        <threshold_configuration maxnomatches="3" maxnoinputs="3">
        </threshold_configuration>
        <failureprompt count="1"/>
        <successprompts count="1"/>
        <successprompts count="2"/>
        <successprompts count="3"/>
        <successcorrectedprompt count="1"/>
        <vxml_properties confidencelevel="0.400" sensitivity="0.500" bargein="true" bargeintype="speech" interdigittimeout="3000ms" termtimeout="200ms" timeout="7000ms"/>
      </global_configuration>
      <confirmation_configuration highconfidencelevel="0.550">
        <grammar_configuration supportsadaptivegrammar="false">
          <grammars filename="confirmation.grxml" count="1"/>
        </grammar_configuration>
        <vxml_properties confidencelevel="0.400" incompletetimeout="500ms" completetimeout="0ms" maxspeechtimeout="5000ms" timeout="7000ms"/>
      </confirmation_configuration>
    </dm-state>

    <subdialog-state id="DC0050_CallTransfer_SD">
      <gotodialog next="CallTransfer_Main_Dialog"/>
    </subdialog-state>
  </dialog>
  