<states-library xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../xsd/states.xsd">
	<dialog id="ApplicationRoot">
		<session-mapping key="errorCount" value="0" type="String"/>
		<session-mapping key="dataaccessErrorCount" value="0" type="String"/>
		<session-mapping key="badFetchErrorCount" value="0" type="String"/>
		<session-mapping key="library" value="default" type="String"/>
		<session-mapping key="version" value="0.1" type="String"/>
		<session-mapping key="applicationname" value="undefined" type="String"/>
		<session-mapping key="appsessionID" value="undefined" type="String"/>
		<session-mapping key="appstepID" value="undefined" type="String"/>
		<session-mapping key="returncode" value="undefined" type="String"/>
		<event name="connection.disconnect.hangup"/>
		<event name="telephone.disconnect.hangup"/>
		<event name="connection.disconnect.transfer"/>
		<event name="event.nuance.dialog.ndm.internalerror event.nuance.dialog.ndm.no_matching_command_reco_option event.nuance.dialog.ndm.no_matching_success_reco_option"/>
		<event name="event.nuance.dialog.ndm.maxnoinputs"/>
		<event name="event.nuance.dialog.ndm.maxretries"/>
		<event name="event.nuance.dialog.ndm.maxnomatches"/>
		<event name="event.nuance.dialog.ndm.maxnotoconfirms"/>
		<event name="event.nuance.dialog.ndm.maxturns"/>
		<event name="event.nuance.dialog.ndm.maxrepeats"/>
		<event name="event.nuance.dialog.ndm.maxhelps"/>
		<event name="event.nuance.dialog"/>
		<event name="error.nuance.dataaccess.system">
			<session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1"/>
		</event>
		<event name="error.nuance.dataaccess.system">
			<session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1"/>
		</event>
		<event name="error.nuance.dataaccess.system">
			<exit/>
		</event>
		<event name="event.nuance.dataaccess.business">
			<session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1"/>
		</event>
		<event name="event.nuance.dataaccess.business">
			<session-mapping key="dataaccessErrorCount" expr="dataaccessErrorCount + 1"/>
		</event>
		<event name="event.nuance.dataaccess.business">
			<exit/>
		</event>
		<event name="error.badfetch">
			<session-mapping key="badFetchErrorCount" expr="badFetchErrorCount + 1"/>
		</event>
		<event name="error.badfetch">
			<session-mapping key="badFetchErrorCount" expr="badFetchErrorCount + 1"/>
		</event>
		<event name="error.badfetch">
			<exit/>
		</event>
		<event name="error.internal">
			<session-mapping key="errorCount" expr="errorCount + 1"/>
		</event>
		<event cond="errorCount == 0 &amp;amp;&amp;amp; returncode!='HANGUP'">
			<session-mapping key="errorCount" expr="errorCount + 1"/>
		</event>
		<event cond="errorCount == 1 &amp;amp;&amp;amp; returncode!='HANGUP'">
			<session-mapping key="errorCount" expr="errorCount + 1"/>
		</event>
		<event cond="errorCount &gt;= 1 &amp;amp;&amp;amp; returncode!='HANGUP'">
			<exit/>
		</event>
	</dialog>
	<dialog id="hl00_CareSharedHL_SD">
		<script className="com.nuance.ps.telefonica.scripts.Initialize"/>
		<custom-state id="setCareShareVariables">
			<action next="hl0001_GetGenesys_KVP"/>
		</custom-state>
		<custom-state id="hl0001_GetGenesys_KVP">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.HandleDirectEntries"/>
			<action label="default" next="hl0002_lookup_init_CDP_DB"/>
		</custom-state>
		<data-access-state id="hl0002_lookup_init_CDP_DB">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<var name="msisdn" path="msisdn" scope="session" isNamelist="true"/>
			<var name="simnumber" path="simnumber" scope="session" isNamelist="true"/>
			<var name="brandId" path="urBrand" scope="session" isNamelist="true"/>
			<var name="iConnId" path="urFirstConnId" scope="session" isNamelist="true"/>
			<data-access id="LookupInitCDP" classname="com.nuance.ps.telefonica.dataaccess.LookupInitCDP">
				<inputs>
					<input-variable name="msisdn" mask="false"/>
					<input-variable name="simnumber" mask="false"/>
					<input-variable name="brandId" mask="false"/>
					<input-variable name="iConnId" mask="false"/>
				</inputs>
				<outputs>
					<output-variable name="pkk" mask="false"/>
					<output-variable name="returnCode" mask="false"/>
				</outputs>
			</data-access>
			<action next="hl0010_WhatsNext_DS"/>
		</data-access-state>
		<decision-state id="hl0010_WhatsNext_DS">
			<action label="goToAgent" next="hl0020_TransferToAgent_PP"/>
			<action label="hangupRequired" next="hl0030_CallTerminate_PP"/>
			<action label="callsteering" next="hl0040_NLCS_SD"/>
			<action label="simcard" next="hl0060_Sim_SD"/>
			<action label="authenticate" next="hl0090_Authentication_SD"/>
			<action label="k2" next="hl0080_K2_SD"/>
			<action label="dsl" next="hl0050_DSL_SD"/>
			<action label="webinfo" next="hl0045_WebInfo_PP"/>
			<action label="sendsms" next="hl0070_SendSMS_SD"/>
			<action label="usageInfo" next="hl0120_UsageInfo_SD"/>
			<action label="addressTopics" next="hl0140_AdressTopics_SD"/>
			<action label="dataContractUpgrade" next="hl0110_DataContractUpgrade_SD"/>
			<action label="thirdPartyBlock" next="hl0130_ThirdPartyBlocking_SD"/>
			<action label="other" next="hl0100_OtherService_DM"/>
			<action label="vssPreMenu" next="hl0150_VSS_PreMenu_DM"/>
			<action label="error" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
			</action>
			<action label="hungup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</action>
			<action label="default" next="Return"/>
		</decision-state>
		<custom-state id="CustomerAgentQueue">
			<action next="Return"/>
		</custom-state>
		<data-access-state id="Return">
			<session-mapping key="returncode" value="SUCCESS"/>
			<script className="com.nuance.ps.telefonica.scripts.PrepareReturn"/>
			<data-access id="PublishReport" classname="com.nuance.ps.telefonica.dataaccess.PublishReport">
				<inputs>
					<input-variable name="appsessionID" mask="false"/>
					<input-variable name="json" mask="false"/>
				</inputs>
				<outputs>
					<output-variable name="returnCode" mask="false"/>
				</outputs>
			</data-access>
			<action next="exit"/>
		</data-access-state>
		<data-access-state id="Hangup">
			<session-mapping key="returncode" value="HANGUP"/>
			<script className="com.nuance.ps.telefonica.scripts.PrepareReturn"/>
			<data-access id="PublishReport" classname="com.nuance.ps.telefonica.dataaccess.PublishReport">
				<inputs>
					<input-variable name="appsessionID" mask="false"/>
					<input-variable name="json" mask="false"/>
				</inputs>
				<outputs>
					<output-variable name="returnCode" mask="false"/>
				</outputs>
			</data-access>
			<action next="exit"/>
		</data-access-state>
		<play-state id="hl0020_TransferToAgent_PP">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.SetRoutingValues"/>
			<audio>
				<if type="java">
					<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
						<param name="compare" value="true"/>
						<param name="value" scope="session" value="forceCallerToHangup"/>
					</condition>
					<prompt id="silence_3500ms">
						<prompt-segments>
							<audiofile src="silence_3500ms.wav" text=""/>
						</prompt-segments>
					</prompt>
					<prompt id="hl0020_out_01">
						<prompt-segments>
							<audiofile src="hl0020_out_01.wav" text="Vielen dank f&#252;r ihren Anruf."/>
						</prompt-segments>
					</prompt>
					<prompt id="silence_6000ms">
						<prompt-segments>
							<audiofile src="silence_6000ms.wav" text=""/>
						</prompt-segments>
					</prompt>
				</if>
				<if type="java">
					<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
						<param name="compare" value="0"/>
						<param name="value" scope="session" value="authenticated"/>
					</condition>
					<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
						<param name="compare" value="VSS"/>
						<param name="value" scope="session" value="ivrOriginalEntry"/>
					</condition>
					<prompt id="hl0020_out_03">
						<prompt-segments>
							<audiofile src="hl0020_out_03.wav" text="Wir verbinden Sie jetzt mit der Kundenhotline."/>
						</prompt-segments>
					</prompt>
				</if>
				<if type="java">
					<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
						<param name="compare" value="kuendigungMobil"/>
						<param name="value" scope="session" value="applicationtag"/>
					</condition>
					<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
						<param name="compare" value="N"/>
						<param name="value" scope="session" value="winback"/>
					</condition>
					<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
						<param name="compare" value="SendSMS"/>
						<param name="value" scope="session" value="comingFrom"/>
						<param name="operator" value="NOT"/>
					</condition>
					<prompt id="hl0020_out_02">
						<prompt-segments>
							<audiofile src="hl0020_out_02.wav" text="Heute kann es leider ein wenig l&#228;nger dauern. Falls Sie Ihren Vertrag k&#252;ndigen m&#246;chten, k&#246;nnen Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2.de/goto/kuendigen. Ich wiederhole: o2.de/goto/kuendigen."/>
						</prompt-segments>
					</prompt>
					<elseif>
						<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
							<param name="compare" value="kuendigungInfoMobil"/>
							<param name="value" scope="session" value="applicationtag"/>
						</condition>
						<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
							<param name="compare" value="N"/>
							<param name="value" scope="session" value="winback"/>
						</condition>
						<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
							<param name="compare" value="SendSMS"/>
							<param name="value" scope="session" value="comingFrom"/>
							<param name="operator" value="NOT"/>
						</condition>
						<prompt id="hl0020_out_02">
							<prompt-segments>
								<audiofile src="hl0020_out_02.wav" text="Heute kann es leider ein wenig l&#228;nger dauern. Falls Sie Ihren Vertrag k&#252;ndigen m&#246;chten, k&#246;nnen Sie dies nur in unserem Onlineportal direkt vornehmen- unter o2.de/goto/kuendigen. Ich wiederhole: o2.de/goto/kuendigen."/>
							</prompt-segments>
						</prompt>
					</elseif>
				</if>
			</audio>
			<action next="CustomerAgentQueue">
				<if cond="('kuendigungMobil'==applicationtag || 'kuendigungInfoMobil'==applicationtag)                 &amp;&amp; ('MSISDN' == ivrCliType || ( 'MSISDN' != ivrCliType &amp;&amp; 'MSISDN' == ivrIdType &amp;&amp; '1' == authenticated ))                 &amp;&amp; 'Y' != winback                &amp;&amp; CorbaAccessible                &amp;&amp; 'SendSMS' != comingFrom">
					<session-mapping key="goToSMS" value="true"/>
					<action next="hl0070_SendSMS_SD"/>
					<else>
						<action next="CustomerAgentQueue"/>
					</else>
				</if>
			</action>
		</play-state>
		<play-state id="hl0030_CallTerminate_PP">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<audio>
				<if type="java">
					<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
						<param name="compare" value="VSS"/>
						<param name="value" scope="session" value="ivrOriginalEntry"/>
					</condition>
					<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
						<param name="compare" value="EASY"/>
						<param name="value" scope="session" value="ivrCustomerSegment"/>
						<param name="operator" value="NOT"/>
					</condition>
					<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
						<param name="compare" value="UNQ"/>
						<param name="value" scope="session" value="ivrIdResult"/>
					</condition>
					<prompt id="hl0030_out_02">
						<prompt-segments>
							<audiofile src="hl0030_out_02.wav" text="Wir haben Sie leider nicht identifizieren k&#246;nnen. Sie k&#246;nnen Ihre Kundenbetreuung anrufen oder auf unserem Online Portal einfach und bequem ihre &#196;nderungen durchf&#252;hren oder mit uns chatten. Auf Wiederh&#246;ren und vielen Dank f&#252;r Ihren Anruf."/>
						</prompt-segments>
					</prompt>
					<else>
						<prompt id="hl0030_out_01">
							<prompt-segments>
								<audiofile src="hl0030_out_01.wav" text="Vielen Dank f&#252;r Ihren Anruf."/>
							</prompt-segments>
						</prompt>
					</else>
				</if>
			</audio>
			<action next="hl0035_general_call_terminate"/>
		</play-state>
		<custom-state id="hl0035_general_call_terminate">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<action next="Hangup"/>
		</custom-state>
		<play-state id="hl0045_WebInfo_PP">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.SelfServiceStart">
				<param name="name" value="INFOTAINMENT"/>
			</script>
			<audio>
				<if type="java">
					<condition classname="com.nuance.ps.telefonica.prompts.HasWebInfoPrompt">
						<param name="name" value="dynamic"/>
					</condition>
					<prompt type="custom" expr="dynamic" scope="request">
						<param name="className" value="com.nuance.framework.vxml.service.audio.dynamic.renderer.VXMLDynamicPromptNameRendererImpl"/>
					</prompt>
				</if>
			</audio>
			<action next="hl0010_WhatsNext_DS">
				<if cond="hangupRequired == 'false' &amp;&amp; goToSMS == 'false' &amp;&amp; goToOtherService == 'false' &amp;&amp; (serviceTarget == 'undefined' || serviceTarget == '' || serviceTarget == '#')">
					<session-mapping key="forceCallerToHangup" value="true"/>
					<session-mapping key="goToAgent" value="true"/>
					<session-mapping key="webInfoRequired" value="false"/>
					<script className="com.nuance.ps.telefonica.scripts.SelfServiceComplete">
						<param name="name" value="INFOTAINMENT"/>
					</script>
					<action next="hl0010_WhatsNext_DS"/>
					<else>
						<session-mapping key="webInfoRequired" value="false"/>
						<script className="com.nuance.ps.telefonica.scripts.SelfServiceComplete">
							<param name="name" value="INFOTAINMENT"/>
						</script>
						<action next="hl0010_WhatsNext_DS"/>
					</else>
				</if>
			</action>
		</play-state>
		<custom-state id="hl0040_NLCS_SD">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
			<session-mapping key="authenticationRequired" value="true"/>
			<session-mapping key="formID" value="Invoke_NLCS_SD"/>
			<view name="/InvokeCS.dvxml"/>
		</custom-state>
		<custom-state id="ReturnFromNclsCs">
			<session-mapping key="comingFrom" value="CallSteering"/>
			<session-mapping key="returncode" scope="request" path="returncode"/>
			<session-mapping key="event" scope="request" path="event"/>
			<script className="com.nuance.ps.telefonica.scripts.UpdateFromNLCS"/>
			<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.ProcessMIS"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
			<script className="com.nuance.ps.telefonica.scripts.ChangeBrandColor"/>
			<script className="com.nuance.ps.telefonica.scripts.ChangeExitTags"/>
			<script className="com.nuance.ps.telefonica.scripts.PostProcessNLCS"/>
			<script className="com.nuance.ps.telefonica.scripts.ReportCallSteeringResult"/>
			<action next="hl0010_WhatsNext_DS"/>
			<event name="connection.disconnect.hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
				<script className="com.nuance.ps.telefonica.scripts.ProcessMIS">
					<param name="cause" value="hangup"/>
				</script>
				<script className="com.nuance.ps.telefonica.scripts.ReportCallSteeringResult"/>
			</event>
			<event name="error" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
				<script className="com.nuance.ps.telefonica.scripts.ProcessMIS">
					<param name="cause" value="error"/>
				</script>
			</event>
		</custom-state>
		<custom-state id="hl0050_DSL_SD">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
			<session-mapping key="formID" value="Invoke_DSL_SD"/>
			<view name="/InvokeCS.dvxml"/>
		</custom-state>
		<decision-state id="ReturnFromDSL">
			<session-mapping key="comingFrom" value="DSL"/>
			<session-mapping key="serviceTarget" value="#"/>
			<script className="com.nuance.ps.telefonica.scripts.UpdateFromDSL"/>
			<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
			<action next="hl0010_WhatsNext_DS">
				<session-mapping key="serviceTarget" value="undefined"/>
			</action>
			<action label="hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</action>
			<event name="connection.disconnect.hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</event>
			<event name="error" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
			</event>
		</decision-state>
		<custom-state id="hl0060_Sim_SD">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
			<session-mapping key="formID" value="Invoke_Sim_SD"/>
			<view name="/InvokeCS.dvxml"/>
		</custom-state>
		<decision-state id="ReturnFromSim">
			<session-mapping key="comingFrom" value="SimCard"/>
			<script className="com.nuance.ps.telefonica.scripts.UpdateFromSim"/>
			<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
			<action label="default" next="hl0010_WhatsNext_DS">
				<if cond="authenticated == '0' &amp;&amp; authenticationRequired == 'true'">
					<action label="reauthentication" next="hl0002_lookup_init_CDP_DB"/>
					<else>
						<session-mapping key="serviceTarget" value="undefined"/>
						<action label="routing" next="hl0010_WhatsNext_DS"/>
					</else>
				</if>
			</action>
			<action label="hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</action>
			<event name="connection.disconnect.hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</event>
			<event name="error" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
			</event>
		</decision-state>
		<subdialog-state id="hl0070_SendSMS_SD">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<gotodialog next="SendSMS_SD"/>
			<action next="hl0010_WhatsNext_DS">
				<session-mapping key="comingFrom" value="SendSMS"/>
				<if cond="hangupRequired == 'false' &amp;&amp; goToOtherService == 'false' &amp;&amp; (serviceTarget == 'undefined' || serviceTarget == '' || serviceTarget == '#')">
					<session-mapping key="forceCallerToHangup" value="true"/>
					<session-mapping key="goToAgent" value="true"/>
					<session-mapping key="goToSMS" value="false"/>
					<action next="hl0010_WhatsNext_DS"/>
					<else>
						<session-mapping key="goToSMS" value="false"/>
						<action next="hl0010_WhatsNext_DS"/>
					</else>
				</if>
			</action>
		</subdialog-state>
		<custom-state id="hl0080_K2_SD">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
			<session-mapping key="formID" value="Invoke_K2_SD"/>
			<view name="/InvokeCS.dvxml"/>
		</custom-state>
		<custom-state id="ReturnFromK2">
			<session-mapping key="comingFrom" value="K2"/>
			<script className="com.nuance.ps.telefonica.scripts.UpdateFromK2"/>
			<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
			<action next="hl0010_WhatsNext_DS"/>
			<event name="connection.disconnect.hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</event>
			<event name="error" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
			</event>
		</custom-state>
		<custom-state id="hl0090_Authentication_SD">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
			<session-mapping key="formID" value="Invoke_Authentication_SD"/>
			<view name="/InvokeCS.dvxml"/>
		</custom-state>
		<decision-state id="ReturnFromAuthentication">
			<session-mapping key="comingFrom" value="Authentication"/>
			<session-mapping key="authenticationRequired" value="false"/>
			<script className="com.nuance.ps.telefonica.scripts.UpdateFromAuthentication"/>
			<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
			<action label="default" next="hl0010_WhatsNext_DS"/>
			<action label="hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</action>
			<event name="connection.disconnect.hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</event>
			<event name="error" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
			</event>
		</decision-state>
		<subdialog-state id="hl0120_UsageInfo_SD">
			<gotodialog next="UsageInfo_SD"/>
			<session-mapping key="comingFrom" value="UsageInfo"/>
			<action next="hl0010_WhatsNext_DS">
				<session-mapping key="serviceTarget" value="#"/>
				<if cond="hungup == 'true'">
					<action label="callerHangup" next="Hangup"/>
					<else>
						<action label="gotoRouting" next="hl0010_WhatsNext_DS"/>
					</else>
				</if>
			</action>
		</subdialog-state>
		<custom-state id="hl0140_AdressTopics_SD">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
			<script className="com.nuance.ps.telefonica.scripts.EncodeParams">
				<param name="encode" value="firstName,lastName,addressId"/>
			</script>
			<session-mapping key="formID" value="Invoke_AdressTopics_SD"/>
			<view name="/InvokeCS.dvxml"/>
		</custom-state>
		<decision-state id="ReturnFromAdressTopics">
			<session-mapping key="comingFrom" value="AdressTopics"/>
			<script className="com.nuance.ps.telefonica.scripts.UpdateFromAdressTopics"/>
			<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
			<action label="default" next="hl0010_WhatsNext_DS"/>
			<action label="hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</action>
			<event name="connection.disconnect.hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</event>
			<event name="error" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
			</event>
		</decision-state>
		<custom-state id="hl0110_DataContractUpgrade_SD">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
			<session-mapping key="formID" value="Invoke_DataContractUpgrade_SD"/>
			<view name="/InvokeCS.dvxml"/>
		</custom-state>
		<decision-state id="ReturnFromDataContractUpgrade">
			<session-mapping key="comingFrom" value="DataContractUpgrade"/>
			<session-mapping key="serviceTarget" value="#"/>
			<script className="com.nuance.ps.telefonica.scripts.UpdateFromDataContractUpgrade"/>
			<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
			<action label="default" next="hl0010_WhatsNext_DS">
				<session-mapping key="serviceTarget" value="undefined"/>
			</action>
			<action label="hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</action>
			<event name="connection.disconnect.hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</event>
			<event name="error" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
			</event>
		</decision-state>
		<custom-state id="hl0130_ThirdPartyBlocking_SD">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIEnterSelfService"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleEnter"/>
			<session-mapping key="formID" value="Invoke_ThirdPartyBlocking_SD"/>
			<view name="/InvokeCS.dvxml"/>
		</custom-state>
		<decision-state id="ReturnFromThirdPartyBlocking">
			<session-mapping key="comingFrom" value="ThirdPartyBlocking"/>
			<script className="com.nuance.ps.telefonica.scripts.UpdateFromThirdPartyBlocking"/>
			<script className="com.nuance.ps.telefonica.scripts.CalllogUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.KPIUpdate"/>
			<script className="com.nuance.ps.telefonica.scripts.ModuleExit"/>
			<action label="default" next="hl0010_WhatsNext_DS"/>
			<action label="hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</action>
			<event name="connection.disconnect.hangup" next="Hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="returncode" value="HANGUP"/>
			</event>
			<event name="error" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
			</event>
		</decision-state>
		<dm-state id="hl0100_OtherService_DM" type="YSNO">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<success>
				<action label="true" next="hl0010_WhatsNext_DS">
					<session-mapping key="goToNLCS" value="true"/>
				</action>
				<action label="false" next="hl0010_WhatsNext_DS">
					<session-mapping key="hangupRequired" value="true"/>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<action next="hl0010_WhatsNext_DS">
					<script className="com.nuance.ps.telefonica.scripts.SetReturnValues"/>
				</action>
			</event>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<action next="hl0010_WhatsNext_DS">
					<script className="com.nuance.ps.telefonica.scripts.SetReturnValues"/>
				</action>
			</event>
			<event name="error" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
			</event>
			<collection_configuration inputmodes="voice">
				<threshold_configuration maxturns="6" maxnomatches="3" maxnoinputs="3"/>
				<grammar_configuration commandgrammar="none" dtmfcommandgrammar="none" supportsadaptivegrammar="false" adaptivewordlist="none" adaptivegrammar="none">
					<grammars filename="hl0100_OtherService_DM.gram" count="1"/>
				</grammar_configuration>
				<vxml_properties incompletetimeout="1200ms"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="hl0100_ini_01">
								<prompt-segments>
									<audiofile src="hl0100_ini_01.wav" text="Haben Sie noch ein weiteres Anliegen?"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<repeatprompts count="1">
						<audio>
							<prompt id="hl0100_ini_01">
								<prompt-segments>
									<audiofile src="hl0100_ini_01.wav" text="Haben Sie noch ein weiteres Anliegen?"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="hl0100_retry1_01">
								<prompt-segments>
									<audiofile src="hl0100_retry1_01.wav" text="K&#246;nnen wir jetzt noch etwas f&#252;r Sie tun? Sagen Sie bitte  ja  oder  nein ."/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht h&#246;ren." src="gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="hl0100_retry2_01">
								<prompt-segments>
									<audiofile src="hl0100_retry2_01.wav" text="Haben Sie noch ein weiteres Anliegen?  Sagen Sie bitte  ja  oder  nein ."/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="3">
						<audio>
							<prompt id="gl_ni3_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht h&#246;ren." src="gl_ni3_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="hl0100_retry1_01">
								<prompt-segments>
									<audiofile src="hl0100_retry1_01.wav" text="K&#246;nnen wir jetzt noch etwas f&#252;r Sie tun? Sagen Sie bitte  ja  oder  nein ."/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Entschuldigung." src="gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="hl0100_retry1_01">
								<prompt-segments>
									<audiofile src="hl0100_retry1_01.wav" text="K&#246;nnen wir jetzt noch etwas f&#252;r Sie tun? Sagen Sie bitte  ja  oder  nein ."/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht verstehen." src="gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="hl0100_retry2_01">
								<prompt-segments>
									<audiofile src="hl0100_retry2_01.wav" text="Haben Sie noch ein weiteres Anliegen?  Sagen Sie bitte  ja  oder  nein ."/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="3">
						<audio>
							<prompt id="gl_nm3_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht verstehen." src="gl_nm3_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="hl0100_retry2_01">
								<prompt-segments>
									<audiofile src="hl0100_retry2_01.wav" text="Haben Sie noch ein weiteres Anliegen?  Sagen Sie bitte  ja  oder  nein ."/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<threshold_configuration maxturns="8"/>
				<vxml_properties maxspeechtimeout="12000ms" completetimeout="0ms" timeout="7000ms" bargein="true"/>
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
		<dm-state id="hl0150_VSS_PreMenu_DM" type="CUST">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<success>
				<action label="sim" next="hl0010_WhatsNext_DS">
					<session-mapping key="comingFrom" value="VSS_PreMenu"/>
				</action>
				<action label="usage">
					<session-mapping key="comingFrom" value="VSS_PreMenu"/>
					<session-mapping key="transferTo" value="AgentCare"/>
					<if cond="authenticated == 1 || (ivrCLI == ivrIdValue &amp;&amp; ivrIdType == 'MSISDN')">
						<action next="hl0120_UsageInfo_SD"/>
						<else>
							<action next="hl0010_WhatsNext_DS">
								<session-mapping key="hangupRequired" value="true"/>
								<audio>
									<prompt id="hl0150_out_01">
										<prompt-segments>
											<audiofile src="hl0150_out_01.wav" text="Zum Anh&#246;ren der Verbrauchsinfos ben&#246;tigen Sie entweder Ihre pers&#246;nliche Kundenkennzahl - oder sie rufen einfach mit dem Handy an, zu dem sie die Informationen ben&#246;tigen."/>
										</prompt-segments>
									</prompt>
								</audio>
							</action>
						</else>
					</if>
				</action>
			</success>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<action next="hl0010_WhatsNext_DS">
					<script className="com.nuance.ps.telefonica.scripts.SetReturnValues"/>
					<session-mapping key="comingFrom" value="VSS_PreMenu"/>
				</action>
			</event>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<action next="hl0010_WhatsNext_DS">
					<script className="com.nuance.ps.telefonica.scripts.SetReturnValues"/>
					<session-mapping key="comingFrom" value="VSS_PreMenu"/>
				</action>
			</event>
			<event name="error" next="hl0020_TransferToAgent_PP">
				<session-mapping key="goToAgent" value="true"/>
				<session-mapping key="comingFrom" value="VSS_PreMenu"/>
			</event>
			<collection_configuration confirmationmode="Never" highconfidencelevel="0.600" inputmodes="voice">
				<threshold_configuration maxinvalidanswers="2" maxturns="6" maxnoinputs="3" maxnomatches="3" maxrepeats="2" maxhelps="2"/>
				<grammar_configuration commandgrammar="none" dtmfcommandgrammar="none" supportsadaptivegrammar="false" adaptivewordlist="none" adaptivegrammar="none">
					<grammars filename="hl0150_VSS_PreMenu_DM.gram" count="1"/>
				</grammar_configuration>
				<vxml_properties confidencelevel="0.450" timeout="7000ms" incompletetimeout="1000ms" maxspeechtimeout="12000ms" termtimeout="10ms" interdigittimeout="2000ms" inputmodes="voice" completetimeout="0ms"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="hl0150_ini_01">
								<prompt-segments>
									<audiofile src="hl0150_ini_01.wav" text="Worum geht es bei Ihrem Anruf? Um eine Verbrauchsinformation - oder um Ihre SIM-Karte?"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<repeatprompts count="1">
						<audio>
							<prompt id="hl0150_ini_01">
								<prompt-segments>
									<audiofile src="hl0150_ini_01.wav" text="Worum geht es bei Ihrem Anruf? Um eine Verbrauchsinformation - oder um Ihre SIM-Karte?"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<repeatprompts count="2"/>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<prompt id="hl0150_retry1_01">
								<prompt-segments>
									<audiofile src="hl0150_retry1_01.wav" text="F&#252;r Fragen zu Ihrem Verbrauch sagen Sie bitte - 'Verbrauchsinfo' und bei Anliegen rund um Ihre SIM-Karte sagen Sie - 'SIM'."/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht h&#246;ren." src="gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="hl0150_retry2_01">
								<prompt-segments>
									<audiofile src="hl0150_retry2_01.wav" text="Bitte w&#228;hlen Sie aus folgenden Alternativen: Verbrauchsinfos - oder - SIM."/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="3">
						<audio>
							<prompt id="gl_ni3_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht h&#246;ren." src="gl_ni3_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="hl0150_retry2_01">
								<prompt-segments>
									<audiofile src="hl0150_retry2_01.wav" text="Bitte w&#228;hlen Sie aus folgenden Alternativen: Verbrauchsinfos - oder - SIM."/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Entschuldigung." src="gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="hl0150_retry1_01">
								<prompt-segments>
									<audiofile src="hl0150_retry1_01.wav" text="F&#252;r Fragen zu Ihrem Verbrauch sagen Sie bitte - 'Verbrauchsinfo' und bei Anliegen rund um Ihre SIM-Karte sagen Sie - 'SIM'."/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht verstehen." src="gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="hl0150_retry2_01">
								<prompt-segments>
									<audiofile src="hl0150_retry2_01.wav" text="Bitte w&#228;hlen Sie aus folgenden Alternativen: Verbrauchsinfos - oder - SIM."/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="3">
						<audio>
							<prompt id="gl_nm3_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht verstehen." src="gl_nm3_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="hl0150_retry2_01">
								<prompt-segments>
									<audiofile src="hl0150_retry2_01.wav" text="Bitte w&#228;hlen Sie aus folgenden Alternativen: Verbrauchsinfos - oder - SIM."/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<notoconfirmprefixes count="2"/>
					<notoconfirmprefixes count="3"/>
					<notoconfirmprefixes count="1"/>
					<notoconfirmprompts count="1"/>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
				<vxml_properties confidencelevel="0.450" sensitivity="0.500" bargein="true" bargeintype="speech"/>
			</global_configuration>
		</dm-state>
	</dialog>
	<dialog id="Exit">
		<custom-state id="EndApplication">
			<var name="IVR_Return" expr="authenticated"/>
			<var name="IVR_TransferTo" expr="transferTo"/>
			<var name="IVR_CSTEERING_APP_TAG" expr="applicationtag"/>
			<var name="selfServicesAborted" expr="selfServicesAborted"/>
			<var name="selfServicesStarted" expr="selfServicesStarted"/>
			<var name="selfServicesCompleted" expr="selfServicesCompleted"/>
			<assign name="session.com.genesyslab.userdata['IVR_Return']" expr="authenticated"/>
			<assign name="session.com.genesyslab.userdata['IVR_TransferTo']" expr="transferTo"/>
			<assign name="session.com.genesyslab.userdata['IVR_CSTEERING_APP_TAG']" expr="applicationtag"/>
			<if cond="returncode=='HANGUP'">
				<disconnect/>
			</if>
			<return namelist="returncode applicationtag IVR_Return IVR_TransferTo IVR_CSTEERING_APP_TAG selfServicesAborted selfServicesStarted selfServicesCompleted"/>
		</custom-state>
		<custom-state id="EndApplication">
			<data srcexpr="'end.nar?appsessionID='+appsessionID" name="end" method="post"/>
			<var name="IVR_Return" expr="authenticated"/>
			<var name="IVR_TransferTo" expr="transferTo"/>
			<var name="IVR_CSTEERING_APP_TAG" expr="applicationtag"/>
			<var name="lastBackendErrors" expr="'${lastBackendErrors}'"/>
			<var name="selfServicesAborted" expr="selfServicesAborted"/>
			<var name="selfServicesStarted" expr="selfServicesStarted"/>
			<var name="selfServicesCompleted" expr="selfServicesCompleted"/>
			<assign name="session.com.genesyslab.userdata['IVR_Return']" expr="authenticated"/>
			<assign name="session.com.genesyslab.userdata['IVR_TransferTo']" expr="transferTo"/>
			<assign name="session.com.genesyslab.userdata['IVR_CSTEERING_APP_TAG']" expr="applicationtag"/>
			<if cond="returncode=='HANGUP'">
				<disconnect/>
			</if>
			<return namelist="returncode applicationtag IVR_Return IVR_TransferTo IVR_CSTEERING_APP_TAG lastBackendErrors selfServicesAborted selfServicesStarted selfServicesCompleted"/>
		</custom-state>
	</dialog>
	<dialog id="Goodbye">
		<custom-state id="goodbye"/>
	</dialog>
	<dialog id="Initialization">
		<custom-state id="StartApplication">
			<session-mapping key="ani" value="" type="String"/>
			<session-mapping key="dnis" value="" type="String"/>
			<if cond="ivrLang == 'tr' || ivrLang == 'TR'">
				<session-mapping key="language" expr="'tr-TR'"/>
				<else>
					<session-mapping key="language" expr="'de-DE'"/>
				</else>
			</if>
			<action next="start"/>
			<event>
				<gotodialog next="Transfer"/>
			</event>
		</custom-state>
		<custom-state id="StartApplication">
			<var name="ani"/>
			<var name="dnis"/>
			<if cond="ivrLang == 'tr' || ivrLang == 'TR'">
				<session-mapping key="language" expr="'tr-TR'"/>
				<else>
					<session-mapping key="language" expr="'de-DE'"/>
				</else>
			</if>
			<action next="start"/>
			<event>
				<action next="exit"/>
			</event>
		</custom-state>
	</dialog>
	<dialog id="InvokeCS">
		<custom-state id="main">
			<var name="formID" expr="'${formID}'"/>
			<block>
				<goto expr="'#'+formID"/>
			</block>
		</custom-state>
		<subdialog-state id="Invoke_Authentication_SD">
			<var name="pkk" expr="'${pkk}'"/>
			<var name="id" expr="'${moduleID}'"/>
			<var name="IVR_MSISDN" expr="'${msisdn}'"/>
			<var name="selfServicesAborted" expr="'${selfServicesAborted}'"/>
			<var name="selfServicesStarted" expr="'${selfServicesStarted}'"/>
			<var name="selfServicesCompleted" expr="'${selfServicesCompleted}'"/>
			<session-mapping key="session.com.genesyslab.userdata['IVR_MSISDN']" expr="IVR_MSISDN"/>
			<var name="hangupRequired" expr="authenticateSD.hangupRequired"/>
			<var name="goToAgent" expr="authenticateSD.goToAgent"/>
			<var name="hangupOccurred" expr="authenticateSD.hangupOccurred"/>
			<var name="kpi" expr="authenticateSD.kpi"/>
			<var name="nodeseq" expr="authenticateSD.nodeseq"/>
			<var name="numBackendErrors" expr="authenticateSD.numBackendErrors"/>
			<var name="numUserErrors" expr="authenticateSD.numUserErrors"/>
			<var name="returncode" expr="authenticateSD.returncode"/>
			<var name="selfServicesAborted" expr="authenticateSD.selfServicesAborted"/>
			<var name="selfServicesStarted" expr="authenticateSD.selfServicesStarted"/>
			<var name="selfServicesCompleted" expr="authenticateSD.selfServicesCompleted"/>
			<assign name="appstepID" expr="authenticateSD.appstepID"/>
			<action next="ReturnFromAuthentication"/>
			<event>
				<action next="ReturnFromAuthentication"/>
			</event>
		</subdialog-state>
		<subdialog-state id="Invoke_Sim_SD">
			<var name="id" expr="'${moduleID}'"/>
			<var name="IVR_MSISDN" expr="'${msisdn}'"/>
			<var name="numberOfActiveSIMs" expr="'${numberOfActiveSIMs}'"/>
			<var name="activationMessage" expr="'${activationMessage}'"/>
			<var name="activationStatus" expr="'${activationStatus}'"/>
			<var name="activationOrderId" expr="'${activationOrderId}'"/>
			<var name="applicationtag" expr="'${applicationtag}'"/>
			<var name="subscriptionId" expr="'${subscriptionId}'"/>
			<var name="currentSIMCardId" expr="'${currentSIMCardId}'"/>
			<var name="simReplaceNewAuthNeeded" expr="'${simReplaceNewAuthNeeded}'"/>
			<var name="simNewCardNewAuthNeeded" expr="'${simNewCardNewAuthNeeded}'"/>
			<var name="replacementForBarredSim" expr="'${replacementForBarredSim}'"/>
			<var name="replacementSimOrdered" expr="'${replacementSimOrdered}'"/>
			<var name="isValid" expr="'${isValid}'"/>
			<var name="sv2610Retried" expr="'${sv2610Retried}'"/>
			<var name="firstSimNrWrongCounter" expr="'${firstSimNrWrongCounter}'"/>
			<var name="numberOfTry" expr="'${numberOfTry}'"/>
			<var name="simcardIdReturned" expr="'${simcardIdReturned}'"/>
			<var name="serviceTarget" expr="'${serviceTarget}'"/>
			<var name="hangupRequired" expr="simcardSD.hangupRequired"/>
			<var name="hangupOccurred" expr="simcardSD.hangupOccurred"/>
			<var name="goToAgent" expr="simcardSD.gotoAgent"/>
			<var name="goToSMS" expr="simcardSD.goToSMS"/>
			<var name="goToOtherService" expr="simcardSD.goToOtherService"/>
			<var name="authenticated" expr="simcardSD.authenticated"/>
			<var name="authenticationRequired" expr="simcardSD.isAuthenticationRequired"/>
			<var name="msisdn" expr="simcardSD.msisdn"/>
			<var name="simnumber" expr="simcardSD.simnumber"/>
			<var name="applicationtag" expr="simcardSD.applicationtag"/>
			<var name="simReplaceNewAuthNeeded" expr="simcardSD.simReplaceNewAuthNeeded"/>
			<var name="simNewCardNewAuthNeeded" expr="simcardSD.simNewCardNewAuthNeeded"/>
			<var name="replacementForBarredSim" expr="simcardSD.replacementForBarredSim"/>
			<var name="simOrderGetAdress" expr="simcardSD.simOrderGetAdress"/>
			<var name="subscriptionId" expr="simcardSD.subscriptionId"/>
			<var name="simOrderAction" expr="simcardSD.simOrderAction"/>
			<var name="addressName" expr="simcardSD.addressName"/>
			<var name="simcardIdIndex" expr="simcardSD.simcardIdIndex"/>
			<var name="numberOfTry" expr="simcardSD.numberOfTry"/>
			<var name="firstSimNrWrongCounter" expr="simcardSD.firstSimNrWrongCounter"/>
			<var name="sv2610Retried" expr="simcardSD.sv2610Retried"/>
			<var name="serviceTarget" expr="simcardSD.serviceTarget"/>
			<var name="kpi" expr="simcardSD.kpi"/>
			<var name="nodeseq" expr="simcardSD.nodeseq"/>
			<var name="lastBackendErrors" expr="simcardSD.lastBackendErrors"/>
			<var name="numBackendErrors" expr="simcardSD.numBackendErrors"/>
			<var name="numUserErrors" expr="simcardSD.numUserErrors"/>
			<var name="returncode" expr="simcardSD.returncode"/>
			<assign name="appstepID" expr="simcardSD.appstepID"/>
			<action next="ReturnFromSim"/>
			<event>
				<action next="ReturnFromSim"/>
			</event>
		</subdialog-state>
		<subdialog-state id="Invoke_NLCS_SD">
			<var name="id" expr="'${moduleID}'"/>
			<var name="callerSegment" expr="'${ivrCallerSegment}'"/>
			<var name="lineOfBusiness" expr="'${ivrCustomerSegment}'"/>
			<var name="sessionID" expr="'${appsessionID}'"/>
			<var name="csEntryCount" expr="'1'"/>
			<var name="brandColor" expr="'${brandColor}'"/>
			<var name="ANI" expr="session.com.genesyslab.userdata['IVR_CLI']"/>
			<var name="DNIS" expr="session.com.genesyslab.userdata['UR_ORIG_DNIS']"/>
			<var name="applicationtag" expr="nlcsSD.applicationtag"/>
			<var name="event" expr="nlcsSD.event"/>
			<var name="IVR_TransferTo" expr="nlcsSD.IVR_TransferTo"/>
			<var name="serviceTarget" expr="nlcsSD.serviceTarget"/>
			<var name="goToSMS" expr="nlcsSD.goToSMS"/>
			<var name="webInfoRequired" expr="nlcsSD.isWebInfoRequired"/>
			<var name="hangupRequired" expr="nlcsSD.hangupRequired"/>
			<var name="MIS" expr="nlcsSD.MIS"/>
			<var name="kpi" expr="nlcsSD.kpi"/>
			<var name="nodeseq" expr="nlcsSD.nodeseq"/>
			<var name="numBackendErrors" expr="nlcsSD.numBackendErrors"/>
			<var name="numUserErrors" expr="nlcsSD.numUserErrors"/>
			<var name="returncode" expr="nlcsSD.returncode"/>
			<assign name="appstepID" expr="nlcsSD.appstepID"/>
			<action next="ReturnFromNclsCs"/>
			<event>
				<action next="ReturnFromNclsCs"/>
			</event>
		</subdialog-state>
		<subdialog-state id="Invoke_DSL_SD">
			<var name="id" expr="'${moduleID}'"/>
			<var name="serviceTarget" expr="'${serviceTarget}'"/>
			<var name="brandColor" expr="'${brandColor}'"/>
			<var name="HNPRODL" expr="'${HNPRODL}'"/>
			<var name="O2PRODL" expr="'${O2PRODL}'"/>
			<var name="fixnetNumber" expr="'${fixnetNumber}'"/>
			<var name="hangupOccurred" expr="dslSD.hangupOccurred"/>
			<var name="goToSMS" expr="dslSD.goToSMS"/>
			<var name="goToAgent" expr="dslSD.goToAgent"/>
			<var name="transferTo" expr="dslSD.IVR_TransferTo"/>
			<var name="BigIncidentPlayed" expr="dslSD.BigIncidentPlayed"/>
			<var name="OrderStatusPlayed" expr="dslSD.OrderStatusPlayed"/>
			<var name="VasaIncidentPlayed" expr="dslSD.VasaIncidentPlayed"/>
			<var name="forceCallerToHangup" expr="dslSD.forceCallerToHangup"/>
			<var name="goToOtherService" expr="dslSD.goToOtherService"/>
			<var name="ivrDispAdInfo" expr="dslSD.ivrDispAdInfo"/>
			<var name="kpi" expr="dslSD.kpi"/>
			<var name="nodeseq" expr="dslSD.nodeseq"/>
			<var name="lastBackendErrors" expr="dslSD.lastBackendErrors"/>
			<var name="numBackendErrors" expr="dslSD.numBackendErrors"/>
			<var name="numUserErrors" expr="dslSD.numUserErrors"/>
			<var name="returncode" expr="dslSD.returncode"/>
			<assign name="appstepID" expr="dslSD.appstepID"/>
			<action next="ReturnFromDSL"/>
			<event>
				<action next="ReturnFromDSL"/>
			</event>
		</subdialog-state>
		<subdialog-state id="Invoke_SendSMS_SD">
			<var name="id" expr="'${moduleID}'"/>
			<var name="ReceiveSMS" expr="smsSD.ReceiveSMS"/>
			<var name="IVR_SMSsent" expr="smsSD.IVR_SMSsent"/>
			<assign name="appstepID" expr="smsSD.appstepID"/>
			<action next="ReturnFromSendSMS"/>
			<event>
				<action next="ReturnFromSendSMS"/>
			</event>
		</subdialog-state>
		<subdialog-state id="Invoke_K2_SD">
			<var name="id" expr="'${moduleID}'"/>
			<var name="goToAgent" expr="k2SD.gotoAgent"/>
			<var name="transferTo" expr="k2SD.IVR_TransferTo"/>
			<var name="hangupRequired" expr="k2SD.hangupRequired"/>
			<var name="applicationtag" expr="k2SD.applicationtag"/>
			<var name="kpi" expr="k2SD.kpi"/>
			<var name="nodeseq" expr="k2SD.nodeseq"/>
			<var name="numBackendErrors" expr="k2SD.numBackendErrors"/>
			<var name="numUserErrors" expr="k2SD.numUserErrors"/>
			<var name="returncode" expr="k2SD.returncode"/>
			<assign name="appstepID" expr="k2SD.appstepID"/>
			<action next="ReturnFromK2"/>
			<event>
				<action next="ReturnFromK2"/>
			</event>
		</subdialog-state>
		<subdialog-state id="Invoke_AdressTopics_SD">
			<var name="id" expr="'${moduleID}'"/>
			<var name="firstName" expr="'${firstName}'"/>
			<var name="lastName" expr="'${lastName}'"/>
			<var name="companyName" expr="'${companyName}'"/>
			<var name="goToAgent" expr="'${goToAgent}'"/>
			<var name="goToOtherService" expr="'${goToOtherService}'"/>
			<var name="hangupRequired" expr="'${hangupRequired}'"/>
			<var name="simOrderGetAdress" expr="'${simOrderGetAdress}'"/>
			<var name="applicationtag" expr="'${applicationtag}'"/>
			<var name="serviceTarget" expr="'${serviceTarget}'"/>
			<var name="addressId" expr="'${addressId}'"/>
			<var name="subscriptionId" expr="'${subscriptionId}'"/>
			<var name="replacementForBarredSim" expr="'${replacementForBarredSim}'"/>
			<var name="simOrderAction" expr="'${simOrderAction}'"/>
			<var name="addressName" expr="'${addressName}'"/>
			<var name="simcardIdIndex" expr="'${simcardIdIndex}'"/>
			<var name="numberOfActiveSIMs" expr="'${numberOfActiveSIMs}'"/>
			<block>
				<session-mapping key="session.com.genesyslab.userdata['ADDRESS_ID']" expr="addressId"/>
			</block>
			<var name="goToOtherService" expr="addrTopSD.goToOtherService"/>
			<var name="hangupRequired" expr="addrTopSD.hangupRequired"/>
			<var name="simOrderGetAdress" expr="addrTopSD.simOrderGetAdress"/>
			<var name="applicationtag" expr="addrTopSD.applicationtag"/>
			<var name="serviceTarget" expr="addrTopSD.serviceTarget"/>
			<var name="hangupOccurred" expr="addrTopSD.hangupOccurred"/>
			<var name="replacementSimOrdered" expr="addrTopSD.replacementSimOrdered"/>
			<var name="kpi" expr="addrTopSD.kpi"/>
			<var name="nodeseq" expr="addrTopSD.nodeseq"/>
			<var name="lastBackendErrors" expr="addrTopSD.lastBackendErrors"/>
			<var name="numBackendErrors" expr="addrTopSD.numBackendErrors"/>
			<var name="numUserErrors" expr="addrTopSD.numUserErrors"/>
			<var name="returncode" expr="addrTopSD.returncode"/>
			<assign name="appstepID" expr="addrTopSD.appstepID"/>
			<action next="ReturnFromAdressTopics"/>
			<event>
				<action next="ReturnFromAdressTopics"/>
			</event>
		</subdialog-state>
		<subdialog-state id="Invoke_DataContractUpgrade_SD">
			<var name="id" expr="'${moduleID}'"/>
			<var name="subscriptionId" expr="'${subscriptionId}'"/>
			<var name="customerId" expr="'${custId}'"/>
			<var name="goToAgent" expr="dcuSD.gotoAgent"/>
			<var name="goToOtherService" expr="dcuSD.goToOtherService"/>
			<var name="hangupRequired" expr="dcuSD.hangupRequired"/>
			<var name="hangupOccurred" expr="dcuSD.hangupOccurred"/>
			<var name="kpi" expr="dcuSD.kpi"/>
			<var name="nodeseq" expr="dcuSD.nodeseq"/>
			<var name="numBackendErrors" expr="dcuSD.numBackendErrors"/>
			<var name="numUserErrors" expr="dcuSD.numUserErrors"/>
			<var name="returncode" expr="dcuSD.returncode"/>
			<assign name="appstepID" expr="dcuSD.appstepID"/>
			<action next="ReturnFromDataContractUpgrade"/>
			<event>
				<action next="ReturnFromDataContractUpgrade"/>
			</event>
		</subdialog-state>
		<subdialog-state id="Invoke_ThirdPartyBlocking_SD">
			<var name="id" expr="'${moduleID}'"/>
			<var name="subscriptionid" expr="'${subscriptionId}'"/>
			<var name="goToOtherService" expr="tpbSD.goToOtherService"/>
			<var name="goToSMS" expr="tpbSD.goToSMS"/>
			<var name="goToAgent" expr="tpbSD.gotoAgent"/>
			<var name="hangupRequired" expr="tpbSD.hangupRequired"/>
			<var name="hangupOccurred" expr="tpbSD.hangupOccurred"/>
			<var name="kpi" expr="tpbSD.kpi"/>
			<var name="nodeseq" expr="tpbSD.nodeseq"/>
			<var name="lastBackendErrors" expr="tpbSD.lastBackendErrors"/>
			<var name="numBackendErrors" expr="tpbSD.numBackendErrors"/>
			<var name="numUserErrors" expr="tpbSD.numUserErrors"/>
			<var name="returncode" expr="tpbSD.returncode"/>
			<assign name="appstepID" expr="tpbSD.appstepID"/>
			<action next="ReturnFromThirdPartyBlocking"/>
			<event>
				<action next="ReturnFromThirdPartyBlocking"/>
			</event>
		</subdialog-state>
	</dialog>
	<custom-state id="start">
		<session-mapping key="ivr_open_services" path="IVR_OpenServices" scope="request"/>
		<session-mapping key="ur_clir" path="UR_CLIR" scope="request"/>
		<session-mapping key="ur_call_source" path="UR_CALL_SOURCE" scope="request"/>
		<session-mapping key="ivr_cli" path="IVR_CLI" scope="request"/>
		<session-mapping key="ur_brand" path="UR_BRAND" scope="request"/>
		<session-mapping key="ur_srv_brand_ID" path="UR_SRV_BRAND_ID" scope="request"/>
		<session-mapping key="ur_first_connid" path="UR_FIRST_CONNID" scope="request"/>
		<session-mapping key="ivr_original_entry" path="IVR_Original_Entry" scope="request"/>
		<session-mapping key="ivr_id_result" path="IVR_ID_RESULT" scope="request"/>
		<session-mapping key="ivr_customer_segment" path="IVR_CustomerSegment" scope="request"/>
		<session-mapping key="vasa_forces_hang_up" path="vasa_forces_hang_up" scope="request"/>
		<action next="main"/>
	</custom-state>
	<dialog id="main">
		<custom-state id="StartIDModule">
			<action next="hl00_CareSharedHL_SD"/>
		</custom-state>
	</dialog>
	<dialog id="Problems">
		<custom-state id="problems"/>
	</dialog>
	<custom-state id="event" className="com.nuance.framework.service.stateengine.state.GlobalEventHandlerState">
		<event name="event" next="transfer"/>
	</custom-state>
	<custom-state id="error" className="com.nuance.framework.service.stateengine.state.GlobalEventHandlerState">
		<event name="error" next="transfer"/>
	</custom-state>
	<custom-state id="disconnect" className="com.nuance.framework.service.stateengine.state.GlobalEventHandlerState">
		<event name="connection.disconnect.transfer" next="exit"/>
	</custom-state>
	<custom-state id="hangup" className="com.nuance.framework.service.stateengine.state.GlobalEventHandlerState">
		<event name="connection.disconnect.hangup" next="Hangup"/>
		<event name="telephone.disconnect.hangup" next="Hangup"/>
	</custom-state>
	<custom-state id="transfer">
		<view name="/Transfer.dvxml"/>
	</custom-state>
	<custom-state id="exit">
		<view name="/exit"/>
	</custom-state>
	<dialog id="SendSMS_SD">
		<data-access-state id="sv8010_GetSSMSData_DB">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.SelfServiceStart">
				<param name="name" value="SMS_POST"/>
			</script>
			<data-access id="GetSMSData" classname="com.nuance.ps.telefonica.dataaccess.GetSMSData">
				<inputs>
					<input-variable name="applicationTag" mask="false"/>
				</inputs>
				<outputs>
					<output-variable name="returnCode" mask="false"/>
				</outputs>
			</data-access>
			<action label="no_data" next="ReturnFromSendSMS">
				<session-mapping key="goToSMS" value="false"/>
				<script className="com.nuance.ps.telefonica.scripts.ReportSmsPost">
					<param name="STATUS" value="99"/>
				</script>
			</action>
			<action next="sv8020_ConfirmationNeeded_DS"/>
		</data-access-state>
		<decision-state id="sv8020_ConfirmationNeeded_DS">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<action label="yes" next="sv8030_ConfirmSMS_DM"/>
			<action label="no" next="sv8040_SendSMS_PP"/>
		</decision-state>
		<dm-state id="sv8030_ConfirmSMS_DM" type="CUST">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<success>
				<action label="yes" next="SendSMS">
					<session-mapping key="IVR_SMSsent" value="true"/>
					<script className="com.nuance.ps.telefonica.scripts.sv8030Assignment"/>
					<audio>
						<if type="java">
							<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
								<param name="compare" value="usageInfoDynamicSMS"/>
								<param name="value" scope="session" value="applicationtag"/>
							</condition>
							<prompt id="sv8030_out_03">
								<prompt-segments>
									<audiofile src="sv8030_out_03.wav" text="Ok Sie erhalten gleich eine SMS."/>
								</prompt-segments>
							</prompt>
							<else>
								<prompt id="sv8030_out_01">
									<prompt-segments>
										<audiofile src="sv8030_out_01.wav" text="Ok Sie erhalten gleich eine SMS. Sie brauchen nur dem angezeigten Link zu folgen"/>
									</prompt-segments>
								</prompt>
							</else>
						</if>
					</audio>
				</action>
				<action label="no" next="ReturnFromSendSMS">
					<script className="com.nuance.ps.telefonica.scripts.sv8030Assignment"/>
					<script className="com.nuance.ps.telefonica.scripts.ReportSmsPost">
						<param name="STATUS" value="2"/>
					</script>
					<audio>
						<prompt id="sv8030_out_02">
							<prompt-segments>
								<audiofile src="sv8030_out_02.wav" text="Ok Sie erhalten keine SMS"/>
							</prompt-segments>
						</prompt>
					</audio>
				</action>
			</success>
			<event name="connection.disconnect.hangup">
				<session-mapping key="hungup" value="true"/>
				<session-mapping key="ReturnFromSendSMScode" value="EVENT"/>
				<action next="ReturnFromSendSMS">
					<script className="com.nuance.ps.telefonica.scripts.ReportSmsPost">
						<param name="STATUS" value="0"/>
					</script>
				</action>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches">
				<action next="ReturnFromSendSMS">
					<script className="com.nuance.ps.telefonica.scripts.SetReturnFromSendSMSValues"/>
					<script className="com.nuance.ps.telefonica.scripts.ReportSmsPost">
						<param name="STATUS" value="3"/>
					</script>
				</action>
			</event>
			<event name="event.nuance.dialog.ndm.maxnoinputs">
				<action next="ReturnFromSendSMS">
					<script className="com.nuance.ps.telefonica.scripts.SetReturnFromSendSMSValues"/>
					<script className="com.nuance.ps.telefonica.scripts.ReportSmsPost">
						<param name="STATUS" value="3"/>
					</script>
				</action>
			</event>
			<event name="event.nuance.dialog.ndm.maxturns">
				<action next="ReturnFromSendSMS">
					<script className="com.nuance.ps.telefonica.scripts.SetReturnFromSendSMSValues"/>
					<script className="com.nuance.ps.telefonica.scripts.ReportSmsPost">
						<param name="STATUS" value="3"/>
					</script>
				</action>
			</event>
			<event name="event.nuance.dialog.ndm.maxinvalidanswers">
				<action next="ReturnFromSendSMS">
					<script className="com.nuance.ps.telefonica.scripts.SetReturnFromSendSMSValues"/>
					<script className="com.nuance.ps.telefonica.scripts.ReportSmsPost">
						<param name="STATUS" value="3"/>
					</script>
				</action>
			</event>
			<event name="event.nuance.dialog.ndm.maxnotoconfirms">
				<action next="ReturnFromSendSMS">
					<script className="com.nuance.ps.telefonica.scripts.SetReturnFromSendSMSValues"/>
					<script className="com.nuance.ps.telefonica.scripts.ReportSmsPost">
						<param name="STATUS" value="3"/>
					</script>
				</action>
			</event>
			<collection_configuration confirmationmode="IF_NECESSARY" highconfidencelevel="0.021" inputmodes="voice">
				<threshold_configuration maxinvalidanswers="2" maxturns="6" maxnoinputs="3" maxnomatches="3" maxrepeats="2" maxhelps="2"/>
				<grammar_configuration commandgrammar="none" dtmfcommandgrammar="none" supportsadaptivegrammar="false" adaptivewordlist="none" adaptivegrammar="none">
					<grammars filename="sv8030_ConfirmSMS_DM.grxml" count="1"/>
				</grammar_configuration>
				<vxml_properties confidencelevel="0.450" timeout="5000ms" incompletetimeout="1500ms" maxspeechtimeout="10000ms" termtimeout="10ms" interdigittimeout="2000ms" inputmodes="voice"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<if type="java">
								<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
									<param name="compare" value="usageInfoDynamicSMS"/>
									<param name="value" scope="session" value="applicationtag"/>
								</condition>
								<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
									<param name="compare" value="MSISDN"/>
									<param name="value" scope="session" value="ivrCliType"/>
									<param name="operator" value="NOT"/>
								</condition>
								<prompt id="sv8030_ini_03">
									<prompt-segments>
										<audiofile src="sv8030_ini_03.wav" text="Sollen wir Ihnen die Informationen per SMS zusenden? "/>
									</prompt-segments>
								</prompt>
							</if>
							<if type="java">
								<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
									<param name="compare" value="usageInfoDynamicSMS"/>
									<param name="value" scope="session" value="applicationtag"/>
								</condition>
								<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
									<param name="compare" value="MSISDN"/>
									<param name="value" scope="session" value="ivrCliType"/>
								</condition>
								<prompt id="sv8030_ini_01">
									<prompt-segments>
										<audiofile src="sv8030_ini_01.wav" text="Sollen wir ihnen die Informationen per SMS an das Handy senden von dem aus Sie gerade anrufen?"/>
									</prompt-segments>
								</prompt>
							</if>
							<if type="java">
								<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
									<param name="compare" value="usageInfoDynamicSMS"/>
									<param name="value" scope="session" value="applicationtag"/>
									<param name="operator" value="NOT"/>
								</condition>
								<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
									<param name="compare" value="MSISDN"/>
									<param name="value" scope="session" value="ivrCliType"/>
									<param name="operator" value="NOT"/>
								</condition>
								<prompt id="sv8030_ini_02">
									<prompt-segments>
										<audiofile src="sv8030_ini_02.wav" text="Sollen wir ihnen jetzt hierzu einen direkten Link per SMS schicken?"/>
									</prompt-segments>
								</prompt>
							</if>
							<if type="java">
								<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
									<param name="compare" value="usageInfoDynamicSMS"/>
									<param name="value" scope="session" value="applicationtag"/>
									<param name="operator" value="NOT"/>
								</condition>
								<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
									<param name="compare" value="MSISDN"/>
									<param name="value" scope="session" value="ivrCliType"/>
								</condition>
								<prompt id="sv8030_ini_04">
									<prompt-segments>
										<audiofile src="sv8030_ini_04.wav" text="Sollen wir Ihnen jetzt hierzu einen direkten Link per SMS an das Handy senden von dem aus Sie gerade anrufen?"/>
									</prompt-segments>
								</prompt>
							</if>
						</audio>
					</initialprompt>
					<nomatchprefixes count="1"/>
					<nomatchprefixes count="2"/>
					<nomatchprefixes count="3"/>
					<noinputprefixes count="1"/>
					<noinputprefixes count="2"/>
					<noinputprefixes count="3"/>
					<noinputprompts count="1">
						<audio>
							<if type="java">
								<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
									<param name="compare" value="usageInfoDynamicSMS"/>
									<param name="value" scope="session" value="applicationtag"/>
								</condition>
								<prompt id="sv8030_retry1_01">
									<prompt-segments>
										<audiofile src="sv8030_retry1_01.wav" text="Wenn wir Ihnen eine SMS mit Informationen schicken sollen, sagen Sie bitte JA, ansonsten NEIN"/>
									</prompt-segments>
								</prompt>
								<else>
									<prompt id="sv8030_retry1_02">
										<prompt-segments>
											<audiofile src="sv8030_retry1_02.wav" text="Wenn wir Ihnen eine SMS mit dem Online-Link schicken sollen, sagen Sie bitte JA, ansonsten NEIN"/>
										</prompt-segments>
									</prompt>
								</else>
							</if>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht h&#246;ren." src="gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="sv8030_retry2_01">
								<prompt-segments>
									<audiofile src="sv8030_retry2_01.wav" text="Sollen wir ihnen die Angaben per SMS schicken? Sagen Sie bitte JA oder NEIN"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="3">
						<audio>
							<prompt id="gl_ni3_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht h&#246;ren." src="gl_ni3_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="sv8030_retry2_01">
								<prompt-segments>
									<audiofile src="sv8030_retry2_01.wav" text="Sollen wir ihnen die Angaben per SMS schicken? Sagen Sie bitte JA oder NEIN"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Entschuldigung." src="gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<if type="java">
								<condition classname="com.nuance.ps.telefonica.prompts.StringCompare">
									<param name="compare" value="usageInfoDynamicSMS"/>
									<param name="value" scope="session" value="applicationtag"/>
								</condition>
								<prompt id="sv8030_retry1_01">
									<prompt-segments>
										<audiofile src="sv8030_retry1_01.wav" text="Wenn wir Ihnen eine SMS mit Informationen schicken sollen, sagen Sie bitte JA, ansonsten NEIN"/>
									</prompt-segments>
								</prompt>
								<else>
									<prompt id="sv8030_retry1_02">
										<prompt-segments>
											<audiofile src="sv8030_retry1_02.wav" text="Wenn wir Ihnen eine SMS mit dem Online-Link schicken sollen, sagen Sie bitte JA, ansonsten NEIN"/>
										</prompt-segments>
									</prompt>
								</else>
							</if>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht verstehen." src="gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="sv8030_retry2_01">
								<prompt-segments>
									<audiofile src="sv8030_retry2_01.wav" text="Sollen wir ihnen die Angaben per SMS schicken? Sagen Sie bitte JA oder NEIN"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="3">
						<audio>
							<prompt id="gl_nm3_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht verstehen." src="gl_nm3_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="sv8030_retry2_01">
								<prompt-segments>
									<audiofile src="sv8030_retry2_01.wav" text="Sollen wir ihnen die Angaben per SMS schicken? Sagen Sie bitte JA oder NEIN"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
		<play-state id="sv8040_SendSMS_PP">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<audio>
				<prompt id="sv8040_out_01">
					<prompt-segments>
						<audiofile src="sv8040_out_01.wav" text="Wir senden Ihnen jetzt hierzu einen direkten Link per SMS!"/>
					</prompt-segments>
				</prompt>
			</audio>
			<action next="SendSMS">
				<session-mapping key="IVR_SMSsent" value="true"/>
			</action>
		</play-state>
		<data-access-state id="SendSMS">
			<data-access id="SendSMS" classname="com.nuance.ps.telefonica.dataaccess.SendSMS">
				<inputs>
					<input-variable name="smsText" mask="false"/>
				</inputs>
				<outputs>
					<output-variable name="returnCode" mask="false"/>
				</outputs>
			</data-access>
			<action next="ReturnFromSendSMS">
				<script className="com.nuance.ps.telefonica.scripts.SelfServiceComplete">
					<param name="name" value="SMS_POST"/>
				</script>
				<script className="com.nuance.ps.telefonica.scripts.ReportSmsPost">
					<param name="STATUS" value="1"/>
				</script>
			</action>
		</data-access-state>
		<custom-state id="ReturnFromSendSMS">
			<action next="return"/>
		</custom-state>
	</dialog>
	<dialog id="Transfer">
		<custom-state id="transfer">
			<gotodialog next="Goodbye"/>
		</custom-state>
	</dialog>
	<dialog id="UsageInfo_SD">
		<custom-state id="resetUsageInfoVariables">
			<session-mapping key="hungup" value="false" type="String"/>
			<session-mapping key="DaysLeftInCurrentBillCycle" value="0" type="Integer"/>
			<session-mapping key="VoiceO2Net" value="0" type="Integer"/>
			<session-mapping key="VoiceFixedNet" value="0" type="Integer"/>
			<session-mapping key="VoiceOtherDomesticMobileNet" value="0" type="Integer"/>
			<session-mapping key="VoiceInternational" value="0" type="Integer"/>
			<session-mapping key="VoicePremium" value="0" type="Integer"/>
			<session-mapping key="SMSTotal" value="0" type="Integer"/>
			<session-mapping key="MobileDataMB" value="0" type="Integer"/>
			<session-mapping key="MobileDataMinutes" value="0" type="Integer"/>
			<action label="default" next="sv3010_SearchMobileUsageMCE_DB"/>
		</custom-state>
		<data-access-state id="sv3010_SearchMobileUsageMCE_DB">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<script className="com.nuance.ps.telefonica.scripts.SelfServiceStart">
				<param name="name" value="MOBILE_USAGE_INFO"/>
			</script>
			<var name="msisdn" path="msisdn" scope="session" isNamelist="true"/>
			<var name="urFirstConnId" path="urFirstConnId" scope="session" isNamelist="true"/>
			<data-access id="SearchMobileUsageMCE" classname="com.nuance.ps.telefonica.dataaccess.SearchMobileUsageMCE">
				<inputs>
					<input-variable name="msisdn" mask="false"/>
				</inputs>
				<outputs>
					<output-variable name="returnCode" mask="false"/>
				</outputs>
			</data-access>
			<action label="success" next="sv3020_UsageInfo_PP"/>
			<action label="error" next="ReturnFromUsageInfo">
				<script className="com.nuance.ps.telefonica.scripts.ReportMobileUsageInfo">
					<param name="STATUS" value="99"/>
				</script>
			</action>
		</data-access-state>
		<play-state id="sv3020_UsageInfo_PP">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<audio>
				<prompt type="custom" bargein="false">
					<param name="className" value="com.nuance.ps.telefonica.prompts.UsageInfoPrompt"/>
				</prompt>
			</audio>
			<action label="default" next="sv3025_HearUsageInfoAgain_DM">
				<script className="com.nuance.ps.telefonica.scripts.ReportMobileUsageInfo">
					<param name="STATUS" value="1"/>
				</script>
			</action>
		</play-state>
		<dm-state id="sv3025_HearUsageInfoAgain_DM" type="CUST">
			<script className="com.nuance.ps.telefonica.scripts.KPIAddNode"/>
			<success>
				<action label="yes" next="sv3020_UsageInfo_PP"/>
				<action label="no" next="ReturnFromUsageInfo">
					<session-mapping key="goToSMS" value="true"/>
					<session-mapping key="applicationtag" value="usageInfoDynamicSMS"/>
					<session-mapping key="goToOtherService" value="true"/>
					<script className="com.nuance.ps.telefonica.scripts.SelfServiceComplete">
						<param name="name" value="MOBILE_USAGE_INFO"/>
					</script>
				</action>
			</success>
			<event name="connection.disconnect.hangup" next="ReturnFromUsageInfo">
				<session-mapping key="hungup" value="true"/>
			</event>
			<event name="event.nuance.dialog.ndm.maxnomatches" next="UsageInfoRouting"/>
			<event name="event.nuance.dialog.ndm.maxnoinputs" next="UsageInfoRouting"/>
			<event name="event.nuance.dialog.ndm.maxturns" next="UsageInfoRouting"/>
			<event name="event.nuance.dialog.ndm.maxinvalidanswers" next="UsageInfoRouting"/>
			<event name="event.nuance.dialog.ndm.maxnotoconfirms" next="UsageInfoRouting"/>
			<collection_configuration confirmationmode="Never" highconfidencelevel="0.021" inputmodes="voice">
				<threshold_configuration maxinvalidanswers="2" maxturns="6" maxnoinputs="3" maxnomatches="3" maxrepeats="2" maxhelps="2"/>
				<grammar_configuration commandgrammar="none" dtmfcommandgrammar="none" supportsadaptivegrammar="false" adaptivewordlist="none" adaptivegrammar="none">
					<grammars filename="sv3025_HearUsageInfoAgain_DM.gram" count="1"/>
				</grammar_configuration>
				<vxml_properties confidencelevel="0.450" timeout="5000ms" incompletetimeout="1500ms" maxspeechtimeout="10000ms" termtimeout="10ms" interdigittimeout="2000ms" inputmodes="voice"/>
				<prompt_configuration>
					<initialprompt count="1">
						<audio>
							<prompt id="sv3025_ini_01">
								<prompt-segments>
									<audiofile text="M&#246;chten Sie die Informationen noch einmal h&#246;ren?" src="sv3025_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</initialprompt>
					<repeatprompts count="1">
						<audio>
							<prompt id="sv3025_ini_01">
								<prompt-segments>
									<audiofile text="M&#246;chten Sie die Informationen noch einmal h&#246;ren?" src="sv3025_ini_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</repeatprompts>
					<nomatchprompts count="1">
						<audio>
							<prompt id="gl_nm1_01">
								<prompt-segments>
									<audiofile text="Entschuldigung." src="gl_nm1_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="sv3025_retry1_01">
								<prompt-segments>
									<audiofile text="Wenn Sie die Verbrauchsinformationen noch einmal h&#246;ren m&#246;chten sagen Sie bitte ja - ansonsten nein." src="sv3025_retry1_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="2">
						<audio>
							<prompt id="gl_nm2_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht verstehen." src="gl_nm2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="sv3025_retry2_01">
								<prompt-segments>
									<audiofile text="M&#246;chten Sie die Informationen noch einmal h&#246;ren? Sagen Sie bitte Ja - oder Nein." src="sv3025_retry2_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<nomatchprompts count="3">
						<audio>
							<prompt id="gl_nm3_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht verstehen." src="gl_nm3_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="sv3025_retry2_01">
								<prompt-segments>
									<audiofile text="M&#246;chten Sie die Informationen noch einmal h&#246;ren? Sagen Sie bitte Ja - oder Nein." src="sv3025_retry2_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</nomatchprompts>
					<noinputprompts count="1">
						<audio>
							<prompt id="sv3025_retry1_01">
								<prompt-segments>
									<audiofile text="Wenn Sie die Verbrauchsinformationen noch einmal h&#246;ren m&#246;chten sagen Sie bitte ja - ansonsten nein." src="sv3025_retry1_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="2">
						<audio>
							<prompt id="gl_ni2_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht h&#246;ren." src="gl_ni2_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="sv3025_retry2_01">
								<prompt-segments>
									<audiofile text="M&#246;chten Sie die Informationen noch einmal h&#246;ren? Sagen Sie bitte Ja - oder Nein." src="sv3025_retry2_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
					<noinputprompts count="3">
						<audio>
							<prompt id="gl_ni3_01">
								<prompt-segments>
									<audiofile text="Ich konnte Sie erneut nicht h&#246;ren." src="gl_ni3_01.wav"/>
								</prompt-segments>
							</prompt>
							<prompt id="sv3025_retry2_01">
								<prompt-segments>
									<audiofile text="M&#246;chten Sie die Informationen noch einmal h&#246;ren? Sagen Sie bitte Ja - oder Nein." src="sv3025_retry2_01.wav"/>
								</prompt-segments>
							</prompt>
						</audio>
					</noinputprompts>
				</prompt_configuration>
			</collection_configuration>
			<global_configuration confirmationmode="Never">
				<failureprompt count="1"/>
				<successprompts count="1"/>
				<successprompts count="2"/>
				<successprompts count="3"/>
				<successcorrectedprompt count="1"/>
			</global_configuration>
		</dm-state>
		<decision-state id="UsageInfoRouting">
			<if cond="ivrOriginalEntry == 'VSS' || ivrOriginalEntry == 'vss'">
				<session-mapping key="hangupRequired" value="true"/>
				<action label="hangup_return" next="ReturnFromUsageInfo"/>
				<elseif cond="ivrOriginalEntry == 'HOTLINE' || ivrOriginalEntry == 'hotline' || ivrOriginalEntry == 'HL' || ivrOriginalEntry == 'hl'">
					<session-mapping key="goToAgent" value="true"/>
					<action label="goToAgent_return" next="ReturnFromUsageInfo"/>
				</elseif>
				<else>
					<action label="default_return" next="ReturnFromUsageInfo"/>
				</else>
			</if>
		</decision-state>
		<custom-state id="ReturnFromUsageInfo">
			<action next="return"/>
		</custom-state>
	</dialog>
</states-library>